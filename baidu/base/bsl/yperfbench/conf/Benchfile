CFLAGS=-O -Iinclude -I../ -I../../../ -g
LDFLAGS=-lpthread -lyperfbench -Llib
if host friday
CC=g++
else
CC=/usr/bin/g++
endif
#lsdjflskjfdlskj
case1 3: src/test.cpp $(CFLAGS) $(LDFLAGS) $(DD)
	!touch tmp
	para = 1 2 3
	para1:1 2 3
	#sdfjksdlfk
	p=2 3 4
	!rm tmp

#case2 3: src/test.cpp $(CFLAGS) $(LDFLAGS) $(DD)
#	!touch tmp
#	para = 1 2 3
#	para1:1 2 3
	#sdfjksdlfk
#	p=2 3 4
#	!rm tmp
#slkdjflskdjfslkdfjslkdjflskdjfslkdfj
