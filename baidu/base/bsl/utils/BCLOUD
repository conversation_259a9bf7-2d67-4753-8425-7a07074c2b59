#edit-mode: -*- python -*-
#coding:gbk

WORKROOT('../../../../')

#C++ flags.
CXXFLAGS('-fsigned-char -fPIC -O2 -ftemplate-depth-128')
if COMPILER_VERSION() == 'gcc482':
    CXXFLAGS('-std=c++11')
else:
    CXXFLAGS('-std=c++17')

#-I path
INCPATHS('. ./include $OUT/include')

user_sources=GLOB("*.cpp")

#release headers
HEADERS('*.h', '$INC/bsl/utils')

#.a
StaticLibrary('bsl_utils', Sources(user_sources))
