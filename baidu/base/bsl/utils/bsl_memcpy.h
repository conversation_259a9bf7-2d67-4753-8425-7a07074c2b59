/***************************************************************************
 * 
 * Copyright (c) 2008 Baidu.com, Inc. All Rights Reserved
 * $Id: bsl_memcpy.h,v 1.6 2009/10/14 08:24:59 chenxm Exp $ 
 * 
 **************************************************************************/
 
 
 
/**
 * @file xmemcpy.h
 * <AUTHOR>
 * @date 2008/11/07 17:17:22
 * @version $Revision: 1.6 $ 
 * @brief 
 *  
 **/


#ifndef  __XMEMCPY_H_
#define  __XMEMCPY_H_


#include <string.h>
namespace bsl
{
void *xmemcpy (void *dest, const void *src, size_t len);
};



#endif  //__XMEMCPY_H_

/* vim: set ts=4 sw=4 sts=4 tw=100 noet: */
