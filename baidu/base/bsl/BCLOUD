#edit-mode: -*- python -*-
#coding:gbk

WORKROOT('../../../')

#gcc version, default 'gcc'
COMPILER('gcc12')

#release headers
HEADERS('*.h', '$INC/bsl')

#sub directory
Directory('utils')
Directory('alloc')
Directory('archive')
Directory('containers')
Directory('pool')
Directory('buffer')
Directory('exception')
Directory('check_cast')
Directory('ResourcePool')
Directory('var/interface')
Directory('var/utils')
Directory('var/implement')
