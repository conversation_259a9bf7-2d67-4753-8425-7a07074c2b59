#edit-mode: -*- python -*-
#coding:gbk

WORKROOT('../../../../')

#C++ flags.
CXXFLAGS('-fsigned-char -Wall -W -pipe -g -fPIC')
if COMPILER_VERSION() == 'gcc482':
    CXXFLAGS('-std=c++11')
else:
    CXXFLAGS('-std=c++17')

#-I path
INCPATHS('. ./include $OUT/include')

user_sources=GLOB("*.cpp")

#release headers
HEADERS('*.h', '$INC/bsl/archive')

#.a
StaticLibrary('bsl_archive', Sources(user_sources))
