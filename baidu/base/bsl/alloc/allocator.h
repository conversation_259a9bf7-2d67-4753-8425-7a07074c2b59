/***************************************************************************
 * 
 * Copyright (c) 2008 Baidu.com, Inc. All Rights Reserved
 * $Id: allocator.h,v 1.2 2008/12/15 09:56:59 xiaowei Exp $ 
 * 
 **************************************************************************/
 
 
 
/**
 * @file allocator.h
 * <AUTHOR>
 * @date 2008/08/22 17:41:08
 * @version $Revision: 1.2 $ 
 * @brief 
 *  
 **/


#ifndef  __ALLOCATOR_H_
#define  __ALLOCATOR_H_


#include <bsl/alloc/bsl_alloc.h>
#include <bsl/alloc/bsl_cpsalloc.h>
#include <bsl/alloc/bsl_sample_alloc.h>



#endif  //__ALLOCATOR_H_

/* vim: set ts=4 sw=4 sts=4 tw=100 noet: */
