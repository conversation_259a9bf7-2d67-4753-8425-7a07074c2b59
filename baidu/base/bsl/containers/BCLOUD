#edit-mode: -*- python -*-
#coding:gbk

WORKROOT('../../../../')

#release headers
HEADERS('string/*.h', '$INC/bsl/containers/string')
HEADERS('slist/*.h', '$INC/bsl/containers/slist')
HEADERS('list/*.h', '$INC/bsl/containers/list')
HEADERS('hash/*.h', '$INC/bsl/containers/hash')
HEADERS('deque/*.h', '$INC/bsl/containers/deque')
HEADERS('btree/*.h', '$INC/bsl/containers/btree')
HEADERS('btree/asm-i386', '$INC/bsl/containers/btree')
HEADERS('btree/asm-x86_64', '$INC/bsl/containers/btree')
HEADERS('btree/gen_xmemcpy_h.py', '$INC/bsl/containers/btree')
