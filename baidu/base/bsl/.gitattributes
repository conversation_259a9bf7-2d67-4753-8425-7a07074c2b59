* text=auto !eol
/AutoBuffer.h -text
/BCLOUD -text
/BCLOUD.lib2-64 -text
/BinBuffer.h -text
/Makefile -text
/README -text
ResourcePool/BCLOUD -text
ResourcePool/Makefile -text
ResourcePool/ResourcePool.cpp -text
ResourcePool/ResourcePool.h -text
ResourcePool/ShallowCopyString.cpp -text
ResourcePool/ShallowCopyString.h -text
ResourcePool/bsl_wrappers.h -text
ResourcePool/bsl_wrappers_config.h -text
alloc/BCLOUD -text
alloc/Makefile -text
alloc/allocator.h -text
alloc/bsl_alloc.h -text
alloc/bsl_cpsalloc.h -text
alloc/bsl_sample_alloc.h -text
archive/BCLOUD -text
archive/Makefile -text
archive/bsl_binarchive.h -text
archive/bsl_filestream.cpp -text
archive/bsl_filestream.h -text
archive/bsl_serialization.h -text
archive/bsl_stream.h -text
/btree.h -text
buffer/AutoBuffer.cpp -text
buffer/AutoBuffer.h -text
buffer/BCLOUD -text
buffer/BinBuffer.cpp -text
buffer/BinBuffer.h -text
buffer/Makefile -text
buffer/Makefile.env -text
buffer/bsl_shared_buffer.cpp -text
buffer/bsl_shared_buffer.h_ -text
check_cast/BCLOUD -text
check_cast/Makefile -text
check_cast/check_cast.h -text
check_cast/check_cast.py -text
check_cast/check_cast_bsl_string.h -text
check_cast/check_cast_cstring.cpp -text
check_cast/check_cast_cstring.h -text
check_cast/check_cast_generated.h -text
containers/BCLOUD -text
containers/Makefile -text
containers/btree/Makefile -text
containers/btree/README -text
containers/btree/asm-i386/df_bitops.h -text
containers/btree/asm-x86_64/df_bitops.h -text
containers/btree/bsl_kv_btree.h -text
containers/btree/bsl_kv_btree_adapter.h -text
containers/btree/bsl_kv_btree_archive_traits.h -text
containers/btree/bsl_kv_btree_data_unit.h -text
containers/btree/bsl_kv_btree_iterator.h -text
containers/btree/bsl_kv_btree_xmemcpy.h -text
containers/btree/df_2d_ary.h -text
containers/btree/df_atomic.h -text
containers/btree/df_bitops.h -text
containers/btree/df_btree.h -text
containers/btree/df_def.h -text
containers/btree/df_log.h -text
containers/btree/df_misc.h -text
containers/btree/df_xz_btree.h -text
containers/btree/gen_xmemcpy_h.py -text
containers/btree/test_bsl_kv_btree.cpp -text
containers/btree/test_bsl_kv_btree_string.cpp -text
containers/btree/test_mt_bsl_kv_btree.cpp -text
containers/btree/test_xmemcpy_correctness.cpp -text
containers/deque/Makefile -text
containers/deque/README -text
containers/deque/bsl_deque.h -text
containers/deque/bsl_rwseque.h -text
containers/hash/Makefile -text
containers/hash/bsl_hash_fun.h -text
containers/hash/bsl_hash_multimap.h -text
containers/hash/bsl_hashmap.h -text
containers/hash/bsl_hashset.h -text
containers/hash/bsl_hashtable.h -text
containers/hash/bsl_hashutils.h -text
containers/hash/bsl_phashmap.h -text
containers/hash/bsl_phashset.h -text
containers/hash/bsl_phashtable.h -text
containers/hash/bsl_readmap.h -text
containers/hash/bsl_readset.h -text
containers/hash/bsl_rwhashset.h -text
containers/list/ChangeLog -text
containers/list/Makefile -text
containers/list/README -text
containers/list/bsl_list.h -text
containers/slist/ChangeLog -text
containers/slist/Makefile -text
containers/slist/README -text
containers/slist/bsl_slist.h -text
containers/string/Makefile -text
containers/string/bsl_string.h -text
cxxtest/README -text
cxxtest/TODO -text
cxxtest/Versions -text
cxxtest/comtest.h -text
cxxtest/cxxtest.spec -text
cxxtest/cxxtest/Descriptions.cpp -text
cxxtest/cxxtest/Descriptions.h -text
cxxtest/cxxtest/DummyDescriptions.cpp -text
cxxtest/cxxtest/DummyDescriptions.h -text
cxxtest/cxxtest/ErrorFormatter.h -text
cxxtest/cxxtest/ErrorPrinter.h -text
cxxtest/cxxtest/Flags.h -text
cxxtest/cxxtest/GlobalFixture.cpp -text
cxxtest/cxxtest/GlobalFixture.h -text
cxxtest/cxxtest/Gui.h -text
cxxtest/cxxtest/LinkedList.cpp -text
cxxtest/cxxtest/LinkedList.h -text
cxxtest/cxxtest/Mock.h -text
cxxtest/cxxtest/ParenPrinter.h -text
cxxtest/cxxtest/QtGui.h -text
cxxtest/cxxtest/RealDescriptions.cpp -text
cxxtest/cxxtest/RealDescriptions.h -text
cxxtest/cxxtest/Root.cpp -text
cxxtest/cxxtest/SelfTest.h -text
cxxtest/cxxtest/StdHeaders.h -text
cxxtest/cxxtest/StdValueTraits.h -text
cxxtest/cxxtest/StdioFilePrinter.h -text
cxxtest/cxxtest/StdioPrinter.h -text
cxxtest/cxxtest/TeeListener.h -text
cxxtest/cxxtest/TestListener.h -text
cxxtest/cxxtest/TestRunner.h -text
cxxtest/cxxtest/TestSuite.cpp -text
cxxtest/cxxtest/TestSuite.h -text
cxxtest/cxxtest/TestTracker.cpp -text
cxxtest/cxxtest/TestTracker.h -text
cxxtest/cxxtest/ValueTraits.cpp -text
cxxtest/cxxtest/ValueTraits.h -text
cxxtest/cxxtest/Win32Gui.h -text
cxxtest/cxxtest/X11Gui.h -text
cxxtest/cxxtest/YesNoRunner.h -text
cxxtest/cxxtestgen.pl -text
cxxtest/cxxtestgen.py -text
cxxtest/demo/Makefile -text
cxxtest/demo/bsl_test_fun.h -text
cxxtest/demo/bsl_test_fun2.h -text
cxxtest/docs/convert.pl -text
cxxtest/docs/guide.html -text
cxxtest/docs/index.html -text
cxxtest/docs/qt.png -text
cxxtest/docs/qt2.png -text
cxxtest/docs/win32.png -text
cxxtest/docs/x11.png -text
cxxtest/sample/Construct -text
cxxtest/sample/CreatedTest.h -text
cxxtest/sample/DeltaTest.h -text
cxxtest/sample/EnumTraits.h -text
cxxtest/sample/ExceptionTest.h -text
cxxtest/sample/FixtureTest.h -text
cxxtest/sample/Makefile.PL -text
cxxtest/sample/Makefile.bcc32 -text
cxxtest/sample/Makefile.msvc -text
cxxtest/sample/Makefile.unix -text
cxxtest/sample/MessageTest.h -text
cxxtest/sample/SimpleTest.h -text
cxxtest/sample/TraitsTest.h -text
cxxtest/sample/aborter.tpl -text
cxxtest/sample/file_printer.tpl -text
cxxtest/sample/only.tpl -text
cxxtest/sample/yes_no_runner.cpp -text
/deque.h -text
/exception.h -text
exception/BCLOUD -text
exception/Makefile -text
exception/Makefile.env -text
exception/bsl_auto_buffer.h -text
exception/bsl_exception.cpp -text
exception/bsl_exception.h -text
exception/stack.py -text
exception/stack_trace.cpp -text
exception/stack_trace.h -text
exception/test_bsl_exception.cpp -text
/list.h -text
/map.h -text
/pool.h -text
pool/BCLOUD -text
pool/Makefile -text
pool/bsl_cachedpool.cpp -text
pool/bsl_cachedpool.h -text
pool/bsl_cachedpoolappend.cpp -text
pool/bsl_cachedpoolappend.h -text
pool/bsl_debugpool.h -text
pool/bsl_pool.h -text
pool/bsl_poolalloc.h -text
pool/bsl_xcompool.cpp -text
pool/bsl_xcompool.h -text
pool/bsl_xmempool.cpp -text
pool/bsl_xmempool.h -text
proctest/Makefile -text
proctest/bsl_thread_hash.cpp -text
proctest/bsl_unsafe_hash.cpp -text
proctest/bslsort.cpp -text
proctest/bsltest.cpp -text
proctest/conf/make.conf -text
proctest/conf/make32.conf -text
proctest/stl_hashmap.h -text
proctest/stllist.cpp -text
proctest/stlsort.cpp -text
/release.bcloud -text
/set.h -text
/string.h -text
test/COMAKE -text
test/Makefile -text
test/test_bsl_hash_multimap.cpp -text
test/test_bsl_hashtable.cpp -text
unittest/Makefile -text
unittest/bsl_test_alloc.h -text
unittest/bsl_test_archive.h -text
unittest/bsl_test_auto_buffer.h -text
unittest/bsl_test_auto_buffer.py -text
unittest/bsl_test_bin_buffer.h -text
unittest/bsl_test_cachedpool.h -text
unittest/bsl_test_cpalloc_address.h -text
unittest/bsl_test_deque.h -text
unittest/bsl_test_exception.h -text
unittest/bsl_test_exception.py -text
unittest/bsl_test_exception_prepare.h_ -text
unittest/bsl_test_hash_qa.h -text
unittest/bsl_test_hashmap.h -text
unittest/bsl_test_hashmap_string.h -text
unittest/bsl_test_hashset.h -text
unittest/bsl_test_hashtable.h -text
unittest/bsl_test_header.h -text
unittest/bsl_test_list.h -text
unittest/bsl_test_memcpy.h -text
unittest/bsl_test_mempool_stable.h -text
unittest/bsl_test_phashmap.h -text
unittest/bsl_test_phashmap_dump.h -text
unittest/bsl_test_phashmap_str_dump.h -text
unittest/bsl_test_phashmap_string.h -text
unittest/bsl_test_phashtable.h -text
unittest/bsl_test_pool.h -text
unittest/bsl_test_queue.h -text
unittest/bsl_test_readmap.h -text
unittest/bsl_test_readset.h -text
unittest/bsl_test_rwhashset.h -text
unittest/bsl_test_rwseque.h -text
unittest/bsl_test_rwseque_pthread.h -text
unittest/bsl_test_rwseque_pthread_string.h -text
unittest/bsl_test_sample_alloc_allocate_deallocate.h -text
unittest/bsl_test_shallow_copy_string.hpp -text
unittest/bsl_test_shallow_copy_string.py -text
unittest/bsl_test_slist.h -text
unittest/bsl_test_stack.h -text
unittest/bsl_test_string.h -text
unittest/bsl_test_string.hpp -text
unittest/bsl_test_string.py -text
unittest/bsl_test_string32.h -text
unittest/bsl_test_utils.h -text
unittest/define.rc -text
unittest/header.hpp -text
unittest/header_string.hpp -text
unittest/test_alloc.h.ini -text
unittest/war.sh -text
utils/BCLOUD -text
utils/Makefile -text
utils/bsl_construct.h -text
utils/bsl_debug.h -text
utils/bsl_memcpy.cpp -text
utils/bsl_memcpy.h -text
utils/bsl_type_traits.h -text
utils/bsl_utils.h -text
var/Makefile -text
var/Makefile.env -text
var/Makefile.rules -text
var/implement/Array.h -text
var/implement/BCLOUD -text
var/implement/BigInt.h -text
var/implement/Bool.h -text
var/implement/Dict.h -text
var/implement/Double.h -text
var/implement/Function.h -text
var/implement/Int32.h -text
var/implement/Int64.h -text
var/implement/MagicArray.h -text
var/implement/MagicDict.h -text
var/implement/MagicRef.cpp -text
var/implement/MagicRef.h -text
var/implement/Makefile -text
var/implement/Method.h -text
var/implement/Null.cpp -text
var/implement/Null.h -text
var/implement/Number.h -text
var/implement/Ref.h -text
var/implement/ShallowRaw.h -text
var/implement/ShallowString.h -text
var/implement/String.h -text
var/implement/implement.h -text
var/interface/ArrayIterator.h -text
var/interface/BCLOUD -text
var/interface/DictIterator.h -text
var/interface/IBinaryDeserializer.h -text
var/interface/IBinarySerializer.h -text
var/interface/IRef.h -text
var/interface/ITextDeserializer.h -text
var/interface/ITextSerializer.h -text
var/interface/IVar.h -text
var/interface/Makefile -text
var/unittest/ITestVar.h -text
var/unittest/Makefile -text
var/unittest/test.h -text
var/unittest/test_ResourcePool.cpp -text
var/unittest/test_ShallowCopystring.cpp -text
var/unittest/test_bigint/COMAKE -text
var/unittest/test_bigint/Makefile -text
var/unittest/test_bigint/test_BigInt.cpp -text
var/unittest/test_bsl_wrappers.cpp -text
var/unittest/test_check_cast.cpp -text
var/unittest/test_check_cast_gen.cpp -text
var/unittest/test_check_cast_gen.py -text
var/unittest/test_data.json -text
var/unittest/test_data.std -text
var/unittest/test_mcpack.json -text
var/unittest/test_mcpack.std -text
var/unittest/test_perform_array.cpp -text
var/unittest/test_perform_dict.cpp -text
var/unittest/test_var_Array.cpp -text
var/unittest/test_var_Array2.cpp -text
var/unittest/test_var_Bool.cpp -text
var/unittest/test_var_Dict.cpp -text
var/unittest/test_var_Double.cpp -text
var/unittest/test_var_Function.cpp -text
var/unittest/test_var_Int32.cpp -text
var/unittest/test_var_Int64.cpp -text
var/unittest/test_var_MagicRef.cpp -text
var/unittest/test_var_Method.cpp -text
var/unittest/test_var_Null.cpp -text
var/unittest/test_var_Number.cpp -text
var/unittest/test_var_Ref.cpp -text
var/unittest/test_var_ShallowRaw.cpp -text
var/unittest/test_var_ShallowString.cpp -text
var/unittest/test_var_String.cpp -text
var/unittest/test_var_String2.cpp -text
var/unittest/test_var_invalid.h -text
var/unittest/test_var_utils.cpp -text
var/utils/BCLOUD -text
var/utils/Makefile -text
var/utils/assign.h -text
var/utils/assign.py -text
var/utils/utils.cpp -text
var/utils/utils.h -text
var/utils/var_traits.h -text
yperfbench/ChangeLog -text
yperfbench/Makefile -text
yperfbench/README -text
yperfbench/bin/bench -text
yperfbench/conf/Benchfile -text
yperfbench/demo/test.cpp -text
yperfbench/include/xutils.h -text
yperfbench/include/yperfbench.h -text
yperfbench/lib/Makefile -text
yperfbench/lib/xutils.cpp -text
yperfbench/lib/yperfbench.cpp -text
