/***************************************************************************
 * 
 * Copyright (c) 2009 Baidu.com, Inc. All Rights Reserved
 * $Id: bsl_test_header.h,v 1.2 2009/03/09 04:56:42 xiaowei Exp $ 
 * 
 **************************************************************************/
 
 
 
/**
 * @file bsl_test_header.h
 * <AUTHOR>
 * @date 2009/01/13 11:42:52
 * @version $Revision: 1.2 $ 
 * @brief 
 *  
 **/


#ifndef  __BSL_TEST_HEADER_H_
#define  __BSL_TEST_HEADER_H_


#include "bsl/deque.h"
#include "bsl/list.h"
#include "bsl/map.h"
#include "bsl/pool.h"
#include "bsl/set.h"

class bsl_test_header : public CxxTest::TestSuite
{
public:
	void test_header_t() {
	}
};
















#endif  //__BSL_TEST_HEADER_H_

/* vim: set ts=4 sw=4 sts=4 tw=100 noet: */
