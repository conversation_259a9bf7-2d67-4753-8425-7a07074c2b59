/***************************************************************************
 * 
 * Copyright (c) 2008 Baidu.com, Inc. All Rights Reserved
 * $Id: define.rc,v 1.1 2008/11/14 03:39:13 xiaowei Exp $ 
 * 
 **************************************************************************/
 
 
 
/**
 * @file define.h
 * <AUTHOR>
 * @date 2008/10/28 11:42:03
 * @version $Revision: 1.1 $ 
 * @brief 
 *  
 **/


#ifndef  __DEFINE_H_
#define  __DEFINE_H_

#include <cxxtest/TestSuite.h>
class MyClass {
    public:
    char name[800];
    char value[800];
    bool operator == (const MyClass & __x) const {
        return ((strcmp(name, __x.name) == 0)&&
                (strcmp(value, __x.value) == 0));
    }
    const MyClass & operator = (const MyClass &__x) {
        memcpy(name, __x.name, sizeof(name));
        memcpy(value, __x.value, sizeof(value));
        memcpy(u, __x.u, 256);
        return *this;
    }
    char *u;
    MyClass() {
        u = (char*)malloc(256);
        memset(u, 0, 128);
    }
    ~MyClass() {
        free(u);
    }
    MyClass(const MyClass &__x) {
        memcpy(name, __x.name, sizeof(name));
        memcpy(value, __x.value, sizeof(value));
        u = (char*)malloc(256);
        memcpy(u, __x.u, 256);
    }
};

class MyStruct {
    public:
    char name[800];
    char value[800];
    char *u;
    MyStruct() {
        u = (char*)malloc(256);
        memset(u, 0, 128);
    }
    ~MyStruct() {
        free(u);
    }
    MyStruct(const MyStruct &__x) {
        memcpy(name, __x.name, sizeof(name));
        memcpy(value, __x.value, sizeof(value));
        u = (char*)malloc(256);
        memcpy(u, __x.u, 256);
    }
};

















#endif  //__DEFINE_H_

/* vim: set ts=4 sw=4 sts=4 tw=100 */
