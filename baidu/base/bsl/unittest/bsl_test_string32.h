/***************************************************************************
 * 
 * Copyright (c) 2008 Baidu.com, Inc. All Rights Reserved
 * $Id: bsl_test_string32.h,v 1.3 2008/12/15 09:57:00 xiaowei Exp $ 
 * 
 **************************************************************************/
 
 
 
/**
 * @file bsl_test_string32.h
 * <AUTHOR>
 * @date 2008/11/19 18:29:35
 * @version $Revision: 1.3 $ 
 * @brief 
 *  
 **/


#ifndef  __BSL_TEST_STRING32_H_
#define  __BSL_TEST_STRING32_H_

#include <cxxtest/TestSuite.h>
#include <bsl/containers/string/bsl_string.h>
#include <bsl/utils/bsl_construct.h>
#include <bsl/containers/deque/bsl_deque.h>
#include <deque>
#include <vector>

class bsl_test_string32 : public CxxTest::TestSuite
{
public:
	void test_string32 () {
		bsl::string str = "adsf2";
		std::vector<bsl::string> vec;
		vec.push_back(str);
		bsl::deque<std::vector<bsl::string> > deq;
		deq.push_back(vec);
	}
};












#endif  //__BSL_TEST_STRING32_H_

/* vim: set ts=4 sw=4 sts=4 tw=100 */
