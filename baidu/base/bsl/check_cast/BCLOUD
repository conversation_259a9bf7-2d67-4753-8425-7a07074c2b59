#edit-mode: -*- python -*-
#coding:gbk

WORKROOT('../../../../')

#Preprocessor flags.
CPPFLAGS(r'-DVAR_DEBUG_FLAG -DBSL_VERSION=\"bsl1.1.0.0\" -DBSL_CVSTAG=\"bsl_1-1-0-0_PD_BL\" -DBSL_PROJECT_NAME=\"bsl\"')

#C++ flags.
CXXFLAGS('-g -pipe -fPIC -finline-functions -fsigned-char -Wall -W -Wshadow -Wpointer-arith -Wcast-qual -Wwrite-strings -Wconversion -Winline -Woverloaded-virtual -Wsign-promo')
if COMPILER_VERSION() == 'gcc482':
    CXXFLAGS('-std=c++11')
else:
    CXXFLAGS('-std=c++17')

#-I path
INCPATHS('. ./include $OUT/include')

user_sources=GLOB("*.cpp")

#release headers
HEADERS('*.h', '$INC/bsl/check_cast')
HEADERS('*.h', '$INC/bsl/')

#.a
StaticLibrary('bsl_check_cast', Sources(user_sources))
