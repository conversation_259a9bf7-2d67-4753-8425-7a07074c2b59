#!/usr/bin/env python
INF = float('inf')

SUFFIX = {
    'long': 'L',
    'long long': 'LL',
    'unsigned int': 'U',
    'unsigned long': 'UL',
    'unsigned long long': 'ULL',
    'float': '.0',
    'double': '.0',
    'long double': '.0L'
}

MIN_32 = { 
    'char': -2**7, 'signed char': -2**7, 'short': -2**15, 'int': -2**31, 'long': -2**31, 'long long': -2**63,
    'unsigned char': 0, 'unsigned short': 0, 'unsigned int': 0, 'unsigned long': 0, 'unsigned long long': 0,
    'float': -INF, 'double': -INF, 'long double': -INF, 
}

MAX_32 = {
    'char': 2**7-1, 'signed char': 2**7-1, 'short': 2**15-1, 'int': 2**31-1, 'long': 2**31-1, 'long long': 2**63-1,
    'unsigned char': 2**8-1, 'unsigned short': 2**16-1, 'unsigned int': 2**32-1, 'unsigned long': 2**32-1, 'unsigned long long': 2**64-1,
    'float': INF, 'double': INF, 'long double': INF,
}

MIN_64 = dict(MIN_32)
MIN_64['long'] = MIN_64['long long']

MAX_64 = dict(MAX_32)
MAX_64['long'] = MAX_64['long long']
MAX_64['unsigned long'] = MAX_64['unsigned long long']

TYPES = MIN_32.keys()

FILE_HEADER = """
#ifndef  __CHECK_CAST_GENERATED_H_
#define  __CHECK_CAST_GENERATED_H_

/* CAUTION: this file is generated by check_cast.py automatically, don't modify it */
//internal use
#ifndef  __CHECK_CAST_H_
#error "this file cannot be included directly, please include check_cast.h instead"
#endif

#include <bits/wordsize.h>
namespace bsl{
"""

FILE_FOOTER = """
}   // namespace bsl
#endif  //__CHECK_CAST_GENERATED_H_
"""

FUNCTION_TEMPLATE = """
    template<>
        inline %(dest_type)s check_cast<%(dest_type)s, %(src_type)s>( %(src_type)s value ){
            %(min_check_code)s
            %(max_check_code)s
            return static_cast<%(dest_type)s>(value);
        }
"""


def gen_function( src_type, dest_type, MIN, MAX ):


    if MIN[src_type] < MIN[dest_type]:
        min_check_code = """
            if ( value < %(bound)s ){ 
                throw bsl::UnderflowException()<<BSL_EARG<<"value["<<value<<"] bound[%(bound)s]";
            }
        """ % { "bound":  str(MIN[dest_type]) + SUFFIX.get(src_type, '') }
    else:
        min_check_code = ""

    if MAX[src_type] > MAX[dest_type]:
        max_check_code = """
            if ( value > %(bound)s ){
                throw bsl::OverflowException()<<BSL_EARG<<"value["<<value<<"] bound[%(bound)s]"; 
            }
        """ % { "bound": str(MAX[dest_type]) + SUFFIX.get(src_type, '') }
    else:
        max_check_code = ""

    return FUNCTION_TEMPLATE % locals()

if __name__ == "__main__":
    print FILE_HEADER

    print "#if __WORDSIZE == 32"
    for src_type in TYPES:
        for dest_type in TYPES:
            print gen_function( src_type, dest_type, MIN_32, MAX_32 )
    print "#else"
    for src_type in TYPES:
        for dest_type in TYPES:
            print gen_function( src_type, dest_type, MIN_64, MAX_64 )
    print "#endif"

    #bool
    min_check_code = max_check_code = "";
    for src_type in TYPES:
        dest_type = 'bool'
        print FUNCTION_TEMPLATE % locals()

    src_type = 'bool'
    for dest_type in TYPES:
        print FUNCTION_TEMPLATE % locals()

    src_type = dest_type = 'bool'
    print FUNCTION_TEMPLATE % locals()

    print FILE_FOOTER
