#edit-mode: -*- python -*-
#coding:gbk

WORKROOT('../../../../')

#Preprocessor flags.
CPPFLAGS(r'-DBSL_DEBUG_FLAG -DBSL_VERSION=\"bsl1.0.2.0\" -DBSL_CVSTAG=\"bsl_1-0-2-0_PD_BL\" -DBSL_PROJECT_NAME=\"bsl\"')

#C++ flags.
CXXFLAGS('-g -pipe -fPIC -finline-functions -fsigned-char -Wall -W -Wshadow -Wpointer-arith -Wcast-qual -Wwrite-strings -Wconversion -Winline -Woverloaded-virtual -Wsign-promo')
if COMPILER_VERSION() == 'gcc482':
    CXXFLAGS('-std=c++11')
else:
    CXXFLAGS('-std=c++17')

#-I path
INCPATHS('. ./include $OUT/include')

user_sources=GLOB("AutoBuffer.cpp BinBuffer.cpp")

#release headers
HEADERS('*.h', '$INC/bsl')

#.a
StaticLibrary('bsl_buffer', Sources(user_sources))
