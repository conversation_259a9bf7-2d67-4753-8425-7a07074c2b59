/***************************************************************************
 * 
 * Copyright (c) 2008 Baidu.com, Inc. All Rights Reserved
 * $Id: bsl_test_fun.h,v 1.1 2008/09/03 06:47:49 xiaowei Exp $ 
 * 
 **************************************************************************/
 
 
 
/**
 * @file bsl_test_fun.cpp
 * <AUTHOR>
 * @date 2008/08/14 17:44:12
 * @version $Revision: 1.1 $ 
 * @brief 
 *  
 **/


 #ifndef  __BSL_TEST_FUN_
 #define  __BSL_TEST_FUN_
 #include <cxxtest/TestSuite.h>


class bsl_test_fun : public CxxTest::TestSuite
{
	public:
		void test_normal_1(void) 
		{   
			int val = 0;
			TSM_ASSERT(val, val == 0);
		}   
		void test_normal_2(void) 
		{   
			int val = 0;
			TSM_ASSERT(val, val != 0);
		}   
};


#endif
















/* vim: set ts=4 sw=4 sts=4 tw=100 noet: */
