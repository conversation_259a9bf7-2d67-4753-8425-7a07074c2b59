/***************************************************************************
 * 
 * Copyright (c) 2009 Baidu.com, Inc. All Rights Reserved
 * $Id: pool.h,v 1.2 2009/03/09 04:56:41 xiaowei Exp $ 
 * 
 **************************************************************************/
 
 
 
/**
 * @file bslpool.h
 * <AUTHOR>
 * @date 2009/01/13 11:40:37
 * @version $Revision: 1.2 $ 
 * @brief 
 *  
 **/


#ifndef  __BSLPOOL_H_
#define  __BSLPOOL_H_


#include "pool/bsl_pool.h"
#include "pool/bsl_poolalloc.h"
#include "pool/bsl_debugpool.h"
#include "pool/bsl_xcompool.h"
#include "pool/bsl_xmempool.h"
#include "pool/bsl_cachedpool.h"


#endif  //__BSLPOOL_H_

/* vim: set ts=4 sw=4 sts=4 tw=100 noet: */
