/***************************************************************************
 * 
 * Copyright (c) 2009 Baidu.com, Inc. All Rights Reserved
 * $Id: ShallowCopyString.cpp,v 1.2 2009/06/15 06:29:04 chenxm Exp $ 
 * 
 **************************************************************************/
 
 
 
/**
 * @file ShallowCopyString.cpp
 * <AUTHOR>
 * @date 2009/05/10 22:52:45
 * @version $Revision: 1.2 $ 
 * @brief 
 *  
 **/

#include "ShallowCopyString.h"

namespace bsl{
//    const char ShallowCopyString::_S_EMPTY_CS[] = "";
}
/* vim: set ts=4 sw=4 sts=4 tw=100 */
