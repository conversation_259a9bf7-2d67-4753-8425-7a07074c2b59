HEADER = """
/***************************************************************************
 * 
 * Copyright (c) 2010 Baidu.com, Inc. All Rights Reserved
 * $Id: assign.py,v 1.2 2010/04/28 12:45:33 scmpf Exp $ 
 * 
 **************************************************************************/
 
 
 
/**
 * @file assign.h
 * <AUTHOR>
 * @date 2010/03/09 17:55:29
 * @version $Revision: 1.2 $ 
 * @brief this file is generated by assign.py. don't modify
 *  
 **/

#ifndef  __BSL_VAR_ASSIGN_H__
#define  __BSL_VAR_ASSIGN_H__
#include "bsl/var/IVar.h"

namespace bsl{ namespace var{"""

ARRAY_FUNC_DOC_HEADER = """
    /**
     * @brief assign an array with %d args
     *
     * @param [out] arr   : IVar&"""
ARRAY_FUNC_DOC_LOOP = """
     * @param [in] arg%d   : IVar&"""
ARRAY_FUNC_DOC_FOOTER = """
     * @return  IVar& 
     * @retval  arr
     * @see 
     * <AUTHOR>
     * @date 2010/03/09 18:01:53
    **/
"""
ARRAY_PARAMS_HEADER = """
    inline IVar& assign( IVar& arr"""
ARRAY_PARAMS_LOOP = """
        , IVar& arg%d"""
ARRAY_PARAMS_FOOTER = """
    ){"""

ARRAY_FUNC_HEADER = """
        arr.clear();"""
ARRAY_FUNC_LOOP = """
        arr.set(%d,arg%d);"""
ARRAY_FUNC_FOOTER = """
        return arr;
    }"""
FOOTER = """
}} //namespace bsl::var
#endif  //__BSL_VAR_ASSIGN_H__
/* vim: set ts=4 sw=4 sts=4 tw=100 */"""


DICT_FUNC_DOC_HEADER = """
    /**
     * @brief assign a dict with %d args
     *
     * @param [out] dict   : IVar&"""
DICT_FUNC_DOC_LOOP = """
     * @param [in] arg%d   : IVar&"""
DICT_FUNC_DOC_FOOTER = """
     * @return  IVar& 
     * @retval  dict
     * @see 
     * <AUTHOR>
     * @date 2010/03/09 18:01:53
    **/
"""
DICT_PARAMS_HEADER = """
    inline IVar& assign( IVar& dict"""
DICT_PARAMS_LOOP = """
        , const bsl::string& key%d, IVar& value%d"""
DICT_PARAMS_FOOTER = """
    ){"""

DICT_FUNC_HEADER = """
        dict.clear();"""
DICT_FUNC_LOOP = """
        dict.set(key%d,value%d);"""
DICT_FUNC_FOOTER = """
        return dict;
    }"""

FOOTER = """
}} //namespace bsl::var
#endif // __BSL_VAR_ASSIGN_H__
/* vim: set ts=4 sw=4 sts=4 tw=100 */"""

if __name__ == "__main__":
    code = [ HEADER ]
    #array
    for i in range(11): # 0~10 args
        #doc
        code.append(ARRAY_FUNC_DOC_HEADER % i)
        for j in range(i):
            code.append(ARRAY_FUNC_DOC_LOOP % j)
        code.append(ARRAY_FUNC_DOC_FOOTER)

        #params
        code.append(ARRAY_PARAMS_HEADER)
        for j in range(i):
            code.append(ARRAY_PARAMS_LOOP % j)
        code.append(ARRAY_PARAMS_FOOTER)

        #func
        code.append(ARRAY_FUNC_HEADER)
        for j in range(i):
            code.append(ARRAY_FUNC_LOOP % (j,j))
        code.append(ARRAY_FUNC_FOOTER)

    for i in range(1,11): # 1~10 args, 0 arg is the same as array
        #doc
        code.append(DICT_FUNC_DOC_HEADER % i)
        for j in range(i):
            code.append(DICT_FUNC_DOC_LOOP % j)
        code.append(DICT_FUNC_DOC_FOOTER)

        #params
        code.append(DICT_PARAMS_HEADER)
        for j in range(i):
            code.append(DICT_PARAMS_LOOP % (j,j))
        code.append(DICT_PARAMS_FOOTER)

        #func
        code.append(DICT_FUNC_HEADER)
        for j in range(i):
            code.append(DICT_FUNC_LOOP % (j,j))
        code.append(DICT_FUNC_FOOTER)
    code.append(FOOTER)
    print ''.join(code)
