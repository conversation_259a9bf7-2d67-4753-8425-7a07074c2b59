
/***************************************************************************
 * 
 * Copyright (c) 2010 Baidu.com, Inc. All Rights Reserved
 * $Id: assign.py,v 1.2 2010/04/28 12:45:33 scmpf Exp $ 
 * 
 **************************************************************************/
 
 
 
/**
 * @file assign.h
 * <AUTHOR>
 * @date 2010/03/09 17:55:29
 * @version $Revision: 1.2 $ 
 * @brief this file is generated by assign.py. don't modify
 *  
 **/

#ifndef  __BSL_VAR_ASSIGN_H__
#define  __BSL_VAR_ASSIGN_H__
#include "bsl/var/IVar.h"

namespace bsl{ namespace var{
    /**
     * @brief assign an array with 0 args
     *
     * @param [out] arr   : IVar&
     * @return  IVar& 
     * @retval  arr
     * @see 
     * <AUTHOR>
     * @date 2010/03/09 18:01:53
    **/

    inline IVar& assign( IVar& arr
    ){
        arr.clear();
        return arr;
    }
    /**
     * @brief assign an array with 1 args
     *
     * @param [out] arr   : IVar&
     * @param [in] arg0   : IVar&
     * @return  IVar& 
     * @retval  arr
     * @see 
     * <AUTHOR>
     * @date 2010/03/09 18:01:53
    **/

    inline IVar& assign( IVar& arr
        , IVar& arg0
    ){
        arr.clear();
        arr.set(0,arg0);
        return arr;
    }
    /**
     * @brief assign an array with 2 args
     *
     * @param [out] arr   : IVar&
     * @param [in] arg0   : IVar&
     * @param [in] arg1   : IVar&
     * @return  IVar& 
     * @retval  arr
     * @see 
     * <AUTHOR>
     * @date 2010/03/09 18:01:53
    **/

    inline IVar& assign( IVar& arr
        , IVar& arg0
        , IVar& arg1
    ){
        arr.clear();
        arr.set(0,arg0);
        arr.set(1,arg1);
        return arr;
    }
    /**
     * @brief assign an array with 3 args
     *
     * @param [out] arr   : IVar&
     * @param [in] arg0   : IVar&
     * @param [in] arg1   : IVar&
     * @param [in] arg2   : IVar&
     * @return  IVar& 
     * @retval  arr
     * @see 
     * <AUTHOR>
     * @date 2010/03/09 18:01:53
    **/

    inline IVar& assign( IVar& arr
        , IVar& arg0
        , IVar& arg1
        , IVar& arg2
    ){
        arr.clear();
        arr.set(0,arg0);
        arr.set(1,arg1);
        arr.set(2,arg2);
        return arr;
    }
    /**
     * @brief assign an array with 4 args
     *
     * @param [out] arr   : IVar&
     * @param [in] arg0   : IVar&
     * @param [in] arg1   : IVar&
     * @param [in] arg2   : IVar&
     * @param [in] arg3   : IVar&
     * @return  IVar& 
     * @retval  arr
     * @see 
     * <AUTHOR>
     * @date 2010/03/09 18:01:53
    **/

    inline IVar& assign( IVar& arr
        , IVar& arg0
        , IVar& arg1
        , IVar& arg2
        , IVar& arg3
    ){
        arr.clear();
        arr.set(0,arg0);
        arr.set(1,arg1);
        arr.set(2,arg2);
        arr.set(3,arg3);
        return arr;
    }
    /**
     * @brief assign an array with 5 args
     *
     * @param [out] arr   : IVar&
     * @param [in] arg0   : IVar&
     * @param [in] arg1   : IVar&
     * @param [in] arg2   : IVar&
     * @param [in] arg3   : IVar&
     * @param [in] arg4   : IVar&
     * @return  IVar& 
     * @retval  arr
     * @see 
     * <AUTHOR>
     * @date 2010/03/09 18:01:53
    **/

    inline IVar& assign( IVar& arr
        , IVar& arg0
        , IVar& arg1
        , IVar& arg2
        , IVar& arg3
        , IVar& arg4
    ){
        arr.clear();
        arr.set(0,arg0);
        arr.set(1,arg1);
        arr.set(2,arg2);
        arr.set(3,arg3);
        arr.set(4,arg4);
        return arr;
    }
    /**
     * @brief assign an array with 6 args
     *
     * @param [out] arr   : IVar&
     * @param [in] arg0   : IVar&
     * @param [in] arg1   : IVar&
     * @param [in] arg2   : IVar&
     * @param [in] arg3   : IVar&
     * @param [in] arg4   : IVar&
     * @param [in] arg5   : IVar&
     * @return  IVar& 
     * @retval  arr
     * @see 
     * <AUTHOR>
     * @date 2010/03/09 18:01:53
    **/

    inline IVar& assign( IVar& arr
        , IVar& arg0
        , IVar& arg1
        , IVar& arg2
        , IVar& arg3
        , IVar& arg4
        , IVar& arg5
    ){
        arr.clear();
        arr.set(0,arg0);
        arr.set(1,arg1);
        arr.set(2,arg2);
        arr.set(3,arg3);
        arr.set(4,arg4);
        arr.set(5,arg5);
        return arr;
    }
    /**
     * @brief assign an array with 7 args
     *
     * @param [out] arr   : IVar&
     * @param [in] arg0   : IVar&
     * @param [in] arg1   : IVar&
     * @param [in] arg2   : IVar&
     * @param [in] arg3   : IVar&
     * @param [in] arg4   : IVar&
     * @param [in] arg5   : IVar&
     * @param [in] arg6   : IVar&
     * @return  IVar& 
     * @retval  arr
     * @see 
     * <AUTHOR>
     * @date 2010/03/09 18:01:53
    **/

    inline IVar& assign( IVar& arr
        , IVar& arg0
        , IVar& arg1
        , IVar& arg2
        , IVar& arg3
        , IVar& arg4
        , IVar& arg5
        , IVar& arg6
    ){
        arr.clear();
        arr.set(0,arg0);
        arr.set(1,arg1);
        arr.set(2,arg2);
        arr.set(3,arg3);
        arr.set(4,arg4);
        arr.set(5,arg5);
        arr.set(6,arg6);
        return arr;
    }
    /**
     * @brief assign an array with 8 args
     *
     * @param [out] arr   : IVar&
     * @param [in] arg0   : IVar&
     * @param [in] arg1   : IVar&
     * @param [in] arg2   : IVar&
     * @param [in] arg3   : IVar&
     * @param [in] arg4   : IVar&
     * @param [in] arg5   : IVar&
     * @param [in] arg6   : IVar&
     * @param [in] arg7   : IVar&
     * @return  IVar& 
     * @retval  arr
     * @see 
     * <AUTHOR>
     * @date 2010/03/09 18:01:53
    **/

    inline IVar& assign( IVar& arr
        , IVar& arg0
        , IVar& arg1
        , IVar& arg2
        , IVar& arg3
        , IVar& arg4
        , IVar& arg5
        , IVar& arg6
        , IVar& arg7
    ){
        arr.clear();
        arr.set(0,arg0);
        arr.set(1,arg1);
        arr.set(2,arg2);
        arr.set(3,arg3);
        arr.set(4,arg4);
        arr.set(5,arg5);
        arr.set(6,arg6);
        arr.set(7,arg7);
        return arr;
    }
    /**
     * @brief assign an array with 9 args
     *
     * @param [out] arr   : IVar&
     * @param [in] arg0   : IVar&
     * @param [in] arg1   : IVar&
     * @param [in] arg2   : IVar&
     * @param [in] arg3   : IVar&
     * @param [in] arg4   : IVar&
     * @param [in] arg5   : IVar&
     * @param [in] arg6   : IVar&
     * @param [in] arg7   : IVar&
     * @param [in] arg8   : IVar&
     * @return  IVar& 
     * @retval  arr
     * @see 
     * <AUTHOR>
     * @date 2010/03/09 18:01:53
    **/

    inline IVar& assign( IVar& arr
        , IVar& arg0
        , IVar& arg1
        , IVar& arg2
        , IVar& arg3
        , IVar& arg4
        , IVar& arg5
        , IVar& arg6
        , IVar& arg7
        , IVar& arg8
    ){
        arr.clear();
        arr.set(0,arg0);
        arr.set(1,arg1);
        arr.set(2,arg2);
        arr.set(3,arg3);
        arr.set(4,arg4);
        arr.set(5,arg5);
        arr.set(6,arg6);
        arr.set(7,arg7);
        arr.set(8,arg8);
        return arr;
    }
    /**
     * @brief assign an array with 10 args
     *
     * @param [out] arr   : IVar&
     * @param [in] arg0   : IVar&
     * @param [in] arg1   : IVar&
     * @param [in] arg2   : IVar&
     * @param [in] arg3   : IVar&
     * @param [in] arg4   : IVar&
     * @param [in] arg5   : IVar&
     * @param [in] arg6   : IVar&
     * @param [in] arg7   : IVar&
     * @param [in] arg8   : IVar&
     * @param [in] arg9   : IVar&
     * @return  IVar& 
     * @retval  arr
     * @see 
     * <AUTHOR>
     * @date 2010/03/09 18:01:53
    **/

    inline IVar& assign( IVar& arr
        , IVar& arg0
        , IVar& arg1
        , IVar& arg2
        , IVar& arg3
        , IVar& arg4
        , IVar& arg5
        , IVar& arg6
        , IVar& arg7
        , IVar& arg8
        , IVar& arg9
    ){
        arr.clear();
        arr.set(0,arg0);
        arr.set(1,arg1);
        arr.set(2,arg2);
        arr.set(3,arg3);
        arr.set(4,arg4);
        arr.set(5,arg5);
        arr.set(6,arg6);
        arr.set(7,arg7);
        arr.set(8,arg8);
        arr.set(9,arg9);
        return arr;
    }
    /**
     * @brief assign a dict with 1 args
     *
     * @param [out] dict   : IVar&
     * @param [in] arg0   : IVar&
     * @return  IVar& 
     * @retval  dict
     * @see 
     * <AUTHOR>
     * @date 2010/03/09 18:01:53
    **/

    inline IVar& assign( IVar& dict
        , const bsl::string& key0, IVar& value0
    ){
        dict.clear();
        dict.set(key0,value0);
        return dict;
    }
    /**
     * @brief assign a dict with 2 args
     *
     * @param [out] dict   : IVar&
     * @param [in] arg0   : IVar&
     * @param [in] arg1   : IVar&
     * @return  IVar& 
     * @retval  dict
     * @see 
     * <AUTHOR>
     * @date 2010/03/09 18:01:53
    **/

    inline IVar& assign( IVar& dict
        , const bsl::string& key0, IVar& value0
        , const bsl::string& key1, IVar& value1
    ){
        dict.clear();
        dict.set(key0,value0);
        dict.set(key1,value1);
        return dict;
    }
    /**
     * @brief assign a dict with 3 args
     *
     * @param [out] dict   : IVar&
     * @param [in] arg0   : IVar&
     * @param [in] arg1   : IVar&
     * @param [in] arg2   : IVar&
     * @return  IVar& 
     * @retval  dict
     * @see 
     * <AUTHOR>
     * @date 2010/03/09 18:01:53
    **/

    inline IVar& assign( IVar& dict
        , const bsl::string& key0, IVar& value0
        , const bsl::string& key1, IVar& value1
        , const bsl::string& key2, IVar& value2
    ){
        dict.clear();
        dict.set(key0,value0);
        dict.set(key1,value1);
        dict.set(key2,value2);
        return dict;
    }
    /**
     * @brief assign a dict with 4 args
     *
     * @param [out] dict   : IVar&
     * @param [in] arg0   : IVar&
     * @param [in] arg1   : IVar&
     * @param [in] arg2   : IVar&
     * @param [in] arg3   : IVar&
     * @return  IVar& 
     * @retval  dict
     * @see 
     * <AUTHOR>
     * @date 2010/03/09 18:01:53
    **/

    inline IVar& assign( IVar& dict
        , const bsl::string& key0, IVar& value0
        , const bsl::string& key1, IVar& value1
        , const bsl::string& key2, IVar& value2
        , const bsl::string& key3, IVar& value3
    ){
        dict.clear();
        dict.set(key0,value0);
        dict.set(key1,value1);
        dict.set(key2,value2);
        dict.set(key3,value3);
        return dict;
    }
    /**
     * @brief assign a dict with 5 args
     *
     * @param [out] dict   : IVar&
     * @param [in] arg0   : IVar&
     * @param [in] arg1   : IVar&
     * @param [in] arg2   : IVar&
     * @param [in] arg3   : IVar&
     * @param [in] arg4   : IVar&
     * @return  IVar& 
     * @retval  dict
     * @see 
     * <AUTHOR>
     * @date 2010/03/09 18:01:53
    **/

    inline IVar& assign( IVar& dict
        , const bsl::string& key0, IVar& value0
        , const bsl::string& key1, IVar& value1
        , const bsl::string& key2, IVar& value2
        , const bsl::string& key3, IVar& value3
        , const bsl::string& key4, IVar& value4
    ){
        dict.clear();
        dict.set(key0,value0);
        dict.set(key1,value1);
        dict.set(key2,value2);
        dict.set(key3,value3);
        dict.set(key4,value4);
        return dict;
    }
    /**
     * @brief assign a dict with 6 args
     *
     * @param [out] dict   : IVar&
     * @param [in] arg0   : IVar&
     * @param [in] arg1   : IVar&
     * @param [in] arg2   : IVar&
     * @param [in] arg3   : IVar&
     * @param [in] arg4   : IVar&
     * @param [in] arg5   : IVar&
     * @return  IVar& 
     * @retval  dict
     * @see 
     * <AUTHOR>
     * @date 2010/03/09 18:01:53
    **/

    inline IVar& assign( IVar& dict
        , const bsl::string& key0, IVar& value0
        , const bsl::string& key1, IVar& value1
        , const bsl::string& key2, IVar& value2
        , const bsl::string& key3, IVar& value3
        , const bsl::string& key4, IVar& value4
        , const bsl::string& key5, IVar& value5
    ){
        dict.clear();
        dict.set(key0,value0);
        dict.set(key1,value1);
        dict.set(key2,value2);
        dict.set(key3,value3);
        dict.set(key4,value4);
        dict.set(key5,value5);
        return dict;
    }
    /**
     * @brief assign a dict with 7 args
     *
     * @param [out] dict   : IVar&
     * @param [in] arg0   : IVar&
     * @param [in] arg1   : IVar&
     * @param [in] arg2   : IVar&
     * @param [in] arg3   : IVar&
     * @param [in] arg4   : IVar&
     * @param [in] arg5   : IVar&
     * @param [in] arg6   : IVar&
     * @return  IVar& 
     * @retval  dict
     * @see 
     * <AUTHOR>
     * @date 2010/03/09 18:01:53
    **/

    inline IVar& assign( IVar& dict
        , const bsl::string& key0, IVar& value0
        , const bsl::string& key1, IVar& value1
        , const bsl::string& key2, IVar& value2
        , const bsl::string& key3, IVar& value3
        , const bsl::string& key4, IVar& value4
        , const bsl::string& key5, IVar& value5
        , const bsl::string& key6, IVar& value6
    ){
        dict.clear();
        dict.set(key0,value0);
        dict.set(key1,value1);
        dict.set(key2,value2);
        dict.set(key3,value3);
        dict.set(key4,value4);
        dict.set(key5,value5);
        dict.set(key6,value6);
        return dict;
    }
    /**
     * @brief assign a dict with 8 args
     *
     * @param [out] dict   : IVar&
     * @param [in] arg0   : IVar&
     * @param [in] arg1   : IVar&
     * @param [in] arg2   : IVar&
     * @param [in] arg3   : IVar&
     * @param [in] arg4   : IVar&
     * @param [in] arg5   : IVar&
     * @param [in] arg6   : IVar&
     * @param [in] arg7   : IVar&
     * @return  IVar& 
     * @retval  dict
     * @see 
     * <AUTHOR>
     * @date 2010/03/09 18:01:53
    **/

    inline IVar& assign( IVar& dict
        , const bsl::string& key0, IVar& value0
        , const bsl::string& key1, IVar& value1
        , const bsl::string& key2, IVar& value2
        , const bsl::string& key3, IVar& value3
        , const bsl::string& key4, IVar& value4
        , const bsl::string& key5, IVar& value5
        , const bsl::string& key6, IVar& value6
        , const bsl::string& key7, IVar& value7
    ){
        dict.clear();
        dict.set(key0,value0);
        dict.set(key1,value1);
        dict.set(key2,value2);
        dict.set(key3,value3);
        dict.set(key4,value4);
        dict.set(key5,value5);
        dict.set(key6,value6);
        dict.set(key7,value7);
        return dict;
    }
    /**
     * @brief assign a dict with 9 args
     *
     * @param [out] dict   : IVar&
     * @param [in] arg0   : IVar&
     * @param [in] arg1   : IVar&
     * @param [in] arg2   : IVar&
     * @param [in] arg3   : IVar&
     * @param [in] arg4   : IVar&
     * @param [in] arg5   : IVar&
     * @param [in] arg6   : IVar&
     * @param [in] arg7   : IVar&
     * @param [in] arg8   : IVar&
     * @return  IVar& 
     * @retval  dict
     * @see 
     * <AUTHOR>
     * @date 2010/03/09 18:01:53
    **/

    inline IVar& assign( IVar& dict
        , const bsl::string& key0, IVar& value0
        , const bsl::string& key1, IVar& value1
        , const bsl::string& key2, IVar& value2
        , const bsl::string& key3, IVar& value3
        , const bsl::string& key4, IVar& value4
        , const bsl::string& key5, IVar& value5
        , const bsl::string& key6, IVar& value6
        , const bsl::string& key7, IVar& value7
        , const bsl::string& key8, IVar& value8
    ){
        dict.clear();
        dict.set(key0,value0);
        dict.set(key1,value1);
        dict.set(key2,value2);
        dict.set(key3,value3);
        dict.set(key4,value4);
        dict.set(key5,value5);
        dict.set(key6,value6);
        dict.set(key7,value7);
        dict.set(key8,value8);
        return dict;
    }
    /**
     * @brief assign a dict with 10 args
     *
     * @param [out] dict   : IVar&
     * @param [in] arg0   : IVar&
     * @param [in] arg1   : IVar&
     * @param [in] arg2   : IVar&
     * @param [in] arg3   : IVar&
     * @param [in] arg4   : IVar&
     * @param [in] arg5   : IVar&
     * @param [in] arg6   : IVar&
     * @param [in] arg7   : IVar&
     * @param [in] arg8   : IVar&
     * @param [in] arg9   : IVar&
     * @return  IVar& 
     * @retval  dict
     * @see 
     * <AUTHOR>
     * @date 2010/03/09 18:01:53
    **/

    inline IVar& assign( IVar& dict
        , const bsl::string& key0, IVar& value0
        , const bsl::string& key1, IVar& value1
        , const bsl::string& key2, IVar& value2
        , const bsl::string& key3, IVar& value3
        , const bsl::string& key4, IVar& value4
        , const bsl::string& key5, IVar& value5
        , const bsl::string& key6, IVar& value6
        , const bsl::string& key7, IVar& value7
        , const bsl::string& key8, IVar& value8
        , const bsl::string& key9, IVar& value9
    ){
        dict.clear();
        dict.set(key0,value0);
        dict.set(key1,value1);
        dict.set(key2,value2);
        dict.set(key3,value3);
        dict.set(key4,value4);
        dict.set(key5,value5);
        dict.set(key6,value6);
        dict.set(key7,value7);
        dict.set(key8,value8);
        dict.set(key9,value9);
        return dict;
    }
}} //namespace bsl::var
#endif // __BSL_VAR_ASSIGN_H__
/* vim: set ts=4 sw=4 sts=4 tw=100 */
