/***************************************************************************
 * 
 * Copyright (c) 2009 Baidu.com, Inc. All Rights Reserved
 * $Id: implement.h,v 1.4 2010/04/28 12:45:33 scmpf Exp $ 
 * 
 **************************************************************************/
 
 
 
/**
 * @file implement.h
 * <AUTHOR>
 * @date 2009/01/09 18:19:10
 * @version $Revision: 1.4 $ 
 * @brief 
 *  
 **/
#ifndef  __BSL_VAR_IMPLEMENT_H__
#define  __BSL_VAR_IMPLEMENT_H__
#include "bsl/var/Null.h"
#include "bsl/var/Ref.h"
#include "bsl/var/Bool.h"
#include "bsl/var/Int32.h"
#include "bsl/var/Int64.h"
#include "bsl/var/BigInt.h"
#include "bsl/var/Double.h"
#include "bsl/var/Number.h"
#include "bsl/var/String.h"
#include "bsl/var/ShallowString.h"
#include "bsl/var/ShallowRaw.h"
#include "bsl/var/Array.h"
#include "bsl/var/Dict.h"
#include "bsl/var/Function.h"
#include "bsl/var/Method.h"
#include "bsl/var/MagicArray.h"
#include "bsl/var/MagicDict.h"
#include "bsl/var/MagicRef.h"

#endif  //__BSL_VAR_IMPLEMENT_H__

/* vim: set ts=4 sw=4 sts=4 tw=100 */
