#edit-mode: -*- python -*-
#coding:gbk

WORKROOT('../../../')

#gcc version, default 'gcc'
#COMPILER('gcc482')

#Preprocessor flags.
CPPFLAGS(r'-D_GNU_SOURCE -D__STDC_LIMIT_MACROS')
UTCPPFLAGS = '-DUNIT_TEST -fno-access-control -DMOCKVIRTUAL=virtual'

#C++ flags.
#CXXFLAGS('-std=c++11 -g -pipe -W -Wall -Werror -Wno-unused-variable -Wno-unused-parameter -Wno-unused-local-typedefs -Wno-write-strings -fPIC -finline-functions')
CXXFLAGS('-std=c++11 -g -pipe -W -Wall -Wno-unused-variable -Wno-unused-parameter -Wno-unused-local-typedefs -Wno-write-strings -fPIC -finline-functions')
# -I path
INCPATHS('. $OUT/include ../../../')

COMPILER("gcc12")
if GLOBAL_GCC_VERSION() == 'gcc82':
    CFLAGS('-Wno-error=implicit-function-declaration')
    CXXFLAGS('-Wno-error=deprecated-declarations')
    CXXFLAGS('-Wno-error=format-truncation')
    CXXFLAGS('-Wno-error=implicit-function-declaration')
    CXXFLAGS('-Wno-error=misleading-indentation')
    CXXFLAGS('-Wno-error=parentheses')
    CXXFLAGS('-Wno-error=reorder')
    CXXFLAGS('-Wno-error=sign-compare')
    CXXFLAGS('-Wno-error=unused-value')
if GLOBAL_GCC_VERSION() == 'gcc12':
    ENABLE_GLOBAL_FLAGS()
#link flags
LDFLAGS('-rdynamic -lpthread -lrt -lutil -lz -ldl -lbfd -liberty -lopcodes')

CONFIGS('baidu/base/ullib@stable')
CONFIGS('third-64/gflags@gflags_1-6-0-100_PD_BL')
CONFIGS('baidu/base/configure@stable')
CONFIGS('third-64/protobuf@protobuf_2-4-1-500_PD_BL')
CONFIGS("baidu/third-party/boost@boost_V1.70.0.3_GCC482_4U3_K3_GEN_PD_BL@git_tag")
CONFIGS('third-64/gtest@gtest_1-6-0-100_PD_BL')
CONFIGS('baidu/base/bmock@stable')
CONFIGS('baidu/base/fault@stable')
#CONFIGS('lib2-64/ml/tree-models@tree-models_2-0-1-0_PD_BL')
CONFIGS('baidu/base/tree-models@stable')
CONFIGS('baidu/anti/base-lib@master@git_branch')
CONFIGS('baidu/anti/log-parser-lib@stable')
CONFIGS('baidu/anti/themis-common-lib@stable')
CONFIGS('app/ecom/anti/dnn-prediction@dnn-prediction_1-0-0_BRANCH@BCLOUD')
#CONFIGS('baidu/anti/dnn-prediction@master@git_branch')
CONFIGS('app/ecom/fcr/model/blas@blas_1-0-0-0_PD_BL')
#CONFIGS('app/ecom/elib/ecommon-lib@ecommon-lib_1-1-14-392_PD_BL@BCLOUD')
CONFIGS('baidu/elib/ecommon-lib@stable')
CONFIGS('thirdsrc/anti-dnnpython@trunk@BCLOUD')
CONFIGS('baidu/anti/proto@stable')
CONFIGS('baidu/anti/io-lib@master@git_branch')
#CONFIGS('app/ecom/sp/worker/interface@worker-interface_1-0-26_BRANCH@BCLOUD')
CONFIGS('baidu/anti/interface@stable')
CONFIGS('baidu/base/baidu-rpc@stable')
CONFIGS("lib2-64/gbrank@gbrank_1-0-18-1_PD_BL")

# for xgboost
CONFIGS('baidu/third-party/xgboost@master@git_branch')
#nlpc
#CONFIGS('baidu/lib/nlpc-ernie-sim-slim@nlpc-ernie-sim-slim_1-0-2-4_gcc482_PD_BL@git_tag')
CONFIGS('lib2-64/nlpc/nlpc-lego@nlpc-lego_2-0-2-128177_PD_BL') 

#DELETE_AUTO_LIBS('$OUT_ROOT/public/sofa-pbrpc/output/lib/libsofa_pbrpc.a')
#release headers
HEADERS(GLOB('*/inc/*.h */dev/feature_accumulator_factory.h'), '$INC')

#release files except headers
OUTPUT('conf', '$OUT')

#.a
header_paths = GLOB('*/inc */dev')
user_sources = GLOB('*/dev/*.cpp')
StaticLibrary('feature-lib', 
        Sources(user_sources, IncludePaths(header_paths,'$OUT/include', '../../../')))

SharedLibrary('feature-lib', Sources(user_sources, IncludePaths(header_paths, '$OUT/include', '../../../')))

#ut
ut_sources=GLOB('*/unittest/*_test.cpp')
for test in ut_sources.split():
    path = os.path.splitext(test)[0]
    UTApplication(
        '../../' + path[0:path.rindex('/')] + '/bin' + path[path.rindex('/'):],
        Sources(test, IncludePaths(header_paths,'$OUT/include', '../../../'), CppFlags(UTCPPFLAGS)),
        Libraries('$OUT/lib/libfeature-lib.a'),
        UTOnServer(False)
    )
