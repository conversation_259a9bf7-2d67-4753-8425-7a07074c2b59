[@policy]
policy_id : 123456
policy_type : rule
[.@rule]
feature_id : 1111
rule_type : segment
gray_file : empty_gray
threshold : 20.0
punish_m : 10
punish_k : 1.0

[@policy]
policy_id : 123457
policy_type : rule
[.@rule]
feature_id : 1112
rule_type : ratio
gray_file : empty_gray
threshold : 0.2
remain : 10

[@policy]
policy_id : 123458
policy_type : rule
[.@rule]
feature_id : 1113
rule_type : distribution
gray_file : empty_gray
threshold : 2
count_threshold : 10
stand_prob : 0.3, 0.7
# max_diff chi_square_test chi_square_dis
func_type : max_diff

[@policy]
policy_id : 123459
policy_type : rule
[.@rule]
feature_id : 1114
rule_type : segment
gray_file : empty_gray
threshold : 10.0
punish_m : 10
punish_k : 1.0
[.@rule]
feature_id : 1115
rule_type : ratio
gray_file : empty_gray
threshold : 0.5
remain : 10

[@policy]
policy_id : 123460
policy_type : rule
[.@rule]
feature_id : 1112
rule_type : boolean
condition : true

[@policy]
policy_id : 20000
policy_type : dnn
conf_path : ./conf
model_conf: dnn.conf
slot_feature : 0,10000;1,10001;2,10002;3,10003;4,10004;5,10005;6,10006;7,10007;8,10008;9,10009;10,10010;11,10011;12,10012;13,10013;14,10014;15,10015;16,10016;17,10017;18,10018;19,10019;20,10020;21,10021;22,10022;23,10023;24,10024;25,10025;26,10026;27,10027;28,10028;29,10029;30,10030;31,10031;32,10032;33,10033;34,10034;35,10035;36,10036;37,10037;38,10038;39,10039;40,10040;41,10041;42,10042;43,10043;44,10044;45,10045;46,10046;47,10047;48,10048;49,10049;50,10050;51,10051;52,10052;53,10053;54,10054;55,10055;56,10056;57,10057;

[@policy]
policy_id : 20001
policy_type : gbdt
conf_path : ./conf
model_conf: gbdt.conf
slot_feature : 0,10000;1,10001;2,10002;3,10003;4,10004;5,10005;6,10006;7,10007;8,10008;9,10009;10,10010;11,10011;12,10012;13,10013;14,10014;15,10015;16,10016;17,10017;18,10018;19,10019;20,10020;21,10021;22,10022;23,10023;24,10024;25,10025;26,10026;27,10027;28,10028;29,10029;30,10030;31,10031;32,10032;33,10033;34,10034;35,10035;36,10036;37,10037;38,10038;39,10039;40,10040;

[@policy]
policy_id : 20002
policy_type : lr
conf_path : ./conf
model_conf: lr.conf
slot_feature : 0,10000;1,10001;2,10002;3,10003;4,10004;5,10005;6,10006;7,10007;8,10008;9,10009;10,10010;11,10011;12,10012;13,10013;14,10014;15,10015;16,10016;17,10017;18,10018;19,10019;20,10020;21,10021;22,10022;23,10023;24,10024;25,10025;26,10026;27,10027;28,10028;29,10029;30,10030;31,10031;32,10032;33,10033;34,10034;35,10035;36,10036;37,10037;38,10038;39,10039;40,10040;
