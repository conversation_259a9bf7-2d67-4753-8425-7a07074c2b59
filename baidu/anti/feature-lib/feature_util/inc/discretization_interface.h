// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// <AUTHOR>
// @brief

#ifndef ANTI_THEMIS_FEATURE_LIB_FEATURE_UTIL_INC_DISCRETIZATION_INTERFACE_H
#define ANTI_THEMIS_FEATURE_LIB_FEATURE_UTIL_INC_DISCRETIZATION_INTERFACE_H

#include <float.h>
#include <Configure.h>

namespace anti {
namespace themis {
namespace feature_lib {

class DiscretizationInterface {
public:
    DiscretizationInterface() {}
    virtual ~DiscretizationInterface() {}
    virtual bool init(const comcfg::ConfigUnit& conf) = 0;
    virtual int64_t discretize(double value) = 0;
    virtual double undo_discretize(const int64_t value) = 0;
};

class DivideDiscretization : public DiscretizationInterface {
public:
    DivideDiscretization() :
            _base_value(1.00000000),
            _max_value(DBL_MAX) {}
    virtual ~DivideDiscretization() {}
    virtual bool init(const comcfg::ConfigUnit& conf) override;
    virtual int64_t discretize(double value) override;
    virtual double undo_discretize(const int64_t value) override;
private:
    double _base_value;
    double _max_value;
};

class DiscretizationFactory {
public:
    static DiscretizationInterface* create(const std::string& type);
};

}
}
}
#endif // ANTI_THEMIS_FEATURE_LIB_FEATURE_UTIL_INC_DISCRETIZATION_INTERFACE_H
