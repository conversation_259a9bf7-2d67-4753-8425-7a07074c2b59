// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// @Author: ch<PERSON><PERSON><PERSON><PERSON>(ch<PERSON><PERSON><PERSON><PERSON>@baidu.com)
// 
// @File: distribution_distance_calculate.h
// @Last modified: 2016-12-28 14:17:20
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_FEATURE_UTIL_INC_DISTRIBUTION_DISTANCE_CALCULATE_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_FEATURE_UTIL_INC_DISTRIBUTION_DISTANCE_CALCULATE_H

#include <float.h>
#include <stdint.h>
#include <math.h>
#include <vector>

namespace anti {
namespace themis {
namespace feature_lib {

double max_diff(
        const std::vector<double>& pb,
        const std::vector<double>& pc,
        uint32_t bucket_idx);

double chi_square_test(
        const std::vector<double>& pb,
        const std::vector<double>& pc,
        uint32_t bucket_idx);

double chi_square_dis(
        const std::vector<double>& pb,
        const std::vector<double>& pc,
        uint32_t bucket_idx);

double kl_divergence(
        const std::vector<double>& pb,
        const std::vector<double>& pc,
        uint32_t bucket_idx);

} // namespace feature_lib
} // namespace themis
} // namespace anti 

#endif // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_FEATURE_UTIL_INC_DISTRIBUTION_DISTANCE_CALCULATE_H
/* vim: set ts=4 sw=4: */

