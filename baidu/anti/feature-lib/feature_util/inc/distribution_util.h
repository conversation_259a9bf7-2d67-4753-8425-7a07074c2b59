// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: ditribution_util.h
// @Last modified: 2017-12-26 14:08:26
// @Brief: 

#ifndef ANTI_THEMIS_FEATURE_LIB_FEATURE_UTIL_INC_DITRIBUTION_UTIL_H
#define ANTI_THEMIS_FEATURE_LIB_FEATURE_UTIL_INC_DITRIBUTION_UTIL_H

#include <string>
#include <vector>
#include <unordered_map>
#include <boost/lexical_cast.hpp>   
#include <boost/algorithm/string.hpp> 
#include <com_log.h>

namespace anti {
namespace themis {
namespace feature_lib {

class DistributionUtil {
public:
    static bool parse_intervals(
            const std::string& interval, 
            std::vector<std::pair<int64_t, int64_t>>* out);
    static bool get_interval_idx(
            const std::vector<std::pair<int64_t, int64_t>>& intervals,
            int64_t  coord,
            uint32_t* idx);
};

template <typename T>
bool parse_intervals(const std::string& str, std::vector<T>* intervals) {
    if (intervals == NULL) {
        CFATAL_LOG("Bug! input intervals is NULL");
        return false;
    }
    std::vector<std::string> points;
    boost::algorithm::split(points, str, boost::is_any_of(", "), boost::token_compress_on);
    for (size_t i = 0U; i < points.size(); ++i) {
        try {
            T cur = boost::lexical_cast<T>(points[i]);
            if (!intervals->empty() && cur <= intervals->back()) {
                CWARNING_LOG("invalid str(%s), intervals size(%lu)", str.data(), intervals->size());
                return false;
            }
            intervals->push_back(cur);
        } catch (const boost::bad_lexical_cast& e) {
            CWARNING_LOG("boost::Exception : %s", e.what());
            return false;
        }
    }
    return true;
}

template <typename T>
int get_interval_idx(const std::vector<T>& intervals, T cur) {
    // find the minimum index which intervals[index] >= cur
    // if all of intervals < cur return intervals.size()
    // e.g. intervals 1 3 5 7 9, cur = 5 => index = 2, cur = 10 => index = 5
    int index = intervals.size();
    int start = 0, end = intervals.size() - 1;
    while (start <= end) {
        int mid = start + (end - start) / 2;
        if (intervals[mid] >= cur) {
            index = mid;
            end = mid - 1;
        } else start = mid + 1;
    }
    return index;
}

class SignificanceTestUtil {
public:
    static double binomial_test(
            uint64_t occurrence_number, 
            uint64_t test_number, 
            double occurrence_prob);
    static double normality_test(
            double sample_mean, 
            double population_mean, 
            double sample_variance);
    // Chi-Square Testing
    // freedom_degree must be in range [3, 15]
    // pvalue must be in {0.01, 0.02, 0.03, 0.04, 0.05, 
    //     0.001, 0.005, 0.0001, 0.0005, 0.00001, 0.00005,
    //     0.000001, 0.000005, 0.0000001, 0.0000005}
    // Return chi-square value
    static double chi_square_test(
            uint64_t freedom_degree,
            double pvalue);

private:
    enum { 
        MAX_TABLE_SIZE = 101,
        MAX_FREEDOM_DEGREE = 15
    };
    static const double DEFAULT_SIGNIFICANCE_LEVEL;
    static const double DEFAULT_CHI_SQUARE_VALUE;
    // table[n] = log(n!)
    static const double FACTORIAL_LOGARITH_TABLE[MAX_TABLE_SIZE];
    static const std::unordered_map<double, double> CHI_SQUARE_DIS_TABLE[MAX_FREEDOM_DEGREE]; 
};

} // namespace feature_lib
} // namespace themis 
} // namespace anti

#endif // ANTI_THEMIS_FEATURE_LIB_FEATURE_UTIL_INC_DITRIBUTION_UTIL_H
/* vim: set ts=4 sw=4: */

