// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: feature_util.h
// @Last modified: 2018-03-23 15:12:43
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_FEATURE_UTIL_INC_FEATURE_UTIL_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_FEATURE_UTIL_INC_FEATURE_UTIL_H

#include <Configure.h>
#include "feature_value.pb.h"

namespace anti {
namespace themis {
namespace feature_lib {

bool parse_list(
        const comcfg::ConfigUnit& conf,
        const std::string& name,
        std::vector<std::string>* vec);

bool parse_list(const std::string& str, std::vector<std::string>* vec);

bool get_fea_double_value(const FeatureValueProto& fea, double *res); 

bool analyze_significance(const std::string& conf_str, double* res);

bool get_binomial_cnk_prob(double mu, double sigma, double significance,
        std::vector<int32_t>* threshold_vec);

std::string get_file_name(const std::string& file_path, 
        const std::string& prefix,  const std::string& version_id = "",  
        const std::string& record_type = "", const std::string& shard = "");
    
}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_FEATURE_UITL_INC_FEATURE_UTIL_H
/* vim: set ts=4 sw=4: */

