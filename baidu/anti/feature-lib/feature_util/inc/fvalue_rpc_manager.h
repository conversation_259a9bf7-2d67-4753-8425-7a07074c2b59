// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: fvalue_rpc_manager.h
// @Last modified: 2017-11-23 10:40:20
// @Brief: 

#ifndef BAIDU_ANTI_FEATURE_LIB_FEATURE_UTIL_INC_FVALUE_RPC_MANAGER_H
#define BAIDU_ANTI_FEATURE_LIB_FEATURE_UTIL_INC_FVALUE_RPC_MANAGER_H

#include <memory>
#include <string>
#include <vector>
#include <mutex>
#include <thread>
#include <com_log.h>
#include <Configure.h>
#include <baidu/rpc/partition_channel.h>
#include <feature_value.pb.h>
#include <lru_window.hpp>

namespace anti {
namespace themis {
namespace feature_lib {

class FValueClient;
class FValueRPCManager {
public:
    FValueRPCManager() : _status(EXIT) {}
    ~FValueRPCManager() { uninit(); }
    bool init(const char* path, const char* file) {
        if (path == nullptr || file == nullptr) {
            CWARNING_LOG("path[%p] or file[%p] is nullptr", path, file);
            return false;
        }
        comcfg::Configure conf;
        if (conf.load(path, file) != 0) {
            CWARNING_LOG("load conf(%s/%s) failed", path, file);
            return false;
        }
        return init(conf);
    }
    bool init(const comcfg::ConfigUnit& conf);
    void uninit();
    
    bool query(const std::shared_ptr<FValQueryTransProto>& tran);
    void sync();

private:
    void _run();

    std::mutex _mu;
    std::vector<std::shared_ptr<FValQueryTransProto>> _trans;

    std::shared_ptr<FValueClient> _client;
    enum Status {IDLE, BUSY, EXIT};
    Status _status;

    std::thread _thread;
};

class FValueClient {
public:
    FValueClient() : _stub(NULL), _cntls(NULL), _cache(NULL) {}
    ~FValueClient() { uninit(); }
    
    bool init(const char* path, const char* file) {
        comcfg::Configure conf;
        if (conf.load(path, file) != 0) {
            CWARNING_LOG("load conf(%s/%s) failed");
            return false;
        }
        return init(conf);
    }
    bool init(const comcfg::ConfigUnit& conf);
    void uninit();

    bool query(std::vector<std::shared_ptr<FValQueryTransProto>>* trans);

private:
    struct StatusInfo {
        StatusInfo() : 
                total_num(0U), 
                succ_num(0U), 
                fail_num(0U), 
                hit_cache(0U),
                latency(0U) {}
        std::string to_string() {
            char buf[1024] = {'\0'};
            snprintf(buf, sizeof(buf), "total_num:%lu|succ_num:%lu|fail_num:%lu|hit_cache:%lu|"
                    "latency:%lu", total_num, succ_num, fail_num, hit_cache, latency);
            return std::move(std::string(buf));
        }
        uint64_t total_num;
        uint64_t succ_num;
        uint64_t fail_num;
        uint64_t hit_cache;
        uint64_t latency;
    };
    void _sync(uint64_t batch_num, StatusInfo* status);

    baidu::rpc::PartitionChannel _chan;
    FValQueryService::Stub* _stub;
    std::vector<baidu::rpc::Controller>* _cntls;

private:
    const FValQResponseProto* _look_up_cache(const FValQRequestProto& freq);
    void _update_cache(const std::vector<std::shared_ptr<FValQueryTransProto>>& trans);
    struct FValReqHash {
        size_t operator() (const FValQRequestProto& x) const {
            return x.feature_id() ^ x.view_sign();
        }
    };
    struct FValReqEqual {
        bool operator() (const FValQRequestProto& l, const FValQRequestProto& r) const {
            return l.feature_id() == r.feature_id() && l.view_sign() == r.view_sign();
        }
    };
    typedef common_lib::NodeAdaptor<FValQRequestProto, FValQResponseProto> FVNode;
    typedef common_lib::LRUWindow<FValQRequestProto, FVNode, FValReqHash, FValReqEqual> FVCache;
    FVCache* _cache;
};

class TagsPartitionParser : public baidu::rpc::PartitionParser {
public:
    TagsPartitionParser(
            int partition_num,
            const std::string& key) : 
            _pnum(partition_num),
            _key(key) {}

    virtual bool ParseFromTag(const std::string& tag, baidu::rpc::Partition* out);

private:
    int _pnum;
    std::string _key;
};

class PartitionCallMapper : public baidu::rpc::CallMapper {
public:
    PartitionCallMapper(int partition_num) : _pnum(partition_num) {}
    virtual ~PartitionCallMapper() {};

    virtual baidu::rpc::SubCall Map(
            int channel_index,
            const google::protobuf::MethodDescriptor* method,
            const google::protobuf::Message* request,
            google::protobuf::Message* response);

private:
    int _pnum;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // BAIDU_ANTI_FEATURE_LIB_FEATURE_UTIL_INC_FVALUE_RPC_MANAGER_H

/* vim: set ts=4 sw=4: */

