// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: feature_util_test.cpp
// @Last modified: 2016-01-20 18:01:23
// @Brief: 

#include <gtest/gtest.h>
#include "feature_util.h"

namespace anti {
namespace themis {
namespace feature_lib {

class FeatureUtilTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {}
    virtual void TearDown() {}
};

TEST_F(FeatureUtilTestSuite, parse_list_from_string_case) {
    std::string str("123, 234,345");
    EXPECT_FALSE(parse_list(str, NULL));
    std::vector<std::string> vec;
    ASSERT_TRUE(parse_list(str, &vec));
    ASSERT_EQ(3U, vec.size());
    const char* exp[] = {"123", "234", "345"};
    for (uint32_t i = 0; i < vec.size(); ++i) {
        EXPECT_STREQ(exp[i], vec[i].data());
    }
}

TEST_F(FeatureUtilTestSuite, parse_list_from_array_case) {
    comcfg::Configure conf;
    ASSERT_TRUE(conf.load("./conf", "parse_list_test.conf") == 0);
    std::vector<std::string> vec;
    ASSERT_TRUE(parse_list(conf, "list", &vec));
    ASSERT_EQ(5U, vec.size());
    const char* exp_fea[] = {"4", "5", "1", "2", "3"};
    for (uint32_t i = 0; i < 5; ++i) {
        EXPECT_STREQ(exp_fea[i], vec[i].data());
    }
    vec.clear();
    
    ASSERT_TRUE(parse_list(conf, "xxx", &vec));
    EXPECT_EQ(0U, vec.size());
}

TEST_F(FeatureUtilTestSuite, get_binomial_cnk_prob_case) {
    std::vector<int32_t> vec;
    ASSERT_TRUE(get_binomial_cnk_prob(0.5, 0.1, 0.01, &vec));
}

TEST_F(FeatureUtilTestSuite, get_file_name) {
    ASSERT_EQ("aaa/test-prefix", get_file_name("aaa", "test-prefix"));
    ASSERT_EQ("aaa/test-prefix_test-versionid", get_file_name("aaa", "test-prefix", "test-versionid"));
    ASSERT_EQ("aaa/test-prefix_test-record-type_test-versionid", 
            get_file_name("aaa", "test-prefix", "test-versionid", "test-record-type"));
    ASSERT_EQ("aaa/test-prefix_test-record-type_test-versionid_0", 
            get_file_name("aaa", "test-prefix", "test-versionid", "test-record-type", "0"));
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti
/* vim: set ts=4 sw=4: */

