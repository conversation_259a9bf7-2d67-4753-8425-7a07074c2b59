// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// <AUTHOR>
// @brief

#include <gtest/gtest.h>
#include "discretization_interface.h"

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}

namespace anti {
namespace themis {
namespace feature_lib {

const std::string conf_path = "./conf";
const std::string conf_file = "discretization.conf";

class TestDivideDiscretizationSuite : public ::testing::Test {
public:
    TestDivideDiscretizationSuite() {}
    ~TestDivideDiscretizationSuite() {}

    virtual void SetUp() {}
    virtual void TearDown() {}

private:
    anti::themis::feature_lib::DivideDiscretization _obj;
};

TEST_F(TestDivideDiscretizationSuite, sample_test) {
    DiscretizationInterface* discretization = 
            DiscretizationFactory::create("error");
    EXPECT_TRUE(discretization == NULL);
    discretization = 
            DiscretizationFactory::create("divide");
    EXPECT_TRUE(discretization != NULL);
    comcfg::Configure cfg;
    ASSERT_EQ(0, cfg.load(conf_path.c_str(), conf_file.c_str()));

    EXPECT_TRUE(cfg["config"][1]["discretization"].selfType() ==
            comcfg::CONFIG_ERROR_TYPE);

    EXPECT_TRUE(discretization->init(cfg["config"][1]));
    EXPECT_TRUE(discretization->init(cfg["config"][2]["discretization"]));
    EXPECT_TRUE(discretization->init(cfg["config"][0]["discretization"]));
    EXPECT_EQ(132, discretization->discretize(1.327));
    EXPECT_EQ(12, discretization->discretize(0.122));
    EXPECT_EQ(0.12, discretization->undo_discretize(12));
    EXPECT_EQ(0.66, discretization->undo_discretize(66));
}

}
}
}
