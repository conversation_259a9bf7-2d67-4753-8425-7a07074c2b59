// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: fvalue_rpc_manager_test.cpp
// @Last modified: 2017-11-23 10:49:32
// @Brief: 

#include <gtest/gtest.h>
#include <bmock.h>
#include "fvalue_rpc_manager.h"

using ::testing::_;
using ::testing::Invoke;
using ::testing::Return;
using ::google::protobuf::RpcController;
using ::google::protobuf::Closure;

namespace anti {
namespace themis {
namespace feature_lib {

class TagsPartitionParserTestSuite : public ::testing::Test {
public:
    TagsPartitionParserTestSuite() : _obj(100, "xyz") {}
    ~TagsPartitionParserTestSuite() {}

protected:
    virtual void SetUp() {}
    virtual void TearDown() {}

private:
    TagsPartitionParser _obj;
};

TEST_F(TagsPartitionParserTestSuite, parse_from_tag_case) {
    EXPECT_STREQ("xyz", _obj._key.data());
    EXPECT_EQ(100, _obj._pnum);

    ASSERT_FALSE(_obj.ParseFromTag("xxxxxxx", NULL));

    baidu::rpc::Partition out;
    ASSERT_TRUE(_obj.ParseFromTag("xyz:14", &out));
    EXPECT_EQ(100, out.num_partition_kinds);
    EXPECT_EQ(14, out.index);
}

class PartitionCallMapperTestSuite : public ::testing::Test {
public:
    PartitionCallMapperTestSuite() : _obj(10) {}
    ~PartitionCallMapperTestSuite() {}

protected:
    virtual void SetUp() {}
    virtual void TearDown() {}

private:
    PartitionCallMapper _obj;
};

TEST_F(PartitionCallMapperTestSuite, map_case) {
    EXPECT_EQ(10, _obj._pnum);

    _obj._pnum = 0;
    ASSERT_TRUE(_obj.Map(0, NULL, NULL, NULL).is_bad());

    _obj._pnum = 10;
    ASSERT_TRUE(_obj.Map(0, NULL, NULL, NULL).is_bad());

    FValQRequestProto req;
    FValQResponseProto res;
    req.set_feature_id(1);
    req.set_view_sign(0);
    auto sc = _obj.Map(0, NULL, &req, &res);
    ASSERT_TRUE(sc.is_skip());

    sc = _obj.Map(1, NULL, &req, &res);
    ASSERT_TRUE(sc.request != NULL);
    ASSERT_TRUE(sc.response != NULL);
}

class FValueClientTestSuite : public ::testing::Test {
public:
    FValueClientTestSuite() {}
    ~FValueClientTestSuite() {}

protected:
    virtual void SetUp() {
        ASSERT_EQ(0, _conf.load("./conf", "fvalue_rpc_manager_test.conf"));
    }
    virtual void TearDown() {}

private:
    FValueClient _obj;
    comcfg::Configure _conf;
};

TEST_F(FValueClientTestSuite, construct_case) {
    EXPECT_TRUE(_obj._stub == NULL);
    EXPECT_TRUE(_obj._cache == NULL);
    EXPECT_TRUE(_obj._cntls == NULL);
}

TEST_F(FValueClientTestSuite, init_case) {
    ASSERT_FALSE(_obj.init("./conf", "xxx"));
    _obj.uninit();
    ASSERT_FALSE(_obj.init("./conf", "fvalue_rpc_manager_test.conf"));
    _obj.uninit();

    for (uint32_t i = 0U; i < _conf["invalid"].size(); ++i) {
        ASSERT_FALSE(_obj.init(_conf["invalid"][i])) << i;
        _obj.uninit();
    }
    ASSERT_TRUE(_obj.init(_conf["valid"]));
    EXPECT_TRUE(_obj._stub != NULL);
    EXPECT_TRUE(_obj._cache != NULL);
    EXPECT_TRUE(_obj._cntls != NULL);
    _obj.uninit();
}

void set_response(RpcController*, const FValQRequestProto* req, 
        FValQResponseProto* res, Closure*) {
    static uint32_t idx = 0;
    if (idx++ == 0) {
        return;
    }
    res->add_value(std::to_string(idx));
}

BMOCK_NS_CLASS_METHOD4(anti::themis::feature_lib, FValQueryService_Stub, Query, 
        void(RpcController*, const FValQRequestProto*, FValQResponseProto*, Closure*));
BMOCK_NS_CLASS_METHOD2(anti::themis::feature_lib, FValueClient, _sync, 
        void(uint64_t, FValueClient::StatusInfo*));

TEST_F(FValueClientTestSuite, query_case) {
    ASSERT_FALSE(_obj.query(NULL));
    
    std::vector<std::shared_ptr<FValQueryTransProto>> trans;
    ASSERT_FALSE(_obj.query(&trans));

    ASSERT_TRUE(_obj.init(_conf["valid"]));
    ASSERT_TRUE(_obj.query(&trans));

    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(anti::themis::feature_lib, FValQueryService_Stub, Query),
            Query(_, _, _, _)).WillRepeatedly(Invoke(set_response));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(anti::themis::feature_lib, FValueClient, _sync), 
            _sync(_, _)).WillRepeatedly(Return());
    std::shared_ptr<FValQueryTransProto> tran(new FValQueryTransProto);
    for (int32_t i = 0; i < 3; ++i) {
        auto t = tran->add_infos()->mutable_request();
        t->set_feature_id(i);
        t->set_view_sign(i);
    }
    trans.push_back(tran);
    {
        ASSERT_TRUE(_obj.query(&trans));
        int exp[] = {0 ,1, 1};
        std::string expv[] = {"", "2", "3"};
        for (int32_t i = 0; i < 3; ++i) {
            auto t = trans[0]->infos(i).response();
            EXPECT_EQ(exp[i], t.value_size());
            if (t.value_size() > 0) {
                EXPECT_STREQ(expv[i].data(), t.value(0).data());
            }
        }
    }
    std::vector<std::shared_ptr<FValQueryTransProto>> tb(trans);
    {
        ASSERT_TRUE(_obj.query(&tb));
        std::string expv[] = {"4", "2", "3"};
        for (int32_t i = 0; i < 3; ++i) {
            auto t = trans[0]->infos(i).response();
            EXPECT_EQ(1, t.value_size());
            EXPECT_STREQ(expv[i].data(), t.value(0).data());
        }
    }
}

class FValueRPCManagerTestSuite : public ::testing::Test {
public:
    FValueRPCManagerTestSuite() {}
    ~FValueRPCManagerTestSuite() {}
    virtual void SetUp() {
        ASSERT_EQ(0, _conf.load("./conf", "fvalue_rpc_manager_test.conf"));
    }
    virtual void TearDown() {}

private:
    FValueRPCManager _obj;
    comcfg::Configure _conf;
};

TEST_F(FValueRPCManagerTestSuite, init_case) {
    EXPECT_EQ(FValueRPCManager::EXIT, _obj._status);
    EXPECT_FALSE(_obj.init("./conf", "xxxxxxxx"));
    _obj.uninit();
    EXPECT_FALSE(_obj.init(_conf["invalid"][0]));
    _obj.uninit();
    EXPECT_TRUE(_obj.init(_conf["valid"]));
    _obj.uninit();
}

TEST_F(FValueRPCManagerTestSuite, query_case) {
    std::shared_ptr<FValQueryTransProto> tran;
    EXPECT_FALSE(_obj.query(tran));
    tran.reset(new FValQueryTransProto);
    for (int32_t i = 0; i < 3; ++i) {
        auto t = tran->add_infos()->mutable_request();
        t->set_feature_id(i);
        t->set_view_sign(i);
    }
    EXPECT_FALSE(_obj.query(tran));
    ASSERT_TRUE(_obj.init(_conf["valid"]));
    ASSERT_TRUE(_obj.query(tran));
    _obj.sync();
    for (int32_t i = 0; i < 3; ++i) {
        EXPECT_TRUE(tran->infos(i).has_response());
    }
    _obj.uninit();
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

