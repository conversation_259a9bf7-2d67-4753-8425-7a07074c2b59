// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: distribution_util_test.cpp
// @Last modified: 2017-12-20 18:59:36
// @Brief: 

#include <gtest/gtest.h>
#include "distribution_util.h"

namespace anti {
namespace themis {
namespace feature_lib {

TEST(DistributionUtilTestSuite, parse_intervals_case) {
    ASSERT_FALSE(parse_intervals<int>("123", NULL));
    {
        std::vector<int> points;
        ASSERT_FALSE(parse_intervals<int>("xxxx", &points));
    }
    {
        std::vector<int> points;
        ASSERT_FALSE(parse_intervals<int>("123, 9", &points));
    }
    {
        std::vector<int> points;
        ASSERT_TRUE(parse_intervals<int>("1, 9", &points));
        ASSERT_EQ(2U, points.size());
        EXPECT_EQ(1U, points[0]);
        EXPECT_EQ(9U, points[1]);
    }
}

TEST(DistributionUtilTestSuite, get_interval_idx_case) {
    {
        std::vector<int> points({1, 3, 5, 7, 9});
        int exp[] = {0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5};
        for (int i = 0; i <= 10; ++i) {
            EXPECT_EQ(exp[i], get_interval_idx<int>(points, i)) << i;
        }
    }
    {
        std::vector<double> points({0.2, 0.4, 0.6, 0.8});
        double curs[] = {0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9};
        int exp[] = {0, 0, 1, 1, 2, 2, 3, 3, 4};
        for (int i = 0; i <= 8; ++i) {
            EXPECT_EQ(exp[i], get_interval_idx<double>(points, curs[i])) << i;
        }
    }
}

TEST(SignificanceTestUtilTestSuite, binomial_test_case) {
    uint64_t test_number = 50;
    std::vector<uint64_t> occurrence_number = {10, 20, 30, 40};
    double occurrence_prob = 0.5;
    std::vector<double> stand_prob = {0.999997, 0.940539, 0.101319, 1.193066e-05};
    const double EPSILON = 0.000001;
    for (uint32_t i = 0; i < occurrence_number.size(); ++i) {
        EXPECT_GT(EPSILON, fabs(stand_prob[i] - SignificanceTestUtil::binomial_test(
                occurrence_number[i], test_number, occurrence_prob))) << i;
    }
    uint64_t test_number1 = 200;
    std::vector<uint64_t> occurrence_number1 = {60, 70, 80, 90};
    double occurrence_prob1 = 0.3;
    std::vector<double> stand_prob1 = {0.500000, 0.0614113, 0.00101412, 1.836287e-06};
    for (uint32_t i = 0; i < occurrence_number1.size(); ++i) {
        EXPECT_GT(FLT_EPSILON, fabs(stand_prob1[i] - SignificanceTestUtil::binomial_test(
                occurrence_number1[i], test_number1, occurrence_prob1))) << i;
    }
}

TEST(SignificanceTestUtilTestSuite, chi_square_test_case) {
    std::vector<uint64_t> freedom_degrees = {5, 7, 15};
    std::vector<double> pvals = {0.01, 0.03, 0.05};
    std::vector<double> chi_squares = {15.086, 12.375, 11.070, 18.475, 
            15.509, 14.067, 30.578, 26.848, 24.996};
    for (uint32_t i = 0; i < freedom_degrees.size(); ++i) {
        for (uint32_t j = 0; j < pvals.size(); ++j) {
            EXPECT_GT(FLT_EPSILON, fabs(chi_squares[i * pvals.size() + j] 
                    - SignificanceTestUtil::chi_square_test(freedom_degrees[i], pvals[j])));
        }
    }
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

