// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// <AUTHOR>
// @brief

#include "discretization_interface.h"
#include <com_log.h>

namespace anti {
namespace themis {
namespace feature_lib {

bool DivideDiscretization::init(const comcfg::ConfigUnit& conf) {
    try {
        if (conf["divide_num"].selfType() != comcfg::CONFIG_ERROR_TYPE) {
            _base_value = conf["divide_num"].to_double();
        }
        if (conf["max_value"].selfType() != comcfg::CONFIG_ERROR_TYPE) {
            _max_value = conf["max_value"].to_double();
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CFATAL_LOG("std::exception : %s", e.what());
        return false;
    }
    CWARNING_LOG("DivideDiscretization init succ! set divide_num:%f",
            _base_value);
    return true;
}

int64_t DivideDiscretization::discretize(double value) {
    value = value > _max_value ? _max_value : value;
    return static_cast<int64_t>(value / _base_value);
}

double DivideDiscretization::undo_discretize(int64_t value) {
    return std::min(value * _base_value, _max_value);
}

DiscretizationInterface* DiscretizationFactory::create(const std::string& type) {
    DiscretizationInterface* discretization = NULL;
    if (type == "divide") {
        discretization = new (std::nothrow) DivideDiscretization();
    } else {
        CFATAL_LOG("illegal discretization type:%s", type.c_str());
        return NULL;
    }
    return discretization;
}

}
}
}
