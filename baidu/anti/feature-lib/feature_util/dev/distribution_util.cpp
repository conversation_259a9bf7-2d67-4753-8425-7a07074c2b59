// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: ditribution_util.cpp
// @Last modified: 2017-12-20 15:50:38
// @Brief: 

#include <math.h>
#include "distribution_util.h"

namespace anti {
namespace themis {
namespace feature_lib {

bool DistributionUtil::parse_intervals( 
        const std::string& interval,
        std::vector<std::pair<int64_t, int64_t>>* out) {
    if (!out) {
        CFATAL_LOG("input out ptr is NULL");
        return false;
    }
    std::vector<std::string> point_strs;
    boost::algorithm::split(point_strs, interval, boost::is_any_of(", "), boost::token_compress_on);
    int64_t start = LLONG_MIN;
    for (size_t i = 0U; i < point_strs.size(); ++i) {
        try {
            int64_t end = boost::lexical_cast<int64_t>(point_strs[i]);
            out->push_back(std::pair<int64_t, int64_t>(start, end));
            start = end;
        } catch (const boost::bad_lexical_cast& e) {
            CWARNING_LOG("boost::Exception : %s", e.what());
            return false;
        }
    }
    out->push_back(std::pair<int64_t, int64_t>(start, LLONG_MAX));
    return true;
}
    
bool DistributionUtil::get_interval_idx(
        const std::vector<std::pair<int64_t, int64_t>>& intervals,
        int64_t coord, 
        uint32_t* idx) {
    if (!idx) {
        CFATAL_LOG("input idx ptr is NULL");
        return false;
    }
    int32_t start = 0;
    int32_t end = intervals.size() - 1;
    while (start <= end) {
        int32_t mid = start + (end - start) / 2;
        if (intervals[mid].first < coord && coord <= intervals[mid].second) {
            *idx = static_cast<uint32_t>(mid);
            return true;
        }
        if (coord <= intervals[mid].first) {
            end = mid - 1;
        } else {
            start = mid + 1;
        }
    }
    CWARNING_LOG("find intervals fail, call init first");
    return false;
}

const double SignificanceTestUtil::DEFAULT_SIGNIFICANCE_LEVEL = 1;
const double SignificanceTestUtil::DEFAULT_CHI_SQUARE_VALUE = DBL_MAX;
const double SignificanceTestUtil::FACTORIAL_LOGARITH_TABLE[MAX_TABLE_SIZE] = {
        0.000000, 0.000000, 0.693147, 1.791759, 3.178054, 4.787492,
        6.579251, 8.525161, 10.604603, 12.801827, 15.104413,
        17.502308, 19.987214, 22.552164, 25.191221, 27.899271,
        30.671860, 33.505073, 36.395445, 39.339884, 42.335616,
        45.380139, 48.471181, 51.606676, 54.784729, 58.003605,
        61.261702, 64.557539, 67.889743, 71.257039, 74.658236,
        78.092224, 81.557959, 85.054467, 88.580828, 92.136176,
        95.719695, 99.330612, 102.968199, 106.631760, 110.320640,
        114.034212, 117.771881, 121.533082, 125.317271, 129.123934,
        132.952575, 136.802723, 140.673924, 144.565744, 148.477767,
        152.409593, 156.360836, 160.331128, 164.320112, 168.327445,
        172.352797, 176.395848, 180.456291, 184.533829, 188.628173,
        192.739047, 196.866182, 201.009316, 205.168199, 209.342587,
        213.532241, 217.736934, 221.956442, 226.190548, 230.439044,
        234.701723, 238.978390, 243.268849, 247.572914, 251.890402,
        256.221136, 260.564941, 264.921650, 269.291098, 273.673124,
        278.067573, 282.474293, 286.893133, 291.323950, 295.766601,
        300.220949, 304.686857, 309.164194, 313.652830, 318.152640,
        322.663499, 327.185288, 331.717887, 336.261182, 340.815059,
        345.379407, 349.954118, 354.539086, 359.134205, 363.739376};

const std::unordered_map<double, double>
SignificanceTestUtil::CHI_SQUARE_DIS_TABLE[MAX_FREEDOM_DEGREE] = {
        {{0.01, 11.345}, {0.02, 9.837}, {0.03, 8.947}, {0.04, 8.311}, {0.05, 7.815}, 
        {0.001, 16.266}, {0.005, 12.838}, {0.0001, 21.108}, {0.0005, 17.730}, 
        {0.00001, 25.902}, {0.00005, 22.555}, {0.000001, 30.665}, {0.000005, 27.338}, 
        {0.0000001, 35.406}, {0.0000005, 32.094}}, {{0.01, 13.277}, {0.02, 11.668}, 
        {0.03, 10.712}, {0.04, 10.026}, {0.05, 9.488}, {0.001, 18.467}, {0.005, 14.860}, 
        {0.0001, 23.513}, {0.0005, 19.997}, {0.00001, 28.473}, {0.00005, 25.013}, 
        {0.000001, 33.377}, {0.000005, 29.954}, {0.0000001, 38.240}, {0.0000005, 34.844}},
        {{0.01, 15.086}, {0.02, 13.388}, {0.03, 12.375}, {0.04, 11.644}, {0.05, 11.070}, 
        {0.001, 20.515}, {0.005, 16.750}, {0.0001, 25.745}, {0.0005, 22.105}, {0.00001, 30.856}, 
        {0.00005, 27.294}, {0.000001, 35.888}, {0.000005, 32.378}, {0.0000001, 40.863}, 
        {0.0000005, 37.391}}, {{0.01, 16.812}, {0.02, 15.033}, {0.03, 13.968}, {0.04, 13.198}, 
        {0.05, 12.592}, {0.001, 22.458}, {0.005, 18.548}, {0.0001, 27.856}, {0.0005, 24.103}, 
        {0.00001, 33.107}, {0.00005, 29.450}, {0.000001, 38.258}, {0.000005, 34.667}, 
        {0.0000001, 43.338}, {0.0000005, 39.794}}, {{0.01, 18.475}, {0.02, 16.622}, 
        {0.03, 15.509}, {0.04, 14.703}, {0.05, 14.067}, {0.001, 24.322}, {0.005, 20.278}, 
        {0.0001, 29.878}, {0.0005, 26.018}, {0.00001, 35.259}, {0.00005, 31.512}, 
        {0.000001, 40.522}, {0.000005, 36.854}, {0.0000001, 45.700}, {0.0000005, 42.088}},
        {{0.01, 20.090}, {0.02, 18.168}, {0.03, 17.010}, {0.04, 16.171}, {0.05, 15.507}, 
        {0.001, 26.124}, {0.005, 21.955}, {0.0001, 31.828}, {0.0005, 27.868}, {0.00001, 37.332}, 
        {0.00005, 33.502}, {0.000001, 42.701}, {0.000005, 38.960}, {0.0000001, 47.972}, 
        {0.0000005, 44.297}}, {{0.01, 21.666}, {0.02, 19.679}, {0.03, 18.480}, {0.04, 17.608}, 
        {0.05, 16.919}, {0.001, 27.877}, {0.005, 23.589}, {0.0001, 33.720}, {0.0005, 29.666}, 
        {0.00001, 39.341}, {0.00005, 35.431}, {0.000001, 44.811}, {0.000005, 41.001}, 
        {0.0000001, 50.172}, {0.0000005, 46.435}}, {{0.01, 23.209}, {0.02, 21.161}, 
        {0.03, 19.922}, {0.04, 19.021}, {0.05, 18.307}, {0.001, 29.588}, {0.005, 25.188}, 
        {0.0001, 35.564}, {0.0005, 31.420}, {0.00001, 41.296}, {0.00005, 37.311}, 
        {0.000001, 46.863}, {0.000005, 42.987}, {0.0000001, 52.310}, {0.0000005, 48.514}},
        {{0.01, 24.725}, {0.02, 22.618}, {0.03, 21.342}, {0.04, 20.412}, {0.05, 19.675}, 
        {0.001, 31.264}, {0.005, 26.757}, {0.0001, 37.367}, {0.0005, 33.137}, {0.00001, 43.206}, 
        {0.00005, 39.148}, {0.000001, 48.866}, {0.000005, 44.926}, {0.0000001, 54.395}, 
        {0.0000005, 50.542}}, {{0.01, 26.217}, {0.02, 24.054}, {0.03, 22.742}, {0.04, 21.785}, 
        {0.05, 21.026}, {0.001, 32.909}, {0.005, 28.300}, {0.0001, 39.134}, {0.0005, 34.821}, 
        {0.00001, 45.076}, {0.00005, 40.948}, {0.000001, 50.825}, {0.000005, 46.824}, 
        {0.0000001, 56.434}, {0.0000005, 52.527}}, {{0.01, 27.688}, {0.02, 25.472}, {0.03, 24.125}, 
        {0.04, 23.142}, {0.05, 22.362}, {0.001, 34.528}, {0.005, 29.819}, {0.0001, 40.871}, 
        {0.0005, 36.478}, {0.00001, 46.912}, {0.00005, 42.715}, {0.000001, 52.747}, 
        {0.000005, 48.687}, {0.0000001, 58.432}, {0.0000005, 54.472}}, {{0.01, 29.141}, 
        {0.02, 26.873}, {0.03, 25.493}, {0.04, 24.485}, {0.05, 23.685}, {0.001, 36.123}, 
        {0.005, 31.319}, {0.0001, 42.579}, {0.0005, 38.109}, {0.00001, 48.716}, {0.00005, 44.454},
        {0.000001, 54.635}, {0.000005, 50.518}, {0.0000001, 60.395}, {0.0000005, 56.384}},
        {{0.01, 30.578}, {0.02, 28.259}, {0.03, 26.848}, {0.04, 25.816}, {0.05, 24.996}, 
        {0.001, 37.697}, {0.005, 32.801}, {0.0001, 44.263}, {0.0005, 39.719}, {0.00001, 50.493}, 
        {0.00005, 46.168}, {0.000001, 56.493}, {0.000005, 52.320}, {0.0000001, 62.326}, 
        {0.0000005, 58.265}}};

double SignificanceTestUtil::binomial_test(
        uint64_t occurrence_number,
        uint64_t test_number,
        double occurrence_prob) {
    if (test_number <= occurrence_number 
            || fabs(occurrence_prob - DEFAULT_SIGNIFICANCE_LEVEL) <= FLT_EPSILON) {
        return DEFAULT_SIGNIFICANCE_LEVEL;
    }
    if (test_number >= MAX_TABLE_SIZE - 1) {
        double population_mean = test_number * occurrence_prob;
        double sample_variance = population_mean * (1 - occurrence_prob);
        return SignificanceTestUtil::normality_test(
                occurrence_number, 
                population_mean, 
                sample_variance);
    }
    double pval = 0;
    double log_occur_prob = log(occurrence_prob);
    double log_nonoccur_prob = log(1 - occurrence_prob);
    for (uint64_t it = occurrence_number; it <= test_number; ++it) {
        double logarith_pval = FACTORIAL_LOGARITH_TABLE[test_number]
                - FACTORIAL_LOGARITH_TABLE[it]
                - FACTORIAL_LOGARITH_TABLE[test_number - it]
                + it * log_occur_prob
                + (test_number - it) * log_nonoccur_prob;
        pval += exp(logarith_pval);
    }
    return pval;
}

double SignificanceTestUtil::normality_test(
        double sample_mean,
        double population_mean,
        double sample_variance) {
    double normal_variable = (sample_mean - population_mean) / sqrt(sample_variance);
    return 1 - erfc(-1 * normal_variable / sqrt(2)) * 0.5;   
}

double SignificanceTestUtil::chi_square_test(uint64_t freedom_degree, double pvalue) {
    if (freedom_degree < 3 || freedom_degree > MAX_FREEDOM_DEGREE) {
        return DEFAULT_CHI_SQUARE_VALUE; 
    }
    auto it = CHI_SQUARE_DIS_TABLE[freedom_degree - 3].find(pvalue);
    return it == CHI_SQUARE_DIS_TABLE[freedom_degree - 3].end() 
            ? DEFAULT_CHI_SQUARE_VALUE : it->second;
}

} // namespace feature_lib
} // namespace themis
} // namespace anti

/* vim: set ts=4 sw=4: */

