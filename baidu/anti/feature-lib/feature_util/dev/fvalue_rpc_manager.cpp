// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: fvalue_rpc_manager.cpp
// @Last modified: 2018-10-09 15:29:35
// @Brief: 

#include "fvalue_rpc_manager.h"
#include <baidu/rpc/global.h>
#include <base/memory/singleton_on_pthread_once.h>

using baidu::rpc::SubCall;
using baidu::rpc::PartitionChannelOptions;
using baidu::rpc::PartitionChannel;
using baidu::rpc::Controller;

namespace anti {
namespace themis {
namespace feature_lib {

bool FValueRPCManager::init(const comcfg::ConfigUnit& conf) {
    if (_client) {
        CFATAL_LOG("client has been inited");
        return false;
    }
    _client.reset(new(std::nothrow) FValueClient);
    if (!_client || !_client->init(conf)) {
        CWARNING_LOG("new or init FValueClient failed");
        return false;
    }
    _status = IDLE;
    _thread = std::thread(&FValueRPCManager::_run, this);
    return true;
}

void FValueRPCManager::uninit() {
    sync();
    _status = EXIT;
    if (_thread.joinable()) {
        _thread.join();
    }
    if (_client) {
        _client->uninit();
        _client.reset();
    }
}

bool FValueRPCManager::query(const std::shared_ptr<FValQueryTransProto>& tran) {
    if (!tran || !_client) {
        CFATAL_LOG("input FValQueryTransProto is empty or never call init before");
        return false;
    }
    std::unique_lock<std::mutex> lock(_mu);
    _trans.push_back(tran);
    return true;
}

void FValueRPCManager::sync() {
    while (true) {
        {
            std::unique_lock<std::mutex> lock(_mu);
            if (_status != BUSY && _trans.empty()) {
                break;
            }
        }
        usleep(50000);
    }
}

void FValueRPCManager::_run() {
    if (!_client) {
        CFATAL_LOG("call FValueRPCManager init first");
        return;
    }
    while (_status != EXIT || !_trans.empty()) {
        if (_trans.empty()) {
            usleep(50000);
            continue;
        }
        std::vector<std::shared_ptr<FValQueryTransProto>> trans;
        {
            std::unique_lock<std::mutex> lock(_mu);
            trans.swap(_trans);
            _status = BUSY;
        }
        _client->query(&trans);
        {
            std::unique_lock<std::mutex> lock(_mu);
            _status = IDLE;
        }
    }
}

bool FValueClient::init(const comcfg::ConfigUnit& conf) {
    try {
        ::baidu::rpc::GlobalInitializeOrDie();
        PartitionChannelOptions op;
        if (conf["connect_timeout_ms"].selfType() != comcfg::CONFIG_ERROR_TYPE) {
            op.connect_timeout_ms = conf["connect_timeout_ms"].to_int32();
        }

        if (conf["rw_timeout_ms"].selfType() != comcfg::CONFIG_ERROR_TYPE) {
            op.timeout_ms = conf["rw_timeout_ms"].to_int32();
        }

        if (conf["max_retry"].selfType() != comcfg::CONFIG_ERROR_TYPE) {
            op.max_retry = conf["max_retry"].to_int32();
        }

        if (conf["connection_type"].selfType() != comcfg::CONFIG_ERROR_TYPE) {
            op.connection_type = conf["connection_type"].to_cstr();
        }
        
        if (conf["fail_limit"].selfType() != comcfg::CONFIG_ERROR_TYPE) {
            op.fail_limit = conf["fail_limit"].to_int32();
        }

        int partition_num = conf["shard_num"].to_int32();
        op.call_mapper = new PartitionCallMapper(partition_num);
        if (_chan.Init(partition_num,
                new TagsPartitionParser(partition_num, conf["tag_key"].to_cstr()),
                conf["service"].to_cstr(), conf["load_balancer"].to_cstr(), &op) != 0) {
            CWARNING_LOG("init PartitionChannel failed, please check conf");
            return false;
        }
        _stub = new FValQueryService::Stub(static_cast<::google::protobuf::RpcChannel*>(&_chan));
        if (conf["cache_limit"].selfType() != comcfg::CONFIG_ERROR_TYPE) {
            const int64_t DEFAULT_EXP = 3600; // in second
            int64_t expiration = conf["cache_expiration"].selfType() != comcfg::CONFIG_ERROR_TYPE ?
                    conf["cache_expiration"].to_int64() : DEFAULT_EXP; 
            _cache = new FVCache(expiration, conf["cache_limit"].to_int64());
        }
        _cntls = new std::vector<Controller>(conf["sub_channel_num"].to_uint32());
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("comcfg::ConfigException : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std::exception : %s", e.what());
        return false;
    }
    return true;
}

void FValueClient::uninit() {
    if (_cache != NULL) {
        delete _cache;
        _cache = NULL;
    }

    if (_stub != NULL) {
        delete _stub;
        _stub = NULL;
    }

    if (_cntls != NULL) {
        delete _cntls;
        _cntls = NULL;
    }
}

class DoNothingClosure : public ::google::protobuf::Closure {
    void Run() {}
};
google::protobuf::Closure* DoNothing() { 
    return base::get_leaky_singleton<DoNothingClosure>();
}

bool FValueClient::query(std::vector<std::shared_ptr<FValQueryTransProto>>* trans) {
    if (trans == NULL || _stub == NULL || _cntls == NULL) {
        CFATAL_LOG("input trans is NULL or never call init before, please check code");
        return false;
    }
    static uint64_t logid = 0;
    uint64_t cidx = 0U;
    StatusInfo status;
    for (auto iter = trans->begin(); iter != trans->end(); ++iter) {
        status.total_num += (*iter)->infos_size();
        for (int i = 0; i < (*iter)->infos_size(); ++i) {
            auto info = (*iter)->mutable_infos(i);
            auto tmp = _look_up_cache(info->request());
            if (tmp) {
                info->mutable_response()->CopyFrom(*tmp);
                ++status.hit_cache;
                continue;
            }
            (*_cntls)[cidx].set_log_id(++logid);
            _stub->Query(
                    &((*_cntls)[cidx]), 
                    &info->request(), 
                    info->mutable_response(), 
                    DoNothing());
            if (++cidx == _cntls->size()) {
                _sync(cidx, &status);
                cidx = 0;
            }
        }
    }
    _sync(cidx, &status);
    _update_cache(*trans);
    CDEBUG_LOG("query info %s", status.to_string().data());
    return true;
}

const FValQResponseProto* FValueClient::_look_up_cache(const FValQRequestProto& freq) {
    if (_cache == NULL) {
        return NULL;
    }
    auto p = _cache->find(freq);
     if (!p) {
        return NULL;
    }
    return &(p->value());
}

void FValueClient::_sync(uint64_t batch_num, StatusInfo* status) {
    batch_num = std::min(batch_num, _cntls->size());
    for (uint64_t i = 0; i < batch_num; ++i) {
        baidu::rpc::Join((*_cntls)[i].call_id());
        if ((*_cntls)[i].Failed()) {
            CDEBUG_LOG("call Query failed, logid(%lu), ErrStr(%s), ErrNo(%d)", 
                    (*_cntls)[i].log_id(), 
                    (*_cntls)[i].ErrorText().data(), 
                    (*_cntls)[i].ErrorCode());
            ++status->fail_num;
            continue;
        }
        ++status->succ_num;
        status->latency += (*_cntls)[i].latency_us();
    }
    for (uint64_t i = 0; i < batch_num; ++i) {
        (*_cntls)[i].Reset();
    }
}

void FValueClient::_update_cache(const std::vector<std::shared_ptr<FValQueryTransProto>>& trans) {
    if (_cache == NULL) {
        return;
    }
    for (uint32_t i = 0; i < trans.size(); ++i) {
        for (int j = 0; j < trans[i]->infos_size(); ++j) {
            auto& info = trans[i]->infos(j);
            auto res = _look_up_cache(info.request());
            if (res || info.response().value_size() == 0) {
                continue;
            }
            _cache->insert(FVNode(info.request(), info.response(), time(NULL)));
        }
    }
    return;
}

bool TagsPartitionParser::ParseFromTag(const std::string& tag, baidu::rpc::Partition* out) {
    size_t pos = tag.find(_key + ":");
    if (pos == std::string::npos) {
        CFATAL_LOG("invalid tag(%s)", tag.data());
        return false;
    }
    out->index = std::stoi(tag.substr(pos + _key.length() + 1));
    out->num_partition_kinds = _pnum;
    return true;
}

SubCall PartitionCallMapper::Map(
        int channel_index,
        const google::protobuf::MethodDescriptor* method,
        const google::protobuf::Message* request,
        google::protobuf::Message* response) {
    if (_pnum <= 0) {
        CFATAL_LOG("invalid partition num(%d)", _pnum);
        return SubCall::Bad();
    }
    const FValQRequestProto* req = dynamic_cast<const FValQRequestProto*>(request);
    if (req == NULL) {
        CFATAL_LOG("dynamic request to FValQRequestProto failed");
        return SubCall::Bad();
    }
        
    if ((req->feature_id() ^ req->view_sign()) % _pnum 
                != static_cast<unsigned int>(channel_index)) {
        return SubCall::Skip();
    }
    return SubCall(method, request, response->New(), baidu::rpc::DELETE_RESPONSE);
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

