// Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
// @Author: wangkaikai(<EMAIL>)
// 
// @Last modified: 2017-02-17 14:07:20
// @Brief: 

#include "distribution_distance_calculate.h"

namespace anti {
namespace themis {
namespace feature_lib {

double max_diff(
        const std::vector<double>& pb,
        const std::vector<double>& pc,
        uint32_t bucket_idx) {
    return  pb[bucket_idx] <= DBL_EPSILON
            ? DBL_MIN_EXP : (pc[bucket_idx] - pb[bucket_idx]) / pb[bucket_idx];
}

double chi_square_test(
        const std::vector<double>& pb,
        const std::vector<double>& pc,
        uint32_t bucket_idx) {
    double s = 0.0;
    for (uint32_t i = 0; i < pb.size(); ++i) {
        double tmp = (pc[i] - pb[i]) / pb[i];
        tmp *= pow(1.8, pb[i] * 10);
        tmp = pow(tmp, 4);
        s += tmp;
    }
    return s;
}

double chi_square_dis(
        const std::vector<double>& pb,
        const std::vector<double>& pc,
        uint32_t bucket_idx) {
    double s = 0.0;
    for (uint32_t i = 0; i < pb.size(); ++i) {
        double tmp = (pc[i] - pb[i]) * (pc[i] - pb[i]);
        tmp /= pb[i];
        s += tmp;
    }
    return s;
}

double kl_divergence(
        const std::vector<double>& pb,
        const std::vector<double>& pc,
        uint32_t bucket_idx) {
    double s = 0.0;
    for (uint32_t i = 0; i < pb.size(); ++i) {
        if (pc[i] <= FLT_EPSILON) {
            continue;
        }
        double tmp = log2(pc[i]) - log2(pb[i]);
        tmp *= pc[i];
        s += tmp;
    }
    return s;
}

} // namespace feature_lib
} // namespace themis
} // namespace anti 

/* vim: set ts=4 sw=4: */

