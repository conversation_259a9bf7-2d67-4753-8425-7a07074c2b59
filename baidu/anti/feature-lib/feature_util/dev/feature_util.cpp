// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: feature_util.cpp
// @Last modified: 2018-03-23 17:56:01
// @Brief: 

#include <math.h>
#include <boost/algorithm/string.hpp>
#include <boost/lexical_cast.hpp>
#include "com_log.h"
#include "feature_util.h"

namespace anti {
namespace themis {
namespace feature_lib {

bool parse_list(
    const comcfg::ConfigUnit& conf,
    const std::string& name,
    std::vector<std::string>* vec) {
    if (vec == NULL) {
        CWARNING_LOG("input name or vec is NULL");
        return false;
    }
    for (uint32_t i = 0; i < conf[name.data()].size(); ++i) {
        parse_list(std::string(conf[name.data()][i].to_cstr()), vec);
    }
    return true;
}

bool parse_list(const std::string& str, std::vector<std::string>* vec) {
    if (vec == NULL) {
        CFATAL_LOG("input vec is NULL");
        return false;
    }
    std::vector<std::string> tmp_vec;
    boost::split(tmp_vec, str,
            boost::is_any_of(", "), boost::token_compress_on);
    vec->insert(vec->begin(), tmp_vec.begin(), tmp_vec.end());
    return true;
}

bool get_fea_double_value(const FeatureValueProto& fea, double *res) {
    if (res == NULL || !fea.has_value()) {
        CWARNING_LOG("invalid fea, feaid(%lu)", fea.feature_id());
        return false;
    }
    // get fea value
    try {
        *res = boost::lexical_cast<double>(fea.value());
    } catch (const boost::bad_lexical_cast& e) {
        CWARNING_LOG("invalid fea value, feaid(%lu)", fea.feature_id());
        return false;
    }
    return true;
}

std::string prob_debug_str(const std::vector<double>& prob) {
    std::stringstream ss;
    for (auto& p : prob) {
        ss << p << ",";
    }
    return ss.str();
}

/*
 * conf_str: significance[,uniq_view_number,exponent]
 * return: significance / (uniq_view_number ** exponent)
 * default: uniq_view_number = 1; exponent = 0.7
 * example:
 *      0.01,10000,0.3
 *      return 0.01/(10000 ** 0.3)
 */
bool analyze_significance(const std::string& conf_str, double* res) {
    if (conf_str == "") {
        CWARNING_LOG("signifrcance empty!");
        return false;
    }
    std::vector<std::string> fields;
    boost::split(fields, conf_str, boost::is_any_of(" ,"),
            boost::token_compress_on);
    if (fields.size() == 0) {
        CWARNING_LOG("part size = 0, invalid conf_str:%s",
                conf_str.data());
        return false;
    }
    try {
        double significance = boost::lexical_cast<double>(fields[0]);
        int64_t uniq_view_number = fields.size() >= 2 ?
                boost::lexical_cast<int64_t>(fields[1]) : 1;
        double exponent = fields.size() >= 3 ?
                boost::lexical_cast<double>(fields[2]) : 0.7;
        *res = significance / std::pow(uniq_view_number, exponent);
        CWARNING_LOG("analyze significance: significance=%f,"
                "uniq_view_number=%d,exponent=%f;"
                " final significance=%e", significance,
                uniq_view_number, exponent, *res);
    } catch (const boost::bad_lexical_cast& e) {
        CWARNING_LOG("invalid transform:%s, Error:%s",
                conf_str.data(), e.what());
        return false;
    }
    return true;
}

/*
 * 单次事件发生概率为mu, binomial(n, k, mu) 代表这个事件n次发生k次概率值
 * binomial(n, k, mu) = (n choose k) * mu^k * (1-mu)^(n-k)
 * binomial(n, k, mu) = binomial(n-1, k, mu) * n/(n-k) * (1-mu)
 * binomial(n, n, mu) = binomial(n-1, n-1, mu) * mu
 * .
 * .
 * .
 * binomial(1, 0, mu) = 1 - mu
 * binomial(1, 1, mu) = mu
 * */

bool get_binomial_cnk_prob(double mu, double sigma, double significance,
        std::vector<int32_t>* threshold_vec) {
    if (threshold_vec == NULL || mu >= 1.0 ||
            sigma >= 1.0 || significance >= 1.0) {
        CWARNING_LOG("invalid param[vec:%p|mu:%f|sigma:%f|signif:%f]",
                threshold_vec, mu, sigma, significance);
        return false;
    }
    if (mu < significance) {
        CWARNING_LOG("mu:%f smaller than significance:%f",
                mu, significance);
        return false;
    }
    // prepare prob
    threshold_vec->clear();
    std::vector<std::vector<double> > prob;
    std::vector<double> empty_vec;
    threshold_vec->resize(2);
    prob.resize(2);
    prob[1].resize(2);
    // push empty n=0
    (*threshold_vec)[0] = 0;
    // prepare n=1
    (*threshold_vec)[1] = 1;
    prob[1][0] = 1 - mu;
    prob[1][1] = mu;
    // start generate from n=2 until exit with two sigma
    int32_t n = 2;
    while (true) {
        // init prob & threshold
        threshold_vec->resize(n + 1);
        prob.resize(n + 1);
        prob[n].resize(n + 1);
        (*threshold_vec)[n] = n;
        prob[n][n] = prob[n - 1][n - 1] * mu;
        // carculate prob & set threshold of Cnk from right to left
        double cumulate_prob = prob[n][n];
        for (int32_t k = n - 1; k >= 0; k--) {
            prob[n][k] = prob[n - 1][k] * n / (n - k) * (1 - mu);
            cumulate_prob += prob[n][k];
            if (cumulate_prob < significance) {
                (*threshold_vec)[n] = k;
            }
        }
        CDEBUG_LOG("prob[%d]=%d", n, (*threshold_vec)[n]);
        // check reach mu+2*sigma or not
        double t_ratio = static_cast<double>((*threshold_vec)[n])
                / static_cast<double>(n);
        if (t_ratio < (mu + 2 * sigma)) {
            (*threshold_vec)[n] = (*threshold_vec)[n - 1];
            CWARNING_LOG("reach min ratio[n=%d,k=%d,ratio=%f]",
                    n, (*threshold_vec)[n], t_ratio);
            break;
        }
        n++;
    }
    return true;
}

std::string get_file_name(const std::string& file_path, 
        const std::string& prefix,  const std::string& version_id,  
        const std::string& record_type, const std::string& shard) {
    std::stringstream res;
    res << file_path << "/" << prefix;
    if (record_type != "") {
        res << "_" << record_type;
    }
    if (version_id != "") {
        res << "_" << version_id;
    }
    if (shard != "") {
        res << "_" << shard;
    }
    return res.str();
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti


/* vim: set ts=4 sw=4: */

