// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yang<PERSON><PERSON><PERSON>(<EMAIL>)
// 
// @File: feature_extractor_manager.h
// @Last modified: 2018-04-19 22:06:54
// @Brief: extract features for each log level in FeatureExtractorManager
// @Usage: 
//     1. new LogRecordInterface log and FeatureExtractorManager f
//     2. log.init(conf) && f.init(conf, log.view_descriptors())
//     3. f->extract(log, std::vector<std::shared_ptr<FeatureValueProto> >*)
// @Note: it's no need to reset log level before calling extract func

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_INC_FEATURE_EXTRACTOR_MANAGER_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_INC_FEATURE_EXTRACTOR_MANAGER_H

#include <string>
#include <vector>
#include <memory>
#include <Configure.h>
#include <log_record_interface.h>
#include "feature_value.pb.h"
#include "care_space.h"
#include "care_exp.h"
#include "feature_extractor_interface.h"
#include "gflags/gflags.h"
#include "extractor_tool.h"

namespace anti {
namespace themis {
namespace feature_lib {

// 简介：特征抽取管理器
// 功能：解析caretag 并进行日志分层, 依据 feature_type 为每个 feature 提供了一个特征抽取器, 在每层日志中提取特征信息
class FeatureExtractorManager {
public:
    typedef anti::themis::log_interface::LogRecordInterface LogRecordInterface;
    typedef anti::themis::log_interface::ViewDescriptor ViewDescriptor;
    typedef anti::themis::log_parser_lib::CareExp CareExp;

    FeatureExtractorManager() : 
            _globle_view_level(0U), 
            _need_using_exp_global(true) {}
    virtual ~FeatureExtractorManager() {
        uninit();    
    }

    bool init(
            const std::string& fea_conf_path,
            const std::string& fea_conf_file,
            const std::vector<ViewDescriptor>& view_des);
    void uninit();

    bool extract(
            LogRecordInterface* log,
            std::vector<std::shared_ptr<FeatureValueProto> >* feas);
    // @brief: pair<feature_id, next_coord>
    bool get_pv_coords(std::vector<std::pair<uint64_t, int64_t> >* coords) const;
    bool reset_pv_coords();

private:
    bool _init_exts(
            const comcfg::ConfigUnit& conf, 
            const std::vector<ViewDescriptor>& view_des,
            std::vector<std::shared_ptr<CareExp>>* care_exps);

    bool _init_care_tags(
            const comcfg::ConfigUnit& conf, 
            const std::vector<ViewDescriptor>& view_des,
            std::vector<std::shared_ptr<CareExp>>* care_exps); 

    bool _ext(
            std::shared_ptr<CareExp> care_exp_ptr,
            std::vector<FeatureExtractorInterface*>* exts,
            LogRecordInterface* log,
            std::vector<std::shared_ptr<FeatureValueProto> >* feas);
    bool _care_global(LogRecordInterface* log, bool *result);

    std::string _global_care_str;
    CareExp _care_exp;
    CareExp _globle_care_exp;
    CareSpace _global_care;
    bool _need_using_exp_global;
    uint32_t _globle_view_level;
    static const uint32_t MAX_LEVEL = 3U;
    // _exts[0] extracts log(level 1)
    // _exts[1] extracts log(level 2)
    // _exts[2] extracts log(level 3)
    std::vector<FeatureExtractorInterface*> _exts[MAX_LEVEL];
    std::vector<std::shared_ptr<CareExp>> _care_exps;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_INC_FEATURE_EXTRACTOR_MANAGER_H

/* vim: set ts=4 sw=4: */

