// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @brief 

#ifndef BAIDU_ANTI_FEATURE_LIB_EXTRACTOR_INC_EXTRACTOR_TOOL_H
#define BAIDU_ANTI_FEATURE_LIB_EXTRACTOR_INC_EXTRACTOR_TOOL_H

#include <vector>
#include "log_record_interface.h"

namespace anti {
namespace themis {
namespace feature_lib {

class ExtractorTool {
public:
    typedef anti::themis::log_interface::ViewDescriptor ViewDescriptor;

    static const ViewDescriptor* parse_view_des(
            const std::string& view_str,
            const std::vector<ViewDescriptor>& all_views);

    static bool parse_view_des(
            const std::string& view_str,
            const std::vector<ViewDescriptor>& all_views,
            std::vector<ViewDescriptor>* target_views);

    static bool need_using_exp(
            const std::string& care_str);
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif
