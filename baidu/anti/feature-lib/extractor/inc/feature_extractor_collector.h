// Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
// @Author: z<PERSON><PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
// 
// @Last modified: 2017-06-07 15:29:15
// @Brief: FeatureExtractorCollector for multi FeatureExtractorManager
// @Usage: 
// @Note: 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_INC_FEATURE_EXTRACTOR_COLLECTOR_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_INC_FEATURE_EXTRACTOR_COLLECTOR_H

#include <string>
#include <vector>
#include <memory>
#include <Configure.h>
#include <log_record_interface.h>
#include <log_record_collector.h>
#include "feature_value.pb.h"
#include "care_space.h"
#include "feature_extractor_interface.h"

namespace anti {
namespace themis {
namespace feature_lib {

typedef std::shared_ptr<FeatureExtractorManager> FeatureExtractorManagerPtr;

class FeatureExtractorCollector {
public:
    typedef anti::themis::log_interface::LogRecordInterface LogRecordInterface;
    typedef anti::themis::log_parser_lib::LogRecordCollector LogRecordCollector;
    typedef std::vector<std::pair<uint64_t, int64_t>> CoordArray;
    typedef std::vector<std::pair<RecordType, CoordArray>> CoordArrayContainer;
    typedef std::shared_ptr<FeatureValueProto> FeaturePtr;

    FeatureExtractorCollector() {}
    virtual ~FeatureExtractorCollector() {}

    bool init(
            const std::string& fea_conf_path,
            const std::string& fea_conf_file,
            LogRecordCollector* log);
    bool reset_pv_coords();
    bool get_pv_coords(CoordArrayContainer* container);
    bool extract(anti::themis::RecordType record_type,
            LogRecordCollector* record_collector,
            std::vector<FeaturePtr>* cur_feat_list);

private:
    FeatureExtractorManagerPtr _init_conf(const std::string& path,
            const std::string& file,
            LogRecordCollector* log_col,
            anti::themis::RecordType record_type);
    FeatureExtractorManagerPtr _manager;
    std::map<anti::themis::RecordType, FeatureExtractorManagerPtr> _manager_map;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_INC_FEATURE_EXTRACTOR_COLLECTOR_H

/* vim: set ts=4 sw=4: */

