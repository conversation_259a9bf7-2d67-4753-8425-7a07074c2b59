// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiax<PERSON>(<EMAIL>)
// 
// @File: feature_extractor_interface.h
// @Last modified: 2018-03-28 19:51:39
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_INC_FEATURE_EXTRACTOR_INTERFACE_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_INC_FEATURE_EXTRACTOR_INTERFACE_H

#include <memory>
#include <Configure.h>
#include <log_record_interface.h>
#include "care_exp.h"
#include "feature_value.pb.h"

namespace anti {
namespace themis {
namespace feature_lib {

// 简介：特征抽取器接口类
// 功能：向外提供统一的接口extract()
// 输入：LogRecord 、extrator_feature.conf
// 输出：FeatureValueProto
class FeatureExtractorInterface {
public:
    typedef anti::themis::log_interface::LogRecordInterface LogRecordInterface;
    typedef anti::themis::log_interface::ViewDescriptor ViewDescriptor;
    typedef anti::themis::log_parser_lib::CareExp CareExp;

    virtual ~FeatureExtractorInterface() {}
    virtual bool init(
            const comcfg::ConfigUnit& conf, 
            const std::vector<ViewDescriptor>& view_des,
            CareExp* care_exp) = 0;
    virtual void uninit() = 0;

    virtual uint64_t feature_id() const = 0;
    // @brief: current pv_coord, if pv_coord no update after reset then return -1
    virtual int64_t pv_coord() const = 0;
    virtual void reset_pv_coord() = 0;

    virtual bool extract(
            const CareExp& care_exp,
            LogRecordInterface* log,
            std::vector<std::shared_ptr<FeatureValueProto> >* feas) = 0;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_INC_FEATURE_EXTRACTOR_INTERFACE_H

/* vim: set ts=4 sw=4: */

