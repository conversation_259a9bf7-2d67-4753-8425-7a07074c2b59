// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: segment_feature_extractor_test.cpp
// @Last modified: 2016-10-21 10:27:52
// @Brief: 

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <Configure.h>
#include "segment_feature_extractor.h"

using anti::themis::log_interface::ViewDescriptor;
using anti::themis::log_interface::LogRecordInterface;
using anti::themis::log_interface::ViewValueProto;
using ::testing::_;
using ::testing::Return;
using ::testing::DoAll;
using ::testing::SetArgPointee;

namespace anti {
namespace themis {
namespace feature_lib {

class SegmentFeatureExtractorTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
        _view_des.push_back({"ClickBaiduLog", 0U, 1U, "userid"});
        _view_des.push_back({"ClickBaiduLog", 1U, 1U, "price"});
    }
    virtual void TearDown() {}

private:
    SegmentFeatureExtractor _obj;
    std::vector<ViewDescriptor> _view_des;
};

TEST_F(SegmentFeatureExtractorTestSuite, construction_case) {
    EXPECT_EQ(FeatureValueProto::SEGMENT, _obj._ftype);
    EXPECT_EQ(FeatureValueProto::PV, _obj._stype);
}

TEST_F(SegmentFeatureExtractorTestSuite, _init_case) {
    comcfg::Configure conf;
    ASSERT_TRUE(conf.load("./conf", "feature_extractor_test.conf") == 0);
    uint32_t conf_size = conf["feature"].size();
    ASSERT_LE(10U, conf_size);
    for (uint32_t i = 5U; i < 8U; ++i) {
        ASSERT_FALSE(_obj._init(conf["feature"][i], _view_des));
        _obj._uninit();
    }
    ASSERT_TRUE(_obj._init(conf["feature"][8], _view_des));
    EXPECT_EQ(FeatureValueProto::SEGMENT, _obj._ftype);
    EXPECT_EQ(FeatureValueProto::PV, _obj._stype);
    _obj._uninit();

    ASSERT_TRUE(_obj._init(conf["feature"][9], _view_des));
    EXPECT_EQ(FeatureValueProto::MULTI_SEG, _obj._ftype);
    EXPECT_EQ(FeatureValueProto::TIME, _obj._stype);
    _obj._uninit();

    ASSERT_TRUE(_obj._init(conf["auto_black"]["auto_segment"]["feature"][0], _view_des));
    EXPECT_EQ(FeatureValueProto::AUTO_SEGMENT, _obj._ftype);
    EXPECT_EQ(FeatureValueProto::PV, _obj._stype);
    _obj._uninit();

    ASSERT_TRUE(_obj._init(conf["behavior"]["behavior_segment"]["feature"][0], _view_des));
    EXPECT_EQ(FeatureValueProto::BEHAVIOR_SEGMENT, _obj._ftype);
    EXPECT_EQ(FeatureValueProto::PV, _obj._stype);
    _obj._uninit();
}

class MockLogRecordInterface : public LogRecordInterface {
public:
    virtual ~MockLogRecordInterface() {}
    MOCK_METHOD1(next, bool(int32_t*));
    MOCK_METHOD2(init, bool(const std::string&, const std::string&));
    MOCK_METHOD0(uninit, void());
    MOCK_CONST_METHOD1(get_view_descriptor, const ViewDescriptor*(const std::string&));
    MOCK_CONST_METHOD0(view_descriptors, const std::vector<ViewDescriptor>*());

    MOCK_METHOD2(parse, bool(const char*, uint64_t));
    MOCK_METHOD0(clear, void());
    MOCK_METHOD1(reset_log_level, bool(uint32_t));
    MOCK_CONST_METHOD0(has_next, bool());
    MOCK_METHOD0(next, bool());

    MOCK_CONST_METHOD0(joinkey, uint64_t());
    MOCK_CONST_METHOD0(log_id, uint64_t());
    MOCK_CONST_METHOD0(log_time, uint64_t());
    MOCK_CONST_METHOD0(is_spam, bool());

    MOCK_METHOD2(get_view_value, bool(const ViewDescriptor&, ViewValueProto*));
    MOCK_METHOD2(get_view_sign, bool(const std::vector<ViewDescriptor>&, ViewValueProto*));
};

TEST_F(SegmentFeatureExtractorTestSuite, _extract_by_invalid_input_case) {
    EXPECT_FALSE(_obj._extract(NULL, NULL));

    MockLogRecordInterface log;
    EXPECT_FALSE(_obj._extract(&log, NULL));

    ViewValueProto value;
    value.add_signs(123LU);
    EXPECT_CALL(log, get_view_sign(_, _))
            .WillOnce(Return(false))
            .WillOnce(DoAll(SetArgPointee<1>(value), Return(true)));
    EXPECT_CALL(log, log_id()).WillRepeatedly(Return(119LU));
    std::vector<std::shared_ptr<FeatureValueProto> > feas;
    EXPECT_FALSE(_obj._extract(&log, &feas));
    EXPECT_FALSE(_obj._extract(&log, &feas));
}

TEST_F(SegmentFeatureExtractorTestSuite, _extract_by_valid_input_case) {
    MockLogRecordInterface log;
    std::vector<std::shared_ptr<FeatureValueProto> > feas;
    _obj._pv_coord = 8L;

    std::string exp_str[2] = {"1", "2"};
    uint64_t exp_sign[2] = {123LU, 4567LU};
    ViewValueProto value;
    value.add_values()->set_str_value(exp_str[0]);
    value.add_values()->set_str_value(exp_str[1]);
    value.add_signs(exp_sign[0]);
    value.add_signs(exp_sign[1]);
    EXPECT_CALL(log, get_view_sign(_, _))
            .WillRepeatedly(DoAll(SetArgPointee<1>(value), Return(true)));
    EXPECT_CALL(log, log_id()).WillRepeatedly(Return(119LU));
    EXPECT_CALL(log, log_time()).WillRepeatedly(Return(110000LU));
    EXPECT_CALL(log, joinkey()).WillRepeatedly(Return(120LU));

    ASSERT_TRUE(_obj._extract(&log, &feas));
    _obj._ftype = FeatureValueProto::MULTI_SEG;
    _obj._stype = FeatureValueProto::TIME;
    ASSERT_TRUE(_obj._extract(&log, &feas));

    ASSERT_EQ(4U, feas.size());
    int64_t exp_coord[] = {8L, 8L, 110L, 110L};
    FeatureValueProto::FeatureType exp_ftype[] = {
            FeatureValueProto::SEGMENT,
            FeatureValueProto::SEGMENT,
            FeatureValueProto::MULTI_SEG,
            FeatureValueProto::MULTI_SEG
    };
    FeatureValueProto::SegType exp_stype[] = {
            FeatureValueProto::PV,
            FeatureValueProto::PV,
            FeatureValueProto::TIME,
            FeatureValueProto::TIME
    };
    for (uint32_t i = 0; i < 4U; ++i) {
        EXPECT_EQ(0LU, feas[i]->feature_id());
        EXPECT_EQ(exp_sign[i % 2], feas[i]->view_sign());
        EXPECT_EQ(exp_ftype[i], feas[i]->feature_type());
        EXPECT_EQ(120LU, feas[i]->joinkey());
        EXPECT_EQ(119LU, feas[i]->log_id());
        EXPECT_EQ(110000LU, feas[i]->log_time());
        EXPECT_STREQ(exp_str[i % 2].data(), feas[i]->view().data());
        EXPECT_EQ(exp_stype[i], feas[i]->seg_type());
        EXPECT_EQ(exp_coord[i], feas[i]->coord());
    }
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

