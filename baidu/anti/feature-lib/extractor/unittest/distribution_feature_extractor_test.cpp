// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: distribution_feature_extractor_test.cpp
// @Last modified: 2016-10-21 11:00:22
// @Brief: 

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <Configure.h>
#include "distribution_feature_extractor.h"

using anti::themis::log_interface::ViewDescriptor;
using anti::themis::log_interface::LogRecordInterface;
using anti::themis::log_interface::ViewValueProto;
using ::testing::_;
using ::testing::Return;
using ::testing::DoAll;
using ::testing::SetArgPointee;

namespace anti {
namespace themis {
namespace feature_lib {

class DistributionFeatureExtractorTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
        _view_des.push_back({"ClickBaiduLog", 0U, 1U, "userid"});
        _view_des.push_back({"ClickBaiduLog", 1U, 1U, "price"});
    }
    virtual void TearDown() {}

private:
    DistributionFeatureExtractor _obj;
    std::vector<ViewDescriptor> _view_des;
};

TEST_F(DistributionFeatureExtractorTestSuite, construction_case) {
    EXPECT_EQ(FeatureValueProto::DISTRIBUTION, _obj._ftype);
    EXPECT_EQ(FeatureValueProto::PV, _obj._stype);
}

TEST_F(DistributionFeatureExtractorTestSuite, _init_case) {
    comcfg::Configure conf;
    ASSERT_TRUE(conf.load("./conf", "feature_extractor_test.conf") == 0);
    uint32_t conf_size = conf["feature"].size();
    ASSERT_LE(24U, conf_size);
    for (uint32_t i = 17U; i < 22U; ++i) {
        ASSERT_FALSE(_obj._init(conf["feature"][i], _view_des));
        _obj._uninit();
    }
    
    FeatureValueProto::SegType stype[] = {
            FeatureValueProto::PV,
            FeatureValueProto::TIME
    };

    for (uint32_t i = 22U; i < 24U; ++i) {
        ASSERT_TRUE(_obj._init(conf["feature"][i], _view_des));
        EXPECT_EQ(FeatureValueProto::DISTRIBUTION, _obj._ftype);
        EXPECT_EQ(stype[i - 22U], _obj._stype);
        EXPECT_STREQ("ClickBaiduLog", _obj._data_view.log_type.data());
        EXPECT_EQ(1U, _obj._data_view.view_id);
        EXPECT_EQ(1U, _obj._data_view.view_level);
        EXPECT_STREQ("price", _obj._data_view.view_name.data());
        int64_t exp_points[] = {LLONG_MIN, 5, 100, LLONG_MAX};
        ASSERT_EQ(3U, _obj._intervals.size());
        for (uint32_t i = 0U; i < 3U; ++i) {
            EXPECT_EQ(exp_points[i], _obj._intervals[i].first);
            EXPECT_EQ(exp_points[i + 1], _obj._intervals[i].second);
        }
        _obj._uninit();
    }

    ASSERT_TRUE(_obj._init(conf["behavior"]["behavior_distribution"]["feature"][0], _view_des));
    EXPECT_EQ(FeatureValueProto::BEHAVIOR_DISTRIBUTION, _obj._ftype);
    EXPECT_EQ(FeatureValueProto::PV, _obj._stype);
    _obj._uninit();
}

TEST_F(DistributionFeatureExtractorTestSuite, _get_interval_idx_by_invalid_input_case) {
    // value.values_size is 1
    ViewValueProto value;
    EXPECT_FALSE(_obj._get_interval_idx(value, NULL));
    
    // idx is NULL
    value.add_values()->set_int_value(1);
    value.mutable_values(0)->set_str_value("xxxxxx");
    EXPECT_FALSE(_obj._get_interval_idx(value, NULL));

    // invalid convert fail
    uint32_t idx = UINT_MAX;
    value.set_type(ViewValueProto::STRING);
    EXPECT_FALSE(_obj._get_interval_idx(value, &idx));
    _obj._uninit();
    
    // invalid value type
    value.set_type(ViewValueProto::NOT_EXIST);
    EXPECT_FALSE(_obj._get_interval_idx(value, &idx));
    
    // not find interval
    value.set_type(ViewValueProto::INT32);
    EXPECT_FALSE(_obj._get_interval_idx(value, &idx));
}

TEST_F(DistributionFeatureExtractorTestSuite, _get_interval_idx_by_valid_input_case) {
    comcfg::Configure conf;
    ASSERT_TRUE(conf.load("./conf", "feature_extractor_test.conf") == 0);
    ASSERT_LE(23U, conf["feature"].size());
    ASSERT_TRUE(_obj._init(conf["feature"][22], _view_des));
    ViewValueProto::ViewValueType type[] = {
            ViewValueProto::UINT32,
            ViewValueProto::INT32,
            ViewValueProto::UINT64,
            ViewValueProto::INT64
    };
    uint32_t exp_idx[] = {0U, 1U, 1U, 2U};
    for (uint32_t i = 0U; i < 4U; ++i) {
        ViewValueProto value;
        value.set_type(type[i]);
        value.add_values()->set_int_value(i * 50);
        uint32_t idx = UINT_MAX;
        ASSERT_TRUE(_obj._get_interval_idx(value, &idx));
        EXPECT_EQ(exp_idx[i], idx);
    }
    _obj._uninit();
}

class MockLogRecordInterface : public LogRecordInterface {
public:
    virtual ~MockLogRecordInterface() {}
    MOCK_METHOD1(next, bool(int32_t*));
    MOCK_METHOD2(init, bool(const std::string&, const std::string&));
    MOCK_METHOD0(uninit, void());
    MOCK_CONST_METHOD1(get_view_descriptor, const ViewDescriptor*(const std::string&));
    MOCK_CONST_METHOD0(view_descriptors, const std::vector<ViewDescriptor>*());

    MOCK_METHOD2(parse, bool(const char*, uint64_t));
    MOCK_METHOD0(clear, void());
    MOCK_METHOD1(reset_log_level, bool(uint32_t));
    MOCK_CONST_METHOD0(has_next, bool());
    MOCK_METHOD0(next, bool());

    MOCK_CONST_METHOD0(joinkey, uint64_t());
    MOCK_CONST_METHOD0(log_id, uint64_t());
    MOCK_CONST_METHOD0(log_time, uint64_t());
    MOCK_CONST_METHOD0(is_spam, bool());

    MOCK_METHOD2(get_view_value, bool(const ViewDescriptor&, ViewValueProto*));
    MOCK_METHOD2(get_view_sign, bool(const std::vector<ViewDescriptor>&, ViewValueProto*));
};

TEST_F(DistributionFeatureExtractorTestSuite, _extract_by_invalid_input_case) {
    EXPECT_FALSE(_obj._extract(NULL, NULL));

    MockLogRecordInterface log;
    EXPECT_FALSE(_obj._extract(&log, NULL));

    // get data view value fail
    ViewValueProto value[3];
    value[1].set_type(ViewValueProto::STRING);
    value[1].add_values()->set_str_value("xxxx");
    value[2].set_type(ViewValueProto::STRING);
    value[2].add_values()->set_str_value("123");
    EXPECT_CALL(log, get_view_value(_, _))
            .WillOnce(Return(false))
            .WillOnce(DoAll(SetArgPointee<1>(value[0]), Return(true)))
            .WillOnce(DoAll(SetArgPointee<1>(value[1]), Return(true)))
            .WillRepeatedly(DoAll(SetArgPointee<1>(value[2]), Return(true)));
    EXPECT_CALL(log, log_id()).WillRepeatedly(Return(119LU));
    std::vector<std::shared_ptr<FeatureValueProto> > feas;
    EXPECT_FALSE(_obj._extract(&log, &feas));
    EXPECT_FALSE(_obj._extract(&log, &feas));

    // get interval idx fail
    EXPECT_FALSE(_obj._extract(&log, &feas));
    
    // get view sign fail
    _obj._intervals.push_back(std::pair<int64_t, int64_t>(0, 200));
    EXPECT_CALL(log, get_view_sign(_, _))
            .WillOnce(Return(false))
            .WillOnce(DoAll(SetArgPointee<1>(value[2]), Return(true)));
    EXPECT_CALL(log, log_id()).WillRepeatedly(Return(119LU));
    EXPECT_FALSE(_obj._extract(&log, &feas));
    EXPECT_FALSE(_obj._extract(&log, &feas));
}

TEST_F(DistributionFeatureExtractorTestSuite, _extract_by_valid_input_case) {
    _obj._intervals.push_back(std::pair<int64_t, int64_t>(-1000, 1000));
    MockLogRecordInterface log;
    std::vector<std::shared_ptr<FeatureValueProto> > feas;
    _obj._pv_coord = 8L;
    
    ViewValueProto value;
    value.set_type(ViewValueProto::STRING);
    value.add_values()->set_str_value("123");
    value.add_signs(456LU);
    EXPECT_CALL(log, get_view_value(_, _))
        .WillRepeatedly(DoAll(SetArgPointee<1>(value), Return(true)));
    EXPECT_CALL(log, get_view_sign(_, _))
        .WillRepeatedly(DoAll(SetArgPointee<1>(value), Return(true)));
    EXPECT_CALL(log, log_id()).WillRepeatedly(Return(119LU));
    EXPECT_CALL(log, log_time()).WillRepeatedly(Return(110000LU));
    EXPECT_CALL(log, joinkey()).WillRepeatedly(Return(120LU));

    ASSERT_TRUE(_obj._extract(&log, &feas));
    _obj._stype = FeatureValueProto::TIME;
    ASSERT_TRUE(_obj._extract(&log, &feas));
    
    FeatureValueProto::SegType exp_stype[] = {
            FeatureValueProto::PV,
            FeatureValueProto::TIME,
    };
    uint64_t exp_coord[] = {8L, 110L};
    for (uint32_t i = 0; i < 2U; ++i) {
        EXPECT_EQ(0LU, feas[i]->feature_id());
        EXPECT_EQ(456LU, feas[i]->view_sign());
        EXPECT_EQ(FeatureValueProto::DISTRIBUTION, feas[i]->feature_type());
        EXPECT_EQ(120LU, feas[i]->joinkey());
        EXPECT_EQ(119LU, feas[i]->log_id());
        EXPECT_EQ(110000LU, feas[i]->log_time());
        EXPECT_STREQ("123", feas[i]->view().data());
        EXPECT_EQ(exp_stype[i], feas[i]->seg_type());
        EXPECT_EQ(exp_coord[i], feas[i]->coord());
        EXPECT_EQ(0U, feas[i]->bucket_idx());
    }
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

