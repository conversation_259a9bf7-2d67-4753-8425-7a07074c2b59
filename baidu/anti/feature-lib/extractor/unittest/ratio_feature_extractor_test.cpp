// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: ratio_feature_extractor_test.cpp
// @Last modified: 2018-04-22 16:27:41
// @Brief: 

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <Configure.h>
#include "ratio_feature_extractor.h"

using anti::themis::log_interface::ViewDescriptor;
using anti::themis::log_interface::LogRecordInterface;
using anti::themis::log_interface::ViewValueProto;
using ::testing::_;
using ::testing::Return;
using ::testing::DoAll;
using ::testing::SetArgPointee;

namespace anti {
namespace themis {
namespace feature_lib {

class RatioFeatureExtractorTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
        _view_des.push_back({"ClickBaiduLog", 0U, 1U, "userid"});
        _view_des.push_back({"ClickBaiduLog", 1U, 1U, "price"});
        _view_des.push_back({"ClickBaiduLog", 2U, 1U, "ip"});
    }
    virtual void TearDown() {}

private:
    RatioFeatureExtractor _obj;
    std::vector<ViewDescriptor> _view_des;
    anti::themis::log_parser_lib::CareExp _care_exp;
};

TEST_F(RatioFeatureExtractorTestSuite, construction_case) {
    EXPECT_EQ(FeatureValueProto::RATIO, _obj._ftype);
    EXPECT_EQ(FeatureValueProto::PV, _obj._stype);
}

TEST_F(RatioFeatureExtractorTestSuite, _init_case) {
    comcfg::Configure conf;
    ASSERT_TRUE(conf.load("./conf", "feature_extractor_test.conf") == 0);
    uint32_t conf_size = conf["feature"].size();
    ASSERT_LE(17U, conf_size);
    for (uint32_t i = 10U; i < 15U; ++i) {
        ASSERT_FALSE(_obj._init(conf["feature"][i], _view_des, &_care_exp));
        _obj._uninit();
    }
    
    FeatureValueProto::SegType stype[] = {
            FeatureValueProto::PV,
            FeatureValueProto::TIME
    };

    for (uint32_t i = 15U; i < 17U; ++i) {
        ASSERT_TRUE(_obj._init(conf["feature"][i], _view_des, &_care_exp));
        EXPECT_EQ(FeatureValueProto::RATIO, _obj._ftype);
        EXPECT_EQ(stype[i - 15U], _obj._stype);
        _obj._uninit();
    }

    ASSERT_TRUE(_obj._init(conf["auto_black"]["auto_ratio"]["feature"][0], _view_des, &_care_exp));
    EXPECT_EQ(FeatureValueProto::AUTO_RATIO, _obj._ftype);
    EXPECT_EQ(FeatureValueProto::PV, _obj._stype);
    _obj._uninit();

    ASSERT_TRUE(_obj._init(conf["behavior"]["behavior_ratio"]["feature"][0], _view_des, &_care_exp));
    EXPECT_EQ(FeatureValueProto::BEHAVIOR_RATIO, _obj._ftype);
    EXPECT_EQ(FeatureValueProto::PV, _obj._stype);
    _obj._uninit();
}

TEST_F(RatioFeatureExtractorTestSuite, _init_kpi_case) {
    comcfg::Configure conf;
    ASSERT_TRUE(conf.load("./conf", "feature_extractor_test.conf") == 0);
    for (uint32_t i = 0U; i < conf["kpi"]["invalid"]["feature"].size(); ++i) {
        ASSERT_FALSE(_obj._init(conf["kpi"]["invalid"]["feature"][i], _view_des, &_care_exp));
        _obj._uninit();
    }

    FeatureValueProto::FeatureType ftype[] = {
            FeatureValueProto::RATE,
            FeatureValueProto::RATE_DISTRIBUTION,
            FeatureValueProto::CPM,
            FeatureValueProto::CPM,
            FeatureValueProto::CPM_DISTRIBUTION
    };

    _obj._views.push_back(_view_des[0]);
    for (uint32_t i = 0U; i < conf["kpi"]["valid"]["feature"].size(); ++i) {
        ASSERT_TRUE(_obj._init(conf["kpi"]["valid"]["feature"][i], _view_des, &_care_exp));
        EXPECT_EQ(ftype[i], _obj._ftype);
        if (i >= 3) {
            EXPECT_TRUE(_obj._price.get() != NULL);
        } else {
            EXPECT_TRUE(_obj._price.get() == NULL);
        }

        if (i == 1 || i == 4) {// RATE_DISTRIBUTION or CPM_DISTRIBUTION
            EXPECT_EQ(2U, _obj._data_views.size());
            EXPECT_EQ(0U, _obj._data_views[0].view_id);
            EXPECT_EQ(2U, _obj._data_views[1].view_id);
        } else {
            EXPECT_EQ(0U, _obj._data_views.size());
        }
        _obj._uninit();
    }
}

class MockLogRecordInterface : public LogRecordInterface {
public:
    virtual ~MockLogRecordInterface() {}
    MOCK_METHOD1(next, bool(int32_t*));
    MOCK_METHOD2(init, bool(const std::string&, const std::string&));
    MOCK_METHOD0(uninit, void());
    MOCK_CONST_METHOD1(get_view_descriptor, const ViewDescriptor*(const std::string&));
    MOCK_CONST_METHOD0(view_descriptors, const std::vector<ViewDescriptor>*());

    MOCK_METHOD2(parse, bool(const char*, uint64_t));
    MOCK_METHOD0(clear, void());
    MOCK_METHOD1(reset_log_level, bool(uint32_t));
    MOCK_CONST_METHOD0(has_next, bool());
    MOCK_METHOD0(next, bool());

    MOCK_CONST_METHOD0(joinkey, uint64_t());
    MOCK_CONST_METHOD0(log_id, uint64_t());
    MOCK_CONST_METHOD0(log_time, uint64_t());
    MOCK_CONST_METHOD0(is_spam, bool());

    MOCK_METHOD2(get_view_value, bool(const ViewDescriptor&, ViewValueProto*));
    MOCK_METHOD2(get_view_sign, bool(const std::vector<ViewDescriptor>&, ViewValueProto*));
};

class MockCareExp : public anti::themis::log_parser_lib::CareExp {
public:
    virtual ~MockCareExp() {}
    MOCK_METHOD2(care, bool(const std::string&, bool*));
};


// TEST_F(RatioFeatureExtractorTestSuite, _extract_by_invalid_input_case) {
//     EXPECT_FALSE(_obj._extract(_care_exp, NULL, NULL));
// 
//     MockLogRecordInterface log;
//     EXPECT_FALSE(_obj._extract(_care_exp, &log, NULL));
// 
//     // call filter care fail
//     EXPECT_CALL(log, log_id()).WillRepeatedly(Return(119LU));
//     std::vector<CareConditionInterface*> co;
//     co.push_back(NULL);
//     _obj._filter._conds.push_back(co);
//     std::vector<std::shared_ptr<FeatureValueProto> > feas;
//     EXPECT_FALSE(_obj._extract(_care_exp, &log, &feas));
// 
//     // call refer care fail
//     _obj._filter._sc = CareSpace::ALL;
//     _obj._refer._conds.push_back(co);
//     EXPECT_FALSE(_obj._extract(_care_exp, &log, &feas));
// 
//     // call get_view_sign fail
//     _obj._refer._sc = CareSpace::ALL;
//     ViewValueProto value;
//     value.add_signs(123LU);
//     EXPECT_CALL(log, get_view_sign(_, _))
//             .WillOnce(Return(false))
//             .WillOnce(DoAll(SetArgPointee<1>(value), Return(true)));
//     EXPECT_CALL(log, log_id()).WillRepeatedly(Return(119LU));
//     EXPECT_FALSE(_obj._extract(_care_exp, &log, &feas));
//     EXPECT_FALSE(_obj._extract(_care_exp, &log, &feas));
// }

TEST_F(RatioFeatureExtractorTestSuite, _extract_weak_check_case) {
    MockLogRecordInterface log;
    std::vector<std::shared_ptr<FeatureValueProto> > feas;
    _obj._pv_coord = 8L;
    _obj._weak_check = true;

    ViewValueProto value;
    value.set_type(ViewValueProto::STRING);
    value.add_values()->set_str_value("sign");
    value.add_signs(123456LU);
    EXPECT_CALL(log, get_view_sign(_, _))
            .WillRepeatedly(DoAll(SetArgPointee<1>(value), Return(true)));
    EXPECT_CALL(log, get_view_value(_, _))
            .WillRepeatedly(DoAll(SetArgPointee<1>(value), Return(true)));
    EXPECT_CALL(log, log_id()).WillRepeatedly(Return(119LU));
    EXPECT_CALL(log, log_time()).WillRepeatedly(Return(110000LU));
    EXPECT_CALL(log, joinkey()).WillRepeatedly(Return(120LU));

    std::string fils[] = {"['userid'NH]", "['userid'HA]", "['userid'NH]", "['userid'NH]"};
    std::string refs[] = {"['userid'NH]", "['userid'NH]", "['userid'HA]", "['userid'NH]"};
    for (uint32_t i = 0; i < 1U; ++i) {
        ASSERT_TRUE(_obj._filter.init(fils[i], _view_des));
        ASSERT_TRUE(_obj._refer.init(refs[i], _view_des));
        anti::themis::log_parser_lib::CareExp care_exp;
        _obj._refer_str = refs[i];
        _obj._filter_str = fils[i];
        ASSERT_TRUE(care_exp.append(fils[i], _view_des));
        ASSERT_TRUE(care_exp.append(refs[i], _view_des));
        ASSERT_TRUE(care_exp.reset(&log));
        _obj._stype = FeatureValueProto::TIME;
        ASSERT_TRUE(_obj._extract(care_exp, &log, &feas));
        _obj._stype = FeatureValueProto::PV;
        ASSERT_TRUE(_obj._extract(care_exp, &log, &feas));
        _obj._filter.uninit();
        _obj._refer.uninit();
    }
    ASSERT_EQ(2U, feas.size());
    ASSERT_FALSE(feas[0]->in_filter());
    ASSERT_FALSE(feas[0]->in_refer());
}

TEST_F(RatioFeatureExtractorTestSuite, _extract_by_valid_input_case) {
    MockLogRecordInterface log;
    std::vector<std::shared_ptr<FeatureValueProto> > feas;
    _obj._pv_coord = 8L;

    ViewValueProto value;
    value.set_type(ViewValueProto::STRING);
    value.add_values()->set_str_value("sign");
    value.add_signs(123456LU);
    EXPECT_CALL(log, get_view_sign(_, _))
            .WillRepeatedly(DoAll(SetArgPointee<1>(value), Return(true)));
    EXPECT_CALL(log, get_view_value(_, _))
            .WillRepeatedly(DoAll(SetArgPointee<1>(value), Return(true)));
    EXPECT_CALL(log, log_id()).WillRepeatedly(Return(119LU));
    EXPECT_CALL(log, log_time()).WillRepeatedly(Return(110000LU));
    EXPECT_CALL(log, joinkey()).WillRepeatedly(Return(120LU));

    std::string fils[] = {"['userid'HA]", "['userid'HA]", "['userid'NH]", "['userid'NH]"};
    std::string refs[] = {"['userid'HA]", "['userid'NH]", "['userid'HA]", "['userid'NH]"};
    for (uint32_t i = 0; i < 4U; ++i) {
        ASSERT_TRUE(_obj._filter.init(fils[i], _view_des));
        ASSERT_TRUE(_obj._refer.init(refs[i], _view_des));
        anti::themis::log_parser_lib::CareExp care_exp;
        _obj._refer_str = refs[i];
        _obj._filter_str = fils[i];
        ASSERT_TRUE(care_exp.append(fils[i], _view_des));
        ASSERT_TRUE(care_exp.append(refs[i], _view_des));
        ASSERT_TRUE(care_exp.reset(&log));
        _obj._stype = FeatureValueProto::TIME;
        ASSERT_TRUE(_obj._extract(care_exp, &log, &feas));
        _obj._stype = FeatureValueProto::PV;
        ASSERT_TRUE(_obj._extract(care_exp, &log, &feas));
        _obj._filter.uninit();
        _obj._refer.uninit();
    }

    ASSERT_EQ(6U, feas.size());
    uint64_t exp_coord[] = {110L, 8L, 110L, 8L, 110L, 8L};
    FeatureValueProto::SegType exp_stype[] = {
            FeatureValueProto::TIME,
            FeatureValueProto::PV,
            FeatureValueProto::TIME,
            FeatureValueProto::PV,
            FeatureValueProto::TIME,
            FeatureValueProto::PV
    };
    bool ext_fil[] = {true, true, true, true, false, false};
    bool ext_ref[] = {true, true, false, false, true, true};
    for (uint32_t i = 0; i < 6U; ++i) {
        EXPECT_EQ(0LU, feas[i]->feature_id());
        EXPECT_EQ(123456LU, feas[i]->view_sign());
        EXPECT_EQ(FeatureValueProto::RATIO, feas[i]->feature_type());
        EXPECT_EQ(120LU, feas[i]->joinkey());
        EXPECT_EQ(119LU, feas[i]->log_id());
        EXPECT_EQ(110000LU, feas[i]->log_time());
        EXPECT_STREQ("sign", feas[i]->view().data());
        EXPECT_EQ(exp_stype[i], feas[i]->seg_type());
        EXPECT_EQ(exp_coord[i], feas[i]->coord());
        EXPECT_EQ(ext_fil[i], feas[i]->in_filter());
        EXPECT_EQ(ext_ref[i], feas[i]->in_refer());
    }
}

TEST_F(RatioFeatureExtractorTestSuite, _extract_kpi_case) {
    comcfg::Configure conf;
    ASSERT_TRUE(conf.load("./conf", "feature_extractor_test.conf") == 0);
    ASSERT_TRUE(_obj._init(conf["kpi"]["valid"]["feature"][4], _view_des, &_care_exp));

    MockLogRecordInterface log;
    ViewValueProto value;
    value.set_type(ViewValueProto::INT64);
    auto v =  value.add_values();
    v->set_str_value("888");
    v->set_int_value(888);
    value.add_signs(123456LU);
    EXPECT_CALL(log, get_view_sign(_, _))
            .WillOnce(Return(false))
            .WillRepeatedly(DoAll(SetArgPointee<1>(value), Return(true)));
    EXPECT_CALL(log, get_view_value(_, _))
            .WillOnce(Return(false))
            .WillRepeatedly(DoAll(SetArgPointee<1>(value), Return(true)));
    EXPECT_CALL(log, log_id()).WillRepeatedly(Return(119LU));
    EXPECT_CALL(log, log_time()).WillRepeatedly(Return(110000LU));
    EXPECT_CALL(log, joinkey()).WillRepeatedly(Return(120LU));

    ASSERT_TRUE(_care_exp.reset(&log));
    std::vector<std::shared_ptr<FeatureValueProto> > feas;
    ASSERT_FALSE(_obj._extract(_care_exp, &log, &feas));
    EXPECT_EQ(0U, feas.size());

    ASSERT_FALSE(_obj._extract(_care_exp, &log, &feas));
    EXPECT_EQ(0U, feas.size());

    ASSERT_TRUE(_obj._extract(_care_exp, &log, &feas));
    EXPECT_EQ(1U, feas.size());
    EXPECT_EQ(888, feas[0]->filter_count());
    EXPECT_EQ(123456LU, feas[0]->data_view_sign());
    EXPECT_STREQ("888", feas[0]->data_view_value().data());
    _obj._uninit();
}


}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

