// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// 
// @Author: wujun08(<EMAIL>)
// @Last modified: 2016-04-19 11:52:43
// @Brief: 

#include <sstream>
#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <Configure.h>
#include "hijack_feature_extractor.h"
#include "mock_log_record_interface.h"
#include "com_log.h"

namespace anti {
namespace themis {
namespace feature_lib {

typedef std::shared_ptr<FeatureValueProto> FeaturePtr;

using anti::themis::log_interface::ViewDescriptor;
using anti::themis::log_interface::ViewValueProto;
using anti::themis::feature_lib::FeatureValueProto;
using ::testing::_;
using ::testing::Return;
using ::testing::DoAll;
using ::testing::SetArgPointee;
using ::testing::Invoke;
using ::testing::InSequence;

class HijackFeatureExtractorTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
        const std::string log_type = "AspLog";
        _view_des.push_back({log_type, 4U, 1U, "flow_group"});
        _view_des.push_back({log_type, 19U, 1U, "pn"});
        _view_des.push_back({log_type, 63U, 1U, "unique_cn"});
    }

    virtual void TearDown() {
    }

private:
    const std::string _conf_path = "./conf";
    const std::string _conf_file = "hijack_extractor_test.conf";
    HijackFeatureExtractor _obj;
    std::vector<ViewDescriptor> _view_des;
};

TEST_F(HijackFeatureExtractorTestSuite, construction) {
    ASSERT_EQ(FeatureValueProto::TIME_DIFF, _obj._ftype);
}

TEST_F(HijackFeatureExtractorTestSuite, _init_fail) {
    comcfg::Configure conf;
    ASSERT_TRUE(conf.load(_conf_path.c_str(), _conf_file.c_str()) == 0);
    for (uint32_t i = 0U; i < conf["invalid"]["feature"].size(); ++i) {
        CDEBUG_LOG("invalid feature: %u", i);
        EXPECT_FALSE(_obj._init(conf["invalid"]["feature"][i], _view_des));
    }
}

TEST_F(HijackFeatureExtractorTestSuite, _init_succ) {
    comcfg::Configure conf;
    ASSERT_TRUE(conf.load(_conf_path.c_str(), _conf_file.c_str()) == 0);
    for (uint32_t i = 0U; i < conf["valid"]["feature"].size(); ++i) {
        CDEBUG_LOG("valid feature: %u", i);
        EXPECT_TRUE(_obj._init(conf["valid"]["feature"][i], _view_des));
    }
}

TEST_F(HijackFeatureExtractorTestSuite, get_hijack_field_succ) {
    FeatureValueProto::HijackProto value;

    comcfg::Configure conf;
    ASSERT_TRUE(conf.load(_conf_path.c_str(), _conf_file.c_str()) == 0);
    ASSERT_TRUE(_obj._init(conf["valid"]["feature"][0], _view_des));

    const std::string unique_cn = "test_cn";
    const std::string flow_group = "test_flow_group";
    int64_t pn = 0;

    ViewValueProto page_no_pb = get_value("0"); 
    page_no_pb.mutable_values(0)->set_int_value(pn);

    ViewValueProto charge_name_pb = get_value(unique_cn);
    ViewValueProto flow_group_pb = get_value(flow_group);

    MockLogRecordInterface log;
    { 
        InSequence s;
        EXPECT_CALL(log, get_view_value(_, _)).
            WillOnce(DoAll(SetArgPointee<1>(charge_name_pb), Return(true)));
        EXPECT_CALL(log, get_view_value(_, _)).
            WillOnce(DoAll(SetArgPointee<1>(flow_group_pb), Return(true)));
        EXPECT_CALL(log, get_view_value(_, _)).
            WillOnce(DoAll(SetArgPointee<1>(page_no_pb), Return(true)));
    }

    ASSERT_TRUE(_obj.get_hijack_field(&log, &value));
    CDEBUG_LOG("%s", value.charge_name().c_str());
    CDEBUG_LOG("%s", value.flow_group().c_str());
    ASSERT_STREQ(value.charge_name().c_str(), unique_cn.c_str());
    ASSERT_STREQ(value.flow_group().c_str(), flow_group.c_str());
    ASSERT_EQ(value.page_no(), pn);
}

TEST_F(HijackFeatureExtractorTestSuite, get_hijack_field_fail_gt_one_field) {
    FeatureValueProto::HijackProto value;

    comcfg::Configure conf;
    ASSERT_TRUE(conf.load(_conf_path.c_str(), _conf_file.c_str()) == 0);
    ASSERT_TRUE(_obj._init(conf["valid"]["feature"][0], _view_des));

    const std::string unique_cn = "test_cn";
    const std::string flow_group = "test_flow_group";
    int64_t pn = 0;

    ViewValueProto page_no_pb = get_value("0"); 
    page_no_pb.mutable_values(0)->set_int_value(pn);

    ViewValueProto charge_name_pb = get_value(unique_cn);
    charge_name_pb.add_values()->set_str_value(unique_cn);
    
    ViewValueProto flow_group_pb = get_value(flow_group);

    MockLogRecordInterface log;
    { 
        InSequence s;
        EXPECT_CALL(log, get_view_value(_, _)).
            WillOnce(DoAll(SetArgPointee<1>(charge_name_pb), Return(true)));
        EXPECT_CALL(log, get_view_value(_, _)).
            WillOnce(DoAll(SetArgPointee<1>(flow_group_pb), Return(true)));
        EXPECT_CALL(log, get_view_value(_, _)).
            WillOnce(DoAll(SetArgPointee<1>(page_no_pb), Return(true)));
    }

    ASSERT_FALSE(_obj.get_hijack_field(&log, &value));
}

TEST_F(HijackFeatureExtractorTestSuite, _extract_get_view_sign_fail) {
    ViewValueProto value = get_value("123");

    comcfg::Configure conf;
    ASSERT_TRUE(conf.load(_conf_path.c_str(), _conf_file.c_str()) == 0);
    ASSERT_TRUE(_obj._init(conf["valid"]["feature"][0], _view_des));

    MockLogRecordInterface log;
    EXPECT_CALL(log, get_view_sign(_, _))
        .WillOnce(DoAll(SetArgPointee<1>(value), Return(false)));
    EXPECT_CALL(log, log_id()).WillOnce(Return(100));
    std::vector<FeaturePtr> feas;
    EXPECT_FALSE(_obj._extract(&log, &feas));
}

TEST_F(HijackFeatureExtractorTestSuite, _extract_fail) {
    EXPECT_FALSE(_obj._extract(NULL, NULL));

    MockLogRecordInterface log;
    EXPECT_FALSE(_obj._extract(&log, NULL));
    std::vector<FeaturePtr> feas;
    EXPECT_FALSE(_obj._extract(NULL, &feas));
}

TEST_F(HijackFeatureExtractorTestSuite, _extract_succ) {

    comcfg::Configure conf;
    ASSERT_TRUE(conf.load(_conf_path.c_str(), _conf_file.c_str()) == 0);
    ASSERT_TRUE(_obj._init(conf["valid"]["feature"][0], _view_des));

    const std::string unique_cn = "test_cn";
    const std::string flow_group = "test_flow_group";
    int64_t pn = 0;

    ViewValueProto page_no_pb = get_value("0"); 
    page_no_pb.mutable_values(0)->set_int_value(pn);

    ViewValueProto charge_name_pb = get_value(unique_cn);
    ViewValueProto flow_group_pb = get_value(flow_group);

    std::stringstream ss;
    ss << unique_cn << "_" << flow_group << "_" << pn; 

    ViewValueProto value = get_value(ss.str());
    MockLogRecordInterface log;
    { 
        InSequence s;
        EXPECT_CALL(log, get_view_value(_, _)).
            WillOnce(DoAll(SetArgPointee<1>(charge_name_pb), Return(true)));
        EXPECT_CALL(log, get_view_value(_, _)).
            WillOnce(DoAll(SetArgPointee<1>(flow_group_pb), Return(true)));
        EXPECT_CALL(log, get_view_value(_, _)).
            WillOnce(DoAll(SetArgPointee<1>(page_no_pb), Return(true)));
    }

    const uint64_t expect_feature_id = 
        conf["valid"]["feature"][0]["feature_id"].to_uint64();
    const uint64_t expect_log_id = 1234;
    const uint64_t expect_log_time = 10000;
    const uint64_t expect_joinkey = 12345678;

    _obj._feature_id = expect_feature_id;
    EXPECT_CALL(log, get_view_sign(_, _))
            .WillOnce(DoAll(SetArgPointee<1>(value), Return(true)));
    EXPECT_CALL(log, log_id()).WillRepeatedly(Return(expect_log_id));
    EXPECT_CALL(log, log_time()).WillRepeatedly(Return(expect_log_time));
    EXPECT_CALL(log, joinkey()).WillRepeatedly(Return(expect_joinkey));

    std::vector<FeaturePtr> feas;
    ASSERT_TRUE(_obj._extract(&log, &feas));
    ASSERT_EQ(value.values_size(), feas.size());
    ASSERT_EQ(expect_feature_id, feas[0]->feature_id());
    ASSERT_EQ(expect_log_id, feas[0]->log_id());
    ASSERT_EQ(expect_log_time, feas[0]->log_time());    
    ASSERT_EQ(expect_log_time / 1000, feas[0]->coord());
    ASSERT_EQ(expect_joinkey, feas[0]->joinkey());
    ASSERT_STREQ(feas[0]->original_hijack_field().charge_name().c_str(), unique_cn.c_str());
    ASSERT_STREQ(feas[0]->original_hijack_field().flow_group().c_str(), flow_group.c_str());
    ASSERT_EQ(feas[0]->original_hijack_field().page_no(), pn);
}

}
}
}
