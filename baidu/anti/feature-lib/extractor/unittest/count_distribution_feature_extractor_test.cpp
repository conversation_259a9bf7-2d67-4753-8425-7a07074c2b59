// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: count_distribution_feature_extractor_test.cpp
// @Last modified: 2018-03-29 17:06:27
// @Brief: 

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <Configure.h>
#include "count_distribution_feature_extractor.h"
using ::testing::_;
using ::testing::Return;
using ::testing::DoAll;
using ::testing::SetArgPointee;

using anti::themis::log_interface::ViewDescriptor;
using anti::themis::log_interface::LogRecordInterface;
using anti::themis::log_interface::ViewValueProto;

namespace anti {
namespace themis {
namespace feature_lib {

class CountDistributionFeatureExtractorTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
        ASSERT_TRUE(_conf.load("./conf", "feature_extractor_test.conf") == 0);
        _view_des.push_back({"ClickBaiduLog", 0U, 1U, "cntname"});
        _view_des.push_back({"ClickBaiduLog", 1U, 1U, "clkip"});
    }
    virtual void TearDown() {}
private:
    CountDistributionFeatureExtractor _obj;
    std::vector<ViewDescriptor> _view_des;
    comcfg::Configure _conf;
    anti::themis::log_parser_lib::CareExp _care_exp;
};

TEST_F(CountDistributionFeatureExtractorTestSuite, construction_case) {
    EXPECT_EQ(FeatureValueProto::COUNT_DISTRIBUTION, _obj._ftype);
    EXPECT_EQ(FeatureValueProto::PV, _obj._stype);
}

TEST_F(CountDistributionFeatureExtractorTestSuite, init_case) {
    // invalid case
    uint32_t conf_size = _conf["count_dist"]["invalid"]["feature"].size();
    ASSERT_EQ(5U, conf_size);
    for (uint32_t i = 0; i <= conf_size; i++) {
        ASSERT_FALSE(_obj.init(_conf["count_dist"]["invalid"]["feature"][i], 
                _view_des, &_care_exp));
        _obj.uninit();
    }
    // valid case 
    ASSERT_TRUE(_obj.init(_conf["count_dist"]["valid"]["feature"][0], _view_des, &_care_exp));
    EXPECT_EQ(FeatureValueProto::COUNT_DISTRIBUTION, _obj._ftype);
    EXPECT_EQ(FeatureValueProto::PV, _obj._stype);
    ASSERT_EQ(2U, _obj._data_views.size());
    EXPECT_EQ(0U, _obj._data_views[0].view_id);
    EXPECT_EQ(1U, _obj._data_views[1].view_id);
    _obj.uninit();
}

class MockLogRecordInterface : public LogRecordInterface {
public:
    virtual ~MockLogRecordInterface() {}
    MOCK_METHOD2(init, bool(const std::string&, const std::string&));
    MOCK_METHOD0(uninit, void());
    MOCK_CONST_METHOD1(get_view_descriptor, const ViewDescriptor*(const std::string&));
    MOCK_CONST_METHOD0(view_descriptors, const std::vector<ViewDescriptor>*());
    MOCK_METHOD2(parse, bool(const char*, uint64_t));
    MOCK_METHOD0(clear, void());
    MOCK_METHOD1(reset_log_level, bool(uint32_t));
    MOCK_CONST_METHOD0(has_next, bool());
    MOCK_METHOD0(next, bool());
    MOCK_CONST_METHOD0(joinkey, uint64_t());
    MOCK_CONST_METHOD0(log_id, uint64_t());
    MOCK_CONST_METHOD0(log_time, uint64_t());
    MOCK_CONST_METHOD0(is_spam, bool());
    MOCK_METHOD2(get_view_value, bool(const ViewDescriptor&, ViewValueProto*));
    MOCK_METHOD2(get_view_sign, bool(const std::vector<ViewDescriptor>&, ViewValueProto*));
    MOCK_METHOD1(next, bool(int32_t*));
};

TEST_F(CountDistributionFeatureExtractorTestSuite, _extract_by_invalid_input_case) {
    MockLogRecordInterface log;
    ViewValueProto value[2];
    value[0].set_type(ViewValueProto::STRING);
    value[0].add_values()->set_str_value("123");
    value[0].add_signs(123LU);
    value[1].set_type(ViewValueProto::STRING);
    value[1].add_values()->set_str_value("456");
    // 1. mock for get data_view sign false: get_view_sign return false 
    // 2&3. mock for get data_view sign false: value size and sign size unequal
    // 4. mock for get view sign false: get_view_sign return false
    // 5&6. mock for get view sign false: value size and sign size unequal
    EXPECT_CALL(log, get_view_sign(_, _))
            .WillOnce(Return(false))
            .WillOnce(DoAll(SetArgPointee<1>(value[1]), Return(false)))
            .WillOnce(DoAll(SetArgPointee<1>(value[0]), Return(true)))
            .WillOnce(Return(false))
            .WillOnce(DoAll(SetArgPointee<1>(value[0]), Return(true)))
            .WillOnce(DoAll(SetArgPointee<1>(value[1]), Return(true)));
    EXPECT_CALL(log, log_id()).WillRepeatedly(Return(119LU));

    EXPECT_FALSE(_obj._extract(NULL, NULL));
    std::vector<std::shared_ptr<FeatureValueProto> > feas;
    // get data_view sign false
    EXPECT_FALSE(_obj._extract(&log, &feas));
    EXPECT_FALSE(_obj._extract(&log, &feas));
    // get data sign false
    EXPECT_FALSE(_obj._extract(&log, &feas));
    EXPECT_FALSE(_obj._extract(&log, &feas));
}

TEST_F(CountDistributionFeatureExtractorTestSuite, _extract_valid_case) {
    MockLogRecordInterface log;
    std::vector<std::shared_ptr<FeatureValueProto> > feas;
    ViewValueProto value;
    value.set_type(ViewValueProto::STRING);
    value.add_values()->set_str_value("123");
    value.add_signs(123LU);
    ViewValueProto data_value;
    data_value.add_values()->set_str_value("456");
    data_value.add_signs(456U);
    EXPECT_CALL(log, get_view_sign(_, _))
            .WillOnce(DoAll(SetArgPointee<1>(data_value), Return(true)))
            .WillOnce(DoAll(SetArgPointee<1>(value), Return(true)));
    EXPECT_CALL(log, log_id()).WillRepeatedly(Return(222LU));
    EXPECT_CALL(log, log_time()).WillRepeatedly(Return(110000LU));
    EXPECT_CALL(log, joinkey()).WillRepeatedly(Return(120LU));
    _obj._stype = FeatureValueProto::PV;
    _obj._ftype = FeatureValueProto::COUNT_DISTRIBUTION;
    _obj._pv_coord = 8L; 

    ASSERT_TRUE(_obj._extract(&log, &feas));
    ASSERT_EQ(1U, feas.size());
    EXPECT_EQ(0LU, feas[0]->feature_id());
    EXPECT_EQ(123LU, feas[0]->view_sign());
    EXPECT_EQ(456LU, feas[0]->data_view_sign());
    EXPECT_EQ(FeatureValueProto::PV, feas[0]->seg_type());
    EXPECT_EQ(FeatureValueProto::COUNT_DISTRIBUTION, feas[0]->feature_type());
    EXPECT_EQ(8L, feas[0]->coord());
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */
