// Copyright 2016 Baidu Inc. All Right Reserved.
// Author <PERSON> (<EMAIL>)
// 
// brief: 

#include <com_log.h>
#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <Configure.h>
#include "distinct_distribution_feature_extractor.h"

namespace anti {
namespace themis {
namespace feature_lib {

using ::testing::_;
using ::testing::Return;
using ::testing::DoAll;
using ::testing::SetArgPointee;

using anti::themis::log_interface::ViewDescriptor;
using anti::themis::log_interface::LogRecordInterface;
using anti::themis::log_interface::ViewValueProto;

class DistinctDistributionFeatureExtractorTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
        ASSERT_TRUE(_conf.load("./conf", "feature_extractor_test.conf") == 0);
        _view_des.push_back({"ClickBaiduLog", 0U, 1U, "userid"});
        _view_des.push_back({"C<PERSON>BaiduLog", 1U, 1U, "baiduid"});
        _view_des.push_back({"ClickBaiduLog", 2U, 1U, "ua"});
    }
    virtual void TearDown() {}
private:
    DistinctDistributionFeatureExtractor _obj;
    std::vector<ViewDescriptor> _view_des;
    comcfg::Configure _conf;
    anti::themis::log_parser_lib::CareExp _care_exp;
};

TEST_F(DistinctDistributionFeatureExtractorTestSuite, construct) {
    DistinctDistributionFeatureExtractor obj;
    EXPECT_EQ(FeatureValueProto::DISTINCT_DISTRIBUTION, obj._ftype);
    EXPECT_EQ(FeatureValueProto::PV, obj._stype);
}

TEST_F(DistinctDistributionFeatureExtractorTestSuite, init_case) {
    // invalid case
    uint32_t conf_size = _conf["distinct_dis"]["invalid"]["feature"].size();
    ASSERT_EQ(5U, conf_size);
    for (uint32_t i = 0; i <= conf_size; i++) {
        ASSERT_FALSE(_obj.init(_conf["distinct_dis"]["invalid"]["feature"][i],
                _view_des, &_care_exp));
        _obj.uninit();
    }
    // valid case 
    ASSERT_TRUE(_obj.init(_conf["distinct_dis"]["valid"]["feature"][0], _view_des, &_care_exp));
    EXPECT_EQ(FeatureValueProto::DISTINCT_DISTRIBUTION, _obj._ftype);
    EXPECT_EQ(FeatureValueProto::PV, _obj._stype);
    ASSERT_EQ(2U, _obj._data_views.size());
    EXPECT_EQ(0U, _obj._data_views[0].view_id);
    EXPECT_EQ(1U, _obj._data_views[1].view_id);
    EXPECT_EQ(1U, _obj._cumulate_views.size());
    _obj.uninit();
}

class MockLogRecordInterface : public LogRecordInterface {
public:
    virtual ~MockLogRecordInterface() {}
    MOCK_METHOD1(next, bool(int32_t*));
    MOCK_METHOD2(init, bool(const std::string&, const std::string&));
    MOCK_METHOD0(uninit, void());
    MOCK_CONST_METHOD1(get_view_descriptor, const ViewDescriptor*(const std::string&));
    MOCK_CONST_METHOD0(view_descriptors, const std::vector<ViewDescriptor>*());
    MOCK_METHOD2(parse, bool(const char*, uint64_t));
    MOCK_METHOD0(clear, void());
    MOCK_METHOD1(reset_log_level, bool(uint32_t));
    MOCK_CONST_METHOD0(has_next, bool());
    MOCK_METHOD0(next, bool());
    MOCK_CONST_METHOD0(joinkey, uint64_t());
    MOCK_CONST_METHOD0(log_id, uint64_t());
    MOCK_CONST_METHOD0(log_time, uint64_t());
    MOCK_CONST_METHOD0(is_spam, bool());
    MOCK_METHOD2(get_view_value, bool(const ViewDescriptor&, ViewValueProto*));
    MOCK_METHOD2(get_view_sign, bool(const std::vector<ViewDescriptor>&, ViewValueProto*));
};

TEST_F(DistinctDistributionFeatureExtractorTestSuite, 
        _extract_by_invalid_input_case) {
    MockLogRecordInterface log;
    ViewValueProto value[2];
    value[0].set_type(ViewValueProto::STRING);
    value[0].add_values()->set_str_value("123");
    value[0].add_signs(123LU);
    value[1].set_type(ViewValueProto::STRING);
    value[1].add_values()->set_str_value("456");
    // 1. mock for get data_view sign false: get_view_sign return false 
    // 2&3. mock for get data_view sign false: value size and sign size unequal
    // 4. mock for get view sign false: get_view_sign return false
    // 5&6. mock for get view sign false: value size and sign size unequal
    EXPECT_CALL(log, get_view_sign(_, _))
            .WillOnce(Return(false))
            .WillOnce(DoAll(SetArgPointee<1>(value[1]), Return(false)))
            .WillOnce(DoAll(SetArgPointee<1>(value[0]), Return(true)))
            .WillOnce(Return(false))
            .WillOnce(DoAll(SetArgPointee<1>(value[0]), Return(true)))
            .WillOnce(DoAll(SetArgPointee<1>(value[1]), Return(true)));
    EXPECT_CALL(log, log_id()).WillRepeatedly(Return(119LU));

    EXPECT_FALSE(_obj._extract(NULL, NULL));
    std::vector<std::shared_ptr<FeatureValueProto> > feas;
    // get data_view sign false
    EXPECT_FALSE(_obj._extract(&log, &feas));
    EXPECT_FALSE(_obj._extract(&log, &feas));
    // get data sign false
    EXPECT_FALSE(_obj._extract(&log, &feas));
    EXPECT_FALSE(_obj._extract(&log, &feas));
}

TEST_F(DistinctDistributionFeatureExtractorTestSuite, _extract_valid_case) {
    MockLogRecordInterface log;
    std::vector<std::shared_ptr<FeatureValueProto> > feas;
    ViewValueProto value;
    value.set_type(ViewValueProto::STRING);
    value.add_values()->set_str_value("123");
    value.add_signs(123LU);
    value.add_values()->set_str_value("888");
    value.add_signs(888LU);
    ViewValueProto data_value;
    data_value.add_values()->set_str_value("456");
    data_value.add_signs(456U);
    data_value.add_values()->set_str_value("888");
    data_value.add_signs(888U);
    ViewValueProto cumulate_value;
    cumulate_value.add_values()->set_str_value("789");
    cumulate_value.add_signs(789U);
    EXPECT_CALL(log, get_view_sign(_, _))
            .WillOnce(DoAll(SetArgPointee<1>(value), Return(true)))
            .WillOnce(DoAll(SetArgPointee<1>(data_value), Return(true)))
            .WillOnce(DoAll(SetArgPointee<1>(cumulate_value), Return(true)));
    EXPECT_CALL(log, log_id()).WillRepeatedly(Return(222LU));
    EXPECT_CALL(log, log_time()).WillRepeatedly(Return(110000LU));
    EXPECT_CALL(log, joinkey()).WillRepeatedly(Return(120LU));
    _obj._stype = FeatureValueProto::PV;
    _obj._ftype = FeatureValueProto::DISTINCT_DISTRIBUTION;
    _obj._pv_coord = 8L; 

    ASSERT_TRUE(_obj._extract(&log, &feas));
    ASSERT_EQ(2U, feas.size());
    EXPECT_EQ(0LU, feas[0]->feature_id());
    EXPECT_EQ(123LU, feas[0]->view_sign());
    EXPECT_EQ(456LU, feas[0]->data_view_sign());
    EXPECT_EQ(789LU, feas[0]->cumulate_view_sign());
    EXPECT_EQ(FeatureValueProto::PV, feas[0]->seg_type());
    EXPECT_EQ(FeatureValueProto::DISTINCT_DISTRIBUTION, feas[0]->feature_type());
    EXPECT_EQ(8L, feas[0]->coord());

    EXPECT_EQ(0LU, feas[1]->feature_id());
    EXPECT_EQ(888LU, feas[1]->view_sign());
    EXPECT_EQ(888LU, feas[1]->data_view_sign());
    EXPECT_EQ(789LU, feas[1]->cumulate_view_sign());
    EXPECT_EQ(FeatureValueProto::PV, feas[1]->seg_type());
    EXPECT_EQ(FeatureValueProto::DISTINCT_DISTRIBUTION, feas[1]->feature_type());
    EXPECT_EQ(8L, feas[1]->coord());
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

