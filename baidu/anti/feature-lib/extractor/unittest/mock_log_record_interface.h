#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_UNITTEST_MOCK_LOG_RECORD_INTERFACE_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_UNITTEST_MOCK_LOG_RECORD_INTERFACE_H

#include <gmock/gmock.h>
#include <sign_util.h>
#include <log_record_interface.h>

namespace anti {
namespace themis {
namespace feature_lib {

class MockLogRecordInterface : 
    public anti::themis::log_interface::LogRecordInterface {
public:
    virtual ~MockLogRecordInterface() {}
    MOCK_METHOD1(next, bool(int32_t*));
    MOCK_METHOD2(init, bool(const std::string&, const std::string&));
    MOCK_METHOD0(uninit, void());
    MOCK_CONST_METHOD1(get_view_descriptor, 
        const anti::themis::log_interface::ViewDescriptor*(const std::string&));
    MOCK_CONST_METHOD0(view_descriptors, 
        const std::vector<anti::themis::log_interface::ViewDescriptor>*());

    MOCK_METHOD2(parse, bool(const char*, uint64_t));
    MOCK_METHOD0(clear, void());
    MOCK_METHOD1(reset_log_level, bool(uint32_t));
    MOCK_CONST_METHOD0(has_next, bool());
    MOCK_METHOD0(next, bool());

    MOCK_CONST_METHOD0(joinkey, uint64_t());
    MOCK_CONST_METHOD0(log_id, uint64_t());
    MOCK_CONST_METHOD0(log_time, uint64_t());
    MOCK_CONST_METHOD0(is_spam, bool());

    MOCK_METHOD2(get_view_value, bool(
        const anti::themis::log_interface::ViewDescriptor&, 
        anti::themis::log_interface::ViewValueProto*));
    MOCK_METHOD2(get_view_sign, bool(
        const std::vector<anti::themis::log_interface::ViewDescriptor>&, 
        anti::themis::log_interface::ViewValueProto*));
};

anti::themis::log_interface::ViewValueProto get_value(const std::string& str_value) {
    anti::themis::log_interface::ViewValueProto value;
    value.add_values()->set_str_value(str_value);
    uint64_t sign;
    anti::baselib::SignUtil::create_sign_md64(str_value, &sign);
    value.add_signs(sign);
    return value;
}

}
}
}

#endif // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_UNITTEST_MOCK_LOG_RECORD_INTERFACE_H
