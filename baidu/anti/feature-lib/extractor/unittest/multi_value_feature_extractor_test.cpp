// Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
// @Author: linfan02
// 
// @File: 

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <sign_util.h>
#include <Configure.h>
#include "multi_value_feature_extractor.h"

using anti::themis::log_interface::ViewDescriptor;
using anti::themis::log_interface::LogRecordInterface;
using anti::themis::log_interface::ViewValueProto;
using anti::baselib::SignUtil;
using ::testing::_;
using ::testing::Return;
using ::testing::DoAll;
using ::testing::SetArgPointee;

namespace anti {
namespace themis {
namespace feature_lib {

class MultiValueFeatureExtractorTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
        _view_des.push_back({"ClickBaiduLog", 0U, 1U, "userid"});
        _view_des.push_back({"ClickBaiduLog", 1U, 1U, "price"});
    }
    virtual void TearDown() {}

private:
    MultiValueFeatureExtractor _obj;
    std::vector<ViewDescriptor> _view_des;
};

TEST_F(MultiValueFeatureExtractorTestSuite, construction_case) {
    EXPECT_EQ(FeatureValueProto::MULTI_VALUE, _obj._ftype);
}

TEST_F(MultiValueFeatureExtractorTestSuite, _init_case) {
    comcfg::Configure conf;
    ASSERT_TRUE(conf.load("./conf", "feature_extractor_test.conf") == 0);
    uint32_t conf_size = conf["multi_value"]["invalid"]["feature"].size();
    ASSERT_LE(2U, conf_size);
    for (uint32_t i = 0U; i < 2U; ++i) {
        ASSERT_FALSE(_obj._init(conf["multi_value"]["invalid"]["feature"][i], _view_des));
        _obj._uninit();
    }

    ASSERT_TRUE(_obj._init(conf["multi_value"]["valid"]["feature"][0], _view_des));
    EXPECT_EQ(FeatureValueProto::MULTI_VALUE, _obj._ftype);
    _obj._uninit();
}

class MockLogRecordInterface : public LogRecordInterface {
public:
    virtual ~MockLogRecordInterface() {}
    MOCK_METHOD1(next, bool(int32_t*));
    MOCK_METHOD2(init, bool(const std::string&, const std::string&));
    MOCK_METHOD0(uninit, void());
    MOCK_CONST_METHOD1(get_view_descriptor, const ViewDescriptor*(const std::string&));
    MOCK_CONST_METHOD0(view_descriptors, const std::vector<ViewDescriptor>*());

    MOCK_METHOD2(parse, bool(const char*, uint64_t));
    MOCK_METHOD0(clear, void());
    MOCK_METHOD1(reset_log_level, bool(uint32_t));
    MOCK_CONST_METHOD0(has_next, bool());
    MOCK_METHOD0(next, bool());

    MOCK_CONST_METHOD0(joinkey, uint64_t());
    MOCK_CONST_METHOD0(log_id, uint64_t());
    MOCK_CONST_METHOD0(log_time, uint64_t());
    MOCK_CONST_METHOD0(is_spam, bool());

    MOCK_METHOD2(get_view_value, bool(const ViewDescriptor&, ViewValueProto*));
    MOCK_METHOD2(get_view_sign, bool(const std::vector<ViewDescriptor>&, ViewValueProto*));
};

TEST_F(MultiValueFeatureExtractorTestSuite, _extract_by_valid_input_case) {
    comcfg::Configure conf;
    ASSERT_TRUE(conf.load("./conf", "feature_extractor_test.conf") == 0);
    ASSERT_TRUE(_obj._init(conf["multi_value"]["valid"]["feature"][0], _view_des));

    MockLogRecordInterface log;
    std::vector<std::shared_ptr<FeatureValueProto> > feas;

    std::string exp_str[] = {"12", "111"};
    uint64_t exp_sign[] = {0, 0};
    ViewValueProto value;
    for (uint32_t i = 0U; i < 2U; ++i) {
        SignUtil::create_sign_md64(exp_str[i], &exp_sign[i]);
        value.add_values()->set_str_value(exp_str[i]);
        value.add_signs(exp_sign[i]);
    }
    EXPECT_CALL(log, get_view_sign(_, _))
            .WillRepeatedly(DoAll(SetArgPointee<1>(value), Return(true)));
    EXPECT_CALL(log, log_id()).WillRepeatedly(Return(119LU));
    EXPECT_CALL(log, log_time()).WillRepeatedly(Return(110000LU));
    EXPECT_CALL(log, joinkey()).WillRepeatedly(Return(120LU));

    ASSERT_TRUE(_obj._extract(&log, &feas));
    ASSERT_EQ(1U, feas.size());

    EXPECT_EQ(0LU, feas[0]->feature_id());
    EXPECT_EQ(exp_sign[0], feas[0]->view_sign());
    EXPECT_EQ(120LU, feas[0]->joinkey());
    EXPECT_EQ(119LU, feas[0]->log_id());
    EXPECT_EQ(110000LU, feas[0]->log_time());
    EXPECT_EQ(110L, feas[0]->coord());
    EXPECT_TRUE(feas[0]->valid());

    ASSERT_EQ(2, feas[0]->values_size());
    ASSERT_EQ(2, feas[0]->views_size());
    for (uint32_t i = 0; i < 2; ++i) {
        EXPECT_STREQ(exp_str[i].data(), feas[0]->views(i).data());
        EXPECT_STREQ(exp_str[i].data(), feas[0]->values(i).data());
    }

    _obj._uninit();
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */
