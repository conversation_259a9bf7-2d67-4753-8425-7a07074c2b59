// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: feature_extractor_manager_test.cpp
// @Last modified: 2018-04-13 17:22:20
// @Brief: 

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "feature_extractor_manager.h"
#include "anti_asp_display.pb.h"
#include "anti_base.pb.h"
#include "com_log.h"
#include "log_record_factory.h"

using anti::themis::log_interface::ViewDescriptor;
using anti::themis::log_interface::LogRecordInterface;
using anti::themis::log_interface::ViewValueProto;
using anti::themis::AspDisplayLog;
using ::testing::_;
using ::testing::Return;
using ::testing::DoAll;
using ::testing::SetArgPointee;

namespace anti {
namespace themis {
namespace feature_lib {

class FeatureExtractorManagerTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
        _view_des.push_back({"ClickBaiduLog", 0U, 1U, "userid"});
        _view_des.push_back({"ClickBaiduLog", 1U, 1U, "price"});
        _asp_view_des.push_back({"AspLog", 0U, 1U, "baiduid"});
        _asp_view_des.push_back({"AspLog", 10U, 3U, "src_id"});
    }
    virtual void TearDown() {}

private:
    std::vector<ViewDescriptor> _view_des;
    std::vector<ViewDescriptor> _asp_view_des;
    FeatureExtractorManager _obj;
};

TEST_F(FeatureExtractorManagerTestSuite, init_case) {
    EXPECT_FALSE(_obj.init("./conf", "xxxx", _view_des));
    _obj.uninit();
    
    EXPECT_FALSE(_obj.init("./conf", "empty.conf", _view_des));
    _obj.uninit();

    EXPECT_FALSE(_obj.init("./conf", "feature_extractor_manger_invalid.conf", _view_des));
    _obj.uninit();

    EXPECT_FALSE(_obj.init("./conf", "feature_extractor_manger_invalid1.conf", _view_des));
    _obj.uninit();

    ASSERT_TRUE(_obj.init("./conf", "feature_extractor_manger_test.conf", _view_des));
    ASSERT_EQ(3U, _obj._exts[0].size());
    for (uint32_t i = 0U; i < _obj._exts[0].size(); ++i) {
        ASSERT_TRUE(_obj._exts[0][i] != NULL);
        EXPECT_EQ(i + 1U, _obj._exts[0][i]->feature_id());
    }
    _obj.uninit();

    ASSERT_TRUE(_obj.init("./conf", "feature_extractor_manger_test1.conf", _asp_view_des));
    ASSERT_EQ(1U, _obj._exts[2].size());
    ASSERT_TRUE(_obj._exts[2][0] != NULL);
    EXPECT_EQ(1U, _obj._exts[2][0]->feature_id());
    _obj.uninit();

    ASSERT_TRUE(_obj.init("./conf", "fea_mgr_cmp_test.conf", _view_des));
    ASSERT_EQ(4U, _obj._exts[0].size());
    uint32_t exp_id[] = {1,2,3,4};
    for (uint32_t i = 0U; i < _obj._exts[0].size(); ++i) {
        ASSERT_TRUE(_obj._exts[0][i] != NULL);
        EXPECT_EQ(exp_id[i], _obj._exts[0][i]->feature_id());
    }
    _obj.uninit();

    ASSERT_TRUE(_obj.init("./conf", "fea_mgr_multi_line_test.conf", _view_des));
    ASSERT_EQ(3U, _obj._exts[0].size());
    uint32_t exp_id1[] = {1,2,3};
    for (uint32_t i = 0U; i < _obj._exts[0].size(); ++i) {
        ASSERT_TRUE(_obj._exts[0][i] != NULL);
        EXPECT_EQ(exp_id1[i], _obj._exts[0][i]->feature_id());
    }
    _obj.uninit();
}

TEST_F(FeatureExtractorManagerTestSuite, _init_ext_by_invalid_input_case) {
    comcfg::Configure conf;
    std::vector<ViewDescriptor> tmp;
    ASSERT_TRUE(conf.load("./conf", "feature_extractor_manger_invalid1.conf") == 0);
    EXPECT_FALSE(_obj._init_exts(conf, tmp, &_obj._care_exps));
    _obj.uninit();

    uint32_t num =  conf["invalid"].size();
    ASSERT_LE(4U, num);
    for (uint32_t i = 0; i < num; ++i) {
        EXPECT_FALSE(_obj._init_exts(conf["invalid"][i], _view_des, &_obj._care_exps));
        _obj.uninit();
    }
}

TEST_F(FeatureExtractorManagerTestSuite, get_or_reset_pv_coords_by_null_case) {
    EXPECT_FALSE(_obj.get_pv_coords(NULL));

    std::vector<std::pair<uint64_t, int64_t> > coords;
    _obj._exts[0].push_back(NULL);
    EXPECT_FALSE(_obj.get_pv_coords(&coords));
    
    EXPECT_FALSE(_obj.reset_pv_coords());
}

class MockLogRecordInterface : public LogRecordInterface {
public:
    virtual ~MockLogRecordInterface() {}
    MOCK_METHOD1(next, bool(int32_t*));
    MOCK_METHOD2(init, bool(const std::string&, const std::string&));
    MOCK_METHOD0(uninit, void());
    MOCK_CONST_METHOD1(get_view_descriptor, const ViewDescriptor*(const std::string&));
    MOCK_CONST_METHOD0(view_descriptors, const std::vector<ViewDescriptor>*());

    MOCK_METHOD2(parse, bool(const char*, uint64_t));
    MOCK_METHOD0(clear, void());
    MOCK_METHOD1(reset_log_level, bool(uint32_t));
    MOCK_CONST_METHOD0(has_next, bool());
    MOCK_METHOD0(next, bool());

    MOCK_CONST_METHOD0(joinkey, uint64_t());
    MOCK_CONST_METHOD0(log_id, uint64_t());
    MOCK_CONST_METHOD0(log_time, uint64_t());
    MOCK_CONST_METHOD0(is_spam, bool());

    MOCK_METHOD2(get_view_value, bool(const ViewDescriptor&, ViewValueProto*));
    MOCK_METHOD2(get_view_sign, bool(const std::vector<ViewDescriptor>&, ViewValueProto*));
};

// TEST_F(FeatureExtractorManagerTestSuite, extract_by_invalid_input_case) {
//     EXPECT_FALSE(_obj.extract(NULL, NULL));
// 
//     MockLogRecordInterface log;
//     EXPECT_FALSE(_obj.extract(&log, NULL));
// 
//     EXPECT_CALL(log, log_id()).WillRepeatedly(Return(119U));
//     EXPECT_CALL(log, reset_log_level(_))
//         .WillOnce(Return(false))
//         .WillRepeatedly(Return(true));
//     EXPECT_CALL(log, has_next()).WillOnce(Return(true));
// 
//     // reset log level for global care fail
//     _obj._globle_view_level = 1;
//     std::vector<std::shared_ptr<FeatureValueProto> > feas;
//     EXPECT_FALSE(_obj.extract(&log, &feas));
// }

// TEST_F(FeatureExtractorManagerTestSuite, extract_by_not_in_global_care_case) {
//     MockLogRecordInterface log;
//     std::vector<std::shared_ptr<FeatureValueProto> > feas;
//     
//     EXPECT_CALL(log, has_next())
//         .WillOnce(Return(true))
//         .WillOnce(Return(false))
//         .WillOnce(Return(true))
//         .WillOnce(Return(false));
//     EXPECT_CALL(log, next())
//         .WillOnce(Return(true))
//         .WillOnce(Return(true));
//     ViewValueProto value;
//     value.set_type(ViewValueProto::NOT_EXIST);
//     value.add_values()->set_str_value("_");
//     EXPECT_CALL(log, get_view_value(_, _))
//             .WillRepeatedly(DoAll(SetArgPointee<1>(value), Return(true)));
//     ASSERT_TRUE(_obj._global_care.init("['price'HA]", _view_des));
//     ASSERT_TRUE(_obj._care_exp.append("['price'HA]", _view_des));
//     ASSERT_TRUE(_obj.extract(&log, &feas));
//     EXPECT_EQ(0U, feas.size());
//     _obj._global_care.uninit();
// 
//     EXPECT_CALL(log, has_next())
//         .WillOnce(Return(true))
//         .WillOnce(Return(false))
//         .WillOnce(Return(true))
//         .WillOnce(Return(false));
//     EXPECT_CALL(log, next())
//         .WillOnce(Return(true))
//         .WillOnce(Return(true));
//     EXPECT_CALL(log, reset_log_level(_)).WillRepeatedly(Return(true));
//     _obj._globle_view_level = 3U;
//     ViewValueProto asp_value;
//     asp_value.set_type(ViewValueProto::NOT_EXIST);
//     asp_value.add_values()->set_str_value("_");
//     EXPECT_CALL(log, get_view_value(_, _))
//             .WillRepeatedly(DoAll(SetArgPointee<1>(asp_value), Return(true)));
//     ASSERT_TRUE(_obj._global_care.init("['baiduid'HA]", _asp_view_des));
//     ASSERT_TRUE(_obj._care_exp.append("['baiduid'HA]", _asp_view_des));
//     ASSERT_TRUE(_obj.extract(&log, &feas));
//     EXPECT_EQ(0U, feas.size());
//     _obj._global_care.uninit();
// 
//     EXPECT_CALL(log, has_next())
//         .WillOnce(Return(true))
//         .WillOnce(Return(false))
//         .WillOnce(Return(true))
//         .WillOnce(Return(false));
//     EXPECT_CALL(log, next())
//         .WillOnce(Return(true))
//         .WillOnce(Return(true));
//     EXPECT_CALL(log, reset_log_level(_)).WillRepeatedly(Return(true));
//     _obj._globle_view_level = 3U;
//     asp_value.clear_type();
//     asp_value.clear_values();
//     asp_value.set_type(ViewValueProto::NOT_EXIST);
//     asp_value.add_values()->set_str_value("_");
//     EXPECT_CALL(log, get_view_value(_, _))
//             .WillRepeatedly(DoAll(SetArgPointee<1>(asp_value), Return(true)));
//     ASSERT_TRUE(_obj._global_care.init("['src_id'HA]", _asp_view_des));
//     ASSERT_TRUE(_obj._care_exp.append("['src_id'HA]", _asp_view_des));
//     ASSERT_TRUE(_obj.extract(&log, &feas));
//     EXPECT_EQ(0U, feas.size());
//     _obj._global_care.uninit();
// }

// TEST_F(FeatureExtractorManagerTestSuite, extract_by_in_global_care_case) {
//     ASSERT_TRUE(_obj.init("./conf", "feature_extractor_manger_test.conf", _view_des));
//     _obj._exts[0].push_back(NULL);
//     _obj._exts[1].push_back(NULL);
// 
//     MockLogRecordInterface log;
//     std::vector<std::shared_ptr<FeatureValueProto> > feas;
//     EXPECT_CALL(log, reset_log_level(_))
//         .WillOnce(Return(true))
//         .WillOnce(Return(true))
//         .WillOnce(Return(true))
//         .WillRepeatedly(Return(false));
//     EXPECT_CALL(log, has_next())
//         .WillOnce(Return(true))
//         .WillOnce(Return(true))
//         .WillRepeatedly(Return(false));
//     EXPECT_CALL(log, next())
//         .WillOnce(Return(true))
//         .WillRepeatedly(Return(false));
// 
//     ViewValueProto value[2];
//     value[0].set_type(ViewValueProto::NOT_EXIST);
//     value[0].add_values()->set_str_value("_");
//     EXPECT_CALL(log, get_view_value(_, _))
//             .WillOnce(DoAll(SetArgPointee<1>(value[0]), Return(true)));
//     value[1].set_type(ViewValueProto::STRING);
//     value[1].add_values()->set_str_value("sign");
//     value[1].add_signs(123456LU);
//     EXPECT_CALL(log, get_view_sign(_, _))
//             .WillRepeatedly(DoAll(SetArgPointee<1>(value[1]), Return(true)));
//     EXPECT_CALL(log, log_id()).WillRepeatedly(Return(119LU));
//     EXPECT_CALL(log, log_time()).WillRepeatedly(Return(110LU));
//     EXPECT_CALL(log, joinkey()).WillRepeatedly(Return(120LU));
// 
//     ASSERT_TRUE(_obj.extract(&log, &feas));
//     ASSERT_EQ(2U, feas.size());
//     FeatureValueProto::FeatureType exp_ftype[] = {
//             FeatureValueProto::SEGMENT,
//             FeatureValueProto::RATIO
//     };
//     for (uint64_t i = 0; i < 2U; ++i) {
//         EXPECT_EQ(i + 1LU, feas[i]->feature_id());
//         EXPECT_EQ(123456LU, feas[i]->view_sign());
//         EXPECT_EQ(exp_ftype[i], feas[i]->feature_type());
//         EXPECT_EQ(120LU, feas[i]->joinkey());
//         EXPECT_EQ(119LU, feas[i]->log_id());
//         EXPECT_EQ(110LU, feas[i]->log_time());
//         EXPECT_STREQ("sign", feas[i]->view().data());
//         EXPECT_EQ(FeatureValueProto::PV, feas[i]->seg_type());
//         EXPECT_EQ(0UL, feas[i]->coord());
//     }
//     EXPECT_TRUE(feas[1]->in_filter());
//     EXPECT_TRUE(feas[1]->in_refer());
// 
//     _obj._exts[0].pop_back();
//     _obj._exts[1].pop_back();
//     std::vector<std::pair<uint64_t, int64_t> > coords;
//     ASSERT_TRUE(_obj.get_pv_coords(&coords));
//     ASSERT_EQ(3U, coords.size());
//     int64_t exp_coords[] = {1, 1, 0};
//     for (uint64_t i = 0U; i < 3U; ++i) {
//         EXPECT_EQ(i + 1LU, coords[i].first);
//         EXPECT_EQ(exp_coords[i], coords[i].second);
//     }
//     coords.clear();
// 
//     ASSERT_TRUE(_obj.reset_pv_coords());
//     ASSERT_TRUE(_obj.get_pv_coords(&coords));
//     ASSERT_EQ(3U, coords.size());
//     for (uint64_t i = 0U; i < 3U; ++i) {
//         EXPECT_EQ(i + 1LU, coords[i].first);
//         EXPECT_EQ(0LU, coords[i].second);
//     }
//     _obj.uninit();
// }

TEST_F(FeatureExtractorManagerTestSuite, extract_asp_by_in_global_care_case) {
    ASSERT_TRUE(_obj.init("./conf", "feature_extractor_manger_test1.conf", _asp_view_des));
    EXPECT_EQ(3U, _obj._globle_view_level);

    MockLogRecordInterface log;
    std::vector<std::shared_ptr<FeatureValueProto> > feas;
    EXPECT_CALL(log, reset_log_level(_)).WillRepeatedly(Return(true));
    EXPECT_CALL(log, has_next())
        .WillOnce(Return(true))
        .WillOnce(Return(true))
        .WillRepeatedly(Return(false));
    EXPECT_CALL(log, next()).WillRepeatedly(Return(true));

    ViewValueProto value;
    value.set_type(ViewValueProto::STRING);
    value.add_values()->set_str_value("sign");
    value.add_signs(123456LU);
    EXPECT_CALL(log, get_view_value(_, _))
            .WillRepeatedly(DoAll(SetArgPointee<1>(value), Return(true)));
    EXPECT_CALL(log, get_view_sign(_, _))
            .WillRepeatedly(DoAll(SetArgPointee<1>(value), Return(true)));
    EXPECT_CALL(log, log_id()).WillRepeatedly(Return(119LU));
    EXPECT_CALL(log, log_time()).WillRepeatedly(Return(110LU));
    EXPECT_CALL(log, joinkey()).WillRepeatedly(Return(120LU));

    ASSERT_TRUE(_obj.extract(&log, &feas));
    ASSERT_EQ(1U, feas.size());

    EXPECT_EQ(1LU, feas[0]->feature_id());
    EXPECT_EQ(123456LU, feas[0]->view_sign());
    EXPECT_EQ(FeatureValueProto::RATIO, feas[0]->feature_type());
    EXPECT_EQ(120LU, feas[0]->joinkey());
    EXPECT_EQ(119LU, feas[0]->log_id());
    EXPECT_EQ(110LU, feas[0]->log_time());
    EXPECT_STREQ("sign", feas[0]->view().data());
    EXPECT_EQ(FeatureValueProto::PV, feas[0]->seg_type());
    EXPECT_EQ(0UL, feas[0]->coord());
    EXPECT_TRUE(feas[0]->in_filter());
    EXPECT_TRUE(feas[0]->in_refer());

    std::vector<std::pair<uint64_t, int64_t> > coords;
    ASSERT_TRUE(_obj.get_pv_coords(&coords));
    ASSERT_EQ(1U, coords.size());
    EXPECT_EQ(1LU, coords[0].first);
    EXPECT_EQ(1LU, coords[0].second);
    coords.clear();

    ASSERT_TRUE(_obj.reset_pv_coords());
    ASSERT_TRUE(_obj.get_pv_coords(&coords));
    ASSERT_EQ(1U, coords.size());
    EXPECT_EQ(1LU, coords[0].first);
    EXPECT_EQ(0LU, coords[0].second);
    _obj.uninit();
}

TEST_F(FeatureExtractorManagerTestSuite, test_ad_level_care) {
    AspDisplayLog log;
    log.set_product(anti::themis::ECOM);
    log.set_timestamp(888);
    log.set_logid(10000000UL);
    log.set_query("xxx");
    for (uint32_t i = 0; i < 3; ++i) {
        anti::themis::AspLogField* asp_log_field = log.mutable_asp_log_field();
        auto src_list = asp_log_field->add_src_field_list();
        src_list->set_src_id(i);
        for (uint32_t j = 0; j < 2; ++j) {
            auto field_list = src_list->add_ad_field_list();
            field_list->set_winfoid(i * 10 + j);
        }
    }
    CWARNING_LOG("asp str[%s]", log.ShortDebugString().c_str());
    std::string str;
    log.SerializeToString(&str);
    auto log_ptr = anti::themis::log_parser_lib::LogRecordFactory::create(
            RecordType::ASP_DISPLAY_LOG);
    EXPECT_TRUE(log_ptr->init("./conf/", "record.conf"));
    EXPECT_TRUE(log_ptr->parse(str.c_str(), str.size()));
    bool res = false;
    auto asp_view_des = log_ptr->view_descriptors();
    ASSERT_TRUE(_obj.init("./conf", "feature_extractor_manger_asp.conf", *asp_view_des));

    EXPECT_EQ(2U, _obj._globle_view_level);
    EXPECT_TRUE(_obj._care_global(log_ptr, &res));
    std::vector<std::shared_ptr<FeatureValueProto>> feas;
    EXPECT_TRUE(_obj.extract(log_ptr, &feas));
    for (auto fea : feas) {
        CWARNING_LOG("fea[%s]", fea->ShortDebugString().c_str());
    }
    EXPECT_EQ(3, feas.size());
    EXPECT_EQ("xxx", feas[0]->view());
    EXPECT_EQ("20", feas[1]->view());
    EXPECT_EQ("21", feas[2]->view());
}

TEST_F(FeatureExtractorManagerTestSuite, test_src_level_globle_care) {
    AspDisplayLog log;
    log.set_product(anti::themis::ECOM);
    log.set_timestamp(888);
    log.set_logid(10000000UL);
    log.set_query("xxx");
    for (uint32_t i = 0; i < 3; ++i) {
        anti::themis::AspLogField* asp_log_field = log.mutable_asp_log_field();
        auto src_list = asp_log_field->add_src_field_list();
        src_list->set_src_id(i);
    }
    CWARNING_LOG("asp str[%s]", log.ShortDebugString().c_str());
    std::string str;
    log.SerializeToString(&str);
    auto log_ptr = anti::themis::log_parser_lib::LogRecordFactory::create(
            RecordType::ASP_DISPLAY_LOG);
    EXPECT_TRUE(log_ptr->init("./conf/", "record.conf"));
    EXPECT_TRUE(log_ptr->parse(str.c_str(), str.size()));
    bool res = false;
    auto asp_view_des = log_ptr->view_descriptors();
    ASSERT_TRUE(_obj.init("./conf", "feature_extractor_manger_asp.conf", *asp_view_des));

    EXPECT_EQ(2U, _obj._globle_view_level);
    EXPECT_TRUE(_obj._care_global(log_ptr, &res));
    ASSERT_TRUE(res);
    std::vector<std::shared_ptr<FeatureValueProto>> feas;
    EXPECT_TRUE(_obj.extract(log_ptr, &feas));
    for (auto fea : feas) {
        CWARNING_LOG("fea[%s]", fea->ShortDebugString().c_str());
    }
    EXPECT_EQ(1, feas.size());
    EXPECT_EQ("xxx", feas[0]->view());
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

