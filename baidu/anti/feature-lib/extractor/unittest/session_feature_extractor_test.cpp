#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <view_value.pb.h>
#include <file_dict_manager.h>
#include "session_feature_extractor.h"
#include "com_log.h"
#include "mock_log_record_interface.h"

using anti::themis::log_interface::ViewDescriptor;
using anti::themis::log_interface::LogRecordInterface;
using anti::themis::log_interface::ViewValueProto;
using anti::themis::common_lib::FileDictManagerSingleton;
using ::testing::_;
using ::testing::Return;
using ::testing::SetArgPointee;
using ::testing::DoAll;

namespace anti {
namespace themis {
namespace feature_lib {

class SessionFeatureExtractorTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
        ASSERT_EQ(_conf.load("conf", "session_feature_extractor_test.conf"), 0);
    }
    virtual void TearDown() {}

private:
    comcfg::Configure _conf;
    SessionFeatureExtractor _obj;
    MockLogRecordInterface _log;
    std::vector<ViewDescriptor> _views = {{"AspLog", 0, 1, "view1"}};
    anti::themis::log_parser_lib::CareExp _care_exp;
};

TEST_F(SessionFeatureExtractorTestSuite, test_init_fail) {
    EXPECT_CALL(_log, view_descriptors())
            .WillRepeatedly(Return(&_views));
    const comcfg::ConfigUnit& conf = _conf["INVALID"];
    for (uint32_t i = 0; i < conf.size(); ++i) {
        CDEBUG_LOG("test invalid %u", i);
        ASSERT_FALSE(_obj._init(conf[i], *(_log.view_descriptors()), NULL));
    }
}

TEST_F(SessionFeatureExtractorTestSuite, test_init_succ) {
    // ViewDescriptor view1{"AspLog", 0, 1, "view1"};
    EXPECT_CALL(_log, view_descriptors())
            .WillRepeatedly(Return(&_views));
    const comcfg::ConfigUnit& conf = _conf["VALID"];
    for (uint32_t i = 0; i < conf.size(); ++i) {
        CDEBUG_LOG("test valid %u", i);
        ASSERT_TRUE(_obj._init(conf[i], *(_log.view_descriptors()), NULL));
    }
}

TEST_F(SessionFeatureExtractorTestSuite, test_get_info_fail_with_null_tr) {
    SessionFeatureExtractor::FeatureInfo fea_info;
    std::vector<std::string> values;
    ASSERT_FALSE(_obj._get_info_value(NULL, fea_info, &values));
    ASSERT_FALSE(_obj._get_info_value(&_log, fea_info, NULL));
}

TEST_F(SessionFeatureExtractorTestSuite, test_get_info_fail_with_invalid_type) {
    SessionFeatureExtractor::FeatureInfo fea_info;
    fea_info.type = static_cast<SessionFeatureExtractor::FeatureInfoType>(2);
    std::vector<std::string> values;
    ASSERT_FALSE(_obj._get_info_value(&_log, fea_info, &values));
}

TEST_F(SessionFeatureExtractorTestSuite, test_get_info_succ_manual) {
    SessionFeatureExtractor::FeatureInfo fea_info(SessionFeatureExtractor::MANUAL, "baidu");
    std::vector<std::string> values;
    ASSERT_TRUE(_obj._get_info_value(&_log, fea_info, &values));
    ASSERT_EQ(values.size(), 1);
    ASSERT_EQ(values[0], "baidu");
}

TEST_F(SessionFeatureExtractorTestSuite, test_get_info_fail_view) {
    EXPECT_CALL(_log, get_view_value(_, _))
            .WillOnce(Return(false));

    SessionFeatureExtractor::FeatureInfo fea_info(SessionFeatureExtractor::VIEW, "baidu");
    std::vector<std::string> values;
    ASSERT_FALSE(_obj._get_info_value(&_log, fea_info, &values));
    ASSERT_EQ(values.size(), 0);
}

TEST_F(SessionFeatureExtractorTestSuite, test_get_info_succ_view) {
    ViewValueProto value;
    value.add_values()->set_str_value("xxx");
    value.add_values()->set_str_value("yyy");
    EXPECT_CALL(_log, get_view_value(_, _))
        .WillOnce(DoAll(SetArgPointee<1>(value), Return(true)));

    SessionFeatureExtractor::FeatureInfo fea_info(SessionFeatureExtractor::VIEW, "baidu");
    std::vector<std::string> values;
    ASSERT_TRUE(_obj._get_info_value(&_log, fea_info, &values));
    ASSERT_EQ(values.size(), 2);
    ASSERT_EQ(values[0], "xxx");
    ASSERT_EQ(values[1], "yyy");
}

TEST_F(SessionFeatureExtractorTestSuite, test_extract_fail_with_null_ptr) {
    std::vector<std::shared_ptr<FeatureValueProto>>  feas;
    ASSERT_FALSE(_obj._extract(_care_exp, NULL, &feas));
    ASSERT_FALSE(_obj._extract(_care_exp, &_log, NULL));
}

TEST_F(SessionFeatureExtractorTestSuite, test_extract_fail_with_get_view_sign_fail) {
    EXPECT_CALL(_log, get_view_sign(_, _))
        .WillOnce(Return(false));
    std::vector<std::shared_ptr<FeatureValueProto>>  feas;
    ASSERT_FALSE(_obj._extract(_care_exp, &_log, &feas));
}

TEST_F(SessionFeatureExtractorTestSuite, test_extract_fail_with_get_info_value) {
    ViewValueProto value;
    value.add_signs(123);
    value.add_values()->set_str_value("123");
    EXPECT_CALL(_log, get_view_sign(_, _))
        .WillOnce(DoAll(SetArgPointee<1>(value), Return(true)));
    SessionFeatureExtractor::FeatureInfo fea_info;
    fea_info.type = static_cast<SessionFeatureExtractor::FeatureInfoType>(2);
    _obj._fea_infos.push_back(fea_info);
    std::vector<std::shared_ptr<FeatureValueProto>>  feas;
    ASSERT_FALSE(_obj._extract(_care_exp, &_log, &feas));
}

TEST_F(SessionFeatureExtractorTestSuite, test_extract_fail_with_set_field_fail) {
    ViewValueProto value;
    value.add_signs(123);
    value.add_values()->set_str_value("123");
    EXPECT_CALL(_log, get_view_sign(_, _))
        .WillOnce(DoAll(SetArgPointee<1>(value), Return(true)));
    SessionFeatureExtractor::FeatureInfo fea_info(SessionFeatureExtractor::MANUAL, "baidu");
    fea_info.dst = "xxx";
    _obj._fea_infos.push_back(fea_info);
    std::vector<std::shared_ptr<FeatureValueProto>>  feas;
    ASSERT_FALSE(_obj._extract(_care_exp, &_log, &feas));
}

TEST_F(SessionFeatureExtractorTestSuite, test_extract_succ) {
    ViewValueProto value;
    value.add_signs(123);
    value.add_values()->set_str_value("123");
    EXPECT_CALL(_log, get_view_sign(_, _))
        .WillOnce(DoAll(SetArgPointee<1>(value), Return(true)));
    SessionFeatureExtractor::FeatureInfo fea_info(SessionFeatureExtractor::MANUAL, "baidu");
    fea_info.dst = "org_cnt";
    _obj._fea_infos.push_back(fea_info);
    fea_info.str = "123";
    fea_info.dst = "dt.query_sign";
    _obj._fea_infos.push_back(fea_info);

    std::vector<std::shared_ptr<FeatureValueProto>>  feas;
    ASSERT_TRUE(_obj._extract(_care_exp, &_log, &feas));
    ASSERT_EQ(feas.size(), 1);
    ASSERT_EQ(feas[0]->view_sign(), 123);
    ASSERT_EQ(feas[0]->session().keys_size(), 1);
    ASSERT_EQ(feas[0]->session().keys(0).view(), 123);
    ASSERT_EQ(feas[0]->session().base_action().log_type(), anti::themis::BASE_LOG);
    ASSERT_EQ(feas[0]->session().base_action().log_type(), anti::themis::BASE_LOG);
    ASSERT_EQ(feas[0]->session().base_action().dt().query_sign(), 123);
    ASSERT_EQ(feas[0]->session().base_action().org_cnt(), "baidu");

    std::string fea_str;
    FeatureValueProto fea;
    ASSERT_TRUE(feas[0]->SerializeToString(&fea_str));
    ASSERT_TRUE(fea.ParseFromString(fea_str));
}

}
}
}
