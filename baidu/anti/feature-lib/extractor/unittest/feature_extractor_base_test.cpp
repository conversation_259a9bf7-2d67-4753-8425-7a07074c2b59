// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: feature_extractor_base_test.cpp
// @Last modified: 2018-03-29 12:12:34
// @Brief: 

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <Configure.h>
#include "feature_extractor_base.h"

using anti::themis::log_interface::ViewDescriptor;
using anti::themis::log_interface::LogRecordInterface;
using anti::themis::log_interface::ViewValueProto;
using ::testing::_;
using ::testing::Return;
using ::testing::DoAll;
using ::testing::SetArgPointee;

namespace anti {
namespace themis {
namespace feature_lib {

class MockFeatureExtractorBase : public FeatureExtractorBase {
public:
    MOCK_METHOD3(_init, bool(
            const comcfg::ConfigUnit&, 
            const std::vector<ViewDescriptor>&,
            CareExp*));
    MOCK_METHOD3(_extract, bool(
            const CareExp&,
            LogRecordInterface*, 
            std::vector<std::shared_ptr<FeatureValueProto> >*));

private:
    virtual void _uninit() {}
};

class FeatureExtractorBaseTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
        _view_des.push_back({"ClickBaiduLog", 0U, 1U, "userid"});
        _view_des.push_back({"ClickBaiduLog", 1U, 1U, "price"});
    }
    virtual void TearDown() {}

private:
    MockFeatureExtractorBase _obj;
    std::vector<ViewDescriptor> _view_des;
    anti::themis::log_parser_lib::CareExp _care_exp;
};

TEST_F(FeatureExtractorBaseTestSuite, construction_case) {
    EXPECT_EQ(0UL, _obj.feature_id());
    EXPECT_EQ(-1L, _obj.pv_coord());
    EXPECT_EQ(0UL, _obj._last_joinkey);
}

TEST_F(FeatureExtractorBaseTestSuite, init_case) {
    comcfg::Configure conf;
    ASSERT_TRUE(conf.load("./conf", "feature_extractor_test.conf") == 0);
    uint32_t conf_size = conf["feature"].size();
    EXPECT_CALL(_obj, _init(_, _, _))
            .WillOnce(Return(false))
            .WillOnce(Return(true));
    ASSERT_LE(4U, conf_size);
    for (uint32_t i = 0U; i < 4U; ++i) {
        ASSERT_FALSE(_obj.init(conf["feature"][i], _view_des, &_care_exp));
        _obj.uninit();
    }
    ASSERT_TRUE(_obj.init(conf["feature"][3], _view_des, &_care_exp));
    EXPECT_EQ(3U, _obj.feature_id());
    ASSERT_EQ(2U, _obj._views.size());
    for (uint32_t i = 0U; i < 2U; ++i) {
        EXPECT_STREQ("ClickBaiduLog", _obj._views[i].log_type.data());
        EXPECT_EQ(0U, _obj._views[i].view_id);
        EXPECT_EQ(1U, _obj._views[i].view_level);
        EXPECT_STREQ("userid", _obj._views[i].view_name.data());
    }
    _obj.uninit();
    EXPECT_EQ(0U, _obj._views.size());
}

class MockLogRecordInterface : public LogRecordInterface {
public:
    virtual ~MockLogRecordInterface() {}
    MOCK_METHOD1(next, bool(int32_t*));
    MOCK_METHOD2(init, bool(const std::string&, const std::string&));
    MOCK_METHOD0(uninit, void());
    MOCK_CONST_METHOD1(get_view_descriptor, const ViewDescriptor*(const std::string&));
    MOCK_CONST_METHOD0(view_descriptors, const std::vector<ViewDescriptor>*());

    MOCK_METHOD2(parse, bool(const char*, uint64_t));
    MOCK_METHOD0(clear, void());
    MOCK_METHOD1(reset_log_level, bool(uint32_t));
    MOCK_CONST_METHOD0(has_next, bool());
    MOCK_METHOD0(next, bool());

    MOCK_CONST_METHOD0(joinkey, uint64_t());
    MOCK_CONST_METHOD0(log_id, uint64_t());
    MOCK_CONST_METHOD0(log_time, uint64_t());
    MOCK_CONST_METHOD0(is_spam, bool());

    MOCK_METHOD2(get_view_value, bool(const ViewDescriptor&, ViewValueProto*));
    MOCK_METHOD2(get_view_sign, bool(const std::vector<ViewDescriptor>&, ViewValueProto*));
};

TEST_F(FeatureExtractorBaseTestSuite, extract_by_valid_input_case) {
    comcfg::Configure conf;
    ASSERT_TRUE(conf.load("./conf", "feature_extractor_test.conf") == 0);
    uint32_t conf_size = conf["feature"].size();
    ASSERT_LE(5U, conf_size);
    EXPECT_CALL(_obj, _init(_, _, _)).WillRepeatedly(Return(true));

    MockLogRecordInterface log;
    std::vector<std::shared_ptr<FeatureValueProto>> feas;

    // not in care
    ASSERT_TRUE(_obj.init(conf["feature"][4], _view_des, &_care_exp));
    ViewValueProto value;
    value.add_values()->set_str_value("_");
    value.set_type(ViewValueProto::NOT_EXIST);
    EXPECT_CALL(log, get_view_value(_, _))
            .WillOnce(DoAll(SetArgPointee<1>(value), Return(true)));
    ASSERT_TRUE(_care_exp.reset(&log));
    ASSERT_TRUE(_obj.extract(_care_exp, &log, &feas));
    EXPECT_EQ(0LU, _obj._last_joinkey);
    EXPECT_EQ(-1L, _obj.pv_coord());
    feas.clear();
    _obj.uninit();
    
    // in care
    EXPECT_CALL(_obj, _extract(_, _, _)).WillRepeatedly(Return(true));
    EXPECT_CALL(log, log_id()).WillRepeatedly(Return(0LU));
    EXPECT_CALL(log, joinkey())
            .WillOnce(Return(123LU))
            .WillOnce(Return(123LU))
            .WillOnce(Return(123LU))
            .WillRepeatedly(Return(456LU));
    EXPECT_CALL(log, get_view_value(_, _))
            .WillRepeatedly(DoAll(SetArgPointee<1>(value), Return(true)));
    ASSERT_TRUE(_obj.init(conf["feature"][3], _view_des, &_care_exp));
    ASSERT_TRUE(_care_exp.reset(&log));
    ASSERT_TRUE(_obj.extract(_care_exp, &log, &feas));
    EXPECT_EQ(123LU, _obj._last_joinkey);
    EXPECT_EQ(0LU, _obj.pv_coord());
    ASSERT_TRUE(_obj.extract(_care_exp, &log, &feas));
    EXPECT_EQ(123LU, _obj._last_joinkey);
    EXPECT_EQ(0LU, _obj.pv_coord());
    ASSERT_TRUE(_obj.extract(_care_exp, &log, &feas));
    EXPECT_EQ(456LU, _obj._last_joinkey);
    EXPECT_EQ(1LU, _obj.pv_coord());
    ASSERT_TRUE(_obj.extract(_care_exp, &log, &feas));
    EXPECT_EQ(456LU, _obj._last_joinkey);
    EXPECT_EQ(1LU, _obj.pv_coord());
    _obj.uninit();
    EXPECT_EQ(0LU, _obj._last_joinkey);
    EXPECT_EQ(-1L, _obj.pv_coord());
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

