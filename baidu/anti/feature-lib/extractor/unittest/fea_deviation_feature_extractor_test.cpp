// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// <AUTHOR>
// @brief

#include <com_log.h>
#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "fea_deviation_feature_extractor.h"

using anti::themis::log_interface::ViewDescriptor;
using anti::themis::log_interface::ViewValueProto;
using anti::themis::log_interface::LogRecordInterface;
using ::testing::_;
using ::testing::Return;
using ::testing::DoAll;
using ::testing::SetArgPointee;

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}

namespace anti {
namespace themis {
namespace feature_lib {

const std::string conf_path = "./conf";
const std::string conf_file = "fea_deviation.conf";

class TestFeaDeviationFeatureExtractorSuite : public ::testing::Test {
public:
    TestFeaDeviationFeatureExtractorSuite() {}
    ~TestFeaDeviationFeatureExtractorSuite() {}

    virtual void SetUp() {
        ASSERT_TRUE(_conf.load(conf_path.c_str(), conf_file.c_str()) == 0);
        _view_des.push_back({"TxtLog", 0U, 1U, "logtime"});
        _view_des.push_back({"TxtLog", 1U, 1U, "view_name"});
        _view_des.push_back({"TxtLog", 2U, 1U, "view"});
        _view_des.push_back({"TxtLog", 3U, 1U, "new_cookie_rate"});
        _view_des.push_back({"TxtLog", 3U, 1U, "click_num"});
    }
    virtual void TearDown() {}

private:
    comcfg::Configure _conf;
    std::vector<ViewDescriptor> _view_des;
    anti::themis::log_parser_lib::CareExp _care_exp;
};

class MockLogRecordInterface : public LogRecordInterface {
public:
    virtual ~MockLogRecordInterface() {}
    MOCK_METHOD1(next, bool(int32_t*));
    MOCK_METHOD2(init, bool(const std::string&, const std::string&));
    MOCK_METHOD0(uninit, void());
    MOCK_CONST_METHOD1(get_view_descriptor, const ViewDescriptor*(const std::string&));
    MOCK_CONST_METHOD0(view_descriptors, const std::vector<ViewDescriptor>*());
    MOCK_METHOD2(parse, bool(const char*, uint64_t));
    MOCK_METHOD0(clear, void());
    MOCK_METHOD1(reset_log_level, bool(uint32_t));
    MOCK_CONST_METHOD0(has_next, bool());
    MOCK_METHOD0(next, bool());
    MOCK_CONST_METHOD0(joinkey, uint64_t());
    MOCK_CONST_METHOD0(log_id, uint64_t());
    MOCK_CONST_METHOD0(log_time, uint64_t());
    MOCK_CONST_METHOD0(is_spam, bool());
    MOCK_METHOD2(get_view_value, bool(const ViewDescriptor&, ViewValueProto*));
    MOCK_METHOD2(get_view_sign, bool(const std::vector<ViewDescriptor>&, ViewValueProto*));
};

TEST_F(TestFeaDeviationFeatureExtractorSuite, find_view_test) {
    FeaDeviationFeatureExtractor obj;

    ViewDescriptor output_des;
    EXPECT_FALSE(obj._find_view(_view_des, "view_name", NULL));
    EXPECT_FALSE(obj._find_view(_view_des, "", &output_des));
    EXPECT_FALSE(obj._find_view(_view_des, "error", &output_des));
    EXPECT_TRUE(obj._find_view(_view_des, "view_name", &output_des));
    EXPECT_STREQ("view_name", output_des.view_name.c_str());
}

TEST_F(TestFeaDeviationFeatureExtractorSuite, init_acc_test) {
    FeaDeviationFeatureExtractor obj;
    // error view name
    EXPECT_FALSE(obj._init_acc(_conf["feature"][1], _view_des));
    // no accumulate
    EXPECT_TRUE(obj._init_acc(_conf["feature"][2], _view_des));
    EXPECT_FALSE(obj._open_accumulate_des);
    // ok case
    EXPECT_TRUE(obj._init_acc(_conf["feature"][0], _view_des));
    EXPECT_TRUE(obj._open_accumulate_des);
    EXPECT_STREQ("click_num", obj._accumulate_des.view_name.c_str());
}

TEST_F(TestFeaDeviationFeatureExtractorSuite, init_des_test) {
    FeaDeviationFeatureExtractor obj;
    // error type
    EXPECT_FALSE(obj._init_dis(_conf["feature"][4]));
    // no discretization
    EXPECT_TRUE(obj._init_dis(_conf["feature"][2]));
    EXPECT_EQ(10, obj._discretization->discretize(10.123));
    // empty type
    EXPECT_TRUE(obj._init_dis(_conf["feature"][3]));
    EXPECT_EQ(101, obj._discretization->discretize(10.123));
    // no divide num
    EXPECT_TRUE(obj._init_dis(_conf["feature"][5]));
    EXPECT_EQ(10, obj._discretization->discretize(10.123));
    // ok case
    EXPECT_TRUE(obj._init_dis(_conf["feature"][0]));
    EXPECT_EQ(1012, obj._discretization->discretize(10.123));
}

TEST_F(TestFeaDeviationFeatureExtractorSuite, init_test) {
    FeaDeviationFeatureExtractor obj;
    obj._views.push_back({"TxtLog", 0U, 1U, "view"});
    // no view name
    EXPECT_FALSE(obj._init(_conf["feature"][6], _view_des));
    // error view name
    EXPECT_FALSE(obj._init(_conf["feature"][7], _view_des));
    // no value
    EXPECT_FALSE(obj._init(_conf["feature"][8], _view_des));
    // value error
    EXPECT_FALSE(obj._init(_conf["feature"][9], _view_des));
    // init acc error
    EXPECT_FALSE(obj._init(_conf["feature"][1], _view_des));
    // init discretization error
    EXPECT_FALSE(obj._init(_conf["feature"][4], _view_des));
    // view size not 1
    obj._views.clear();
    EXPECT_FALSE(obj._init(_conf["feature"][0], _view_des));
    // ok case
    obj._views.push_back({"TxtLog", 0U, 1U, "view"});
    EXPECT_TRUE(obj._init(_conf["feature"][0], _view_des));
}

TEST_F(TestFeaDeviationFeatureExtractorSuite, get_str_view_test) {
    FeaDeviationFeatureExtractor obj;
    MockLogRecordInterface log;
    ViewDescriptor des;
    std::string result;
    ASSERT_TRUE(obj.init(_conf["feature"][0], _view_des, &_care_exp));
    // illegal params
    EXPECT_FALSE(obj._get_str_view(NULL, des, &result));
    EXPECT_FALSE(obj._get_str_view(&log, des, NULL));
    // set return
    ViewValueProto view_value;
    view_value.set_type(ViewValueProto::STRING);
    view_value.add_values()->set_str_value("test_str");
    view_value.add_signs(123LU);
    ViewValueProto view_value_error;
    view_value_error.set_type(ViewValueProto::STRING);
    view_value_error.add_values()->set_str_value("test_str");
    view_value_error.add_values()->set_str_value("test_str2");
    view_value_error.add_signs(123LU);
    EXPECT_CALL(log, get_view_value(_, _))
        .WillOnce(Return(false))
        .WillOnce(DoAll(SetArgPointee<1>(view_value_error), Return(true)))
        .WillRepeatedly(DoAll(SetArgPointee<1>(view_value), Return(true)));
    EXPECT_CALL(log, log_id()).WillRepeatedly(Return(190));
    // get_view_value failed!
    EXPECT_FALSE(obj._get_str_view(&log, des, &result));
    // view_value size error
    EXPECT_FALSE(obj._get_str_view(&log, des, &result));
    // ok case
    EXPECT_TRUE(obj._get_str_view(&log, des, &result));
    EXPECT_STREQ("test_str", result.c_str());
}

TEST_F(TestFeaDeviationFeatureExtractorSuite, get_int_value_test) {
    FeaDeviationFeatureExtractor obj;
    ASSERT_TRUE(obj.init(_conf["feature"][0], _view_des, &_care_exp));

    ViewValueProto view_value;
    view_value.set_type(ViewValueProto::STRING);
    view_value.add_values()->set_str_value("456");
    view_value.add_signs(123LU);

    int64_t result;

    // value NULL
    EXPECT_FALSE(obj._get_int_value(view_value, NULL));
    // value size != 1
    view_value.add_values()->set_str_value("123");
    EXPECT_FALSE(obj._get_int_value(view_value, &result));
    // illegal type
    view_value.clear_values();
    view_value.add_values()->set_str_value("123");
    view_value.set_type(ViewValueProto::INVALID);
    EXPECT_FALSE(obj._get_int_value(view_value, &result));
    // str value error
    view_value.clear_values();
    view_value.add_values()->set_str_value("not_num");
    view_value.set_type(ViewValueProto::STRING);
    EXPECT_FALSE(obj._get_int_value(view_value, &result));
    // str case
    view_value.clear_values();
    view_value.add_values()->set_str_value("1372");
    view_value.set_type(ViewValueProto::STRING);
    EXPECT_TRUE(obj._get_int_value(view_value, &result));
    EXPECT_EQ(1372, result);
    // int case
    view_value.clear_values();
    view_value.add_values()->set_int_value(137);
    view_value.set_type(ViewValueProto::INT32);
    EXPECT_TRUE(obj._get_int_value(view_value, &result));
    EXPECT_EQ(137, result);
}

TEST_F(TestFeaDeviationFeatureExtractorSuite, get_int_view_test) {
    FeaDeviationFeatureExtractor obj;
    MockLogRecordInterface log;
    ViewDescriptor des;
    int64_t result;
    ASSERT_TRUE(obj.init(_conf["feature"][0], _view_des, &_care_exp));
    // set return view
    ViewValueProto view_value;
    view_value.set_type(ViewValueProto::STRING);
    view_value.add_values()->set_str_value("456");
    view_value.add_signs(123LU);
    // set error view
    ViewValueProto view_value_error;
    view_value_error.set_type(ViewValueProto::STRING);
    view_value_error.add_values()->set_str_value("test_str");
    view_value_error.add_values()->set_str_value("test_str2");
    view_value_error.add_signs(123LU);

    // illegal params
    EXPECT_FALSE(obj._get_int_view(NULL, des, &result));
    EXPECT_FALSE(obj._get_int_view(&log, des, NULL));
    // set call return
    EXPECT_CALL(log, get_view_value(_, _))
        .WillOnce(Return(false))
        .WillOnce(DoAll(SetArgPointee<1>(view_value_error), Return(true)))
        .WillRepeatedly(DoAll(SetArgPointee<1>(view_value), Return(true)));
    EXPECT_CALL(log, log_id()).WillRepeatedly(Return(190));
    // get_view_value failed!
    EXPECT_FALSE(obj._get_int_view(&log, des, &result));
    // get_int_value failed!
    EXPECT_FALSE(obj._get_int_view(&log, des, &result));
    // ok case
    EXPECT_TRUE(obj._get_int_view(&log, des, &result));
    EXPECT_EQ(456, result);
}

TEST_F(TestFeaDeviationFeatureExtractorSuite, get_discretization_value_test) {
    FeaDeviationFeatureExtractor obj;
    ASSERT_TRUE(obj.init(_conf["feature"][0], _view_des, &_care_exp));
    std::string str = "-1.56#1.1#-0.2";
    std::string error_str = "error#what";
    int64_t result;
    // illegal param
    EXPECT_FALSE(obj._get_discretization_value(str, NULL));
    // error str
    EXPECT_FALSE(obj._get_discretization_value(error_str, &result));
    // discretize not set
    std::shared_ptr<DiscretizationInterface> tmp = obj._discretization;
    obj._discretization.reset();
    EXPECT_FALSE(obj._get_discretization_value(str, &result));
    // ok case
    obj._discretization = tmp;
    EXPECT_TRUE(obj._get_discretization_value(str, &result));
    EXPECT_EQ(-156, result);
    // > max value case
    str = "102.7#1.1#-0.2";
    EXPECT_TRUE(obj._get_discretization_value(str, &result));
    EXPECT_EQ(10000, result);
}

TEST_F(TestFeaDeviationFeatureExtractorSuite, extract_test) {
    FeaDeviationFeatureExtractor obj;
    ASSERT_TRUE(obj.init(_conf["feature"][0], _view_des, &_care_exp));
    MockLogRecordInterface log;
    std::vector<std::shared_ptr<FeatureValueProto> > feas;
    // illegal params
    EXPECT_FALSE(obj._extract(NULL, &feas));
    EXPECT_FALSE(obj._extract(&log, NULL));
    // prepare data
    ViewValueProto view_name_proto;
    view_name_proto.set_type(ViewValueProto::STRING);
    view_name_proto.add_values()->set_str_value("cntname");
    view_name_proto.add_signs(123LU);
    ViewValueProto value_proto;
    value_proto.set_type(ViewValueProto::STRING);
    value_proto.add_values()->set_str_value("0.53#1.0#1.3");
    value_proto.add_signs(123LU);
    ViewValueProto acc_proto;
    acc_proto.set_type(ViewValueProto::STRING);
    acc_proto.add_values()->set_str_value("10");
    acc_proto.add_signs(123LU);
    ViewValueProto view_proto;
    view_proto.set_type(ViewValueProto::STRING);
    view_proto.add_values()->set_str_value("baiduid");
    view_proto.add_signs(123LU);
    EXPECT_CALL(log, log_id()).WillRepeatedly(Return(190));
    EXPECT_CALL(log, joinkey()).WillRepeatedly(Return(187));
    EXPECT_CALL(log, log_time()).WillRepeatedly(Return(189));
    EXPECT_CALL(log, get_view_value(_, _))
        .WillOnce(Return(false))
        .WillOnce(DoAll(SetArgPointee<1>(view_name_proto), Return(true)))
        .WillOnce(Return(false))
        .WillOnce(DoAll(SetArgPointee<1>(view_name_proto), Return(true)))
        .WillOnce(DoAll(SetArgPointee<1>(value_proto), Return(true)))
        .WillOnce(Return(false))
        .WillOnce(DoAll(SetArgPointee<1>(view_name_proto), Return(true)))
        .WillOnce(DoAll(SetArgPointee<1>(value_proto), Return(true)))
        .WillOnce(DoAll(SetArgPointee<1>(acc_proto), Return(true)))
        .WillOnce(DoAll(SetArgPointee<1>(view_name_proto), Return(true)))
        .WillOnce(DoAll(SetArgPointee<1>(value_proto), Return(true)))
        .WillOnce(DoAll(SetArgPointee<1>(acc_proto), Return(true)))
        .WillOnce(DoAll(SetArgPointee<1>(view_name_proto), Return(true)))
        .WillOnce(DoAll(SetArgPointee<1>(value_proto), Return(true)))
        .WillOnce(DoAll(SetArgPointee<1>(acc_proto), Return(true)));
    EXPECT_CALL(log, get_view_sign(_, _))
        .WillOnce(Return(false))
        .WillOnce(DoAll(SetArgPointee<1>(view_proto), Return(true)));
    // get view_name failed
    EXPECT_FALSE(obj._extract(&log, &feas));
    // get value failed
    EXPECT_FALSE(obj._extract(&log, &feas));
    // get acc failed
    EXPECT_FALSE(obj._extract(&log, &feas));
    std::shared_ptr<DiscretizationInterface> tmp = obj._discretization;
    obj._discretization.reset();
    // dis failed!
    EXPECT_FALSE(obj._extract(&log, &feas));
    obj._discretization = tmp;
    // get view failed
    EXPECT_FALSE(obj._extract(&log, &feas));
    // ok case
    EXPECT_TRUE(obj._extract(&log, &feas));
    EXPECT_EQ(1, feas.size());
    printf("%s\n", feas[0]->ShortDebugString().c_str());
}

}
}
}
