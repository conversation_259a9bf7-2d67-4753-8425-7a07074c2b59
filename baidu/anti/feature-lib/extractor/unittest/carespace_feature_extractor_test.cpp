// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: carespace_feature_extractor_test.cpp
// @Last modified: 2018-03-28 22:58:53
// @Brief: 

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <Configure.h>
#include <sign_util.h>
#include "carespace_feature_extractor.h"

using anti::baselib::SignUtil;
using anti::themis::log_interface::ViewDescriptor;
using anti::themis::log_interface::LogRecordInterface;
using anti::themis::log_interface::ViewValueProto;
using ::testing::_;
using ::testing::Return;
using ::testing::DoAll;
using ::testing::SetArgPointee;

namespace anti {
namespace themis {
namespace feature_lib {

class CarespaceFeatureExtractorTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
        _view_des.push_back({"ClickBaiduLog", 0U, 1U, "userid"});
        _view_des.push_back({"ClickBaiduLog", 1U, 1U, "price"});
        _view_des.push_back({"ClickBaiduLog", 2U, 1U, "baiduid"});
    }
    virtual void TearDown() {}

private:
    CarespaceFeatureExtractor _obj;
    std::vector<ViewDescriptor> _view_des;
    anti::themis::log_parser_lib::CareExp _care_exp;
};

TEST_F(CarespaceFeatureExtractorTestSuite, construction_case) {
    EXPECT_EQ(FeatureValueProto::CARESPACE, _obj._ftype);
}

TEST_F(CarespaceFeatureExtractorTestSuite, _init_case) {
    comcfg::Configure conf;
    ASSERT_TRUE(conf.load("./conf", "feature_extractor_test.conf") == 0);
    uint32_t conf_size = conf["carespace"]["invalid"]["feature"].size();
    ASSERT_LE(2U, conf_size);
    for (uint32_t i = 0U; i < 2U; ++i) {
        ASSERT_FALSE(_obj._init(conf["carespace"]["invalid"]["feature"][i], _view_des, &_care_exp));
        _obj._uninit();
    }

    conf_size = conf["carespace"]["valid"]["feature"].size();
    ASSERT_LE(2U, conf_size);
    for (uint32_t i = 0U; i < 2U; ++i) {
        ASSERT_TRUE(_obj._init(conf["carespace"]["valid"]["feature"][i], _view_des, &_care_exp));
        EXPECT_EQ(FeatureValueProto::CARESPACE, _obj._ftype);
        _obj._uninit();
    }
}

class MockLogRecordInterface : public LogRecordInterface {
public:
    virtual ~MockLogRecordInterface() {}
    MOCK_METHOD2(init, bool(const std::string&, const std::string&));
    MOCK_METHOD1(next, bool(int32_t*));
    MOCK_METHOD0(uninit, void());
    MOCK_CONST_METHOD1(get_view_descriptor, const ViewDescriptor*(const std::string&));
    MOCK_CONST_METHOD0(view_descriptors, const std::vector<ViewDescriptor>*());

    MOCK_METHOD2(parse, bool(const char*, uint64_t));
    MOCK_METHOD0(clear, void());
    MOCK_METHOD1(reset_log_level, bool(uint32_t));
    MOCK_CONST_METHOD0(has_next, bool());
    MOCK_METHOD0(next, bool());

    MOCK_CONST_METHOD0(joinkey, uint64_t());
    MOCK_CONST_METHOD0(log_id, uint64_t());
    MOCK_CONST_METHOD0(log_time, uint64_t());
    MOCK_CONST_METHOD0(is_spam, bool());

    MOCK_METHOD2(get_view_value, bool(const ViewDescriptor&, ViewValueProto*));
    MOCK_METHOD2(get_view_sign, bool(const std::vector<ViewDescriptor>&, ViewValueProto*));
};

// TEST_F(CarespaceFeatureExtractorTestSuite, _extract_by_invalid_input_case) {
//     EXPECT_FALSE(_obj._extract(_care_exp, NULL, NULL));
// 
//     MockLogRecordInterface log;
//     EXPECT_FALSE(_obj._extract(_care_exp, &log, NULL));
// 
//     std::vector<CareConditionInterface*> con;
//     con.push_back(NULL);
//     _obj._fea_care._conds.push_back(con);
//     std::vector<std::shared_ptr<FeatureValueProto> > feas;
//     EXPECT_FALSE(_obj._extract(_care_exp, &log, &feas));
//     _obj._fea_care.uninit();
//     _obj._fea_care._sc = CareSpace::ALL;
// 
//     ViewValueProto value;
//     value.add_signs(123LU);
//     EXPECT_CALL(log, get_view_sign(_, _))
//             .WillOnce(Return(false))
//             .WillOnce(DoAll(SetArgPointee<1>(value), Return(true)));
//     EXPECT_CALL(log, log_id()).WillRepeatedly(Return(119LU));
//     EXPECT_FALSE(_obj._extract(_care_exp, &log, &feas));
//     EXPECT_FALSE(_obj._extract(_care_exp, &log, &feas));
// }

TEST_F(CarespaceFeatureExtractorTestSuite, _extract_by_valid_input_case) {
    comcfg::Configure conf;
    ASSERT_TRUE(conf.load("./conf", "feature_extractor_test.conf") == 0);
    ASSERT_TRUE(_obj._init(conf["carespace"]["valid"]["feature"][0], _view_des, &_care_exp));

    MockLogRecordInterface log;
    std::vector<std::shared_ptr<FeatureValueProto> > feas;

    ViewValueProto value;
    value.add_values()->set_str_value("123");
    value.add_signs(123456LU);
    EXPECT_CALL(log, get_view_sign(_, _))
            .WillRepeatedly(DoAll(SetArgPointee<1>(value), Return(true)));
    EXPECT_CALL(log, log_id()).WillRepeatedly(Return(119LU));
    EXPECT_CALL(log, log_time()).WillRepeatedly(Return(110000LU));
    EXPECT_CALL(log, joinkey()).WillRepeatedly(Return(120LU));

    ASSERT_TRUE(_care_exp.reset(&log));
    ASSERT_TRUE(_obj._extract(_care_exp, &log, &feas));
    ASSERT_EQ(1U, feas.size());
    for (uint32_t i = 0; i < 1U; ++i) {
        EXPECT_EQ(0LU, feas[i]->feature_id());
        EXPECT_EQ(123456LU, feas[i]->view_sign());
        EXPECT_EQ(FeatureValueProto::CARESPACE, feas[i]->feature_type());
        EXPECT_EQ(120LU, feas[i]->joinkey());
        EXPECT_EQ(119LU, feas[i]->log_id());
        EXPECT_EQ(110000LU, feas[i]->log_time());
        EXPECT_STREQ("123", feas[i]->view().data());
        EXPECT_EQ(110LU, feas[i]->coord());
        EXPECT_TRUE(feas[i]->valid());
        EXPECT_STREQ("1", feas[i]->value().data());
        EXPECT_TRUE(feas[i]->condition());
    }
    _obj._uninit();
}

TEST_F(CarespaceFeatureExtractorTestSuite, _extract_by_baiduid) {
    comcfg::Configure conf;
    ASSERT_TRUE(conf.load("./conf", "feature_extractor_test.conf") == 0);
    ASSERT_TRUE(_obj._init(conf["baiduid"]["empty"]["feature"][0], _view_des, &_care_exp));
    MockLogRecordInterface log;
    std::vector<std::shared_ptr<FeatureValueProto> > feas;
    ViewValueProto value;
    value.add_values()->set_str_value("00000000000000000000000000000000");
    value.set_type(ViewValueProto::NOT_EXIST);
    uint64_t sign = 0;
    SignUtil::create_sign_md64("00000000000000000000000000000000", &sign);
    value.add_signs(sign);
    EXPECT_CALL(log, get_view_sign(_, _))
            .WillRepeatedly(DoAll(SetArgPointee<1>(value), Return(true)));
    EXPECT_CALL(log, log_id()).WillRepeatedly(Return(119LU));
    EXPECT_CALL(log, log_time()).WillRepeatedly(Return(110000LU));
    EXPECT_CALL(log, joinkey()).WillRepeatedly(Return(120LU));
    EXPECT_CALL(log, get_view_value(_, _))
            .WillRepeatedly(DoAll(SetArgPointee<1>(value), Return(true)));

    ASSERT_TRUE(_care_exp.reset(&log));
    ASSERT_TRUE(_obj._extract(_care_exp, &log, &feas));
    ASSERT_EQ(1U, feas.size());
    EXPECT_EQ("1", feas[0]->value());
    EXPECT_TRUE(feas[0]->condition());
    _obj._uninit();
    ASSERT_TRUE(_obj._init(conf["baiduid"]["empty"]["feature"][1], _view_des, &_care_exp));
    feas.clear();

    ASSERT_TRUE(_care_exp.reset(&log));
    ASSERT_TRUE(_obj._extract(_care_exp, &log, &feas));
    ASSERT_EQ(1U, feas.size());
    EXPECT_EQ("1", feas[0]->value());
    EXPECT_TRUE(feas[0]->condition());
}

TEST_F(CarespaceFeatureExtractorTestSuite, _extract_by_baiduid_invalid) {
    comcfg::Configure conf;
    ASSERT_TRUE(conf.load("./conf", "feature_extractor_test.conf") == 0);
    ASSERT_TRUE(_obj._init(conf["baiduid"]["invalid"]["feature"][0], _view_des, &_care_exp));
    MockLogRecordInterface log;
    std::vector<std::shared_ptr<FeatureValueProto> > feas;
    ViewValueProto value;
    value.add_values()->set_str_value("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF");
    value.set_type(ViewValueProto::INVALID);
    uint64_t sign = 0;
    SignUtil::create_sign_md64("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF", &sign);
    value.add_signs(sign);
    EXPECT_CALL(log, get_view_sign(_, _))
            .WillRepeatedly(DoAll(SetArgPointee<1>(value), Return(true)));
    EXPECT_CALL(log, log_id()).WillRepeatedly(Return(119LU));
    EXPECT_CALL(log, log_time()).WillRepeatedly(Return(110000LU));
    EXPECT_CALL(log, joinkey()).WillRepeatedly(Return(120LU));
    EXPECT_CALL(log, get_view_value(_, _))
            .WillRepeatedly(DoAll(SetArgPointee<1>(value), Return(true)));

    ASSERT_TRUE(_care_exp.reset(&log));
    ASSERT_TRUE(_obj._extract(_care_exp, &log, &feas));
    ASSERT_EQ(1U, feas.size());
    EXPECT_EQ("1", feas[0]->value());
    EXPECT_TRUE(feas[0]->condition());
    _obj._uninit();
    ASSERT_TRUE(_obj._init(conf["baiduid"]["invalid"]["feature"][1], _view_des, &_care_exp));
    feas.clear();
    ASSERT_TRUE(_care_exp.reset(&log));
    ASSERT_TRUE(_obj._extract(_care_exp, &log, &feas));
    ASSERT_EQ(1U, feas.size());
    EXPECT_EQ("1", feas[0]->value());
    EXPECT_TRUE(feas[0]->condition());
}

TEST_F(CarespaceFeatureExtractorTestSuite, _extract_by_baiduid_valid) {
    comcfg::Configure conf;
    ASSERT_TRUE(conf.load("./conf", "feature_extractor_test.conf") == 0);
    ASSERT_TRUE(_obj._init(conf["baiduid"]["valid"]["feature"][0], _view_des, &_care_exp));
    MockLogRecordInterface log;
    std::vector<std::shared_ptr<FeatureValueProto> > feas;
    ViewValueProto value;
    value.add_values()->set_str_value("FFFFFFFFFFFFFFFFFFFFFFFFFFFF0000");
    value.set_type(ViewValueProto::STRING);
    uint64_t sign = 0;
    SignUtil::create_sign_md64("FFFFFFFFFFFFFFFFFFFFFFFFFFFF0000", &sign);
    value.add_signs(sign);
    EXPECT_CALL(log, get_view_sign(_, _))
            .WillRepeatedly(DoAll(SetArgPointee<1>(value), Return(true)));
    EXPECT_CALL(log, log_id()).WillRepeatedly(Return(119LU));
    EXPECT_CALL(log, log_time()).WillRepeatedly(Return(110000LU));
    EXPECT_CALL(log, joinkey()).WillRepeatedly(Return(120LU));
    EXPECT_CALL(log, get_view_value(_, _))
            .WillRepeatedly(DoAll(SetArgPointee<1>(value), Return(true)));

    ASSERT_TRUE(_care_exp.reset(&log));
    ASSERT_TRUE(_obj._extract(_care_exp, &log, &feas));
    ASSERT_EQ(1U, feas.size());
    EXPECT_EQ("1", feas[0]->value());
    EXPECT_TRUE(feas[0]->condition());
    _obj._uninit();
    ASSERT_TRUE(_obj._init(conf["baiduid"]["valid"]["feature"][1], _view_des, &_care_exp));
    feas.clear();
    ASSERT_TRUE(_care_exp.reset(&log));
    ASSERT_TRUE(_obj._extract(_care_exp, &log, &feas));
    ASSERT_EQ(1U, feas.size());
    EXPECT_EQ("1", feas[0]->value());
    EXPECT_TRUE(feas[0]->condition());

    ViewValueProto value_empty;
    value_empty.add_values()->set_str_value("00000000000000000000000000000000");
    value_empty.set_type(ViewValueProto::NOT_EXIST);
    SignUtil::create_sign_md64("00000000000000000000000000000000", &sign);
    value_empty.add_signs(sign);
    EXPECT_CALL(log, get_view_value(_, _))
            .WillRepeatedly(DoAll(SetArgPointee<1>(value_empty), Return(true)));
    EXPECT_CALL(log, get_view_sign(_, _))
            .WillRepeatedly(DoAll(SetArgPointee<1>(value_empty), Return(true)));
    feas.clear();
    ASSERT_TRUE(_care_exp.reset(&log));
    ASSERT_TRUE(_obj._extract(_care_exp, &log, &feas));
    ASSERT_EQ(sign, feas[0]->view_sign());
    EXPECT_EQ("0", feas[0]->value());
    EXPECT_FALSE(feas[0]->condition());

    ViewValueProto value_invalid;
    value_invalid.add_values()->set_str_value("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF");
    value_invalid.set_type(ViewValueProto::INVALID);
    SignUtil::create_sign_md64("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF", &sign);
    value_invalid.add_signs(sign);
    EXPECT_CALL(log, get_view_value(_, _))
            .WillRepeatedly(DoAll(SetArgPointee<1>(value_invalid), Return(true)));
    EXPECT_CALL(log, get_view_sign(_, _))
            .WillRepeatedly(DoAll(SetArgPointee<1>(value_invalid), Return(true)));
    feas.clear();
    ASSERT_TRUE(_care_exp.reset(&log));
    ASSERT_TRUE(_obj._extract(_care_exp, &log, &feas));
    ASSERT_EQ(sign, feas[0]->view_sign());
    EXPECT_EQ("0", feas[0]->value());
    EXPECT_FALSE(feas[0]->condition());
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

