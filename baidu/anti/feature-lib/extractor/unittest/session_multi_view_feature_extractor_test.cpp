#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <view_value.pb.h>
#include <file_dict_manager.h>
#include "session_multi_view_feature_extractor.h"
#include "com_log.h"
#include "mock_log_record_interface.h"

using anti::themis::log_interface::ViewDescriptor;
using anti::themis::log_interface::LogRecordInterface;
using anti::themis::log_interface::ViewValueProto;
using anti::themis::common_lib::FileDictManagerSingleton;
using ::testing::_;
using ::testing::Return;
using ::testing::SetArgPointee;
using ::testing::DoAll;

namespace anti {
namespace themis {
namespace feature_lib {

class SessionExtendViewExtractorTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
        ASSERT_EQ(_conf.load("conf", "session_mulit_view_feature_extractor_test.conf"), 0);
    }
    virtual void TearDown() {}

private:
    comcfg::Configure _conf;
    SessionExtendViewExtractor _obj;
    MockLogRecordInterface _log;
    std::vector<ViewDescriptor> _view_des = {{"AspLog", 0, 1, "view1"}};
    anti::themis::log_parser_lib::CareExp _care_exp;
};

TEST_F(SessionExtendViewExtractorTestSuite, init_fail) {
    comcfg::ConfigUnit& conf = _conf["EXTEND_VIEW"]["INVALID"];
    for (uint32_t i = 0; i < conf.size(); ++i) {
        CDEBUG_LOG("TEST INVALID %u", i);
        ASSERT_FALSE(_obj.init(conf[i], _view_des, &_care_exp));
    }
}

TEST_F(SessionExtendViewExtractorTestSuite, init_succ) {
    comcfg::ConfigUnit& conf = _conf["EXTEND_VIEW"]["VALID"];
    ASSERT_EQ(2, conf.size());
    ASSERT_TRUE(_obj.init(conf[0], _view_des, &_care_exp));
    ASSERT_TRUE(_obj._is_reliable);
    ASSERT_TRUE(_obj._add_views.empty());

    ASSERT_TRUE(_obj.init(conf[1], _view_des, &_care_exp));
    ASSERT_FALSE(_obj._is_reliable);
    ASSERT_EQ(2, _obj._add_views.size());
}

TEST_F(SessionExtendViewExtractorTestSuite, extract_extend_view_fail_with_null_ptr) {
    MockLogRecordInterface log;
    FeatureValueProto fea;
    ASSERT_FALSE(_obj.extract_extend_view(_care_exp, NULL, &fea));
    ASSERT_FALSE(_obj.extract_extend_view(_care_exp, &log, NULL));
}

TEST_F(SessionExtendViewExtractorTestSuite, extract_extend_view_fail_with_get_view_value) {
    MockLogRecordInterface log;
    FeatureValueProto fea;
    ASSERT_TRUE(_care_exp.append("[*]", _view_des));
    _obj._care_str = "[*]";

    ViewValueProto value;
    value.add_signs(123);
    value.add_values()->set_str_value("123");
    EXPECT_CALL(log, get_view_sign(_, _))
        .WillOnce(DoAll(SetArgPointee<1>(value), Return(true)));

    _obj._add_views.push_back(std::make_pair(_view_des[0], "xxx"));
    EXPECT_CALL(log, get_view_value(_, _)).WillOnce(Return(false));
    ASSERT_TRUE(_care_exp.reset(&log));
    ASSERT_FALSE(_obj.extract_extend_view(_care_exp, &log, &fea));
}

TEST_F(SessionExtendViewExtractorTestSuite, extract_extend_view_succ_no_add_view) {
    MockLogRecordInterface log;
    FeatureValueProto fea;
    ASSERT_TRUE(_care_exp.append("[*]", _view_des));

    _obj._care_str = "[*]";
    ViewValueProto value;
    value.add_signs(123);
    value.add_values()->set_str_value("123");
    EXPECT_CALL(log, get_view_sign(_, _))
        .WillOnce(DoAll(SetArgPointee<1>(value), Return(true)));

    ASSERT_TRUE(_care_exp.reset(&log));
    ASSERT_TRUE(_obj.extract_extend_view(_care_exp, &log, &fea));
    ASSERT_EQ(1, fea.session().keys_size());
    ASSERT_EQ(123, fea.session().keys(0).view());
}

TEST_F(SessionExtendViewExtractorTestSuite, extract_extend_view_fail_with_set_field_value) {
    MockLogRecordInterface log;
    FeatureValueProto fea;
    ASSERT_TRUE(_care_exp.append("[*]", _view_des));

    _obj._care_str = "[*]";
    ViewValueProto value;
    value.add_signs(123);
    value.add_values()->set_str_value("123");
    EXPECT_CALL(log, get_view_sign(_, _))
        .WillOnce(DoAll(SetArgPointee<1>(value), Return(true)));

    _obj._add_views.push_back(std::make_pair(_view_des[0], "xxx"));
    EXPECT_CALL(log, get_view_value(_, _))
        .WillOnce(DoAll(SetArgPointee<1>(value), Return(true)));

    ASSERT_TRUE(_care_exp.reset(&log));
    ASSERT_FALSE(_obj.extract_extend_view(_care_exp, &log, &fea));
}

TEST_F(SessionExtendViewExtractorTestSuite, extract_extend_view_succ) {
    MockLogRecordInterface log;
    FeatureValueProto fea;
    ASSERT_TRUE(_care_exp.append("[*]", _view_des));

    _obj._care_str = "[*]";
    ViewValueProto value;
    value.add_signs(123);
    value.add_values()->set_str_value("123");
    EXPECT_CALL(log, get_view_sign(_, _))
        .WillOnce(DoAll(SetArgPointee<1>(value), Return(true)));

    _obj._add_views.push_back(std::make_pair(_view_des[0], "baiduid_fresh"));
    EXPECT_CALL(log, get_view_value(_, _))
        .WillOnce(DoAll(SetArgPointee<1>(value), Return(true)));
    ASSERT_TRUE(_care_exp.reset(&log));
    ASSERT_TRUE(_obj.extract_extend_view(_care_exp, &log, &fea));
    ASSERT_EQ(1, fea.session().keys_size());
    ASSERT_EQ(123, fea.session().keys(0).view());
}

class SessionMultiViewFeatureExtractorTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
        ASSERT_EQ(_conf.load("conf", "session_mulit_view_feature_extractor_test.conf"), 0);
    }
    virtual void TearDown() {}

private:
    comcfg::Configure _conf;
    SessionMultiViewFeatureExtractor _obj;
    MockLogRecordInterface _log;
    std::vector<ViewDescriptor> _view_des = {{"AspLog", 0, 1, "view1"}};
    anti::themis::log_parser_lib::CareExp _care_exp;
};

TEST_F(SessionMultiViewFeatureExtractorTestSuite, init_fail) {
    comcfg::ConfigUnit& conf = _conf["SESSION_MULTI_VIEW"]["INVALID"];
    for (uint32_t i = 0; i < conf.size(); ++i) {
        CDEBUG_LOG("test SessionMultiViewFeatureExtract invalid %u", i);
        ASSERT_FALSE(_obj._init(conf[i], _view_des, &_care_exp));
    }
}

TEST_F(SessionMultiViewFeatureExtractorTestSuite, init_succ) {
    comcfg::ConfigUnit& conf = _conf["SESSION_MULTI_VIEW"]["VALID"];
    for (uint32_t i = 0; i < conf.size(); ++i) {
        CDEBUG_LOG("test SessionMultiViewFeatureExtract valid %u", i);
        ASSERT_TRUE(_obj._init(conf[i], _view_des, &_care_exp));
    }
}

TEST_F(SessionMultiViewFeatureExtractorTestSuite, do_extract_fail_with_null_ptr) {
    MockLogRecordInterface log;
    FeatureValueProto fea;
    bool valid;
    ASSERT_FALSE(_obj._do_extract(_care_exp, NULL, &fea, &valid));
    ASSERT_FALSE(_obj._do_extract(_care_exp, &log, NULL, &valid));
    ASSERT_FALSE(_obj._do_extract(_care_exp, &log, &fea, NULL));
}

TEST_F(SessionMultiViewFeatureExtractorTestSuite, do_extract_fail_with_extract_extend_view) {
    MockLogRecordInterface log;
    FeatureValueProto fea;
    bool valid;

    ASSERT_TRUE(_obj._init(_conf["SESSION_MULTI_VIEW"]["VALID"][0], _view_des, &_care_exp));
    EXPECT_CALL(log, get_view_sign(_, _)).WillOnce(Return(false));
    ASSERT_TRUE(_care_exp.reset(&log));
    ASSERT_FALSE(_obj._do_extract(_care_exp, &log, &fea, &valid));
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

