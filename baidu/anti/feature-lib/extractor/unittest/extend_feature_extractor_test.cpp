// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: extend_feature_extractor_test.cpp
// @Last modified: 2018-04-02 21:13:26
// @Brief: 

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <Configure.h>
#include <com_log.h>
#include "extend_feature_extractor.h"

using anti::themis::log_interface::ViewDescriptor;
using anti::themis::log_interface::LogRecordInterface;
using anti::themis::log_interface::ViewValueProto;
using ::testing::_;
using ::testing::Return;
using ::testing::DoAll;
using ::testing::SetArgPointee;

namespace anti {
namespace themis {
namespace feature_lib {

const char* CONF_PATH = "./conf";
const char* CONF_FILE = "extend_fea_ext_test.conf";
class ExtendFeatureExtractorTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
        ASSERT_EQ(0, _conf.load(CONF_PATH, CONF_FILE));
        _view_des.push_back({"ClickBaiduLog", 0U, 1U, "baiduid"});
        _view_des.push_back({"ClickBaiduLog", 1U, 1U, "cntname"});
    }
    virtual void TearDown() {}

private:
    ExtendFeatureExtractor _obj;
    comcfg::Configure _conf;
    std::vector<ViewDescriptor> _view_des;
};

TEST_F(ExtendFeatureExtractorTestSuite, init_case) {
    for (uint32_t i = 0; i < _conf["invalid"].size(); ++i) {
        CWARNING_LOG("current i: %u", i);
        ExtendFeatureExtractor obj;
        EXPECT_FALSE(obj._init(_conf["invalid"][i], _view_des));
    }

    ExtendFeatureExtractor obj;
    ASSERT_TRUE(obj._init(_conf["valid"][0], _view_des));
    EXPECT_EQ(2U, obj._infos.size()); 
    EXPECT_EQ(FeatureValueProto::PV, obj._stype);
    uint64_t exp_fid[] = {111U, 222U};
    uint64_t exp_finfo[] = {1U, 0U};
    const char* exp_view_name[] = {"baiduid", "baiduid"};
    for (uint32_t i = 0; i < obj._infos.size(); ++i) {
        EXPECT_EQ(exp_fid[i], obj._infos[i].feature_id);
        ASSERT_EQ(1U, obj._infos[i].views.size());
        EXPECT_STREQ(exp_view_name[i], obj._infos[i].views[0].view_name.data());
        EXPECT_EQ(exp_finfo[i], obj._infos[i].finfos.size());
    }
}

class MockLogRecordInterface : public LogRecordInterface {
public:
    virtual ~MockLogRecordInterface() {}
    MOCK_METHOD1(next, bool(int32_t*));
    MOCK_METHOD2(init, bool(const std::string&, const std::string&));
    MOCK_METHOD0(uninit, void());
    MOCK_CONST_METHOD1(get_view_descriptor, const ViewDescriptor*(const std::string&));
    MOCK_CONST_METHOD0(view_descriptors, const std::vector<ViewDescriptor>*());

    MOCK_METHOD2(parse, bool(const char*, uint64_t));
    MOCK_METHOD0(clear, void());
    MOCK_METHOD1(reset_log_level, bool(uint32_t));
    MOCK_CONST_METHOD0(has_next, bool());
    MOCK_METHOD0(next, bool());

    MOCK_CONST_METHOD0(joinkey, uint64_t());
    MOCK_CONST_METHOD0(log_id, uint64_t());
    MOCK_CONST_METHOD0(log_time, uint64_t());
    MOCK_CONST_METHOD0(is_spam, bool());

    MOCK_METHOD2(get_view_value, bool(const ViewDescriptor&, ViewValueProto*));
    MOCK_METHOD2(get_view_sign, bool(const std::vector<ViewDescriptor>&, ViewValueProto*));
};

TEST_F(ExtendFeatureExtractorTestSuite, _extract_fea_trans_case) {
    // case: input invalid case
    {
        ExtendFeatureExtractor obj;
        EXPECT_TRUE(obj._init(_conf["valid"][0], _view_des));
        EXPECT_FALSE(obj._extract_fea_trans(NULL, NULL));
    }
    // case: get extend view_sign fail case
    {
        ExtendFeatureExtractor obj;
        ASSERT_TRUE(obj._init(_conf["valid"][0], _view_des));
        MockLogRecordInterface log;
        EXPECT_CALL(log, get_view_sign(_, _)) 
                .WillOnce(Return(false));
        EXPECT_CALL(log, log_id())
                .WillOnce(Return(123));
        FValQueryTransProto fea_trans;
        EXPECT_FALSE(obj._extract_fea_trans(&log, &fea_trans));
    }
    // case: succ
    {
        
        ExtendFeatureExtractor obj;
        ASSERT_TRUE(obj._init(_conf["valid"][0], _view_des));
        MockLogRecordInterface log;
        ViewValueProto value;
        value.add_values()->set_str_value("567");
        value.add_signs(567);
        EXPECT_CALL(log, get_view_sign(_, _)) 
                .WillRepeatedly(DoAll(SetArgPointee<1>(value), Return(true)));
        FValQueryTransProto fea_trans;
        EXPECT_TRUE(obj._extract_fea_trans(&log, &fea_trans));
        ASSERT_EQ(2, fea_trans.infos_size());
        for (int i = 0; i < fea_trans.infos_size(); ++i) {
            auto& qv = fea_trans.infos(i);
            ASSERT_TRUE(qv.has_request() && qv.request().has_view_sign());
            ASSERT_EQ(567, qv.request().view_sign());
            if (i == 0) {
                ASSERT_TRUE(qv.request().has_max_value_num());
                EXPECT_EQ(10, qv.request().max_value_num());
            }
        }
    }
}

TEST_F(ExtendFeatureExtractorTestSuite, _extract_case) {
    // case: input invalid case
    {
        ExtendFeatureExtractor obj;
        ASSERT_FALSE(obj._extract(NULL, NULL));
    }
    // case: extract view_sign fail
    {
        ExtendFeatureExtractor obj;
        ASSERT_TRUE(obj._init(_conf["valid"][0], _view_des));
        MockLogRecordInterface log;
        EXPECT_CALL(log, get_view_sign(_, _)) 
                .WillRepeatedly(Return(false));
        EXPECT_CALL(log, log_id())
                .WillOnce(Return(123));
        std::vector<std::shared_ptr<FeatureValueProto>> res;
        ASSERT_FALSE(obj._extract(&log, &res));
    }
    // case: call _extract_fea_trans fail 
    {
        ExtendFeatureExtractor obj;
        ASSERT_TRUE(obj._init(_conf["valid"][0], _view_des));
        MockLogRecordInterface log;
        ViewValueProto value;
        value.add_values()->set_str_value("567");
        value.add_signs(567);
        EXPECT_CALL(log, get_view_sign(_, _)) 
                .WillOnce(DoAll(SetArgPointee<1>(value), Return(true)))
                .WillOnce(Return(false));
        EXPECT_CALL(log, log_id())
                .WillRepeatedly(Return(123));
        EXPECT_CALL(log, joinkey())
                .WillRepeatedly(Return(245));
        EXPECT_CALL(log, log_time())
                .WillRepeatedly(Return(345));
        std::vector<std::shared_ptr<FeatureValueProto>> res;
        ASSERT_FALSE(obj._extract(&log, &res));
    }
    // case: succ
    {
        ExtendFeatureExtractor obj;
        anti::themis::log_parser_lib::CareExp _care_exp;
        obj._pv_coord = 100;
        ASSERT_TRUE(obj.init(_conf["valid"][0], _view_des, &_care_exp));
        MockLogRecordInterface log;
        ViewValueProto value;
        value.add_values()->set_str_value("567");
        value.add_signs(567);
        ViewValueProto ext_value;
        ext_value.add_values()->set_str_value("678");
        ext_value.add_signs(678);
        EXPECT_CALL(log, get_view_sign(_, _)) 
                .WillOnce(DoAll(SetArgPointee<1>(value), Return(true)))
                .WillOnce(DoAll(SetArgPointee<1>(value), Return(true)))
                .WillRepeatedly(DoAll(SetArgPointee<1>(ext_value), Return(true)));
        EXPECT_CALL(log, log_id())
                .WillRepeatedly(Return(123));
        EXPECT_CALL(log, joinkey())
                .WillRepeatedly(Return(245));
        EXPECT_CALL(log, log_time())
                .WillRepeatedly(Return(345));
        std::vector<std::shared_ptr<FeatureValueProto>> res;
        FeatureExtractorInterface* p = &obj;
        ASSERT_TRUE(_care_exp.reset(&log));  
        ASSERT_TRUE(p->extract(_care_exp, &log, &res));
        ASSERT_EQ(1U, res.size());
        auto fea = res[0];
        ASSERT_TRUE(fea->has_feature_id());
        EXPECT_EQ(888U, fea->feature_id());
        ASSERT_TRUE(fea->has_view_sign());
        EXPECT_EQ(567, fea->view_sign());
        ASSERT_TRUE(fea->has_feature_type());
        EXPECT_EQ(FeatureValueProto::FEATURE_VALUE_EXTEND, fea->feature_type());
        ASSERT_TRUE(fea->has_joinkey());
        EXPECT_EQ(245U, fea->joinkey());
        ASSERT_TRUE(fea->has_log_id());
        EXPECT_EQ(123U, fea->log_id());
        ASSERT_TRUE(fea->has_log_time());
        EXPECT_EQ(345, fea->log_time());
        ASSERT_TRUE(fea->has_view());
        EXPECT_STREQ("567", fea->view().data());
        ASSERT_TRUE(fea->has_seg_type());
        EXPECT_EQ(FeatureValueProto::PV, fea->seg_type());
        ASSERT_TRUE(fea->has_coord());
        EXPECT_EQ(101, fea->coord());
        ASSERT_TRUE(fea->has_fea_serv_trans());
        ASSERT_EQ(2, fea->fea_serv_trans().infos_size());
    }
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

