// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: lijie(<EMAIL>)
// 
//
#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <Configure.h>
#include "concentration_feature_extractor.h"
using ::testing::_;
using ::testing::Return;
using ::testing::DoAll;
using ::testing::SetArgPointee;

using anti::themis::log_interface::ViewDescriptor;
using anti::themis::log_interface::LogRecordInterface;
using anti::themis::log_interface::ViewValueProto;

namespace anti {
namespace themis {
namespace feature_lib {

class ConcentrationFeatureExtractorTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
        ASSERT_TRUE(_conf.load("./conf", "feature_extractor_test.conf") == 0);
        _view_des.push_back({"ClickBaiduLog", 0U, 1U, "ipb"});
        _view_des.push_back({"ClickBaiduLog", 1U, 1U, "cntname"});
        _view_des.push_back({"ClickBaiduLog", 2U, 1U, "price"});
        _view_des.push_back({"ClickBaiduLog", 3U, 1U, "ip"});
        _view_des.push_back({"ClickBaiduLog", 4U, 1U, "ua"});
        _view_des.push_back({"ClickBaiduLog", 5U, 1U, "baiduid"});
    }
    virtual void TearDown() {}
private:
    ConcentrationFeatureExtractor _obj;
    std::vector<ViewDescriptor> _view_des;
    comcfg::Configure _conf;
};

TEST_F(ConcentrationFeatureExtractorTestSuite, construct) {
    ConcentrationFeatureExtractor obj;
    EXPECT_EQ(FeatureValueProto::CONCENTRATION, obj._ftype);
    EXPECT_EQ(FeatureValueProto::PV, obj._stype);
}

TEST_F(ConcentrationFeatureExtractorTestSuite, test_init) {
    uint32_t conf_size = _conf["concentration"]["invalid"]["feature"].size();
    ASSERT_EQ(5U, conf_size);
    for (uint32_t i = 0; i <= conf_size; i++) {
        ConcentrationFeatureExtractor obj;
        ASSERT_FALSE(obj._init(_conf["concentration"]["invalid"]["feature"][i], _view_des));
    }

    conf_size = _conf["concentration"]["valid"]["feature"].size();
    ASSERT_EQ(3U, conf_size);
    uint32_t data_vids[] = {1, 1, 2};
    FeatureValueProto::FeatureType f[] = {
            FeatureValueProto::CONCENTRATION, 
            FeatureValueProto::DISTINCT, 
            FeatureValueProto::SUM_SEGMENT};
    bool acc_main_view[] = {false, false, true};
    for (uint32_t i = 0; i < conf_size; ++i) {
        ConcentrationFeatureExtractor obj;
        ASSERT_TRUE(obj._init(_conf["concentration"]["valid"]["feature"][i], _view_des));
        ASSERT_EQ(1U, obj._data_views.size());
        EXPECT_EQ(f[i], obj._ftype);
        EXPECT_EQ(data_vids[i], obj._data_views[0].view_id);
        EXPECT_EQ(acc_main_view[i], obj._acc_main_view);
    }

}

class MockLogRecordInterface : public LogRecordInterface {
public:
    virtual ~MockLogRecordInterface() {}
    MOCK_METHOD1(next, bool(int32_t*));
    MOCK_METHOD2(init, bool(const std::string&, const std::string&));
    MOCK_METHOD0(uninit, void());
    MOCK_CONST_METHOD1(get_view_descriptor, const ViewDescriptor*(const std::string&));
    MOCK_CONST_METHOD0(view_descriptors, const std::vector<ViewDescriptor>*());
    MOCK_METHOD2(parse, bool(const char*, uint64_t));
    MOCK_METHOD0(clear, void());
    MOCK_METHOD1(reset_log_level, bool(uint32_t));
    MOCK_CONST_METHOD0(has_next, bool());
    MOCK_METHOD0(next, bool());
    MOCK_CONST_METHOD0(joinkey, uint64_t());
    MOCK_CONST_METHOD0(log_id, uint64_t());
    MOCK_CONST_METHOD0(log_time, uint64_t());
    MOCK_CONST_METHOD0(is_spam, bool());
    MOCK_METHOD2(get_view_value, bool(const ViewDescriptor&, ViewValueProto*));
    MOCK_METHOD2(get_view_sign, bool(const std::vector<ViewDescriptor>&, ViewValueProto*));
};

TEST_F(ConcentrationFeatureExtractorTestSuite, _extract_by_invalid_input_case) {
    ASSERT_TRUE(_obj._init(_conf["concentration"]["valid"]["feature"][0], _view_des));
    MockLogRecordInterface log;
    ViewValueProto value[2];
    value[0].set_type(ViewValueProto::STRING);
    value[0].add_values()->set_str_value("123");
    value[0].add_signs(123LU);
    value[1].set_type(ViewValueProto::STRING);
    value[1].add_values()->set_str_value("456");
    // 1. mock for get data_view sign false: get_view_sign return false 
    // 2. mock for get data_view sign false: value size and sign size unequal
    // 3&4. mock for get view sign false: get_view_sign return false
    // 5&6. mock for get view sign false: value size and sign size unequal
    EXPECT_CALL(log, get_view_sign(_, _))
            .WillOnce(Return(false))
            .WillOnce(DoAll(SetArgPointee<1>(value[1]), Return(true)))
            .WillOnce(DoAll(SetArgPointee<1>(value[0]), Return(true)))
            .WillOnce(Return(false))
            .WillOnce(DoAll(SetArgPointee<1>(value[0]), Return(true)))
            .WillOnce(DoAll(SetArgPointee<1>(value[1]), Return(true)));
    EXPECT_CALL(log, log_id()).WillRepeatedly(Return(119LU));

    EXPECT_FALSE(_obj._extract(NULL, NULL));
    std::vector<std::shared_ptr<FeatureValueProto> > feas;
    // get data_view sign false
    EXPECT_FALSE(_obj._extract(&log, &feas));
    EXPECT_FALSE(_obj._extract(&log, &feas));
    // get data sign false
    EXPECT_FALSE(_obj._extract(&log, &feas));
    EXPECT_FALSE(_obj._extract(&log, &feas));
}

TEST_F(ConcentrationFeatureExtractorTestSuite, _extract_valid_case) {
    ASSERT_TRUE(_obj._init(_conf["concentration"]["valid"]["feature"][0], _view_des));
    MockLogRecordInterface log;
    std::vector<std::shared_ptr<FeatureValueProto> > feas;
    ViewValueProto value;
    value.set_type(ViewValueProto::STRING);
    value.add_values()->set_str_value("123");
    value.add_signs(123LU);
    ViewValueProto data_value;
    data_value.add_values()->set_str_value("456");
    data_value.add_signs(456U);
    EXPECT_CALL(log, get_view_sign(_, _))
            .WillOnce(DoAll(SetArgPointee<1>(data_value), Return(true)))
            .WillOnce(DoAll(SetArgPointee<1>(value), Return(true)));
    EXPECT_CALL(log, log_id()).WillRepeatedly(Return(222LU));
    EXPECT_CALL(log, log_time()).WillRepeatedly(Return(110000LU));
    EXPECT_CALL(log, joinkey()).WillRepeatedly(Return(120LU));
    _obj._stype = FeatureValueProto::PV;
    _obj._ftype = FeatureValueProto::CONCENTRATION;
    _obj._pv_coord = 8L; 

    ASSERT_TRUE(_obj._extract(&log, &feas));
    ASSERT_EQ(1U, feas.size());
    EXPECT_EQ(123LU, feas[0]->view_sign());
    EXPECT_EQ(456LU, feas[0]->data_view_sign());
    EXPECT_EQ(FeatureValueProto::PV, feas[0]->seg_type());
    EXPECT_EQ(FeatureValueProto::CONCENTRATION, feas[0]->feature_type());
    EXPECT_EQ(8L, feas[0]->coord());
}

TEST_F(ConcentrationFeatureExtractorTestSuite, test_acp_init) {
    uint32_t conf_size = _conf["acp"]["invalid"]["feature"].size();
    ASSERT_EQ(5U, conf_size);
    ConcentrationFeatureExtractor obj;
    for (uint32_t i = 0; i <= conf_size; i++) {
        ASSERT_FALSE(obj._init(_conf["acp"]["invalid"]["feature"][i], _view_des));
    }
    ASSERT_TRUE(obj._init(_conf["acp"]["valid"]["feature"][0], _view_des));
    ASSERT_EQ(1U, obj._data_views.size());
    EXPECT_EQ(2U, obj._data_views[0].view_id);
}

TEST_F(ConcentrationFeatureExtractorTestSuite, _extract_acp_valid_case) {
    ASSERT_TRUE(_obj._init(_conf["acp"]["valid"]["feature"][0], _view_des));
    MockLogRecordInterface log;
    std::vector<std::shared_ptr<FeatureValueProto> > feas;
    ViewValueProto value;
    value.set_type(ViewValueProto::STRING);
    value.add_values()->set_str_value("123");
    value.add_signs(123LU);
    ViewValueProto data_value;
    data_value.add_values()->set_int_value(456L);
    data_value.add_signs(456U);
    EXPECT_CALL(log, get_view_sign(_, _))
            .WillOnce(DoAll(SetArgPointee<1>(data_value), Return(true)))
            .WillOnce(DoAll(SetArgPointee<1>(value), Return(true)));
    EXPECT_CALL(log, log_id()).WillRepeatedly(Return(222LU));
    EXPECT_CALL(log, log_time()).WillRepeatedly(Return(110000LU));
    EXPECT_CALL(log, joinkey()).WillRepeatedly(Return(120LU));
    _obj._ftype = FeatureValueProto::ACP;
    //_obj._pv_coord = 8L; 

    ASSERT_TRUE(_obj._extract(&log, &feas));
    ASSERT_EQ(1U, feas.size());
    EXPECT_EQ(123LU, feas[0]->view_sign());
    EXPECT_EQ(456LU, feas[0]->data_view_sign());
    EXPECT_EQ(456LU, feas[0]->acp_field().price());
    EXPECT_EQ(FeatureValueProto::TIME, feas[0]->seg_type());
    EXPECT_EQ(FeatureValueProto::ACP, feas[0]->feature_type());
    EXPECT_EQ(110L, feas[0]->coord());
}

TEST_F(ConcentrationFeatureExtractorTestSuite, _extract_main_view) {
    ASSERT_TRUE(_obj._init(_conf["concentration"]["valid"]["feature"][2], _view_des));
    MockLogRecordInterface log;
    std::vector<std::shared_ptr<FeatureValueProto> > feas;
    ViewValueProto value;
    value.set_type(ViewValueProto::STRING);
    value.add_values()->set_str_value("123");
    value.add_signs(123LU);
    ViewValueProto data_value;
    data_value.add_values()->set_str_value("456");
    data_value.add_signs(456U);
    EXPECT_CALL(log, get_view_sign(_, _))
            .WillOnce(DoAll(SetArgPointee<1>(data_value), Return(true)))
            .WillOnce(DoAll(SetArgPointee<1>(value), Return(true)));
    EXPECT_CALL(log, log_id()).WillRepeatedly(Return(222LU));
    EXPECT_CALL(log, log_time()).WillRepeatedly(Return(110000LU));
    EXPECT_CALL(log, joinkey()).WillRepeatedly(Return(120LU));
    _obj._stype = FeatureValueProto::PV;
    _obj._ftype = FeatureValueProto::CONCENTRATION;
    _obj._pv_coord = 8L; 

    ASSERT_TRUE(_obj._extract(&log, &feas));
    ASSERT_EQ(2U, feas.size());
    EXPECT_EQ(123LU, feas[0]->view_sign());
    EXPECT_FALSE(feas[0]->has_data_view_sign());
    EXPECT_EQ(FeatureValueProto::PV, feas[0]->seg_type());
    EXPECT_EQ(FeatureValueProto::CONCENTRATION, feas[0]->feature_type());
    EXPECT_EQ(8L, feas[0]->coord());
    EXPECT_EQ(123LU, feas[1]->view_sign());
    EXPECT_EQ(456LU, feas[1]->data_view_sign());
    EXPECT_EQ(FeatureValueProto::PV, feas[1]->seg_type());
    EXPECT_EQ(FeatureValueProto::CONCENTRATION, feas[1]->feature_type());
    EXPECT_EQ(8L, feas[1]->coord());
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti
