// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangji<PERSON><PERSON>(<EMAIL>)
// 
// @File: dict_feature_extractor_test.cpp
// @Last modified: 2015-08-28 16:57:00
// @Brief: 

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <sign_util.h>
#include <Configure.h>
#include <file_dict_manager.h>
#include "dict_feature_extractor.h"

using anti::themis::log_interface::ViewDescriptor;
using anti::themis::log_interface::LogRecordInterface;
using anti::themis::log_interface::ViewValueProto;
using anti::themis::common_lib::FileDictManagerSingleton;
using anti::baselib::SignUtil;
using ::testing::_;
using ::testing::Return;
using ::testing::DoAll;
using ::testing::SetArgPointee;

namespace anti {
namespace themis {
namespace feature_lib {

class DictFeatureExtractorTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
        _view_des.push_back({"ClickBaiduLog", 0U, 1U, "userid"});
        _view_des.push_back({"ClickBaiduLog", 1U, 1U, "price"});
    }
    virtual void TearDown() {}

private:
    DictFeatureExtractor _obj;
    std::vector<ViewDescriptor> _view_des;
};

TEST_F(DictFeatureExtractorTestSuite, construction_case) {
    EXPECT_EQ(FeatureValueProto::FILE_DICT, _obj._ftype);
}

TEST_F(DictFeatureExtractorTestSuite, _init_case) {
    ASSERT_TRUE(FileDictManagerSingleton::instance().init("./conf", "file_dict_manager.conf"));

    comcfg::Configure conf;
    ASSERT_TRUE(conf.load("./conf", "feature_extractor_test.conf") == 0);
    uint32_t conf_size = conf["dict"]["invalid"]["feature"].size();
    ASSERT_LE(4U, conf_size);
    for (uint32_t i = 0U; i < 4U; ++i) {
        ASSERT_FALSE(_obj._init(conf["dict"]["invalid"]["feature"][i], _view_des));
        _obj._uninit();
    }

    ASSERT_TRUE(_obj._init(conf["dict"]["valid"]["feature"][0], _view_des));
    EXPECT_EQ(FeatureValueProto::FILE_DICT, _obj._ftype);
    EXPECT_TRUE(_obj._dict.get() != NULL);
    _obj._uninit();

    FileDictManagerSingleton::instance().uninit();
}

class MockLogRecordInterface : public LogRecordInterface {
public:
    virtual ~MockLogRecordInterface() {}
    MOCK_METHOD1(next, bool(int32_t*));
    MOCK_METHOD2(init, bool(const std::string&, const std::string&));
    MOCK_METHOD0(uninit, void());
    MOCK_CONST_METHOD1(get_view_descriptor, const ViewDescriptor*(const std::string&));
    MOCK_CONST_METHOD0(view_descriptors, const std::vector<ViewDescriptor>*());

    MOCK_METHOD2(parse, bool(const char*, uint64_t));
    MOCK_METHOD0(clear, void());
    MOCK_METHOD1(reset_log_level, bool(uint32_t));
    MOCK_CONST_METHOD0(has_next, bool());
    MOCK_METHOD0(next, bool());

    MOCK_CONST_METHOD0(joinkey, uint64_t());
    MOCK_CONST_METHOD0(log_id, uint64_t());
    MOCK_CONST_METHOD0(log_time, uint64_t());
    MOCK_CONST_METHOD0(is_spam, bool());

    MOCK_METHOD2(get_view_value, bool(const ViewDescriptor&, ViewValueProto*));
    MOCK_METHOD2(get_view_sign, bool(const std::vector<ViewDescriptor>&, ViewValueProto*));
};

TEST_F(DictFeatureExtractorTestSuite, _extract_by_invalid_input_case) {
    EXPECT_FALSE(_obj._extract(NULL, NULL));

    MockLogRecordInterface log;
    EXPECT_FALSE(_obj._extract(&log, NULL));

    ViewValueProto value;
    value.add_values()->set_str_value("xxxx");
    value.add_signs(123LU);
    EXPECT_CALL(log, get_view_sign(_, _))
            .WillOnce(Return(false))
            .WillOnce(DoAll(SetArgPointee<1>(value), Return(true)));
    EXPECT_CALL(log, log_id()).WillRepeatedly(Return(119LU));
    std::vector<std::shared_ptr<FeatureValueProto> > feas;
    EXPECT_FALSE(_obj._extract(&log, &feas));

    _obj._feature_id = 123LU;
    EXPECT_FALSE(_obj._extract(&log, &feas));
}

TEST_F(DictFeatureExtractorTestSuite, _extract_by_valid_input_case) {
    ASSERT_TRUE(FileDictManagerSingleton::instance().init("./conf", "file_dict_manager.conf"));
    comcfg::Configure conf;
    ASSERT_TRUE(conf.load("./conf", "feature_extractor_test.conf") == 0);
    ASSERT_TRUE(_obj._init(conf["dict"]["valid"]["feature"][0], _view_des));

    MockLogRecordInterface log;
    std::vector<std::shared_ptr<FeatureValueProto> > feas;

    bool exp_con[] = {true, false};
    std::string exp_value[] = {"1", "0"};
    std::string exp_str[] = {"12", "111"};
    uint64_t exp_sign[] = {0, 0};
    ViewValueProto value;
    for (uint32_t i = 0U; i < 2U; ++i) {
        SignUtil::create_sign_md64(exp_str[i], &exp_sign[i]);
        value.add_values()->set_str_value(exp_str[i]);
        value.add_signs(exp_sign[i]);
    }
    EXPECT_CALL(log, get_view_sign(_, _))
            .WillRepeatedly(DoAll(SetArgPointee<1>(value), Return(true)));
    EXPECT_CALL(log, log_id()).WillRepeatedly(Return(119LU));
    EXPECT_CALL(log, log_time()).WillRepeatedly(Return(110000LU));
    EXPECT_CALL(log, joinkey()).WillRepeatedly(Return(120LU));

    ASSERT_TRUE(_obj._extract(&log, &feas));
    ASSERT_EQ(2U, feas.size());
    for (uint32_t i = 0; i < 2U; ++i) {
        EXPECT_EQ(0LU, feas[i]->feature_id());
        EXPECT_EQ(exp_sign[i], feas[i]->view_sign());
        EXPECT_EQ(FeatureValueProto::FILE_DICT, feas[i]->feature_type());
        EXPECT_EQ(120LU, feas[i]->joinkey());
        EXPECT_EQ(119LU, feas[i]->log_id());
        EXPECT_EQ(110000LU, feas[i]->log_time());
        EXPECT_STREQ(exp_str[i].data(), feas[i]->view().data());
        EXPECT_EQ(110LU, feas[i]->coord());
        EXPECT_EQ(exp_con[i], feas[i]->condition());
        EXPECT_TRUE(feas[i]->valid());
        EXPECT_STREQ(exp_value[i].data(), feas[i]->value().data());
    }

    _obj._uninit();
    FileDictManagerSingleton::instance().uninit();
}

TEST_F(DictFeatureExtractorTestSuite, test_detect_ip6map_dict) {
    ASSERT_TRUE(FileDictManagerSingleton::instance().init("./conf", "file_dict_manager.conf"));
    const FileDictInterface* dict = FileDictManagerSingleton::instance().get_file_dict("care_test").get();
    std::string exp_str[] = {"2001:4::", "2001:5::"};
    uint64_t exp_sign[] = {0, 0};
    ViewValueProto value;
    for (uint32_t i = 0U; i < 2U; ++i) {
        SignUtil::create_sign_md64(exp_str[i], &exp_sign[i]);
        value.add_values()->set_str_value(exp_str[i]);
        value.add_signs(exp_sign[i]);
    }
    EXPECT_FALSE(detect_ip6map_dict(dict, value, 0)); 

    dict = FileDictManagerSingleton::instance().get_file_dict("ip6.black").get();
    EXPECT_FALSE(detect_ip6map_dict(dict, value, 0)); 
    EXPECT_TRUE(detect_ip6map_dict(dict, value, 1)); 

    FileDictManagerSingleton::instance().uninit();
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

