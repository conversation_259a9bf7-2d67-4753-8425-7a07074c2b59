# 0.ok case 
[@feature]
feature_id : 101
feature_type : fea_deviation
care : ['view_name':='cntname']['click_num':>15]
view : view
view_name : view_name
value : new_cookie_rate
accumulate : click_num
[.discretization]
type : divide
divide_num : 0.01
max_value : 100.0000000001

# 1.accumulate error
[@feature]
feature_id : 101
feature_type : fea_deviation
care : ['view_name':='cntname']['click_num':>15]
view : view
view_name : view_name
value : new_cookie_rate
accumulate : error3

# 2.no accumulate & no discretization
[@feature]
feature_id : 101
feature_type : fea_deviation
care : ['view_name':='cntname']['click_num':>15]
view : view
view_name : view_name
value : new_cookie_rate

# 3.discretization type empty
[@feature]
feature_id : 101
feature_type : fea_deviation
care : ['view_name':='cntname']['click_num':>15]
view : view
view_name : view_name
value : new_cookie_rate
[.discretization]
divide_num : 0.1

# 4.discretization type error
[@feature]
feature_id : 101
feature_type : fea_deviation
care : ['view_name':='cntname']['click_num':>15]
view : view
view_name : view_name
value : new_cookie_rate
[.discretization]
type : error_type
divide_num : 0.3

# 5.discretization no divide num
[@feature]
feature_id : 101
feature_type : fea_deviation
care : ['view_name':='cntname']['click_num':>15]
view : view
view_name : view_name
value : new_cookie_rate
[.discretization]
type : divide

# 6.no view_name
[@feature]
feature_id : 101
feature_type : fea_deviation
care : ['view_name':='cntname']['click_num':>15]
view : view
value : new_cookie_rate

# 7.view_name error
[@feature]
feature_id : 101
feature_type : fea_deviation
care : ['view_name':='cntname']['click_num':>15]
view : view
view_name : error_name
value : new_cookie_rate

# 8.no value
[@feature]
feature_id : 101
feature_type : fea_deviation
care : ['view_name':='cntname']['click_num':>15]
view : view
view_name : view_name

# 9.value error
[@feature]
feature_id : 101
feature_type : fea_deviation
care : ['view_name':='cntname']['click_num':>15]
view : view
view_name : view_name
value : error

