# feature_id 0~4 used for FeatureExtractorBase
[@feature]
# miss care
feature_id : 0

[@feature]
# invalid care
feature_id : 1
care : xxxx

[@feature]
# invalid view
feature_id : 2
care : [*]
view : xxx

[@feature]
feature_id : 3
care : [*]
view_level : click_level
view : userid,userid

[@feature]
feature_id : 4
care : ['price':>100]
view_level : click_level
view : userid

# feature_id 5~9 used for SegmentFeatureExtractor
[@feature]
# miss feature_type
feature_id : 5
care : [*]
view : userid

[@feature]
# invalid feature_type
feature_id : 6
care : [*]
view : userid
feature_type : xxx

[@feature]
# invalid window_type
feature_id : 7
care : [*]
view : userid
feature_type : segment
window_type : xxxx

[@feature]
feature_id : 8
care : [*]
view : userid
feature_type : segment
window_type : pv

[@feature]
feature_id : 9
care : [*]
view : userid
feature_type : multi_seg
window_type : time

# feature_id 10~16 used for RatioFeatureExtractor
[@feature]
# miss feature_type
feature_id : 10
care : [*]
view : userid

[@feature]
# invalid feature_type
feature_id : 11
care : [*]
view : userid
feature_type : xxx

[@feature]
# invalid window_type
feature_id : 12
care : [*]
view : userid
feature_type : ratio
window_type : xxxx

[@feature]
# invalid filter
feature_id : 13
care : [*]
view : userid
feature_type : ratio
filter : xxx
window_type : pv

[@feature]
# invalid refer
feature_id : 14
care : [*]
view : userid
feature_type : ratio
filter : [*]
refer : xxxxx
window_type : time

[@feature]
feature_id : 15
care : [*]
view : userid
feature_type : ratio
filter : [*]
refer : [*]
window_type : pv

[@feature]
feature_id : 16
care : [*]
view : userid
feature_type : ratio
filter : [*]
refer : [*]
window_type : time

# feature_id 17~23 used for DistributionFeatureExtractor
[@feature]
# miss feature_type
feature_id : 17
care : [*]
view : userid

[@feature]
# invalid feature_type
feature_id : 18
care : [*]
view : userid
feature_type : xxx

[@feature]
# invalid window_type
feature_id : 19
care : [*]
view : userid
feature_type : distribution
window_type : xxxx

[@feature]
# invalid data_view
feature_id : 20
care : [*]
view : userid
feature_type : distribution
data_view : xxx
window_type : pv

[@feature]
# invalid interval_endpoints
feature_id : 21
care : [*]
view : userid
feature_type : distribution
data_view : price
interval_endpoints : xxxxxxxxx
window_type : time

[@feature]
feature_id : 22
care : [*]
view : userid
feature_type : distribution
data_view : price
interval_endpoints : 5,100
window_type : pv

[@feature]
feature_id : 23
care : [*]
view : userid
feature_type : distribution
data_view : price
interval_endpoints : 5,100
window_type : time

[dict]
[.invalid]
[..@feature]
# no file key
feature_id : 123456
care : [*]
view : userid
feature_type : xxxx

[..@feature]
# no file_key
feature_id : 123456
care : [*]
view : userid
feature_type : file_dict

[..@feature]
# no file_key
feature_id : 123456
care : [*]
view : userid
feature_type : file_dict
file_key : xxxxxx

[..@feature]
# no file_key
feature_id : 123456
care : [*]
view : userid
feature_type : file_dict
file_key : unused-case 

[.valid]
[..@feature]
# no file_key
feature_id : 123456
care : [*]
view : userid
feature_type : file_dict
file_key : care_test

[carespace]
[.invalid]
[..@feature]
# no feature type
feature_id : 123456
care : [*]
view : userid

[..@feature]
# invalid feature type
feature_id : 123456
care : [*]
view : userid
feature_type : xxxx

[.valid]
[..@feature]
feature_id : 123456
care : [*]
view : userid
feature_type : carespace

[..@feature]
feature_id : 123456
care : [*]
view : userid
feature_type : carespace
condition : [*]

[multi_value]
[.invalid]
[..@feature]
# feature_type error
feature_id : 99990
care : [*]
view : userid
feature_type : multi_value1

[..@feature]
# feature_type null
feature_id : 99990
care : [*]
view : userid
feature_type : 

[multi_value]
[.valid]
[..@feature]
feature_id : 99990
care : [*]
view : cdtime_diff
feature_type : multi_value

[original]
[.invalid]
[..@feature]
# feature_type error
feature_id : 99990
care : [*]
view : userid
feature_type : original1

[..@feature]
# feature_type null
feature_id : 99990
care : [*]
view : userid
feature_type : 

[original]
[.valid]
[..@feature]
feature_id : 99990
care : [*]
view : cdtime_diff
feature_type : original

[auto_black]
[.auto_segment]
[..@feature]
feature_id : 12345
care : [*]
view : userid
feature_type : auto_segment
window_type : pv

[.auto_ratio]
[..@feature]
feature_id : 23456
care : [*]
view : userid
feature_type : auto_ratio
window_type : pv
refer  : [*]
filter : [*]

[count_dist]
[.invalid]
[..@feature]
feature_id : 0
care : [*]
view : cntname

[..@feature]
feature_id : 1
care : [*]
view : cntname
feature_type : distribution

[..@feature]
feature_id : 2
care : [*]
view : cntname
feature_type : count_distribution

[..@feature]
feature_id : 3
care : [*]
view : cntname
feature_type : count_distribution
window_type : xxx

[..@feature]
feature_id : 4
care : [*]
view : cntname
feature_type : count_distribution
window_type : pv
data_view : xxx

[.valid]
[..@feature]
feature_id : 4
care : [*]
view : cntname
feature_type : count_distribution
window_type : pv
data_view : clkip

# concentration
[concentration]
[.invalid]
[..@feature]
feature_id : 100
care : [*]
view : ipb

[..@feature]
feature_id : 101
care : [*]
view : ipb
feature_type : xxx

[..@feature]
feature_id : 102
care : [*]
view : ipb
feature_type : concentration

[..@feature]
feature_id : 103
care : [*]
view : ipb
feature_type : concentration
window_type : xxx

[..@feature]
feature_id : 104
care : [*]
view : ipb
feature_type : concentration
window_type : pv
data_view : xxx

[.valid]
[..@feature]
feature_id : 105
care : [*]
view : ipb
feature_type : concentration
window_type : pv
data_view : cntname

[..@feature]
feature_id : 106
care : [*]
view : ipb
feature_type : distinct
window_type : time
data_view : cntname

[..@feature]
feature_id : 107
care : [*]
view : ipb
feature_type : sum_segment
window_type : time
data_view : price
acc_main_view : 1

# acp
[acp]
[.invalid]
[..@feature]
feature_id : 110
care : [*]
view : cntname

[..@feature]
feature_id : 111
care : [*]
view : cntname
feature_type : xxx

[..@feature]
feature_id : 112
care : [*]
view : cntname
feature_type : acp

[..@feature]
feature_id : 113
care : [*]
view : cntname
feature_type : acp
window_type : xxx

[..@feature]
feature_id : 114
care : [*]
view : cntname
feature_type : acp
window_type : time
data_view : xxx

[.valid]
[..@feature]
feature_id : 115
care : [*]
view : cntname
feature_type : acp
window_type : time
data_view : price

[.valid]
[..@feature]
feature_id : 116
care : [*]
view : baiduid
feature_type : community
window_type : time
data_view : ip, ua

#click_hijack
[click_hijack]
[.@feature]
feature_id : 91300201
feature_type : click_hijack_dup_traffic
view_level : click_level
care : [*]
view : dispip,q,useragent
version : 1

#distinct_distribution
[distinct_dis]
[.invalid]
[..@feature]
feature_id : 0
care : [*]
view : userid

[..@feature]
feature_id : 1
care : [*]
view : userid
feature_type : distribution

[..@feature]
feature_id : 2
care : [*]
view : userid
feature_type : distinct_distribution

[..@feature]
feature_id : 3
care : [*]
view : userid
feature_type : distinct_distribution
window_type : pv
data_view : xxx

[..@feature]
feature_id : 4
care : [*]
view : userid
feature_type : distinct_distribution
window_type : pv
data_view : baiduid
cumulate_view : xxx

[.valid]
[..@feature]
feature_id : 4
care : [*]
view : userid
feature_type : distinct_distribution
window_type : pv
data_view : baiduid
cumulate_view : ua

[baiduid]
[.empty]
[..@feature]
feature_id   : 91181
view_level   : click_level
condition    : ['baiduid':='00000000000000000000000000000000']
feature_type : carespace
view         : baiduid

[..@feature]
feature_id   : 91181
view_level   : click_level
condition    : ['baiduid'NH]
feature_type : carespace
view         : baiduid

[.invalid]
[..@feature]
feature_id   : 91181
view_level   : click_level
condition    : ['baiduid':='FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF']
feature_type : carespace
view         : baiduid

[..@feature]
feature_id   : 91181
view_level   : click_level
condition    : ['baiduid'IV]
feature_type : carespace
view         : baiduid

[.valid]
[..@feature]
feature_id   : 91181
view_level   : click_level
condition    : ['baiduid'!='FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF']['baiduid'!='00000000000000000000000000000000']
feature_type : carespace
view         : baiduid

[..@feature]
feature_id   : 91181
view_level   : click_level
condition    : ['baiduid'VA]
feature_type : carespace
view         : baiduid

[behavior]
[.behavior_segment]
[..@feature]
feature_id : 888
care : [*]
view : userid
feature_type : behavior_segment
window_type : pv

[.behavior_ratio]
[..@feature]
feature_id : 999
care : [*]
view : userid
feature_type : behavior_ratio
window_type : pv
filter : [*]
refer  : [*] 

[.behavior_distribution]
[..@feature]
feature_id : 789
care : [*]
view : userid
feature_type : behavior_distribution
window_type : pv
data_view   : price
interval_endpoints : 10,100

[kpi]
[.invalid]
# miss data_view
[..@feature]
feature_id : 1234

[..@feature]
feature_id : 777
# find data view fail
data_view : xxxxxxxx

[..@feature]
feature_id : 663
care : [*]
view : userid
feature_type : rate
filter : [*]
refer : [*]
window_type : pv

[..@feature]
feature_id : 123786
care : [*]
view : userid
feature_type : rate_distribution
filter : [*]
refer : [*]
window_type : time

[.valid]
[..@feature]
feature_id : 663
care : [*]
view : userid
feature_type : rate
filter : [*]
refer : [*]
window_type : time

[..@feature]
feature_id : 222
care : [*]
view : userid
feature_type : rate_distribution
data_view : ip
filter : [*]
refer : [*]
window_type : time

[..@feature]
feature_id : 768
care : [*]
view : userid
feature_type : cpm
filter : [-]
refer : [*]
window_type : time

[..@feature]
feature_id : 432
care : [*]
view : userid
feature_type : cpm
price_view : price
filter : [*]
refer : [*]
window_type : time

[..@feature]
feature_id : 222
care : [*]
view : userid
feature_type : cpm_distribution
price_view : price
data_view : ip
filter : [*]
refer : [*]
window_type : time


