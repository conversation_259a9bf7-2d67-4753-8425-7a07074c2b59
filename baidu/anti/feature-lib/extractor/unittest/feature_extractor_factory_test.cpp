// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: feature_extractor_factory_test.cpp
// @Last modified: 2017-11-01 11:01:51
// @Brief: 

#include <gtest/gtest.h>
#include "feature_extractor_factory.h"
#include "segment_feature_extractor.h"
#include "ratio_feature_extractor.h"
#include "distribution_feature_extractor.h"
#include "dict_feature_extractor.h"
#include "carespace_feature_extractor.h"
#include "count_distribution_feature_extractor.h"
#include "extend_feature_extractor.h"

namespace anti {
namespace themis {
namespace feature_lib {

TEST(FeatureExtractorFactoryTestSuite, create_case) {
    EXPECT_TRUE(FeatureExtractorFactory::create("xxxx") == NULL);
    FeatureExtractorInterface* obj = NULL;

    obj = FeatureExtractorFactory::create("segment");
    EXPECT_TRUE(dynamic_cast<SegmentFeatureExtractor*>(obj) != NULL);
    delete obj;
    obj = NULL;

    obj = FeatureExtractorFactory::create("multi_seg");
    EXPECT_TRUE(dynamic_cast<SegmentFeatureExtractor*>(obj) != NULL);
    delete obj;
    obj = NULL;

    obj = FeatureExtractorFactory::create("auto_segment");
    EXPECT_TRUE(dynamic_cast<SegmentFeatureExtractor*>(obj) != NULL);
    delete obj;
    obj = NULL;

    obj = FeatureExtractorFactory::create("behavior_segment");
    EXPECT_TRUE(dynamic_cast<SegmentFeatureExtractor*>(obj) != NULL);
    delete obj;
    obj = NULL;

    obj = FeatureExtractorFactory::create("ratio");
    EXPECT_TRUE(dynamic_cast<RatioFeatureExtractor*>(obj) != NULL);
    delete obj;
    obj = NULL;

    obj = FeatureExtractorFactory::create("auto_ratio");
    EXPECT_TRUE(dynamic_cast<RatioFeatureExtractor*>(obj) != NULL);
    delete obj;
    obj = NULL;

    obj = FeatureExtractorFactory::create("behavior_ratio");
    EXPECT_TRUE(dynamic_cast<RatioFeatureExtractor*>(obj) != NULL);
    delete obj;
    obj = NULL;

    obj = FeatureExtractorFactory::create("rate");
    EXPECT_TRUE(dynamic_cast<RatioFeatureExtractor*>(obj) != NULL);
    delete obj;
    obj = NULL;

    obj = FeatureExtractorFactory::create("cpm");
    EXPECT_TRUE(dynamic_cast<RatioFeatureExtractor*>(obj) != NULL);
    delete obj;
    obj = NULL;

    obj = FeatureExtractorFactory::create("distribution");
    EXPECT_TRUE(dynamic_cast<DistributionFeatureExtractor*>(obj) != NULL);
    delete obj;
    obj = NULL;
    
    obj = FeatureExtractorFactory::create("behavior_distribution");
    EXPECT_TRUE(dynamic_cast<DistributionFeatureExtractor*>(obj) != NULL);
    delete obj;
    obj = NULL;

    obj = FeatureExtractorFactory::create("file_dict");
    EXPECT_TRUE(dynamic_cast<DictFeatureExtractor*>(obj) != NULL);
    delete obj;
    obj = NULL;

    obj = FeatureExtractorFactory::create("carespace");
    EXPECT_TRUE(dynamic_cast<CarespaceFeatureExtractor*>(obj) != NULL);
    delete obj;
    obj = NULL;

    obj = FeatureExtractorFactory::create("count_distribution");
    EXPECT_TRUE(dynamic_cast<CountDistributionFeatureExtractor*>(obj) != NULL);
    delete obj;
    obj = NULL;

    obj = FeatureExtractorFactory::create("rate_distribution");
    EXPECT_TRUE(dynamic_cast<RatioFeatureExtractor*>(obj) != NULL);
    delete obj;
    obj = NULL;

    obj = FeatureExtractorFactory::create("cpm_distribution");
    EXPECT_TRUE(dynamic_cast<RatioFeatureExtractor*>(obj) != NULL);
    delete obj;
    
    obj = FeatureExtractorFactory::create("fea_value_distribution");
    EXPECT_TRUE(dynamic_cast<ExtendFeatureExtractor*>(obj) != NULL);
    delete obj;
    obj = NULL;

    obj = FeatureExtractorFactory::create("fea_value_rate");
    EXPECT_TRUE(dynamic_cast<ExtendFeatureExtractor*>(obj) != NULL);
    delete obj;
    obj = NULL;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

