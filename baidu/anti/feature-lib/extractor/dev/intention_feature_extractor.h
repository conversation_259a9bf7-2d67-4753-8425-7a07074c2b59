// Copyright (c) 2021 Baidu.com, Inc. All Rights Reserved
// @Author: longfei(<EMAIL>)
// 
// @Brief: 

#pragma once

#include "feature_extractor_base.h"

namespace anti {
namespace themis {
namespace feature_lib {

class IntentionFeatureExtractor : public FeatureExtractorBase {
public:
    IntentionFeatureExtractor() : 
            _ftype(FeatureValueProto::INTENTION), 
            _stype(FeatureValueProto::TIME) {}
    virtual ~IntentionFeatureExtractor() {
        uninit();
    }

private:
    virtual bool _init(
            const comcfg::ConfigUnit& conf, 
            const std::vector<ViewDescriptor>& view_des);
    virtual void _uninit() {}

    virtual bool _extract(
            LogRecordInterface* log, 
            std::vector<std::shared_ptr<FeatureValueProto> >* feas);
 
    FeatureValueProto::FeatureType _ftype;
    FeatureValueProto::SegType _stype;
    std::vector<ViewDescriptor> _query_views;
    std::vector<ViewDescriptor> _extend_views;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

