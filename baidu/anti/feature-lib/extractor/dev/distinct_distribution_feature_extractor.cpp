// Copyright 2016 Baidu Inc. All Right Reserved.
// Author <PERSON> (<EMAIL>)
// 
// brief: 

#include "distinct_distribution_feature_extractor.h"

#include <boost/lexical_cast.hpp>
#include <boost/algorithm/string.hpp>
#include <com_log.h>
#include <view_value.pb.h>

namespace anti {
namespace themis {
namespace feature_lib {

using anti::themis::log_interface::ViewValueProto;

bool DistinctDistributionFeatureExtractor::_init(
        const comcfg::ConfigUnit& conf,
        const std::vector<ViewDescriptor>& view_des) {
    try {
        std::string fea_type(conf["feature_type"].to_cstr());
        if (fea_type == "distinct_distribution") {
            _ftype = FeatureValueProto::DISTINCT_DISTRIBUTION;
        } else if (fea_type == "three_level_tree") {
            _ftype = FeatureValueProto::THREE_LEVEL_TREE;
        } else {
            CWARNING_LOG("invalid feature_type(%s)", fea_type.data());
            return false;
        }
        std::string seg_type(conf["window_type"].to_cstr());
        if (seg_type == "pv") {
            _stype = FeatureValueProto::PV;
        } else if (seg_type == "time") {
            _stype = FeatureValueProto::TIME;
        } else {
            CWARNING_LOG("invalid window_type(%s)", seg_type.data());
            return false;
        }
        // combine view and data view
        _data_views.insert(_data_views.begin(), _views.begin(), _views.end());
        std::string data_view(conf["data_view"].to_cstr());
        if (!ExtractorTool::parse_view_des(data_view, view_des, &_data_views)) {
            CWARNING_LOG("call parse_view_des fail, data_view(%s)", data_view.data());
            return false;
        }
        // get cumulate_views
        std::string cumulate_view(conf["cumulate_view"].to_cstr());
        if (!ExtractorTool::parse_view_des(cumulate_view, view_des, &_cumulate_views)) {
            CWARNING_LOG("call parse_view_des fail, data_view(%s)", data_view.data());
            return false;
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std::Exception : %s", e.what());
        return false;
    }
    return true;
} 

bool DistinctDistributionFeatureExtractor::_extract(LogRecordInterface* log,
        std::vector<std::shared_ptr<FeatureValueProto> >* feas) {
    if (log == NULL || feas == NULL) {
        CFATAL_LOG("input log == NULL || feas == NULL");
        return false;
    }
    // get view sign
    ViewValueProto value;
    if (!log->get_view_sign(_views, &value)
            || value.values_size() != value.signs_size()) {
        CWARNING_LOG("call get data sign fail, logid(%lu)", log->log_id());
        return false;
    }
    // get data_views sign
    ViewValueProto data_value;
    if (!log->get_view_sign(_data_views, &data_value)
            || data_value.values_size() != data_value.signs_size()) {
        CWARNING_LOG("call get data_views sign fail, logid(%lu)", log->log_id());
        return false;
    }
    // view size must same as data_views size
    if (data_value.values_size() != value.values_size()) {
        CWARNING_LOG("data_value size[%lu] not equal to value size[%lu]", 
                data_value.values_size(), value.values_size());
        return false;
    }
    // get cumulate_views sign
    ViewValueProto cumulate_value;
    if (!log->get_view_sign(_cumulate_views, &cumulate_value)
            || cumulate_value.values_size() != cumulate_value.signs_size()) {
        CWARNING_LOG("call get cumulate_views sign fail, logid(%lu)", log->log_id());
        return false;
    }
    if (cumulate_value.signs_size() != 1) {
        CWARNING_LOG("cumulate_value sign_size(%lu) != 1, logid(%lu)", 
                cumulate_value.signs_size(), log->log_id());
        return false;
    }
    // set feature
    for (int i = 0; i < value.signs_size(); ++i) {
        std::shared_ptr<FeatureValueProto> fea(new(std::nothrow) FeatureValueProto());
        if (!fea) {
            CFATAL_LOG("new feature fail, feature_id(%lu)", feature_id());
            return false;
        }
        fea->set_feature_id(feature_id());
        fea->set_view_sign(value.signs(i));
        fea->set_view(value.values(i).str_value());
        fea->set_data_view_sign(data_value.signs(i));
        fea->set_data_view_value(data_value.values(i).str_value());
        fea->set_cumulate_view_sign(cumulate_value.signs(0));
        fea->set_cumulate_view_value(cumulate_value.values(0).str_value());
        fea->set_feature_type(_ftype);
        fea->set_joinkey(log->joinkey());
        fea->set_log_id(log->log_id());
        fea->set_log_time(log->log_time());
        fea->set_seg_type(_stype);
        if (_stype == FeatureValueProto::PV) {
            fea->set_coord(_pv_coord);
        } else {
            // ms -> s
            fea->set_coord(log->log_time() / 1000);
        }
        fea->set_pv_coord(_pv_coord);
        feas->push_back(fea);
    }
    return true;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti
