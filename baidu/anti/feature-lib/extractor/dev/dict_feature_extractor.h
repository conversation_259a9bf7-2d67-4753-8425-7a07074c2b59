// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: dict_feature_extractor.h
// @Last modified: 2015-06-04 16:31:53
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_DICT_FEATURE_EXTRACTOR_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_DICT_FEATURE_EXTRACTOR_H

#include "file_dict_manager.h"
#include "feature_extractor_base.h"
#include "multi_column_file_dict.h"

namespace anti {
namespace themis {
namespace feature_lib {

typedef common_lib::FileDictInterface FileDictInterface;
typedef log_interface::ViewValueProto ViewValueProto;

typedef bool (*DictDetectFunc)(const FileDictInterface*,
        const ViewValueProto&, const int32_t);
bool detect_multi_columns_dict(const FileDictInterface* dict,
        const ViewValueProto& value, const int32_t idx);
bool detect_ipmap_dict(const FileDictInterface* dict,
        const ViewValueProto& value, const int32_t idx);
bool detect_ip6map_dict(const FileDictInterface* dict,
        const ViewValueProto& value, const int32_t idx);

class DictFeatureExtractor : public FeatureExtractorBase {
public:
    DictFeatureExtractor() : _ftype(FeatureValueProto::FILE_DICT),
            _detect_func(NULL) {}
    virtual ~DictFeatureExtractor() {
        uninit();
    }

private:
    virtual bool _init(
            const comcfg::ConfigUnit& conf, 
            const std::vector<ViewDescriptor>& view_des);
    virtual void _uninit() {}

    virtual bool _extract(
            LogRecordInterface* log, 
            std::vector<std::shared_ptr<FeatureValueProto> >* feas);
 
    FeatureValueProto::FeatureType _ftype;
    common_lib::FileDictManager::ConstDictPtr _dict;
    DictDetectFunc _detect_func;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_DICT_FEATURE_EXTRACTOR_H

/* vim: set ts=4 sw=4: */

