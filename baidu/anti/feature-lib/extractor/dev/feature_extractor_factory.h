// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: feature_extractor_factory.h
// @Last modified: 2015-05-19 12:00:01
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_FEATURE_EXTRACTOR_FACTORY_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_FEATURE_EXTRACTOR_FACTORY_H

#include <string>
#include "feature_extractor_interface.h"

namespace anti {
namespace themis {
namespace feature_lib {

class FeatureExtractorFactory {
public:
    static FeatureExtractorInterface* create(const std::string& type);
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_FEATURE_EXTRACTOR_FACTORY_H

/* vim: set ts=4 sw=4: */

