// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: feature_extractor_base.cpp
// @Last modified: 2018-04-22 16:04:58
// @Brief: 

#include "feature_extractor_base.h"
#include <boost/algorithm/string.hpp>
#include <com_log.h>
#include <gflags/gflags.h>
#include <view_level_util.h>

namespace anti {
namespace themis {
namespace feature_lib {


using anti::themis::log_interface::ViewDescriptor;
using anti::themis::log_interface::ViewValueProto;
using anti::themis::log_parser_lib::ViewLevelUtil;

bool FeatureExtractorBase::init(
        const comcfg::ConfigUnit& conf, 
        const std::vector<ViewDescriptor>& view_des,
        CareExp* care_exp) {
    try {
        _feature_id = conf["feature_id"].to_uint64();
        _care_str = conf["care"].to_cstr();
        if (ExtractorTool::need_using_exp(_care_str)) {
            _need_using_exp = true;
            if (care_exp == NULL) {
                CFATAL_LOG("invalid input!");
                return false;
            }
            if (!care_exp->append(_care_str, view_des)) {
                CWARNING_LOG("call carespace init(%s) failed", _care_str.data());
                return false;
            }
        } else {
            _need_using_exp = false;
            if (!_care.init(_care_str, view_des)) {
                CWARNING_LOG("call carespace init(%s) failed", _care_str.data());
                return false;
            }
        }
        // if (!view_des.empty() && conf["view_level"].selfType() != comcfg::CONFIG_ERROR_TYPE)  {
        //     uint32_t view_level = ViewLevelUtil::get_view_level(
        //             view_des[0].log_type, conf["view_level"].to_cstr());
        //     if (_care.max_view_level() > view_level) {
        //         CFATAL_LOG("carespace's max view_level(%u) invalid, "
        //                 "should be smaller than extractor view_level(%u)",
        //                 _care.max_view_level(), view_level);
        //         return false;
        //     }
        // }
        std::string view_str(conf["view"].to_cstr());
        if (!ExtractorTool::parse_view_des(view_str, view_des, &_views)) {
            CWARNING_LOG("call parse_view_des fail, view(%s)", view_str.data());
            return false;
        }
    } catch (const comcfg::ConfigException& e) {    
        CWARNING_LOG("ConfigException : %s", e.what()); 
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std::Exception : %s", e.what());
        return false;
    }

    if (!_init(conf, view_des, care_exp)) {
        CWARNING_LOG("call _init fail, feature_id(%lu)", _feature_id);
        return false;
    }
    CWARNING_LOG("init feature extractor(%lu) success", _feature_id);
    return true;
}

void FeatureExtractorBase::uninit() {
    _views.clear();
    reset_pv_coord();
    _uninit();
}

bool FeatureExtractorBase::_parse_info(
        const std::string& str, 
        const std::vector<ViewDescriptor>& view_des,
        FeatureInfo* info) const {
    std::vector<std::string> info_vec;
    boost::split(info_vec, str, boost::is_any_of(", "), boost::token_compress_on);
    if (info == NULL || info_vec.size() != 2 || info_vec[0].size() < 1) {
        CWARNING_LOG("invalid value(%s)", str.c_str());
        return false;
    }
    const auto& name = info_vec[0];
    if (name.front() == '\'' && name.back() == '\'' && name.size() > 2) {
        info->type = MANUAL;
        info->str = name.substr(1, name.size() - 2);
    } else if (name.front() != '\'' && name.back() != '\'') {
        info->type = VIEW;
        info->str = name;
    } else {
        CWARNING_LOG("invalid value(%s)", str.c_str());
        return false;
    }
    if (info->type == VIEW) {
        auto tmp = ExtractorTool::parse_view_des(name, view_des);
        if (!tmp) {
            CWARNING_LOG("invalid view_name(%s)", name.c_str());
            return false;
        }
        info->view_des = *tmp;
    }
    info->dst = info_vec[1];
    return true;
}

bool FeatureExtractorBase::_get_info_value(
        LogRecordInterface* log,
        const FeatureInfo& info,
        std::vector<std::string>* values) const {
    if (!log || !values) {
        CFATAL_LOG("invalid args, log(%p), values(%s)", log, values);
        return false;
    }
    switch (info.type) {
    case MANUAL: {
        values->push_back(info.str);
        return true;
    }
    case VIEW: {
        ViewValueProto view_value;
        if (!log->get_view_value(info.view_des, &view_value)) {
            CWARNING_LOG("get_view_value fail, view(%s), log_id(%lu)",
                    info.str.c_str(), log->log_id());
            return false;
        }
        for (int32_t i = 0; i < view_value.values_size(); ++i) {
            values->push_back(view_value.values(i).str_value());
        }
        return true;
    }
    default:
        CWARNING_LOG("invalid feature info type(%u)", info.type);
        return false;

    }
}

bool FeatureExtractorBase::extract(
        const CareExp& care_exp,
        LogRecordInterface* log, 
        std::vector<std::shared_ptr<FeatureValueProto> >* feas) {
    if (log == NULL || feas == NULL) {
        CFATAL_LOG("input log == NULL || feas == NULL");
        return false;
    }
    
    bool result = false;
    if (_need_using_exp) {
        if (!care_exp.care(_care_str, &result)) {
            CWARNING_LOG("call carespace care fail, logid(%lu)", log->log_id());
            return false;
        }
    } else {
        if (!_care.care(log, &result)) {
            CWARNING_LOG("call carespace care fail, logid(%lu)", log->log_id());
            return false;
        }
    }
    if (!result) {
        // not care
        return true;
    }
    // update pv_coord when joinkey doesn't equal to last one;
    if (_last_joinkey != log->joinkey()) {
        ++_pv_coord;
        _last_joinkey = log->joinkey();
    }

    if (!_extract(care_exp, log, feas)) {
        CWARNING_LOG("call _extract fail, feature_id(%lu), logid(%lu)", 
                feature_id(), log->log_id());
        return false;
    }
    return true;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

