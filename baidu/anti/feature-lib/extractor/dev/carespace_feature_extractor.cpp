// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: carespace_feature_extractor.cpp
// @Last modified: 2018-04-20 20:15:01
// @Brief: 

#include "carespace_feature_extractor.h"
#include <com_log.h>
#include <view_value.pb.h>
#include <gflags/gflags.h>

namespace anti {
namespace themis {
namespace feature_lib {

using anti::themis::log_interface::ViewValueProto;

bool CarespaceFeatureExtractor::_init(
        const comcfg::ConfigUnit& conf, 
        const std::vector<ViewDescriptor>& view_des,
        CareExp* care_exp) {
    try {
        std::string fea_type(conf["feature_type"].to_cstr());
        if (fea_type == "carespace") {
            _ftype = FeatureValueProto::CARESPACE;
        } else {
            CWARNING_LOG("invalid feature_type(%s)", fea_type.data());
            return false;
        }

        char buf[1024] = {'\0'};
        if (conf["condition"].get_cstr(buf, sizeof(buf), "[*]") != 0) {
            CWARNING_LOG("get condition fail, using [*] instead");
        }
        _fea_care_str = buf;
        if (ExtractorTool::need_using_exp(_fea_care_str)) {
            _need_using_exp_fea = true;
            if (care_exp == NULL) {
                CFATAL_LOG("invalid input!");
                return false;
            }
            if (!care_exp->append(_fea_care_str, view_des)) {
                CWARNING_LOG("init carespace(%s) fail", _fea_care_str.data());
                return false;
            }
        } else {
            _need_using_exp_fea = false;
            if (!_fea_care.init(_fea_care_str, view_des)) {
                CWARNING_LOG("init carespace(%s) fail", _fea_care_str.data());
                return false;
            }
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std::Exception : %s", e.what());
        return false;
    }
    return true;
}

bool CarespaceFeatureExtractor::_extract(
        const CareExp& care_exp,
        LogRecordInterface* log, 
        std::vector<std::shared_ptr<FeatureValueProto> >* feas) {
    if (log == NULL || feas == NULL) {
        CFATAL_LOG("input log == NULL || feas == NULL");
        return false;
    }
    bool result = false;
    if (_need_using_exp_fea) {
        if (!care_exp.care(_fea_care_str, &result)) {
            CWARNING_LOG("call log(%lu) care fail", log->log_id());
            return false;
        }
    } else {
        if (!_fea_care.care(log, &result)) {
            CWARNING_LOG("call log(%lu) care fail", log->log_id());
            return false;
        }
    }

    ViewValueProto value;
    if (!log->get_view_sign(_views, &value) 
            || value.values_size() != value.signs_size()) {
        CWARNING_LOG("call get_view_sign logid(%lu)", log->log_id());
        return false;
    }

    for (int i = 0; i < value.signs_size(); ++i) {
        std::shared_ptr<FeatureValueProto> fea(new(std::nothrow) FeatureValueProto());
        if (!fea) {
            CFATAL_LOG("new feature fail, feature_id(%lu)", feature_id());
            return false;
        }
        fea->set_feature_id(feature_id());
        fea->set_view_sign(value.signs(i));
        fea->set_feature_type(_ftype);
        fea->set_joinkey(log->joinkey());
        fea->set_log_id(log->log_id());
        fea->set_log_time(log->log_time());
        fea->set_view(value.values(i).str_value());
        // ms -> s
        fea->set_coord(log->log_time() / 1000);
        fea->set_condition(result);
        fea->set_valid(true);
        // in carespace : 1
        // not in carespace : 0
        fea->set_value(result ? "1" : "0");
        feas->push_back(fea);
    }
    return true;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

