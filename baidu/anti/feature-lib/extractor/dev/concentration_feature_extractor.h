// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: lijie(<EMAIL>)
// 
//
#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_CONCENTRATION_FEATURE_EXTRACTOR_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_CONCENTRATION_FEATURE_EXTRACTOR_H

#include <view_value.pb.h>
#include "feature_extractor_base.h"

namespace anti {
namespace themis {
namespace feature_lib {

class ConcentrationFeatureExtractor : public FeatureExtractorBase {
public:
    ConcentrationFeatureExtractor() :
            _ftype(FeatureValueProto::CONCENTRATION), 
            _stype(FeatureValueProto::PV) {}
    virtual ~ConcentrationFeatureExtractor() {
        uninit();
    }

private:
    virtual bool _init(
            const comcfg::ConfigUnit& conf, 
            const std::vector<ViewDescriptor>& view_des);
    virtual void _uninit() {}

    virtual bool _extract(
            LogRecordInterface* log, 
            std::vector<std::shared_ptr<FeatureValueProto> >* feas);
    virtual std::shared_ptr<FeatureValueProto> _create_base_view(LogRecordInterface* log);

    FeatureValueProto::FeatureType _ftype;
    FeatureValueProto::SegType _stype;
    std::vector<ViewDescriptor> _data_views;
    bool _acc_main_view;
};

} // feature_lib
} // themis
} // anti

#endif // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_CONCENTRATION_FEATURE_EXTRACTOR_H
