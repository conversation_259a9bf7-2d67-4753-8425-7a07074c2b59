// Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
// @Author: linfan02
// 
// @Brief: 
//  feature with multiple values and views.
//  there's no need to accumulate this type of feature.

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_MULTI_VALUE_FEATURE_EXTRACTOR_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_MULTI_VALUE_FEATURE_EXTRACTOR_H

#include "feature_extractor_base.h"

namespace anti {
namespace themis {
namespace feature_lib {

class MultiValueFeatureExtractor : public FeatureExtractorBase {
public:
    MultiValueFeatureExtractor() : 
            _ftype(FeatureValueProto::MULTI_VALUE) {}
    virtual ~MultiValueFeatureExtractor() {
        uninit();
    }

private:
    virtual bool _init(
            const comcfg::ConfigUnit& conf, 
            const std::vector<ViewDescriptor>& view_des);
    virtual void _uninit() {}

    virtual bool _extract(
            LogRecordInterface* log, 
            std::vector<std::shared_ptr<FeatureValueProto> >* feas);
 
    FeatureValueProto::FeatureType _ftype;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_MULTI_VALUE_FEATURE_EXTRACTOR_H

/* vim: set ts=4 sw=4: */

