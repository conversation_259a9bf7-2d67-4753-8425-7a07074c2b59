/***************************************************************************
 * 
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
/**
 * @file extractor/dev/session_multi_view_feature_extractor.h
 * <AUTHOR>
 * @date 2017/06/08 15:26:56
 * @brief 
 *  
 **/

#ifndef  FEATURE_LIB_EXTRACTOR_DEV_SESSION_MULTI_VIEW_FEATURE_EXTRACTOR_H
#define  FEATURE_LIB_EXTRACTOR_DEV_SESSION_MULTI_VIEW_FEATURE_EXTRACTOR_H

#include "session_feature_extractor.h"

namespace anti {
namespace themis {
namespace feature_lib {

typedef anti::themis::log_interface::ViewDescriptor ViewDescriptor;
typedef anti::themis::log_interface::LogRecordInterface LogRecordInterface;
typedef anti::themis::log_parser_lib::CareExp CareExp;

class SessionExtendViewExtractor {
public:
    SessionExtendViewExtractor() : 
            _need_using_exp(true) {}
    ~SessionExtendViewExtractor() {}
    bool init(
            const comcfg::ConfigUnit& conf,
            const std::vector<ViewDescriptor>& view_des,
            CareExp* care_exp);
    bool extract_extend_view(
            const CareExp& care_exp,
            LogRecordInterface* log,
            FeatureValueProto* fea);

private:
    std::vector<ViewDescriptor> _views;
    std::string _care_str;
    CareSpace _care;
    bool _need_using_exp;
    bool _is_reliable;
    std::vector<std::pair<const ViewDescriptor, std::string> > _add_views;
};

typedef std::shared_ptr<SessionExtendViewExtractor> SessionExtendViewExtractorPtr;

class SessionMultiViewFeatureExtractor : public SessionFeatureExtractor {
public:
    SessionMultiViewFeatureExtractor() : 
            _need_using_exp_main(true) {}
    virtual ~SessionMultiViewFeatureExtractor() {
        _uninit();
    }
    virtual bool _init(
            const comcfg::ConfigUnit& conf, 
            const std::vector<ViewDescriptor>& view_des,
            CareExp* care_exp);
    virtual void _uninit() {}
    virtual bool _do_extract(
            const CareExp& care_exp,
            LogRecordInterface* log, 
            FeatureValueProto* feas,
            bool* valid);
    virtual bool _main_view_adjust(
            const CareExp& care_exp, 
            LogRecordInterface* log,
            FeatureValueProto* fea,
            bool* valid);

private:
    std::vector<SessionExtendViewExtractorPtr> _extend_views;
    std::string _main_care_str;
    bool _need_using_exp_main;
    CareSpace _main_care;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif
