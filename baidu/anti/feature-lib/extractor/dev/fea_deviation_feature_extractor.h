// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// <AUTHOR>
// @brief

#ifndef ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_FEA_DEVIATION_FEATURE_EXTRACTOR_H
#define ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_FEA_DEVIATION_FEATURE_EXTRACTOR_H

#include "feature_extractor_base.h"
#include "discretization_interface.h"

namespace anti {
namespace themis {
namespace feature_lib {

class FeaDeviationFeatureExtractor : public FeatureExtractorBase {
public:
    typedef anti::themis::log_interface::ViewValueProto ViewValueProto;
    FeaDeviationFeatureExtractor() :
            _open_accumulate_des(false),
            _ftype(FeatureValueProto::FEA_DEVIATION){}
    virtual ~FeaDeviationFeatureExtractor() {}

private:
    virtual bool _init(
            const comcfg::ConfigUnit& conf,
            const std::vector<ViewDescriptor>& view_des);

    virtual void _uninit() {}

    virtual bool _extract(
            LogRecordInterface* log,
            std::vector<std::shared_ptr<FeatureValueProto> >* feas);

    bool _init_acc(const comcfg::ConfigUnit& conf,
            const std::vector<ViewDescriptor>& view_des);

    bool _init_dis(const comcfg::ConfigUnit& conf);

    bool _find_view(
            const std::vector<ViewDescriptor>& view_des,
            const std::string& view_str,
            ViewDescriptor* out_des);

    bool _get_str_view(
            LogRecordInterface* log,
            const ViewDescriptor& des,
            std::string* value);

    bool _get_int_view(
            LogRecordInterface* log,
            const ViewDescriptor& des,
            int64_t* value);

    bool _get_int_value(
            const ViewValueProto& view_value,
            int64_t* value);

    bool _get_discretization_value(
            const std::string& str,
            int64_t* value);

    bool _open_accumulate_des;
    ViewDescriptor _view_name_des;
    ViewDescriptor _accumulate_des;
    ViewDescriptor _value_des;
    std::shared_ptr<DiscretizationInterface> _discretization;
    FeatureValueProto::FeatureType _ftype;
};

}
}
}
#endif // ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_FEA_DEVIATION_FEATURE_EXTRACTOR_H
