// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: feature_extractor_manager.cpp
// @Last modified: 2018-04-23 12:39:15
// @Brief: 

#include <algorithm>
#include <gflags/gflags.h>
#include "boost/algorithm/string.hpp"
#include "feature_extractor_manager.h"
#include <com_log.h>
#include <view_level_util.h>
#include "feature_extractor_factory.h"
#include "big_feature.h"
#include "feature_util.h"
#include <set>

using anti::themis::log_parser_lib::ViewLevelUtil;

namespace anti {
namespace themis {
namespace feature_lib {

DEFINE_bool(compatible_to_remora, false, "drop all features if one feature extract failed");

bool FeatureExtractorManager::init(
        const std::string& fea_conf_path,
        const std::string& fea_conf_file,
        const std::vector<ViewDescriptor>& view_des) {
    comcfg::Configure conf;
    if (conf.load(fea_conf_path.data(), fea_conf_file.data()) != 0) {
        CFATAL_LOG("load %s/%s failed", fea_conf_path.data(), fea_conf_file.data());
        return false;
    }
    if (!_init_care_tags(conf, view_des, &_care_exps)) {
        CFATAL_LOG("init care tag failed!");
        return false;
    }
    try {
        std::string care_str(conf["global_care"].to_cstr());
        if (ExtractorTool::need_using_exp(care_str)) {
            CWARNING_LOG("global care using exp ");
            _global_care_str = care_str;
            _need_using_exp_global = true;
            if (!_globle_care_exp.append(care_str, view_des)) {
                CWARNING_LOG("init global care(%s) fail", care_str.data());
                return false;
            }
            _globle_view_level = _globle_care_exp.max_view_level();
        } else {
            _need_using_exp_global = false;
            CWARNING_LOG(" global care using carespace");
            if (!_global_care.init(care_str, view_des)) {
                    CWARNING_LOG("init global care(%s) fail", care_str.data());
                    return false;
            }
            _globle_view_level = _global_care.max_view_level();
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("comcfg::ConfigException : %s", e.what());
        return false;
    }
    if (!_init_exts(conf, view_des, &_care_exps)) {
        CWARNING_LOG("call _init_exts fail");
        return false;
    }
    CWARNING_LOG("init feature extractor manager success");
    return true;
}

bool FeatureExtractorManager::_init_care_tags(
        const comcfg::ConfigUnit& conf, 
        const std::vector<ViewDescriptor>& view_des,
        std::vector<std::shared_ptr<CareExp>>* care_exps) {
    if (care_exps == NULL) {
        CFATAL_LOG("invalid input!");
        return false;
    }
    for (uint32_t i = 0; i < MAX_LEVEL; ++i) {
        std::shared_ptr<CareExp> ptr(new (std::nothrow) CareExp());
        if (!ptr) {
            CWARNING_LOG("new CareExp failed!");
            return false;
        }
        care_exps->push_back(ptr);
    }
    auto& pv_care_exps = care_exps->at(0);
    auto& src_care_exps = care_exps->at(1);
    auto& ad_care_exps = care_exps->at(2);
    for (uint32_t i = 0; i < conf["care_tag"].size(); ++i) {
        std::string care_tag(conf["care_tag"][i].to_cstr());
        for (auto& care_exp : *care_exps) {
            if (!care_exp->append(care_tag, view_des)) {
                CWARNING_LOG("care_exp append care_tag[%s] failed!", care_tag.c_str());
                return false;
            }
        }
    }
    for (uint32_t i = 0; i < conf["pv_care_tag"].size(); ++i) {
        std::string care_tag(conf["pv_care_tag"][i].to_cstr());
        if (!pv_care_exps->append(care_tag, view_des)) {
            CWARNING_LOG("pv_care_exp append care_tag[%s] failed!", care_tag.c_str());
            return false;
        }
    }
    for (uint32_t i = 0; i < conf["src_care_tag"].size(); ++i) {
        std::string care_tag(conf["src_care_tag"][i].to_cstr());
        if (!src_care_exps->append(care_tag, view_des)) {
            CWARNING_LOG("src_care_exp append care_tag[%s] failed!", care_tag.c_str());
            return false;
        }
    }
    for (uint32_t i = 0; i < conf["ad_care_tag"].size(); ++i) {
        std::string care_tag(conf["ad_care_tag"][i].to_cstr());
        if (!ad_care_exps->append(care_tag, view_des)) {
            CWARNING_LOG("pv_care_exp append care_tag[%s] failed!", care_tag.c_str());
            return false;
        }
    }
    return true;
}

bool FeatureExtractorManager::_init_exts(
        const comcfg::ConfigUnit& conf,
        const std::vector<ViewDescriptor>& view_des,
        std::vector<std::shared_ptr<CareExp>>* care_exps) {
    if (view_des.size() == 0U) {
        CFATAL_LOG("input view_des size is zero");
        return false;
    }
    try {
        std::vector<std::string> feature_list;
        if (conf["feature_list"].selfType() != comcfg::CONFIG_ERROR_TYPE 
                && !parse_list(std::string(conf["feature_list"].to_cstr()), &feature_list)) {
            CFATAL_LOG("call parse_list(%s) fail", conf["feature_list"].to_cstr());
            return false;
        }
        
        if (conf["fea_list"].selfType() != comcfg::CONFIG_ERROR_TYPE 
                && !parse_list(conf, std::string("fea_list"), &feature_list)) {
            CFATAL_LOG("call parse_list(fea_list) fail");
            return false;
        }

        if (feature_list.size() == 0) {
            CFATAL_LOG("no feature_list or fea_list in conf");
            return false;
        }
        const std::string& log_type = view_des[0].log_type;
        uint32_t ext_num = conf["feature"].size();
        std::set<std::string> uniq_feaid_set;
        for (uint32_t i = 0U; i < ext_num; ++i) {
            std::string feature_id(conf["feature"][i]["feature_id"].to_cstr());
            std::vector<std::string>::iterator feature_ite = std::find(
                    feature_list.begin(),
                    feature_list.end(),
                    feature_id);
            if (feature_ite == feature_list.end()) {
                CWARNING_LOG("feature_id[%s] is NOT in policy_list or already exists", 
                        feature_id.c_str());
                continue;
            }
            // feature unique
            if (uniq_feaid_set.find(feature_id) != uniq_feaid_set.end()) {
                CFATAL_LOG("feature_id[%s] has been define", feature_id.c_str());
                continue;
            }
            uniq_feaid_set.insert(feature_id);
            std::string vl(conf["feature"][i]["view_level"].to_cstr());
            uint32_t view_level = ViewLevelUtil::get_view_level(log_type, vl);
            if (view_level == 0U || view_level > MAX_LEVEL) {
                CWARNING_LOG("get_view_level(%s, %s) fail, ret view_level(%u)",
                        log_type.data(), vl.data(), view_level);
                return false;
            }
            std::string fea_type(conf["feature"][i]["feature_type"].to_cstr());
            FeatureExtractorInterface* ext = FeatureExtractorFactory::create(fea_type);
            CareExp* care_ptr = _care_exps[view_level - 1].get();
            if (ext == NULL || !ext->init(conf["feature"][i], view_des, care_ptr)) {
                CWARNING_LOG("new or init feature_type(%s) fail", fea_type.data());
                if (ext != NULL) {
                    delete ext;
                    ext = NULL;
                }
                return false;
            }
            _exts[view_level - 1U].push_back(ext);
        }
    } catch (const comcfg::ConfigException& e) {
            CWARNING_LOG("comcfg::ConfigException : %s", e.what());
            return false;
    }
    return true;
}

void FeatureExtractorManager::uninit() {
    for (uint32_t i = 0U; i < MAX_LEVEL; ++i) {
        std::vector<FeatureExtractorInterface*>& exts = _exts[i];
        for (size_t j = 0U; j < exts.size(); ++j) {
            if (exts[j] != NULL) {
                delete exts[j];
                exts[j] = NULL;
            }
        }
        exts.clear();
        _care_exps.clear();
        _global_care.uninit();
    }
    _globle_view_level = 0U;
}

bool FeatureExtractorManager::_care_global(LogRecordInterface* log, bool *result) {
    if (!log->reset_log_level(_globle_view_level)) {
        CWARNING_LOG("call reset_log_level(%u) fail, logid(%lu)", 
                _globle_view_level, log->log_id());
        return false;
    }
    while (log->has_next()) {
        bool hit_care = false;
        if (!_need_using_exp_global) {
            if (!_global_care.care(log, &hit_care)) {
                CWARNING_LOG("call _global_care care fail, logid(%lu)", log->log_id());
                return false;
            }
        } else {
            if (!_globle_care_exp.reset(log)) {
                CWARNING_LOG("reset globle care failed! cur level[%u]", _globle_view_level);
                return false;
            }
            if (!_globle_care_exp.care(_global_care_str, &hit_care)) {
                CWARNING_LOG("call _global_care care fail, logid(%lu)", log->log_id());
                return false;
            }
        }
        if (hit_care) {
            *result = true;
            return true;
        }

        if (!log->next()) {
            CWARNING_LOG("call log next fail, logid(%lu)", log->log_id());
            return false;
        }
    }
    return true;
}

bool FeatureExtractorManager::extract(
        LogRecordInterface* log,
        std::vector<std::shared_ptr<FeatureValueProto> >* feas) {
    if (log == NULL || feas == NULL) {
        CFATAL_LOG("input log == NULL || feas == NULL");
        return false;
    }
    
    bool result = false;
    if (!_care_global(log, &result)) {
        CWARNING_LOG("call global care fail, logid(%lu)", log->log_id());
        return false;
    }
    if (!result) {
        return true;
    }
    // TODO(fail extract num >= threshold return false)
    for (uint32_t i = 0U; i < MAX_LEVEL; ++i) {
        uint32_t log_level = i + 1U;
        if (_exts[i].size() == 0U) {
            continue;
        }
        if (!log->reset_log_level(log_level)) {
            CWARNING_LOG("call reset_log_level(%u) fail, logid(%lu)", 
                    log_level, log->log_id());
            // reset log level fail not return false;
            continue;
        }
        if (!_ext(_care_exps[i], &_exts[i], log, feas)) {
            CWARNING_LOG("call _ext_by_level(%u) fail", i + 1);
            // ext by log level fail not return false
            // go on to ext by other level
            if (FLAGS_compatible_to_remora) { return false; }
            continue;
        }
    }

    return true;
}

bool FeatureExtractorManager::_ext(
        std::shared_ptr<CareExp> care_exp_ptr,
        std::vector<FeatureExtractorInterface*>* exts,
        LogRecordInterface* log,
        std::vector<std::shared_ptr<FeatureValueProto> >* feas) {
    while (log->has_next()) {
        if (!care_exp_ptr->reset(log)) {
            CFATAL_LOG("care_exp reset log failed!");
            return false;
        }
        for (size_t j = 0U; j < exts->size(); ++j) {
            if ((*exts)[j] == NULL || !(*exts)[j]->extract(*care_exp_ptr, log, feas)) {
                CWARNING_LOG("_exts[%lu] is NULL or call extract fail, logid(%lu)", 
                        j, log->log_id());
                if (FLAGS_compatible_to_remora) { return false; }
            }
        }
        if (!log->next()) {
            CWARNING_LOG("call log next fail, logid(%lu)", log->log_id());
            return false;
        }
    }
    return true;
}

bool FeatureExtractorManager::get_pv_coords(
        std::vector<std::pair<uint64_t, int64_t> >* coords) const {
    if (coords == NULL) {
        CFATAL_LOG("input coords is NULL");
        return false;
    }

    for (uint32_t i = 0U; i < MAX_LEVEL; ++i) {
        const std::vector<FeatureExtractorInterface*>& exts = _exts[i];
        for (size_t j = 0U; j < exts.size(); ++j) {
            if (exts[j] == NULL) {
                CFATAL_LOG("_exts[%u][%lu] is NULL, check code", i, j);
                return false;
            }
            coords->push_back(std::pair<uint64_t, int64_t>(
                    exts[j]->feature_id(), exts[j]->pv_coord() + 1));
        }
    }
    return true;
}
    
bool FeatureExtractorManager::reset_pv_coords() {
    for (uint32_t i = 0U; i < MAX_LEVEL; ++i) {
        std::vector<FeatureExtractorInterface*>& exts = _exts[i];
        for (size_t j = 0U; j < exts.size(); ++j) {
            if (exts[j] == NULL) {
                CFATAL_LOG("_exts[%u][%lu] is NULL, check code", i, j);
                return false;
            }
            exts[j]->reset_pv_coord();
        }
    }
    return true;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

