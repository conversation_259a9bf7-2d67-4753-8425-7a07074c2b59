// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: lijie(<EMAIL>)
// @Last Modify time: 2018-05-29 21:30:45 
//
#include "concentration_feature_extractor.h"

#include <boost/lexical_cast.hpp>
#include <boost/algorithm/string.hpp>
#include <com_log.h>
#include <view_value.pb.h>

using anti::themis::log_interface::ViewValueProto;
namespace anti {
namespace themis {
namespace feature_lib {

bool ConcentrationFeatureExtractor::_init(
        const comcfg::ConfigUnit& conf, 
        const std::vector<ViewDescriptor>& view_des) {
    try {
        std::string fea_type(conf["feature_type"].to_cstr());
        if (fea_type == "concentration") {
            _ftype = FeatureValueProto::CONCENTRATION;
        } else if (fea_type == "distinct") {
            _ftype = FeatureValueProto::DISTINCT;
        } else if (fea_type == "sum_segment") {
            _ftype = FeatureValueProto::SUM_SEGMENT;
        } else if (fea_type == "acp" || fea_type == "community") {
            _ftype = FeatureValueProto::ACP;
        } else if (fea_type == "query_similar") {
            _ftype = FeatureValueProto::QUERY_SIMILAR;
        } else if (fea_type == "time_interval") {
            _ftype = FeatureValueProto::TIME_INTERVAL;
        } else {
            CWARNING_LOG("invalid feature_type(%s)", fea_type.data());
            return false;
        }
        std::string seg_type(conf["window_type"].to_cstr());
        if (seg_type == "pv") {
            _stype = FeatureValueProto::PV;
        } else if (seg_type == "time") {
            _stype = FeatureValueProto::TIME;
        } else {
            CWARNING_LOG("invalid window_type(%s)", seg_type.data());
            return false;
        }
        std::string data_view(conf["data_view"].to_cstr());
        if (!ExtractorTool::parse_view_des(data_view, view_des, &_data_views)) {
            CWARNING_LOG("call parse_view_des fail, data_view(%s)", data_view.data());
            return false;
        }
        const uint32_t DEFAULT_ACC_MAIN_VIEW = 0;
        uint32_t acc_main_view = DEFAULT_ACC_MAIN_VIEW;
        conf["acc_main_view"].get_uint32(&acc_main_view, DEFAULT_ACC_MAIN_VIEW);
        _acc_main_view = acc_main_view != DEFAULT_ACC_MAIN_VIEW;
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std::Exception : %s", e.what());
        return false;
    }
    return true;
}

bool ConcentrationFeatureExtractor::_extract(LogRecordInterface* log,
        std::vector<std::shared_ptr<FeatureValueProto> >* feas) {
    if (log == NULL || feas == NULL) {
        CFATAL_LOG("input log == NULL || feas == NULL");
        return false;
    }
    // get data_views sign
    ViewValueProto data_value;
    if (!log->get_view_sign(_data_views, &data_value)
            || data_value.values_size() != data_value.signs_size()) {
        CWARNING_LOG("call get data_views sign fail, logid(%lu)", log->log_id());
        return false;
    }
    // get view sign
    ViewValueProto value;
    if (!log->get_view_sign(_views, &value)
            || value.values_size() != value.signs_size()) {
        CWARNING_LOG("call get data sign fail, logid(%lu)", log->log_id());
        return false;
    }
    // set feature
    for (int i = 0; i < value.signs_size(); ++i) {
        if (_ftype == FeatureValueProto::CONCENTRATION && _acc_main_view) {
            auto fea = _create_base_view(log);
            if (!fea) {
                CWARNING_LOG("create feature fail, feature_id(%lu)", feature_id());
                return false;
            }
            fea->set_view_sign(value.signs(i));
            fea->set_view(value.values(i).str_value());
            feas->push_back(fea);
        }
        for (int j = 0; j < data_value.signs_size(); ++j) {
            auto fea = _create_base_view(log);
            if (!fea) {
                CWARNING_LOG("create feature fail, feature_id(%lu)", feature_id());
                return false;
            }
            fea->set_view_sign(value.signs(i));
            fea->set_view(value.values(i).str_value());
            fea->set_data_view_sign(data_value.signs(j));
            fea->set_data_view_value(data_value.values(j).str_value());
            if (_ftype == FeatureValueProto::ACP) {
                fea->mutable_acp_field()->set_price(data_value.values(0).int_value());
            }
            feas->push_back(fea);
        }
    }
    return true;
}

std::shared_ptr<FeatureValueProto> ConcentrationFeatureExtractor::_create_base_view(
        LogRecordInterface *log) {
    if (log == NULL) {
        CFATAL_LOG("input log is NULL");
        return std::shared_ptr<FeatureValueProto>();
    }
    std::shared_ptr<FeatureValueProto> fea(new (std::nothrow) FeatureValueProto());
    if (!fea) {
       CFATAL_LOG("new feature fail, feature_id(%lu)", feature_id());
       return std::shared_ptr<FeatureValueProto>();
    }
    fea->set_feature_id(feature_id());
    fea->set_feature_type(_ftype);
    fea->set_joinkey(log->joinkey());
    fea->set_log_id(log->log_id());
    fea->set_log_time(log->log_time());
    fea->set_seg_type(_stype);
    if (_stype == FeatureValueProto::PV) {
        fea->set_coord(_pv_coord);
    } else {
        // ms -> s
        fea->set_coord(log->log_time() / 1000);
    }
    fea->set_pv_coord(_pv_coord);
    return fea;    
} 

} // feature_lib
} // themis
} // anti
