// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: extend_feature_extractor.cpp
// @Last modified: 2018-05-27 18:29:02
// @Brief: 

#include "extend_feature_extractor.h"
#include <com_log.h>
#include <pb_reflector.h>

using anti::themis::common_lib::PbReflector;

namespace anti {
namespace themis {
namespace feature_lib {

bool ExtendFeatureExtractor::_init(
        const comcfg::ConfigUnit& conf, 
        const std::vector<ViewDescriptor>& view_des) {
    try {
        std::string seg_type(conf["window_type"].to_cstr());
        if (seg_type == "pv") {
            _stype = FeatureValueProto::PV;
        } else if (seg_type == "time") {
            _stype = FeatureValueProto::TIME;
        } else {
            CWARNING_LOG("invalid window_type(%s)", seg_type.data());
            return false;
        }

        if (conf["extend"].size() == 0) {
            CWARNING_LOG("no extend config found");
            return false;
        }
        for (uint32_t i = 0; i < conf["extend"].size(); ++i) {
            ExtendInfo info;
            info.feature_id = conf["extend"][i]["feature_id"].to_uint64();
            std::string view_str(conf["extend"][i]["view"].to_cstr()); 
            if (!ExtractorTool::parse_view_des(view_str, view_des, &(info.views))) {
                CWARNING_LOG("pare view_str(%s) fail", view_str.data());
                return false;
            }
            for (uint32_t j = 0; j < conf["extend"][i]["einfo"].size(); ++j) {
                FeatureInfo finfo;
                if (!_parse_info(conf["extend"][i]["einfo"][j].to_cstr(), view_des, &finfo)) {
                    CWARNING_LOG("parse FeatureInfo(%s) failed",
                            conf["extend"][i]["einfo"][j].to_cstr());
                    return false;
                }
                info.finfos.push_back(finfo);
            }
            _infos.push_back(info);
        }
        for (const auto& info : _infos) {
            CDEBUG_LOG("info id[%lu]", info.feature_id);
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std::Exception : %s", e.what());
        return false;
    }
    return true;
}

void ExtendFeatureExtractor::_uninit() {
    _infos.clear();
}

bool ExtendFeatureExtractor::_extract(
        LogRecordInterface* log, 
        std::vector<std::shared_ptr<FeatureValueProto> >* feas) {
    if (!log || !feas) {
        CFATAL_LOG("input log or feas ptr invalid");
        return false;
    }   
    ViewValueProto value;
    if (!log->get_view_sign(_views, &value)
            || value.values_size() != value.signs_size()) {
        CWARNING_LOG("call get_view_sign fail, logid(%lu)", log->log_id());
        return false;
    }
    for (int i = 0; i < value.signs_size(); ++i) {
        std::shared_ptr<FeatureValueProto> fea(new (std::nothrow) FeatureValueProto);
        if (!fea) {
            CFATAL_LOG("new FeatureValueProto obj fail, feature_id(%lu)", feature_id());
            return false;
        }
        fea->set_feature_id(feature_id());
        fea->set_view_sign(value.signs(i));
        fea->set_feature_type(FeatureValueProto::FEATURE_VALUE_EXTEND);
        fea->set_joinkey(log->joinkey());
        fea->set_log_id(log->log_id());
        fea->set_log_time(log->log_time());
        fea->set_view(value.values(i).str_value());
        fea->set_seg_type(_stype);
        if (_stype == FeatureValueProto::PV) {
            fea->set_coord(_pv_coord);
        } else {
            // ms -> s
            fea->set_coord(log->log_time() / 1000);
        }   
        auto fea_trans = fea->mutable_fea_serv_trans();
        if (!_extract_fea_trans(log, fea_trans)) {
            CWARNING_LOG("extract fea_serv_trans field fail, logid(%lu)", log->log_id());
            return false;
        }
        
        feas->push_back(fea);
    };
    return true;
}

bool ExtendFeatureExtractor::_extract_fea_trans(
        LogRecordInterface* log,
        FValQueryTransProto* fea_trans) {
    if (!fea_trans || !log) {
        CFATAL_LOG("input fea_trans or log ptr is NULL");
        return false;
    }
    for (const auto& info : _infos) {
        ViewValueProto value;
        if (!log->get_view_sign(info.views, &value)
                || value.values_size() != value.signs_size()) {
            CWARNING_LOG("call get_view_sign fail, logid(%lu)", log->log_id());
            return false;
        }
        for (const auto& sign : value.signs()) {
            auto fqp = fea_trans->add_infos();
            if (!fqp) {
                CWARNING_LOG("add FValQInfoProto fail, feature_id(%lu)", feature_id());
                return false;
            }
            auto request = fqp->mutable_request();
            if (!request) {
                CWARNING_LOG("add FValQRequestProto fail, feature_id(%lu)", feature_id());
                return false;
            }
            request->set_feature_id(info.feature_id);
            request->set_view_sign(sign);

            for (auto& finfo : info.finfos) {
                std::vector<std::string> values;
                if (!_get_info_value(log, finfo, &values)) {
                    CWARNING_LOG("get info value fail");
                    return false;
                }
                if (!PbReflector::set_field_val(request, finfo.dst, values)) {
                    CWARNING_LOG("failed to set base field, key(%s)", finfo.dst.c_str());
                    return false;
                }
            }
        }
    }
    return true;
}

} // namespace feature_lib
} // namespace themis
} // namespace anti
/* vim: set ts=4 sw=4: */

