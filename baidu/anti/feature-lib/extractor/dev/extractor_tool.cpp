// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: feature_extractor_base.cpp
// @Last modified: 2018-04-22 16:04:58
// @Brief: 

#include <boost/algorithm/string.hpp>
#include <com_log.h>
#include "extractor_tool.h"

namespace anti {
namespace themis {
namespace feature_lib {

typedef anti::themis::log_interface::ViewDescriptor ViewDescriptor;

const ViewDescriptor* ExtractorTool::parse_view_des(    
        const std::string& view_str,    
        const std::vector<ViewDescriptor>& all_views) { 
    for (uint32_t i = 0; i < all_views.size(); ++i) {   
        if (all_views[i].view_name == view_str) {   
            return &all_views[i];   
        }   
    }   
    return NULL;    
}

bool ExtractorTool::parse_view_des(
        const std::string& view_str,
        const std::vector<ViewDescriptor>& all_views,
        std::vector<ViewDescriptor>* target_views) {
    std::vector<std::string> views;
    boost::split(views, view_str, boost::is_any_of(", "), boost::token_compress_on);
    for (uint32_t i = 0; i < views.size(); ++i) {
        const std::string& vi = views[i];
        std::vector<ViewDescriptor>::const_iterator iter = 
                std::find_if(all_views.begin(), all_views.end(),
                        [&vi](const ViewDescriptor& v)->bool { 
                                return vi == v.view_name;
                        });
        if (iter == all_views.end()) {
            CWARNING_LOG("find view(%s) fail", views[i].data());
            return false;
        }
        target_views->push_back(*iter);
    }
    return true;
}

bool ExtractorTool::need_using_exp(
    const std::string& care_str) {
    if (care_str.find("&&") != std::string::npos
            || care_str.find("(") != std::string::npos
            || care_str.find(")") != std::string::npos) {
        return true; 
    } 
    return false; 
}

}
}
}
