// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: segment_feature_extractor.h
// @Last modified: 2015-05-19 12:00:21
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_SEGMENT_FEATURE_EXTRACTOR_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_SEGMENT_FEATURE_EXTRACTOR_H

#include "feature_extractor_base.h"

namespace anti {
namespace themis {
namespace feature_lib {

class SegmentFeatureExtractor : public FeatureExtractorBase {
public:
    SegmentFeatureExtractor() : 
            _ftype(FeatureValueProto::SEGMENT), 
            _stype(FeatureValueProto::PV) {}
    virtual ~SegmentFeatureExtractor() {
        uninit();
    }

private:
    virtual bool _init(
            const comcfg::ConfigUnit& conf, 
            const std::vector<ViewDescriptor>& view_des);
    virtual void _uninit() {}

    virtual bool _extract(
            LogRecordInterface* log, 
            std::vector<std::shared_ptr<FeatureValueProto> >* feas);
 
    FeatureValueProto::FeatureType _ftype;
    FeatureValueProto::SegType _stype;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_SEGMENT_FEATURE_EXTRACTOR_H

/* vim: set ts=4 sw=4: */

