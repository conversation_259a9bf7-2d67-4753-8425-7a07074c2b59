// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @brief 

#include <algorithm>
#include <com_log.h>
#include <view_value.pb.h>
#include "hijack_feature_extractor.h"

namespace anti {
namespace themis {
namespace feature_lib {

typedef std::shared_ptr<FeatureValueProto> FeaturePtr;
typedef log_interface::ViewValueProto ViewValueProto;

static const std::string kUniqCnView = "unique_cn";
static const std::string kFlowGroupView = "flow_group";
static const std::string kPageNoView = "pn";

bool find_view_descriptor(
        const std::vector<log_interface::ViewDescriptor>& views,
        const std::string& target_name,
        log_interface::ViewDescriptor* ret) {
    for (auto& obj : views) {
        if (obj.view_name == target_name) {
            *ret = obj;
            return true;
        }
    }
    return false;
}

HijackFeatureExtractor::HijackFeatureExtractor() : 
        _ftype(FeatureValueProto::TIME_DIFF),
        _stype(FeatureValueProto::TIME) {}
HijackFeatureExtractor::~HijackFeatureExtractor() {}

bool HijackFeatureExtractor::_init(const comcfg::ConfigUnit& conf,
        const std::vector<ViewDescriptor>& view_des) {
    try {
        std::string fea_type(conf["feature_type"].to_cstr());
        if (fea_type == "hijack") {
            _ftype = FeatureValueProto::HIJACK;
        } else {
            CWARNING_LOG("invalid feature_type(%s)", fea_type.data());
            return false;
        }

        std::string seg_type(conf["window_type"].to_cstr());
        if (seg_type == "pv") {
            _stype = FeatureValueProto::PV;
        } else if (seg_type == "time") {
            _stype = FeatureValueProto::TIME;
        } else {
            CWARNING_LOG("invalid window_type(%s)", seg_type.data());
            return false;
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std::Exception : %s", e.what());
        return false;
    }

    if (!find_view_descriptor(view_des, kUniqCnView, &_uniq_cn_des)
            ||!find_view_descriptor(view_des, kFlowGroupView, &_flow_group_des)
            ||!find_view_descriptor(view_des, kPageNoView, &_page_no_des)) {
        CWARNING_LOG("find uniq_cn/flow_group/page_no view descriptor failed");
        return false;
    }

    return true;
}

void HijackFeatureExtractor::_uninit() { }

bool HijackFeatureExtractor::_extract(LogRecordInterface* log,
        std::vector<FeaturePtr>* feas) {
    if (log == NULL || feas == NULL) {
        CFATAL_LOG("invalid input args. [log:%p,feat:%p]", log, feas);
        return false;
    }

    ViewValueProto value;
    if (!log->get_view_sign(_views, &value)
            || value.values_size() != value.signs_size()) {
        CWARNING_LOG("get_view_sign failed. [logid:%lu]", log->log_id());
        return false;
    }

    FeatureValueProto::HijackProto hijack_val;
    if (!get_hijack_field(log, &hijack_val)) {
        CWARNING_LOG("get hijack field failed");
        return false;
    }

    for (int i = 0; i < value.signs_size(); ++i) {
        FeaturePtr fea(new (std::nothrow) FeatureValueProto());
        if (!fea) {
            CFATAL_LOG("alloc feature failed");
            return false;
        }

        fea->set_feature_id(feature_id());
        fea->set_view_sign(value.signs(i));
        fea->set_feature_type(_ftype);
        fea->set_joinkey(log->joinkey());
        fea->set_log_id(log->log_id());
        fea->set_log_time(log->log_time());
        fea->set_view(value.values(i).str_value());
        fea->set_seg_type(_stype);
        if (_stype == FeatureValueProto::PV) {
            fea->set_coord(_pv_coord);
        } else {
            // ms -> s
            fea->set_coord(log->log_time() / 1000);
        }
        fea->set_pv_coord(_pv_coord);
        fea->mutable_original_hijack_field()->CopyFrom(hijack_val);

        feas->push_back(fea);
    }
    return true;
}

bool HijackFeatureExtractor::get_hijack_field(LogRecordInterface* log,
        FeatureValueProto::HijackProto* hijack_field) {
    ViewValueProto uniq_cn;
    if (!log->get_view_value(_uniq_cn_des, &uniq_cn)) {
        CWARNING_LOG("get uniq_cn failed");
        return false;
    }
    ViewValueProto flow_group;
    if (!log->get_view_value(_flow_group_des, &flow_group)) {
        CWARNING_LOG("get flow_group failed");
        return false;
    }
    ViewValueProto page_no;
    if (!log->get_view_value(_page_no_des, &page_no)) {
        CWARNING_LOG("get page_no failed");
        return false;
    }
    if (uniq_cn.values_size() != 1 || 
            flow_group.values_size() != 1 || 
            page_no.values_size() != 1) {
        CWARNING_LOG("error unqi_cn/flow_group/page_no size. \
                [cn:%d, fgroup:%d, page_no:%d]",
                uniq_cn.values_size(), 
                flow_group.values_size(),
                page_no.values_size());
        return false;
    }
    if (page_no.values(0).int_value() < 0) {
        CWARNING_LOG("error page_no is negative:[page_no:%ld]", 
                page_no.values(0).int_value());
        return false;
    }
    hijack_field->set_charge_name(uniq_cn.values(0).str_value());
    hijack_field->set_flow_group(flow_group.values(0).str_value());
    hijack_field->set_page_no(uint32_t(page_no.values(0).int_value()));
    return true;
}

}
}
}
