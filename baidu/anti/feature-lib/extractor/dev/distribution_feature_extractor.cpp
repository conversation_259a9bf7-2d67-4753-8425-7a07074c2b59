// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: distribution_feature_extractor.cpp
// @Last modified: 2016-10-21 09:54:13
// @Brief: 

#include "distribution_feature_extractor.h"
#include <boost/lexical_cast.hpp>
#include <boost/algorithm/string.hpp>
#include <com_log.h>
#include <view_value.pb.h>

using anti::themis::log_interface::ViewValueProto;

namespace anti {
namespace themis {
namespace feature_lib {

bool DistributionFeatureExtractor::_init(
        const comcfg::ConfigUnit& conf, 
        const std::vector<ViewDescriptor>& view_des) {
    try {
        std::string fea_type(conf["feature_type"].to_cstr());
        if (fea_type == "distribution") {
            _ftype = FeatureValueProto::DISTRIBUTION;
        } else if (fea_type == "behavior_distribution") {
            _ftype = FeatureValueProto::BEHAVIOR_DISTRIBUTION;
        } else {
            CWARNING_LOG("invalid feature_type(%s)", fea_type.data());
            return false;
        }

        std::string seg_type(conf["window_type"].to_cstr());
        if (seg_type == "pv") {
            _stype = FeatureValueProto::PV;
        } else if (seg_type == "time") {
            _stype = FeatureValueProto::TIME;
        } else {
            CWARNING_LOG("invalid window_type(%s)", seg_type.data());
            return false;
        }

        std::string data_view(conf["data_view"].to_cstr());
        std::vector<ViewDescriptor>::const_iterator iter = 
                std::find_if(view_des.begin(), view_des.end(),
                        [&data_view](const ViewDescriptor& v)->bool { 
                            return data_view == v.view_name;
                        });
        if (iter == view_des.end()) {
            CWARNING_LOG("find data_view(%s) fail", data_view.data());
            return false;
        }
        _data_view = *iter;

        std::string points(conf["interval_endpoints"].to_cstr());
        if (!_init_intervals(points)) {
            CWARNING_LOG("call _init_intervals(%s) fail", points.data());
            return false;
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std::Exception : %s", e.what());
        return false;
    }
    return true;
}

bool DistributionFeatureExtractor::_init_intervals(const std::string& points) {
    std::vector<std::string> point_strs;
    boost::algorithm::split(point_strs, points, boost::is_any_of(", "), boost::token_compress_on);
    int64_t start = LLONG_MIN;
    for (size_t i = 0U; i < point_strs.size(); ++i) {
        try {
            int64_t end = boost::lexical_cast<int64_t>(point_strs[i]);
            _intervals.push_back(std::pair<int64_t, int64_t>(start, end));
            start = end;
        } catch (const boost::bad_lexical_cast& e) {
            CWARNING_LOG("boost::Exception : %s", e.what());
            return false;
        }
    }
    _intervals.push_back(std::pair<int64_t, int64_t>(start, LLONG_MAX));
    return true;
}

bool DistributionFeatureExtractor::_extract(
        LogRecordInterface* log, 
        std::vector<std::shared_ptr<FeatureValueProto> >* feas) {
    if (log == NULL || feas == NULL) {
        CFATAL_LOG("input log == NULL || feas == NULL");
        return false;
    }
    ViewValueProto data_value;
    if (!log->get_view_value(_data_view, &data_value)
            || data_value.values_size() != 1U) {
        CWARNING_LOG("call get view value fail, logid(%lu)", log->log_id());
        return false;
    }

    uint32_t idx = UINT_MAX;
    if (!_get_interval_idx(data_value, &idx)) {
        CWARNING_LOG("call _get_interval_idx fail, logid(%lu)", log->log_id());
        return false;
    }

    ViewValueProto value;
    if (!log->get_view_sign(_views, &value)
            || value.values_size() != value.signs_size()) {
        CWARNING_LOG("call get_view_sign fail, logid(%lu)", log->log_id());
        return false;
    }

    for (int i = 0; i < value.signs_size(); ++i) {
        std::shared_ptr<FeatureValueProto> fea(new(std::nothrow) FeatureValueProto());
        if (!fea) {
            CFATAL_LOG("new feature fail, feature_id(%lu)", feature_id());
            return false;
        }
        fea->set_feature_id(feature_id());
        fea->set_view_sign(value.signs(i));
        fea->set_feature_type(_ftype);
        fea->set_joinkey(log->joinkey());
        fea->set_log_id(log->log_id());
        fea->set_log_time(log->log_time());
        fea->set_view(value.values(i).str_value());
        fea->set_seg_type(_stype);
        if (_stype == FeatureValueProto::PV) {
            fea->set_coord(_pv_coord);
        } else {
            // ms -> s
            fea->set_coord(log->log_time() / 1000);
        }
        fea->set_bucket_idx(idx);
        fea->set_data_view_value(data_value.values(0).str_value());
        feas->push_back(fea);
    }
    return true;
}

bool DistributionFeatureExtractor::_get_interval_idx(
        const ViewValueProto& value, 
        uint32_t* idx) {
    if (value.values_size() != 1U || idx == NULL) {
        CFATAL_LOG("input value.size() != 1 || idx == NULL");
        return false;
    }
    int64_t coord = 0;
    switch (value.type()) {
    case ViewValueProto::UINT32:
    case ViewValueProto::INT32:
    case ViewValueProto::UINT64:
    case ViewValueProto::INT64: {
        coord = value.values(0).int_value();
        break;
    }
    case ViewValueProto::STRING: {
        try {
            coord = boost::lexical_cast<int64_t>(value.values(0).str_value());
            break;
        } catch (const boost::bad_lexical_cast& e) {
            CWARNING_LOG("boost::Exception: %s", e.what());
            return false;
        }
    }
    default:
        CWARNING_LOG("invalid value type(%d)", value.type());
        return false;
    }

    int32_t start = 0;
    int32_t end = _intervals.size() - 1;
    while (start <= end) {
        int32_t mid = start + (end - start) / 2;
        if (_intervals[mid].first < coord && coord <= _intervals[mid].second) {
            *idx = static_cast<uint32_t>(mid);
            return true;
        }
        if (coord <= _intervals[mid].first) {
            end = mid - 1;
        } else {
            start = mid + 1;
        }
    }
    CWARNING_LOG("find interval fail, call init first");
    return false;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

