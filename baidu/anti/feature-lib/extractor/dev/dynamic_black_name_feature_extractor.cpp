// Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
// @Author: tanweiliang(<EMAIL>)
// 
// @File: dynamic_black_name.cpp
// @Last modified: 2022-09-02 11:21:00
// @Brief: dynamic black name feature extractor SL

#include <gflags/gflags.h>
#include <com_log.h>
#include <view_value.pb.h>
#include <boost/lexical_cast.hpp>
#include <boost/algorithm/string.hpp>
#include "dynamic_black_name_feature_extractor.h"

using anti::themis::log_interface::ViewValueProto;

namespace anti {
namespace themis {
namespace feature_lib {

bool DynamicBlackNameFeatureExtractor::_init(
        const comcfg::ConfigUnit& conf, 
        const std::vector<ViewDescriptor>& view_des) {
    try {
        std::string fea_type(conf["feature_type"].to_cstr());
        if (fea_type == "dynamic_black_name") {
            _ftype = FeatureValueProto::DYNAMIC_BLACK_NAME;
        } else if (fea_type == "anti_service_judge") {
            _ftype = FeatureValueProto::ANTI_SERVICE_JUDGE;
        } else {
            CWARNING_LOG("invalid feature_type(%s)", fea_type.data());
            return false;
        }
        std::string black_hit_feaid(conf["black_hit_feaid"].to_cstr());
        std::vector<std::string> raw_feaid;
        if (black_hit_feaid != "") {
            boost::split(raw_feaid, black_hit_feaid, boost::is_any_of(", "), boost::token_compress_on);
            for (int i = 0; i < raw_feaid.size(); i++) {
                _black_hit_feaid.push_back(boost::lexical_cast<uint64_t>(raw_feaid[i]));
            }
        } else {
            CWARNING_LOG("get related_black_hit_feaid from conf failed");
            return false;
        }
    
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    } catch (const boost::bad_lexical_cast & e) {
        CWARNING_LOG("std::Exception : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std::Exception : %s", e.what());
        return false;
    }
    return true;
}

bool DynamicBlackNameFeatureExtractor::_extract(
        LogRecordInterface* log, 
        std::vector<std::shared_ptr<FeatureValueProto> >* feas) {
    if (log == NULL || feas == NULL) {
        CFATAL_LOG("input log == NULL || feas == NULL");
        return false;
    }
    ViewValueProto value;
    if (!log->get_view_sign(_views, &value) 
            || value.values_size() != value.signs_size()) {
        CWARNING_LOG("call get_view_sign logid(%lu)", log->log_id());
        return false;
    }

    for (int i = 0; i < value.signs_size(); ++i) {

        std::shared_ptr<FeatureValueProto> fea(new(std::nothrow) FeatureValueProto());
        if (!fea) {
            CFATAL_LOG("new feature fail, feature_id(%lu)", feature_id());
            return false;
        }
        fea->set_feature_id(feature_id());
        fea->set_view_sign(value.signs(i));
        fea->set_feature_type(_ftype);
        fea->set_joinkey(log->joinkey());
        fea->set_log_id(log->log_id());
        fea->set_log_time(log->log_time());
        fea->set_view(value.values(i).str_value());
        // ms -> s
        fea->set_coord(log->log_time() / 1000);
        // default not in redis, use view_sign to query redis
        fea->set_condition(false);
        fea->set_valid(true);
        fea->set_value("0");
        // set related redis black name fea ids
        for (int i = 0; i < _black_hit_feaid.size(); i++) {
            fea->add_black_hit_feaids(_black_hit_feaid[i]);
        }

        feas->push_back(fea);
    }
    return true;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

