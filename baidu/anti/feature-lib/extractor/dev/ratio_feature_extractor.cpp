// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: ratio_feature_extractor.cpp
// @Last modified: 2018-04-20 20:13:22
// @Brief: 

#include "ratio_feature_extractor.h"
#include <com_log.h>
#include <view_value.pb.h>
#include <gflags/gflags.h>

namespace anti {
namespace themis {
namespace feature_lib {

using anti::themis::log_interface::ViewValueProto;

bool RatioFeatureExtractor::_init(
        const comcfg::ConfigUnit& conf, 
        const std::vector<ViewDescriptor>& view_des,
        CareExp* care_exp) {
    try {
        std::string fea_type(conf["feature_type"].to_cstr());
        if (fea_type == "ratio") {
            _ftype = FeatureValueProto::RATIO;
        } else if (fea_type == "auto_ratio") {
            _ftype = FeatureValueProto::AUTO_RATIO;
        } else if (fea_type == "behavior_ratio") {
            _ftype = FeatureValueProto::BEHAVIOR_RATIO;
        } else if (fea_type == "rate") {
            _ftype = FeatureValueProto::RATE;
        } else if (fea_type == "cpm") {
            _ftype = FeatureValueProto::CPM;
        } else if (fea_type == "rate_distribution") {
            _ftype = FeatureValueProto::RATE_DISTRIBUTION;
        } else if (fea_type == "cpm_distribution") {
            _ftype = FeatureValueProto::CPM_DISTRIBUTION;
        } else {
            CWARNING_LOG("invalid feature_type(%s)", fea_type.data());
            return false;
        }

        if ((_ftype == FeatureValueProto::CPM || _ftype == FeatureValueProto::CPM_DISTRIBUTION)
                && !_init_price_view(conf, view_des)) {
            CWARNING_LOG("init price view fail. Please ignore, if in asp extractor");
        }

        std::string seg_type(conf["window_type"].to_cstr());
        if (seg_type == "pv" 
                && _ftype != FeatureValueProto::RATE 
                && _ftype != FeatureValueProto::CPM) {
            _stype = FeatureValueProto::PV;
        } else if (seg_type == "time") {
            _stype = FeatureValueProto::TIME;
        } else {
            CWARNING_LOG("invalid window_type(%s)", seg_type.data());
            return false;
        }
        _filter_str = conf["filter"].to_cstr();
        _refer_str = conf["refer"].to_cstr();
        if (ExtractorTool::need_using_exp(_filter_str)) {
            _need_using_exp_filter = true;
            if (care_exp == NULL) {
                CFATAL_LOG("invalid input!");
                return false;
            }
            if (!care_exp->append(_filter_str, view_des)) {
                CWARNING_LOG("call filter init(%s) fail", _filter_str.data());
                return false;
            }
        } else {
            _need_using_exp_filter = false;
            if (!_filter.init(_filter_str, view_des)) {
                CWARNING_LOG("call filter init(%s) fail", _filter_str.data());
                return false;
            }
        }
        if (ExtractorTool::need_using_exp(_refer_str)) {
            _need_using_exp_refer = true;
            if (!care_exp->append(_refer_str, view_des)) {
                CWARNING_LOG("call refer init(%s) fail", _refer_str.data());
                return false;
            }
        } else {
            _need_using_exp_refer = false;
            if (!_refer.init(_refer_str, view_des)) {
                CWARNING_LOG("call refer init(%s) fail", _refer_str.data());
                return false;
            }
        }
        if (_ftype == FeatureValueProto::CPM_DISTRIBUTION
                || _ftype == FeatureValueProto::RATE_DISTRIBUTION) {
            // combine view and data_view inside
            _data_views.insert(_data_views.begin(), _views.begin(), _views.end());
            std::string data_view(conf["data_view"].to_cstr());
            if (!ExtractorTool::parse_view_des(data_view, view_des, &_data_views)) {
                CWARNING_LOG("call parse_view_des fail, data_view(%s)", data_view.data());
                return false;
            }
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std::Exception : %s", e.what());
        return false;
    }

    const int32_t DEFAULT_CHECK_MODE = 0;
    int32_t check_mode = 0;
    if (conf["weak_check"].get_int32(&check_mode, DEFAULT_CHECK_MODE)) {
        CWARNING_LOG("weak check close default");
    }
    if (check_mode != 0 && check_mode != 1) {
        CWARNING_LOG("illegal weak check[weak_check=%d], "
                "should be 0(close) or 1(open)", check_mode);
        return false;
    }
    _weak_check = (check_mode == 0 ? false : true);
    return true;
}

bool RatioFeatureExtractor::_init_price_view(
        const comcfg::ConfigUnit& conf,
        const std::vector<ViewDescriptor>& view_des) {
    try {
        std::string data_view(conf["price_view"].to_cstr());
        const ViewDescriptor* vd = ExtractorTool::parse_view_des(data_view, view_des);
        if (vd == NULL) {
            CWARNING_LOG("no data view. Please ignore, if in asp extractor");
            return false;
        }
        _price.reset(new(std::nothrow) ViewDescriptor(*vd));
        if (!_price) {
            CWARNING_LOG("call new ViewDescriptor failed");
            return false;
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std::Exception : %s", e.what());
        return false;
    }

    return true;
}

bool RatioFeatureExtractor::_extract(
        const CareExp& care_exp,
        LogRecordInterface* log, 
        std::vector<std::shared_ptr<FeatureValueProto> >* feas) {
    if (log == NULL || feas == NULL) {
        CFATAL_LOG("input log == NULL || feas == NULL");
        return false;
    }

    bool in_filter = false;
    bool in_refer = false;
    if (_need_using_exp_filter) {
        if (!care_exp.care(_filter_str, &in_filter)) {
            CWARNING_LOG("call _filter care fail, logid(%lu)", log->log_id());
            return false;
        }
    } else {
        if (!_filter.care(log, &in_filter)) {
            CWARNING_LOG("call _filter care fail, logid(%lu)", log->log_id());
            return false;
        }
    }
    if (_need_using_exp_refer) {
        if (!care_exp.care(_refer_str, &in_refer)) {
            CWARNING_LOG("call _refer care fail, logid(%lu)", log->log_id());
            return false;
        }
    } else {
        if (!_refer.care(log, &in_refer)) {
            CWARNING_LOG("call _refer care fail, logid(%lu)", log->log_id());
            return false;
        }
    }
    if (!in_filter && !in_refer && !_weak_check) {
        return true;
    }

    // get price for cpm or cpm distribution
    int64_t filter_count = in_filter ? 1L : 0L;
    if (in_filter && _price) {
        ViewValueProto p;
        if (!log->get_view_value(*_price, &p) 
                || p.values_size() != 1
                || !p.values(0).has_int_value()) {
            CWARNING_LOG("get data view fail");
            return false;
        }
        filter_count = p.values(0).int_value();
    }

    // get data_view signs for cpm distribution and rate distribution 
    ViewValueProto dv;
    if (_data_views.size() > 0U && !log->get_view_sign(_data_views, &dv)) {
        CWARNING_LOG("call get data_views sign fail, logid(%lu)", log->log_id());
        return false;
    }
   
    ViewValueProto value;
    if (!log->get_view_sign(_views, &value)
            || value.values_size() != value.signs_size()) {
        CWARNING_LOG("call get_view_sign logid(%lu)", log->log_id());
        return false;
    }

    for (int i = 0; i < value.signs_size(); ++i) {
        std::shared_ptr<FeatureValueProto> fea(new(std::nothrow) FeatureValueProto());
        if (!fea) {
            CFATAL_LOG("new feature fail, feature_id(%lu)", feature_id());
            return false;
        }
        fea->set_feature_id(feature_id());
        fea->set_view_sign(value.signs(i));
        fea->set_feature_type(_ftype);
        fea->set_joinkey(log->joinkey());
        fea->set_log_id(log->log_id());
        fea->set_log_time(log->log_time());
        fea->set_view(value.values(i).str_value());
        fea->set_seg_type(_stype);
        if (_stype == FeatureValueProto::PV) {
            fea->set_coord(_pv_coord);
        } else {
            // ms -> s
            fea->set_coord(log->log_time() / 1000);
        }
        fea->set_in_filter(in_filter);
        fea->set_filter_count(filter_count);
        fea->set_in_refer(in_refer);
        fea->set_pv_coord(_pv_coord);

        if (dv.values_size() > 0 && dv.signs_size() > 0) {
            fea->set_data_view_value(dv.values(0).str_value());
            fea->set_data_view_sign(dv.signs(0));
        }
        feas->push_back(fea);
    }
    return true;
}
    
}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

