// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: houruijie
// 
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_ORIGINAL_FEATURE_EXTRACTOR_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_ORIGINAL_FEATURE_EXTRACTOR_H

#include "feature_extractor_base.h"

namespace anti {
namespace themis {
namespace feature_lib {

class OriginalFeatureExtractor : public FeatureExtractorBase {
public:
    OriginalFeatureExtractor() : 
            _ftype(FeatureValueProto::ORIGINAL) {}
    virtual ~OriginalFeatureExtractor() {
        uninit();
    }

private:
    virtual bool _init(
            const comcfg::ConfigUnit& conf, 
            const std::vector<ViewDescriptor>& view_des);
    virtual void _uninit() {}

    virtual bool _extract(
            LogRecordInterface* log, 
            std::vector<std::shared_ptr<FeatureValueProto> >* feas);
 
    FeatureValueProto::FeatureType _ftype;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_ORIGINAL_FEATURE_EXTRACTOR_H

/* vim: set ts=4 sw=4: */

