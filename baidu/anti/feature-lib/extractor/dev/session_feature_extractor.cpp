// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// <AUTHOR>
// @brief 

#include "session_feature_extractor.h"
#include "pb_reflector.h"
#include "com_log.h"

using anti::themis::log_interface::ViewDescriptor;
using anti::themis::log_interface::ViewValueProto;
using anti::themis::common_lib::PbReflector;

namespace anti {
namespace themis {
namespace feature_lib {

bool SessionFeatureExtractor::_init(
        const comcfg::ConfigUnit& conf, 
        const std::vector<ViewDescriptor>& view_des,
        CareExp* care_exp) {
    try {
        for (uint32_t i = 0; i < conf["feature_info"].size(); ++i) {
            FeatureInfo info;
            if (!_parse_info(conf["feature_info"][i].to_cstr(), view_des, &info)) {
                CWARNING_LOG("call parser info (%s) failed", conf["feature_info"][i].to_cstr());
                return false;
            }
            _fea_infos.push_back(info);
        }
        return true;
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    }
}

bool SessionFeatureExtractor::_extract(
        const CareExp& care_exp,
        LogRecordInterface* log,
        std::vector<std::shared_ptr<FeatureValueProto> >* feas) {
    if (log == NULL || feas == NULL) {
        CFATAL_LOG("input log == NULL || feas == NULL");
        return false;
    }

    ViewValueProto value;
    if (!log->get_view_sign(_views, &value) 
            || value.values_size() != value.signs_size()) {
        CWARNING_LOG("get_view_sign for feature(%lu), logid(%lu) fail",
                feature_id(), log->log_id());
        return false;
    }

    for (int i = 0; i < value.signs_size(); ++i) {
        std::shared_ptr<FeatureValueProto> fea(new(std::nothrow) FeatureValueProto());
        if (!fea) {
            CFATAL_LOG("new feature fail, feature_id(%lu)", feature_id());
            return false;
        }
        fea->set_feature_id(feature_id());
        fea->set_view_sign(value.signs(i));
        fea->set_feature_type(FeatureValueProto::SESSION);
        fea->set_joinkey(log->joinkey());
        fea->set_log_id(log->log_id());
        fea->set_log_time(log->log_time());
        fea->set_view(value.values(i).str_value());
        fea->set_coord(0);

        auto key = fea->mutable_session()->add_keys();
        key->set_view(value.signs(i));
        key->set_reliable(true);
        auto base = fea->mutable_session()->mutable_base_action();
        base->set_log_time(log->log_time());
        base->set_log_id(log->log_id());
        // ToDo, set real log type
        base->set_log_type(log->log_type());

        for (const auto& info : _fea_infos) {
            std::vector<std::string> values;
            if (!_get_info_value(log, info, &values)) {
                CWARNING_LOG("get info value fail");
                return false;
            }
            if (!PbReflector::set_field_val(base, info.dst, values)) {
                CWARNING_LOG("failed to set base field, key(%s)",
                        info.dst.c_str());
                return false;
            }
        }
        bool valid = true;
        if (!_do_extract(care_exp, log, fea.get(), &valid)) {
            CWARNING_LOG("call do_extract fail");
            return false;
        }
        if (valid) {
            feas->push_back(fea);
        }

    }
    return true;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti
