// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: carespace_feature_extractor.h
// @Last modified: 2018-04-20 10:07:07
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_CARESPACE_FEATURE_EXTRACTOR_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_CARESPACE_FEATURE_EXTRACTOR_H

#include "feature_extractor_base.h"
#include "care_space.h"

namespace anti {
namespace themis {
namespace feature_lib {

class CarespaceFeatureExtractor : public FeatureExtractorBase {
public:
    CarespaceFeatureExtractor() : 
            _ftype(FeatureValueProto::CARESPACE), 
            _need_using_exp_fea(true) {}
    virtual ~CarespaceFeatureExtractor() {
        uninit();
    }

private:
    virtual bool _init(
            const comcfg::ConfigUnit& conf, 
            const std::vector<ViewDescriptor>& view_des,
            CareExp* care_exp);
    virtual void _uninit() {}

    virtual bool _extract(
            const CareExp& care_exp,
            LogRecordInterface* log, 
            std::vector<std::shared_ptr<FeatureValueProto> >* feas);
 
    FeatureValueProto::FeatureType _ftype;
    std::string _fea_care_str;
    CareSpace _fea_care;
    bool _need_using_exp_fea;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_CARESPACE_FEATURE_EXTRACTOR_H

/* vim: set ts=4 sw=4: */

