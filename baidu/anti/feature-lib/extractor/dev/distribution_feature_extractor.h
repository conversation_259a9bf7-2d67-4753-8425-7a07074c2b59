// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: distribution_feature_extractor.h
// @Last modified: 2015-05-19 11:59:40
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_DISTRIBUTION_FEATURE_EXTRACTOR_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_DISTRIBUTION_FEATURE_EXTRACTOR_H

#include <view_value.pb.h>
#include "feature_extractor_base.h"

namespace anti {
namespace themis {
namespace feature_lib {

class DistributionFeatureExtractor : public FeatureExtractorBase {
public:
    DistributionFeatureExtractor() :
            _ftype(FeatureValueProto::DISTRIBUTION), 
            _stype(FeatureValueProto::PV) {}
    virtual ~DistributionFeatureExtractor() {
        uninit();
    }

private:
    virtual bool _init(
            const comcfg::ConfigUnit& conf, 
            const std::vector<ViewDescriptor>& view_des);
    bool _init_intervals(const std::string& points);

    virtual void _uninit() {
        _intervals.clear();
    }

    virtual bool _extract(
            LogRecordInterface* log, 
            std::vector<std::shared_ptr<FeatureValueProto> >* feas);
    typedef anti::themis::log_interface::ViewValueProto ViewValueProto;
    bool _get_interval_idx(const ViewValueProto& value, uint32_t* idx);
 
    FeatureValueProto::FeatureType _ftype;
    FeatureValueProto::SegType _stype;
    ViewDescriptor _data_view;
    std::vector<std::pair<int64_t, int64_t> > _intervals;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_DISTRIBUTION_FEATURE_EXTRACTOR_H

/* vim: set ts=4 sw=4: */

