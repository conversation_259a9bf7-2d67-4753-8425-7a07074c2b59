// Copyright 2016 Baidu Inc. All Right Reserved.
// Author <PERSON> (<EMAIL>)
// 
// brief: 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_DISTINCT_DISTRIBUTION_FEATURE_EXTRACTOR_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_DISTINCT_DISTRIBUTION_FEATURE_EXTRACTOR_H

#include <view_value.pb.h>
#include "feature_extractor_base.h"

namespace anti {
namespace themis {
namespace feature_lib {

class DistinctDistributionFeatureExtractor : public FeatureExtractorBase {
public:
    DistinctDistributionFeatureExtractor() :
            _ftype(FeatureValueProto::DISTINCT_DISTRIBUTION), 
            _stype(FeatureValueProto::PV) {}
    virtual ~DistinctDistributionFeatureExtractor() {
        uninit();
    }

private:
    virtual bool _init(
            const comcfg::ConfigUni<PERSON>& conf, 
            const std::vector<ViewDescriptor>& view_des);
    virtual void _uninit() {
        _data_views.clear();
        _cumulate_views.clear();
    }

    virtual bool _extract(
            LogRecordInterface* log, 
            std::vector<std::shared_ptr<FeatureValueProto> >* feas);

    FeatureValueProto::FeatureType _ftype;
    FeatureValueProto::SegType _stype;
    std::vector<ViewDescriptor> _data_views;
    std::vector<ViewDescriptor> _cumulate_views;
};

} // feature_lib
} // themis
} // anti

#endif // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_DISTINCT_DISTRIBUTION_FEATURE_EXTRACTOR_H
/* vim: set ts=4 sw=4: */

