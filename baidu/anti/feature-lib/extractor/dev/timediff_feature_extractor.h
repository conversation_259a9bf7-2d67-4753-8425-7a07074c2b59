// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @brief 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_TIMEDIFF_FEATURE_EXTRACTOR_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_TIMEDIFF_FEATURE_EXTRACTOR_H

#include <memory>
#include "feature_extractor_base.h"

namespace anti {
namespace themis {
namespace feature_lib {

class TDExtractor : public FeatureExtractorBase {
public:
    TDExtractor();
    virtual ~TDExtractor();

private:
    virtual bool _init(const comcfg::ConfigUnit& conf,
            const std::vector<ViewDescriptor>& view_des);
    virtual void _uninit();

    virtual bool _extract(LogRecordInterface* log,
            std::vector<std::shared_ptr<FeatureValueProto>>* feas);

private:
    FeatureValueProto::FeatureType _ftype;
    FeatureValueProto::SegType _stype;
    std::vector<ViewDescriptor> _data_views;
};

}
}
}

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_TIMEDIFF_FEATURE_EXTRACTOR_H
