// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: extend_feature_extractor.h
// @Last modified: 2017-12-19 14:19:01
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_EXTEND_FEATURE_EXTRACTOR_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_EXTEND_FEATURE_EXTRACTOR_H

#include <unordered_map>
#include "feature_extractor_base.h"

namespace anti {
namespace themis {
namespace feature_lib {

class ExtendFeatureExtractor : public FeatureExtractorBase {
public:
    ExtendFeatureExtractor() {}
    virtual ~ExtendFeatureExtractor() {}

private:
    virtual bool _init(
            const comcfg::ConfigUnit& conf, 
            const std::vector<ViewDescriptor>& view_des);
    virtual void _uninit();
    typedef anti::themis::log_interface::ViewValueProto ViewValueProto;
    virtual bool _extract(
            LogRecordInterface* log, 
            std::vector<std::shared_ptr<FeatureValueProto> >* feas);

    bool _extract_fea_trans(
            LogRecordInterface* log, 
            FValQueryTransProto* fea_trans);
    
    struct ExtendInfo {
        uint64_t feature_id;
        std::vector<ViewDescriptor> views;
        std::vector<FeatureInfo> finfos;
    };
    std::vector<ExtendInfo> _infos;
    FeatureValueProto::SegType _stype;
};

} // namespace feature_lib
} // namespace themis
} // namespace anti
#endif // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_EXTEND_FEATURE_EXTRACTOR_H
/* vim: set ts=4 sw=4: */

