/***************************************************************************
 * 
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/

#include "session_multi_view_feature_extractor.h"
#include <com_log.h>
#include <boost/algorithm/string.hpp>
#include "pb_reflector.h"
#include <gflags/gflags.h>

namespace anti {
namespace themis {
namespace feature_lib {


using anti::themis::log_interface::ViewValueProto;
using anti::themis::common_lib::PbReflector;

bool SessionExtendViewExtractor::init(
        const comcfg::ConfigUnit& conf,
        const std::vector<ViewDescriptor>& view_des,
        CareExp* care_exp) {
    try {
        _care_str = conf["care"].to_cstr();
        if (ExtractorTool::need_using_exp(_care_str)) {
            _need_using_exp = true; 
            if (care_exp == NULL) {
                CFATAL_LOG("invalid input!");
                return false;
            }
            if (!care_exp->append(_care_str, view_des)) {
                CWARNING_LOG("care init from str(%s) fail", conf["care"].to_cstr());
                return false;
            }
        } else {
            _need_using_exp = false; 
            if (!_care.init(conf["care"].to_cstr(), view_des)) {
                CWARNING_LOG("care init from str(%s) fail", conf["care"].to_cstr());
                return false;
            }
        }
        std::string view_str = conf["view"].to_cstr();
        if (!ExtractorTool::parse_view_des(view_str, view_des, &_views)) {
            CWARNING_LOG("parse view_str(%s) fail", view_str.c_str());
            return false;
        }
        if (conf["is_reliable"].selfType() == comcfg::CONFIG_ERROR_TYPE) {
            _is_reliable = true;
        } else {
            _is_reliable = conf["is_reliable"].to_int32() == 0 ? false : true;
        }
        for (uint32_t i = 0; i < conf["add_info"].size(); ++i) {
            std::vector<std::string> add_info_vec;
            std::string add_info_str = conf["add_info"][i].to_cstr();
            boost::split(add_info_vec,
                    add_info_str,
                    boost::is_any_of(", "),
                    boost::token_compress_on);
            if (add_info_vec.size() != 2) {
                CWARNING_LOG("invalid add_info(%s)", add_info_str.c_str());
                return false;
            }
            auto add_view_des = ExtractorTool::parse_view_des(add_info_vec[0], view_des);
            if (add_view_des == NULL) {
                CWARNING_LOG("find view(%s) fail", add_info_vec[0].c_str());
                return false;
            }
            _add_views.push_back(std::make_pair(*add_view_des, add_info_vec[1]));
        }
        return true;
        
    } catch (const comcfg::ConfigException& ex) {
        CWARNING_LOG("ConfigException:%s", ex.what());
        return false;
    }
    return true;
}

bool SessionExtendViewExtractor::extract_extend_view(
        const CareExp& care_exp,
        LogRecordInterface* log,
        FeatureValueProto* fea) {
    if (!log || !fea) {
        CFATAL_LOG("invalid args, log(%p), fea(%p)", log, fea);
        return false;
    }
    bool care = false;
    if (_need_using_exp) {
        if (!care_exp.care(_care_str, &care)) {
            CWARNING_LOG("call care fail");
            return false;
        }
    } else {
        if (!_care.care(log, &care)) {
            CWARNING_LOG("call care fail");
            return false;
        }
    }
    if (!care) {
        return true;
    }
    ViewValueProto value;
    if (!log->get_view_sign(_views, &value) || value.signs_size() != 1) {
        CWARNING_LOG("get_view_sign fail or view is not level 1");
        return false;
    }
    auto key = fea->mutable_session()->add_keys();
    key->set_view(value.signs(0));
    key->set_reliable(_is_reliable);
    for (auto& view_pair : _add_views) {
        ViewValueProto value;
        if (!log->get_view_value(view_pair.first, &value)) {
            CWARNING_LOG("get_view_value fail, view(%s), logid(%lu)", view_pair.first.view_name.c_str(), log->log_id());
            return false;
        }
        for (int i = 0; i< value.values_size(); ++i) {

            if (!PbReflector::set_field_val(key, view_pair.second, value.values(i).str_value())) {
                CWARNING_LOG("set field val fail, dst(%s)", view_pair.second.c_str());
                return false;
            }
        }

    }
    return true;
}

bool SessionMultiViewFeatureExtractor::_init(
        const comcfg::ConfigUnit& conf,
        const std::vector<ViewDescriptor>& view_des,
        CareExp* care_exp) {
    if (!SessionFeatureExtractor::_init(conf, view_des, care_exp)) {
        CWARNING_LOG("SessionFeatureExractor _init fail");
        return false;
    }

    for (uint32_t i = 0; i < conf["extend_view"].size(); ++i) {
        SessionExtendViewExtractorPtr extend_view(new SessionExtendViewExtractor());
        if (!extend_view->init(conf["extend_view"][i], view_des, care_exp)) {
            CWARNING_LOG("extend view init fail");
            return false;
        }
        _extend_views.push_back(extend_view);
    }
    if (conf["main_care"].selfType() == comcfg::CONFIG_ERROR_TYPE) {
        _main_care_str = "[*]";
    } else {
        _main_care_str = std::string(conf["main_care"].to_cstr());
    }
    if (ExtractorTool::need_using_exp(_main_care_str)) {
        _need_using_exp_main = true;
        if (care_exp == NULL) {
            CFATAL_LOG("invalid input!");
            return false;
        }
        if (!care_exp->append(_main_care_str, view_des)) {
            CWARNING_LOG("main_care init from str(%s) fail", _main_care_str.c_str());
            return false;
        }
    } else {
        _need_using_exp_main = false;
        if (!_main_care.init(_main_care_str, view_des)) {
            CWARNING_LOG("main_care init from str(%s) fail", _main_care_str.c_str());
            return false;
        }
    }
    return true;
}

bool SessionMultiViewFeatureExtractor::_do_extract(
        const CareExp& care_exp,
        LogRecordInterface* log,
        FeatureValueProto* fea,
        bool* valid) {
    if (!log || !fea || !valid) {
        CWARNING_LOG("invalid args, log(%p), fea(%p), valie(%p)", log, fea, valid);
        return false;
    }
    *valid = true;
    for (auto extend_view : _extend_views) {
        if (!extend_view->extract_extend_view(care_exp, log, fea)) {
            CWARNING_LOG("extract extend view fail");
            return false;
        }
    }
    if (!_main_view_adjust(care_exp, log, fea, valid)) {
        CWARNING_LOG("call main_view_adjust fail");
        return false;
    }
    if (*valid) {
        fea->mutable_session()->set_uid(std::to_string(fea->session().keys(0).view()));
    }
    return true;
}

bool SessionMultiViewFeatureExtractor::_main_view_adjust(
        const CareExp& care_exp,
        LogRecordInterface* log,
        FeatureValueProto* fea,
        bool* valid) {
    if (!log || !fea || !valid) {
        return false;
    }
    bool care = false;
    if (_need_using_exp_main) {
        if (!care_exp.care(_main_care_str, &care)) {
            CWARNING_LOG("main care call care fail");
            return false;
        }
    } else {
        if (!_main_care.care(log, &care)) {
            CWARNING_LOG("main care call care fail");
            return false;
        }
    }
    // main view is valid, no need fill main view with extend view
    if (care) {
        return true;
    }
    // main view is invalid, but no extend view, so return
    if (fea->session().keys_size() < 2) {
        *valid = false;
        return true;
    }
    // use extend view fill main view
    auto session = fea->mutable_session();
    session->mutable_keys(0)->CopyFrom(session->keys(1));
    return true;
}
    
}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
