// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: feature_extractor_base.h
// @Last modified: 2018-04-19 22:08:47
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_FEATURE_EXTRACTOR_BASE_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_FEATURE_EXTRACTOR_BASE_H

#include "extractor_tool.h"
#include "feature_extractor_interface.h"
#include "care_space.h"

namespace anti {
namespace themis {
namespace feature_lib {

class FeatureExtractorBase : public FeatureExtractorInterface {
public:
    FeatureExtractorBase() : 
            _pv_coord(-1L),
            _feature_id(0UL), 
            _last_joinkey(0UL),
            _need_using_exp(true) {}
    virtual ~FeatureExtractorBase() {}

    virtual bool init(
            const comcfg::ConfigUnit& conf, 
            const std::vector<ViewDescriptor>& view_des,
            CareExp* care_exp);
    virtual void uninit();

    virtual uint64_t feature_id() const {
        return _feature_id;
    }
    virtual int64_t pv_coord() const {
        return _pv_coord;
    }
    virtual void reset_pv_coord() {
        _pv_coord = -1L;
        _last_joinkey = 0UL;
    }

    // @note: update _pv_coord and _extract function called;
    virtual bool extract(
            const CareExp& care_exp,
            LogRecordInterface* log,
            std::vector<std::shared_ptr<FeatureValueProto> >* feas);

protected:
    std::vector<ViewDescriptor> _views;
    int64_t _pv_coord;

    enum FeatureInfoType {
        VIEW = 0,
        MANUAL = 1,
    };
    struct FeatureInfo {
        FeatureInfo() : type(MANUAL) {};
        FeatureInfo(FeatureInfoType t, const std::string& s) : type(t), str(s) {};
        FeatureInfoType type;
        std::string str;
        ViewDescriptor view_des;
        std::string dst;
    };

    bool _parse_info(
            const std::string& str, 
            const std::vector<ViewDescriptor>& view_des, 
            FeatureInfo* info) const;
    bool _get_info_value(LogRecordInterface* log, const FeatureInfo& info, std::vector<std::string>* values) const;

private:
    virtual bool _init(
            const comcfg::ConfigUnit& conf, 
            const std::vector<ViewDescriptor>& view_des) {
        return true;
    }
    virtual bool _init(
            const comcfg::ConfigUnit& conf, 
            const std::vector<ViewDescriptor>& view_des,
            CareExp* care_exp) {
        return _init(conf, view_des);        
    }
    virtual void _uninit() = 0;

    virtual bool _extract(
            LogRecordInterface* log, 
            std::vector<std::shared_ptr<FeatureValueProto> >* feas) {
        return true;
    }

    virtual bool _extract(
            const CareExp& care_exp,
            LogRecordInterface* log, 
            std::vector<std::shared_ptr<FeatureValueProto> >* feas) {
        return _extract(log, feas);
    }

    uint64_t _feature_id;
    uint64_t _last_joinkey;
    std::string _care_str;
    CareSpace _care;
    bool _need_using_exp;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_FEATURE_EXTRACTOR_BASE_H

/* vim: set ts=4 sw=4: */

