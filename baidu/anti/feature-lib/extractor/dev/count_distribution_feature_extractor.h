// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: freq_dist_feature_extrator.h
// @Last modified: 2016-07-14 13:48:44
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_COUNT_DISTRIBUTION_FEATURE_EXTRACTOR_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_COUNT_DISTRIBUTION_FEATURE_EXTRACTOR_H

#include <view_value.pb.h>
#include "feature_extractor_base.h"

namespace anti {
namespace themis {
namespace feature_lib {

class CountDistributionFeatureExtractor : public FeatureExtractorBase {
public:
    CountDistributionFeatureExtractor() :
            _ftype(FeatureValueProto::COUNT_DISTRIBUTION), 
            _stype(FeatureValueProto::PV) {}
    virtual ~CountDistributionFeatureExtractor() {
        uninit();
    }

private:
    virtual bool _init(
            const comcfg::ConfigUnit& conf, 
            const std::vector<ViewDescriptor>& view_des);
    virtual void _uninit() {
        _data_views.clear();
    }

    virtual bool _extract(
            LogRecordInterface* log, 
            std::vector<std::shared_ptr<FeatureValueProto> >* feas);

    FeatureValueProto::FeatureType _ftype;
    FeatureValueProto::SegType _stype;
    std::vector<ViewDescriptor> _data_views;
};

} // feature_lib
} // themis
} // anti

#endif // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_COUNT_DISTRIBUTION_FEATURE_EXTRACTOR_H
/* vim: set ts=4 sw=4: */

