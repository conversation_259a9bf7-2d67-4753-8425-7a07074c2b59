// Copyright (c) 2021 Baidu.com, Inc. All Rights Reserved
// @Author: longfei(<EMAIL>)
// 
// @Brief: 

#include "intention_feature_extractor.h"
#include <com_log.h>
#include <view_value.pb.h>

using anti::themis::log_interface::ViewValueProto;

namespace anti {
namespace themis {
namespace feature_lib {

bool IntentionFeatureExtractor::_init(
        const comcfg::ConfigUnit& conf, 
        const std::vector<ViewDescriptor>& view_des) {
    try {
        std::string fea_type(conf["feature_type"].to_cstr());
        if (fea_type == "intention") {
            _ftype = FeatureValueProto::INTENTION;
        } else {
            CWARNING_LOG("invalid feature_type(%s)", fea_type.data());
            return false;
        }

        std::string query_view(conf["query_view"].to_cstr());
        if (!ExtractorTool::parse_view_des(query_view, view_des, &_query_views)) {
            CWARNING_LOG("call parse_view_des fail, query_view(%s)", query_view.data());
            return false;
        }
        if (_query_views.size() != 1) {
            CWARNING_LOG("only support one query view, query_view string:%s", query_view.c_str());
            return false;
        }

        if (conf["extend_view"].selfType() != comcfg::CONFIG_ERROR_TYPE) {
            std::string extend_view;
            extend_view.assign(conf["extend_view"].to_cstr());
            if (!ExtractorTool::parse_view_des(extend_view, view_des, &_extend_views)) {
                CWARNING_LOG("call parse_view_des fail, extend_view(%s)", extend_view.data());
                return false;
            }
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std::Exception : %s", e.what());
        return false;
    }
    return true;
}

bool IntentionFeatureExtractor::_extract(
        LogRecordInterface* log, 
        std::vector<std::shared_ptr<FeatureValueProto> >* feas) {
    if (log == NULL || feas == NULL) {
        CFATAL_LOG("input log == NULL || feas == NULL");
        return false;
    }

    ViewValueProto value;
    if (!log->get_view_sign(_views, &value) 
            || value.values_size() != value.signs_size()) {
        CWARNING_LOG("call get_view_sign logid(%lu)", log->log_id());
        return false;
    }

    ViewValueProto query_value, extend_view_value;
    if (_query_views.size() == 1) {
        if (!log->get_view_value(_query_views[0], &query_value)) {
            CFATAL_LOG("fail to get_view_value, view_name:%s", _query_views[0].view_name.c_str());
            return false;
        }
    } else {
        CWARNING_LOG("only support one query view, query_view string:%s", _query_views[0].view_name.c_str());
        return false;    
    }

    if (_extend_views.size() != 0) {
        if (!log->get_view_value(_extend_views[0], &extend_view_value)) {
            CFATAL_LOG("fail to get_view_value, view_name:%s", _extend_views[0].view_name.c_str());
            return false;
        }
    } else {
        CDEBUG_LOG("extend_view not set.");
    }

    for (int i = 0; i < value.signs_size(); ++i) {
        std::shared_ptr<FeatureValueProto> fea(new(std::nothrow) FeatureValueProto());
        if (!fea) {
            CFATAL_LOG("new feature fail, feature_id(%lu)", feature_id());
            return false;
        }
        fea->set_feature_id(feature_id());
        fea->set_view_sign(value.signs(i));
        fea->set_feature_type(_ftype);
        fea->set_joinkey(log->joinkey());
        fea->set_log_id(log->log_id());
        fea->set_log_time(log->log_time());
        fea->set_view(value.values(i).str_value());
        fea->set_seg_type(FeatureValueProto::TIME);
        fea->set_coord(log->log_time() / 1000);

        auto intention_view = fea->mutable_intention_data_view();
        if (_extend_views.size() != 0) {
                intention_view->set_extend_view_value(extend_view_value.values(0).int_value());
        }
        intention_view->set_query(query_value.values(0).str_value());
        feas->push_back(fea);
    }
    return true;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

