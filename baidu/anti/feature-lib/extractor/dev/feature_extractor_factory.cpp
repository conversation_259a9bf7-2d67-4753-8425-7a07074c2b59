// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: feature_extractor_factory.cpp
// @Last modified: 2021-06-03 19:18:22
// @Brief: 

#include "feature_extractor_factory.h"
#include <com_log.h>
#include "carespace_feature_extractor.h"
#include "dict_feature_extractor.h"
#include "segment_feature_extractor.h"
#include "ratio_feature_extractor.h"
#include "distribution_feature_extractor.h"
#include "original_feature_extractor.h"
#include "timediff_feature_extractor.h"
#include "count_distribution_feature_extractor.h"
#include "concentration_feature_extractor.h"
#include "hijack_feature_extractor.h"
#include "distinct_distribution_feature_extractor.h"
#include "fea_deviation_feature_extractor.h"
#include "session_feature_extractor.h"
#include "session_multi_view_feature_extractor.h"
#include "extend_feature_extractor.h"
#include "multi_value_feature_extractor.h"
#include "intention_feature_extractor.h"
#include "dynamic_black_name_feature_extractor.h"

namespace anti {
namespace themis {
namespace feature_lib {

FeatureExtractorInterface* FeatureExtractorFactory::create(const std::string& type) {
    FeatureExtractorInterface* obj = NULL;
    if (type == "segment" 
            || type == "multi_seg" 
            || type == "auto_segment"
            || type == "behavior_segment") {
        obj = new(std::nothrow) SegmentFeatureExtractor();
    } else if (type == "ratio" 
            || type == "auto_ratio"
            || type == "behavior_ratio"
            || type == "rate"
            || type == "cpm"
            || type == "rate_distribution"
            || type == "cpm_distribution") {
        obj = new(std::nothrow) RatioFeatureExtractor();
    } else if (type == "distribution" 
            || type == "behavior_distribution") {
        obj = new(std::nothrow) DistributionFeatureExtractor();
    } else if (type == "file_dict") {
        obj = new(std::nothrow) DictFeatureExtractor();
    } else if (type == "carespace") {
        obj = new(std::nothrow) CarespaceFeatureExtractor();
    } else if (type == "original") {
        obj = new(std::nothrow) OriginalFeatureExtractor();
    } else if (type == "timediff" || type == "dataview_timediff") {
        obj = new(std::nothrow) TDExtractor();
    } else if (type == "count_distribution") {
        obj = new(std::nothrow) CountDistributionFeatureExtractor();
    } else if (type == "concentration" || type == "distinct" 
            || type == "sum_segment" || type == "acp"
            || type == "community" || type == "query_similar"
            || type == "time_interval") {
        obj = new(std::nothrow) ConcentrationFeatureExtractor();
    } else if (type == "hijack") {
        obj = new(std::nothrow) HijackFeatureExtractor();
    } else if (type == "distinct_distribution" || type == "three_level_tree") {
        obj = new(std::nothrow) DistinctDistributionFeatureExtractor();
    } else if (type == "fea_deviation") {
        obj = new(std::nothrow) FeaDeviationFeatureExtractor();
    } else if (type == "session") {
        obj = new(std::nothrow) SessionFeatureExtractor();
    } else if (type == "session_multi_view") {
        obj = new(std::nothrow) SessionMultiViewFeatureExtractor();
    } else if (type == "fea_value_distribution" 
            || type == "fea_value_rate"
            || type == "fea_value_cdf") {
        obj = new(std::nothrow) ExtendFeatureExtractor();
    } else if (type == "multi_value") {
        obj = new(std::nothrow) MultiValueFeatureExtractor();
    } else if (type == "intention") {
        obj = new(std::nothrow) IntentionFeatureExtractor();
    } else if (type == "dynamic_black_name") {
        obj = new(std::nothrow) DynamicBlackNameFeatureExtractor();
    } else if (type == "anti_service_judge") {
        obj = new(std::nothrow) DynamicBlackNameFeatureExtractor();
    } else {
        CWARNING_LOG("invalid type(%s)", type.data());
        return NULL;
    }

    if (obj == NULL) {
        CFATAL_LOG("new FeatureExtractorInterface fail");
        return NULL;
    }
    return obj;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

