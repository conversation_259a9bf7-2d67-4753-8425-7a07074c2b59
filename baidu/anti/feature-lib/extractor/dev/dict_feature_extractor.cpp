// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: dict_feature_extractor.cpp
// @Last modified: 2016-05-07 13:14:00
// @Brief: 

#include <gflags/gflags.h>
#include <com_log.h>
#include <view_value.pb.h>
#include "dict_feature_extractor.h"
#include "multi_column_file_dict.h"
#include "ipmap_dict.h"
#include "string_match_dict.h"

DEFINE_bool(dict_feat_keep_hit_only, false, "keep only one hit feature");

using anti::themis::log_interface::ViewValueProto;
using anti::themis::common_lib::FileDictManager;
using anti::themis::common_lib::FileDictManagerSingleton;

namespace anti {
namespace themis {
namespace feature_lib {

bool detect_multi_columns_dict(const FileDictInterface* dict,
        const ViewValueProto& value, const int32_t idx) {
    auto real_dict = dynamic_cast<const common_lib::MultiColumnFileDict*>(dict);
    if (real_dict == NULL) { return false; }
    return real_dict->lookup(value.signs(idx));
}

bool detect_ipmap_dict(const FileDictInterface* dict,
        const ViewValueProto& value, const int32_t idx) {
    auto real_dict = dynamic_cast<const common_lib::IpmapDict*>(dict);
    if (real_dict == NULL) { return false; }
    return real_dict->lookup(value.values(idx).str_value());
}

bool detect_ip6map_dict(const FileDictInterface* dict,
        const ViewValueProto& value, const int32_t idx) {
    auto real_dict = dynamic_cast<const common_lib::Ip6mapDict*>(dict);
    if (real_dict == NULL) { return false; }
    return real_dict->lookup(value.values(idx).str_value());
}

bool detect_str_match_dict(const FileDictInterface* dict,
        const ViewValueProto& value, const int32_t idx) {
    auto real_dict = dynamic_cast<const common_lib::StringMatchDict*>(dict);
    if (real_dict == NULL) { return false; }
    return real_dict->lookup(value.values(idx).str_value());
}

bool DictFeatureExtractor::_init(
        const comcfg::ConfigUnit& conf, 
        const std::vector<ViewDescriptor>& view_des) {
    std::string dict_type;
    try {
        std::string fea_type(conf["feature_type"].to_cstr());
        if (fea_type == "file_dict") {
            _ftype = FeatureValueProto::FILE_DICT;
        } else {
            CWARNING_LOG("invalid feature_type(%s)", fea_type.data());
            return false;
        }
        std::string file_key(conf["file_key"].to_cstr());
        _dict = FileDictManagerSingleton::instance().get_file_dict(file_key);
        if (!_dict) {
            CWARNING_LOG("call get_file_dict fail, file_key(%s)", file_key.data());
            return false;
        }
        if (conf["dict_type"].selfType() == comcfg::CONFIG_ERROR_TYPE) {
            dict_type = "multicolumn";
        } else {
            dict_type = std::string(conf["dict_type"].to_cstr());
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std::Exception : %s", e.what());
        return false;
    }

    if (dict_type == "ipmap") {
        _detect_func = detect_ipmap_dict;
    } else if (dict_type == "ip6map") {
        _detect_func = detect_ip6map_dict;
    } else if (dict_type == "str_match"){
        _detect_func = detect_str_match_dict;
    } else {
        _detect_func = detect_multi_columns_dict;
    }

    return true;
}

bool DictFeatureExtractor::_extract(
        LogRecordInterface* log, 
        std::vector<std::shared_ptr<FeatureValueProto> >* feas) {
    if (log == NULL || feas == NULL) {
        CFATAL_LOG("input log == NULL || feas == NULL");
        return false;
    }
    ViewValueProto value;
    if (!log->get_view_sign(_views, &value) 
            || value.values_size() != value.signs_size()) {
        CWARNING_LOG("call get_view_sign logid(%lu)", log->log_id());
        return false;
    }

    if (!_dict) {
        CFATAL_LOG("feature_id(%lu) _file is NULL, call init first", feature_id());
        return false;
    }

    for (int i = 0; i < value.signs_size(); ++i) {
        bool in_file = _detect_func(_dict.get(), value, i);
        if (!in_file && FLAGS_dict_feat_keep_hit_only) { continue; }

        std::shared_ptr<FeatureValueProto> fea(new(std::nothrow) FeatureValueProto());
        if (!fea) {
            CFATAL_LOG("new feature fail, feature_id(%lu)", feature_id());
            return false;
        }
        fea->set_feature_id(feature_id());
        fea->set_view_sign(value.signs(i));
        fea->set_feature_type(_ftype);
        fea->set_joinkey(log->joinkey());
        fea->set_log_id(log->log_id());
        fea->set_log_time(log->log_time());
        fea->set_view(value.values(i).str_value());
        // ms -> s
        fea->set_coord(log->log_time() / 1000);
        //bool in_file = _file->lookup(value.signs(i));
        //bool in_file = _detect_func(_dict.get(), value, i);
        fea->set_condition(in_file);
        fea->set_valid(true);
        // in file : 1
        // not in file : 0
        fea->set_value(in_file ? "1" : "0");
        feas->push_back(fea);
        // IF open this flag, only keep one hit-feature
        if (FLAGS_dict_feat_keep_hit_only) { break; }
    }
    return true;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

