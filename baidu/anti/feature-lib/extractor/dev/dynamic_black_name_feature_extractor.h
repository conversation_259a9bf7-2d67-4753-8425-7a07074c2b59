// Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
// @Author: tanweiliang(<EMAIL>)
// 
// @File: dynamic_black_name_feature_extractor.h
// @Brief: 

#pragma once

#include "feature_extractor_base.h"

namespace anti {
namespace themis {
namespace feature_lib {

typedef log_interface::ViewValueProto ViewValueProto;

class DynamicBlackNameFeatureExtractor : public FeatureExtractorBase {
public:
    DynamicBlackNameFeatureExtractor() : _ftype(FeatureValueProto::FILE_DICT) {}
    virtual ~DynamicBlackNameFeatureExtractor() {
        uninit();
    }

private:
    virtual bool _init(
            const comcfg::ConfigUnit& conf, 
            const std::vector<ViewDescriptor>& view_des);
    virtual void _uninit() {
        _black_hit_feaid.clear();
    }

    virtual bool _extract(
            LogRecordInterface* log, 
            std::vector<std::shared_ptr<FeatureValueProto> >* feas);
 
    FeatureValueProto::FeatureType _ftype;
    std::vector<uint64_t> _black_hit_feaid;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

