// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @brief 

#include <string>
#include <com_log.h>
#include "del_feature.h"
#include "big_feature.h"

namespace anti {
namespace themis {
namespace feature_lib {

int32_t DelfeaLineParser::parse(
        char* line,
        uint64_t* sign,
        int32_t* policy_id)
{
    if (NULL == line || NULL == sign || NULL == policy_id) {
        CWARNING_LOG("param error, [line:%p sign:%p id:%p]",
                line, sign, policy_id);
        return -1;
    }
    if ('\0' == *line || '#' == *line) {
        return 1;
    }

    // found separator tab
    char* tab_sep = rindex(line, '\t');
    if (NULL == tab_sep) {
        CFATAL_LOG("format of delete feature file error,"
                " [line:%s]", line);
        return -1;
    }
    *tab_sep = '\0';

    // parser feature
    FeatureSign fs;
    //creat_sign_md64(line, strlen(line), &(fs.sign32[0]), &(fs.sign32[1]));
    anti::baselib::SignUtil::create_sign_md64(line, strlen(line), &(fs.sign64));

    // parse threshold
    char* th_end = NULL;
    *policy_id = static_cast<int32_t>(strtol(tab_sep + 1, &th_end, 10));
    if (th_end == (tab_sep + 1)
            || NULL == th_end
            || ('\0' != *th_end && '\n' != *th_end)) {
        CFATAL_LOG("format of delete feature file error,"
                " [line:%s]", line);
        return -1;
    }

    *sign = sign_with_id(fs.sign64, *policy_id);
    return 0;
}

void DelFeaWrapper::delete_fea(std::vector<FeaPtr>* feas) {
    if (_delfea_map == NULL) { return; }
    if (feas->size() == 0) { return; }

    std::vector<std::shared_ptr<FeatureValueProto>> tmp;
    for (uint32_t i = 0; i < feas->size(); ++i) {
        auto item = feas->at(i);
        if (!item) { continue; }
        uint64_t key = sign_with_id(item->view_sign(), item->feature_id());
        auto iter = _delfea_map->find(key);
        if (iter == _delfea_map->end()
                || iter->second != (int32_t)item->feature_id()) {
            tmp.push_back(item);
        }
    }
    if (tmp.size() != feas->size()) {
       feas->clear();
       feas->assign(tmp.begin(), tmp.end());
    }
}

void DelFeaWrapper::delete_fea(std::vector<std::vector<FeaPtr>>* feas) {
    if (feas == NULL) { return; }
    for (uint32_t i = 0; i < feas->size(); ++i) {
        delete_fea(&((*feas)[i]));
    }
    return;
}

}
}
}
