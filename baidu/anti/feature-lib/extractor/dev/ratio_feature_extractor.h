// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: ratio_feature_extractor.h
// @Last modified: 2018-04-20 10:23:52
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_RATIO_FEATURE_EXTRACTOR_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_RATIO_FEATURE_EXTRACTOR_H

#include "feature_extractor_base.h"

namespace anti {
namespace themis {
namespace feature_lib {

class RatioFeatureExtractor : public FeatureExtractorBase {
public:
    RatioFeatureExtractor() :
            _ftype(FeatureValueProto::RATIO),
            _stype(FeatureValueProto::PV),
            _weak_check(false),
            _need_using_exp_filter(true),
            _need_using_exp_refer(true) {}
    virtual ~RatioFeatureExtractor() {
        uninit();
    }

private:
    virtual bool _init(
            const comcfg::ConfigUnit& conf, 
            const std::vector<ViewDescriptor>& view_des,
            CareExp* care_exp);
    bool _init_price_view(
            const comcfg::ConfigUnit& conf,
            const std::vector<ViewDescriptor>& view_des);

    virtual void _uninit() {
        _price.reset();
        _data_views.clear();
    }

    virtual bool _extract(
            const CareExp& care_exp,
            LogRecordInterface* log, 
            std::vector<std::shared_ptr<FeatureValueProto> >* feas);
 
    FeatureValueProto::FeatureType _ftype;
    FeatureValueProto::SegType _stype;
    std::string _filter_str;
    std::string _refer_str;
    CareSpace _filter;
    bool _need_using_exp_filter;
    CareSpace _refer;
    bool _need_using_exp_refer;
    bool _weak_check;

    std::shared_ptr<ViewDescriptor> _price;    // only for cpm and cpm_distribution
    std::vector<ViewDescriptor> _data_views;   // only for cpm_distribution and rate_distribution

};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_DEV_RATIO_FEATURE_EXTRACTOR_H

/* vim: set ts=4 sw=4: */

