// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @brief 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_HIJACK_FEATURE_EXTRACTOR_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_HIJACK_FEATURE_EXTRACTOR_H

#include <memory>
#include "feature_extractor_base.h"

namespace anti {
namespace themis {
namespace feature_lib {

class HijackFeatureExtractor : public FeatureExtractorBase {
public:
    HijackFeatureExtractor();
    virtual ~HijackFeatureExtractor();

private:
    virtual bool _init(const comcfg::ConfigUnit& conf,
            const std::vector<ViewDescriptor>& view_des);
    virtual void _uninit();

    virtual bool _extract(LogRecordInterface* log,
            std::vector<std::shared_ptr<FeatureValueProto>>* feas);

    bool get_hijack_field(LogRecordInterface*, FeatureValueProto::HijackProto*);

private:
    ViewDescriptor _uniq_cn_des;
    ViewDescriptor _flow_group_des;
    ViewDescriptor _page_no_des;
    FeatureValueProto::FeatureType _ftype;
    FeatureValueProto::SegType _stype;
};

}
}
}

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_HIJACK_FEATURE_EXTRACTOR_H
