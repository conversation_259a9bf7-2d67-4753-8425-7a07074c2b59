// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// <AUTHOR>
// @brief

#include "fea_deviation_feature_extractor.h"
#include <boost/lexical_cast.hpp>
#include <boost/algorithm/string.hpp>
#include <com_log.h>

namespace anti {
namespace themis {
namespace feature_lib {

bool FeaDeviationFeatureExtractor::_init(
        const comcfg::ConfigUnit& conf,
        const std::vector<ViewDescriptor>& view_des) {
    try {
        if (!_find_view(view_des, conf["view_name"].to_cstr(), &_view_name_des)) {
            CWARNING_LOG("find view name des failed!");
            return false;
        }
        if (!_find_view(view_des, conf["value"].to_cstr(), &_value_des)) {
            CWARNING_LOG("find value des failed!");
            return false;
        }
        if (!_init_acc(conf, view_des)) {
            CWARNING_LOG("init accumulate failed!");
            return false;
        }
        if (!_init_dis(conf)) {
            CWARNING_LOG("init discretization failed!");
            return false;
        }
        if (_views.size() != 1) {
            CWARNING_LOG("views size illegal![%u]", _views.size());
            return false;
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std::exception : %s", e.what());
        return false;
    }
    return true;
}

bool FeaDeviationFeatureExtractor::_extract(
        LogRecordInterface* log,
        std::vector<std::shared_ptr<FeatureValueProto> >* feas) {
    if (log == NULL || feas == NULL) {
        CFATAL_LOG("params illegal! log:%p, feas:%p", log, feas);
        return false;
    }
    std::string view_name_str;
    std::string value_str;
    std::string view_str;
    int64_t acc_value = 1;
    int64_t dis_value = 0;
    if (!_get_str_view(log, _view_name_des, &view_name_str)) {
        CWARNING_LOG("get view_name from log failed! logid:%lu", log->log_id());
        return false;
    }
    if (!_get_str_view(log, _value_des, &value_str)) {
        CWARNING_LOG("get value from log failed! logid:%lu", log->log_id());
        return false;
    }
    if (_open_accumulate_des && !_get_int_view(log, _accumulate_des, &acc_value)) {
        CWARNING_LOG("get accumulate value from log failed! logid:%lu",
                log->log_id());
        return false;
    }
    if (!_get_discretization_value(value_str, &dis_value)) {
        CWARNING_LOG("get discretization value failed! logid:%lu", log->log_id());
        return false;
    }
    std::shared_ptr<FeatureValueProto> fea(new (std::nothrow) FeatureValueProto);
    if (!fea) {
        CFATAL_LOG("new feature failed!");
        return false;
    }
    ViewValueProto view_value;
    if (!log->get_view_sign(_views, &view_value) ||
            view_value.values_size() != 1U) {
        CWARNING_LOG("get view failed! logid:%lu view_size:%u",
                log->log_id(), view_value.values_size());
        return false;
    }
    fea->set_feature_id(feature_id());
    fea->set_feature_type(_ftype);
    fea->set_joinkey(log->joinkey());
    fea->set_log_id(log->log_id());
    fea->set_log_time(log->log_time());
    // view
    fea->set_view_sign(view_value.signs(0));
    fea->set_view(view_value.values(0).str_value());
    // view_name
    fea->set_view_name(view_name_str);
    // value
    fea->set_value(value_str);
    // coord
    fea->set_coord(log->log_time());
    // fea deviation
    FeaDeviationProto fea_dev_proto;
    fea_dev_proto.set_devia_fea_value(dis_value);
    fea_dev_proto.set_devia_fea_count(acc_value);
    fea->mutable_fea_deviation_field()->CopyFrom(fea_dev_proto);
    feas->push_back(fea);
    return true;
}

bool FeaDeviationFeatureExtractor::_init_acc(const comcfg::ConfigUnit& conf,
        const std::vector<ViewDescriptor>& view_des) {
    _open_accumulate_des = false;
    if (conf["accumulate"].selfType() != comcfg::CONFIG_ERROR_TYPE) {
        if (!_find_view(view_des, conf["accumulate"].to_cstr(),
                &_accumulate_des)) {
            CWARNING_LOG("find accumulate des failed!");
            return false;
        }
        _open_accumulate_des = true;
    }
    return true;
}

bool FeaDeviationFeatureExtractor::_init_dis(const comcfg::ConfigUnit& conf) {
    std::string dis_type = "";
    bool need_init = false;
    if (conf["discretization"].selfType() == comcfg::CONFIG_ERROR_TYPE) {
        dis_type = "divide";
    } else if (conf["discretization"]["type"].selfType() ==
            comcfg::CONFIG_ERROR_TYPE) {
        dis_type = "divide";
        need_init = true;
    } else {
        dis_type = conf["discretization"]["type"].to_cstr();
        need_init = true;
    }
    _discretization.reset(DiscretizationFactory::create(dis_type));
    if (!_discretization) {
        CFATAL_LOG("reset discretization failed!");
        return false;
    }
    if (need_init && !_discretization->init(conf["discretization"])) {
        CWARNING_LOG("init discretization failed!");
        return false;
    }
    return true;
}

bool FeaDeviationFeatureExtractor::_find_view(
        const std::vector<ViewDescriptor>& view_des, const std::string& view_str,
        ViewDescriptor* out_des) {
    if (out_des == NULL) {
        CFATAL_LOG("param illegal");
        return false;
    }
    auto view_name_it = std::find_if(view_des.begin(), view_des.end(),
            [&view_str](const ViewDescriptor& v)->bool {
                return view_str == v.view_name;
            });
    if (view_name_it == view_des.end()) {
        CWARNING_LOG("find view_des failed![%s]", view_str.c_str());
        return false;
    }
    *out_des = *view_name_it;
    return true;
}

bool FeaDeviationFeatureExtractor::_get_str_view(
        LogRecordInterface* log,
        const ViewDescriptor& des,
        std::string* value) {
    if (log == NULL || value == NULL) {
        CFATAL_LOG("param illegal! log:%p, value:%p", log, value);
        return false;
    }
    ViewValueProto view_value;
    if (!log->get_view_value(des, &view_value)) {
        CWARNING_LOG("get view failed! logid:%lu", log->log_id());
        return false;
    }
    if (view_value.values_size() != 1U) {
        CWARNING_LOG("view value size not 1 but %u", view_value.values_size());
        return false;
    }
    *value = view_value.values(0).str_value();
    return true;
}

bool FeaDeviationFeatureExtractor::_get_int_view(
        LogRecordInterface* log,
        const ViewDescriptor& des,
        int64_t* value) {
    if (log == NULL || value == NULL) {
        CFATAL_LOG("param illegal! log:%p, value:%p", log, value);
        return false;
    }
    ViewValueProto view_value;
    if (!log->get_view_value(des, &view_value)) {
        CWARNING_LOG("get view failed! logid:%lu", log->log_id());
        return false;
    }
    if (!_get_int_value(view_value, value)) {
        CWARNING_LOG("get int value from view proto failed!");
        return false;
    }
    return true;
}

bool FeaDeviationFeatureExtractor::_get_int_value(
        const ViewValueProto& view_value,
        int64_t* value) {
    if (value == NULL || view_value.values_size() != 1U) {
        CFATAL_LOG("param illegal! value:%p, value_size:%u", value,
                view_value.values_size());
        return false;
    }
    switch (view_value.type()) {
    case ViewValueProto::UINT32:
    case ViewValueProto::INT32:
    case ViewValueProto::UINT64:
    case ViewValueProto::INT64: {
        *value = view_value.values(0).int_value();
        break;
    }
    case ViewValueProto::STRING: {
        try {
            *value = boost::lexical_cast<int64_t>(
                    view_value.values(0).str_value());
        } catch (const boost::bad_lexical_cast& e) {
            CWARNING_LOG("boost::Exception: %s", e.what());
            return false;
        }
        break;
    }
    default:
        CWARNING_LOG("invalid value type(%d)", view_value.type());
        return false;
    }
    return true;
}

bool FeaDeviationFeatureExtractor::_get_discretization_value(
        const std::string& str, int64_t* value) {
    if (value == NULL) {
        CFATAL_LOG("param illegal!");
        return false;
    }
    std::vector<std::string> parts;
    boost::split(parts, str, boost::is_any_of("#"),
            boost::token_compress_on);
    double original_value = 0.0;
    try {
        original_value = boost::lexical_cast<double>(parts[0]);
    } catch (const boost::bad_lexical_cast& e) {
        CWARNING_LOG("boost::Exception: %s", e.what());
        return false;
    }
    if (!_discretization) {
        CFATAL_LOG("discretization not init");
        return false;
    }
    *value = _discretization->discretize(original_value);
    return true;
}

}
}
}
