// Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
// @Author: linfan02
// 
// @Brief: 

#include <com_log.h>
#include <view_value.pb.h>
#include "multi_value_feature_extractor.h"

using anti::themis::log_interface::ViewValueProto;

namespace anti {
namespace themis {
namespace feature_lib {

bool MultiValueFeatureExtractor::_init(
        const comcfg::ConfigUnit& conf, 
        const std::vector<ViewDescriptor>& view_des) {
    try {
        std::string fea_type(conf["feature_type"].to_cstr());
        if (fea_type == "multi_value") {
            _ftype = FeatureValueProto::MULTI_VALUE;
        } else {
            CWARNING_LOG("invalid feature_type(%s)", fea_type.data());
            return false;
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std::Exception : %s", e.what());
        return false;
    }
    return true;
}

bool MultiValueFeatureExtractor::_extract(
        LogRecordInterface* log, 
        std::vector<std::shared_ptr<FeatureValueProto> >* feas) {
    if (log == NULL || feas == NULL) {
        CFATAL_LOG("input log == NULL || feas == NULL");
        return false;
    }

    ViewValueProto value;
    if (!log->get_view_sign(_views, &value) 
            || value.values_size() != value.signs_size()) {
        CWARNING_LOG("call get_view_sign logid(%lu)", log->log_id());
        return false;
    }
    if (value.values_size() == 0) {
        return true;
    }

    std::shared_ptr<FeatureValueProto> fea(new(std::nothrow) FeatureValueProto());
    if (!fea) {
        CFATAL_LOG("new feature fail, feature_id(%lu)", feature_id());
        return false;
    }
    fea->set_view_sign(value.signs(0));
    fea->set_feature_id(feature_id());
    fea->set_feature_type(_ftype);
    fea->set_joinkey(log->joinkey());
    fea->set_log_id(log->log_id());
    fea->set_log_time(log->log_time());
    // ms -> s
    fea->set_coord(log->log_time() / 1000);
    fea->set_valid(true);

    for (uint32_t i = 0; i < value.signs_size(); ++i) {
        fea->add_values(value.values(i).str_value());
        fea->add_views(value.values(i).str_value());
    }
    feas->push_back(fea);
    return true;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */
