// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// <AUTHOR>
// @brief 
#ifndef FEATURE_LIB_EXTRACTOR_INC_SESSION_FEATURE_EXTRACTOR_H
#define FEATURE_LIB_EXTRACTOR_INC_SESSION_FEATURE_EXTRACTOR_H 
#include "feature_extractor_base.h"

namespace anti {
namespace themis {
namespace feature_lib {

class SessionFeatureExtractor : public FeatureExtractorBase {
public:
    SessionFeatureExtractor() {}
    ~SessionFeatureExtractor() {
        uninit();
    }

protected:
    virtual bool _init(
            const comcfg::ConfigUnit& conf, 
            const std::vector<ViewDescriptor>& view_des,
            CareExp* care_exp);
private:
    virtual void _uninit() {}
    virtual bool _extract(
            const CareExp& care_exp,
            LogRecordInterface* log, 
            std::vector<std::shared_ptr<FeatureValueProto> >* feas);
    virtual bool _do_extract(
            const CareExp& care_exp,
            LogRecordInterface* log, 
            FeatureValueProto* fea,
            bool* valid) {
        *valid = true;
        return true;
    }

    std::vector<FeatureInfo> _fea_infos;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif
