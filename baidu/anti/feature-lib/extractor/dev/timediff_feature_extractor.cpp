// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @brief 

#include <com_log.h>
#include <view_value.pb.h>
#include "timediff_feature_extractor.h"

namespace anti {
namespace themis {
namespace feature_lib {

typedef std::shared_ptr<FeatureValueProto> FeaturePtr;
typedef log_interface::ViewValueProto ViewValueProto;

TDExtractor::TDExtractor() : 
        _ftype(FeatureValueProto::TIME_DIFF),
        _stype(FeatureValueProto::TIME) {}
TDExtractor::~TDExtractor() {}

bool TDExtractor::_init(const comcfg::ConfigUnit& conf,
        const std::vector<ViewDescriptor>& view_des) {
    try {
        std::string fea_type(conf["feature_type"].to_cstr());
        if (fea_type == "timediff") {
            _ftype = FeatureValueProto::TIME_DIFF;
        } else if (fea_type == "dataview_timediff")
        {
            _ftype = FeatureValueProto::DATAVIEW_TIME_DIFF;
        } else {
            CWARNING_LOG("invalid feature_type(%s)", fea_type.data());
            return false;
        }

        if(fea_type == "dataview_timediff"){
            std::string data_view(conf["data_view"].to_cstr());
            if (!ExtractorTool::parse_view_des(data_view, view_des, &_data_views)) {
                CWARNING_LOG("call parse_view_des fail, data_view(%s)", data_view.data());
                return false;
            }
        }

        std::string seg_type(conf["window_type"].to_cstr());
        if (seg_type == "pv") {
            _stype = FeatureValueProto::PV;
        } else if (seg_type == "time") {
            _stype = FeatureValueProto::TIME;
        } else {
            CWARNING_LOG("invalid window_type(%s)", seg_type.data());
            return false;
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std::Exception : %s", e.what());
        return false;
    }
    return true;
}

void TDExtractor::_uninit() { }

bool TDExtractor::_extract(LogRecordInterface* log,
        std::vector<FeaturePtr>* feas) {
    if (log == NULL || feas == NULL) {
        CFATAL_LOG("invalid input args. [log:%p,feat:%p]", log, feas);
        return false;
    }

    ViewValueProto data_value;
    if (FeatureValueProto::DATAVIEW_TIME_DIFF == _ftype){
        if (!log->get_view_sign(_data_views, &data_value)
                || data_value.values_size() != data_value.signs_size()) {
            CWARNING_LOG("call get data_views sign fail, logid(%lu)", log->log_id());
            return false;        
        }
    }

    ViewValueProto value;
    if (!log->get_view_sign(_views, &value)
            || value.values_size() != value.signs_size()) {
        CWARNING_LOG("get_view_sign failed. [logid:%lu]", log->log_id());
        return false;
    }

    for (int i = 0; i < value.signs_size(); ++i) {
        FeaturePtr fea(new (std::nothrow) FeatureValueProto());
        if (!fea) {
            CFATAL_LOG("alloc feature failed");
            return false;
        }

        fea->set_feature_id(feature_id());
        fea->set_view_sign(value.signs(i));
        if(FeatureValueProto::DATAVIEW_TIME_DIFF == _ftype){
            fea->set_data_view_sign(data_value.signs(i));
            fea->set_data_view_value(data_value.values(i).str_value());
        }
        fea->set_feature_type(_ftype);
        fea->set_joinkey(log->joinkey());
        fea->set_log_id(log->log_id());
        fea->set_log_time(log->log_time());
        fea->set_view(value.values(i).str_value());
        fea->set_seg_type(_stype);
        if (_stype == FeatureValueProto::PV) {
            fea->set_coord(_pv_coord);
        } else {
            // ms -> s
            fea->set_coord(log->log_time() / 1000);
        }
        fea->set_pv_coord(_pv_coord);
        feas->push_back(fea);
    }
    return true;
}

}
}
}
