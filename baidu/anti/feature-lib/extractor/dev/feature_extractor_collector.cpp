// Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
// @Author: z<PERSON><PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
// 
// @File: feature_extractor_collector.cpp
// @Last modified: 2017-06-12 19:12:42
// @Brief: 

#include <algorithm>
#include <gflags/gflags.h>
#include "boost/algorithm/string.hpp"
#include "feature_extractor_manager.h"
#include "feature_extractor_collector.h"
#include <com_log.h>
#include <view_level_util.h>
#include "feature_extractor_factory.h"
#include "big_feature.h"
#include "feature_util.h"
#include <set>

using anti::themis::log_parser_lib::ViewLevelUtil;

namespace anti {
namespace themis {
namespace feature_lib {

bool FeatureExtractorCollector::init(
        const std::string& path,
        const std::string& file,
        LogRecordCollector* log_col) {
    comcfg::Configure conf;
    if (conf.load(path.data(), file.data()) != 0) {
        CFATAL_LOG("load %s/%s failed", path.data(), file.data());
        return false;
    }
    try {
        anti::themis::RecordType record_type;
        if (conf["source"].size() == 0) {
            //if source size is zero, log_col should by get only one type record
            //with any record_type.
            _manager = _init_conf(path, file, log_col, record_type);
            if (_manager == NULL) {
                CWARNING_LOG("_init_conf fail");
                return false;
            }
        }

        for (uint32_t source_id = 0; source_id < conf["source"].size(); ++source_id) {
            const auto& src_conf = conf["source"][source_id];
            std::string log_type(src_conf["log_type"].to_cstr());
            std::string conf_path(src_conf["feature_extractor_path"].to_cstr());
            std::string conf_file(src_conf["feature_extractor_file"].to_cstr());

            if (!RecordType_Parse(log_type, &record_type)) {
                CWARNING_LOG("get type fail:%s", log_type.c_str());
                return false;
            }

            FeatureExtractorManagerPtr mgr_ptr = _init_conf(conf_path, conf_file,
                        log_col, record_type);
            if (mgr_ptr == NULL) {
                CWARNING_LOG("_init_conf multi fail %s error", log_type.c_str());
                return false;
            }
            
            _manager_map.insert(std::pair<anti::themis::RecordType,
                    FeatureExtractorManagerPtr>(record_type, mgr_ptr));
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("comcfg::ConfigException : %s", e.what());
        return false;
    }

    CWARNING_LOG("init feature extractor collector success");
    return true;
}

FeatureExtractorManagerPtr FeatureExtractorCollector::_init_conf(const std::string& path,
        const std::string& file,
        LogRecordCollector* log_col,
        anti::themis::RecordType record_type) {
    std::map<anti::themis::RecordType, FeatureExtractorManagerPtr>::iterator iter;
    iter = _manager_map.find(record_type);
    if (iter != _manager_map.end()) {
        CWARNING_LOG("record_type [%d] already initialized", record_type);
        return NULL;
    }

    LogRecordInterface* record = log_col->get_record(record_type);
    if (record == NULL) {
        CWARNING_LOG("record type [%d] not in parser conf", record_type);
        return NULL;
    }

    FeatureExtractorManagerPtr mgr_ptr(new (std::nothrow) FeatureExtractorManager());
    if (mgr_ptr == NULL) {
        CWARNING_LOG("factory create record type [%d] error", record_type);
        return NULL;
    }

    if (!mgr_ptr->init(path, file, *(record->view_descriptors()))) {
        CWARNING_LOG("mgr init record [%d] error", record_type);
        return NULL;
    }

    return mgr_ptr;
}

bool FeatureExtractorCollector::extract(anti::themis::RecordType record_type,
        LogRecordCollector* record_collector,
        std::vector<FeaturePtr>* cur_feat_list) {
    LogRecordInterface* record = record_collector->get_record(record_type);
    if (record == NULL) {
        CWARNING_LOG("record type extract fail no [%d] type record", record_type);
        return false;
    }

    if (_manager_map.size() == 0) {
        return _manager->extract(record, cur_feat_list);
    }

    auto iter = _manager_map.find(record_type);
    if (iter == _manager_map.end()) {
        CWARNING_LOG("record_type [%d] extract fail", record_type);
        return false;
    }
    auto manager = iter->second;
    return manager->extract(record, cur_feat_list);
}

bool FeatureExtractorCollector::reset_pv_coords() {
    if (_manager_map.size() == 0) {
        return _manager->reset_pv_coords();
    }

    bool ret = true;
    for (auto iter = _manager_map.begin(); iter != _manager_map.end(); iter++) {
        auto mgr= iter->second;
        if (!mgr->reset_pv_coords()) {
            ret = false;
        }
    }
    return ret;
}

bool FeatureExtractorCollector::get_pv_coords(CoordArrayContainer* container) {
    if (container == NULL) {
        CFATAL_LOG("container ptr is NULL");
        return false;
    }
    if (_manager_map.size() == 0) {
        CoordArray coords;
        if (!_manager->get_pv_coords(&coords)) {
            CFATAL_LOG("manager get fail");
            return false;
        }
        anti::themis::RecordType record_type;
        container->push_back(
                std::pair<anti::themis::RecordType, CoordArray>(record_type, coords));
        return true;
    }

    bool ret = true;
    for (auto iter = _manager_map.begin(); iter != _manager_map.end(); iter++) {
        auto mgr= iter->second;
        CoordArray coords;
        if (!mgr->get_pv_coords(&coords)) {
            CWARNING_LOG("manger get fail. record type:[%d]", iter->first);
            ret = false;
        } else {
            container->push_back(
                    std::pair<anti::themis::RecordType, CoordArray>(iter->first, coords));
        }
    }
    return ret;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

