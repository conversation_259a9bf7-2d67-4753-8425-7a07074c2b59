// Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
// @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
// 
// @Last modified: 2017-06-29 15:29:15
// @Brief:
// @Usage: 
// @Note: 

#ifndef BAIDU_ANTI_FEATURE_LIB_JUDGE_COLLECTOR_H
#define BAIDU_ANTI_FEATURE_LIB_JUDGE_COLLECTOR_H

#include <string>
#include <vector>
#include <memory>
#include <Configure.h>
#include "record.pb.h"
#include "judge.h"

namespace anti {
namespace themis {
namespace feature_lib {

typedef anti::themis::RecordType RecordType;
typedef std::shared_ptr<Judge> JudgePtr;

// 简介：日志判定管理器
// 功能：保存一个map<RecordType,  FeatureExtractorManagerPtr>对多个不同类型的 log 进行判定
// 输入：JudgeCollector.conf
// 输出：bool 值判定日志是否为 spam 作弊日志
class JudgeCollector {
public:
    JudgeCollector() {}
    ~JudgeCollector() {}

    bool init(
            const std::string& fea_conf_path,
            const std::string& fea_conf_file);
    bool judge(const std::vector<PolicyResultProto>& results,
            bool* spam,
            RecordType record_type);
    bool judge(const PolicyResultProto& results);
    bool judge(const std::vector<PolicyResultProto>& results, bool* spam); 

private:
    JudgePtr _get_fea_mgr(anti::themis::RecordType record_type);
    JudgePtr _init_conf(const std::string& path,
            const std::string& file,
            const anti::themis::RecordType record_type);
    JudgePtr _manager;
    std::map<anti::themis::RecordType, JudgePtr> _manager_map;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // BAIDU_ANTI_FEATURE_LIB_JUDGE_COLLECTOR_H 

/* vim: set ts=4 sw=4: */

