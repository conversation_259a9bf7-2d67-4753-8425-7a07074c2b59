// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangji<PERSON><PERSON>(<EMAIL>)
// 
// @File: judge.h
// @Last modified: 2015-05-27 18:48:33
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_JUDGE_INC_JUDGE_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_JUDGE_INC_JUDGE_H

#include <string>
#include <vector>
#include <unordered_map>
#include "policy_result.pb.h"

namespace anti {
namespace themis {
namespace feature_lib {

struct PolicyInfo {
    int32_t weight;
    double probability;

    PolicyInfo() : weight(0), probability(1.0) {}
    PolicyInfo(int32_t wgt, double prob) : weight(wgt), probability(prob) {}
};

// 简介：日志作弊判定类
// 功能：完成命中策略的权重累计, 与阈值比较判定日志是否作弊
// 输入：judge.conf 、PolicyResultProto
// 输出：bool 值判定日志是否作弊
class Judge {
public:
    Judge() : _threshold(0L) {}
    virtual ~Judge () {
        uninit();
    }

    bool init(const std::string& conf_path, const std::string& conf_file);
    void uninit() {
        _policy_infos.clear();
    }

    bool judge(const std::vector<PolicyResultProto>& results, bool* spam) const;
    bool judge(const PolicyResultProto& result) const;

private:
    bool _init_policies(const std::string& policies);
    int32_t _get_policy_weight(const PolicyResultProto& result) const;

    int64_t _threshold;
    std::unordered_map<uint64_t, PolicyInfo> _policy_infos;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_JUDGE_INC_JUDGE_H

/* vim: set ts=4 sw=4: */

