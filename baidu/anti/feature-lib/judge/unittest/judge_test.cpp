// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: judge_test.cpp
// @Last modified: 2015-05-28 11:47:18
// @Brief: 

#include <gtest/gtest.h>
#include "bmock.h"
#include "judge.h"

using ::testing::Return;
BMOCK_METHOD0(rand, int32_t());

namespace anti {
namespace themis {
namespace feature_lib {

class JudgeTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
        BMOCK_STOP(rand, int32_t());
    }
    virtual void TearDown() {
        BMOCK_STOP(rand, int32_t());
    }

private:
    Judge _obj;
};

TEST_F(JudgeTestSuite, construction_case) {
    EXPECT_EQ(0L, _obj._threshold);
}

TEST_F(JudgeTestSuite, init_case) {
    EXPECT_FALSE(_obj.init("./conf", "xxxxxx"));
    _obj.uninit();

    EXPECT_FALSE(_obj.init("./conf", "invalid_judge.conf"));
    _obj.uninit();

    EXPECT_FALSE(_obj.init("./conf", "invalid_judge1.conf"));
    _obj.uninit();

    ASSERT_TRUE(_obj.init("./conf", "judge_test.conf"));
    ASSERT_EQ(4U, _obj._policy_infos.size());
    EXPECT_EQ(10L, _obj._threshold);
    uint64_t policy_ids[] = {70032LU, 11001LU, 351LU, 361LU};
    int32_t ws[] = {15, 10, 10000, -100};
    for (uint32_t i = 0U; i < 4U; ++i) {
        EXPECT_EQ(ws[i], _obj._policy_infos[policy_ids[i]].weight);
    }
    _obj.uninit();
}

TEST_F(JudgeTestSuite, _init_policies_by_invalid_input_case) {
    // miss #
    EXPECT_FALSE(_obj._init_policies("123"));
    _obj.uninit();

    // invalid policy_id
    EXPECT_FALSE(_obj._init_policies("xxx#123"));
    _obj.uninit();

    // insert same policy_id
    EXPECT_FALSE(_obj._init_policies("70032#123, 70032#456"));
    _obj.uninit();
}

TEST_F(JudgeTestSuite, judge_case) {
    ASSERT_TRUE(_obj.init("./conf", "judge_test.conf"));
    std::vector<PolicyResultProto> results;
    EXPECT_FALSE(_obj.judge(results, NULL));

    uint64_t policy_ids[] = {123456LU, 351LU, 70032LU, 361LU};
    bool hits[] = {true, false, true, true};
    bool spams[] = {false, false, true, false};
    for (uint32_t i = 0U; i < 4U; ++i) {
        PolicyResultProto re;
        re.set_policy_id(policy_ids[i]);
        re.set_hit(hits[i]);
        results.push_back(re);
        bool spam = false;
        ASSERT_TRUE(_obj.judge(results, &spam));
        EXPECT_EQ(spams[i], spam);
    }
    _obj.uninit();
}

TEST_F(JudgeTestSuite, judge_case_prob) {
    BMOCK_RESUME(rand, int32_t());
    // get prob 0.2, when first and second run.
    // get prob 0.7, when third run
    EXPECT_CALL(BMOCK_OBJECT(rand), rand())
            .WillOnce(Return(1503238553))
            .WillOnce(Return(429496729))
            .WillOnce(Return(429496729))
            .WillOnce(Return(429496729))
            .WillOnce(Return(429496729)) 
            .WillOnce(Return(1503238553)); 

    ASSERT_TRUE(_obj.init("./conf", "judge_test_prob.conf"));
    std::vector<PolicyResultProto> results;
    EXPECT_FALSE(_obj.judge(results, NULL));

    uint64_t policy_ids[] = {123456LU, 351LU, 70032LU, 361LU};
    bool spams1[] = {false, false, true, false};
    for (uint32_t i = 0U; i < 4U; ++i) {
        PolicyResultProto re;
        re.set_policy_id(policy_ids[i]);
        EXPECT_EQ(spams1[i], _obj.judge(re)) << i;
    }
    bool hits[] = {true, false, true, true};
    bool spams2[] = {false, false, true, true};
    for (uint32_t i = 0U; i < 4U; ++i) {
        PolicyResultProto re;
        re.set_policy_id(policy_ids[i]);
        re.set_hit(hits[i]);
        results.push_back(re);
        bool spam = false;
        ASSERT_TRUE(_obj.judge(results, &spam));
        EXPECT_EQ(spams2[i], spam) << i;
    }
    _obj.uninit();
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

