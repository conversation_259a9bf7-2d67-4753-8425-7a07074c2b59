// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
// 
// @File: judge_collector_test.cpp
// @Last modified: 2017-06-30 10:47:18
// @Brief: 

#include <gtest/gtest.h>
#include "judge_collector.h"

namespace anti {
namespace themis {
namespace feature_lib {

class JudgeCollectorTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {}
    virtual void TearDown() {}

private:
    JudgeCollector _obj;
};

TEST_F(JudgeCollectorTestSuite, init_case) {
    EXPECT_FALSE(_obj.init("./conf", "xxxxxx"));


    ASSERT_TRUE(_obj.init("./conf", "products.conf"));
    ASSERT_EQ(2U, _obj._manager_map.size());
    EXPECT_EQ(10L, _obj._manager_map[RecordType::ASP_DISPLAY_LOG]->_threshold);
    
}

TEST_F(JudgeCollectorTestSuite, _init_sigle) {
    // miss #
    ASSERT_TRUE(_obj.init("./conf", "judge_test.conf"));

    ASSERT_EQ(4U, _obj._manager->_policy_infos.size());
}


}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

