// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: judge.cpp
// @Last modified: 2015-06-02 13:35:23
// @Brief: 

#include "judge.h"
#include <boost/lexical_cast.hpp>
#include <boost/algorithm/string.hpp>
#include <ctime>
#include <com_log.h>
#include <Configure.h>

namespace anti {
namespace themis {
namespace feature_lib {

bool Judge::init(const std::string& conf_path, const std::string& conf_file) {
    comcfg::Configure conf;
    if (conf.load(conf_path.data(), conf_file.data()) != 0) {
        CFATAL_LOG("load %s/%s fail", conf_path.data(), conf_file.data());
        return false;
    }

    try {
        _threshold = conf["threshold"].to_int64();
        for (uint64_t i = 0LU; i < conf["policy"].size(); ++i) {
            std::string policies(conf["policy"][i].to_cstr());
            if (!_init_policies(policies)) {
                CWARNING_LOG("call _init_policies(%s) fail", policies.data());
                return false;
            }
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("comcfg::ConfigException : %s", e.what());
        return false;
    }
    // init rand seed
    srand(time(0));
    CWARNING_LOG("judge init success");
    return true;
}

bool Judge::_init_policies(const std::string& policies) {
    std::vector<std::string> pw;
    boost::algorithm::split(pw, policies, boost::is_any_of(", "), boost::token_compress_on);
    for (uint64_t i = 0Lu; i < pw.size(); ++i) {
        std::vector<std::string> policy_fields;
        boost::algorithm::split(policy_fields, pw[i], 
                boost::is_any_of("#"), boost::token_compress_on);
        if (policy_fields.size() < 2) {
            CWARNING_LOG("invalid policy(%s)", pw[i].data());
            return false;
        }
        try {
            uint64_t policy_id = boost::lexical_cast<uint64_t>(policy_fields[0]);
            int32_t weight = boost::lexical_cast<int32_t>(policy_fields[1]);
            const double DEFAULT_PROB = 1.0;
            double prob = DEFAULT_PROB;
            if (policy_fields.size() > 2) {
                prob = boost::lexical_cast<double>(policy_fields[2]);
                if (prob < 0.0 || prob > 1.0) {
                    CWARNING_LOG("invalid policy(%s)", pw[i].data());
                    return false;
                }
            }
            if (!_policy_infos.emplace(policy_id, PolicyInfo(weight, prob)).second) {
                CWARNING_LOG("insert policy_id(%lu) weight(%d), prob(%lf) fail", 
                        policy_id, weight, prob);
                return false;
            }
        } catch (const boost::bad_lexical_cast& e) {
            CWARNING_LOG("boost::bad_lexical_cast : %s", e.what());
            return false;
        }
    }
    return true;
}

bool Judge::judge(const std::vector<PolicyResultProto>& results, bool* spam) const {
    if (spam == NULL) {
        CFATAL_LOG("input spam == NULL");
        return false;
    }

    int64_t w = 0L;
    for (uint64_t i = 0LU; i < results.size(); ++i) {
        if (!results[i].hit()) {
            continue;
        }
        w += _get_policy_weight(results[i]);
    }
    *spam = w > _threshold;
    return true;
}

bool Judge::judge(const PolicyResultProto& result) const {
    return _policy_infos.find(result.policy_id()) != _policy_infos.end() && 
            _get_policy_weight(result) > _threshold;
}

int32_t Judge::_get_policy_weight(const PolicyResultProto& result) const {
    auto iter = _policy_infos.find(result.policy_id());
    if (iter == _policy_infos.end()) {
        CWARNING_LOG("find policy_id(%lu) in judge fail", result.policy_id());
        return 0;
    }
    const PolicyInfo& pinfo = iter->second;
    if (fabs(pinfo.probability - 1.0) < FLT_EPSILON) {
        return pinfo.weight;
    }
    double prob = 1.0 * rand() / RAND_MAX;
    return prob < pinfo.probability ? pinfo.weight : 0;
} 

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

