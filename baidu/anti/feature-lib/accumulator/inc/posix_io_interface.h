// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// @Author: chen<PERSON><PERSON><PERSON>(ch<PERSON><PERSON><PERSON><EMAIL>)
// 
// @File: posix_io_interface.h
// @Last modified: 2017-02-24 00:52:55
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_INC_POSIX_IO_INTERFACE_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_INC_POSIX_IO_INTERFACE_H

namespace anti {
namespace themis {
namespace feature_lib {

class PosixIoReaderInterface {
public:
    PosixIoReaderInterface() {}
    virtual ~PosixIoReaderInterface() {}
    virtual int open(const char *file) = 0;
    virtual int open(const char* file, off_t start_off, int chunksize) = 0;
    virtual int close() = 0;
    virtual ssize_t read(void *buf, size_t size) = 0;
    virtual bool eof() const = 0;
    virtual off_t offset() const = 0;
    virtual bool seek(off_t offset) = 0;
    virtual off_t file_size() = 0;
};

class PosixIoWriterInterface {
public:
    PosixIoWriterInterface() {}
    virtual ~PosixIoWriterInterface() {}
    virtual int open(const char *file) = 0;
    virtual int close() = 0;
    virtual ssize_t write(const void* buf, size_t size) = 0;
    virtual off_t offset() const = 0;
    virtual bool flush() = 0;
};

} // feature_lib
} // themis
} // anti

#endif //APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_INC_POSIX_IO_INTERFACE_H

/* vim: set ts=4 sw=4: */

