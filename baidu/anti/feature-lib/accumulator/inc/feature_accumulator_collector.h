// Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
// @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
//
// @Last modified: 2017-10-26 17:07:01
// @Brief:
// @Usage:
// @Note:

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_INC_FEATURE_ACCUMULATOR_COLLECTOR_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_INC_FEATURE_ACCUMULATOR_COLLECTOR_H

#include <string>
#include <vector>
#include <memory>
#include <Configure.h>
#include "record.pb.h"
#include "feature_accumulator_manager.h"
#include "proto/feature_coordinate.pb.h"

namespace anti {
namespace themis {
namespace feature_lib {

typedef std::shared_ptr<FeatureAccumulatorManager> FeatureAccumulatorManagerPtr;

class FeatureAccumulatorCollector {
public:
    FeatureAccumulatorCollector() : _last_monitor_time(0) {}
    virtual ~FeatureAccumulatorCollector() {}

    bool init(
            const std::string& fea_conf_path,
            const std::string& fea_conf_file);
    bool reload();
    bool load(const std::string& checkpoint_path);
    bool dump(const std::string& checkpoint_path);
    void set_version_id(const std::string& version_id);
    bool update(const FeatureValueProto& fea);
    bool update_and_query(FeatureValueProto* fea);
    void increase_coords(const TaskCoordinate& coordinate);
    bool query(FeatureValueProto* fea);
    bool query(
            const FValQRequestProto& /*request*/, 
            FValQResponseProto* /*respons*/) const;
    void sync(std::vector<std::shared_ptr<FeatureValueProto>>* feas);
    bool get_record_types(std::vector<std::string>* record_types);

private:
    // 监控相关方法
    void _periodic_monitor() const;
    std::string _get_readable_time(time_t timestamp) const;
    bool _should_run_monitor(time_t& last_monitor_time) const;
    const FeatureAccumulatorManagerPtr _const_fea_mgr(anti::themis::RecordType record_type) const;
    FeatureAccumulatorManagerPtr _get_fea_mgr(anti::themis::RecordType record_type);
    FeatureAccumulatorManagerPtr _init_conf(const std::string& path,
            const std::string& file,
            const anti::themis::RecordType record_type);
    FeatureAccumulatorManagerPtr _manager;
    std::map<anti::themis::RecordType, FeatureAccumulatorManagerPtr> _manager_map;

    // 监控时间跟踪
    mutable time_t _last_monitor_time;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_EXTRACTOR_INC_FEATURE_ACCUMULATOR_COLLECTOR_H