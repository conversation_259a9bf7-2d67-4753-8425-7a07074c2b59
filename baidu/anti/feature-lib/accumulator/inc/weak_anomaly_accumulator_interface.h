// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: ch<PERSON><PERSON><PERSON><PERSON>(ch<PERSON><PERSON><PERSON><PERSON>@baidu.com)
// 
// @File: weak_anomaly_accumulator_interface.h
// @Last modified: 2017-12-19 20:02:05
// @Brief: 

#ifndef BAIDU_ANTI_FEATURE_LIB_ACCUMULATOR_INC_WEAK_ANOMALY_ACCUMULATOR_INTERFACE_H
#define BAIDU_ANTI_FEATURE_LIB_ACCUMULATOR_INC_WEAK_ANOMALY_ACCUMULATOR_INTERFACE_H

#include "feature_accumulator_interface.h"
#include "weak_anomaly_manager.h"

namespace anti {
namespace themis {
namespace feature_lib {

// @brief
// interface of a weak anomaly policy
// derived accumulator needs to rewrite private pure virtual function
// weak anomaly manager will trans fea to weak anomaly modle
class WeakAnomalyAccumulatorInterface : public FeatureAccumulatorInterface {
public:
    virtual ~WeakAnomalyAccumulatorInterface() {}
    virtual uint64_t feature_id() const = 0;
    virtual bool init(const comcfg::ConfigUnit& conf);
    virtual void uninit();

    virtual bool update(const FeatureValueProto& fea);
    virtual bool update_and_query(FeatureValueProto* fea);
    virtual bool query(FeatureValueProto* fea) const;
    virtual bool query(
            const FValQRequestProto& /*request*/, 
            FValQResponseProto* /*response*/) const override { 
        return false; 
    }

    virtual bool load(PosixIoReaderInterface *reader);
    virtual bool dump(PosixIoWriterInterface *writer) const;

private:
    std::shared_ptr<WeakAnomalyManager> _weak_ab_mgr;
    virtual bool _init(const comcfg::ConfigUnit& conf) = 0;
    virtual void _uninit() = 0;

    virtual bool _update(const FeatureValueProto& fea) = 0;
    virtual bool _update_and_query(FeatureValueProto* fea) = 0;
    virtual bool _query(FeatureValueProto* fea) const = 0;

    virtual bool _load(PosixIoReaderInterface *reader) = 0;
    virtual bool _dump(PosixIoWriterInterface *writer) const = 0;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif // BAIDU_ANTI_FEATURE_LIB_ACCUMULATOR_INC_WEAK_ANOMALY_ACCUMULATOR_INTERFACE_H

/* vim: set ts=4 sw=4: */

