// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangji<PERSON><PERSON>(<EMAIL>)
//
// @File: feature_accumulator_interface.h
// @Last modified: 2017-12-27 11:30:53
// @Brief:

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_INC_FEATURE_ACCUMULATOR_INTERFACE_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_INC_FEATURE_ACCUMULATOR_INTERFACE_H

#include <string>
#include <Configure.h>
#include "fvalue_rpc_manager.h"
#include "feature_value.pb.h"
#include "posix_io_interface.h"

namespace anti {
namespace themis {
namespace feature_lib {

// 简介：特征累积器接口类
// 功能：向外提供统一的接口update()/query()/update_and_query()
// 输入：FeatureValueProto 、accumulator_feature.conf
// 输出：FeatureValueProto(特征累积后)
class FeatureAccumulatorInterface {
public:
    virtual ~FeatureAccumulatorInterface() {}
    virtual uint64_t feature_id() const = 0;
    virtual bool init(const comcfg::ConfigUnit& conf) = 0;
    virtual bool init(
            const comcfg::ConfigUnit& conf, 
            const std::shared_ptr<FValueRPCManager>& /*mgr*/) {
        return init(conf);
    }
    virtual void uninit() = 0;

    virtual bool update(const FeatureValueProto& fea) = 0;
    virtual bool update_and_query(FeatureValueProto* fea) = 0;
    virtual bool query(FeatureValueProto* fea) const = 0;

    virtual bool load(PosixIoReaderInterface *reader) = 0;
    virtual bool dump(PosixIoWriterInterface *writer) const = 0;

    virtual bool query(
            const FValQRequestProto& /*request*/, 
            FValQResponseProto* /*response*/) const {
        return false;
    }
    virtual void sync(std::vector<std::shared_ptr<FeatureValueProto>>* /*batch_feas*/) {}

    // 打印监控日志
    virtual void print_monitor_log() const {}
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_INC_FEATURE_ACCUMULATOR_INTERFACE_H