// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
//
// @File: feature_accumulator_manager.h
// @Last modified: 2017-10-26 17:07:55
// @Brief: acucumulate feature for each feature_id, which provides provides two mode
//         update_and_query and update*+query*
//          update_and_query for streaming & update*+query* for batch
//         input (ip,1) (ip,2) (ip,3)
//         update_and_query output (ip,1) (ip,2) (ip,3)
//         update*+query* output (ip,3) (ip,3) (ip,3)
// @Usage:
// update_and_query:
//     1. parse FeatureValueProto fea and new FeatureAccumulatorManager fam
//     2. fam->update_and_query(&fea)
//
// upadate* + query*
//     1. parse FeatureValueProtos feas and new FeatureAccumulatorManager fam
//     2. for fea in feas
//     3.    fam->update(fea);
//     4. for fea in feas
//     5.    fam->query(&fea)

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_INC_FEATURE_ACCUMULATOR_MANAGER_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_INC_FEATURE_ACCUMULATOR_MANAGER_H

#include <time.h>
#include <string>
#include <vector>
#include <unordered_map>
#include <mutex>
#include <memory>
#include "fvalue_rpc_manager.h"
#include "feature_value.pb.h"
#include "feature_accumulator_interface.h"
#include "feature_accumulator_factory.h"
#include "task_queue.h"
#include "record.pb.h"

namespace anti {
namespace themis {
namespace feature_lib {
// 简介：特征累积管理器
// 功能：管理所有特征的累积过程, 并依据 feature_type 为每个 feature 提供了一个特征累积器
// @主要用法
// update + query : 适用于批处理数据, 所得的累积值是批数据内所有数据的累积值
// update_and_query : 适用于流处理数据, 所得的累积值是当前 FeatureValueProto 与之前的数据累积值
class FeatureAccumulatorManager {
public:
    FeatureAccumulatorManager() : _modify_time(0LU), _version_id("") {}
    virtual ~FeatureAccumulatorManager() {
        uninit();
    }

    bool init(const std::string& conf_path, const std::string& conf_file);
    void uninit();
    bool reload();

    bool load(const std::string& checkpoint_path);
    bool load(const std::string& checkpoint_path, anti::themis::RecordType record_type);
    bool dump(const std::string& checkpoint_path);
    bool dump(const std::string& checkpoint_path, anti::themis::RecordType record_type);
    void set_version_id(const std::string& version_id) {
        _version_id = version_id;
    }
    std::vector<std::shared_ptr<FeatureAccumulatorInterface>> get_accs();

    bool update(const FeatureValueProto& fea);
    // @Note: pv coord may be change after query
    bool update_and_query(FeatureValueProto* fea);
    // @Note: pv coord may be change after query
    bool query(FeatureValueProto* fea) const;

    void increase_coords(const std::vector<std::pair<uint64_t, int64_t> >& coords);

    bool query(
            const FValQRequestProto& /*request*/, 
            FValQResponseProto* /*respons*/) const;
    void sync(std::vector<std::shared_ptr<FeatureValueProto>>* feas);

private:
    // 添加此处数据结构，是为了替换CoordMap、AccMap
    // 为了防止在并行化的acc调用此处的时候，产生的线程不安全
    struct FeatureAccumulatorAndCoord {
        std::shared_ptr<FeatureAccumulatorInterface> acc_iface;
        int64_t coord;
        FeatureAccumulatorAndCoord() {
            std::shared_ptr<FeatureAccumulatorInterface> default_acc(
                    FeatureAccumulatorFactory::create("segment"));
            acc_iface = default_acc;
            coord = 0;
        }
        FeatureAccumulatorAndCoord(std::shared_ptr<FeatureAccumulatorInterface> iface) 
                : acc_iface(iface)
                , coord(0) {}
    };

    bool _reload();
    bool _init_fprc_mgr();
    time_t _modify_time;
    std::string _conf_path;
    std::string _conf_file;
    std::string _version_id;

    typedef std::unordered_map<uint64_t, int64_t> CoordMap;
    typedef typename CoordMap::iterator CMIter;
    typedef typename CoordMap::const_iterator CMCIter;
    //CoordMap _coords;

    typedef std::unordered_map<uint64_t, 
            std::shared_ptr<FeatureAccumulatorInterface>> AccMap;
    typedef typename AccMap::iterator AMIter;
    typedef typename AccMap::const_iterator AMCIter;
    //AccMap _accs;
    std::shared_ptr<FValueRPCManager> _fprc_mgr;

    typedef std::unordered_map<uint64_t, 
            std::shared_ptr<FeatureAccumulatorAndCoord>> AccCoordMap;
    typedef typename AccCoordMap::iterator ACMIter;
    typedef typename AccCoordMap::const_iterator ACMCIter;
    AccCoordMap _acc_coords;

    void _split_map(CoordMap* coords, AccMap* accs);
    bool _merge_map(const CoordMap& coords, const AccMap& accs);
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_INC_FEATURE_ACCUMULATOR_MANAGER_H