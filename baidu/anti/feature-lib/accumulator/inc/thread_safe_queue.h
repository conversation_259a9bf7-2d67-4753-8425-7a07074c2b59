// Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
// @Author: ch<PERSON><PERSON><PERSON><PERSON>(ch<PERSON><PERSON><PERSON><PERSON>@baidu.com)
// 
// @File: thread_safe_queue.h
// @Last modified: 2017-02-22 12:47:17
// @Brief: this is a simple thread safe queue,
//         provide two pop() 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_THREAD_SAFE_QUEUE_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_THREAD_SAFE_QUEUE_H

#include <mutex>
#include <queue>
#include <memory>

namespace anti {
namespace themis {
namespace feature_lib {

template <typename Item>
class ThreadSafeQueue {
public:
    ThreadSafeQueue() {}
    virtual ~ThreadSafeQueue() {}

    bool empty() {
        std::lock_guard<std::mutex> locker(_mut);
        return _queue.empty();
    }

    void push(const Item &item) {
        std::lock_guard<std::mutex> locker(_mut);
        _queue.push(item);
        return;
    }
    
    bool pop(Item &item) {
        std::lock_guard<std::mutex> locker(_mut);
        if (_queue.empty()) {
            return false;
        }
        item = _queue.front();
        _queue.pop();
        return true;
    }
    std::shared_ptr<Item> pop() {
        std::lock_guard<std::mutex> locker(_mut);
        if (_queue.empty()) {
            return std::shared_ptr<Item>(NULL);
        }
        std::shared_ptr<Item> res(std::make_shared<Item>(_queue.front()));
        _queue.pop();
        return res;
    }
private:
    std::mutex _mut;
    std::queue<Item> _queue;
};

} // feature_lib
} // themis
} // anti

#endif

/* vim: set ts=4 sw=4: */

