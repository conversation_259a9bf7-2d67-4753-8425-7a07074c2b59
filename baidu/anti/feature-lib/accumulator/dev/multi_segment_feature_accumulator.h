// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @brief 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_MULTI_SEGMENT_FEATURE_ACCUMULATOR_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_MULTI_SEGMENT_FEATURE_ACCUMULATOR_H

#include "feature_accumulator_interface.h"
#include "item.h"
#include "multi_segment.h"

namespace anti {
namespace themis {
namespace feature_lib {

class MultiSegmentAccumulator : public FeatureAccumulatorInterface {
private:
    typedef anti::themis::common_lib::SegmentItem SegmentItem;
    typedef anti::themis::common_lib::MultiSegment<SegmentItem> MultiSegment;

public:
    MultiSegmentAccumulator();
    virtual ~MultiSegmentAccumulator();

    virtual uint64_t feature_id() const { return _feature_id; }

    virtual bool init(const comcfg::ConfigUnit& conf);
    virtual void uninit();

    virtual bool update(const FeatureValueProto& fea);
    virtual bool update_and_query(FeatureValueProto* fea);
    virtual bool query(FeatureValueProto* fea) const;

    virtual bool load(PosixIoReaderInterface *reader);
    virtual bool dump(PosixIoWriterInterface *writer) const;
private:
    bool _check_feature(const FeatureValueProto& fea) const;
    void _fillup_feature(const SegmentItem* ptr, FeatureValueProto* fea) const;
   
    uint64_t _feature_id;
    uint64_t _version;
    MultiSegment _segment;
};

}
}
}

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_MULTISEGMENT_FEATURE_ACCUMULATOR_H
