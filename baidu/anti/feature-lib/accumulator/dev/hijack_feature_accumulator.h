// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @brief 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_HIJACK_FEATURE_ACCUMULATOR_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_HIJACK_FEATURE_ACCUMULATOR_H

#include <memory>
#include <com_log.h>
#include "goe_list.hpp"
#include "utils.h"
#include "feature_accumulator_interface.h"

namespace anti {
namespace themis {
namespace feature_lib {

static const int32_t kNodeBufLen = 128;

struct StrBuf {
    char buf[kNodeBufLen];
};

bool to_buf(const std::string& str, StrBuf* buf);
bool to_str(const StrBuf& buf, std::string* str);

struct HjSecondElement {
    HjSecondElement(uint64_t sign, int64_t ms, 
            uint64_t uniq_id, uint32_t page_no,
            const std::string& cn,
            const std::string& flow_group) : 
            sign(sign),
            time_ms(ms),
            uniq_id(uniq_id),
            page_no(page_no),
            cn(cn),
            flow_group(flow_group) {}

    HjSecondElement(const HjSecondElement& rhs) {
        sign = rhs.sign;
        time_ms = rhs.time_ms;
        uniq_id = rhs.uniq_id;
        page_no = rhs.page_no;
        cn = rhs.cn;
        flow_group = rhs.flow_group;
    }

    HjSecondElement& operator=(const HjSecondElement& rhs) {
        sign = rhs.sign;
        time_ms = rhs.time_ms;
        uniq_id = rhs.uniq_id;
        page_no = rhs.page_no;
        cn = rhs.cn;
        flow_group = rhs.flow_group;

        return *this;
    }

    bool operator==(const HjSecondElement& rhs) {
        return sign == rhs.sign &&
            time_ms == rhs.time_ms &&
            uniq_id == rhs.uniq_id &&
            page_no == rhs.page_no &&
            cn == rhs.cn &&
            flow_group == rhs.flow_group;
    }

    bool operator!=(const HjSecondElement& rhs) {
        return !(*this == rhs);
    }

    uint64_t sign;
    int64_t time_ms;
    uint64_t uniq_id;
    uint32_t page_no;
    std::string cn;
    std::string flow_group;

    template<typename Archive>
    bool serialize(Archive* ar) const {
        DUMP_VAR(sign, ar, uint64_t);
        DUMP_VAR(time_ms, ar, int64_t);
        DUMP_VAR(uniq_id, ar, uint64_t);
        DUMP_VAR(page_no, ar, uint32_t);

        StrBuf buf[2];
        if (!to_buf(cn, &buf[0]) || !to_buf(flow_group, &buf[1])) {
            CFATAL_LOG("convert string failed");
            return false;
        }
        DUMP_VAR(buf[0], ar, StrBuf);
        DUMP_VAR(buf[1], ar, StrBuf);
        return true;
    }

    template<typename Archive>
    bool deserialize(Archive* ar) {
        LOAD_VAR(&sign, ar, uint64_t);
        LOAD_VAR(&time_ms, ar, int64_t);
        LOAD_VAR(&uniq_id, ar, uint64_t);
        LOAD_VAR(&page_no, ar, uint32_t);

        StrBuf buf[2];
        LOAD_VAR(&buf[0], ar, StrBuf);
        LOAD_VAR(&buf[1], ar, StrBuf);

        if (!to_str(buf[0], &cn) || !to_str(buf[1], &flow_group)) {
            CWARNING_LOG("convert buf to string failed");
            return false;
        }

        return true;
    }
};  

class HjSecondNode {
public:
    HjSecondNode(uint64_t sign, int64_t ms, 
            uint64_t id, uint32_t page_no, 
            const std::string& cn,
            const std::string& flow_group) :
            _start(sign, ms, id, page_no, cn, flow_group),
            _end(sign, ms, id, page_no, cn, flow_group) {}

    HjSecondNode() : HjSecondNode(0, 0, 0, 0, "", "") {}
    ~HjSecondNode() {}

    int64_t coord() const { return _start.time_ms / 1000; }

    HjSecondElement& start() {
        return _start;
    }

    HjSecondElement& end() {
        return _end;
    }

    uint64_t sign() const {
        return _start.sign;
    }

    HjSecondNode& operator+=(const HjSecondNode& rhs) {
        if (rhs.coord() == coord() && rhs.sign() == sign())  {
            if (rhs._end.time_ms > _end.time_ms) {
                _end = rhs._end;
            }
            if (rhs._start.time_ms < _start.time_ms) {
                _start = rhs._start;
            }
        }
        return *this;
    }

    bool operator==(const HjSecondNode& rhs) {
        return _start == rhs._start && _end == rhs._end;
    }

    bool is_single_point() {
        return _start == _end;
    }

    template<typename Archive>
    bool serialize(Archive* ar) const {
        return _start.serialize(ar) && _end.serialize(ar);
    }

    template<typename Archive>
    bool deserialize(Archive* ar) {
        return _start.deserialize(ar) && _end.deserialize(ar);
    }

private:
    HjSecondElement _start;
    HjSecondElement _end;
};

HjSecondNode make_hjnode(const FeatureValueProto& feature);

class HijackAccumulator : public FeatureAccumulatorInterface {
public:
    HijackAccumulator();
    virtual ~HijackAccumulator();

    virtual uint64_t feature_id() const { return _feature_id; }
    virtual bool init(const comcfg::ConfigUnit& conf);
    virtual void uninit();

    virtual bool update(const FeatureValueProto& fea);
    virtual bool query(FeatureValueProto* fea) const;

    virtual bool update_and_query(FeatureValueProto*);

    virtual bool load(PosixIoReaderInterface *reader);
    virtual bool dump(PosixIoWriterInterface *writer) const;

private:
    bool check_feature(const FeatureValueProto& fea) const;

private:
    typedef common_lib::GOEList<HjSecondNode> GOEList;

    uint64_t _feature_id;
    uint64_t _version;
    uint64_t _range;
    std::shared_ptr<GOEList> _goe_list;
};

}
}
}

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_HIJACK_FEATURE_ACCUMULATOR_H
