#include <nlpc_client.h>
#include <nlpc/ver_1_0_0/lego2_input.h>
#include <nlpc/ver_1_0_0/lego2_output.h>
#include "nlpc_client_manager.h"
#include "intention_node.h"

namespace anti {
namespace themis {
namespace feature_lib {

DEFINE_int32(max_query, 20, "max query for each intention");
DEFINE_int32(max_intention, 10, "max intention");

using namespace nlpc::ver_1_0_0;
using namespace drpc;

typedef ::drpc::NLPCClient NLPCClient;
typedef ::nlpc::ver_1_0_0::lego2_inputPtr lego2_inputPtr;
typedef ::nlpc::ver_1_0_0::lego2_outputPtr lego2_outputPtr;
typedef ::nlpc::ver_1_0_0::lego2_pairPtr lego2_pairPtr;

bool IntentionNode::_calc_similar(const std::string& in_str1, const std::string& in_str2,
        float* value) {
    NLPCClient* client = common_lib::NLPCClientManagerSingleton::instance().
                                 get_nlpc_client("nlpc_simnet_bow_1106");
    if (client == NULL) {
        CFATAL_LOG("fail to get nlpc_client!");
        return false;
    }

    lego2_inputPtr input_ptr;
    lego2_outputPtr output_ptr;
    try {
        input_ptr = ::sofa::create<lego2_input>();
        output_ptr = ::sofa::create<lego2_output>();
    } catch (std::exception& ex) {
        CWARNING_LOG("create input and output failed !!![%s]", ex.what());
        return false;
    }
    if (input_ptr == NULL || output_ptr == NULL) {
        CFATAL_LOG("input/output ptr is NULL!");
        return false;
    }
    lego2_pairPtr pair = ::sofa::create<lego2_pair>();
    pair->set_text_1(in_str1);
    pair->set_text_2(in_str2);
    input_ptr->texts().push_back(pair);
        
    std::string str_input, str_output;
    if (!input_ptr->to_binary(&str_input)) {
        CWARNING_LOG("serialize failed");
        return false;
    }

    int ret = client->call_method(str_input, str_output);
    if (0 != ret) {
        CFATAL_LOG("call method failed ret:%d", ret);
        return false;
    }

    if (!output_ptr->from_binary(str_output)) {
        CFATAL_LOG("deserialize failed");
        return false;
    }

    *value = output_ptr->results()[0]->items()[0]->score();
    CDEBUG_LOG("similar result:%f", *value);
    return true;
}

bool IntentionNode::add(const IntentionNode& node, const int64_t& /*segment_len*/) {
    for (const auto row_ptr : *(node.intentions())) {
        for (const auto unit : *row_ptr) {
            if (!_add_query_to_intentions(unit)) {
                CFATAL_LOG("fail to add query to intention.");
                return false;
            }
        }
    }
    // maybe disorder node, _update_time is latest
    if (_update_time < node.update_time()) {
        CWARNING_LOG("node.update_time(%ld) > update_time(%ld), update update_time.", node.coord, _update_time);
        _update_time = node.update_time();
    }
    return true;
}

bool IntentionNode::_add_query_to_intentions(QueryUnitPtr query_unit_ptr) {
    CHECK_PTR(query_unit_ptr);
    std::vector<IntentionListIter> merge_vec;
    for (auto it = _intent_list_ptr->begin(); it != _intent_list_ptr->end(); ++it) {
        auto& row = **it;
        for (const auto unit : row) {
            // extend_view_value equal || similar > threshold, push i to merge_vec.
            int64_t extend_value = query_unit_ptr->extend_view_value;
            if (extend_value != -1 && extend_value == unit->extend_view_value) {
                merge_vec.push_back(it);
                CDEBUG_LOG("tradeid equal, push merge, extend_value:%ld", extend_value);
                break;
            }
            float similar = 0;
            std::string query = query_unit_ptr->query;
            if (!_calc_similar(query, unit->query, &similar)) {
                CFATAL_LOG("fail to calc similar, input str1:%s, str2:%s",
                        query.c_str(), unit->query.c_str());
                return false;
            }
            if (similar > _threshold) {
                merge_vec.push_back(it);
                CDEBUG_LOG("similar > threshold, push iterator to merge_vec, similar:%f, threshold:%f, merge_vec size:%d",
                        similar, _threshold, merge_vec.size());
                break;
            }
        }
    }

    if (merge_vec.size() == 0) {
        QueryUnitListPtr list_ptr(new(std::nothrow) QueryUnitList());
        list_ptr->emplace_back(query_unit_ptr);
        _intent_list_ptr->emplace_front(list_ptr);
        _intent_count++;
        if (_intent_count > FLAGS_max_intention) {
            //clear oldest intention
            _intent_list_ptr->pop_back();
            --_intent_count;
            CDEBUG_LOG("the size of intentions more than max_intention, pop oldest intentions");
        }
    } else if (merge_vec.size() == 1) {
        if (!_add_query_to_one_intention(merge_vec, query_unit_ptr)) {
            CWARNING_LOG("fail to add query to intention group.");
            return false;
        }
    } else {
        if (!_merge_intention_group(merge_vec, query_unit_ptr)) {
            CWARNING_LOG("fail to merge intention gorup.");
            return false;
        }
    }
    return true;
}

bool compare(const QueryUnitPtr& r_value, const QueryUnitPtr& l_value) {
    CHECK_PTR(l_value);
    return r_value->update_time > l_value->update_time;
}

bool IntentionNode::_merge_intention_group(const std::vector<IntentionListIter>& merge_vec,
        const QueryUnitPtr query_unit_ptr) {
    CHECK_PTR(query_unit_ptr);
    if (merge_vec.size() <= 1) {
        CWARNING_LOG("intention don't need merge.");
        return true;
    }
    auto list_ptr = *(merge_vec[0]);
    for (size_t i = 1; i < merge_vec.size(); ++i) {
        (*list_ptr).merge(**(merge_vec[i]), compare);
        _intent_list_ptr->erase(merge_vec[i]);
        _intent_count--;
    }
    
    if (!_add_query_to_one_intention(merge_vec, query_unit_ptr)) {
        CFATAL_LOG("fail to add query to intention group, query:%s", query_unit_ptr->query.c_str());
        return false;
    }
    return true;
}

bool IntentionNode::_add_query_to_one_intention(const std::vector<IntentionListIter>& merge_vec,
        const QueryUnitPtr query_unit_ptr) {
    auto row_ptr = *(merge_vec[0]);
    CHECK_PTR(query_unit_ptr);
    CHECK_PTR(row_ptr);
    auto it = row_ptr->begin();
    while ((it != row_ptr->end()) && (*it)->update_time > query_unit_ptr->update_time) {
        it++;
    }
    row_ptr->insert(it, query_unit_ptr);
    //move update intention to list head
    _intent_list_ptr->push_front(row_ptr);
    _intent_list_ptr->erase(merge_vec[0]);
    //pop oldest query
    int32_t diff = row_ptr->size() - FLAGS_max_query;
    if (diff > 0) {
        while (diff--) {
            row_ptr->pop_back();
        }
    }
    return true;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

