// Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
// @Author: langwen<PERSON>(<EMAIL>)
// 
// @File: fea_value_cdf_accumulator.cpp
// @Brief:
//

#include <sstream>
#include <boost/lexical_cast.hpp>
#include "fea_value_cdf_accumulator.h"

namespace anti {
namespace themis {
namespace feature_lib {

inline bool response_check(const FValQInfoProto& info) {
    return info.has_response()
            && info.response().value_size() == 1;
}

inline const FValQResponseProto* fetch_response(
        const std::shared_ptr<FValQueryTransProto>& req, uint64_t feature_id) {
    for (int32_t i = 0; i < req->infos_size(); ++i) {
        if (req->infos(i).request().feature_id() == feature_id) {
            return &req->infos(i).response();
        }
    }
    return NULL;
}

uint64_t FeatureValueCdfAccumulator::_get_extend_view_sign(const FeatureValueProto& fea) const {
    return fea.fea_serv_trans().infos(0).request().view_sign();
}
bool FeatureValueCdfAccumulator::_init(const comcfg::ConfigUnit& conf) {
    try {
        if (conf["extend"].size() != 1) {
            CWARNING_LOG("invalid extend config size(%d), should be (%d), feature_id(%lu)", 
                    conf["extend"].size(), 1, feature_id());
            return false;
        }
        _extend_conf.fea_id = conf["extend"][0]["feature_id"].to_uint64();
        _cdf_conf.step = conf["step"].to_uint32();
        _cdf_conf.ratio = conf["ratio"].to_double();
        bool ok = _window->register_extra_gc_func([this](const GroupViewItem& node){
                    auto value_ptr = node.value();
                    if (!value_ptr) {
                        return; 
                    }
                    this->_dis.decrease_coord(value_ptr->get_view_val(), 1);
                });
        if (!ok) {
            CWARNING_LOG("register extra gc func failed!");
            return false;
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what()); 
        return false; 
    } catch (const std::exception& e) {
        CWARNING_LOG("catch std::exception : %s", e.what());
        return false;
    }
    return true;
}

bool FeatureValueCdfAccumulator::update(const FeatureValueProto& fea) {
    if (!_check_fea(fea)) {
        CWARNING_LOG("check fea failed!");
        return false;
    }
    uint64_t extend_view_sign = _get_extend_view_sign(fea);
    auto node_ptr = _enter_window(extend_view_sign, fea.coord());
    if (!node_ptr) {
        CWARNING_LOG("enter node to window fail, view_sign(%lu), feature_id(%lu)",
                fea.view_sign(), feature_id());
        return false;
    }
    _triger_query(fea, *node_ptr);
    return true;
}

void FeatureValueCdfAccumulator::_triger_query(
        const FeatureValueProto& fea,
        const GroupViewItem& node) {
    if (node.value()->get_appear_count() < _cdf_conf.step) {
        return;
    }
    node.value()->clear_appear_count();
    uint64_t extend_view_sign = _get_extend_view_sign(fea);
    if (_val_query_map.find(extend_view_sign) != _val_query_map.end()) {
        return; 
    }
    ValueQuery req(new (std::nothrow)FValQueryTransProto);
    req->CopyFrom(fea.fea_serv_trans());
    req->mutable_infos(0)->mutable_request()->clear_value();
    _val_query_map[extend_view_sign] = req; 
    _rpc_mgr->query(req);
}

bool FeatureValueCdfAccumulator::query(FeatureValueProto* fea) const {
    if (!fea || !_check_fea(*fea)) {
        CWARNING_LOG("input fea invalid, feature_id(%lu)", feature_id());
        return false;
    }
    if (!_window) {
        CFATAL_LOG("window ptr is NULL, call init first, feature_id(%lu)", feature_id());
        return false;
    }
    uint64_t extend_view_sign = _get_extend_view_sign(*fea); 
    auto node = _window->find(extend_view_sign);
    if (!node || !node->value()) {
        CDEBUG_LOG("query for view_sing(%lu) fail, feature_id(%lu)",fea->view_sign(), feature_id());
        return false;
    }
    std::shared_ptr<ViewItem> cdf_item_ptr = node->value();
    double cdf_val = 0.0;
    bool ok = _dis.get_cdf_val(cdf_item_ptr->get_view_val(), &cdf_val); 
    if (!ok) {
        CDEBUG_LOG("get_cdf_val failed!");
        return false;
    }
    // 只保留小数点后两位
    std::stringstream ss; 
    ss.precision(2);
    ss << cdf_val;
    fea->set_value(std::move(ss.str()));
    return true;
}

bool FeatureValueCdfAccumulator::update_and_query(FeatureValueProto* fea) {
    if (!update(*fea)) {
        CWARNING_LOG("update fea failed!");
        return false;
    }
    if (!query(fea)) {
        CWARNING_LOG("query fea failed!");
        return false;
    }
    return true;
}

std::shared_ptr<GroupViewItem> FeatureValueCdfAccumulator::_enter_window(
        const uint64_t view_sign,
        const uint64_t coord) {
    std::shared_ptr<GroupViewItem> node(new (std::nothrow) GroupViewItem(view_sign, coord));
    if (!node) {
        CFATAL_LOG("new node obj fail, feature_id(%lu)", feature_id());
        return nullptr;
    }

    auto old_node = _window->find(view_sign);
    if (old_node) {
        node->set_latest_extend(old_node->latest_extend());
        node->set_value(old_node->value());
        node->value()->increase_appear_count();
    } else {
        std::shared_ptr<ViewItem> item(new (std::nothrow) ViewItem); 
        if (!item) {
            CFATAL_LOG("new tiem failed! memory not enough!");
            return nullptr;
        }
        item->increase_appear_count();
        node->set_value(item);
    }
    _window->insert(node);
    return node;
}

bool FeatureValueCdfAccumulator::_check_fea(const FeatureValueProto& fea) const {
    if (fea.feature_id() != feature_id()) {
        CWARNING_LOG("input fea id invalid, cur fea id:%lu, shouble be:%lu", 
                fea.feature_id(), 
                feature_id());
        return false;
    }
    if (fea.feature_type() != FeatureValueProto::FEATURE_VALUE_EXTEND) {
        CWARNING_LOG("input fea type invalid, cur fea type:%s, shouble be:%s", 
                FeatureValueProto_FeatureType_Name(fea.feature_type()).data(), 
                FeatureValueProto_FeatureType_Name(FeatureValueProto::FEATURE_VALUE_EXTEND).data());
        return false;
    }
    if (!fea.has_fea_serv_trans()) {
        CWARNING_LOG("input fea does not have fea_serv_trans");
        return false;
    }
    if (fea.fea_serv_trans().infos_size() != 1) {
        CWARNING_LOG("input fea serv trans's infos size is %d, should be 1", fea.fea_serv_trans().infos_size());
        return false;
    }
    if (!fea.fea_serv_trans().infos(0).has_request()) {
        CWARNING_LOG("request not exist!");
        return false;
    }
    if (!fea.fea_serv_trans().infos(0).request().has_value()) {
        CWARNING_LOG("request does not have value!");
        return false;
    }
    return true;
}

void FeatureValueCdfAccumulator::sync(std::vector<std::shared_ptr<FeatureValueProto>>* /*feas*/) {
    for (auto& iter : _val_query_map) {
        std::shared_ptr<FValQueryTransProto> query = iter.second;
        if (!response_check(query->infos(0))) {
            CDEBUG_LOG("invalid response[%s]", iter.second->ShortDebugString().data());
            continue;
        }
        auto response = fetch_response(iter.second, _extend_conf.fea_id);
        int64_t val = 0;
        try {
            double dbl_val = (boost::lexical_cast<double>(response->value(0)) * _cdf_conf.ratio);
            val = static_cast<int64_t>(dbl_val);
        } catch (const boost::bad_lexical_cast& e) {
            CWARNING_LOG("boost::Exception : %s", e.what());
            continue;
        }
        const uint64_t extend_view_sign = iter.first;
        auto node = _window->find(extend_view_sign);
        if (!node) {
            CFATAL_LOG("find node of view_sign(%lu) fail, feature_id(%lu)", iter.first, feature_id());
            continue;
        }
        auto item_ptr = node->value();
        int64_t old_val = item_ptr->get_view_val();
        item_ptr->set_view_val(val);
        if (val == old_val) {
            continue;
        }
        _dis.increase_coord(val, 1);
        _dis.decrease_coord(old_val, 1);
    }
    _val_query_map.clear();
    bool ok = _dis.exec_batch_update();
    if (!ok) {
        CWARNING_LOG("exec batch updaate failed!"); 
    }
}

bool FeatureValueCdfAccumulator::load(PosixIoReaderInterface *reader) {
    bool ok = FeatureValueCdfAccBase::load(reader);
    if (!ok) {
        CFATAL_LOG("base load failed!");
        return false;
    }
    ok = _dis.deserialize(reader);
    if (!ok) {
        CFATAL_LOG("load _dis failed!");
        return false;
    }
    return true;
}
bool FeatureValueCdfAccumulator::dump(PosixIoWriterInterface *writer) const {
    bool ok = FeatureValueCdfAccBase::dump(writer);
    if (!ok) {
        CFATAL_LOG("base dump failed!");
        return false;
    }
    ok = _dis.serialize(writer);
    if (!ok) {
        CFATAL_LOG("dump _dis failed!");
        return false;
    }
    return true;
}

}
}
}
