// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: count_distribution_feature_accumulator.h
// @Last modified: 2018-01-17 21:18:14
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_COUNT_DISTRIBUTION_FEATURE_ACCUMULATOR_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_COUNT_DISTRIBUTION_FEATURE_ACCUMULATOR_H

#include "feature_accumulator_interface.h"
#include <click_node.hpp>
#include <click_segment.hpp>
#include "distribution_distance.h"

namespace anti {
namespace themis {
namespace feature_lib {

class CountDistributionFeatureAccumulator : public FeatureAccumulatorInterface {
public:
    CountDistributionFeatureAccumulator() : 
            _feature_id(0L), 
            _version(0L) {} 
    virtual ~CountDistributionFeatureAccumulator() {
        uninit();
    }

    virtual uint64_t feature_id() const {
        return _feature_id;
    }
    virtual bool init(const comcfg::ConfigUnit& conf);
    virtual void uninit();

    virtual bool update(const FeatureValueProto& fea);
    virtual bool update_and_query(FeatureValueProto* fea);
    virtual bool query(FeatureValueProto* fea) const;

    virtual bool load(PosixIoReaderInterface *reader);
    virtual bool dump(PosixIoWriterInterface *writer) const;

private:
    bool _init_intervals(const std::string& points);
    bool _check_feature(const FeatureValueProto& fea) const;

    typedef anti::themis::common_lib::SegLimitNode CountNode;
    typedef anti::themis::common_lib::ClickSegment<CountNode> CountSegment;
    typedef anti::themis::common_lib::DistributionLimitNode DistributionLimitNode;
    typedef anti::themis::common_lib::ClickSegment<DistributionLimitNode> DistributionSegment;

    bool _update(const FeatureValueProto& fea, const DistributionLimitNode** node, uint32_t* idx);
    bool _get_interval_idx(int64_t cumulant, uint32_t* idx) const;
    bool _set_feature(
            const DistributionLimitNode* node, uint32_t idx, FeatureValueProto* fea) const;

    uint64_t _feature_id;
    uint64_t _version;
    CountSegment _count_seg;
    DistributionSegment _dist_seg;
    std::vector<std::pair<int64_t, int64_t> > _intervals;
    std::shared_ptr<DistributionDistance> _distribution_distance;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_COUNT_DISTRIBUTION_FEATURE_ACCUMULATOR_H


/* vim: set ts=4 sw=4: */

