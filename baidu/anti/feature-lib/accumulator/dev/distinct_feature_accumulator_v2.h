// Copyright 2025 Baidu Inc. All Right Reserved.
// Author <PERSON> (<EMAIL>)
// 
// brief: V2版本的DistinctFeatureAccumulator，使用内存池优化

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_DISTINCT_FEATURE_ACCUMULATOR_V2_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_DISTINCT_FEATURE_ACCUMULATOR_V2_H

#include <thread>
#include <vector>
#include "feature_accumulator_interface.h"
#include "item_v2.h"
#include "sliding_window_v2.hpp"
#include "gc_slab_mempool_32.h"

namespace anti {
namespace themis {
namespace feature_lib {

DECLARE_bool(need_set_distinct_data_view_signs);
DECLARE_int32(max_distinct_data_view_signs_num);
DECLARE_int32(pool_mempool_block_item_num);

/**
 * @brief DistinctFeatureAccumulator的V2版本，使用内存池优化
 * 
 * 主要优化：
 * 1. 使用SlidingWindowV2替代原版SlidingWindow
 * 2. 使用DistinctItemV2替代原版DistinctItem
 * 3. 内存池由FeatureAccumulator管理，提供更好的内存利用率
 * 4. 保持与原版完全相同的外部接口和语义
 */
class DistinctFeatureAccumulatorV2 : public FeatureAccumulatorInterface {
public:
    DistinctFeatureAccumulatorV2() : 
            _feature_id(0LU), 
            _version(0LU) {}

    virtual ~DistinctFeatureAccumulatorV2() {
        uninit();
    }

    virtual uint64_t feature_id() const override {
        return _feature_id;
    }

    virtual bool init(const comcfg::ConfigUnit& conf) override;
    virtual void uninit() override;

    virtual bool update(const FeatureValueProto& fea) override;
    virtual bool update_and_query(FeatureValueProto* fea) override;
    virtual bool query(FeatureValueProto* fea) const override;

    virtual bool load(PosixIoReaderInterface *reader) override;
    virtual bool dump(PosixIoWriterInterface *writer) const override;
    
    /**
     * @brief 查询接口，支持详细的data_view_sign列表返回
     */
    bool query(
            const FValQRequestProto& request, 
            FValQResponseProto* response) const;

    /**
     * @brief 打印监控日志，包含内存池使用情况
     */
    virtual void print_monitor_log() const override;

private:
    typedef anti::themis::common_lib::DistinctItemV2 DistinctItem;
    typedef anti::themis::common_lib::GCSlabMempool32 MemPoolType;
    typedef anti::themis::common_lib::SlidingWindowV2<DistinctItem> SlidingWindow;

    /**
     * @brief 初始化内存池
     * @return 成功返回true，失败返回false
     */
    bool _init_memory_pool();

    /**
     * @brief 检查特征有效性
     */
    bool _check_feature(const FeatureValueProto& fea) const;

    /**
     * @brief 填充特征响应
     */
    bool _fillup_feature(
            const DistinctItem* item, 
            FeatureValueProto* fea) const;


    uint64_t _feature_id;           // 特征ID
    uint64_t _version;              // 版本号
    MemPoolType _mem_pool;          // 内存池，由累积器管理生命周期
    SlidingWindow _pool_window;     // 基于内存池的滑动窗口
    mutable std::mutex _mutex;      // 线程安全锁
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_DISTINCT_FEATURE_ACCUMULATOR_V2_H