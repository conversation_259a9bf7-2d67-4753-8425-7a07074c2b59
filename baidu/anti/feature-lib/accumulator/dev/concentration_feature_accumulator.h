// Copyright 2015 Baidu Inc. All Right Reserved.
// Author <PERSON> (<EMAIL>)
// 
// brief: 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_CONCENTRATION_FEATURE_ACCUMULATOR_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_CONCENTRATION_FEATURE_ACCUMULATOR_H

#include "feature_accumulator_interface.h"
#include <item.h>
#include <sliding_window.h>
#include <mutex>
#include <click_segment.hpp>

namespace anti {
namespace themis {
namespace feature_lib {
class ConcentrationFeatureAccumulator : public FeatureAccumulatorInterface {
public:
    ConcentrationFeatureAccumulator() : 
            _feature_id(0LU), 
            _version(0LU),
            _remain(0L),
            _remain_numerator(false),
            _acc_main_view(false),
            _top(0UL),
            _need_top_data_views(false) {
    }

    virtual ~ConcentrationFeatureAccumulator() {
        uninit();
    }

    virtual uint64_t feature_id() const {
        return _feature_id;
    }
    virtual bool init(const comcfg::ConfigUnit& conf);
    virtual void uninit();

    virtual bool update(const FeatureValueProto& fea);
    virtual bool update_and_query(FeatureValueProto* fea);
    virtual bool query(FeatureValueProto* fea) const;
    virtual bool query(const FValQRequestProto& request, FValQResponseProto* response) const;

    virtual bool load(PosixIoReaderInterface *reader);
    virtual bool dump(PosixIoWriterInterface *writer) const;

private:
    typedef anti::themis::common_lib::ConItem ConItem;
    typedef anti::themis::common_lib::SegLimitNode SegLimitNode;
    typedef anti::themis::common_lib::SlidingWindow<ConItem> SlidingWindow;
    typedef anti::themis::common_lib::ClickSegment<SegLimitNode> ClickSegment;
    typedef anti::themis::common_lib::ValueCumNode ValueCumNode;

    bool _init_con_item(ConItem* item, const FeatureValueProto& fea); 
    bool _check_feature(const FeatureValueProto& fea) const;
    bool _fillup_feature(
            const ConItem* item, 
            const SegLimitNode* seg_item,
            FeatureValueProto* fea) const;
    uint64_t _feature_id;
    uint64_t _version;
    SlidingWindow _window;
    ClickSegment _segment;
    int64_t _remain;
    bool _remain_numerator;
    bool _acc_main_view;
    uint64_t _top;
    mutable std::mutex _mutex;
    bool _need_top_data_views;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_CONCENTRATION_FEATURE_ACCUMULATOR_H
