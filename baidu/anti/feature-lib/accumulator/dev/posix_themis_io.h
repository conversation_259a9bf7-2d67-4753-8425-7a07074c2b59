// Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
// @Author: ch<PERSON><PERSON><PERSON><PERSON>(ch<PERSON><PERSON><PERSON><PERSON>@baidu.com)
// 
// @File: posix_themis_io.h
// @Last modified: 2017-02-24 00:27:56
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_FEATRUE_LIB_ACCUMULATOR_DEV_POSIX_THEMIS_IO_H
#define APP_ECOM_ANTI_THEMIS_FEATRUE_LIB_ACCUMULATOR_DEV_POSIX_THEMIS_IO_H

#include "posix_io_interface.h"
#include "tio_interface.h"
#include "tio_factory.h"

namespace anti {
namespace themis {
namespace feature_lib {

class PosixThemisIoReader : public PosixIoReaderInterface {
public:
    PosixThemisIoReader() {
        _reader.reset(anti::themis::io_lib::TioFactory::create_reader());
    }
    PosixThemisIoReader(int io_type) {
        _reader.reset(anti::themis::io_lib::TioFactory::create_reader(io_type));
    }
    int open(const char *file) {
        return _reader->open(file) ? 0 : -1;
    }
    int open(const char* file, off_t start_off, int chunksize) {
        return _reader->open(file, start_off, chunksize) ? 0 : -1;
    }
    int close() {
        _reader->close();
        return 0;
    }
    ssize_t read(void *buf, size_t size) {
        int nread = 0U;
        if (!_reader->read(buf, size, &nread)) {
            return -1;
        }
        return static_cast<ssize_t>(nread);
    }
    bool eof() const {
        return _reader->eof();
    }
    off_t offset() const {
        return _reader->offset();
    }
    bool seek(off_t offset) {
        return _reader->seek(offset);
    }
    off_t file_size() {
        return _reader->file_size();
    }
private:
    std::shared_ptr<anti::themis::io_lib::ReaderInterface> _reader;
};

class PosixThemisIoWriter : public PosixIoWriterInterface {
public:
    PosixThemisIoWriter() {
        _writer.reset(anti::themis::io_lib::TioFactory::create_writer());    
    }
    PosixThemisIoWriter(int io_type) {
        _writer.reset(anti::themis::io_lib::TioFactory::create_writer(io_type));    
    }
    PosixThemisIoWriter(int io_type, int compress_type) {
        _writer.reset(anti::themis::io_lib::TioFactory::create_writer(
                io_type, compress_type));
    }
    int open(const char *file) {
        return _writer->open(file) ? 0 : -1;
    }
    int close() {
        _writer->close();
        return 0;
    }
    ssize_t write(const void *buf, size_t size) {
        if (!_writer->write(buf, static_cast<int>(size))) {
            return -1;
        }
        return static_cast<ssize_t>(size);
    }
    off_t offset() const {
        return _writer->offset();
    }
    bool flush() {
        return _writer->flush();
    }
private:
    std::shared_ptr<anti::themis::io_lib::WriterInterface> _writer;
};
} // feature_lib
} // themis
} // anti

#endif // APP_ECOM_ANTI_THEMIS_FEATRUE_LIB_ACCUMULATOR_DEV_POSIX_THEMIS_IO_H

/* vim: set ts=4 sw=4: */

