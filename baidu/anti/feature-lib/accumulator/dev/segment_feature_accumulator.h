// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
//
// @File: segment_feature_accumulator.h
// @Last modified: 2017-11-20 19:18:25
// @Brief:

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_SEGMENT_FEATURE_ACCUMULATOR_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_SEGMENT_FEATURE_ACCUMULATOR_H

#include <mutex>
#include "feature_accumulator_interface.h"
#include <item.h>
#include <pool_sliding_window.h>
#include <sliding_window.h>
#include <click_segment.hpp>

namespace anti {
namespace themis {
namespace feature_lib {

class SegmentFeatureAccumulator : public FeatureAccumulatorInterface {
private:
    using SegmentItem = anti::themis::common_lib::SegmentItem;
    using StdSlidingWindow = anti::themis::common_lib::SlidingWindow<SegmentItem>;
    using PoolSlidingWindow = anti::themis::common_lib::PoolSlidingWindow<SegmentItem>;
    using SegLimitNode = anti::themis::common_lib::SegLimitNode;
    using ClickSegment = anti::themis::common_lib::ClickSegment<SegLimitNode>;

public:
    SegmentFeatureAccumulator() : 
            SegmentFeatureAccumulator(false) {}
    SegmentFeatureAccumulator(bool add_date_view) :
        _feature_id(0LU),
        _version(0LU),
        _click_mode(false),
        _use_mem_pool(false),
        _pass_coord_factor(false),
        _add_data_view(add_date_view) {}

    virtual ~SegmentFeatureAccumulator() {
        uninit();
    }

    virtual uint64_t feature_id() const {
        return _feature_id;
    }
        virtual bool init(const comcfg::ConfigUnit& conf);
    virtual void uninit();

    virtual bool update(const FeatureValueProto& fea);
    virtual bool update_and_query(FeatureValueProto* fea);
    virtual bool query(FeatureValueProto* fea) const;
    virtual bool query(
            const FValQRequestProto& request,
            FValQResponseProto* response) const;

    virtual bool load(PosixIoReaderInterface *reader);
    virtual bool dump(PosixIoWriterInterface *writer) const;

    virtual void print_monitor_log() const override;

private:
    bool _check_feature(const FeatureValueProto& fea) const;
    void _fillup_feature(const SegmentItem* ptr, FeatureValueProto* fea) const;
    int64_t _get_cumulant(const FeatureValueProto& fea) const;

    uint64_t _feature_id;
    uint64_t _version;
    StdSlidingWindow _std_window;
    PoolSlidingWindow _pool_window;
    ClickSegment _seg;
    bool _click_mode;
    bool _pass_coord_factor;
    bool _add_data_view;
    bool _use_mem_pool;
    mutable std::mutex _mutex;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_SEGMENT_FEATURE_ACCUMULATOR_H