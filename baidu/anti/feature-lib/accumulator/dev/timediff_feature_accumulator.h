// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @brief 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_TIMEDIFF_FEATURE_ACCUMULATOR_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_TIMEDIFF_FEATURE_ACCUMULATOR_H

#include <memory>
#include <com_log.h>
#include "goe_list.hpp"
#include "utils.h"
#include "feature_accumulator_interface.h"

namespace anti {
namespace themis {
namespace feature_lib {

class SecondNode {
public:
    SecondNode(uint64_t sign, int64_t ms, uint64_t id) :
            _sign(sign),
            _time_ms(ms), _time_second(ms / 1000), _id(id) {}
    SecondNode() : SecondNode(0, 0, 9) {}
    ~SecondNode() {}

    uint64_t sign() const { return _sign; }
    int64_t coord() const { return _time_second; }
    uint64_t id() const { return _id; }
    
    SecondNode& operator+=(const SecondNode& rhs) {
        if (rhs.coord() == coord())  {
            if (rhs._time_ms < _time_ms) {
                _time_ms = rhs._time_ms;
                _id = rhs._id;
            }
        }
        return *this;
    }

    template<typename Archive>
    bool serialize(Archive* ar) const {
        DUMP_VAR(_sign, ar, uint64_t);
        DUMP_VAR(_time_ms, ar, int64_t);
        DUMP_VAR(_time_second, ar, int64_t);
        DUMP_VAR(_id, ar, uint64_t);
        return true;
    }

    template<typename Archive>
    bool deserialize(Archive* ar) {
        LOAD_VAR(&_sign, ar, uint64_t);
        LOAD_VAR(&_time_ms, ar, int64_t);
        LOAD_VAR(&_time_second, ar, int64_t);
        LOAD_VAR(&_id, ar, uint64_t);
        return true;
    }

private:
    uint64_t _sign;
    int64_t _time_ms;
    int64_t _time_second;
    uint64_t _id;
};

class TDAccumulator : public FeatureAccumulatorInterface {
public:
    TDAccumulator();
    virtual ~TDAccumulator();

    virtual uint64_t feature_id() const { return _feature_id; }
    virtual bool init(const comcfg::ConfigUnit& conf);
    virtual void uninit();

    virtual bool update(const FeatureValueProto& fea);
    virtual bool query(FeatureValueProto* fea) const;

    virtual bool update_and_query(FeatureValueProto*);

    virtual bool load(PosixIoReaderInterface *reader);
    virtual bool dump(PosixIoWriterInterface *writer) const;

private:
    bool check_feature(const FeatureValueProto& fea) const;

private:
    typedef common_lib::GOEList<SecondNode> GOEList;

    uint64_t _feature_id;
    uint64_t _version;
    int64_t _range;
    std::shared_ptr<GOEList> _goe_list;
};

}
}
}

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_TIMEDIFF_FEATURE_ACCUMULATOR_H
