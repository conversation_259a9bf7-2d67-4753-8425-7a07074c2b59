// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
//
// @File: ratio_feature_accumulator.h
// @Last modified: 2017-12-26 17:23:58
// @Brief:

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_RATIO_FEATURE_ACCUMULATOR_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_RATIO_FEATURE_ACCUMULATOR_H

#include "feature_accumulator_interface.h"
#include "weak_anomaly_accumulator_interface.h"
#include <item.h>
#include <pool_sliding_window.h>
#include <sliding_window.h>
#include <click_segment.hpp>

namespace anti {
namespace themis {
namespace feature_lib {

class RatioFeatureAccumulator : public WeakAnomalyAccumulatorInterface {
public:
    using WeakAnomalyAccumulatorInterface::query;
    RatioFeatureAccumulator() :
        _feature_id(0LU),
        _version(0LU),
        _click_mode(false),
        _remain_numerator(false),
        _remain(0L),
        _use_mem_pool(false) {
    }

    virtual ~RatioFeatureAccumulator() {
        uninit();
    }

    virtual uint64_t feature_id() const {
        return _feature_id;
    }

    virtual bool query(
            const FValQRequestProto& request, 
            FValQResponseProto* response) const override;

    virtual void print_monitor_log() const override;

private:
    using RatioItem = anti::themis::common_lib::RateItem;
    using StdSlidingWindow = anti::themis::common_lib::SlidingWindow<RatioItem>;
    using PoolSlidingWindow = anti::themis::common_lib::PoolSlidingWindow<RatioItem>;
    using RateLimitNode = anti::themis::common_lib::RateLimitNode;
    using ClickSegment = anti::themis::common_lib::ClickSegment<RateLimitNode>;
    
    virtual bool _init(const comcfg::ConfigUnit& conf);
    virtual void _uninit();

    virtual bool _update(const FeatureValueProto& fea);
    virtual bool _update_and_query(FeatureValueProto* fea);
    virtual bool _query(FeatureValueProto* fea) const;

    virtual bool _load(PosixIoReaderInterface *reader);
    virtual bool _dump(PosixIoWriterInterface *writer) const;

    bool _check_feature(const FeatureValueProto& fea) const;
    bool _set_feature(const RatioItem* item, FeatureValueProto* fea) const;
    bool _set_feature(const RateLimitNode* node, FeatureValueProto* fea) const;
    bool _cal_ratio(const uint64_t view_sign, double* ratio) const;

    uint64_t _feature_id;
    uint64_t _version;
    StdSlidingWindow _std_window;
    PoolSlidingWindow _pool_window;
    ClickSegment _seg;
    bool _click_mode;
    bool _remain_numerator;
    int64_t _remain;
    bool _use_mem_pool;
    mutable std::mutex _mutex;
    bool _weak_check;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_RATIO_FEATURE_ACCUMULATOR_H