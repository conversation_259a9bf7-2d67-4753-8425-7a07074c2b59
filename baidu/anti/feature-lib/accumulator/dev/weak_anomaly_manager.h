// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: ch<PERSON><PERSON><PERSON><PERSON>(ch<PERSON><PERSON><PERSON><PERSON>@baidu.com)
// 
// @File: weak_anomaly_manager.h
// @Last modified: 2017-12-26 10:25:17
// @Brief: 

#ifndef BAIDU_ANTI_FEATURE_LIB_ACCUMULATOR_DEV_WEAK_ANOMALY_MANAGER_H
#define BAIDU_ANTI_FEATURE_LIB_ACCUMULATOR_DEV_WEAK_ANOMALY_MANAGER_H

#include <feature_accumulator_interface.h>
#include <item.h>
#include <lru_window.hpp>
#include <sliding_window.h>
#include <Configure.h>

namespace anti {
namespace themis {
namespace feature_lib {

// @brife: using a uint64_t represents a _seq of 01 
//         every bit represents normal(0) or abnormal click(1)
//         so the _seq can represents at most 63 clicks window
class WeakAnomalySeq {
public:
    WeakAnomalySeq() : _seq(0U), _seq_size(0), _valid_size(0) {}
    ~WeakAnomalySeq() {}

    bool set_size(uint32_t size);
    void reset() {
        _seq = 0;
        _valid_size = 0;
        return;
    }
    // @brife: push a click into seq
    void add(bool ab);
    // @brife: abnormal click proportion
    double ratio() const;

    template<typename Archive>
    bool serialize(Archive *ar) const;
    template<typename Archive>
    bool deserialize(Archive* ar);

private:
    // @brige: get valid seq
    inline uint64_t _get_seq() const {
        return _seq & ((1 << _valid_size) - 1);
    }
    // @brife: represents a bitmap by using a uint64_t
    //         the latest bits are valid
    uint64_t _seq;
    // @brife: max valid size of seq
    uint32_t _seq_size;
    // @brife: valid bits, less than or equal to _seq_size
    uint32_t _valid_size;
};

// @brife: the node who records abnormal degree and seq
//         cached by lru
class WeakAnomalyNode {
public:
    WeakAnomalyNode() {}
    ~WeakAnomalyNode() {}
    bool init(uint32_t seq_size) {
        return _seq.set_size(seq_size);
    }
    double get_e() const {
        return _e;
    }
    void set_e(double e) {
        _e = e;
    }
    WeakAnomalySeq* get_seq() {
        return &_seq;
    }
    void reset() {
        _e = 0.0;
        _seq.reset();
        return;
    }

    template<typename Archive>
    bool serialize(Archive *ar) const;
    template<typename Archive>
    bool deserialize(Archive* ar);

private:
    double _e;
    WeakAnomalySeq _seq;
};

// @brife: converts normal fea to weak anomaly fea
class WeakAnomalyManager {
public:
    WeakAnomalyManager() : 
            _feature_id(0LU),
            _version(0LU),
            _standard_threshold(0.0), 
            _incr_step(0),
            _remain(0),
            _e_base(0.0) {}
    ~WeakAnomalyManager() {}
    bool init(const comcfg::ConfigUnit& conf);
    void uninit();
    bool update_and_query(FeatureValueProto* fea);
    bool update(const FeatureValueProto& fea);
    bool query(FeatureValueProto* fea) const;

    bool load(PosixIoReaderInterface *reader);
    bool dump(PosixIoWriterInterface *writer) const;
private:
    uint64_t _feature_id;
    uint64_t _version;
    // @brife: the threshold to judge whether a click for weak anomaly click
    double _standard_threshold; 
    // @brife: length of short-term window
    uint32_t _incr_step;
    uint32_t _remain;
    // @brife: increment of weak anomaly, if a weak anomaly click arrived
    double _e_base; 
    WeakAnomalyNode _weak_ab_node;
    typedef anti::themis::common_lib::RateItem RatioItem;
    typedef anti::themis::common_lib::SlidingWindow<RatioItem> SlidingWindow;
    typedef anti::themis::common_lib::NodeAdaptor<uint64_t, WeakAnomalyNode> AbNode;
    typedef anti::themis::common_lib::LRUWindow<uint64_t, AbNode> AbWindow;
    // @brife: long-term window, recording abnormal click proportion in long-term
    std::shared_ptr<SlidingWindow> _l_window;
    // @brife: short-term window, recording abnormal degree 
    //         and abnormal click proportion in short-term 
    std::shared_ptr<AbWindow> _ab_window;
};

} // feature_lib
} // themis
} // anti

#endif // BAIDU_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_WEAK_ANOMALY_MANAGER_H

/* vim: set ts=4 sw=4: */

