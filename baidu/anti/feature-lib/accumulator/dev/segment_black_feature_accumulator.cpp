// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: segment_black_feature_accumulator.cpp
// @Last modified: 2017-03-16 14:11:59
// @Brief: 

#include "boost/lexical_cast.hpp"
#include "segment_black_feature_accumulator.h"

namespace anti {
namespace themis {
namespace feature_lib {

typedef anti::themis::common_lib::SegmentInfo SegmentInfo;

bool SegmentBlackFeatureAccumulator::init(const comcfg::ConfigUnit& conf) {
    int64_t window_length = 0L;
    int64_t upper_bound = 0L;
    int64_t discard = 0L;
    double lamda = 0.0;
    int64_t max_seg_num = 0L;
    try {
        _feature_id = conf["feature_id"].to_uint64();
        _version = conf["version"].to_uint64();
        window_length = conf["window_length"].to_int64();
        upper_bound = conf["upper_bound"].to_int64();
        discard = conf["discard"].to_int64();
        lamda = conf["lamda"].to_double();
        max_seg_num = conf["max_seg_num"].to_int64();
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    }
    if (upper_bound <= 0 || discard <= 0 || lamda <= 0) {
        CFATAL_LOG("invalid parameter!upper_bound:%ld, discard:%ld, lamda:%f",
                upper_bound, discard, lamda);
        return false;
    }
    int64_t tmp_set_num = static_cast<int64_t>((log(discard) -  log(upper_bound)) / log(lamda));
    int64_t segment_num = tmp_set_num > max_seg_num ? max_seg_num : tmp_set_num;
    // SegmentInfo 
    SegmentInfo info(window_length, segment_num, upper_bound, lamda);
    if (!_seg.init(info)) {
        CFATAL_LOG("call segment init fail");
        return false;
    }
    CWARNING_LOG("init SegmentBlackFeatureAccumulator(%lu) success", _feature_id);
    return true;
}

void SegmentBlackFeatureAccumulator::uninit() {
    _seg.uninit();
    _feature_id = 0LU;
    _version = 0LU;
}

bool SegmentBlackFeatureAccumulator::update(const FeatureValueProto& fea) {
    if (!_check_feature(fea)) {
        CFATAL_LOG("call _check_feature fail");
        return false;
    }

    if (!_seg.enter(SegBlackNode(fea.view_sign(), fea.coord()), NULL)) {
        CFATAL_LOG("enter click segment fail");
        return false;
    }
    return true;
}

bool SegmentBlackFeatureAccumulator::update_and_query(FeatureValueProto* fea) {
    if (fea == NULL || !_check_feature(*fea)) {
        CFATAL_LOG("fea invalid");
        return false;
    }
    const SegBlackNode* res = NULL;
    if (!_seg.enter(SegBlackNode(fea->view_sign(), fea->coord()), &res) || res == NULL) {
        CWARNING_LOG("enter click segment fail");
        return false;
    }
    return _fillup_feature(res, fea);
}

bool SegmentBlackFeatureAccumulator::query(FeatureValueProto* fea) const {
    if (fea == NULL || !_check_feature(*fea)) {
        CFATAL_LOG("fea invalid");
        return false;
    }
    const SegBlackNode* node = _seg.query_segment(fea->view_sign(), fea->coord());
    return node == NULL ? false : _fillup_feature(node, fea);
}

bool SegmentBlackFeatureAccumulator::_fillup_feature(
        const SegBlackNode* node,
        FeatureValueProto* fea) const {
    if (node == NULL || fea == NULL) {
        CWARNING_LOG("invalid input node or fea is NULL");
        return false;
    }
    fea->set_valid(true);
    fea->set_cur_seg_count(node->last_cumulant + node->cumulant);
    // last_seg_count is used for cur_count caculation 
    // but useless for the following rule check. Set 0 here.
    fea->set_last_seg_count(0);
    fea->set_value(boost::lexical_cast<std::string>(fea->cur_seg_count()));
    return true;
}

bool SegmentBlackFeatureAccumulator::_check_feature(const FeatureValueProto& fea) const {
    if (fea.feature_id() != _feature_id
            || fea.feature_type() != FeatureValueProto::AUTO_SEGMENT) {
        CFATAL_LOG("feature_id(%lu) != _feature_id(%lu) or invalid feature_type(%d), logid(%lu)", 
                fea.feature_id(), _feature_id, fea.feature_type(), fea.log_id());
        return false;
    }
    return true;
}

bool SegmentBlackFeatureAccumulator::load(PosixIoReaderInterface *reader) {
    uint64_t ver = 0LU;
    if (reader->read(&ver, sizeof(ver)) != static_cast<int>(sizeof(ver))) {
        CWARNING_LOG("load version fail");
        return false;
    }
    // version > checkpoint version return true directly
    if (ver < _version) {
        return true;
    }

    if (!_seg.deserialize(reader)) {
        CWARNING_LOG("call segment deserialize fail, feature_id(%lu)", _feature_id);
        return false;
    }
    return true;
}

bool SegmentBlackFeatureAccumulator::dump(PosixIoWriterInterface *writer) const {
    if (writer->write(&_version, sizeof(_version)) 
            != static_cast<int>(sizeof(_version))) {
        CWARNING_LOG("write version(%lu) fail, feature_id(%lu)", 
                _version, _feature_id);
        return false;
    }

    if (!_seg.serialize(writer)) {
        CWARNING_LOG("call segment serialize fail, feature_id(%lu)", _feature_id);
        return false;
    }
    return true;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

