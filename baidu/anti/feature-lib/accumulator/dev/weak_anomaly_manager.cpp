// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: ch<PERSON><PERSON><PERSON><PERSON>(ch<PERSON><PERSON><PERSON><PERSON>@baidu.com)
// 
// @File: weak_anomaly_manager.cpp
// @Last modified: 2017-12-27 21:22:54
// @Brief: 

#include "weak_anomaly_manager.h"
#include <boost/lexical_cast.hpp>

namespace anti {
namespace themis {
namespace feature_lib {

bool WeakAnomalySeq::set_size(uint32_t size) {
    if (size >= 64) {
        CWARNING_LOG("anomaly seq size[%u]", size);
        return false;
    }
    _seq_size = size;
    return true;
}

void WeakAnomalySeq::add(bool ab) {
    _seq <<= 1;
    if (ab) {
        _seq += 1;
    }
    _valid_size = _valid_size + 1 < _seq_size ? _valid_size + 1 : _seq_size;
    return;
}

double WeakAnomalySeq::ratio() const {
    auto seq =  _get_seq();
    uint64_t count = 0;
    while (seq != 0) {
        ++count;
        seq &= (seq - 1);
    }
    return _valid_size == 0L ? 0.0 : 1.0 * count / _valid_size;
}

template<typename Archive>
bool WeakAnomalySeq::serialize(Archive* ar) const {
    DUMP_VAR(_seq, ar, uint64_t);
    DUMP_VAR(_seq_size, ar, uint32_t);
    DUMP_VAR(_valid_size, ar, uint32_t);
    return true;
}

template<typename Archive>
bool WeakAnomalySeq::deserialize(Archive* ar) {
    LOAD_VAR(&_seq, ar, uint64_t);
    LOAD_VAR(&_seq_size, ar, uint32_t);
    LOAD_VAR(&_valid_size, ar, uint32_t);
    return true;
}

template<typename Archive>
bool WeakAnomalyNode::serialize(Archive* ar) const {
    DUMP_VAR(_e, ar, double);
    return _seq.serialize(ar);
}

template<typename Archive>
bool WeakAnomalyNode::deserialize(Archive* ar) {
    LOAD_VAR(&_e, ar, double);
    return _seq.deserialize(ar);
}

bool WeakAnomalyManager::init(const comcfg::ConfigUnit& conf) {
    if (conf["weak_abnormity_threshold"].selfType() == comcfg::CONFIG_ERROR_TYPE) {
        CWARNING_LOG("weak_abnormity_threshold dose not exist!");
        return false;
    }
    _standard_threshold = conf["weak_abnormity_threshold"].to_double();
    const uint32_t INCR_STEP = 8;
    conf["incr_step"].get_uint32(&_incr_step, INCR_STEP);
    conf["remain"].get_uint32(&_remain, _incr_step);
    _e_base = 1.0 / _incr_step;
    int mode = 0;
    const int32_t DEFAULT_MODE = 0;
    conf["click_mode"].get_int32(&mode, DEFAULT_MODE);
    if (mode == 1) {
        CWARNING_LOG("weak anomaly only using sliding window");
        return false;
    }
    int64_t window_length = 0L;
    try {
        _feature_id = conf["feature_id"].to_uint64();
        _version = conf["version"].to_uint64();
        window_length = conf["window_length"].to_int64();
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    }

    const int DEFAULT_STEP_NUM = 4;
    long long step_length = 0L;
    if (conf["step_length"].get_int64(&step_length, window_length / DEFAULT_STEP_NUM) != 0) {
        CWARNING_LOG("feature_id(%lu) no step_length, using %ld", 
                _feature_id, window_length / DEFAULT_STEP_NUM);
    }

    if (window_length <= 0L 
            || step_length <= 0L 
            || window_length % step_length != 0) {
        CFATAL_LOG("invalid window_length(%ld) step_length(%ld)", 
                window_length, step_length);
        return false;
    }

    const int CACHE_STEP_NUM = 1;
    int max_step_num = CACHE_STEP_NUM + window_length / step_length;
    _l_window.reset(new (std::nothrow) SlidingWindow);
    if (!_l_window || !_l_window->init(step_length, max_step_num, window_length)) {
        CWARNING_LOG("call window init(%ld, %ld, %ld) fail",
                step_length, max_step_num, window_length);
        return false;
    }
    _ab_window.reset(new (std::nothrow) AbWindow(window_length ,LLONG_MAX));
    if (!_ab_window) {
        CFATAL_LOG("AbWindow new or init failed!");
        return false;
    }
    if (!_weak_ab_node.init(_incr_step)) {
        CWARNING_LOG("init ab info failed! incr_step[%u]", _incr_step);
        return false;
    }
    CWARNING_LOG("init WeakAnomalyManager feaid[%lu] success", _feature_id);
    return true;
}

void WeakAnomalyManager::uninit() {
    _feature_id = 0LU;
    _version = 0UL;
    _standard_threshold = 0.0;
    _incr_step = 0U;
    _remain = 0U;
    _e_base = 0.0;
    _l_window->uninit();
    _l_window = NULL;
    _ab_window = NULL;
    return;
}

bool WeakAnomalyManager::query(FeatureValueProto* fea) const {
    if (!fea) {
        CWARNING_LOG("invalid input fea!");
        return false;
    }
    auto p_ab_node = _ab_window->find(fea->view_sign());
    if (!p_ab_node) {
        return false;
    }       
    fea->set_value(boost::lexical_cast<std::string>(p_ab_node->value().get_e()));
    // remain
    auto p_l_item = _l_window->query_segment(fea->view_sign());
    if (!p_l_item) {
        return false;
    }
    auto refer_num = p_l_item->second();
    // check fea
    fea->set_valid(fea->valid() && refer_num > _remain);
    fea->set_feature_type(FeatureValueProto::WEAK_ANOMALY);
    return true;
}

bool WeakAnomalyManager::update(const FeatureValueProto& fea) {
    double standard_value = 0;
    try {
        standard_value = boost::lexical_cast<double>(fea.value());
    } catch (const boost::bad_lexical_cast& e) {
        CWARNING_LOG("invalid fea.value(%s), bad_lexical_cast(%s)", fea.value().c_str(), e.what());
        return false;
    }
    _weak_ab_node.reset();
    auto p_ab_node = _ab_window->find(fea.view_sign());
    if (p_ab_node) {
        _weak_ab_node = p_ab_node->value(); 
    }       
    double e = _weak_ab_node.get_e();
    if (fea.in_anomaly_care() && standard_value > _standard_threshold) {
        // update e 
        e += _e_base;
        // update window
        _l_window->enter(fea.view_sign(), fea.coord(), RatioItem(1L, 1L), NULL);
        _weak_ab_node.get_seq()->add(true);
    } else {
        const RatioItem* p_l_item = NULL;
        _l_window->enter(fea.view_sign(), fea.coord(), RatioItem(0L, 1L), &p_l_item);
        _weak_ab_node.get_seq()->add(false);    
        double e_ratio = std::max(
                1.0 * p_l_item->first() / p_l_item->second(), 
                _weak_ab_node.get_seq()->ratio());
        e = e_ratio < e ? e_ratio : e;
    }
    CDEBUG_LOG("fea value[%lf], fea coord[%lu], fea e[%lf]",
            standard_value, fea.coord(), e);
    _weak_ab_node.set_e(e);
    _ab_window->insert(AbNode(fea.view_sign(), _weak_ab_node, fea.coord()));
    return true;
}

bool WeakAnomalyManager::update_and_query(FeatureValueProto* fea) { 
    return update(*fea) && query(fea);
}

bool WeakAnomalyManager::load(PosixIoReaderInterface *reader) {
    uint64_t ver = 0LU;
    if (reader->read(&ver, sizeof(ver)) != static_cast<int>(sizeof(ver))) {
        CWARNING_LOG("load version failed!");
        return false;
    }
    if (ver < _version) {
        return true;
    }
    if (!_l_window->deserialize(reader)
            || !_ab_window->deserialize(reader)) {
        CWARNING_LOG("weak anomaly load failed!");
        return false;
    }
    return true;
}

bool WeakAnomalyManager::dump(PosixIoWriterInterface *writer) const {
    if (writer->write(&_version, sizeof(_version)) 
            != static_cast<int>(sizeof(_version))) {
        CWARNING_LOG("weak anomaly write version[%lu] failed! fea[%lu]", _version, _feature_id);
        return false;
    }
    if (!_l_window->serialize(writer)
            || !_ab_window->serialize(writer)) {
        CWARNING_LOG("weak anomaly serialize failed! fea[%lu]", _feature_id);
        return false;
    }
    return true;
}

} // feature_lib
} // themis
} // anti

/* vim: set ts=4 sw=4: */

