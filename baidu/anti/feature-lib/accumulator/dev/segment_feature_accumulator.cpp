// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiax<PERSON>(<EMAIL>)
//
// @File: segment_feature_accumulator.cpp
// @Last modified: 2017-11-22 11:47:55
// @Brief:

#include "boost/lexical_cast.hpp"
#include "segment_feature_accumulator.h"

namespace anti {
namespace themis {
namespace feature_lib {

DECLARE_bool(enable_mem_pool_for_acc);

bool SegmentFeatureAccumulator::init(const comcfg::ConfigUnit& conf) {
    int64_t window_length = 0L;
    try {
        _feature_id = conf["feature_id"].to_uint64();
        _version = conf["version"].to_uint64();
        window_length = conf["window_length"].to_int64();
        if (conf["pass_coord_factor"].selfType() == comcfg::CONFIG_ERROR_TYPE) {
            _pass_coord_factor = false;
        } else {
            _pass_coord_factor = conf["pass_coord_factor"].to_int32() != 0;
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    }

    const int DEFAULT_MODE = 0;
    int mode = 0;
    if (conf["click_mode"].get_int32(&mode, DEFAULT_MODE)) {
        CWARNING_LOG("feature_id(%lu) no click_mode, using %d",
                _feature_id, DEFAULT_MODE);
    }
    _click_mode = mode != 0 ? true : false;

    const int DEFAULT_STEP_NUM = 4;
    long long step_length = 0L;
    if (conf["step_length"].get_int64(&step_length, window_length / DEFAULT_STEP_NUM) != 0) {
        CWARNING_LOG("feature_id(%lu) no step_length, using %ld", 
                _feature_id, window_length / DEFAULT_STEP_NUM);
    }

    if (window_length <= 0L 
            || step_length <= 0L 
            || window_length % step_length != 0) {
        CFATAL_LOG("invalid window_length(%ld) step_length(%ld)", 
                window_length, step_length);
        return false;
    }

    // 决定是否使用内存池
    _use_mem_pool = FLAGS_enable_mem_pool_for_acc;

    if (!_click_mode) {
        const int CACHE_STEP_NUM = 1;
        int max_step_num = CACHE_STEP_NUM + window_length / step_length;

        if (_use_mem_pool) {
            if (!_pool_window.init(step_length, max_step_num, window_length)) {
                CWARNING_LOG("call pool window init(%ld, %ld, %ld) fail", step_length, max_step_num, window_length);
                return false;
            }
            if (_pass_coord_factor) {
                _pool_window.set_log_key_first_appear();
            }
        } else {
            if (!_std_window.init(step_length, max_step_num, window_length)) {
                CWARNING_LOG("call window init(%ld, %ld, %ld) fail", step_length, max_step_num, window_length);
                return false;
            }
            if (_pass_coord_factor) {
                _std_window.set_log_key_first_appear();
            }
        }
    } else {
        if (!_seg.init(window_length)) {
            CWARNING_LOG("call click segment init fail, seg_len=%ld",
                    window_length);
            return false;
        }
    }
    CWARNING_LOG("init SegmentFeatureAccumulator(%lu) success", _feature_id);
    return true;
}

void SegmentFeatureAccumulator::uninit() {
    if (!_click_mode) {
        if (_use_mem_pool) {
            _pool_window.uninit();
        } else {
            _std_window.uninit();
        }
    } else {
        _seg.uninit();
    }
    _feature_id = 0LU;
    _version = 0LU;
    _click_mode = false;
    _add_data_view = false;
    _use_mem_pool = false;
}

bool SegmentFeatureAccumulator::update(const FeatureValueProto& fea) {
    if (!_check_feature(fea)) {
        CFATAL_LOG("call _check_feature fail");
        return false;
    }
    std::lock_guard<std::mutex> locker(_mutex);
    if (_click_mode) {
        return _seg.enter(SegLimitNode(fea.view_sign(), fea.coord()), NULL);
    }

    if (_use_mem_pool) {
        return _pool_window.enter(fea.view_sign(), fea.coord(), SegmentItem(_get_cumulant(fea)));
    } else {
        return _std_window.enter(fea.view_sign(), fea.coord(), SegmentItem(_get_cumulant(fea)));
    }
}

int64_t SegmentFeatureAccumulator::_get_cumulant(const FeatureValueProto& fea) const {
    if (!_add_data_view) {
        return 1L;
    }
    try {
        return boost::lexical_cast<int64_t>(fea.data_view_value());
    } catch (const boost::bad_lexical_cast& e) {
        CFATAL_LOG("invalid data view value(%s) for fea_id(%lu) logid(%lu)", 
                fea.data_view_value().data(), fea.feature_id(), fea.log_id());
        CWARNING_LOG("using 1L instread");
        return 1L;
    }
}

bool SegmentFeatureAccumulator::update_and_query(FeatureValueProto* fea) {
    if (fea == NULL || !_check_feature(*fea)) {
        CFATAL_LOG("input fea == NULL or call _check_feature fail");
        return false;
    }
    std::lock_guard<std::mutex> locker(_mutex);
    if (_click_mode) {
        const SegLimitNode* res = NULL;
        if (!_seg.enter(SegLimitNode(fea->view_sign(), fea->coord()), &res) || res == NULL) {
            CWARNING_LOG("enter click segment fail");
            return false;
        }
        fea->set_cur_seg_count(res->cumulant);
        fea->set_last_seg_count(res->last_cumulant);
        fea->set_valid(true);
        fea->set_value(boost::lexical_cast<std::string>(fea->cur_seg_count()));
        return true;
    }

    const SegmentItem* res = NULL;
    bool enter_success = false;
    if (_use_mem_pool) {
        enter_success = _pool_window.enter(fea->view_sign(), fea->coord(), SegmentItem(_get_cumulant(*fea)), &res);
    } else {
        enter_success = _std_window.enter(fea->view_sign(), fea->coord(), SegmentItem(_get_cumulant(*fea)), &res);
    }
    if (!enter_success) {
        CWARNING_LOG("enter click segment fail");
        return false;
    }
    _fillup_feature(res, fea);
    return true;
}

bool SegmentFeatureAccumulator::query(FeatureValueProto* fea) const {
    if (fea == NULL || !_check_feature(*fea)) {
        CFATAL_LOG("input fea == NULL or call _check_feature fail");
        return false;
    }
    std::lock_guard<std::mutex> locker(_mutex);
    if (_click_mode) {
        const SegLimitNode* node = _seg.query_segment(fea->view_sign());
        fea->set_valid(true);
        if (node == NULL) {
            fea->set_cur_seg_count(0);
            fea->set_last_seg_count(0);
            fea->set_value("0");
            return true;
        }
        fea->set_cur_seg_count(node->cumulant);
        fea->set_last_seg_count(node->last_cumulant);
        fea->set_value(boost::lexical_cast<std::string>(fea->cur_seg_count()));
        return true;
    }

    const SegmentItem* node = NULL;
    if (_use_mem_pool) {
        node = _pool_window.query_segment(fea->view_sign(), fea->coord());
    } else {
        node = _std_window.query_segment(fea->view_sign(), fea->coord());
    }
    if (node == NULL) {
        CTRACE_LOG("call window query fail feature_id(%lu) logid(%lu)",
                fea->feature_id(), fea->log_id());
        return false;
    }
    _fillup_feature(node, fea);
    return true;
}

bool SegmentFeatureAccumulator::_check_feature(const FeatureValueProto& fea) const {
    if (fea.feature_id() != _feature_id
        || (fea.feature_type() != FeatureValueProto::SEGMENT 
            && fea.feature_type() != FeatureValueProto::SUM_SEGMENT
            && fea.feature_type() != FeatureValueProto::BEHAVIOR_SEGMENT)) {
        CFATAL_LOG("feature_id(%lu) != _feature_id(%lu) or invalid feature_type(%d), logid(%lu)", 
                fea.feature_id(), _feature_id, fea.feature_type(), fea.log_id());
        return false;
    }
    return true;
}

void SegmentFeatureAccumulator::_fillup_feature(const SegmentItem* node,
        FeatureValueProto* fea) const {
    fea->set_cur_seg_count(node ? node->cumulant() : 0);
    fea->set_valid(true);
    fea->set_value(boost::lexical_cast<std::string>(fea->cur_seg_count()));

    SegmentItem last_item = _use_mem_pool ? 
        _pool_window.query_last_segment(fea->view_sign()) :
        _std_window.query_last_segment(fea->view_sign());
    fea->set_last_seg_count(last_item.cumulant());

    if (!_pass_coord_factor) {
        fea->clear_coord_factor();
        return;
    }

    int64_t fea_start_coord = 0;
    int32_t step_length = 0;
    int32_t step_num = 0;
    if (_use_mem_pool) {
        fea_start_coord = _pool_window.query_start_coord(fea->view_sign());
        step_length = _pool_window.step_length();
        step_num = _pool_window.step_num();
    } else {
        fea_start_coord = _std_window.query_start_coord(fea->view_sign());
        step_length = _std_window.step_length();
        step_num = _std_window.step_num();
    }

    // 安全检查除零风险
    if (step_length == 0 || step_num == 0) {
        CWARNING_LOG("step_length(%d) or step_num(%d) is zero for feature_id(%lu), logid(%lu)", 
                   step_length, step_num, fea->feature_id(), fea->log_id());
        fea->clear_coord_factor();
        return;
    }

    int32_t step_idx = (fea->coord() - fea_start_coord) / step_length;
    double factor = static_cast<double>(step_idx + 1) / step_num;
    fea->set_coord_factor(factor);
}

bool SegmentFeatureAccumulator::query(
        const FValQRequestProto& request,
        FValQResponseProto* response) const {
    if (response == NULL || !request.has_view_sign()) {
        CFATAL_LOG("request or response invalid, feture_id(%lu)", _feature_id);
        return false;
    }
    std::lock_guard<std::mutex> locker(_mutex);
    if (_click_mode) {
        auto node = _seg.query_segment(request.view_sign());
        response-> add_value(
                !node ? "0" : boost::lexical_cast<std::string>(node->cumulant));
        response->set_coord(_seg.latest_coord());
        return true;
    }

    const SegmentItem* item = NULL;
    if (_use_mem_pool) {
        item = _pool_window.query_segment(request.view_sign());
    } else {
        item = _std_window.query_segment(request.view_sign());
    }
    response->add_value(!item ? "0" : boost::lexical_cast<std::string>(item->cumulant()));

    int64_t latest_coord = 0;
    if (_use_mem_pool) {
        latest_coord = _pool_window.latest_coord();
    } else {
        latest_coord = _std_window.latest_coord();
    }
    response->set_coord(latest_coord);
    return true;
}

bool SegmentFeatureAccumulator::load(PosixIoReaderInterface *reader) {
    uint64_t ver = 0LU;
    if (reader->read(&ver, sizeof(ver)) != static_cast<int>(sizeof(ver))) {
        CWARNING_LOG("load version fail");
        return false;
    }

    // version > checkpoint version return true directly
    if (ver < _version) {
        return true;
    }
    std::lock_guard<std::mutex> locker(_mutex);
    if (_click_mode) {
        if (!_seg.deserialize(reader)) {
            CWARNING_LOG("call segment deserialize fail, feature_id(%lu)", _feature_id);
            return false;
        }
    } else {
        bool deserialize_success = false;
        if (_use_mem_pool) {
            deserialize_success = _pool_window.deserialize(reader);
        } else {
            deserialize_success = _std_window.deserialize(reader);
        }
        if (!deserialize_success) {
            CWARNING_LOG("call window deserialize fail, feature_id(%lu)", _feature_id);
            return false;
        }
    }
    return true;
}

bool SegmentFeatureAccumulator::dump(PosixIoWriterInterface *writer) const {
    if (writer->write(&_version, sizeof(_version)) 
            != static_cast<int>(sizeof(_version))) {
        CWARNING_LOG("write version(%lu) fail, feature_id(%lu)", 
                _version, _feature_id);
        return false;
    }
    std::lock_guard<std::mutex> locker(_mutex);
    if (_click_mode) {
        if (!_seg.serialize(writer)) {
            CWARNING_LOG("call segment serialize fail, feature_id(%lu)", _feature_id);
            return false;
        }
    } else {
        bool serialize_success = false;
        if (_use_mem_pool) {
            serialize_success = _pool_window.serialize(writer);
        } else {
            serialize_success = _std_window.serialize(writer);
        }
        if (!serialize_success) {
            CWARNING_LOG("call window serialize fail, feature_id(%lu)", _feature_id);
            return false;
        }
    }
    return true;
}

void SegmentFeatureAccumulator::print_monitor_log() const {
    if (_click_mode) {
        // SEG模式：无内存统计
        uint64_t element_count = _seg.window_view_sign_size();
        CNOTICE_LOG("Feature: ID=%lu, Type=SEGMENT, Elements=%lu, Memory=N/A", _feature_id, element_count);

    } else if (_use_mem_pool) {
        // POOL_WINDOW模式：详细内存统计
        bsl::var::Dict pool_dict;
        bsl::ResourcePool rp;
        _pool_window.monitor(pool_dict, rp);

        // === 元素统计 ===
        uint64_t total_elements = pool_dict["TOTAL_ELEMENTS"].to_uint64();
        uint64_t segment_elements = pool_dict["SEGMENT_ELEMENTS"].to_uint64();
        uint64_t window_elements = pool_dict["WINDOW_ELEMENTS"].to_uint64();
        
        // === 内存统计 ===
        uint64_t data_mem = pool_dict["DATA_MEM"].to_uint64();
        uint64_t pool_mgmt_overhead = pool_dict["POOL_MGMT_OVERHEAD"].to_uint64();
        uint64_t sliding_window_overhead = pool_dict["SLIDING_WINDOW_OVERHEAD"].to_uint64();
        uint64_t accumulator_overhead = sizeof(SegmentFeatureAccumulator);
        
        uint64_t total_overhead = pool_mgmt_overhead + sliding_window_overhead + accumulator_overhead;
        uint64_t total_mem = data_mem + total_overhead;
        
        double avg_mem_per_element = total_elements > 0 ? 
            static_cast<double>(total_mem) / total_elements : 0.0;

        // === 第一行：简洁格式 ===
        CNOTICE_LOG(
            "Feature: ID=%lu, Type=SEGMENT, Elements=%lu(Seg=%lu,Win=%lu), "
            "Memory(Total=%lu,Data=%lu,Overhead=%lu,AvgPerElement=%.2f)",
            _feature_id, total_elements, segment_elements, window_elements,
            total_mem, data_mem, total_overhead, avg_mem_per_element);

        // === 第二行：详细格式 ===
        CNOTICE_LOG(
            "Feature: ID=%lu, Details: Coords[SegmentLen=%ld,OldestCoord=%ld,LatestCoord=%ld],"
            "SegmentHashMap[Size=%lu,BucketCount=%lu,LoadFactor=%.3f],"
            "MemPool[NodeNum=%lu,DataMem=%lu,MgmtOverhead=%lu]",
            _feature_id,
            pool_dict["SEGMENT_LENGTH"].to_int64(),
            pool_dict["OLDEST_COORD"].to_int64(), 
            pool_dict["LATEST_COORD"].to_int64(),
            pool_dict["SEGMENT_SIZE"].to_uint64(),
            pool_dict["SEGMENT_BUCKET_COUNT"].to_uint64(),
            pool_dict["SEGMENT_LOAD_FACTOR"].to_double(),
            total_elements,
            data_mem,
            pool_mgmt_overhead);

    } else {
        // STD_WINDOW模式：无内存统计
        uint64_t segment_size = _std_window.segment_view_sign_size();
        uint64_t window_size = _std_window.window_view_sign_size();
        uint64_t element_count = segment_size + window_size;

        CNOTICE_LOG("Feature: ID=%lu, Type=SEGMENT, Elements=%lu, Memory=N/A", _feature_id, element_count);
    }
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti