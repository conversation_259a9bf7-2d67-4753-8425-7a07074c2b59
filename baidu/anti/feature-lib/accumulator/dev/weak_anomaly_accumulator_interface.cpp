// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: ch<PERSON><PERSON><PERSON><PERSON>(ch<PERSON><PERSON><PERSON><PERSON>@baidu.com)
// 
// @File: weak_anomaly_accumulator_interface.cpp
// @Last modified: 2017-12-27 21:14:59
// @Brief: 

#include "weak_anomaly_accumulator_interface.h"

namespace anti {
namespace themis {
namespace feature_lib {

bool WeakAnomalyAccumulatorInterface::init(const comcfg::ConfigUnit& conf) {
    if (conf["weak_abnormity_threshold"].selfType() != comcfg::CONFIG_ERROR_TYPE) {
        _weak_ab_mgr.reset(new (std::nothrow) WeakAnomalyManager());
        if (!_weak_ab_mgr || !_weak_ab_mgr->init(conf)) {
            CWARNING_LOG("weak_ab_manager new or init failed!");
            return false;
        }
    }
    return _init(conf);
}

void WeakAnomalyAccumulatorInterface::uninit() {
    _uninit();
    return;
}

bool WeakAnomalyAccumulatorInterface::update(const FeatureValueProto& fea) {
    if (!_update(fea)) {
        return false;
    }
    if (_weak_ab_mgr.get() && !_weak_ab_mgr->update(fea)) {
        CWARNING_LOG("weak_ab_mgr update failed!");
        return false;
    }
    return true;
}

bool WeakAnomalyAccumulatorInterface::update_and_query(FeatureValueProto* fea) {
    if (!_update_and_query(fea)) {
        return false;
    }
    if (_weak_ab_mgr.get() && !_weak_ab_mgr->update_and_query(fea)) {
        CWARNING_LOG("weak_ab_mgr update_and_query failed!");
        return false;
    }
    return true;
}

bool WeakAnomalyAccumulatorInterface::query(FeatureValueProto* fea) const {
    if (!fea) {
        CFATAL_LOG("invalid input fea is NULL");
        return false;
    }
    if (fea->feature_type() != FeatureValueProto::WEAK_ANOMALY) {
        if (!_query(fea)) {
            return false;
        }
        return true;
    }
    if (!_weak_ab_mgr || !_weak_ab_mgr->query(fea)) {
        CWARNING_LOG("weak_ab_mgr query failed!");
        return false;
    }
    return true;
}

bool WeakAnomalyAccumulatorInterface::load(PosixIoReaderInterface *reader) {
    if (!_load(reader)) {
        return false;
    }
    if (_weak_ab_mgr.get() && !_weak_ab_mgr->load(reader)) {
        CWARNING_LOG("weak_ab_mgr load failed!");
        return false;
    }
    return true;
}

bool WeakAnomalyAccumulatorInterface::dump(PosixIoWriterInterface *writer) const {
    if (!_dump(writer)) {
        return false;
    }
    if (_weak_ab_mgr.get() && !_weak_ab_mgr->dump(writer)) {
        CWARNING_LOG("weak_ab_mgr dump failed!");
        return false;
    }
    return true;
}

} // feature_lib
} // themis
} // anti

/* vim: set ts=4 sw=4: */

