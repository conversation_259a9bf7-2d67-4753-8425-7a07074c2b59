// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: feature_accumulator_factory.h
// @Last modified: 2015-05-19 12:02:43
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_FEATURE_ACCUMULATOR_FACTORY_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_FEATURE_ACCUMULATOR_FACTORY_H

#include <string>
#include <unordered_set>
#include "feature_accumulator_interface.h"

namespace anti {
namespace themis {
namespace feature_lib {

class FeatureAccumulatorFactory {
public:
    static FeatureAccumulatorInterface* create(const std::string& type);
private:
    static const std::unordered_set<std::string> BASE_ACCUMULATOR_TYPE_SET;
    static const std::unordered_set<std::string> FVALUE_QUERY_TYPE_SET;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_FEATURE_ACCUMULATOR_FACTORY_H

/* vim: set ts=4 sw=4: */

