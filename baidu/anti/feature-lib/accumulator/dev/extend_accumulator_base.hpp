// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: feature_accumulator_base.h
// @Last modified: 2017-07-28 16:04:56
// @Brief: 

#include <com_log.h>

namespace anti {
namespace themis {
namespace feature_lib {

template<typename Node>
bool ExtendAccumulatorBase<Node>::_init_window_conf(const comcfg::ConfigUnit& conf) {
    _window_conf.window_length = conf["window_length"].to_int64();
    const int64_t MAX_ITEM_NUM = 10000;
    _window_conf.max_item = MAX_ITEM_NUM;
    if (conf["max_num"].selfType() != comcfg::CONFIG_ERROR_TYPE) {
        _window_conf.max_item = conf["max_num"].to_int64();
    }
    const int64_t REMAIN_SUB_FEA_NUM = 100;
    _window_conf.remain = REMAIN_SUB_FEA_NUM;
    if (conf["confidence_min"].selfType() != comcfg::CONFIG_ERROR_TYPE) {
        _window_conf.remain = conf["confidence_min"].to_int64();
    }
    // expiration in seconds format
    const int64_t EXPIRATION = 3600; 
    _window_conf.expiration = EXPIRATION;
    if (conf["expiration"].selfType() != comcfg::CONFIG_ERROR_TYPE) {
        _window_conf.expiration = conf["expiration"].to_int64(); 
    }
    _window.reset(new (std::nothrow) 
            ExtendWindow(_window_conf.window_length, LLONG_MAX));
    if (!_window) {
        CFATAL_LOG("create LRUWindow fail, feature_id(%lu)", feature_id());
        return false;
    }
    return true;
}

template<typename Node>
bool ExtendAccumulatorBase<Node>::init(const comcfg::ConfigUnit& conf) {
    try {
        _feature_id = conf["feature_id"].to_uint64();
        _version = conf["version"].to_uint64();
        if (!_init_window_conf(conf)) {
            CWARNING_LOG("call _init_window_conf fail, feature_id(%lu)", _feature_id);
            return false;
        }

        if (!_init(conf)) {
            CWARNING_LOG("call _init fail, feature_id(%lu)", _feature_id);
            return false;
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std::Exception : %s", e.what());
        return false;
    }
    CWARNING_LOG("init ExtendAccumulator(%lu) succ", _feature_id);
    return true;
}

template<typename Node>
bool ExtendAccumulatorBase<Node>::dump(PosixIoWriterInterface *writer) const {
    if (!writer) {
        CFATAL_LOG("writer ptr invalid, feature_id(%lu)", _feature_id);
        return false;
    }

    if (!_window) {
        CFATAL_LOG("window ptr is null, call init first please, feature_id(%lu)", 
                _feature_id);
        return false;
    }

    if (writer->write(&_version, sizeof(_version)) 
                != static_cast<int>(sizeof(_version))) {
        CWARNING_LOG("write version(%lu) fail, feature_id(%lu)", 
                _version, _feature_id);
        return false;
    }
    
    if (!_window->serialize(writer)) {
        CWARNING_LOG("dump window fail, feature_id(%lu)", _feature_id);
        return false;
    }
    return true;
}

template<typename Node>
bool ExtendAccumulatorBase<Node>::load(PosixIoReaderInterface *reader) {
    if (!reader) {
        CFATAL_LOG("writer ptr invalid, feature_id(%lu)", _feature_id);
        return false;
    }

    if (!_window) {
        CFATAL_LOG("window ptr is null, call init first please, feature_id(%lu)", 
                _feature_id);
        return false;
    }
    uint64_t ver = 0LU;
    if (reader->read(&ver, sizeof(ver)) != static_cast<int>(sizeof(ver))) {
        CWARNING_LOG("load version fail, feature_id(%lu)", _feature_id);
        return false;
    }
    // version > checkpoint version return true directly
    if (ver < _version) {
        return true;
    }
    
    if (!_window->deserialize(reader, _window_conf.window_length, _window_conf.max_item)) {
        CWARNING_LOG("load window fail, feature_id(%lu)", _feature_id);
        return false;
    }
    return true;
}

} // namespace feature_lib
} // namespace themis
} // namespace anti
