// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: fea_value_dis_accumulator.cpp
// @Last modified: 2018-01-03 09:56:17
// @Brief: 

#include <bvar/bvar.h>
#include "fea_value_dis_accumulator.h"
#include <boost/lexical_cast.hpp>
#include <boost/algorithm/string.hpp>
#include "distribution_util.h"

namespace anti {
namespace themis {
namespace feature_lib{

static bvar::Adder<uint32_t> s_fval_dis_total_query_count;
static bvar::Adder<uint32_t> s_fval_dis_real_query_count;
static bvar::Adder<uint32_t> s_fval_ext_query_count;
static bvar::PerSecond<bvar::Adder<uint32_t> > s_fval_dis_total_query_second(
        "fval_dis_total_query_second", &s_fval_dis_total_query_count, 60);    
static bvar::PerSecond<bvar::Adder<uint32_t> > s_fval_dis_real_query_second(
        "fval_dis_real_query_second", &s_fval_dis_real_query_count, 60); 
static bvar::PerSecond<bvar::Adder<uint32_t> > s_fval_ext_query_second(
        "fval_ext_query_second", &s_fval_ext_query_count, 60);

bool FeaValueDisAccumulator::query(FeatureValueProto* fea) const {
    if (!fea || !_check_fea(*fea)) {
        CWARNING_LOG("check fea fail, feature_id(%lu)", feature_id());
        return false;
    }
    auto gnode = _query_view_node(fea->view_sign());
    if (!gnode) {
        CDEBUG_LOG("level1 view_sign(%lu) not exist, feature_id(%lu)", 
                fea->view_sign(), feature_id());
        return false;
    } 
    auto req = fetch_request(*fea, _extend_fids[1]);
    if (_fea_reqs.find(req->view_sign()) != _fea_reqs.end()) {
        return true;
    }
    std::shared_ptr<FValQueryTransProto> q_ftrans(new (std::nothrow)FValQueryTransProto);
    q_ftrans->add_infos()->mutable_request()->CopyFrom(*req);
    _fea_reqs[req->view_sign()] = q_ftrans;
    _rpc_mgr->query(q_ftrans);
    return true;
}

bool FeaValueDisAccumulator::query(
        const FValQRequestProto& request, 
        FValQResponseProto* response) const {
    if (response == NULL) {
        CFATAL_LOG("input response is NULL");
        return false;
    }
    auto gnode = _query_view_node(request.view_sign());
    if (!gnode || !gnode->value()) {
        CDEBUG_LOG("find view_sign(%lu) fail, feature_id(%lu)", request.view_sign(), feature_id());
        return true;
    }
    return request.has_distpq() ? 
            _bucket_query(request, gnode->value(), response) : 
            _distance_query(request, gnode->value(), response);
}

bool FeaValueDisAccumulator::_bucket_query(
        const FValQRequestProto& request,
        const DistributionItemPtr& item,
        FValQResponseProto* response) const {
    double sum = 0;
    for (uint32_t i = 0; i <= _intervals.size(); ++i) {
        sum += (*item)[i];
    }
    if (sum < _distance.count_threshold()) {
        CDEBUG_LOG("the sum of buckets is less than count_threshold, "
                "view_sign(%lu) feature_id(%lu)", request.view_sign(), feature_id());
        return true;
    }
    const auto& pq = request.distpq();
    if (pq.bucket_idx() >= 0) {
        response->add_value(std::to_string((*item)[pq.bucket_idx()] / sum));
    } else {
        for (uint32_t i = 0; i <= _intervals.size(); ++i) {
            response->add_value(std::to_string((*item)[i]));
        }
    }
    return true;
}

bool FeaValueDisAccumulator::update(const FeatureValueProto& fea) {
    if (!_check_fea(fea)) {
        CWARNING_LOG("check fea fail, feature_id(%lu)", feature_id());
        return false;
    }
    s_fval_dis_total_query_count << 1U;
    auto node = _enter_window(fea.view_sign(), fea.coord());
    if (!node) {
        CFATAL_LOG("get node of view_sign(%lu) fail", fea.view_sign());
        return false;
    }
    int exp = _get_expiration(fea.feature_id(), fea.view_sign());
    if (time(NULL) - node->latest_extend() < exp) {
        return true;
    }   
    
    if (_extend_fea_reqs.find(fea.view_sign()) != _extend_fea_reqs.end()) {
        return true;
    }
    auto iter = _items_reqs.find(fea.view_sign());
    if (iter == _items_reqs.end()) {
        _triger_items_query(fea);
        return true;
    }

    if (iter->second->infos(0).has_response()) {
        auto dis_req = iter->second;
        _items_reqs.erase(iter);
        _triger_extend_fea_query(fea.view_sign(), dis_req);
        return true;
    }
    return true;
}

bool FeaValueDisAccumulator::update_and_query(FeatureValueProto* fea) {
    if (!_check_fea(*fea)) {
        CFATAL_LOG("input fea invalid, feature_id(%lu)", feature_id());
        return false;
    }

    if (!update(*fea)) {
        CDEBUG_LOG("update fea fail, view_sign(%lu), feature_id(%lu)", fea->view_sign(), feature_id());
    }
    
    if (!query(fea)) {
        CDEBUG_LOG("query fea fail, view_sign(%lu), feature_id(%lu)", fea->view_sign(), feature_id());
    }

    return true;
}

void FeaValueDisAccumulator::sync(
        std::vector<std::shared_ptr<FeatureValueProto>>* feas) {
    // 1.sync all extend_fea request to update distribuition
    for (auto& iter : _extend_fea_reqs) {
        _sync(iter.first, iter.second);
    }
    _extend_fea_reqs.clear();
    // 2.triger extend request 
    for (auto& iter : _items_reqs) {
        _triger_extend_fea_query(iter.first, iter.second);
    }
    // 3.complete fea
    for (auto iter = feas->begin(); iter != feas->end(); ++iter) {
        if (!(*iter) || !_check_fea(*(*iter))) {
            continue;
        }
        auto req = _fea_reqs.find(
                fetch_request(*(*iter), _extend_fids[1])->view_sign());
        if (req == _fea_reqs.end()) {
            CDEBUG_LOG("query signal fea fail, fea(%s)", (*iter)->ShortDebugString().data());
            continue;
        }
        _complete_fea(req->second, (*iter).get());
    }
    _items_reqs.clear();
    _fea_reqs.clear();
}

void FeaValueDisAccumulator::_complete_fea(
        const std::shared_ptr<FValQueryTransProto>& req,
        FeatureValueProto* fea) {
    // 1.check request
    if (!req->infos(0).has_response()
            || req->infos(0).response().value_size() == 0) {
        CDEBUG_LOG("extend level 2 fea value fail, feature_id(%lu)", feature_id());
        return;
    }
    // 2.check level one view_sign
    auto gnode = _query_view_node(fea->view_sign());
    if (!gnode || !gnode->value()) {
        return;
    } 
    // 3.set distribution
    auto dis = gnode->value();
    for (uint32_t i = 0; i <= _intervals.size(); ++i) {
        auto bucket = fea->add_buckets();
        bucket->set_idx(i);
        bucket->set_count((*dis)[i]);
    }
    // 4.set bucket 
    double value = 0;
    try {
        value = boost::lexical_cast<double>(req->infos(0).response().value(0));
    } catch (const boost::bad_lexical_cast& e) {
        CWARNING_LOG("boost::Exception : %s", e.what());
        return;
    }
    int bucket_idx = get_interval_idx<double>(_intervals, value); 
    if (bucket_idx < 0) {
        CWARNING_LOG("get bucket idx fail, value(%ld), feature_id(%lu)", value, feature_id());
        return;
    }   
    fea->set_bucket_idx(bucket_idx);
    // 5.go calculate
    fea->set_feature_type(FeatureValueProto::DISTRIBUTION);
    if (!_distance.calculate(fea)) {
        CWARNING_LOG("caculate distance fail, feature_id(%lu)", feature_id());
        return;
    }
    CDEBUG_LOG("query fea succ, fea[%s]", fea->ShortDebugString().data());
    return;
}

void FeaValueDisAccumulator::_triger_items_query(const FeatureValueProto& fea) {
    std::shared_ptr<FValQueryTransProto> u_ftrans(new (std::nothrow)FValQueryTransProto);
    auto info = u_ftrans->add_infos(); 
    info->mutable_request()->CopyFrom(*fetch_request(fea, _extend_fids[0]));
    info->mutable_request()->set_max_value_num(_window_conf.max_item);
    _items_reqs[fea.view_sign()] = u_ftrans;
    _rpc_mgr->query(u_ftrans);
    return;
}

void FeaValueDisAccumulator::_triger_extend_fea_query(
        int64_t view_sign,
        const std::shared_ptr<FValQueryTransProto>& req) {
    s_fval_dis_real_query_count << 1U;
    auto node = _query_view_node(view_sign);
    if (!node) {
        CWARNING_LOG("view_sign(%lu) has expired, no need call extend", view_sign);
        return;
    }

    if (_extend_fea_reqs.size() > _max_req_num) {
        CWARNING_LOG("current ext fea req size(%u) exceeds max_req_num(%u), wait next time", 
                _extend_fea_reqs.size(), _max_req_num);
        return;
    }
    auto ft = _convert_request(req);
    if (!ft) {
        return;
    }
    _extend_fea_reqs[view_sign] = ft;
    _rpc_mgr->query(ft);
    return;
}

bool FeaValueDisAccumulator::_init(const comcfg::ConfigUnit& conf) {    
    try {
        const AccValueType DEFAULT_TYPE = CLICK_VALUE;
        _acc_value_type = DEFAULT_TYPE;
        if (conf["acc_type"].selfType() != comcfg::CONFIG_ERROR_TYPE) {
            _acc_value_type = (conf["acc_type"].to_int32() == 0) ? CLICK_VALUE : UNIQ_VALUE;
        }
        const uint32_t MAX_REQ_NUM = 10U;
        _max_req_num = MAX_REQ_NUM;
        if (conf["max_req_num"].selfType() != comcfg::CONFIG_ERROR_TYPE) {
            _max_req_num = conf["max_req_num"].to_uint32();
        }
        // init dis conf
        if (!parse_intervals<double>(
                conf["interval_endpoints"].to_cstr(),
                &_intervals)) {
            CWARNING_LOG("init interval_endpoints(%s) fail, feature_id(%lu)", 
                    conf["interval_endpoints"].to_cstr(), feature_id());
            return false;
        }
        // The default value of max_distance_magnify is 1.0. It means that pc will not to be magnified.
        double max_distance_magnify = 1.0;
        const double MAX_DISTANCE_MAGNIFY_DEFAULT = 1.0;
        conf["max_distance_magnify"].get_double(&max_distance_magnify, MAX_DISTANCE_MAGNIFY_DEFAULT);
        if (max_distance_magnify < MAX_DISTANCE_MAGNIFY_DEFAULT) {
            max_distance_magnify = MAX_DISTANCE_MAGNIFY_DEFAULT;
            CWARNING_LOG("Max_distance_magnify is smaller than 1.0 and the value will be 1.0");
        }
        if (!_distance.init(
                    conf["func_type"].to_cstr(), 
                    conf["stand_prob"].to_cstr(), 
                    conf["count_threshold"].to_int64(),
                    max_distance_magnify)) {
            CWARNING_LOG("init distance caculator fail, func_type:%s, stand_prob:%s, "
                    "count_threshold:%ld, max_distance_magnify:%lf, feature_id(%lu)", 
                    conf["func_type"].to_cstr(),
                    conf["stand_prob"].to_cstr(),
                    conf["count_threshold"].to_int64(),
                    max_distance_magnify,
                    feature_id());
            return false;
        }
        // init extend conf 
        if (conf["extend"].size() != 2U) {
            CWARNING_LOG("invalid extend configure size(%u) in conf", conf["extend"].size());
            return false;
        }
        for (uint32_t i = 0; i < conf["extend"].size(); ++i) {
            _extend_fids.push_back(conf["extend"][i]["feature_id"].to_uint64());
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std::Exception : %s", e.what());
        return false;
    }
    return true;
}


void FeaValueDisAccumulator::_sync(
        uint64_t view_sign,
        const std::shared_ptr<FValQueryTransProto>& fea_trans) { 
    auto gnode = _query_view_node(view_sign);
    if (!gnode) {
        CDEBUG_LOG("query_view_node of view_sign(%lu) fail, feature_id(%lu)", view_sign, feature_id());
        return;
    }
    auto distribution = _calculate_dis(fea_trans);
    if (distribution) {
        gnode->set_value(distribution);
    }
    gnode->set_latest_extend(time(NULL));
    CDEBUG_LOG("sync succ, feature_id(%lu)", feature_id());
    return;
}

std::shared_ptr<FValQueryTransProto> FeaValueDisAccumulator::_convert_request(
        const std::shared_ptr<FValQueryTransProto>& req) {
    if (!req->infos(0).has_response() || req->infos(0).response().value_size() == 0) {
        CDEBUG_LOG("invalid response(%s)", req->ShortDebugString().data());
        return NULL;
    }
    
    if (static_cast<uint64_t>(req->infos(0).response().value_size()) < _window_conf.remain) {
        CDEBUG_LOG("too few gloable items(%ld), should exeeds remain(%ld)", 
                req->infos(0).response().value_size(), _window_conf.remain);
        return NULL;
    }
    std::shared_ptr<FValQueryTransProto> ft(new (std::nothrow) FValQueryTransProto);
    if (!ft) {
        CFATAL_LOG("new FValQueryTransProto fail, feature_id(%lu)", feature_id());
        return NULL;
    }
    // construct extend fea request
    s_fval_ext_query_count << req->infos(0).response().value_size();
    for (int32_t i = 0; i < req->infos(0).response().value_size(); ++i) {
        auto ext_fea = ft->add_infos();
        if (!ext_fea) {
            CFATAL_LOG("add FValQInfoProto fail, feature_id(%lu)", feature_id());
            return NULL;
        }
        auto request = ext_fea->mutable_request();
        if (!request) {
            CFATAL_LOG("new request fail, feature_id(%lu)", feature_id());
            return NULL;
        }
        request->set_feature_id(_extend_fids[1]);
        try {
            request->set_view_sign(
                    boost::lexical_cast<uint64_t>(req->infos(0).response().value(i)));
        } catch (const boost::bad_lexical_cast& e) {
            CWARNING_LOG("boost::Exception : %s", e.what());
            return NULL;
        }
    }
    CDEBUG_LOG("construct request succ, request[%s]", ft->ShortDebugString().data());
    return ft;
}

FeaValueDisAccumulator::GNodePtr 
        FeaValueDisAccumulator::_query_view_node(uint64_t view_sign) {
    if (!_window) {
        CFATAL_LOG("window ptr is NULL, feature_id(%lu)", feature_id());
        return GNodePtr();
    }
    return _window->find(view_sign);
}

const FeaValueDisAccumulator::GNodePtr 
        FeaValueDisAccumulator::_query_view_node(uint64_t view_sign) const {
    if (!_window) {
        CFATAL_LOG("window ptr is NULL, feature_id(%lu)", feature_id());
        return NULL;
    }
    return _window->find(view_sign);
}

FeaValueDisAccumulator::GNodePtr 
        FeaValueDisAccumulator::_enter_window(uint64_t view_sign, uint64_t coord) {
    GNodePtr node(new (std::nothrow) GNode(view_sign, coord));
    if (!node) {
        CFATAL_LOG("new node fail, feature_id(%lu)", feature_id());
        return NULL;
    }
    auto old_node = _query_view_node(view_sign);
    if (old_node) {
        node->set_value(old_node->value());
        node->set_latest_extend(old_node->latest_extend());
    }
    _window->insert(node);
    return node;
}

FeaValueDisAccumulator::DistributionItemPtr FeaValueDisAccumulator::_calculate_dis(
        const std::shared_ptr<FValQueryTransProto>& fea_trans) {
    DistributionItemPtr dis_ptr(new (std::nothrow) DistributionItem());
    if (!dis_ptr) {
        CFATAL_LOG("new DistributionItem obj fail");
        return NULL;
    }
    uint64_t succ = 0; 
    for (int32_t i = 0; i < fea_trans->infos_size(); ++i) {
        // 1.check response
        if (!fea_trans->infos(i).has_response() 
                || fea_trans->infos(i).response().value_size() == 0) {
            CDEBUG_LOG("no response found, feature_id(%lu), extend_feature_id(%lu)",
                    feature_id(), fea_trans->infos(i).request().feature_id());
            continue;
        }
        // 2. calculate distribution and bucket idx
        double value = 0;
        int bucket_idx = 0; 
        try {
            value = boost::lexical_cast<double>(fea_trans->infos(i).response().value(0));
            bucket_idx = get_interval_idx<double>(_intervals, value);
            if (bucket_idx < 0) {
                CWARNING_LOG("get bucket idx fail, value(%ld)", value);
                continue;
            }
        } catch (const boost::bad_lexical_cast& e) {
            CWARNING_LOG("boost::Exception : %s", e.what());
            continue;
        }   
        DistributionItem di;
        di[bucket_idx] = _acc_value_type == CLICK_VALUE ? value : 1; 
        *dis_ptr += di; 
        ++succ;
    } 
    
    if (succ < _window_conf.remain) {
        CWARNING_LOG("confidetial response items[%u] < confititail remain(%u), distribution not update", succ, _window_conf.remain);
        return NULL;
    }
    CDEBUG_LOG("confidential response item[%u], confidential remain(%u)", succ, _window_conf.remain);
    return dis_ptr;
}

bool FeaValueDisAccumulator::_check_fea(const FeatureValueProto& fea) const {
    if (!fea.has_feature_id() || fea.feature_id() != feature_id()    
            || fea.feature_type() != FeatureValueProto::FEATURE_VALUE_EXTEND 
            || !fea.has_fea_serv_trans()
            || fea.fea_serv_trans().infos_size() != 2) {
        CWARNING_LOG("input fea invalid, feature_id(%lu)", feature_id());  
        return false;
    }
    for (uint32_t i = 0; i < 2U; ++i) {
        auto req = fetch_request(fea, _extend_fids[i]);
        if (!req || !req->has_view_sign()) {
            CWARNING_LOG("request for feature_id(%lu) NOT found or invalid, request[%s]", 
                    _extend_fids[i], fea.ShortDebugString().data());
            return false;
        }
    }   
    return true;
}

} // namespace feature_lib
} // namespace themis
} // namespace anti
/* vim: set ts=4 sw=4: */

