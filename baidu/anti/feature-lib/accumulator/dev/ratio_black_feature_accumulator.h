// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: ratio_black_feature_accumulator.h
// @Last modified: 2017-03-16 14:09:21
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_RATIO_BLACK_FEATURE_ACCUMULATOR_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_RATIO_BLACK_FEATURE_ACCUMULATOR_H

#include "feature_accumulator_interface.h"
#include <item.h>
#include <click_segment.hpp>

namespace anti {
namespace themis {
namespace feature_lib {

class RatioBlackFeatureAccumulator : public FeatureAccumulatorInterface {
public:
    RatioBlackFeatureAccumulator() : 
            _feature_id(0LU), 
            _version(0LU),
            _remain(0L) {
    }

    virtual ~RatioBlackFeatureAccumulator() {
        uninit();
    }

    virtual uint64_t feature_id() const {
        return _feature_id;
    }
    virtual bool init(const comcfg::ConfigUnit& conf);
    virtual void uninit();

    virtual bool update(const FeatureValueProto& fea);
    virtual bool update_and_query(FeatureValueProto* fea);
    virtual bool query(FeatureValueProto* fea) const;

    virtual bool load(PosixIoReaderInterface *reader);
    virtual bool dump(PosixIoWriterInterface *writer) const;

private:
    typedef anti::themis::common_lib::RatioBlackNode RatioBlackNode;
    typedef anti::themis::common_lib::ClickSegment<RatioBlackNode> ClickSegment;

    bool _check_feature(const FeatureValueProto& fea) const;
    bool _set_feature(const RatioBlackNode* node, FeatureValueProto* fea) const;

    uint64_t _feature_id;
    uint64_t _version;
    ClickSegment _seg;
    int64_t _remain;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif //APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_RATIO_BLACK_FEATURE_ACCUMULATOR_H

/* vim: set ts=4 sw=4: */

