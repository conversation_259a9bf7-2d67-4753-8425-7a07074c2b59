// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: basic_feature_accumulator.h
// @Last modified: 2017-03-16 11:58:42
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_BASIC_FEATURE_ACCUMULATOR_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_BASIC_FEATURE_ACCUMULATOR_H

#include <com_log.h>
#include "feature_accumulator_interface.h"

namespace anti {
namespace themis {
namespace feature_lib {

class BasicFeatureAccumulator : public FeatureAccumulatorInterface {
public:
    BasicFeatureAccumulator() : _feature_id(0LU) {}
    virtual ~BasicFeatureAccumulator() {
        uninit();
    }

    virtual uint64_t feature_id() const {
        return _feature_id;
    }

    virtual bool init(const comcfg::ConfigUnit& conf) {
        try {
            _feature_id = conf["feature_id"].to_uint64();
        } catch (const comcfg::ConfigException& e) {
            CWARNING_LOG("ConfigException : %s", e.what());
            return false;
        }
        CWARNING_LOG("init BasicFeatureAccumulator(%lu) success", _feature_id);
        return true;
    }

    virtual void uninit() {}

    virtual bool update(const FeatureValueProto& fea) {
        return true;
    }
    virtual bool update_and_query(FeatureValueProto* fea) {
        return true;
    }
    virtual bool query(FeatureValueProto* fea) const {
        return true;
    }

    virtual bool load(PosixIoReaderInterface *reader) {
        return true;
    }
    virtual bool dump(PosixIoWriterInterface *writer) const {
        return true;
    }

private:
    uint64_t _feature_id;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_BASIC_FEATURE_ACCUMULATOR_H

/* vim: set ts=4 sw=4: */

