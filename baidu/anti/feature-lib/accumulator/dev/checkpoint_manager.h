// Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
// @Author: ch<PERSON><PERSON><PERSON><PERSON>(ch<PERSON><PERSON><PERSON><PERSON>@baidu.com)
// 
// @File: checkpoint_manager.h
// @Last modified: 2017-03-15 15:54:44
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_CHECKPOINT_MANAGER_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_CHECKPOINT_MANAGER_H

#include "feature_accumulator_interface.h"
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <com_log.h>
#include "thread_safe_queue.h"
#include <boost/lexical_cast.hpp>
#include <boost/algorithm/string.hpp>

namespace anti {
namespace themis {
namespace feature_lib {

struct FeaInfo {
    FeaInfo() : fea_id(0LU), coord(0L), fea_ptr(NULL) {}
    FeaInfo(uint64_t id, int64_t co, std::shared_ptr<FeatureAccumulatorInterface> p) : 
            fea_id(id), coord(co), fea_ptr(p) {}
    uint64_t fea_id;
    int64_t coord;
    std::shared_ptr<FeatureAccumulatorInterface> fea_ptr;
};

struct DescInfo {
    DescInfo() : fea_id(0LU), off_set(0), length(0U), write_record_type(false) {}
    DescInfo(uint64_t id, const char *f, off_t off, size_t le) :
            fea_id(id),
            file(f),
            off_set(off),
            length(le),
            write_record_type(false) {}
    DescInfo(uint64_t id, const char *rt, const char *f, off_t off, size_t le) :
            fea_id(id),
            record_type(rt),
            file(f),
            off_set(off),
            length(le),
            write_record_type(true) {}
    uint64_t fea_id;
    std::string record_type;
    std::string file;
    off_t off_set;
    size_t length;
    bool write_record_type;

    std::string serialize() const {
        std::string s;
        if (write_record_type) {
            s = std::to_string(fea_id)
                    + "\t" + file
                    + "\t" + std::to_string(off_set)
                    + "\t" + std::to_string(length)
                    + "\t" + record_type
                    + "\n";
        } else {
            s = std::to_string(fea_id)
                    + "\t" + file
                    + "\t" + std::to_string(off_set)
                    + "\t" + std::to_string(length)
                    + "\n";
        }
        return s;
    }

    bool deserialize(const std::string &str) {
        std::vector<std::string> fields;
        boost::split(fields, str, boost::is_any_of("\t"));
        if (fields.size() < 4U && fields.size() > 5U) {
            CWARNING_LOG("DescInfo deserialize size[%d] invalid!", fields.size());
            return false;
        }   
        try {
            fea_id = boost::lexical_cast<uint64_t>(fields[0]);
            file = fields[1];
            off_set = boost::lexical_cast<off_t>(fields[2]);
            length = boost::lexical_cast<size_t>(fields[3]);
            if (fields.size() >= 5) {
                record_type = fields[4];
            }
        } catch (const boost::bad_lexical_cast &e) {
            CWARNING_LOG("invalid DescInfo(%s), bad_lexical_cast(%s)", str.c_str(), e.what());
            return false;
        }   
        return true;
    }
};

class CheckpointManager {
public:
    CheckpointManager() = delete;
    CheckpointManager(const std::string &checkpoint_path) :
            _checkpoint_path(checkpoint_path),
            _record_type(""),
            _use_record_type(false) {}
    CheckpointManager(const std::string &checkpoint_path,
            const std::string record_type) :
            _checkpoint_path(checkpoint_path),
            _record_type(record_type),
            _use_record_type(true) {}
    virtual ~CheckpointManager() {}
    using CoordMap = std::unordered_map<uint64_t, int64_t>;
    using AccMap = std::unordered_map<uint64_t, 
            std::shared_ptr<FeatureAccumulatorInterface>>;
    bool dump(const CoordMap &coord_map, const AccMap &acc_map);
    bool load(CoordMap *coord_map, AccMap *acc_map);
    void set_version_id(const std::string& version_id) {
        _version_id = version_id;
    }

private:
    using FileDescsMap = std::unordered_map<std::string,
            std::vector<DescInfo>>;

    std::vector<DescInfo> _thread_dump(
            const std::string &ckpt_name, 
            std::shared_ptr<ThreadSafeQueue<FeaInfo>> queue);
    std::vector<FeaInfo> _thread_load(
            const std::string &ckpt_name,
            const std::vector<DescInfo> descs,
            AccMap *acc_map);
    bool _fea_load(
            const DescInfo &desc,
            PosixIoReaderInterface *reader,
            FeaInfo *fea_info);
            
    bool _dump_desc(const std::vector<DescInfo> &desc_infos);
    bool _cover_ckpt();
    bool _load_desc(FileDescsMap *file_descs_map);
    std::shared_ptr<ThreadSafeQueue<FeaInfo>> _push_feas(
            const CoordMap &coord_map, 
            const AccMap &acc_map);

    std::string _checkpoint_path;
    std::string _version_id;
    std::string _record_type;
    bool _use_record_type;
};

} // feature_lib
} // themis
} // anti


#endif // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_CHECKPOINT_MANAGER_H

/* vim: set ts=4 sw=4: */

