// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: fea_value_ratio_accumulator.h
// @Last modified: 2017-10-31 16:43:37
// @Brief:

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_FEA_VALUE_RATIO_ACCUMULATOR_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_FEA_VALUE_RATIO_ACCUMULATOR_H

#include "extend_accumulator_base.h"

namespace anti {
namespace themis {
namespace feature_lib {

class RateItem;
typedef GroupNode<RateItem> GroupRateItem;
typedef ExtendAccumulatorBase<GroupRateItem> FeatureValueRateAccBase;

enum Rate {
    NUMERATOR = 0,
    DENOMINATOR,
    TOTAL
};

class FeatureValueRateAccumulator : public FeatureValueRateAccBase {
public:
    FeatureValueRateAccumulator() :
            _time_threshold(0L) {}
    virtual ~FeatureValueRateAccumulator() {}
    
    virtual bool query(FeatureValueProto* fea) const;
    virtual bool update_and_query(FeatureValueProto* fea);
    virtual bool update(const FeatureValueProto& fea);
    virtual void sync(std::vector<std::shared_ptr<FeatureValueProto>>* feas);
    
private:
    virtual bool _init(const comcfg::ConfigUnit& conf);
    virtual void _uninit() {};

private:
    bool _check_fea(const FeatureValueProto& fea) const;
    std::shared_ptr<GroupRateItem> _enter_window(uint64_t view_sign, uint64_t coord);
    int64_t _get_expiration(uint64_t view_sign) const {
        return  _window_conf.expiration == 0 ?
            0 : _window_conf.expiration + (feature_id() ^ view_sign) % _window_conf.expiration;
    }
    void _triger_query(const FeatureValueProto& fea);
    typedef std::shared_ptr<FValQueryTransProto> RateQuery;
    std::shared_ptr<RateItem> _caculate_rate(const RateQuery& req) const;
    
private:
    typedef std::unordered_map<uint64_t, RateQuery> RateQueryMaps;
    mutable RateQueryMaps _requests;
    struct ExtConf {
        uint64_t ext_id;
        int64_t remain;
        
        ExtConf(uint64_t eid, int64_t re) : 
                ext_id(eid),
                remain(re) {}
        ExtConf(const ExtConf& e) : ExtConf(e.ext_id, e.remain) {}
        ExtConf() : ExtConf(0U, 0L) {}
    };

    ExtConf _ext_conf[TOTAL];
    int64_t _time_threshold;
};

inline bool response_check(const FValQInfoProto& info) {
    return info.has_response() 
            && info.response().value_size() == 1 && 
            info.response().has_coord();
}

inline const FValQResponseProto* fetch_response(
        const std::shared_ptr<FValQueryTransProto>& req, uint64_t feature_id) {
    for (int32_t i = 0; i < req->infos_size(); ++i) {
        if (req->infos(i).request().feature_id() == feature_id) {
            return &req->infos(i).response();
        }
    }
    return NULL;
}

struct RateItem {
    typedef std::pair<int64_t, int64_t> Item;
    Item items[TOTAL];

    Item& operator[] (int32_t i) {
        return items[i];
    }
    template<typename Archive>
    bool serialize(Archive* ar) const {
        if (!ar) {
            return false;
        }
        return anti::themis::common_lib::t_write(items[NUMERATOR], ar)
                && anti::themis::common_lib::t_write(items[DENOMINATOR], ar);
    }

    template<typename Archive>
    bool deserialize(Archive* ar) {
        if (!ar) {
            return false;
        }
        return anti::themis::common_lib::t_read(&items[NUMERATOR], ar)
                && anti::themis::common_lib::t_read(&items[DENOMINATOR], ar);
    }
};

} // namespace feature_lib
} // namespace themis
} // namespace anti

#endif // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_FEA_VALUE_RATIO_ACCUMULATOR_H
/* vim: set ts=4 sw=4: */

