// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// <AUTHOR>
// @brief

#include "fea_deviation_feature_accumulator.h"
#include <sstream>
#include <cmath>
#include <algorithm>
#include <com_log.h>
#include <float.h>

namespace anti {
namespace themis {
namespace feature_lib {

const uint32_t LEAST_NUM_TO_CHECK_INFLECTION = 3;

bool FeaDeviationFeatureAccumulator::init(const comcfg::ConfigUnit& conf) {
    try {
        _feature_id = conf["feature_id"].to_uint64();
        uint32_t smooth_window = 1;
        if (conf["smooth_window"].selfType() != comcfg::CONFIG_ERROR_TYPE) {
            smooth_window = conf["smooth_window"].to_uint32();
        }
        _smooth_buffer.reset(new SmoothBuffer(smooth_window));
        if (!_smooth_buffer) {
            CFATAL_LOG("new smooth buffer failed!");
            return false;
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std::exception : %s", e.what());
        return false;
    }
    return true;
}

bool FeaDeviationFeatureAccumulator::update(const FeatureValueProto& fea) {
    if (!fea.has_fea_deviation_field() ||
            !fea.fea_deviation_field().has_devia_fea_value() ||
            !fea.fea_deviation_field().has_devia_fea_count()) {
        CWARNING_LOG("illegal feature: %s", fea.ShortDebugString().c_str());
        return false;
    }
    auto& fea_devia_field = fea.fea_deviation_field();
    if (_cur_fea && _cur_fea->value == fea_devia_field.devia_fea_value()) {
        _cur_fea->count += fea_devia_field.devia_fea_count();
        return true;
    }
    _store_smooth_fea(_cur_fea);
    // new item
    _cur_fea.reset(new Item(
            fea_devia_field.devia_fea_value(),
            fea_devia_field.devia_fea_count(),
            _get_total_sum(_original_feas)));
    if (!_cur_fea) {
        CFATAL_LOG("new Item failed!");
        return false;
    }
    _original_feas.emplace_back(_cur_fea);
    // add it to map
    auto itor = _original_feas.end();
    _fea_map[_cur_fea->value] = --itor;
    return true;
}

bool FeaDeviationFeatureAccumulator::dump(PosixIoWriterInterface *writer) const {
    std::string dump_str = "";
    if (!_dump(&dump_str)) {
        CWARNING_LOG("dump to str failed!");
        return false;
    }
    if (writer->write(dump_str.data(), dump_str.length()) 
            != static_cast<int>(dump_str.length())) {
        CWARNING_LOG("write file failed!");
        return false;
    }
    return true; 
}

bool FeaDeviationFeatureAccumulator::query(FeatureValueProto* fea) const {
    if (fea == NULL) {
        CFATAL_LOG("fea is NULL");
        return false;
    }
    if (!fea->has_fea_deviation_field() ||
            !fea->fea_deviation_field().has_devia_fea_value() ||
            !fea->fea_deviation_field().has_devia_fea_count()) {
        CWARNING_LOG("illegal feature: %s", fea->ShortDebugString().c_str());
        return false;
    }
    auto field = fea->mutable_fea_deviation_field();
    int64_t range_sum = _get_range_sum(_original_feas, field->devia_fea_value());
    int64_t total_sum = _get_total_sum(_original_feas);
    int64_t inflect_range_sum = _get_inflect_range_sum(_smooth_feas);
    int64_t smooth_total_sum = _get_total_sum(_smooth_feas);
    if (range_sum > total_sum || inflect_range_sum > smooth_total_sum ||
            range_sum < 0) {
        CWARNING_LOG("count illegal![range_sum:%ld, total_sum:%ld]"
                " [inflect_range_sum:%ld, smooth_total_sum:%ld]",
                range_sum, total_sum, inflect_range_sum, smooth_total_sum);
        return false;
    }
    field->set_devia_ratio(total_sum != 0 ?
            (static_cast<double>(range_sum)) / total_sum : 0.00000001);
    field->set_devia_inflect_ratio(smooth_total_sum != 0 ?
            (static_cast<double>(inflect_range_sum)) / smooth_total_sum : 0.00000001);
    
    double variance = DBL_MAX;
    if (_get_variance(_smooth_feas, &variance)) {
        field->set_variance(variance);
    }
    return true;
}

void FeaDeviationFeatureAccumulator::_store_smooth_fea(const ItemPtr& cur_fea) {
    if (!cur_fea) {
        return;
    }
    // push and get smooth value
    int64_t smooth_value = 0;
    if (!_push_and_get(cur_fea->count, &smooth_value)) {
        CDEBUG_LOG("skip a item for smooth! value:%ld count:%ld total:%ld",
                cur_fea->value, cur_fea->count, cur_fea->pre_sum);
        return;
    }
    ItemPtr smooth_fea(new Item(cur_fea->value, smooth_value,
            _get_total_sum(_smooth_feas)));
    // add to deque
    _smooth_feas.emplace_back(smooth_fea);
    if (_update_max_point(_smooth_feas)) {
        _inflect_point.reset();
        return;
    }
    _update_inflect_point(_smooth_feas);
}

int64_t FeaDeviationFeatureAccumulator::_get_total_sum(
        const ItemPtrDeque& sorted_feas) const {
    if (sorted_feas.empty()) {
        return 0;
    }
    auto last = sorted_feas.back();
    return last->range_sum();
}

int64_t FeaDeviationFeatureAccumulator::_get_range_sum(
        const ItemPtrDeque& sorted_feas, int64_t value) const {
    if (sorted_feas.empty()) {
        return 0;
    }
    auto it = _fea_map.find(value);
    if (it != _fea_map.end()) {
        auto item_ptr = (*(it->second));
        return item_ptr->range_sum();
    }
    auto first_ptr = sorted_feas.front();
    auto last_ptr = sorted_feas.back();
    if (value < first_ptr->value) {
        return 0;
    } else if (value > last_ptr->value) {
        return _get_total_sum(sorted_feas);
    }
    return -1;
}

int64_t FeaDeviationFeatureAccumulator::_get_inflect_range_sum(
        const ItemPtrDeque& sorted_feas) const {
    if (!_inflect_point) {
        return _get_total_sum(sorted_feas);
    }
    return _inflect_point->range_sum();
}

bool FeaDeviationFeatureAccumulator::_update_max_point(
        const ItemPtrDeque& sorted_feas) {
    if (sorted_feas.empty()) {
        return false;
    }
    if (!_max_point) {
        _max_point = sorted_feas.back();
        return true;
    }
    auto last = sorted_feas.back();
    if (_max_point->count >= last->count) {
        return false;
    }
    _max_point = last;
    return true;
}

void FeaDeviationFeatureAccumulator::_update_inflect_point(
        const ItemPtrDeque& sorted_feas) {
    if (sorted_feas.size() < LEAST_NUM_TO_CHECK_INFLECTION || _inflect_point) {
        // no need check inflect point if feas not enough or
        // inflect point is already be set
        return;
    }
    ItemPtr after_ptr = _get_ptr(sorted_feas, TAILENDER);
    ItemPtr check_ptr = _get_ptr(sorted_feas, PENULTIMATE);
    ItemPtr before_ptr = _get_ptr(sorted_feas, ANTEPENULTIMATE);
    if (_is_inflect(after_ptr, check_ptr, before_ptr)) {
        _inflect_point = check_ptr;
    }
}

bool FeaDeviationFeatureAccumulator::_push_and_get(
        int64_t original_value, int64_t* smooth_value) {
    _smooth_buffer->push_back(original_value);
    if (!_smooth_buffer->full()) {
        return false;
    }
    int64_t total = std::accumulate(_smooth_buffer->begin(),
            _smooth_buffer->end(), 0);
    *smooth_value = total / _smooth_buffer->capacity();
    return true;
}

bool FeaDeviationFeatureAccumulator::_is_inflect(const ItemPtr& after, 
        const ItemPtr& check, const ItemPtr& before) {
    if (after->count > check->count && before->count >= check->count) {
        return true;
    }
    return false;
}

bool FeaDeviationFeatureAccumulator::_dump(std::string* data) const {
    if (data == NULL) {
        CFATAL_LOG("param [data] NULL");
        return false;
    }
    int64_t total_sum = _get_total_sum(_original_feas);
    int64_t smooth_total_sum = _get_total_sum(_smooth_feas);
    std::stringstream ss;
    ss << "=============================\n";
    ss << "feature_id:" << _feature_id << " ";
    // fill variance to checkpoint
    double smooth_variance = 0.0;
    bool ret = _get_variance(_smooth_feas, &smooth_variance);
    if (!ret) {
        CWARNING_LOG("get variance of smooth feas failed!"); 
    } else {
        ss << "smooth_variance:" << smooth_variance << " ";
    }
    double original_variance = 0.0;
    ret = _get_variance(_original_feas, &original_variance);
    if (!ret) {
        CWARNING_LOG("get variance of original feas failed!"); 
    } else {
        ss << "original_variance:" << original_variance << " ";
    }
    ss << "\n";
    if (_max_point) {
        ss << "max_point[value:" << _max_point->value;
        ss << " count:" << _max_point->count << "]\n";
    }
    if (_inflect_point) {
        ss << "inflect_point[value:" << _inflect_point->value;
        ss << " count:" << _inflect_point->count << "]\n";
    }
    ss << "=============================\n";
    ss << "original feature distribution\n";
    for (auto item : _original_feas) {
        int64_t range_sum = _get_range_sum(_original_feas, item->value);
        ss << "value:" << item->value << " count:" << item->count;
        ss << " pre_sum:" << item->pre_sum << " ratio: ";
        ss << (total_sum != 0 ? (static_cast<double>(range_sum)) / total_sum : 0.0);
        ss << "\n";
    }
    ss << "=============================\n";
    ss << "smooth feature distribution\n";
    for (auto item : _smooth_feas) {
        ss << "value:" << item->value << " count:" << item->count;
        ss << " pre_sum:" << item->pre_sum << "\n";
    }
    *data = ss.str();
    return true;
}

bool FeaDeviationFeatureAccumulator::_get_variance(
        const ItemPtrDeque& sorted_feas,
        double * const variance_ptr) const{
    if (sorted_feas.size() == 0) {
        CWARNING_LOG("features size is 0!");
        return false;
    }
    double val_sum = 0.0;
    for (const auto& fea : sorted_feas) {
        val_sum += fea->count * fea->value;
    }
    double count_sum = sorted_feas.back()->pre_sum + sorted_feas.back()->count;
    double mean = val_sum / count_sum;

    double& variance = *variance_ptr;
    variance = 0.0;
    for (const auto& fea : sorted_feas) {
        variance += std::pow(fea->value - mean, 2) * fea->count;
    }
    variance = variance / count_sum;
    return true;
}

}
}
}
