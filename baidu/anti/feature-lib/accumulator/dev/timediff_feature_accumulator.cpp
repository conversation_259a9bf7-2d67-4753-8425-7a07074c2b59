// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @brief 

#include "timediff_feature_accumulator.h"

namespace anti {
namespace themis {
namespace feature_lib {

const int32_t kNodeListMaxLength = 512;
const int64_t kMaxDiffValue = 0x1FFFFFFF;

TDAccumulator::TDAccumulator() : _feature_id(0u), _version(0u), _range(0) {}
TDAccumulator::~TDAccumulator() { uninit(); }

bool TDAccumulator::init(const comcfg::ConfigUnit& conf) {
    int64_t window_length = 0;
    try {
        _feature_id = conf["feature_id"].to_uint64();
        _version = conf["version"].to_uint64();
        window_length = conf["window_length"].to_int64();
        if (conf["range"].selfType() == comcfg::CONFIG_ERROR_TYPE) {
            CDEBUG_LOG("no range option! use the default value %ld", _range);
        } else {
            _range = conf["range"].to_int64();
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    }

    if (window_length < 0) {
        CFATAL_LOG("invalid window_length(%ld)", window_length);
        return false;
    }

    _goe_list.reset(new (std::nothrow) GOEList(window_length, kNodeListMaxLength));
    if (!_goe_list) {
        CFATAL_LOG("alloc goe_list failed");
        return false;
    }

    CWARNING_LOG("init TDAccumulator(%lu) success", _feature_id);
    return true;
}
void TDAccumulator::uninit() { _goe_list.reset(); }

bool TDAccumulator::update(const FeatureValueProto& fea) {
    if (!check_feature(fea)) {
        CWARNING_LOG("invalid fea");
        return false;
    }

    SecondNode node(fea.view_sign(), fea.log_time(), fea.log_time());
    auto ptr = _goe_list->insert(node);
    if (!ptr) {
        CWARNING_LOG("insert feat failed, [fea:%s]",
                fea.ShortDebugString().data());
        return false;
    }

    return true;
}

bool TDAccumulator::query(FeatureValueProto* fea) const {
    if (!check_feature(*fea)) {
        CWARNING_LOG("invalid fea");
        return false;
    }
    
    int32_t diff = kMaxDiffValue;
    auto list_ptr = _goe_list->query(fea->view_sign());
    if (!list_ptr) {
        // treat query failed case as normal feature
        CWARNING_LOG("query feat_list failed. [fea:%s]",
                fea->ShortDebugString().data());
    } else {
        SecondNode node(fea->view_sign(), fea->log_time(), fea->log_time());
        // check this list_ptr
        for (auto iter = list_ptr->begin(); iter != list_ptr->end(); ++iter) {
            if (iter->coord() > node.coord()) { continue; }
            if (iter->coord() == node.coord() && iter->id() == node.id()) {
                // TODO use log_time[ms] as this feature's uniq_id!
                // May need fix it!
                // SecondNode is Second-Unit. In this Second, may exist many
                // features. Only treat the oldest feature as the normal and save
                // its id in the node!
                // so if the id of Node in the list equal current feature, means this node
                // is itself!. Just Skip it!
                continue;
            } 

            diff = node.coord() - iter->coord();
            if (_range == 0 || diff > _range) {
                break;  // don't need more comparisions!
            } else {
                fea->add_neighbor_feat_distances(diff);
            } 
        }
    }

    // for compatibility
    if (fea->neighbor_feat_distances_size() > 0) {
        fea->set_pre_feat_distance(fea->neighbor_feat_distances(0));
    } else {
        fea->set_pre_feat_distance(diff);
    }
    fea->set_valid(true);

    return true;
}

bool TDAccumulator::update_and_query(FeatureValueProto* fea) {
    if (!update(*fea)) {
        CWARNING_LOG("update feat failed");
        return false;
    }
    if (!query(fea)) {
        CWARNING_LOG("query feat failed");
        return true;
    }
    return true;
}

bool TDAccumulator::load(PosixIoReaderInterface *reader) {
    uint64_t ver = 0LU;
    if (reader->read(&ver, sizeof(ver)) != static_cast<int>(sizeof(ver))) {
       CWARNING_LOG("load version fail");
       return false;
    }

    // version > checkpoint version return true directly
    if (ver < _version) {
        CWARNING_LOG("checkpoint version is low, skip this checkpoint.[%d -> %d]",
                _version, ver);
       return true;
    }
    _version = ver;

    if (!_goe_list->deserialize(reader)) {
        CWARNING_LOG("goe_list deserialize failed. [id:%lu]", _feature_id);
        return false;
    }
    return true;
}

bool TDAccumulator::dump(PosixIoWriterInterface *writer) const {
    if (writer->write(&_version, sizeof(_version)) 
            != static_cast<int>(sizeof(_version))) {
        CWARNING_LOG("write version(%lu) fail, feature_id(%lu)", 
                _version, _feature_id);
        return false;
    }

    if (!_goe_list->serialize(writer)) {
        CWARNING_LOG("dump goe_list failed. [id:%lu]", _feature_id);
        return false;
    }
    return true;
}

bool TDAccumulator::check_feature(const FeatureValueProto& fea) const {
    if (fea.feature_id() != _feature_id
                || fea.feature_type() != FeatureValueProto::TIME_DIFF) {
        CFATAL_LOG("invalid feature. [fea:%s]", fea.ShortDebugString().data());
        return false;
    }
    return true;
}

}
}
}

