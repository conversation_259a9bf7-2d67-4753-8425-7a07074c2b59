// Copyright 2016 Baidu Inc. All Right Reserved.
// Author <PERSON> (<EMAIL>)
// 
// brief: 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_DISTINCT_FEATURE_ACCUMULATOR_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_DISTINCT_FEATURE_ACCUMULATOR_H

#include <thread>
#include "feature_accumulator_interface.h"
#include <item.h>
#include <sliding_window.h>

namespace anti {
namespace themis {
namespace feature_lib {

DECLARE_bool(need_set_distinct_data_view_signs);
DECLARE_int32(max_distinct_data_view_signs_num);

class DistinctFeatureAccumulator : public FeatureAccumulatorInterface {
public:
    DistinctFeatureAccumulator() : 
            _feature_id(0LU), 
            _version(0LU) {}

    virtual ~DistinctFeatureAccumulator() {
        uninit();
    }

    virtual uint64_t feature_id() const {
        return _feature_id;
    }
    virtual bool init(const comcfg::ConfigUnit& conf);
    virtual void uninit();

    virtual bool update(const FeatureValueProto& fea);
    virtual bool update_and_query(FeatureValueProto* fea);
    virtual bool query(FeatureValueProto* fea) const;

    virtual bool load(PosixIoReaderInterface *reader);
    virtual bool dump(PosixIoWriterInterface *writer) const;
    bool query(
            const FValQRequestProto& request, 
            FValQResponseProto* response) const; 

private:
    typedef anti::themis::common_lib::DistinctItem DistinctItem;
    typedef anti::themis::common_lib::SlidingWindow<DistinctItem> SlidingWindow;

    bool _check_feature(const FeatureValueProto& fea) const;
    bool _fillup_feature(
            const DistinctItem* item, 
            FeatureValueProto* fea) const;

    uint64_t _feature_id;
    uint64_t _version;
    SlidingWindow _window;
    mutable std::mutex _mutex;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_DISTINCT_FEATURE_ACCUMULATOR_H
