// Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved
// Author <PERSON> (<EMAIL>)
// 
// @brief 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_DATAVIEW_TIMEDIFF_FEATURE_ACCUMULATOR_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_DATAVIEW_TIMEDIFF_FEATURE_ACCUMULATOR_H

#include <memory>
#include <com_log.h>
#include "goe_list.hpp"
#include "utils.h"
#include "feature_accumulator_interface.h"
#include <unordered_map>

namespace anti {
namespace themis {
namespace feature_lib {

typedef std::unordered_map<uint64_t, uint64_t> DataViewMap;

class DataViewNode {
public:
    DataViewNode(uint64_t sign, int64_t ms,  uint64_t max_size, std::unordered_map<uint64_t, uint64_t> data_view) :
            _sign(sign), _time_second(ms / 1000) ,_max_dataview_size(max_size) {
                for (auto it = data_view.begin(); it!= data_view.end(); ++it) {
                    _data_view_map.insert(std::make_pair(it->first, it->second));
                }
            }
    DataViewNode(uint64_t sign, int64_t ms) :
            _sign(sign), _time_second(ms / 1000) {
                _max_dataview_size = 1000;
            } 
    DataViewNode() : DataViewNode(0, 0) {}  
    ~DataViewNode() { 
        _data_view_map.clear();
        };
    
    
    uint64_t sign() const { return _sign; }
    int64_t coord() const { return _time_second; }
    int64_t max_dataview_size() const { return _max_dataview_size; }
    DataViewMap data_view_map() const { return _data_view_map; }

    DataViewNode& operator+=(const DataViewNode& rhs) {
        if (rhs.coord() == coord())  {              
            for (auto& kv : rhs.data_view_map()) {
                if (_data_view_map.size() >= max_dataview_size()) {
                    CFATAL_LOG("DataViewNode overflow when add DataViewNode, [viewsign:%d,coor:%d,max_size:%d]", 
                    sign(), coord(), max_dataview_size());
                    break;
                }

                if (_data_view_map.find(kv.first) == _data_view_map.end()){
                    _data_view_map.insert(std::make_pair(kv.first, kv.second));
                    continue;
                } 

                if (_data_view_map[kv.first] > kv.second) {
                    _data_view_map[kv.first] = kv.second;
                }           
           } 
        }
        return *this;
    }

    template<typename Archive>
    bool serialize(Archive* ar) const {
        DUMP_VAR(_sign, ar, uint64_t);
        DUMP_VAR(_time_second, ar, int64_t);
        DUMP_VAR(_max_dataview_size, ar, int64_t);
        int64_t total_num = _data_view_map.size();
        DUMP_VAR(total_num, ar, int64_t);
        for (auto iter = _data_view_map.begin();
            iter != _data_view_map.end(); ++iter) {     
            DUMP_VAR(iter->first, ar, uint64_t);
            DUMP_VAR(iter->second, ar, uint64_t);
        }
        return true;
    }

    template<typename Archive>
    bool deserialize(Archive* ar) {
        LOAD_VAR(&_sign, ar, uint64_t);
        LOAD_VAR(&_time_second, ar, int64_t);
        LOAD_VAR(&_max_dataview_size, ar, int64_t);
        int64_t total_num = 0;
        LOAD_VAR(&total_num, ar, int64_t);      
        while (total_num != 0) {
            uint64_t first, second;
            LOAD_VAR(&first, ar, uint64_t);
            LOAD_VAR(&second, ar, uint64_t);
            _data_view_map.insert(std::make_pair(first, second));
            total_num--;
        }
        return true;
    }
    
private:
    uint64_t _sign;//主view_sign
    int64_t _time_second;//秒，作为节点坐标
    int64_t _max_dataview_size;//最大的数据视图大小
    DataViewMap _data_view_map;//存储节点内的子维度种类
};


class DataViewTDAccumulator : public FeatureAccumulatorInterface {
public:
    DataViewTDAccumulator();
    virtual ~DataViewTDAccumulator();

    virtual uint64_t feature_id() const { return _feature_id; }
    virtual bool init(const comcfg::ConfigUnit& conf);
    virtual void uninit();

    virtual bool update(const FeatureValueProto& fea);
    virtual bool query(FeatureValueProto* fea) const;

    virtual bool update_and_query(FeatureValueProto*);

    virtual bool load(PosixIoReaderInterface *reader);
    virtual bool dump(PosixIoWriterInterface *writer) const;

private:
    bool check_feature(const FeatureValueProto& fea) const;

private:
    typedef common_lib::GOEList<DataViewNode> GOEList;

    uint64_t _feature_id;
    uint64_t _version;
    int64_t _range;
    int64_t _max_dataview_size;
    std::shared_ptr<GOEList> _goe_list;
};

}
}
}

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_DATAVIEW_TIMEDIFF_FEATURE_ACCUMULATOR_H
