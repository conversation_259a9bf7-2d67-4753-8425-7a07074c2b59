// Copyright (c) 2025 Baidu.com, Inc. All Rights Reserved
// @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>(<EMAIL>)

#include <gflags/gflags.h>

namespace anti {
namespace themis {
namespace feature_lib {

DEFINE_bool(enable_fea_service_acc, true, "Flag for opening fvalue query or not");
DEFINE_bool(enable_mem_pool_for_acc, false, "Enable memory pool for feature accumulators");
DEFINE_bool(enable_feature_monitor, false, "Enable monitor for feature accumulators");
DEFINE_int32(monitor_period_minutes, 30, "Monitor period in minutes");

// DistinctFeatureAccumulator相关的GFLAGS (从distinct_feature_accumulator.cpp迁移过来)
DEFINE_bool(need_set_distinct_data_view_signs, false, "flag for setting distinct data view values");
DEFINE_int32(max_distinct_data_view_signs_num, 10000, "flag for setting distinct data view values");

// V2版本专用的GFLAGS
DEFINE_int32(pool_mempool_block_item_num, 20000, "Memory pool block item number for V2 accumulators");

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti
