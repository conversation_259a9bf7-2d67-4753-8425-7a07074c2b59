// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @brief 

#include <boost/lexical_cast.hpp>
#include "multi_segment_feature_accumulator.h"

namespace anti {
namespace themis {
namespace feature_lib {

MultiSegmentAccumulator::MultiSegmentAccumulator() :
        _feature_id(0U), _version(0U) {}

MultiSegmentAccumulator::~MultiSegmentAccumulator() { uninit(); }

bool MultiSegmentAccumulator::init(const comcfg::ConfigUnit& conf) {
    int64_t window_length = 0;
    try {
        _feature_id = conf["feature_id"].to_uint64();
        _version = conf["version"].to_uint64();
        window_length = conf["window_length"].to_int64();
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    }

    long long step_length = 0L;
    const int kDefaultStepNum = 4;
    if (conf["step_length"].get_int64(&step_length,
                window_length / kDefaultStepNum)) {
        CWARNING_LOG("feature_id(%lu) has no step_length, using default: %ld",
                feature_id(), step_length);
    }

    if (window_length <= 0L 
            || step_length <= 0L 
            || window_length % step_length != 0) {
        CFATAL_LOG("invalid window_length(%ld) step_length(%ld)", 
                window_length, step_length);
        return false;
    }

    if (!_segment.init(step_length, window_length)) {
        CWARNING_LOG("MultiSegment init failed. [step_len:%d, win_len:%d]",
                step_length, window_length);
        return false;
    }
    CWARNING_LOG("init MultiSegmentAccumulator(%lu) success", _feature_id);
    return true;
}

void MultiSegmentAccumulator::uninit() { _segment.uninit(); }

bool MultiSegmentAccumulator::update(const FeatureValueProto& fea) {
    if (!_check_feature(fea)) {
        CFATAL_LOG("call _check_feature fail");
        return false;
    }

    return _segment.enter(fea.view_sign(), fea.coord(), SegmentItem(1), NULL);
}

bool MultiSegmentAccumulator::update_and_query(FeatureValueProto* fea) {
    if (fea == NULL || !_check_feature(*fea)) {
        CFATAL_LOG("input fea == NULL or call _check_feature fail");
        return false;
    }

    const SegmentItem* acc = NULL; 
    if (!_segment.enter(fea->view_sign(), fea->coord(), SegmentItem(1L), &acc)) {
        CWARNING_LOG("enter multi-segment failed");
        return false;
    }
    _fillup_feature(acc, fea);
    return true;
}

bool MultiSegmentAccumulator::query(FeatureValueProto* fea) const {
    if (fea == NULL || !_check_feature(*fea)) {
        CFATAL_LOG("input fea == NULL or call _check_feature fail");
        return false;
    }
    
    const SegmentItem* acc = _segment.query(fea->view_sign(), fea->coord());
    _fillup_feature(acc, fea);
    return true;
}

bool MultiSegmentAccumulator::load(PosixIoReaderInterface *reader) {
    uint64_t ver = 0LU;
    if (reader->read(&ver, sizeof(ver)) != static_cast<int>(sizeof(ver))) {
        CWARNING_LOG("load version fail");
        return false;
    }

    // version > checkpoint version return true directly
    if (ver < _version) {
        CWARNING_LOG("old version, ignore this checkpoint");
        return true;
    }

    if (!_segment.deserialize(reader)) {
        CWARNING_LOG("segment deserialize failed. feature_id(%lu)",
                _feature_id);
        return false;
    }
    return true; 
}

bool MultiSegmentAccumulator::dump(PosixIoWriterInterface *writer) const {
    if (writer->write(&_version, sizeof(_version)) 
            != static_cast<int>(sizeof(_version))) {
        CWARNING_LOG("write version(%lu) fail, feature_id(%lu)", 
                _version, _feature_id);
        return false;
    }
    
    if (!_segment.serialize(writer)) {
        CWARNING_LOG("call window serialize fail, feature_id(%lu)", _feature_id);
        return false;
    }
    return true;
}

bool MultiSegmentAccumulator::_check_feature(
        const FeatureValueProto& fea) const {
    if (fea.feature_id() != _feature_id
            || fea.feature_type() != FeatureValueProto::MULTI_SEG) {
        CFATAL_LOG("feature_id(%lu) != _feature_id(%lu) or invalid feature_type(%d), logid(%lu)", 
                fea.feature_id(), _feature_id, fea.feature_type(), fea.log_id());
        return false;
    }
    return true;
}

void MultiSegmentAccumulator::_fillup_feature(const SegmentItem* node,
        FeatureValueProto* fea) const {
    if (node != NULL) {
        fea->set_cur_seg_count(node->cumulant());
    } else {
        fea->set_cur_seg_count(0);
    }
    fea->set_last_seg_count(0);
    fea->set_valid(true);
    fea->set_value(boost::lexical_cast<std::string>(fea->cur_seg_count()));
}

}
}
}
