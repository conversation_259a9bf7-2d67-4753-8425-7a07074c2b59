// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @brief 

#include <cmath>
#include "hijack_feature_accumulator.h"

namespace anti {
namespace themis {
namespace feature_lib {

const int32_t kNodeListMaxLength = 512;
const static int64_t g_MS_PER_SECOND = 1000;

//====================
// Non-Member functions
bool to_buf(const std::string& str, StrBuf* buf) {
    int ret = snprintf(buf->buf, kNodeBufLen, "%s", str.data());
    if (ret < 0 || ret >= kNodeBufLen) {
        CWARNING_LOG("snprintf str to buf failed");
        return false;
    }
    buf->buf[ret] = 0;
    return true;
}

bool to_str(const StrBuf& buf, std::string* str) {
    str->clear();
    str->assign(buf.buf);
    return true;
}

HjSecondNode make_hjnode(const FeatureValueProto& fea) {
    return HjSecondNode(fea.view_sign(), 
            fea.log_time(),
            fea.uniq_feat_sign(),
            fea.original_hijack_field().page_no(),
            fea.original_hijack_field().charge_name(),
            fea.original_hijack_field().flow_group());
}

bool in_range(const int64_t lhs, const int64_t rhs, const uint64_t range) {
    return std::abs(lhs - rhs) <= (int64_t)range;
}

FeatureValueProto::HijackProto make_hj_proto(const std::string& cn,
        const std::string& flow_group, uint32_t page_no, int32_t dist) {
    FeatureValueProto::HijackProto val;
    val.set_charge_name(cn);
    val.set_flow_group(flow_group);
    val.set_distance(dist);
    val.set_page_no(page_no);
    return val;
}

FeatureValueProto::HijackProto make_hj_proto(const HjSecondElement& pre,
        const HjSecondElement& cur) {
    int32_t distance = 
        cur.time_ms / g_MS_PER_SECOND - pre.time_ms / g_MS_PER_SECOND;
    // add a shit to distinguish the same second's left and right
    //     // left -> 0
    //         // right -> -1
    if (distance == 0 && cur.time_ms < pre.time_ms) {
        distance = -1;
    }
    return make_hj_proto(pre.cn, pre.flow_group, pre.page_no, distance);
}

void add_hj_proto(FeatureValueProto* fea,
        const FeatureValueProto::HijackProto&& hijack) {
    fea->add_fix_hijack_field()->CopyFrom(hijack);
}

//====================
// HijackAccumulator
HijackAccumulator::HijackAccumulator()
        : _feature_id(0u), _version(0u), _range(10) {}
HijackAccumulator::~HijackAccumulator() { uninit(); }

bool HijackAccumulator::init(const comcfg::ConfigUnit& conf) {
    int64_t window_length = 0;
    try {
        _feature_id = conf["feature_id"].to_uint64();
        _version = conf["version"].to_uint64();
        window_length = conf["window_length"].to_int64();
        _range = conf["range"].to_uint64();
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    }

    if (window_length < 0) {
        CFATAL_LOG("invalid window_length(%ld)", window_length);
        return false;
    }

    _goe_list.reset(new (std::nothrow) GOEList(window_length, kNodeListMaxLength));
    if (!_goe_list) {
        CFATAL_LOG("alloc goe_list failed");
        return false;
    }

    CWARNING_LOG("init HijackAccumulator(%lu) success", _feature_id);
    return true;
}
void HijackAccumulator::uninit() { _goe_list.reset(); }

bool HijackAccumulator::update(const FeatureValueProto& fea) {
    if (!check_feature(fea)) {
        CWARNING_LOG("invalid fea");
        return false;
    }

    auto node = make_hjnode(fea);
    auto ptr = _goe_list->insert(node);
    if (!ptr) {
        CWARNING_LOG("insert feat failed, [fea:%s]",
                fea.ShortDebugString().data());
        return false;
    }

    return true;
}

bool HijackAccumulator::query(FeatureValueProto* fea) const {
    if (!check_feature(*fea)) {
        CWARNING_LOG("invalid fea");
        return false;
    }
    
    auto list_ptr = _goe_list->query(fea->view_sign());
    if (!list_ptr) {
        // treat query failed case as normal feature
        CWARNING_LOG("query feat_list failed. [fea:%s]",
                fea->ShortDebugString().data());
    } else {
        auto node = make_hjnode(*fea);
        bool exist_hijack = false;
        for (auto item : *list_ptr) {
            // in descending order
            if (in_range(item.coord(), node.coord(), _range)) {
                if (item.end() != node.start()) {
                    add_hj_proto(fea, make_hj_proto(item.end(), node.start()));
                }

                if (item.is_single_point()) {
                    // the start and end is the same, no need to check
                    // the start 
                    continue; 
                }

                if (item.start() != node.start()) {
                    add_hj_proto(fea, make_hj_proto(item.start(), node.start()));
                } 
            }
            // the coord of item has been out of range!
            if (item.coord() + (int64_t)_range < node.coord()) {
                break;
            }
        }
    }

    fea->set_valid(true);

    return true;
}

bool HijackAccumulator::update_and_query(FeatureValueProto* fea) {
    if (!update(*fea)) {
        CWARNING_LOG("update feat failed");
        return false;
    }
    if (!query(fea)) {
        CWARNING_LOG("query feat failed");
        return true;
    }
    return true;
}

bool HijackAccumulator::load(PosixIoReaderInterface *reader) {
    uint64_t ver = 0LU;
    if (reader->read(&ver, sizeof(ver)) != static_cast<int>(sizeof(ver))) {
       CWARNING_LOG("load version fail");
       return false;
    }

    // version > checkpoint version return true directly
    if (ver < _version) {
        CWARNING_LOG("checkpoint version is low, skip this checkpoint.[%d -> %d]",
                _version, ver);
       return true;
    }
    _version = ver;

    if (!_goe_list->deserialize(reader)) {
        CWARNING_LOG("goe_list deserialize failed. [id:%lu]", _feature_id);
        return false;
    }
    return true;
}

bool HijackAccumulator::dump(PosixIoWriterInterface *writer) const {
    if (writer->write(&_version, sizeof(_version)) != static_cast<int>(sizeof(_version))) {
        CWARNING_LOG("write version(%lu) fail, feature_id(%lu)", _version, _feature_id);
        return false;
    }

    if (!_goe_list->serialize(writer)) {
        CWARNING_LOG("dump goe_list failed. [id:%lu]", _feature_id);
        return false;
    }
    return true;
}

bool HijackAccumulator::check_feature(const FeatureValueProto& fea) const {
    if (fea.feature_id() != _feature_id
                || fea.feature_type() != FeatureValueProto::HIJACK) {
        CFATAL_LOG("invalid feature. [fea:%s]", fea.ShortDebugString().data());
        return false;
    }
    return true;
}

}
}
}
