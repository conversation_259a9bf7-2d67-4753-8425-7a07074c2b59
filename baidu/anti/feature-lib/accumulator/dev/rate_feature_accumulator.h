// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: rate_feature_accumulator.h
// @Last modified: 2017-03-16 14:07:52
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_RATE_FEATURE_ACCUMULATOR_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_RATE_FEATURE_ACCUMULATOR_H

#include "feature_accumulator_interface.h"
#include <item.h>
#include <sliding_window.h>

namespace anti {
namespace themis {
namespace feature_lib {

// Calculate ctr, cpm and other rates which need two(or more) different logs
// Using two sliding windows, accumulate numerator and denominator separately
class RateFeatureAccumulator : public FeatureAccumulatorInterface {
public:
    RateFeatureAccumulator() :
            _feature_id(0LU), 
            _version(0LU),
            _time_threshold(0L) {}
    virtual ~RateFeatureAccumulator() {
        uninit();
    }

    virtual uint64_t feature_id() const {
        return _feature_id;
    }
    virtual bool init(const comcfg::ConfigUnit& conf);
    virtual void uninit() {
        for (uint32_t i = 0U; i < TOTAL; ++i) {
            _windows[i].uninit();
            _remain[i] = 0L;
        }
        _version = 0LU;
        _feature_id = 0LU;
        _time_threshold = 0L;
    }

    virtual bool update(const FeatureValueProto& fea);
    virtual bool update_and_query(FeatureValueProto* fea);
    virtual bool query(FeatureValueProto* fea) const;

    virtual bool load(PosixIoReaderInterface *reader);
    virtual bool dump(PosixIoWriterInterface *writer) const;

private:
    typedef anti::themis::common_lib::SegmentItem SegmentItem;
    typedef anti::themis::common_lib::SlidingWindow<SegmentItem> SlidingWindow;

    bool _check_feature(const FeatureValueProto& fea) const;
    bool _update(const FeatureValueProto& fea);

    // query nu & de, calculate rate and set fea
    bool _calculate_feature(FeatureValueProto* fea) const;

    uint64_t _feature_id;
    uint64_t _version;

    enum Rate {
        NUMERATOR = 0,
        DENOMINATOR = 1,
        TOTAL = 2
    };
    SlidingWindow _windows[TOTAL];
    int64_t _remain[TOTAL];
    int64_t _time_threshold;
};

}  // feature_lib
}  // themis
}  // anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_RATE_FEATURE_ACCUMULATOR_H

/* vim: set ts=4 sw=4: */

