// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangji<PERSON><PERSON>(<EMAIL>)
// 
// @File: rate_distribution_feature_accumulator.cpp
// @Last modified: 2017-03-16 12:08:40
// @Brief: 

#include "rate_distribution_feature_accumulator.h"
#include <boost/algorithm/string.hpp>
#include <boost/lexical_cast.hpp>

namespace anti {
namespace themis {
namespace feature_lib {

bool RateDistributionFeatureAccumulator::init(const comcfg::ConfigUnit& conf) {
    int64_t window_length = 0L;
    try {
        _feature_id = conf["feature_id"].to_uint64();
        _version = conf["version"].to_uint64();
        window_length = conf["window_length"].to_int64();
        _time_threshold = conf["time_threshold"].to_int64();
        std::string remain(conf["remain"].to_cstr());
        if (sscanf(remain.data(), "%ld,%ld", &_remain[NUM], &_remain[DEN]) != TOTAL) {
            CWARNING_LOG("invalid remain %s", remain.data());
            return false;
        }
        std::string points(conf["interval_endpoints"].to_cstr());
        if (!_init_intervals(points)) {
            CWARNING_LOG("call _init_intervals(%s) fail", points.data());
            return false;
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std exception : %s", e.what());
        return false;
    }

    if (!_swindows[NUM].init(window_length, 2, window_length)
            || !_swindows[DEN].init(window_length, 2, window_length)
            || !_dwindow.init(window_length, 2, window_length)) {
        CWARNING_LOG("call init window fail, window_length(%ld)", window_length);
        return false;
    }
    CWARNING_LOG("init RateDistributionFeatureAccumulator(%lu) success", _feature_id);
    return true;
}

bool RateDistributionFeatureAccumulator::_init_intervals(const std::string& points) {
    std::vector<std::string> point_strs;
    boost::algorithm::split(point_strs, points, boost::is_any_of(", "), boost::token_compress_on);
    double start = 0;
    for (size_t i = 0U; i < point_strs.size(); ++i) {
        try {
            int64_t end = boost::lexical_cast<double>(point_strs[i]);
            _intervals.emplace_back(start, end);
            start = end;
        } catch (const boost::bad_lexical_cast& e) {
            CWARNING_LOG("boost::Exception : %s", e.what());
            return false;
        }
    }
    _intervals.emplace_back(start, DBL_MAX);
    return true;
}

bool RateDistributionFeatureAccumulator::update(const FeatureValueProto& fea) {
    if (!_check_feature(fea)) {
        CWARNING_LOG("call _check_feature fail, fea_id(%lu)", _feature_id);
        return false;
    }
    int64_t last = _get_bucket_idx(_query_segment(fea));
    int64_t cur = _get_bucket_idx(_update_segment(fea));
    _update_distribution(last, cur, fea);
    return true;
};

int64_t RateDistributionFeatureAccumulator::_get_bucket_idx(double rate) const {
    int64_t bucket_idx = -1;
    if (rate < 0) {
        return bucket_idx;
    }
    for (uint32_t i = 0; i < _intervals.size(); ++i) {
        if (_intervals[i].first < rate && 
                (rate < _intervals[i].second 
                        || fabs(_intervals[i].second - rate) <= FLT_EPSILON)) {
            bucket_idx = i;
            break;
        }
    }
    return bucket_idx;
}

const RateDistributionFeatureAccumulator::DistributionItem*
        RateDistributionFeatureAccumulator::_update_distribution(
        int64_t last_idx,
        int64_t cur_idx,
        const FeatureValueProto& fea) {
    DistributionItem inc;
    if (cur_idx < 0 || last_idx == cur_idx) {
        // do nothing
        ;
    } else if (last_idx < 0) {
        inc[cur_idx] += 1;
    } else {
        inc[cur_idx] += 1;
        inc[last_idx] -= 1;
    }
    const DistributionItem* ret = NULL;
    if (!_dwindow.enter(fea.view_sign(), fea.coord(), inc, &ret)) {
        CWARNING_LOG("call fea_id(%lu) dwindow enter failed", _feature_id);
        return NULL;
    }
    return ret;
}

bool RateDistributionFeatureAccumulator::update_and_query(FeatureValueProto* fea) {
    if (fea == NULL || !_check_feature(*fea)) {
        CWARNING_LOG("call _check_feature fail, fea_id(%lu)", _feature_id);
        return false;
    }
    int64_t last = _get_bucket_idx(_query_segment(*fea));
    int64_t cur = _get_bucket_idx(_update_segment(*fea));
    // CDEBUG_LOG("last(%ld), cur(%ld)", last, cur);
    const DistributionItem* di = _update_distribution(last, cur, *fea);
    _set_feature(cur, di, fea);
    return true;
}

void RateDistributionFeatureAccumulator::_set_feature(
        int64_t cur_idx,
        const DistributionItem* di,
        FeatureValueProto* fea) const {
    if (cur_idx < 0 || di == NULL) {
        // invalid cur idx or query fail
        return;
    }
    fea->set_bucket_idx(cur_idx);
    for (uint32_t i = 0; i < _intervals.size(); ++i) {
        FeatureValueProto::BucketProto* bucket = fea->add_buckets();
        bucket->set_idx(i);
        bucket->set_count((*di)[i]);
    }
    return;
}

bool RateDistributionFeatureAccumulator::query(FeatureValueProto* fea) const {
    if (fea == NULL || !_check_feature(*fea)) {
        CWARNING_LOG("call _check_feature fail, fea_id(%lu)", _feature_id);
        return false;
    }
    int64_t cur = _get_bucket_idx(_query_segment(*fea));
    const DistributionItem* di = _dwindow.query_segment(fea->view_sign());
    _set_feature(cur, di, fea);
    return true;
}

double RateDistributionFeatureAccumulator::_query_segment(const FeatureValueProto& fea) const {
    const SegmentItem* nu = _swindows[NUM].query_segment(fea.data_view_sign());
    const SegmentItem* de = _swindows[DEN].query_segment(fea.data_view_sign());
    if (llabs(_swindows[NUM].latest_coord() - _swindows[DEN].latest_coord()) > _time_threshold
            || nu == NULL || de == NULL
            || (nu->cumulant() < _remain[NUM] && de->cumulant() < _remain[DEN])) {
        return -1;
    }

    if (de->cumulant() == 0) {
        return DBL_MAX;
    }
    return 1.0 * nu->cumulant() / de->cumulant();
}

double RateDistributionFeatureAccumulator::_update_segment(const FeatureValueProto& fea) {
    if (fea.in_filter()) {
        int64_t filter = 
                fea.feature_type() != FeatureValueProto::CPM_DISTRIBUTION ? 
                1L : fea.filter_count();
        _swindows[NUM].enter(fea.data_view_sign(), fea.coord(), SegmentItem(filter));
    }

    if (fea.in_refer()) {
        _swindows[DEN].enter(fea.data_view_sign(), fea.coord(), SegmentItem(1L));
    }
    return _query_segment(fea);
}

bool RateDistributionFeatureAccumulator::_check_feature(const FeatureValueProto& fea) const {
    if (fea.feature_id() != _feature_id
            || (fea.feature_type() != FeatureValueProto::RATE_DISTRIBUTION
            && fea.feature_type() != FeatureValueProto::CPM_DISTRIBUTION)) {
        CFATAL_LOG("feature_id(%lu) != _feature_id(%lu) || invalid feature_type(%d), logid(%lu)", 
                fea.feature_id(), _feature_id, fea.feature_type(), fea.log_id());
        return false;
    }

    if (!fea.in_filter() && !fea.in_refer()) {
        CWARNING_LOG("neither in filter nor in refer, feature_id(%lu), logid(%lu)",
                _feature_id, fea.log_id());
        return false;
    }

    if (fea.in_filter() 
            && fea.feature_type() == FeatureValueProto::CPM_DISTRIBUTION
            && !fea.has_filter_count()) {
        CWARNING_LOG("feature(%lu) has no filter count, logid(%lu)", _feature_id, fea.log_id());
        return false;
    }

    if (!fea.has_data_view_sign()) {
        CWARNING_LOG("feature(%lu) has no data view sign, logid(%lu)", _feature_id, fea.log_id());
        return false;
    }
    return true;
}

bool RateDistributionFeatureAccumulator::load(PosixIoReaderInterface *reader) {
    uint64_t ver = 0LU;
    if (reader->read(&ver, sizeof(ver)) 
            != static_cast<int>(sizeof(ver))) {
        CWARNING_LOG("load version fail");
        return false;
    }

    // version > checkpoint version return true directly
    if (ver < _version) {
        return true;
    }

    for (uint32_t i = 0; i < TOTAL; ++i) {
        if (!_swindows[i].deserialize(reader)) {
            CWARNING_LOG("call swindows[%u] deserialize fail, feature_id(%lu)", 
                    i, _feature_id);
            return false;
        }
    }
    if (!_dwindow.deserialize(reader)) {
        CWARNING_LOG("call dwindows deserialize fail, feature_id(%lu)", _feature_id);
        return false;
    }
    return true;
}

bool RateDistributionFeatureAccumulator::dump(PosixIoWriterInterface *writer) const {
    if (writer->write(&_version, sizeof(_version)) 
            != static_cast<int>(sizeof(_version))) {
        CWARNING_LOG("write version(%lu) fail, feature_id(%lu)", 
                _version, _feature_id);
        return false;
    }
    for (uint32_t i = 0; i < TOTAL; ++i) {
        if (!_swindows[i].serialize(writer)) {
            CWARNING_LOG("call swindow[%u] serialize fail, feature_id(%lu)", 
                    i, _feature_id);
            return false;
        }
    }
    if (!_dwindow.serialize(writer)) {
        CWARNING_LOG("call dwindow serialize fail, feature_id(%lu)", _feature_id);
        return false;
    }
    return true; 
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

