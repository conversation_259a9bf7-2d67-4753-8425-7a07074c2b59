// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: fea_value_rate_accumulator.cpp
// @Last modified: 2017-10-31 17:18:27
// @Brief: 

#include "fea_value_rate_accumulator.h"
#include <boost/lexical_cast.hpp>

namespace anti {
namespace themis {
namespace feature_lib {

bool FeatureValueRateAccumulator::_init(const comcfg::ConfigUnit& conf) {
    try {
        _time_threshold = conf["time_threshold"].to_int64();
        if (conf["extend"].size() != TOTAL) {
            CWARNING_LOG("invalid extend config size(%d), should be (%d), feature_id(%lu)", 
                    conf["extend"].size(), TOTAL, feature_id());
            return false;
        }
        _ext_conf[NUMERATOR] = ExtConf(
                conf["extend"][NUMERATOR]["feature_id"].to_uint64(), 
                conf["extend"][NUMERATOR]["remain"].to_int64());
        _ext_conf[DENOMINATOR] = ExtConf(
                conf["extend"][DENOMINATOR]["feature_id"].to_uint64(), 
                conf["extend"][DENOMINATOR]["remain"].to_int64());
    } catch (const comcfg::ConfigException& e) { 
        CWARNING_LOG("ConfigException : %s", e.what()); 
        return false; 
    } catch (const std::exception& e) {
        CWARNING_LOG("catch exception : %s", e.what());
        return false;
    }  
    return true;
}

bool FeatureValueRateAccumulator::update_and_query(FeatureValueProto* fea) {
    if (!fea) {
        CWARNING_LOG("input fea is NULL, feature_id(%lu)", feature_id());
        return false;
    }
    
    if (!update(*fea)) {
        CDEBUG_LOG("update fea fail, feature_id(%lu)", feature_id());
    }

    if (!query(fea)) {
        CDEBUG_LOG("query fea fail, feature_id(%lu)", feature_id());
    }
    return true;
}

bool FeatureValueRateAccumulator::update(const FeatureValueProto& fea) {
    if (!_check_fea(fea)) {
        CWARNING_LOG("check fea fail, feature_id(%lu)", feature_id());
        return false;
    }
    auto node = _enter_window(fea.view_sign(), fea.coord());
    if (!node) {
        CWARNING_LOG("enter node to window fail, view_sign(%lu), feature_id(%lu)",
                fea.view_sign(), feature_id());
        return false;
    }
    int exp = _get_expiration(fea.view_sign());
    if (time(NULL) - node->latest_extend() < exp
            || _requests.find(fea.view_sign()) != _requests.end()) {
        return true;
    }
    _triger_query(fea);
    return true;
}

bool FeatureValueRateAccumulator::query(FeatureValueProto* fea) const {
    if (!fea || !_check_fea(*fea)) {
        CWARNING_LOG("input fea invalid, feature_id(%lu)", feature_id());
        return false;
    }
    
    if (!_window) {
        CFATAL_LOG("window ptr is NULL, call init first, feature_id(%lu)", feature_id());
        return false;
    }
    auto node = _window->find(fea->view_sign());
    if (!node || !node->value()) {
        CDEBUG_LOG("query for view_sing(%lu) fail, feature_id(%lu)",fea->view_sign(), feature_id());
        return false;
    }
    auto rate_item = node->value();
    fea->set_filter_count((*rate_item)[NUMERATOR].first);
    fea->set_refer_count((*rate_item)[DENOMINATOR].first);
    // vaild = (nu or de >= remain) && (coord diff <= time_threshold)
    bool valid = (*rate_item)[NUMERATOR].first >= _ext_conf[NUMERATOR].remain 
            || (*rate_item)[DENOMINATOR].first >= _ext_conf[DENOMINATOR].remain;
    fea->set_valid(valid
            && llabs((*rate_item)[NUMERATOR].second - (*rate_item)[DENOMINATOR].second) <= _time_threshold);
    double rate = (*rate_item)[DENOMINATOR].first != 0 ? 
            1.0 * (*rate_item)[NUMERATOR].first / (*rate_item)[DENOMINATOR].first : 1;
    fea->set_value(std::to_string(rate));
    CDEBUG_LOG("query fea succ, fea[%s]", fea->ShortDebugString().data());
    return true;
}

std::shared_ptr<GroupRateItem> FeatureValueRateAccumulator::_enter_window(
        uint64_t view_sign, 
        uint64_t coord) {
    std::shared_ptr<GroupRateItem> node(new (std::nothrow) GroupRateItem(view_sign, coord));
    if (!node) {
        CFATAL_LOG("new node obj fail, feature_id(%lu)", feature_id());
        return NULL;
    }
    auto old_node = _window->find(view_sign);
    if (old_node) {
        node->set_value(old_node->value());
        node->set_latest_extend(old_node->latest_extend());
    }
    _window->insert(node);
    return node;
}

void FeatureValueRateAccumulator::_triger_query(const FeatureValueProto& fea) {
    RateQuery req(new (std::nothrow)FValQueryTransProto);
    req->CopyFrom(fea.fea_serv_trans());
    _requests[fea.view_sign()] = req; 
    _rpc_mgr->query(req);
    return;
}

void FeatureValueRateAccumulator::sync(
        std::vector<std::shared_ptr<FeatureValueProto>>* /*feas*/) {
    for (auto& iter : _requests) {
        if (!response_check(iter.second->infos(0)) 
                || !response_check(iter.second->infos(1))) {
            CDEBUG_LOG("invalid response[%s]", iter.second->ShortDebugString().data());
            continue;
        }
        auto node = _window->find(iter.first);
        if (!node) {
            CDEBUG_LOG("find node of view_sign(%lu) fail, feature_id(%lu)", iter.first, feature_id());
            continue;
        }
        auto rate = _caculate_rate(iter.second);
        if (rate) {
            node->set_value(rate);
        }
        node->set_latest_extend(time(NULL));
    }
    _requests.clear();
    return;
}

bool FeatureValueRateAccumulator::_check_fea(const FeatureValueProto& fea) const {
    if (fea.feature_id() != feature_id()
            || fea.feature_type() != FeatureValueProto::FEATURE_VALUE_EXTEND
            || !fea.has_fea_serv_trans()
            || fea.fea_serv_trans().infos_size() != TOTAL) {
        CWARNING_LOG("input fea invalid, feature_id(%lu)", feature_id());
        return false;
    }
    return true;
}

std::shared_ptr<RateItem> FeatureValueRateAccumulator::_caculate_rate(const RateQuery& req) const {
    std::shared_ptr<RateItem> rate(new (std::nothrow)RateItem);
    for (int32_t i = 0; i < TOTAL; ++i) {
        auto res = fetch_response(req, _ext_conf[i].ext_id);
        try {
            (*rate)[i] = RateItem::Item(
                    boost::lexical_cast<int64_t>(res->value(0)), 
                    res->coord());
        } catch (const boost::bad_lexical_cast& e) {
            CWARNING_LOG("boost::Exception : %s", e.what());
            return NULL;
        }
    }
    CDEBUG_LOG("caculate rate succ, req[%s]", req->ShortDebugString().data());
    return rate;
}

} // namespace feature_lib
} // namespace themis
} // namespace anti
/* vim: set ts=4 sw=4: */

