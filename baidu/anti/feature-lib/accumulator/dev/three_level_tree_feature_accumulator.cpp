// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: ch<PERSON><PERSON><PERSON><PERSON>(ch<PERSON><PERSON><PERSON><PERSON>@baidu.com)
// 
// @File: three_level_tree_feature_accumulator.cpp
// @Last modified: 2018-06-01 11:39:08
// @Brief: 

#include <boost/lexical_cast.hpp>
#include <boost/algorithm/string.hpp>
#include "three_level_tree_feature_accumulator.h"

namespace anti {
namespace themis {
namespace feature_lib {

bool ThreeLevelTreeFeatureAccumulator::init(const comcfg::ConfigUnit& conf) {
    int64_t window_length = 0L;
    try {
        _feature_id = conf["feature_id"].to_uint64();
        _version = conf["version"].to_uint64();
        window_length = conf["window_length"].to_int64();
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std::Exception : %s", e.what());
        return false;
    }
    if (window_length <= 0L) {
        CWARNING_LOG("invalid window_length(%ld)", window_length);
        return false;
    }
    const int DEFAULT_STEP_NUM = 4;
    long long step_length = 0L;
    if (conf["step_length"].get_int64(&step_length, window_length / DEFAULT_STEP_NUM) != 0) {
        CWARNING_LOG("feature_id(%lu) no step_length, using %ld", 
                _feature_id, window_length / DEFAULT_STEP_NUM);
    }
    if (window_length <= 0L 
            || step_length <= 0L 
            || window_length % step_length != 0) {
        CFATAL_LOG("invalid window_length(%ld) step_length(%ld)", 
                window_length, step_length);
        return false;
    }
    const int CACHE_STEP_NUM = 1;
    int max_step_num = CACHE_STEP_NUM + window_length / step_length;
    _window.reset(new (std::nothrow) ThreeLevelTreeWindow());
    if (!_window || !_window->init(step_length, max_step_num, window_length)) {
        CWARNING_LOG("new or init ThreeLevelTreeWindow failed!");
        return false;
    }
    std::string statistic_level = "data_view";
    if (conf["statistic_level"].selfType() != comcfg::CONFIG_ERROR_TYPE) {
        statistic_level = conf["statistic_level"].to_cstr();
    }
    if (statistic_level == "data_view") {
        _tree_cond.statistic_level = anti::themis::common_lib::TreeItemStatisticCond::ROOT;
    } else if (statistic_level == "cumulate_view") {
        _tree_cond.statistic_level = anti::themis::common_lib::TreeItemStatisticCond::DISTINCT_LEAF;
    } else {
        CFATAL_LOG("invalid statistic_level[%s]", statistic_level.c_str());
        return false;
    }
    std::string cumulate_view_cond = "";
    if (conf["cumulate_view_cond"].selfType() != comcfg::CONFIG_ERROR_TYPE) {
        cumulate_view_cond = conf["cumulate_view_cond"].to_cstr();
    }
    _tree_cond.leaf_condition = cumulate_view_cond;

    CWARNING_LOG("init ThreeLevelTreeFeatureAccumulator(%lu) success", _feature_id);
    return true;
}

void ThreeLevelTreeFeatureAccumulator::uninit() {
    _window->uninit();
    _feature_id = 0LU;
    _version = 0LU;
}

bool ThreeLevelTreeFeatureAccumulator::update_and_query(FeatureValueProto* fea) {
    if (fea == NULL) {
        CFATAL_LOG("input fea == NULL, feature_id(%lu)", _feature_id);
        return false;
    }
    const ThreeLevelTreeItem* res = NULL;
    if (!_update(*fea, &res)) {
        CWARNING_LOG("call _update fail, feature_id(%lu), cood(%ld), logid(%lu)",
                _feature_id, fea->coord(), fea->log_id());
        return false;
    }
    return _fillup_feature(res, fea);
}

bool ThreeLevelTreeFeatureAccumulator::update(const FeatureValueProto& fea) {
    return _update(fea, NULL);
}

bool ThreeLevelTreeFeatureAccumulator::query(FeatureValueProto* fea) const {
    if (fea == NULL || !_check_feature(*fea)) {
        CFATAL_LOG("input fea == NULL or call _check_feature fail, feature_id(%lu)", _feature_id);
        return false;
    }
    const ThreeLevelTreeItem* res = _window->query_segment(
            fea->view_sign(), fea->coord());
    if (res == NULL) {
        CTRACE_LOG("call ThreeLevelTreeWindow query fail feature_id(%lu) logid(%lu)",
                fea->feature_id(), fea->log_id());
        return false;
    }
    return _fillup_feature(res, fea);
}

bool ThreeLevelTreeFeatureAccumulator::_update(
        const FeatureValueProto& fea, const ThreeLevelTreeItem** res_ptr) {
    if (!_check_feature(fea)) {
        CFATAL_LOG("call _check_feature fail");
        return false;
    }
    ThreeLevelTreeItem item(
        static_cast<uint64_t>(fea.data_view_sign()),
        static_cast<uint64_t>(fea.cumulate_view_sign()),
        _tree_cond); 
    _window->enter(fea.view_sign(), fea.coord(), item, res_ptr);
    return true; 
}

bool ThreeLevelTreeFeatureAccumulator::_check_feature(const FeatureValueProto& fea) const {
    if (fea.feature_id() != _feature_id
            || fea.feature_type() != FeatureValueProto::THREE_LEVEL_TREE) {
        CFATAL_LOG("feature_id(%lu) != _feature_id(%lu) or invalid feature_type(%d), logid(%lu)",
                fea.feature_id(), _feature_id, fea.feature_type(), fea.log_id());
        return false;
    }
    return true;
}

bool ThreeLevelTreeFeatureAccumulator::_fillup_feature(
        const ThreeLevelTreeItem* item, FeatureValueProto* fea) const {
    // fill
    int64_t value = item != NULL ? item->condition_value() : 0;
    fea->set_valid(true);
    fea->set_value(std::to_string(value));
    return true;
}

bool ThreeLevelTreeFeatureAccumulator::load(PosixIoReaderInterface *reader) {
    uint64_t ver = 0LU;
    if (reader->read(&ver, sizeof(ver)) != static_cast<int>(sizeof(ver))) {
        CWARNING_LOG("load version fail");
        return false;
    }

    if (ver < _version) {
        return true;
    }

    if (!_window->deserialize(reader)) {
        CWARNING_LOG("call _window deserialize fail, feature_id(%lu)", _feature_id);
        return false;
    }
    return true;
}

bool ThreeLevelTreeFeatureAccumulator::dump(PosixIoWriterInterface *writer) const {
    if (writer->write(&_version, sizeof(_version)) != static_cast<int>(sizeof(_version))) {
        CWARNING_LOG("write version(%lu) fail, feature_id(%lu)", _version, _feature_id);
        return false;
    }

    if (!_window->serialize(writer)) {
        CWARNING_LOG("call _window serialize fail, feature_id(%lu)", _feature_id);
        return false;
    }
    return true;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

