// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
//
// @File: distribution_feature_accumulator.h
// @Last modified: 2018-01-17 21:04:37
// @Brief:

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_DISTRIBUTION_FEATURE_ACCUMULATOR_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_DISTRIBUTION_FEATURE_ACCUMULATOR_H

#include "feature_accumulator_interface.h"
#include "weak_anomaly_accumulator_interface.h"
#include "distribution_distance.h"
#include <item.h>
#include <pool_sliding_window.h>
#include <sliding_window.h>
#include <click_segment.hpp>

namespace anti {
namespace themis {
namespace feature_lib {

class DistributionFeatureAccumulator : public WeakAnomalyAccumulatorInterface {
public:
    DistributionFeatureAccumulator() :
        _feature_id(0L),
        _version(0L),
        _bucket_num(0),
        _click_mode(false),
        _simple_distribution(false) {}
    virtual ~DistributionFeatureAccumulator() {
        uninit();
    }

    virtual uint64_t feature_id() const {
        return _feature_id;
    }

private:
    virtual void print_monitor_log() const override;

    virtual bool _init(const comcfg::ConfigUnit& conf);
    virtual void _uninit();

    virtual bool _update(const FeatureValueProto& fea);
    virtual bool _update_and_query(FeatureValueProto* fea);
    virtual bool _query(FeatureValueProto* fea) const;

    virtual bool _load(PosixIoReaderInterface *reader);
    virtual bool _dump(PosixIoWriterInterface *writer) const;

    const static int MAX_BUCKET_NUM = 10;
    using DistributionItem = anti::themis::common_lib::ArrayItem<int64_t, MAX_BUCKET_NUM>;
    using StdSlidingWindow = anti::themis::common_lib::SlidingWindow<DistributionItem>;
    using PoolSlidingWindow = anti::themis::common_lib::PoolSlidingWindow<DistributionItem>;
    using DistributionLimitNode = anti::themis::common_lib::DistributionLimitNode;
    using ClickSegment = anti::themis::common_lib::ClickSegment<DistributionLimitNode>;

    bool _check_feature(const FeatureValueProto& fea) const;

    bool _set_feature(
            const DistributionItem* item,
            FeatureValueProto* fea) const;

    bool _set_feature(
            const DistributionLimitNode* node,
            FeatureValueProto* fea) const;

    uint64_t _feature_id;
    uint64_t _version;
    int _bucket_num;
    StdSlidingWindow _std_window;
    PoolSlidingWindow _pool_window;
    ClickSegment _seg;
    bool _click_mode;
    bool _use_mem_pool;
    std::shared_ptr<DistributionDistance> _distribution_distance;
    bool _simple_distribution;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_DISTRIBUTION_FEATURE_ACCUMULATOR_H
