// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// <AUTHOR>
// @brief

#ifndef ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_FEA_DEVIATION_FEATURE_ACCUMULATOR_H
#define ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_FEA_DEVIATION_FEATURE_ACCUMULATOR_H

#include <deque>
#include <numeric>
#include <unordered_map>
#include <boost/circular_buffer.hpp>
#include "feature_accumulator_interface.h"

namespace anti {
namespace themis {
namespace feature_lib {

class FeaDeviationFeatureAccumulator : public FeatureAccumulatorInterface {
public:
    FeaDeviationFeatureAccumulator() : _feature_id(0) {}
    virtual ~FeaDeviationFeatureAccumulator() {
        uninit();
    }

    virtual uint64_t feature_id() const {
        return _feature_id;
    }

    virtual bool init(const comcfg::ConfigUnit& conf);
    virtual void uninit() {}

    virtual bool update(const FeatureValueProto& fea);
    virtual bool update_and_query(FeatureValueProto* fea) { return false; }
    virtual bool query(FeatureValueProto* fea) const;

    virtual bool load(PosixIoReaderInterface *reader) { return true; }
    virtual bool dump(PosixIoWriterInterface *writer) const;

private:
    struct Item {
        int64_t value;
        uint64_t count;
        uint64_t pre_sum;
        Item(int64_t v, uint64_t c, uint64_t t) :
                value(v), count(c), pre_sum(t) {}
        Item() : Item(0, 0, 0) {}
        inline uint64_t range_sum() { return count + pre_sum; }
    };
    typedef std::shared_ptr<Item> ItemPtr;
    typedef std::deque<ItemPtr> ItemPtrDeque;
    typedef std::deque<ItemPtr>::iterator ItemPtrDequeIt;
    typedef boost::circular_buffer<int64_t> SmoothBuffer;
    typedef std::shared_ptr<SmoothBuffer> SmoothBufferPtr;

    void _store_smooth_fea(const ItemPtr& cur_fea);

    int64_t _get_total_sum(const ItemPtrDeque& sorted_feas) const;
    int64_t _get_range_sum(const ItemPtrDeque& sorted_feas, int64_t value) const;
    int64_t _get_inflect_range_sum(const ItemPtrDeque& sorted_feas) const;

    // @brief : return false means not update anything this round
    //          return true means update max_point
    bool _update_max_point(const ItemPtrDeque& sorted_feas);

    // @brief : add inflect point if needed
    void _update_inflect_point(const ItemPtrDeque& sorted_feas);

    // @brief : push a value, get smooth value if need
    //          return true means smooth buffer can produce value now
    //          return false means not enough value in this buffer
    bool _push_and_get(int64_t original_value, int64_t* smooth_value);

    bool _is_inflect(const ItemPtr& after, const ItemPtr& check,
            const ItemPtr& before);

    bool _dump(std::string* data) const;

    bool _get_variance(const ItemPtrDeque& _sorted_feas, double * const variance) const;

    enum Position {
        TAILENDER = 1,          // last one
        PENULTIMATE = 2,        // last but one 
        ANTEPENULTIMATE = 3     // last but two
    };

    const ItemPtr& _get_ptr(const ItemPtrDeque& sorted_feas, Position pos) {
        return sorted_feas[sorted_feas.size() - pos];
    }

    uint64_t _feature_id;

    SmoothBufferPtr _smooth_buffer;
    ItemPtrDeque _smooth_feas;
    ItemPtrDeque _original_feas;
    std::unordered_map<int64_t, ItemPtrDequeIt> _fea_map;

    ItemPtr _cur_fea;
    ItemPtr _max_point;
    ItemPtr _inflect_point;
};

}
}
}
#endif // ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_FEA_DEVIATION_FEATURE_ACCUMULATOR_H
