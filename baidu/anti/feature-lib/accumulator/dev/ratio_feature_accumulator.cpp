// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiax<PERSON>(<EMAIL>)
//
// @File: ratio_feature_accumulator.cpp
// @Last modified: 2017-12-26 16:28:06
// @Brief:

#include "ratio_feature_accumulator.h"
#include <gflags/gflags.h>
#include "boost/lexical_cast.hpp"

namespace anti {
namespace themis {
namespace feature_lib {

DECLARE_bool(enable_mem_pool_for_acc);

bool RatioFeatureAccumulator::_init(const comcfg::ConfigUnit& conf) {
    int64_t window_length = 0L;
    try {
        _feature_id = conf["feature_id"].to_uint64();
        _version = conf["version"].to_uint64();
        _remain = conf["remain"].to_int64();
        window_length = conf["window_length"].to_int64();
        const uint32_t DEFAULT_REMAIN_DENOMINATOR = 0U;
        uint32_t remain_numerator = DEFAULT_REMAIN_DENOMINATOR;
        conf["remain_numerator"].get_uint32(&remain_numerator, DEFAULT_REMAIN_DENOMINATOR);
        _remain_numerator = remain_numerator != DEFAULT_REMAIN_DENOMINATOR;
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    }

    const int32_t DEFAULT_CHECK_MODE = 0;
    int32_t check_mode = 0;
    if (conf["weak_check"].get_int32(&check_mode, DEFAULT_CHECK_MODE)) {
        CWARNING_LOG("no weak check");
    }
    if (check_mode != 0 && check_mode != 1) {
        CWARNING_LOG("illegal weak check[weak_check=%d], "
                "should be 0(close) or 1(open)", check_mode);
        return false;
    }
    _weak_check = (check_mode == 0 ? false : true);

    const int DEFAULT_MODE = 0;
    int mode = 0;
    if (conf["click_mode"].get_int32(&mode, DEFAULT_MODE)) {
        CWARNING_LOG("feature_id(%lu) no click_mode, using %d",
                _feature_id, DEFAULT_MODE);
    }
    _click_mode = mode != 0 ? true : false;

    const int DEFAULT_STEP_NUM = 4;
    long long step_length = 0L;
    if (conf["step_length"].get_int64(&step_length, window_length / DEFAULT_STEP_NUM) != 0) {
        CWARNING_LOG("feature_id(%lu) no step_length, using %ld", 
                _feature_id, window_length / DEFAULT_STEP_NUM);
    }

    if (window_length <= 0L 
            || step_length <= 0L 
            || window_length % step_length != 0) {
        CFATAL_LOG("invalid window_length(%ld) step_length(%ld)", 
                window_length, step_length);
        return false;
    }

    // 决定是否使用内存池
    _use_mem_pool = FLAGS_enable_mem_pool_for_acc;

    if (_click_mode) {
        if (!_seg.init(window_length)) {
            CWARNING_LOG("call click segment init fail, seglen=%ld", window_length);
            return false;
        }
    } else {
        const int CACHE_STEP_NUM = 1;
        int max_step_num = CACHE_STEP_NUM + window_length / step_length;

        if (_use_mem_pool) {
            if (!_pool_window.init(step_length, max_step_num, window_length)) {
                CWARNING_LOG("call pool window init(%ld, %ld, %ld) fail", step_length, max_step_num, window_length);
                return false;
            }
        } else {
            if (!_std_window.init(step_length, max_step_num, window_length)) {
                CWARNING_LOG("call window init(%ld, %ld, %ld) fail", step_length, max_step_num, window_length);
                return false;
            }
        }
    }

    CWARNING_LOG("init RatioFeatureAccumulator(%lu) success", _feature_id);
    return true;
}

void RatioFeatureAccumulator::_uninit() {
    if (!_click_mode) {
        if (_use_mem_pool) {
            _pool_window.uninit();
        } else {
            _std_window.uninit();
        }
    } else {
        _seg.uninit();
    }
    _feature_id = 0LU;
    _version = 0LU;
    _click_mode = false;
    _remain_numerator = false;
    _remain = 0L;
    _use_mem_pool = false;
}

bool RatioFeatureAccumulator::_update(const FeatureValueProto& fea) {
    if (!_check_feature(fea)) {
        CFATAL_LOG("call _check_feature fail");
        return false;
    }
    int64_t filter = fea.in_filter() ? 1L : 0L;
    int64_t refer = fea.in_refer() ? 1L : 0L;

    std::lock_guard<std::mutex> lock(_mutex);
    if (_click_mode) {
        return _seg.enter(RateLimitNode(fea.view_sign(), fea.coord(), filter, refer), NULL);
    }

    if (_use_mem_pool) {
        return _pool_window.enter(fea.view_sign(), fea.coord(), RatioItem(filter, refer));
    } else {
        return _std_window.enter(fea.view_sign(), fea.coord(), RatioItem(filter, refer));
    }
}

bool RatioFeatureAccumulator::_update_and_query(FeatureValueProto* fea) {
    if (fea == NULL || !_check_feature(*fea)) {
        CFATAL_LOG("input fea == NULL or call _check_feature fail");
        return false;
    }

    int64_t filter = fea->in_filter() ? 1L : 0L;
    int64_t refer = fea->in_refer() ? 1L : 0L;

    std::lock_guard<std::mutex> lock(_mutex);
    if (_click_mode) {
        const RateLimitNode* res = NULL;
        if (!_seg.enter(RateLimitNode(fea->view_sign(), fea->coord(), filter, refer), &res)) {
            CWARNING_LOG("call click segment enter fail, feature_id(%lu), coord(%ld)",
                    _feature_id, fea->coord());
            return false;
        }
        return _set_feature(res, fea);
    }

    const RatioItem* ri = NULL;
    bool enter_success = false;
    if (_use_mem_pool) {
        enter_success = _pool_window.enter(fea->view_sign(), fea->coord(), RatioItem(filter, refer), &ri);
    } else {
        enter_success = _std_window.enter(fea->view_sign(), fea->coord(), RatioItem(filter, refer), &ri);
    }
    if (!enter_success) {
        CWARNING_LOG("call window enter fail, feature_id(%lu), coord(%ld)", _feature_id, fea->coord());
        return false;
    }
    return _set_feature(ri, fea);
}

bool RatioFeatureAccumulator::_query(FeatureValueProto* fea) const {
    if (fea == NULL || !_check_feature(*fea)) {
        CFATAL_LOG("input fea == NULL or call _check_feature fail");
        return false;
    }

    std::lock_guard<std::mutex> lock(_mutex);
    if (_click_mode) {
        return _set_feature(_seg.query_segment(fea->view_sign()), fea);
    }

    const RatioItem* ri = NULL;
    if (_use_mem_pool) {
        ri = _pool_window.query_segment(fea->view_sign(), fea->coord());
    } else {
        ri = _std_window.query_segment(fea->view_sign(), fea->coord());
    }

    if (ri == NULL && _weak_check) {
        return true;
    } else if (ri != NULL) {
        return _set_feature(ri, fea);
    }
    return false;
}

bool RatioFeatureAccumulator::_check_feature(const FeatureValueProto& fea) const {
    if (fea.feature_id() != _feature_id
        || (fea.feature_type() != FeatureValueProto::RATIO
            && fea.feature_type() != FeatureValueProto::BEHAVIOR_RATIO)) {
        CFATAL_LOG("feature_id(%lu) != _feature_id(%lu) || invalid feature_type(%d), logid(%lu)", 
                fea.feature_id(), _feature_id, fea.feature_type(), fea.log_id());
        return false;
    }

    if (!fea.in_filter() && !fea.in_refer() && !_weak_check) {
        CWARNING_LOG("neither in filter nor in refer, feature_id(%lu), logid(%lu)",
                _feature_id, fea.log_id());
        return false;
    }
    return true;
}

bool RatioFeatureAccumulator::_set_feature(
        const RatioItem* item,
        FeatureValueProto* fea) const {
    if (item == NULL) {
        fea->set_filter_count(0);
        fea->set_refer_count(0);
        fea->set_valid(_weak_check);
        fea->set_value("0");
        return true;
    }
    fea->set_filter_count(item->first());
    fea->set_refer_count(item->second());
    fea->set_valid(_remain_numerator ? 
            fea->filter_count() > _remain : fea->refer_count() > _remain);
    fea->set_in_anomaly_care(fea->in_filter());

    if (item->second() == 0) {
        fea->set_value("1");
        return true;
    }
    double ratio = 1.0 * item->first() / item->second();
    fea->set_value(boost::lexical_cast<std::string>(ratio));
    return true;
}

bool RatioFeatureAccumulator::_set_feature(
        const RateLimitNode* node,
        FeatureValueProto* fea) const {
    if (node == NULL) {
        fea->set_filter_count(0);
        fea->set_refer_count(0);
        fea->set_valid(false);
        fea->set_value("1");
        return true;
    }
    fea->set_filter_count(node->fit_cumulant);
    fea->set_refer_count(node->ref_cumulant);
    fea->set_valid(_remain_numerator ? 
            fea->filter_count() > _remain : fea->refer_count() > _remain);
    fea->set_in_anomaly_care(fea->in_filter());
    if (node->ref_cumulant == 0) {
        fea->set_value("1");
        return true;
    }
    double ratio = 1.0 * node->fit_cumulant / node->ref_cumulant;
    fea->set_value(boost::lexical_cast<std::string>(ratio));
    return true;
}

bool RatioFeatureAccumulator::_load(PosixIoReaderInterface *reader) {
    uint64_t ver = 0LU;
    if (reader->read(&ver, sizeof(ver)) != static_cast<int>(sizeof(ver))) {
        CWARNING_LOG("load version fail");
        return false;
    }

    // version > checkpoint version return true directly
    if (ver < _version) {
        return true;
    }

    std::lock_guard<std::mutex> locker(_mutex);
    if (_click_mode) {
        if (!_seg.deserialize(reader)) {
            CWARNING_LOG("call segment deserialize fail, feature_id(%lu)", _feature_id);
            return false;
        }
    } else {
        bool deserialize_success = false;
        if (_use_mem_pool) {
            deserialize_success = _pool_window.deserialize(reader);
        } else {
            deserialize_success = _std_window.deserialize(reader);
        }
        if (!deserialize_success) {
            CWARNING_LOG("call window deserialize fail, feature_id(%lu)", _feature_id);
            return false;
        }
    }
    return true;
}

bool RatioFeatureAccumulator::_dump(PosixIoWriterInterface *writer) const {
    if (writer->write(&_version, sizeof(_version)) 
            != static_cast<int>(sizeof(_version))) {
        CWARNING_LOG("write version(%lu) fail, feature_id(%lu)", 
                _version, _feature_id);
        return false;
    }

    std::lock_guard<std::mutex> locker(_mutex);
    if (_click_mode) {
        if (!_seg.serialize(writer)) {
            CWARNING_LOG("call segment serialize fail, feature_id(%lu)", _feature_id);
            return false;
        }
    } else {
        bool serialize_success = false;
        if (_use_mem_pool) {
            serialize_success = _pool_window.serialize(writer);
        } else {
            serialize_success = _std_window.serialize(writer);
        }
        if (!serialize_success) {
            CWARNING_LOG("call window serialize fail, feature_id(%lu)", _feature_id);
            return false;
        }
    }
    return true;
}

bool RatioFeatureAccumulator::query(
        const FValQRequestProto& request, 
        FValQResponseProto* response) const {
    if (!response || !request.has_view_sign()) {
        CFATAL_LOG("response is nullptr! or request invalid, feature_id(%lu)", _feature_id);
        return false;
    }
    std::lock_guard<std::mutex> locker(_mutex);
    uint64_t view_sign = request.view_sign();
    double ratio = 0.0;
    bool ok = _cal_ratio(view_sign, &ratio);
    if (!ok) {
        CWARNING_LOG("cal ratio failed!");
        return false;
    }
    response->add_value(boost::lexical_cast<std::string>(ratio));
    return true;
}

bool RatioFeatureAccumulator::_cal_ratio(
        const uint64_t sign, 
        double* ratio) const {
    int64_t filter_count = 0;
    int64_t refer_count = 0;
    if (_click_mode) {
        const RateLimitNode* node = _seg.query_segment(sign);
        if (!node) {
            CWARNING_LOG("query segment from _seg failed!view_sign[%lld]", sign);
            return false;
        }
        filter_count = node->fit_cumulant;
        refer_count = node->ref_cumulant;
    }
    else {
        const RatioItem* item = NULL;
        if (_use_mem_pool) {
            item = _pool_window.query_segment(sign);
        } else {
            item = _std_window.query_segment(sign);
        }
        if (!item) {
            CWARNING_LOG("query segment from _window failed!view_sign[%lld]", sign);
            return false;
        }
        filter_count = item->first();
        refer_count = item->second();
    }
    bool valid = _remain_numerator ? 
            filter_count > _remain : refer_count > _remain;
    if (!valid) {
        CWARNING_LOG("sign's result is invalid!"
                "fit : %d, ref:%d, _remain_numerator:%d, _remain:%d",
                filter_count, refer_count, int(_remain_numerator), _remain);
        return false;
    }
    if (refer_count == 0) {
        *ratio = 1.0;
        return true;
    }
    *ratio = 1.0 * filter_count / refer_count;
    return true;
}

void RatioFeatureAccumulator::print_monitor_log() const {
    if (_click_mode) {
        // SEG模式：无内存统计
        uint64_t element_count = _seg.window_view_sign_size();
        CNOTICE_LOG("Feature: ID=%lu, Type=RATIO, Elements=%lu, Memory=N/A", _feature_id, element_count);

    } else if (_use_mem_pool) {
        // POOL_WINDOW模式：详细内存统计
        bsl::var::Dict pool_dict;
        bsl::ResourcePool rp;
        _pool_window.monitor(pool_dict, rp);

        // === 元素统计 ===
        uint64_t total_elements = pool_dict["TOTAL_ELEMENTS"].to_uint64();
        uint64_t segment_elements = pool_dict["SEGMENT_ELEMENTS"].to_uint64();
        uint64_t window_elements = pool_dict["WINDOW_ELEMENTS"].to_uint64();
        
        // === 内存统计 ===
        uint64_t data_mem = pool_dict["DATA_MEM"].to_uint64();
        uint64_t pool_mgmt_overhead = pool_dict["POOL_MGMT_OVERHEAD"].to_uint64();
        uint64_t sliding_window_overhead = pool_dict["SLIDING_WINDOW_OVERHEAD"].to_uint64();
        uint64_t accumulator_overhead = sizeof(RatioFeatureAccumulator);
        
        uint64_t total_overhead = pool_mgmt_overhead + sliding_window_overhead + accumulator_overhead;
        uint64_t total_mem = data_mem + total_overhead;
        
        double avg_mem_per_element = total_elements > 0 ? 
            static_cast<double>(total_mem) / total_elements : 0.0;

        // === 第一行：简洁格式 ===
        CNOTICE_LOG(
            "Feature: ID=%lu, Type=RATIO, Elements=%lu(Seg=%lu,Win=%lu), "
            "Memory(Total=%lu,Data=%lu,Overhead=%lu,AvgPerElement=%.2f)",
            _feature_id, total_elements, segment_elements, window_elements,
            total_mem, data_mem, total_overhead, avg_mem_per_element);

        // === 第二行：详细格式 ===
        CNOTICE_LOG(
            "Feature: ID=%lu, Details: Coords[SegmentLen=%ld,OldestCoord=%ld,LatestCoord=%ld],"
            "SegmentHashMap[Size=%lu,BucketCount=%lu,LoadFactor=%.3f],"
            "MemPool[NodeNum=%lu,DataMem=%lu,MgmtOverhead=%lu]",
            _feature_id,
            pool_dict["SEGMENT_LENGTH"].to_int64(),
            pool_dict["OLDEST_COORD"].to_int64(), 
            pool_dict["LATEST_COORD"].to_int64(),
            pool_dict["SEGMENT_SIZE"].to_uint64(),
            pool_dict["SEGMENT_BUCKET_COUNT"].to_uint64(),
            pool_dict["SEGMENT_LOAD_FACTOR"].to_double(),
            total_elements,
            data_mem,
            pool_mgmt_overhead);

    } else {
        // STD_WINDOW模式：无内存统计
        uint64_t segment_size = _std_window.segment_view_sign_size();
        uint64_t window_size = _std_window.window_view_sign_size();
        uint64_t element_count = segment_size + window_size;

        CNOTICE_LOG("Feature: ID=%lu, Type=RATIO, Elements=%lu, Memory=N/A", _feature_id, element_count);
    }
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti