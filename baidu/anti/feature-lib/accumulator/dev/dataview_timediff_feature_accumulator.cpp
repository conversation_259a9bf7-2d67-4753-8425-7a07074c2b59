// Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved
// Author <PERSON> (<EMAIL>)
// 
// @brief 

#include "dataview_timediff_feature_accumulator.h"

namespace anti {
namespace themis {
namespace feature_lib {

const int32_t kNodeListMaxLength = 512;
const int64_t kMaxDiffValue = 0x1FFFFFFF;

DataViewTDAccumulator::DataViewTDAccumulator() : _feature_id(0u), _version(0u), _range(0), _max_dataview_size(100) {}
DataViewTDAccumulator::~DataViewTDAccumulator() { uninit(); }

bool DataViewTDAccumulator::init(const comcfg::ConfigUnit& conf) {
    int64_t window_length = 0;
    try {
        _feature_id = conf["feature_id"].to_uint64();
        _version = conf["version"].to_uint64();
        window_length = conf["window_length"].to_int64();

        if (conf["range"].selfType() == comcfg::CONFIG_ERROR_TYPE) {
            CDEBUG_LOG("no range option! use the default value %ld", _range);
        } else {
            _range = conf["range"].to_int64();
        }

        if (conf["max_dataview_size"].selfType() == comcfg::CONFIG_ERROR_TYPE) {
            CDEBUG_LOG("no max_dataview_size option! use the default value %ld", _max_dataview_size);
        } else {
            _max_dataview_size = conf["max_dataview_size"].to_int64();
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    }

    if (window_length < 0) {
        CFATAL_LOG("invalid window_length(%ld)", window_length);
        return false;
    }

    _goe_list.reset(new (std::nothrow) GOEList(window_length, kNodeListMaxLength));
    if (!_goe_list) {
        CFATAL_LOG("alloc goe_list failed");
        return false;
    }

    CWARNING_LOG("init DataViewTDAccumulator(%lu) success", _feature_id);
    return true;
}

void DataViewTDAccumulator::uninit() { _goe_list.reset(); }

bool DataViewTDAccumulator::update(const FeatureValueProto& fea) {
    if (!check_feature(fea)) {
        CWARNING_LOG("invalid fea");
        return false;
    }
    
    std::unordered_map<uint64_t, uint64_t> data_view;
    data_view.insert(std::make_pair(fea.data_view_sign(), fea.log_time()));
    DataViewNode node(fea.view_sign(), fea.log_time(), _max_dataview_size, data_view);
    auto ptr = _goe_list->insert(node);
    if (!ptr) {
        CWARNING_LOG("insert feat failed, [fea:%s]",
                fea.ShortDebugString().data());
        return false;
    }

    return true;
}

bool DataViewTDAccumulator::query(FeatureValueProto* fea) const {
    if (!check_feature(*fea)) {
        CWARNING_LOG("invalid fea");
        return false;
    }
    int32_t diff = kMaxDiffValue;
    auto list_ptr = _goe_list->query(fea->view_sign());
    if (!list_ptr) {
        // treat query failed case as normal feature
        CWARNING_LOG("query feat_list failed. [fea:%s]",
                fea->ShortDebugString().data());
    } else {
        std::unordered_map<uint64_t, uint64_t> data_view;
        uint64_t data_view_sign = fea->data_view_sign();
        uint64_t log_time = fea->log_time();
        data_view.insert(std::make_pair(data_view_sign, log_time));
        DataViewNode node(fea->view_sign(), log_time, _max_dataview_size, data_view);
        // check this list_ptr
        for (auto iter = list_ptr->begin(); iter != list_ptr->end(); ++iter) {
            if (iter->coord() > node.coord()) { continue; }
            if (iter->coord() == node.coord()) {
                if (iter->data_view_map().size() >= _max_dataview_size ||
                iter->data_view_map()[data_view_sign] != log_time) {
                    fea->add_neighbor_feat_distances(0);
                    break;
                }
                continue;
            }

            diff = node.coord() - iter->coord();

            if (_range == 0 || diff > _range) {
                break;  // don't need more comparisions!
            } else {
                if (iter->data_view_map().size() < _max_dataview_size && 
                iter->data_view_map().find(data_view_sign) == iter->data_view_map().end()) {
                    break;
                }
                fea->add_neighbor_feat_distances(diff);
                if (iter->data_view_map().size() > 1) {
                    break;
                }
            } 
        }
    }

    // for compatibility
    if (fea->neighbor_feat_distances_size() > 0) {
        fea->set_pre_feat_distance(fea->neighbor_feat_distances(0));
    } else {
        fea->set_pre_feat_distance(kMaxDiffValue);

    }
    fea->set_valid(true);

    CDEBUG_LOG("fea's [view_sign:%d], [data_view:%d], [log_time:%d], [coor:%d], [pre_dis:%d]", 
    fea->view_sign(), fea->data_view_sign(), fea->log_time(), fea->log_time() / 1000, fea->pre_feat_distance());
    for (auto dist : fea->neighbor_feat_distances()) {
        CDEBUG_LOG("fea's [view_sign:%d], [data_view:%d], [log_time:%d], [coor:%d], [neighbor_dis:%d]", 
        fea->view_sign(), fea->data_view_sign(), fea->log_time(), fea->log_time() / 1000, dist);
    }

    return true;
}

bool DataViewTDAccumulator::update_and_query(FeatureValueProto* fea) {
    if (fea == nullptr)
    {
        CWARNING_LOG("fea is null");
        return false;
    }
    if (!update(*fea)) {
        CWARNING_LOG("update feat failed");
        return false;
    }
    if (!query(fea)) {
        CWARNING_LOG("query feat failed");
        return true;
    }
    return true;
}

bool DataViewTDAccumulator::load(PosixIoReaderInterface *reader) {
    if (reader == nullptr)
    {
        CWARNING_LOG("reader is null");
        return false;
    }
    uint64_t ver = 0LU;
    if (reader->read(&ver, sizeof(ver)) != static_cast<int>(sizeof(ver))) {
       CWARNING_LOG("load version fail");
       return false;
    }

    // version > checkpoint version return true directly
    if (ver < _version) {
        CWARNING_LOG("checkpoint version is low, skip this checkpoint.[%d -> %d]",
                _version, ver);
       return true;
    }
    _version = ver;

    if (!_goe_list->deserialize(reader)) {
        CWARNING_LOG("goe_list deserialize failed. [id:%lu]", _feature_id);
        return false;
    }
    return true;
}

bool DataViewTDAccumulator::dump(PosixIoWriterInterface *writer) const {
    if (writer == nullptr)
    {
        CWARNING_LOG("writer is null");
        return false;
    }

    if (writer->write(&_version, sizeof(_version)) 
            != static_cast<int>(sizeof(_version))) {
        CWARNING_LOG("write version(%lu) fail, feature_id(%lu)", 
                _version, _feature_id);
        return false;
    }

    if (!_goe_list->serialize(writer)) {
        CWARNING_LOG("dump goe_list failed. [id:%lu]", _feature_id);
        return false;
    }
    return true;
}

bool DataViewTDAccumulator::check_feature(const FeatureValueProto& fea) const {
    if (fea.feature_id() != _feature_id
                || fea.feature_type() != FeatureValueProto::DATAVIEW_TIME_DIFF) {
        CFATAL_LOG("invalid feature. [fea:%s]", fea.ShortDebugString().data());
        return false;
    }
    return true;
}

}
}
}

