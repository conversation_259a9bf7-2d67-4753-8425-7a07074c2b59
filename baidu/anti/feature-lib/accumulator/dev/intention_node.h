#pragma once

#include <stdio.h>
#include <boost/lexical_cast.hpp>
#include <gflags/gflags.h>
#include <com_log.h>
#include "feature_value.pb.h"
#include "utils.h"

namespace anti {
namespace themis {
namespace feature_lib {

DECLARE_int32(max_query);
DECLARE_int32(max_intention);

#define CHECK_PTR(PTR)  \
    if (PTR == NULL)  { \
        CWARNING_LOG("#PTR is NULL"); \
        return false; \
    }

struct QueryUnit {
    QueryUnit(const std::string& input_query,
            const uint64_t& time,
            const int32_t& extend_value) :
        query(input_query),
        update_time(time),
        extend_view_value(extend_value) {}
    QueryUnit() {}

    std::string query;
    uint64_t update_time;
    int64_t extend_view_value;
};

typedef std::shared_ptr<QueryUnit> QueryUnitPtr;
typedef std::list<QueryUnitPtr> QueryUnitList;
typedef std::shared_ptr<QueryUnitList> QueryUnitListPtr;
typedef std::list<QueryUnitListPtr> IntentionList;
typedef IntentionList::iterator IntentionListIter;
typedef std::shared_ptr<IntentionList> IntentionListPtr;

class IntentionNode {
public:
    IntentionNode(const uint64_t& view_sign, 
            const int64_t& coord,
            const std::string& query,
            const uint32_t& extend_value,
            const float& threshold) :
        sign(view_sign),
        coord(coord),
        _update_time(coord),
        _threshold(threshold) {
            _intent_list_ptr.reset(new(std::nothrow) IntentionList());
            QueryUnitPtr unit(new(std::nothrow) QueryUnit(query, coord, extend_value));
            QueryUnitListPtr list_ptr(new(std::nothrow) QueryUnitList());
            list_ptr->emplace_back(unit);
            _intent_list_ptr->emplace_back(list_ptr);
            _intent_count = 1;
    };
     
    IntentionNode() : sign(0), coord(0), _update_time(0) {}

    //used by SessionSegment::_add_node
    IntentionNode(const IntentionNode& node) {
        sign = node.sign;
        coord = node.coord;
        _update_time = node._update_time;
        _threshold = node._threshold;
        _intent_list_ptr = node.intentions();
        _intent_count = node.value();
    }

    uint64_t update_time() const {
        return _update_time;
    }

    IntentionListPtr intentions() const {
        return _intent_list_ptr;
    }
 
    int64_t value() const {
        return _intent_count;
    }

    bool add(const IntentionNode& node, const int64_t& /*segment_len*/);

    template<typename Archive>
    bool serialize(Archive* ar) const;

    template<typename Archive>
    bool deserialize(Archive* ar);
    
    // used by SessionSegment
    uint64_t sign;
    uint64_t coord;

private:
    int64_t _update_time;
    int64_t _intent_count;
    float _threshold;
    IntentionListPtr _intent_list_ptr;

    bool _calc_similar(const std::string& in_str1, const std::string& in_str2 , float* value);
    bool _merge_intention_group(const std::vector<IntentionListIter>& merge_vec,
            const QueryUnitPtr query_unit_ptr);
    bool _add_query_to_intentions(QueryUnitPtr query_unit_ptr);
    bool _add_query_to_one_intention(
            const std::vector<IntentionListIter>& merge_vec,
            const QueryUnitPtr query_unit_ptr);
};

template<typename Archive>
bool IntentionNode::serialize(Archive* ar) const {
    if (!ar) {
        CWARNING_LOG("input Archive is NULL!");
        return false;
    }
    DUMP_VAR(_update_time, ar, int64_t);
    DUMP_VAR(_intent_count, ar, int64_t);
    DUMP_VAR(sign, ar, uint64_t);
    DUMP_VAR(coord, ar, uint64_t);
    for (const auto& row : *_intent_list_ptr) {
        uint64_t query_size = row->size();
        DUMP_VAR(query_size, ar, uint64_t);
        for (const auto unit : *row) {
            DUMP_VAR(unit->update_time, ar, uint64_t);
            DUMP_VAR(unit->extend_view_value, ar, int64_t);
            uint64_t query_len = unit->query.size();
            DUMP_VAR(query_len, ar, uint64_t);
            if (query_len != ar->write(unit->query.c_str(), query_len)) {
                CFATAL_LOG("fail to write query string, query:%s", unit->query.c_str());
                return false;
             }
        }
    }
    return true;
}

template<typename Archive>
bool IntentionNode::deserialize(Archive* ar) {
    if (!ar) {
        CWARNING_LOG("input Archive is NULL!");
        return false;
    }
    uint64_t str_size;
    LOAD_VAR(&_update_time, ar, int64_t);
    LOAD_VAR(&_intent_count, ar, int64_t);
    LOAD_VAR(&sign, ar, uint64_t);
    LOAD_VAR(&coord, ar, uint64_t);
    CDEBUG_LOG("update_time:%ld, _intention_count:%ld, sign:%ld, coord:%ld", _update_time, _intent_count, sign, coord);
    _intent_list_ptr.reset(new(std::nothrow) IntentionList());
    for (size_t i = 0; i < _intent_count; ++i) {
        QueryUnitListPtr list(new(std::nothrow) QueryUnitList());
        uint64_t query_size;
        LOAD_VAR(&query_size, ar, uint64_t);
        for (size_t i = 0; i < query_size; ++i) {
            QueryUnitPtr unit(new(std::nothrow) QueryUnit());
            if (unit == NULL) {
                CWARNING_LOG("fail to new QueryUnit.");
                return false;
            }
            LOAD_VAR(&(unit->update_time), ar, uint64_t);
            LOAD_VAR(&(unit->extend_view_value), ar, int64_t);
            uint64_t query_len;
            LOAD_VAR(&query_len, ar, uint64_t);
            char buf[query_len + 1] = {0};
            if (query_len != ar->read(buf, query_len)) {
                CFATAL_LOG("fail to read query string");
                return false;
             }
             unit->query.assign(buf);
             list->push_back(unit);
        }
        _intent_list_ptr->emplace_back(list);
    }
    return true;
}

} //feature_lib
} //themis
} //anti
