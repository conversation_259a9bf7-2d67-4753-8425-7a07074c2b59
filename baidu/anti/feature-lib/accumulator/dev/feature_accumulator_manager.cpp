// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangji<PERSON><PERSON>(<EMAIL>)
//
// @File: feature_accumulator_manager.cpp
// @Last modified: 2018-01-18 11:34:04
// @Brief:

#include <algorithm>
#include <gflags/gflags.h>
#include "boost/algorithm/string.hpp"
#include "feature_accumulator_manager.h"
#include <sys/stat.h>
#include <unistd.h>
#include <boost/lexical_cast.hpp>
#include <Configure.h>
#include <com_log.h>
#include "feature_util.h"
#include "thread_pool.h"
#include "checkpoint_manager.h"
#include "extend_accumulator_base.h"

namespace anti {
namespace themis {
namespace feature_lib {

DECLARE_bool(enable_fea_service_acc);

bool FeatureAccumulatorManager::init(
        const std::string& conf_path,
        const std::string& conf_file) {
    _conf_path = conf_path;
    _conf_file = conf_file;
    if (!_init_fprc_mgr()) {
        CFATAL_LOG("init FValueRPCManager fail, conf(%s/%s)", _conf_path.data(), _conf_file.data());
        return false;
    }
    if (!reload()) {
        CFATAL_LOG("call reload fail, conf(%s/%s)", _conf_path.data(), _conf_file.data());
        return false;
    }
    CWARNING_LOG("init FeatureAccumulatorManager success");
    return true;
}

void FeatureAccumulatorManager::uninit() {
    _acc_coords.clear();
    if (_fprc_mgr) {
        _fprc_mgr.reset();
    }
    return;
}

bool FeatureAccumulatorManager::reload() {
    std::string conf = _conf_path + "/" + _conf_file;
    struct stat buf;
    if (stat(conf.data(), &buf) != 0) {
        CFATAL_LOG("stat %s failed", conf.data());
        return false;
    }

    if (_modify_time == buf.st_mtime) {
        return true;
    }

    if (!_reload()) {
        CFATAL_LOG("call conf(%s) _reload() fail", conf.data());
        return false;
    }
    _modify_time = buf.st_mtime;
    CWARNING_LOG("reload accmulator(%s) success", conf.data());
    return true;
}

bool FeatureAccumulatorManager::_reload() {
    comcfg::Configure conf;
    if (conf.load(_conf_path.c_str(), _conf_file.c_str()) != 0) {
        CFATAL_LOG("load %s/%s failed", _conf_path.c_str(), _conf_file.c_str());
        return false;
    }
    AccCoordMap accs_to_del(_acc_coords);
    try {
        std::vector<std::string> feature_list;
        if (conf["feature_list"].selfType() != comcfg::CONFIG_ERROR_TYPE
            && !parse_list(std::string(conf["feature_list"].to_cstr()), &feature_list)) {
            CFATAL_LOG("call parse_list(%s) fail", conf["feature_list"].to_cstr());
            return false;
        }

        if (conf["fea_list"].selfType() != comcfg::CONFIG_ERROR_TYPE
            && !parse_list(conf, std::string("fea_list"), &feature_list)) {
            CFATAL_LOG("call parse_list(fea_list) fail");
            return false;
        }

        if (feature_list.size() == 0) {
            CFATAL_LOG("no feature_list or fea_list in conf");
            return false;
        }

        uint32_t num = conf["feature"].size();
        for (uint32_t i = 0U; i < num; ++i) {
            uint64_t fea_id = conf["feature"][i]["feature_id"].to_uint64();
            std::vector<std::string>::iterator feature_ite = std::find(
                    feature_list.begin(),
                    feature_list.end(),
                    boost::lexical_cast<std::string>(fea_id));
            if (feature_ite == feature_list.end()) {
                CWARNING_LOG("feature_id[%lu] is NOT in feature_list or already exists",
                        fea_id);
                continue;
            }
            // 1. remove feature_id in conf
            accs_to_del.erase(fea_id);
            // 2. insert feature_id which not in accs
            if (_acc_coords.find(fea_id) != _acc_coords.end()) {
                continue;
            }
            std::string fea_type(conf["feature"][i]["feature_type"].to_cstr());
            std::shared_ptr<FeatureAccumulatorInterface> acc(
                    FeatureAccumulatorFactory::create(fea_type));
            if (!acc || !acc->init(conf["feature"][i], _fprc_mgr)) {
                CWARNING_LOG("create and init accumulator(%s) fail", fea_type.data());
                return false;
            }
            _acc_coords[fea_id] = std::make_shared<FeatureAccumulatorAndCoord>(
                    FeatureAccumulatorAndCoord(acc));
        }
    } catch (const comcfg::ConfigException& e) {
        CFATAL_LOG("ConfigException : %s", e.what());
        return false;
    }
    // 3. delete feature_id in accs_to_del(remove from _accs first)
    for (ACMIter iter = accs_to_del.begin(); iter != accs_to_del.end(); ++iter) {
        _acc_coords.erase(iter->first);
    }
    accs_to_del.clear();
    return true;
}

bool FeatureAccumulatorManager::load(const std::string& checkpoint_path) {
    CheckpointManager checkpoint_manager(checkpoint_path);
    if (_version_id != "") {
        checkpoint_manager.set_version_id(_version_id);
    }
    AccMap accs;
    CoordMap coords;
    _split_map(&coords, &accs);
    coords.clear();
    return checkpoint_manager.load(&coords, &accs) && _merge_map(coords, accs);
}

bool FeatureAccumulatorManager::load(const std::string& checkpoint_path,
        anti::themis::RecordType record_type) {
    CheckpointManager checkpoint_manager(checkpoint_path, RecordType_Name(record_type));
    if (_version_id != "") {
        checkpoint_manager.set_version_id(_version_id);
    }
    AccMap accs;
    CoordMap coords;
    _split_map(&coords, &accs);
    coords.clear();
    return checkpoint_manager.load(&coords, &accs) && _merge_map(coords, accs);
}

bool FeatureAccumulatorManager::dump(const std::string& checkpoint_path) {
    CheckpointManager checkpoint_manager(checkpoint_path);
    if (_version_id != "") {
        checkpoint_manager.set_version_id(_version_id);
    }
    AccMap accs;
    CoordMap coords;
    _split_map(&coords, &accs);
    return checkpoint_manager.dump(coords, accs);
}

bool FeatureAccumulatorManager::dump(const std::string& checkpoint_path,
        anti::themis::RecordType record_type) {
    CheckpointManager checkpoint_manager(checkpoint_path, RecordType_Name(record_type));
    if (_version_id != "") {
        checkpoint_manager.set_version_id(_version_id);
    }
    AccMap accs;
    CoordMap coords;
    _split_map(&coords, &accs);
    return checkpoint_manager.dump(coords, accs);
}

bool FeatureAccumulatorManager::update(const FeatureValueProto& fea) {
    ACMIter iter = _acc_coords.find(fea.feature_id());
    if (iter == _acc_coords.end()) {
        CWARNING_LOG("find accmulator(%lu) fail", fea.feature_id());
        return false;
    }

    if (fea.has_seg_type() 
            && fea.seg_type() == FeatureValueProto::PV) {
        // pv coord
        FeatureValueProto real_fea(fea);
        real_fea.set_coord(fea.coord() + iter->second->coord);
        return iter->second->acc_iface->update(real_fea);
    }
    // time coord
    if (fea.seg_type() == FeatureValueProto::TIME) {
        // time coord insert to _coords
        iter->second->coord = fea.coord();
    }
    return iter->second->acc_iface->update(fea);
}

bool FeatureAccumulatorManager::update_and_query(FeatureValueProto* fea) {
    if (fea == NULL) {
        CWARNING_LOG("input fea is NULL");
        return false;
    }
    ACMIter iter = _acc_coords.find(fea->feature_id());
    if (iter == _acc_coords.end()) {
        CWARNING_LOG("find accmulator(%lu) fail", fea->feature_id());
        return false;
    }

    if (fea->has_seg_type() 
            && fea->seg_type() == FeatureValueProto::PV) {
        // pv coord
        CDEBUG_LOG("pv coord (%lu), feature id (%lu)", iter->second->coord, fea->feature_id());
        fea->set_coord(fea->coord() + iter->second->coord);
    }
    // time coord
    if (fea->seg_type() == FeatureValueProto::TIME) {
        // time coord insert to _acc_coords
        iter->second->coord = fea->coord();
    }
    return iter->second->acc_iface->update_and_query(fea);
}

bool FeatureAccumulatorManager::query(FeatureValueProto* fea) const {
    if (fea == NULL) {
        CWARNING_LOG("input fea is NULL");
        return false;
    }
    ACMCIter iter = _acc_coords.find(fea->feature_id());
    if (iter == _acc_coords.end()) {
        CWARNING_LOG("find accmulator(%lu) fail", fea->feature_id());
        return false;
    }
    if (fea->has_seg_type() 
            && fea->seg_type() == FeatureValueProto::PV) {
        // pv coord
        fea->set_coord(fea->coord() + iter->second->coord);
    }
    // time coord
    return iter->second->acc_iface->query(fea);
}

void FeatureAccumulatorManager::increase_coords(
        const std::vector<std::pair<uint64_t, int64_t> >& coords) {
    for (uint64_t i = 0U; i < coords.size(); ++i) {
        uint64_t fea_id = coords[i].first;
        if (_acc_coords.find(fea_id) != _acc_coords.end()) {
            CDEBUG_LOG("before increase feature id is [%lu], coords is [%lu]", coords[i].first, _acc_coords[fea_id]->coord);
            _acc_coords[fea_id]->coord += coords[i].second;
            CDEBUG_LOG("after increase feature id is [%lu], coords is [%lu]", fea_id, _acc_coords[fea_id]->coord);
            continue;
        }
        CDEBUG_LOG("not found fea_id[%lu] in acc_coord_map, skip increase", fea_id);
    }
}

std::vector<std::shared_ptr<FeatureAccumulatorInterface>> FeatureAccumulatorManager::get_accs() {
    std::vector<std::shared_ptr<FeatureAccumulatorInterface>> output_accs;
    for (auto acc : _acc_coords) {
        output_accs.emplace_back(acc.second->acc_iface);
    }
    return output_accs;
}

bool FeatureAccumulatorManager::_init_fprc_mgr() {
    if (!FLAGS_enable_fea_service_acc) {
        return true;
    }
    comcfg::Configure conf;
    if (conf.load(_conf_path.c_str(), _conf_file.c_str()) != 0) {
        CFATAL_LOG("load %s/%s failed", _conf_path.c_str(), _conf_file.c_str());
        return false;
    }

    if (conf["fs_client_conf_path"].selfType() == comcfg::CONFIG_ERROR_TYPE
        || conf["fs_client_conf_file"].selfType() == comcfg::CONFIG_ERROR_TYPE) {
        return true;
    }
    std::string fs_conf_path(conf["fs_client_conf_path"].to_cstr());
    std::string fs_conf_file(conf["fs_client_conf_file"].to_cstr());
    _fprc_mgr.reset(new (std::nothrow) FValueRPCManager());
    if (!_fprc_mgr 
            || !_fprc_mgr->init(fs_conf_path.data(), fs_conf_file.data())) {
        CWARNING_LOG("feature client new or init fail, conf(%s/%s)",
                fs_conf_path.data(), fs_conf_file.data());
        return false;
    }
    return true;
}

bool FeatureAccumulatorManager::query(
    const FValQRequestProto& request,
    FValQResponseProto* response) const {
    if (response == NULL || !request.has_feature_id()) {
        CWARNING_LOG("input response is NULL");
        return false;
    }
    ACMCIter iter = _acc_coords.find(request.feature_id());
    if (iter == _acc_coords.end()) {
        CWARNING_LOG("find accmulator(%lu) fail", request.feature_id());
        return true;
    }
    return iter->second->acc_iface->query(request, response);
}

void FeatureAccumulatorManager::sync( 
        std::vector<std::shared_ptr<FeatureValueProto>>* feas) {
    if (!feas) {
        CFATAL_LOG("input feas ptr is NULL");
        return;
    }

    if (_fprc_mgr) {
        _fprc_mgr->sync();
    }
    // split feas by feature id
    std::unordered_map<uint64_t, std::vector<std::shared_ptr<FeatureValueProto>>> split_feas;
    for (auto iter = feas->begin(); iter != feas->end(); ++iter) {
        auto item = split_feas.find((*iter)->feature_id());
        if (item != split_feas.end()) {
            item->second.push_back(*iter);
        } else {
            std::vector<std::shared_ptr<FeatureValueProto>> vec;
            vec.push_back(*iter);
            split_feas[(*iter)->feature_id()] = vec;
        }
    }

    for (auto& feas : split_feas) {
        auto acc = _acc_coords.find(feas.first);
        if (acc == _acc_coords.end()) {
            continue;
        }

        if (acc->second) {
            acc->second->acc_iface->sync(&(feas.second));
        }
    }
    return;
}

void FeatureAccumulatorManager::_split_map(CoordMap* coords, AccMap* accs) {
    for (ACMIter iter = _acc_coords.begin(); iter != _acc_coords.end(); ++iter) {
        CDEBUG_LOG("map spiliting, the feature id is [%lu], coord is [%lu]", iter->first, iter->second->coord);
        (*accs)[iter->first] = iter->second->acc_iface;
        (*coords)[iter->first] = iter->second->coord;
    }
}

bool FeatureAccumulatorManager::_merge_map(const CoordMap& coords, const AccMap& accs) {
    for(AMCIter iter = accs.begin(); iter != accs.end(); ++iter) {
        _acc_coords[iter->first] = std::make_shared<FeatureAccumulatorAndCoord>(FeatureAccumulatorAndCoord(iter->second));
        auto coord_iter = coords.find(iter->first);
        if (coord_iter != coords.end()) {
            _acc_coords[iter->first]->coord = coord_iter->second;
            CDEBUG_LOG("map merging, the feature id is [%lu], coord is [%lu]", coord_iter->first, coord_iter->second);
        } else {
            CWARNING_LOG("a feature id [%lu] is found in accs, but not found in coords", iter->first);
        }
    }
    return true;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti