// Copyright 2016 Baidu Inc. All Right Reserved.
// Author <PERSON> (<EMAIL>)
// 
// brief: 

#include <boost/lexical_cast.hpp>
#include <boost/algorithm/string.hpp>
#include "distinct_distribution_feature_accumulator.h"

namespace anti {
namespace themis {
namespace feature_lib {

bool DistinctDistributionFeatureAccumulator::init(const comcfg::ConfigUnit& conf) {
    int64_t window_len = 0L;
    try {
        _feature_id = conf["feature_id"].to_uint64();
        _version = conf["version"].to_uint64();
        window_len = conf["window_length"].to_int64();
        std::string interval_points(conf["interval_endpoints"].to_cstr());
        if (!_init_intervals(interval_points)) {
            CWARNING_LOG("call _init_intervals(%s) failed, feature_id(%lu)",
                    interval_points.data(), _feature_id);
            return false;
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std::Exception : %s", e.what());
        return false;
    }
    _distribution_distance.reset(new(std::nothrow) DistributionDistance());
    if (!_distribution_distance || 
            !_distribution_distance->init(conf)) {
        _distribution_distance.reset();
        CWARNING_LOG("create distance or init failed, feaid[%lu]", _feature_id);
    }

    if (window_len <= 0L) {
        CWARNING_LOG("invalid window_len(%ld)", window_len);
        return false;
    }
    const int MAX_STEP_NUM = 2;
    if (!_seg_window.init(window_len, MAX_STEP_NUM, window_len)) {
        CWARNING_LOG("call segment window init(%ld, %ld, %ld) fail",
                window_len, MAX_STEP_NUM, window_len);
        return false;
    }

    if (!_dis_window.init(window_len, MAX_STEP_NUM, window_len)) {
        CWARNING_LOG("call distribution window init(%ld, %ld, %ld) fail",
                window_len, MAX_STEP_NUM, window_len);
        return false;
    }
    
    CWARNING_LOG("init DistinctDistributionFeatureAccumulator(%lu) success", _feature_id);
    return true;
}

void DistinctDistributionFeatureAccumulator::uninit() {
    _seg_window.uninit();
    _dis_window.uninit();
    _intervals.clear();
    _feature_id = 0LU;
    _version = 0LU;
}

bool DistinctDistributionFeatureAccumulator::_init_intervals(const std::string& points) {
    std::vector<std::string> point_strs;
    boost::algorithm::split(point_strs, points, boost::is_any_of(", "), boost::token_compress_on);
    int64_t start = LLONG_MIN;
    for (size_t i = 0U; i < point_strs.size(); ++i) {
        try {
            int64_t end = boost::lexical_cast<int64_t>(point_strs[i]);
            _intervals.emplace_back(start, end);
            start = end;
        } catch (const boost::bad_lexical_cast& e) {
            CWARNING_LOG("boost::Exception : %s", e.what());
            return false;
        }
    }
    _intervals.emplace_back(start, LLONG_MAX);
    return true;
}

bool DistinctDistributionFeatureAccumulator::_get_interval_idx(
        int64_t cumulant, uint32_t* idx, bool jump_essential) const {
    uint32_t tmp_idx = UINT_MAX;
    bool jump_bucket = false;
    for (uint32_t i = 0; i < _intervals.size(); i++) {
        if (_intervals[i].first < cumulant && cumulant <= _intervals[i].second) {
            tmp_idx = i;
            jump_bucket = jump_essential && (cumulant == _intervals[i].first + 1);
            break;
        }
    }
    *idx = tmp_idx;
    return jump_bucket;
}

bool DistinctDistributionFeatureAccumulator::update_and_query(FeatureValueProto* fea) {
    if (fea == NULL) {
        CFATAL_LOG("input fea == NULL, feature_id(%lu)", _feature_id);
        return false;
    }
    const DistributionItem* dis_res = NULL;
    uint32_t idx = 0;
    if (!_update(*fea, &dis_res, &idx)) {
        CWARNING_LOG("call _update fail, feature_id(%lu), cood(%ld), logid(%lu)",
                _feature_id, fea->coord(), fea->log_id());
        return false;
    }
    return _fillup_feature(dis_res, idx, fea);
}

bool DistinctDistributionFeatureAccumulator::update(const FeatureValueProto& fea) {
    return _update(fea, NULL, NULL);
}

bool DistinctDistributionFeatureAccumulator::query(FeatureValueProto* fea) const {
    if (fea == NULL || !_check_feature(*fea)) {
        CFATAL_LOG("input fea == NULL or call _check_feature fail, feature_id(%lu)", _feature_id);
        return false;
    }
    const DistinctItem* seg_res = _seg_window.query_segment(
            fea->data_view_sign(), fea->coord());
    if (seg_res == NULL) {
        CTRACE_LOG("call segment query fail feature_id(%lu) logid(%lu)",
                fea->feature_id(), fea->log_id());
        return false;
    }
    uint32_t idx = 0;
    _get_interval_idx(seg_res->distinct_num(), &idx, true);
    return _fillup_feature(_dis_window.query_segment(fea->view_sign()), idx, fea);
}

bool DistinctDistributionFeatureAccumulator::_update(
        const FeatureValueProto& fea, const DistributionItem** item, uint32_t* idx) {
    if (!_check_feature(fea)) {
        CFATAL_LOG("call _check_feature fail");
        return false;
    }

    DistinctItem distinct_item(fea.cumulate_view_sign(), 1L);
    const DistinctItem* pre_query_seg_res = _seg_window.query_segment(
            fea.data_view_sign(), fea.coord());
    int64_t pre_distinct_num = 0;

    if (pre_query_seg_res != NULL) {
        pre_distinct_num = pre_query_seg_res->distinct_num();     
    }
    const DistinctItem* seg_res = NULL;
    if (!_seg_window.enter(fea.data_view_sign(), fea.coord(), distinct_item, &seg_res)
            || seg_res == NULL) {
        CWARNING_LOG("enter _seg_window fail, feature_id(%lu), coord(%ld), logid(%lu)",
                _feature_id, fea.coord(), fea.log_id());
        return false;
    }
    uint32_t tmp_idx = 0;
    DistributionItem dis_item;
    bool jump_essential = pre_distinct_num != seg_res->distinct_num();
   
    if (_get_interval_idx(seg_res->distinct_num(), &tmp_idx, jump_essential)) {
        // use distinct num to shard total to distribute
        // erase last bucket: 0 - (total - 1)
        dis_item[tmp_idx - 1] = (1 - seg_res->total());
        dis_item[tmp_idx] = seg_res->total();
    } else {
        dis_item[tmp_idx] = 1;
    }
    if (!_dis_window.enter(fea.view_sign(), fea.coord(), dis_item, item)) {
        CWARNING_LOG("enter _dis_window fail, feature_id(%lu), coord(%ld), logid(%lu)",
                _feature_id, fea.coord(), fea.log_id());
        return false;
    }

    if (idx != NULL) {
        *idx = tmp_idx;
    }
    return true;
}

bool DistinctDistributionFeatureAccumulator::_check_feature(const FeatureValueProto& fea) const {
    if (fea.feature_id() != _feature_id
            || fea.feature_type() != FeatureValueProto::DISTINCT_DISTRIBUTION) {
        CFATAL_LOG("feature_id(%lu) != _feature_id(%lu) or invalid feature_type(%d), logid(%lu)",
                fea.feature_id(), _feature_id, fea.feature_type(), fea.log_id());
        return false;
    }
    return true;
}

bool DistinctDistributionFeatureAccumulator::_fillup_feature(
        const DistributionItem* item, uint32_t idx, FeatureValueProto* fea) const {
    fea->set_bucket_idx(idx);
    if (item == NULL) {
        for (uint32_t i = 0; i < _intervals.size(); ++i) {
            FeatureValueProto::BucketProto* bucket = fea->add_buckets();
            bucket->set_idx(i);
            bucket->set_count(0);
        }
        return true;
    }
    for (uint32_t i = 0; i < _intervals.size(); ++i) {
        FeatureValueProto::BucketProto* bucket = fea->add_buckets();
        bucket->set_idx(i);
        bucket->set_count((*item)[i]);
    }
    if (_distribution_distance.get() != NULL && !_distribution_distance->calculate(fea)) {
        CWARNING_LOG("distribution distance calculate error, feature_id(%lu).",
                fea->feature_id());
        return false;
    }
    return true;
}

bool DistinctDistributionFeatureAccumulator::load(PosixIoReaderInterface *reader) {
    uint64_t ver = 0LU;
    if (reader->read(&ver, sizeof(ver)) != static_cast<int>(sizeof(ver))) {
        CWARNING_LOG("load version fail");
        return false;
    }

    if (ver < _version) {
        return true;
    }

    if (!_seg_window.deserialize(reader)) {
        CWARNING_LOG("call _seg_window deserialize fail, feature_id(%lu)", _feature_id);
        return false;
    }

    if (!_dis_window.deserialize(reader)) {
        CWARNING_LOG("call dis_window deserialize fail, feature_id(%lu)", _feature_id);
        return false;
    }
    return true;
}

bool DistinctDistributionFeatureAccumulator::dump(PosixIoWriterInterface *writer) const {
    if (writer->write(&_version, sizeof(_version)) != static_cast<int>(sizeof(_version))) {
        CWARNING_LOG("write version(%lu) fail, feature_id(%lu)", _version, _feature_id);
        return false;
    }

    if (!_seg_window.serialize(writer)) {
        CWARNING_LOG("call _seg_window serialize fail, feature_id(%lu)", _feature_id);
        return false;
    }

    if (!_dis_window.serialize(writer)) {
        CWARNING_LOG("call dis_window serialize fail, feature_id(%lu)", _feature_id);
        return false;
    }
    return true;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti
