// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// @Author: houruijie(<EMAIL>)
//
// @File: distribution_distance.h
// @Last modified: 2018-01-17 20:43:43
// @Brief:

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_DISTRIBUTION_DISTANCE_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_DISTRIBUTION_DISTANCE_H

#include <string>
#include "feature_accumulator_interface.h"

namespace anti {
namespace themis {
namespace feature_lib {

typedef double (*Distance)(
            const std::vector<double>& pb,
            const std::vector<double>& pc,
            uint32_t bucket_idx);

class DistributionDistance {
public:
    DistributionDistance() : 
            _distance_operator(NULL), 
            _count_threshold(0L), 
            _max_distance_magnify(1.0) {}
    bool init(
            const std::string& dis_type,
            const std::string& sta_pro, 
            int count_threshold, 
            double max_distance_magnify);
    bool init(const comcfg::ConfigUnit& conf);
    bool calculate(FeatureValueProto* fea) const;
    int64_t count_threshold() const { return _count_threshold;}
    bool magnify_dis(const std::vector<double>& pb, std::vector<double>* pc) const;
private:
    bool _create_type(const std::string& dis_type);
    bool _split_bucket(const std::string& sta_pro);
    Distance _distance_operator;
    int64_t _count_threshold;
    double _max_distance_magnify;
    std::vector<double> _stand_prob;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_DISTRIBUTION_DISTANCE_H

/* vim: set ts=4 sw=4: */
