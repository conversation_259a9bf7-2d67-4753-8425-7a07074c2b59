// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: ch<PERSON><PERSON><PERSON><PERSON>(ch<PERSON><PERSON><PERSON><PERSON>@baidu.com)
// 
// @File: three_level_tree_feature_accumulator.h
// @Last modified: 2018-05-24 21:11:35
// @Brief: 

#ifndef BAIDU_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_THREE_LEVEL_TREE_FEATURE_ACCUMULATOR_H
#define BAIDU_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_THREE_LEVEL_TREE_FEATURE_ACCUMULATOR_H

#include "feature_accumulator_interface.h"
#include <item.h>
#include <sliding_window.h>

namespace anti {
namespace themis {
namespace feature_lib {

class ThreeLevelTreeFeatureAccumulator : public FeatureAccumulatorInterface {
public:
    ThreeLevelTreeFeatureAccumulator() : 
            _feature_id(0LU), 
            _version(0LU) {
    }

    virtual ~ThreeLevelTreeFeatureAccumulator() {
        uninit();
    }
    virtual uint64_t feature_id() const {
        return _feature_id;
    }
    virtual bool init(const comcfg::ConfigUnit& conf);
    virtual void uninit();

    virtual bool update(const FeatureValueProto& fea);
    virtual bool update_and_query(FeatureValueProto* fea);
    virtual bool query(FeatureValueProto* fea) const;

    virtual bool load(PosixIoReaderInterface *reader);
    virtual bool dump(PosixIoWriterInterface *writer) const;

private:
    // const static int MAX_BUCKET_NUM = 10;
    typedef anti::themis::common_lib::SecondaryLeafNode SecondaryLeafNode;
    typedef anti::themis::common_lib::TreeItem<SecondaryLeafNode> ThreeLevelTreeItem;
    typedef anti::themis::common_lib::SlidingWindow<ThreeLevelTreeItem> ThreeLevelTreeWindow;
    typedef anti::themis::common_lib::TreeItemStatisticCond TreeItemStatisticCond;
    bool _update(const FeatureValueProto& fea, const ThreeLevelTreeItem** res_ptr);
    bool _check_feature(const FeatureValueProto& fea) const;
    bool _fillup_feature(const ThreeLevelTreeItem* item, FeatureValueProto* fea) const;
    std::shared_ptr<ThreeLevelTreeWindow> _window;
    uint64_t _feature_id;
    uint64_t _version;
    TreeItemStatisticCond _tree_cond;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif // BAIDU_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_THREE_LEVEL_TREE_FEATURE_ACCUMULATOR_H

/* vim: set ts=4 sw=4: */

