// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: count_distribution_feature_accumulator.cpp
// @Last modified: 2018-01-23 21:02:09
// @Brief: 

#include "count_distribution_feature_accumulator.h"
#include <boost/algorithm/string.hpp>
#include <boost/lexical_cast.hpp>

namespace anti {
namespace themis {
namespace feature_lib {

bool CountDistributionFeatureAccumulator::init(const comcfg::ConfigUnit& conf) {
    try {
        _feature_id = conf["feature_id"].to_uint64();
        _version = conf["version"].to_uint64();
        int64_t window_length = conf["window_length"].to_int64();
        std::string points(conf["interval_endpoints"].to_cstr());
        // init intervals
        if (!_init_intervals(points)) {
            CWARNING_LOG("call _init_intervals(%s) fail, feature_id(%lu)", 
                    points.data(), _feature_id);
            return false;
        }
        // inint count segment
        if (!_count_seg.init(window_length)) {
            CWARNING_LOG("call _count_seg init(%ld) fail, feature_id(%lu)", 
                    window_length, _feature_id);
            return false;
        }
        // init  dist segment
        if (!_dist_seg.init(window_length)) {
            CWARNING_LOG("call _dist_seg init(%ld) fail, feature_id(%lu)",
                    window_length, _feature_id);
            return false;
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std::Exception : %s", e.what());
        return false;
    }
    _distribution_distance.reset(new(std::nothrow) DistributionDistance());
    if (!_distribution_distance || 
            !_distribution_distance->init(conf)) {
        _distribution_distance.reset();  
        CWARNING_LOG("create distance or init failed, feaid[%lu]", _feature_id);
    }
    CWARNING_LOG("init CountDistributionFeatureAccumulator(%lu) success", _feature_id);
    return true;
}

void CountDistributionFeatureAccumulator::uninit() {
    _count_seg.uninit();
    _dist_seg.uninit();
    _intervals.clear();
    _feature_id = 0LU;
    _version = 0LU;
}

bool CountDistributionFeatureAccumulator::update_and_query(FeatureValueProto* fea) {
    if (fea == NULL) {
        CFATAL_LOG("input fea == NULL, feature_id(%lu)", _feature_id);
        return false;
    }
    const DistributionLimitNode* dist_res = NULL;
    uint32_t idx = 0;
    if (!_update(*fea, &dist_res, &idx)) {
        CWARNING_LOG("call _update fail, feature_id(%lu), cood(%ld), logid(%lu)",
                _feature_id, fea->coord(), fea->log_id());
        return false;
    }
    _set_feature(dist_res, idx, fea);
    // unreasonable but to be same as dorado
    fea->mutable_buckets(idx)->set_count(dist_res->buckets[idx] - 1L);
    return true;
}

bool CountDistributionFeatureAccumulator::update(const FeatureValueProto& fea) {
    return _update(fea, NULL, NULL);
}

bool CountDistributionFeatureAccumulator::query(FeatureValueProto* fea) const {
    if (fea == NULL || !_check_feature(*fea)) {
        CFATAL_LOG("input fea == NULL or call _check_feature fail, feature_id(%lu)", _feature_id);
        return false;
    }
    const CountNode* count_res = _count_seg.query_segment(
            fea->data_view_sign(), fea->coord());
    if (count_res == NULL) {
        CTRACE_LOG("call segment query fail feature_id(%lu) logid(%lu)",
                fea->feature_id(), fea->log_id());
        return false;
    }
    uint32_t idx = 0;
    _get_interval_idx(count_res->cumulant, &idx);
    return _set_feature(_dist_seg.query_segment(fea->view_sign()), idx, fea);
}

bool CountDistributionFeatureAccumulator::load(PosixIoReaderInterface *reader) {
    uint64_t ver = 0LU;
    if (reader->read(&ver, sizeof(ver)) != static_cast<int>(sizeof(ver))) {
        CWARNING_LOG("load version fail");
        return false;
    }

    if (ver < _version) {
        return true;
    }

    if (!_count_seg.deserialize(reader)) {
        CWARNING_LOG("call count_seg deserialize fail, feature_id(%lu)", _feature_id);
        return false;
    }

    if (!_dist_seg.deserialize(reader)) {
        CWARNING_LOG("call dist_seg deserialize fail, feature_id(%lu)", _feature_id);
        return false;
    }
    return true;
}

bool CountDistributionFeatureAccumulator::dump(PosixIoWriterInterface *writer) const {
    if (writer->write(&_version, sizeof(_version)) != static_cast<int>(sizeof(_version))) {
        CWARNING_LOG("write version(%lu) fail, feature_id(%lu)", _version, _feature_id);
        return false;
    }

    if (!_count_seg.serialize(writer)) {
        CWARNING_LOG("call count_seg serialize fail, feature_id(%lu)", _feature_id);
        return false;
    }

    if (!_dist_seg.serialize(writer)) {
        CWARNING_LOG("call dist_seg serialize fail, feature_id(%lu)", _feature_id);
        return false;
    }
    return true;
}

bool CountDistributionFeatureAccumulator::_init_intervals(const std::string& points) {
    std::vector<std::string> point_strs;
    boost::algorithm::split(point_strs, points, boost::is_any_of(", "), boost::token_compress_on);
    int64_t start = LLONG_MIN;
    for (size_t i = 0U; i < point_strs.size(); ++i) {
        try {
            int64_t end = boost::lexical_cast<int64_t>(point_strs[i]);
            _intervals.emplace_back(start, end);
            start = end;
        } catch (const boost::bad_lexical_cast& e) {
            CWARNING_LOG("boost::Exception : %s", e.what());
            return false;
        }
    }
    _intervals.emplace_back(start, LLONG_MAX);
    return true;
}
bool CountDistributionFeatureAccumulator::_get_interval_idx(
        int64_t cumulant, uint32_t* idx) const {
    uint32_t tmp_idx = UINT_MAX;
    bool jump_bucket = false;
    for (uint32_t i = 0; i < _intervals.size(); i++) {
        if (_intervals[i].first < cumulant && cumulant <= _intervals[i].second) {
            tmp_idx = i;
            jump_bucket = (cumulant == _intervals[i].first + 1);
            break;
        }
    }
    *idx = tmp_idx;
    return jump_bucket;
}

bool CountDistributionFeatureAccumulator::_check_feature(const FeatureValueProto& fea) const {
    if (fea.feature_id() != _feature_id
            || fea.feature_type() != FeatureValueProto::COUNT_DISTRIBUTION) {
        CFATAL_LOG("feature_id(%lu) != _feature_id(%lu) or invalid feature_type(%d), logid(%lu)",
                fea.feature_id(), _feature_id, fea.feature_type(), fea.log_id());
        return false;
    }
    return true;
}

bool CountDistributionFeatureAccumulator::_set_feature(
        const DistributionLimitNode* node, uint32_t idx, FeatureValueProto* fea) const {
    fea->set_bucket_idx(idx);
    if (node == NULL) {
        for (uint32_t i = 0; i < _intervals.size(); ++i) {
            FeatureValueProto::BucketProto* bucket = fea->add_buckets();
            bucket->set_idx(i);
            bucket->set_count(0);
        }
        return true;
    }
    for (uint32_t i = 0; i < _intervals.size(); ++i) {
        FeatureValueProto::BucketProto* bucket = fea->add_buckets();
        bucket->set_idx(i);
        bucket->set_count(node->buckets[i]);
    }
    if (_distribution_distance.get() != NULL && !_distribution_distance->calculate(fea)) {
        CWARNING_LOG("distribution distance calculate error, feature_id(%lu).",
                fea->feature_id());
        return false;
    }
    return true;
}

bool CountDistributionFeatureAccumulator::_update(
        const FeatureValueProto& fea, const DistributionLimitNode** node, uint32_t* idx) {
    if (!_check_feature(fea)) {
        CFATAL_LOG("call _check_feature fail");
        return false;
    }
    const CountNode* count_res = NULL;
    if (!_count_seg.enter(CountNode(fea.data_view_sign(), fea.coord()), &count_res)
            || count_res == NULL) {
        CWARNING_LOG("enter _count_seg fail, feature_id(%lu), coord(%ld), logid(%lu)",
                _feature_id, fea.coord(), fea.log_id());
        return false;
    }
    uint32_t tmp_idx = 0;
    DistributionLimitNode dist_node;
    dist_node.sign = fea.view_sign();
    dist_node.coord = fea.coord();
    // get idx and deal wheather is endpoint
    if (_get_interval_idx(count_res->cumulant, &tmp_idx)) {
        // erase last bucket: 0 - (cumulant - 1)
        (dist_node.buckets)[tmp_idx - 1] = (1 - count_res->cumulant);
        (dist_node.buckets)[tmp_idx] = count_res->cumulant;
    } else {
        (dist_node.buckets)[tmp_idx] = 1;
    }
    if (!_dist_seg.enter(dist_node, node)) {
        CWARNING_LOG("enter _dist_seg fail, feature_id(%lu), coord(%ld), logid(%lu)",
                _feature_id, fea.coord(), fea.log_id());
        return false;
    }

    if (idx != NULL) {
        *idx = tmp_idx;
    }
    return true;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti
/* vim: set ts=4 sw=4: */

