// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: feature_accumulator_base.h
// @Last modified: 2017-12-27 09:18:06
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_EXTEND_ACCUMULATOR_BASE_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_EXTEND_ACCUMULATOR_BASE_H

#include <unordered_set>
#include "fvalue_rpc_manager.h"
#include <lru_window.hpp>
#include <utils.h>
#include "feature_accumulator_interface.h"

namespace anti {
namespace themis {
namespace feature_lib{

template<typename FeatureValueType>
struct GroupNode;

template<typename Node>
class ExtendAccumulatorBase : public FeatureAccumulatorInterface {
public:
    ExtendAccumulatorBase() :
            _feature_id(0U),
            _version(0U) {}
    virtual ~ExtendAccumulatorBase() {}
    virtual uint64_t feature_id() const {
        return _feature_id;
    }
    virtual bool init(const comcfg::ConfigUnit& conf);
    virtual bool init(
            const comcfg::ConfigUnit& conf, 
            const std::shared_ptr<FValueRPCManager>& rpc_mgr) {
        if (!rpc_mgr) {
            CFATAL_LOG("input FValueRPCManager prt is NULL");
            return false;
        }
        _rpc_mgr = rpc_mgr;
        return init(conf);
    }
  
    virtual void uninit() { 
        _window.reset();
        _uninit(); 
    }

    virtual bool load(PosixIoReaderInterface *reader);
    virtual bool dump(PosixIoWriterInterface *writer) const;

    virtual bool update(const FeatureValueProto& fea) = 0;
    virtual bool update_and_query(FeatureValueProto* fea) = 0;
    virtual bool query(FeatureValueProto* fea) const = 0;
    virtual void sync(std::vector<std::shared_ptr<FeatureValueProto>>* feas) = 0;

protected:
    typedef anti::themis::common_lib::ThreadSafeLRUWindow<uint64_t, Node> ExtendWindow;
    typedef std::shared_ptr<ExtendWindow> ExtendWindowPtr;
    ExtendWindowPtr _window;
    struct WindowConf {
        int64_t window_length;
        int64_t max_item;
        uint64_t remain;
        int64_t expiration;
    };
    WindowConf _window_conf;
    mutable std::shared_ptr<FValueRPCManager> _rpc_mgr;

private:
    virtual bool _init(const comcfg::ConfigUnit& conf) = 0;
    virtual void _uninit() { return; }

private:
    bool _init_window_conf(const comcfg::ConfigUnit& conf);

    uint64_t _feature_id;
    uint64_t _version;
    enum AccumulateType{
        ACCUMULATE = 0,
        EXTEND = 1
    };
};

template<typename ValueType>
class GroupNode {
public:
    GroupNode(
            uint64_t view,
            int64_t coord) :
            _view(view), 
            _coord(coord),
            _latest_extend(time(NULL)),
            _value(NULL) {}
    GroupNode(const GroupNode& node) {
        _view = node._view;
        _coord = node._coord;
        _latest_extend = node._latest_extend;
        _value = node._value;
    }

    GroupNode() : GroupNode(0, 0) {}
    ~GroupNode() {}

    uint64_t key() const { return _view; }
    int64_t coord() const { return _coord; }

    template<typename Archive>
    bool serialize(Archive* ar) {
        uint32_t has_value = _value ? 1 : 0;
        if (!anti::themis::common_lib::t_write(_view, ar) 
                || !anti::themis::common_lib::t_write(_coord, ar)
                || !anti::themis::common_lib::t_write(_latest_extend, ar)  
                || !anti::themis::common_lib::t_write(has_value, ar)) {
            return false;
        }
        return has_value ? _value->serialize(ar) : true;
    }   

    template<typename Archive>
    bool deserialize(Archive* ar) {
        uint32_t has_value = 0;
        if (!anti::themis::common_lib::t_read(&_view, ar) 
                || !anti::themis::common_lib::t_read(&_coord, ar)
                || !anti::themis::common_lib::t_read(&_latest_extend, ar)
                || !anti::themis::common_lib::t_read(&has_value, ar)) {
            return false;
        }
        
        if (!has_value) {
            return true;
        }
        _value.reset(new (std::nothrow) ValueType);
        if (!_value) {
            CFATAL_LOG("new ValueType obj fail");
            return false;
        }
        return _value->deserialize(ar);
    }   

    typedef std::shared_ptr<ValueType> ValueTypePtr;
    void set_value(const ValueTypePtr& val) {
        _value = val;
    }   
    const ValueTypePtr& value() const {
        return _value;
    }   
    void set_latest_extend(int64_t ltime) {
        _latest_extend = ltime;
    }
    int64_t latest_extend() const {
        return _latest_extend;
    }

private:
    uint64_t _view;
    int64_t _coord;
    int64_t _latest_extend;
    ValueTypePtr _value;
};

} // namespace feature_lib
} // namespace themis 
} // namespace anti

#include "extend_accumulator_base.hpp"

#endif // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_EXTEND_ACCUMULATOR_BASE_H
/* vim: set ts=4 sw=4: */

