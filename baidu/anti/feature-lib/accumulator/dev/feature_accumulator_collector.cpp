// Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
// @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
//
// @File: feature_accumulator_collector.cpp
// @Last modified: 2017-10-26 17:07:24
// @Brief:

#include <set>
#include <algorithm>
#include <gflags/gflags.h>
#include <boost/algorithm/string.hpp>
#include <com_log.h>
#include "feature_accumulator_manager.h"
#include "feature_accumulator_collector.h"
#include "feature_util.h"

namespace anti {
namespace themis {
namespace feature_lib {

DECLARE_int32(monitor_period_minutes);
DECLARE_bool(enable_feature_monitor);

typedef anti::themis::RecordType RecordType;

bool FeatureAccumulatorCollector::init(
        const std::string& path,
        const std::string& file) {
    comcfg::Configure conf;
    if (conf.load(path.data(), file.data()) != 0) {
        CFATAL_LOG("load %s/%s failed", path.data(), file.data());
        return false;
    }
    try {
        RecordType record_type;
        if (conf["source"].size() == 0) {
            //if source size is zero, log_col should by get only one type record
            //with any record_type.
            _manager = _init_conf(path, file, record_type);
            if (_manager == NULL) {
                CWARNING_LOG("_init_conf fail");
                return false;
            }
        }

        for (uint32_t source_id = 0; source_id < conf["source"].size(); ++source_id) {
            const auto& src_conf = conf["source"][source_id];
            std::string log_type(src_conf["log_type"].to_cstr());
            std::string conf_path(src_conf["feature_accumulator_path"].to_cstr());
            std::string conf_file(src_conf["feature_accumulator_file"].to_cstr());
            if (!RecordType_Parse(log_type, &record_type)) {
                CWARNING_LOG("get type fail:%s", log_type.c_str());
                return false;
            }

            FeatureAccumulatorManagerPtr mgr_ptr = _init_conf(conf_path, conf_file, record_type);
            if (mgr_ptr == NULL) {
                CWARNING_LOG("_init_conf multi fail %s error", log_type.c_str());
                return false;
            }

            _manager_map.emplace(record_type, mgr_ptr);
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("comcfg::ConfigException : %s", e.what());
        return false;
    }

    CWARNING_LOG("init feature accumulator collector success");
    return true;
}

FeatureAccumulatorManagerPtr FeatureAccumulatorCollector::_init_conf(const std::string& path,
        const std::string& file,
        RecordType record_type) {
    auto iter = _manager_map.find(record_type);
    if (iter != _manager_map.end()) {
        CWARNING_LOG("record type [%d] already initialized, please check config");
        return std::shared_ptr<FeatureAccumulatorManager>();
    }

    FeatureAccumulatorManagerPtr mgr_ptr(new (std::nothrow) FeatureAccumulatorManager());
    if (mgr_ptr == NULL) {
        CWARNING_LOG("manager allcate [%d] error", record_type);
        return std::shared_ptr<FeatureAccumulatorManager>();
    }

    if (!mgr_ptr->init(path, file)) {
        CWARNING_LOG("mgr init [%d] error", record_type);
        return std::shared_ptr<FeatureAccumulatorManager>();
    }

    return mgr_ptr;
}

FeatureAccumulatorManagerPtr FeatureAccumulatorCollector::_get_fea_mgr(
        RecordType record_type) {
    if (_manager_map.size() == 0) {
        return _manager;
    }
    auto iter = _manager_map.find(record_type);
    if (iter != _manager_map.end()) {
        return iter->second;
    }
    //CWARNING_LOG("record_type:[%d] do not manager get instance", record_type);
    return std::shared_ptr<FeatureAccumulatorManager>();
}

const FeatureAccumulatorManagerPtr FeatureAccumulatorCollector::_const_fea_mgr(
        anti::themis::RecordType record_type) const {
    if (_manager_map.size() == 0) {
        return _manager;
    }
    auto iter = _manager_map.find(record_type);
    if (iter != _manager_map.end()) {
        return iter->second;
    }
    CWARNING_LOG("record_type:[%d] do not manager get instance", record_type);
    return std::shared_ptr<FeatureAccumulatorManager>();
}

bool FeatureAccumulatorCollector::reload() {
    // 检查是否需要运行监控
    if (_should_run_monitor(_last_monitor_time)) {
        _periodic_monitor();
    }

    if (_manager_map.size() == 0) {
        return _manager.get() != NULL && _manager->reload();
    }

    bool ret = true;
    for (auto iter = _manager_map.begin(); iter != _manager_map.end(); iter++) {
        FeatureAccumulatorManagerPtr ptr(iter->second);
        if (!ptr->reload()) {
            CWARNING_LOG("reload type [%d] error", iter->first);
            ret = false;
        }
    }
    return ret;
}

bool FeatureAccumulatorCollector::load(const std::string& checkpoint_path) {
    if (_manager_map.size() == 0) {
        return _manager->load(checkpoint_path);
    }

    bool ret = true;
    for (auto iter = _manager_map.begin(); iter != _manager_map.end(); iter++) {
        FeatureAccumulatorManagerPtr ptr(iter->second);
        if (!ptr->load(checkpoint_path, iter->first)) {
            CWARNING_LOG("load type [%d] error", iter->first);
            ret = false;
        }
    }
    return ret;
}

bool FeatureAccumulatorCollector::dump(const std::string& checkpoint_path) {
    if (_manager_map.size() == 0) {
        return _manager->dump(checkpoint_path);
    }

    bool ret = true;
    for (auto iter = _manager_map.begin(); iter != _manager_map.end(); iter++) {
        FeatureAccumulatorManagerPtr ptr(iter->second);
        if (!ptr->dump(checkpoint_path, iter->first)) {
            CWARNING_LOG("dump type [%d] error", iter->first);
            ret = false;
        }
    }
    return ret;
}

bool FeatureAccumulatorCollector::update(const FeatureValueProto& fea) {
    if (_manager_map.size() == 0) {
        return _manager->update(fea);
    }

    RecordType record_type = RecordType(fea.record_type());
    FeatureAccumulatorManagerPtr mgr = _get_fea_mgr(record_type);
    if (mgr == NULL) {
        CWARNING_LOG("update fail no [%d] type mgr", record_type);
        return false;
    }
    return mgr->update(fea);
}

bool FeatureAccumulatorCollector::update_and_query(FeatureValueProto* fea) {
    if (_manager_map.size() == 0) {
        return _manager->update_and_query(fea);
    }

    RecordType record_type = RecordType(fea->record_type());
    FeatureAccumulatorManagerPtr mgr = _get_fea_mgr(record_type);
    if (mgr == NULL) {
        CWARNING_LOG("update_and_query fail no [%d] type mgr", record_type);
        return false;
    }
    return mgr->update_and_query(fea);
}

bool FeatureAccumulatorCollector::query(FeatureValueProto* fea) {
    if (_manager_map.size() == 0) {
        return _manager->query(fea);
    }

    RecordType record_type = RecordType(fea->record_type());
    FeatureAccumulatorManagerPtr mgr = _get_fea_mgr(record_type);
    if (mgr == NULL) {
        CWARNING_LOG("query fail no [%d] type mgr", record_type);
        return false;
    }
    return mgr->query(fea);
}

void FeatureAccumulatorCollector::increase_coords(const TaskCoordinate& coordinate) {
    for (int32_t i = 0; i < coordinate.coordinates_size(); ++i) {
        std::vector<std::pair<uint64_t, int64_t>> coords;
        auto& feat_coord = coordinate.coordinates(i);
        coords.emplace_back(feat_coord.feature_id(), feat_coord.pv_coord());

        if (_manager_map.size() == 0) {
            _manager->increase_coords(coords);
        } else {
            RecordType record_type = RecordType(feat_coord.record_type());
            FeatureAccumulatorManagerPtr mgr = _get_fea_mgr(record_type);
            if (mgr == NULL) {
                //CWARNING_LOG("increase coords fail no [%d] type mgr", record_type);
            } else {
                mgr->increase_coords(coords);
            }
        }
    }
}

bool FeatureAccumulatorCollector::query(
        const FValQRequestProto& request,
        FValQResponseProto* response) const {
    if (!response) {
        CFATAL_LOG("input response ptr is NULL");
        return false;
    }
    const FeatureAccumulatorManagerPtr mgr = 
            request.has_record_type() ? _const_fea_mgr(RecordType(request.record_type())) : _manager;
    if (!mgr) {
        CWARNING_LOG("get FeatureAccumulatorManagerPtr fail");
        return false;
    }

    if (!mgr->query(request, response)) {
        CWARNING_LOG("gloable query for feature_id(%lu) fail", request.feature_id());
        return false;
    }
    CDEBUG_LOG("gloable succ. [request:%s][response:%s]", 
            request.ShortDebugString().data(),
            response->ShortDebugString().data());
    return true;
}

void FeatureAccumulatorCollector::sync(
        std::vector<std::shared_ptr<FeatureValueProto>>* feas) {
    for (auto& fea_mgr : _manager_map) {
        if (fea_mgr.second) {
            fea_mgr.second->sync(feas);
        }
    }
    if (_manager) {
        _manager->sync(feas);
    }
}

void FeatureAccumulatorCollector::set_version_id(const std::string& version_id) {
    for (auto& fea_mgr : _manager_map) {
        if (fea_mgr.second) {
            fea_mgr.second->set_version_id(version_id);
        }
    }
    if (_manager) {
        _manager->set_version_id(version_id);
    }
}

bool FeatureAccumulatorCollector::get_record_types(std::vector<std::string>* record_types) {
    if (record_types == NULL) {
        CFATAL_LOG("input vector is null");
        return false;
    }
    for (auto& fea_mgr : _manager_map) {
        if (fea_mgr.second) {
            CDEBUG_LOG("get record type from collector, [%s]", RecordType_Name(fea_mgr.first).c_str());
            record_types->emplace_back(RecordType_Name(fea_mgr.first));
        }
    }
    return true;
}

void FeatureAccumulatorCollector::_periodic_monitor() const {
    if (!FLAGS_enable_feature_monitor) {
        return;
    }

    time_t now = time(nullptr);
    std::string current_time = _get_readable_time(now);

    CNOTICE_LOG("=== FeatureAccumulatorCollector Monitor === Time: %s", current_time.c_str());

    if (_manager_map.size() == 0 && _manager) {
        // 单manager模式
        CNOTICE_LOG("Manager [SINGLE]:");
        auto accs = _manager->get_accs();
        for (const auto& acc : accs) {
            if (acc) {
                acc->print_monitor_log();
            }
        }
    } else {
        // 多manager模式
        for (const auto& pair : _manager_map) {
            const anti::themis::RecordType& record_type = pair.first;
            auto manager = pair.second;

            if (!manager)
                continue;

            std::string record_type_name = anti::themis::RecordType_Name(record_type);
            CNOTICE_LOG("Manager [%s]:", record_type_name.c_str());

            auto accs = manager->get_accs();
            for (const auto& acc : accs) {
                if (acc) {
                    acc->print_monitor_log();
                }
            }
        }
    }

    CNOTICE_LOG("=== End FeatureAccumulatorCollector Monitor ===");
}

std::string FeatureAccumulatorCollector::_get_readable_time(time_t timestamp) const {
    struct tm* timeinfo = localtime(&timestamp);
    
    if (timeinfo == nullptr) {
        CWARNING_LOG("localtime failed for timestamp: %ld", timestamp);
        return "INVALID_TIME";
    }
    
    char buffer[80];
    strftime(buffer, sizeof(buffer), "%Y-%m-%d %H:%M:%S", timeinfo);
    return std::string(buffer);
}

bool FeatureAccumulatorCollector::_should_run_monitor(time_t& last_monitor_time) const {
    if (!FLAGS_enable_feature_monitor || FLAGS_monitor_period_minutes <= 0) {
        return false;
    }

    time_t now = time(nullptr);
    time_t period_seconds = FLAGS_monitor_period_minutes * 60;

    if (last_monitor_time == 0 || (now - last_monitor_time) >= period_seconds) {
        last_monitor_time = now;
        return true;
    }

    return false;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti