// Copyright 2021 Baidu Inc. All Right Reserved.
// Author longfei04 (<EMAIL>)

#include <stdio.h>
#include <boost/lexical_cast.hpp>
#include <gflags/gflags.h>
#include "boost/lexical_cast.hpp"
#include "intention_feature_accumulator.h"

namespace anti {
namespace themis {
namespace feature_lib {

bool IntentionFeatureAccumulator::init(const comcfg::ConfigUnit& conf) {
    try {
        _feature_id = conf["feature_id"].to_uint64();
        _version = conf["version"].to_uint64();
        _segment_len = conf["window_length"].to_int64();
        _session_len = conf["session_len"].to_uint64();
        _threshold = conf["similar_threshold"].to_float();
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    }
    if (_segment_len <= 0L) {
        CFATAL_LOG("invalid window_len(%ld)", _segment_len);
        return false;
    }
    if (!_session_segment.init(_segment_len, _session_len)) {
        CFATAL_LOG("fail init session segment!");
        return false;
    }

    CDEBUG_LOG("init IntentionFeatureAccumulator(%lu) success", _feature_id);
    return true;
}

void IntentionFeatureAccumulator::uninit() {
    _feature_id = 0LU;
    _version = 0LU;
    _segment_len = 0L;
    _session_len = 0L;
    _session_segment.uninit();
}

bool IntentionFeatureAccumulator::update(
        const FeatureValueProto& fea) {
    if (!_check_feature(fea)) {
        CFATAL_LOG("call _check_feature fail, feature_id:%lu, logid:%lu", 
                _feature_id, fea.log_id());
        return false;
    }
    IntentionNode node(fea.view_sign(), fea.coord(), fea.intention_data_view().query(),
                           fea.intention_data_view().has_extend_view_value() ?
                                   fea.intention_data_view().extend_view_value() : -1,
                           _threshold);
    std::lock_guard<std::mutex> locker(_mutex);
    if (!_session_segment.enter(node, NULL)) {
        CFATAL_LOG("fail to insert node");
        return false;
    }
    CDEBUG_LOG("update succ, feature_type:%d", fea.feature_type());
    return true;
}

bool IntentionFeatureAccumulator::update_and_query(FeatureValueProto* fea) {
    if (!fea || !_check_feature(*fea)) {
        CFATAL_LOG("check feature fail");
        return false;
    }
    if (!update(*fea)) {
        CWARNING_LOG("fail to update feature.");
        fea->set_valid(false);
        return true;
    }

    if (!query(fea)) {
        CWARNING_LOG("fail to query feature.");
        fea->set_valid(false);
        return true;
    }
    CDEBUG_LOG("update_and_query succ, valid:%d, ret value:%s, feature_type:%d",
            fea->valid(), fea->value().c_str(), fea->feature_type());
    return true;
}

bool IntentionFeatureAccumulator::query(FeatureValueProto* fea) const {
    if (fea == NULL || !_check_feature(*fea)) {
        CFATAL_LOG("input fea == NULL or check feature fail");
        return false;
    }

    std::lock_guard<std::mutex> locker(_mutex);
    const IntentionNode* res_node = _session_segment.query_segment(fea->view_sign());
    if (!res_node) {
        CWARNING_LOG("can't find node, node views:%d", fea->view_sign());
        fea->set_valid(false);
        return true;
    }
    if (!_fillup_feature(res_node, fea)) {
        CFATAL_LOG("fail to set result to FeatureValueProto.");
        return false;
    }

    CDEBUG_LOG("query ret value:%s, feature_type:%d", fea->value().c_str(), fea->feature_type());
    return true;
}

bool IntentionFeatureAccumulator::_check_feature(
        const FeatureValueProto& fea) const {
    if (fea.feature_id() != _feature_id
            || (fea.feature_type() != FeatureValueProto::INTENTION)) {
        CFATAL_LOG("feature_id(%lu) != _feature_id(%lu) || invalid feature_type(%d), logid(%lu)", 
                fea.feature_id(), _feature_id, fea.feature_type(), fea.log_id());
        return false;
    }
    if (!fea.has_intention_data_view()) {
        CFATAL_LOG("feature value proto not have intention_data_view field!");
        return false;
    }
    return true;
}

bool IntentionFeatureAccumulator::load(PosixIoReaderInterface *reader) {
    if (!reader) {
        CFATAL_LOG("writer ptr invalid, feature_id(%lu)", _feature_id);
        return false;
    }
    uint64_t ver = 0LU;
    if (reader->read(&ver, sizeof(ver)) != static_cast<int>(sizeof(ver))) {
        CWARNING_LOG("load version fail, feature_id(%lu)", _feature_id);
        return false;
    }
    // version > checkpoint version return true directly
    if (ver < _version) {
        CWARNING_LOG("feature version in conf > checkpoint version, return true directly");
        return true;
    }
    
    std::lock_guard<std::mutex> locker(_mutex);
    if (!_session_segment.deserialize_no_pod_data(reader)) {
        CWARNING_LOG("load window fail, feature_id(%lu)", _feature_id);
        return false;
    }
    return true;
}

bool IntentionFeatureAccumulator::dump(PosixIoWriterInterface *writer) const {
    if (!writer) {
        CFATAL_LOG("writer ptr invalid, feature_id(%lu)", _feature_id);
        return false;
    }

    if (writer->write(&_version, sizeof(_version)) 
                != static_cast<int>(sizeof(_version))) {
        CWARNING_LOG("write version(%lu) fail, feature_id(%lu)", 
                _version, _feature_id);
        return false;
    }
    
    std::lock_guard<std::mutex> locker(_mutex);
    if (!_session_segment.serialize_no_pod_data(writer)) {
        CWARNING_LOG("dump window fail, feature_id(%lu)", _feature_id);
        return false;
    }
    return true;
}

bool IntentionFeatureAccumulator::_fillup_feature(const IntentionNode* node,
        FeatureValueProto* fea) const {
    fea->set_valid(true);
    fea->set_value(std::to_string(node->value()));
    auto intention = fea->mutable_intention_data_view();
    IntentionList intention_list = *(node->intentions());
    for (const auto& row : intention_list) {
        for (const auto& unit : *row) {
            intention->add_history_query(unit->query);
        }
    }
    return true;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

