// Copyright 2016 Baidu Inc. All Right Reserved.
// Author <PERSON> (<EMAIL>)
// 
// brief: 

#include "distinct_feature_accumulator.h"
#include "boost/lexical_cast.hpp"

namespace anti {
namespace themis {
namespace feature_lib {

DECLARE_bool(need_set_distinct_data_view_signs);
DECLARE_int32(max_distinct_data_view_signs_num);

bool DistinctFeatureAccumulator::init(const comcfg::ConfigUnit& conf) {
    int64_t window_length = 0L;
    try {
        _feature_id = conf["feature_id"].to_uint64();
        _version = conf["version"].to_uint64();
        window_length = conf["window_length"].to_int64();
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    }

    const int DEFAULT_STEP_NUM = 4;
    long long step_length = 0L;
    if (conf["step_length"].get_int64(&step_length, window_length / DEFAULT_STEP_NUM) != 0) {
        CWARNING_LOG("feature_id(%lu) no step_length, using %ld", 
                _feature_id, window_length / DEFAULT_STEP_NUM);
    }

    if (window_length <= 0L 
            || step_length <= 0L 
            || window_length % step_length != 0) {
        CFATAL_LOG("invalid window_length(%ld) step_length(%ld)", 
                window_length, step_length);
        return false;
    }

    const int CACHE_STEP_NUM = 1;
    int max_step_num = CACHE_STEP_NUM + window_length / step_length;
    if (!_window.init(step_length, max_step_num, window_length)) {
        CWARNING_LOG("call window init(%ld, %ld, %ld) fail",
                step_length, max_step_num, window_length);
        return false;
    }

    CWARNING_LOG("init DistinctFeatureAccumulator(%lu) success", _feature_id);
    return true;
}

void DistinctFeatureAccumulator::uninit() {
    _window.uninit();
    _feature_id = 0LU;
    _version = 0LU;
}

bool DistinctFeatureAccumulator::update(
        const FeatureValueProto& fea) {
    if (!_check_feature(fea)) {
        CFATAL_LOG("call _check_feature fail");
        return false;
    }
    DistinctItem item(fea.data_view_sign(), 1L);
    std::lock_guard<std::mutex> locker(_mutex);
    return _window.enter(fea.view_sign(), fea.coord(), item);
}

bool DistinctFeatureAccumulator::update_and_query(FeatureValueProto* fea) {
    if (fea == NULL || !_check_feature(*fea)) {
        CFATAL_LOG("input fea == NULL or call _check_feature fail");
        return false;
    }

    DistinctItem item(fea->data_view_sign(), 1L);
    const DistinctItem* res = NULL;
    std::lock_guard<std::mutex> locker(_mutex);
    if (!_window.enter(fea->view_sign(), fea->coord(), item, &res)) {
        CWARNING_LOG("enter sliding window fail");
        return false;
    }

    return _fillup_feature(res, fea);
}

bool DistinctFeatureAccumulator::query(FeatureValueProto* fea) const {
    if (fea == NULL || !_check_feature(*fea)) {
        CFATAL_LOG("input fea == NULL or call _check_feature fail");
        return false;
    }
    std::lock_guard<std::mutex> locker(_mutex);
    const DistinctItem* item = _window.query_segment(fea->view_sign(), fea->coord());
    return item == NULL ? false : _fillup_feature(item, fea);
}

bool DistinctFeatureAccumulator::_check_feature(
        const FeatureValueProto& fea) const {
    if (fea.feature_id() != _feature_id
            || fea.feature_type() != FeatureValueProto::DISTINCT) {
        CFATAL_LOG("feature_id(%lu) != _feature_id(%lu) || invalid feature_type(%d), logid(%lu)", 
                fea.feature_id(), _feature_id, fea.feature_type(), fea.log_id());
        return false;
    }
    return true;
}

bool DistinctFeatureAccumulator::_fillup_feature(
        const DistinctItem* item,
        FeatureValueProto* fea) const {
    fea->set_valid(true);
    if (item == NULL) {
        fea->set_value("0");
        return true;
    }
    int64_t distinct_num = item->distinct_num();
    fea->set_value(boost::lexical_cast<std::string>(distinct_num));
    if (!FLAGS_need_set_distinct_data_view_signs) {
        return true;
    }
    uint32_t count = 0;
    fea->clear_data_view_signs();
    for (auto iter = item->map().begin();
            iter != item->map().end() 
            && count < FLAGS_max_distinct_data_view_signs_num; 
            ++iter) {
        fea->add_data_view_signs(std::to_string(iter->first));
        ++count;
    }
    return true;
}

bool DistinctFeatureAccumulator::load(PosixIoReaderInterface *reader) {
    uint64_t ver = 0LU;
    if (reader->read(&ver, sizeof(ver)) != static_cast<int>(sizeof(ver))) {
        CWARNING_LOG("load version fail");
        return false;
    }

    // version > checkpoint version return true directly
    if (ver < _version) {
        return true;
    }
    std::lock_guard<std::mutex> locker(_mutex);
    if (!_window.deserialize(reader)) {
        CWARNING_LOG("call window deserialize fail, feature_id(%lu)", _feature_id);
        return false;
    }
    return true;
}

bool DistinctFeatureAccumulator::dump(PosixIoWriterInterface *writer) const {
    if (writer->write(&_version, sizeof(_version)) 
            != static_cast<int>(sizeof(_version))) {
        CWARNING_LOG("write version(%lu) fail, feature_id(%lu)", 
                _version, _feature_id);
        return false;
    }
    std::lock_guard<std::mutex> locker(_mutex);
    if (!_window.serialize(writer)) {
        CWARNING_LOG("call window serialize fail, feature_id(%lu)", _feature_id);
        return false;
    }
    return true;
}

bool DistinctFeatureAccumulator::query(
        const FValQRequestProto& request,
        FValQResponseProto* response) const {
    if (response == NULL || !request.has_view_sign()) {
        CFATAL_LOG("request or response invalid, feture_id(%lu)", _feature_id);
        return false;
    }
    std::lock_guard<std::mutex> locker(_mutex);
    const DistinctItem* item = _window.query_segment(request.view_sign());
    if (!item) {
        CDEBUG_LOG("query view_sign(%lu) fail, feature_id(%lu)", request.view_sign(), _feature_id);
        return true;
    }
    if (!request.has_max_value_num()) {
        response->add_value(boost::lexical_cast<std::string>(item->distinct_num()));
        CDEBUG_LOG("query succ, feature_id(%lu), request(%s), response(%s)", 
                _feature_id, request.ShortDebugString().data(), response->ShortDebugString().data());
    } else {
        uint32_t count = 0;
        for (auto iter = item->map().begin(); 
                iter != item->map().end() && count < request.max_value_num(); 
                ++iter) {
            response->add_value(std::to_string(iter->first));
            ++count;
        }
    }
    return true;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti
