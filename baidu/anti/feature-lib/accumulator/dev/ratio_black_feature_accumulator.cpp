// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: ratio_black_feature_accumulator.cpp
// @Last modified: 2017-03-16 14:09:50
// @Brief: 

#include "boost/lexical_cast.hpp"
#include "ratio_black_feature_accumulator.h"

namespace anti {
namespace themis {
namespace feature_lib {

bool RatioBlackFeatureAccumulator::init(const comcfg::ConfigUnit& conf) {
    int64_t window_length = 0L;
    try {
        _feature_id = conf["feature_id"].to_uint64();
        _version = conf["version"].to_uint64();
        window_length = conf["window_length"].to_int64();
        _remain = conf["remain"].to_int64();
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    }

    if (!_seg.init(window_length)) {
        CWARNING_LOG("call click segment init fail, seglen=%ld", window_length);
        return false;
    }

    CWARNING_LOG("init RatioBlackFeatureAccumulator(%lu) success", _feature_id);
    return true;
}

void RatioBlackFeatureAccumulator::uninit() {
    _seg.uninit();
    _feature_id = 0LU;
    _version = 0LU;
    _remain = 0L; 
}

bool RatioBlackFeatureAccumulator::update(const FeatureValueProto& fea) {
    if (!_check_feature(fea)) {
        CFATAL_LOG("call check feature fail");
        return false;
    }

    int64_t filter = fea.in_filter() ? 1L : 0L;
    int64_t refer = fea.in_refer() ? 1L : 0L;
    if (!_seg.enter(RatioBlackNode(fea.view_sign(), fea.coord(), filter, refer), NULL)) {
        CWARNING_LOG("call click segment enter fail, feature_id[%lu], coord(%ld)",
                _feature_id, fea.coord());
        return false;
    }
    return true;
}

bool RatioBlackFeatureAccumulator::update_and_query(FeatureValueProto* fea) {
    if (fea == NULL || !_check_feature(*fea)) {
        CFATAL_LOG("input fea == NULL or call _check_feature fail");
        return false;
    }

    int64_t filter = fea->in_filter() ? 1L : 0L;
    int64_t refer = fea->in_refer() ? 1L : 0L;

    const RatioBlackNode* res = NULL;
    if (!_seg.enter(RatioBlackNode(fea->view_sign(), fea->coord(), filter, refer), &res)) {
        CWARNING_LOG("call click segment enter fail, feature_id[%lu], coord(%ld)",
                _feature_id, fea->coord());
        return false;
    }
    return _set_feature(res, fea);
}

bool RatioBlackFeatureAccumulator::query(FeatureValueProto* fea) const {
    if (fea == NULL || !_check_feature(*fea)) {
        CFATAL_LOG("input fea == NULL or call _check_feature fail");
        return false;
    }
    const RatioBlackNode* res = _seg.query_segment(fea->view_sign(), fea->coord());
    return res == NULL ? false : _set_feature(res, fea);
}

bool RatioBlackFeatureAccumulator::_check_feature(const FeatureValueProto& fea) const {
    if (fea.feature_id() != _feature_id
            || fea.feature_type() != FeatureValueProto::AUTO_RATIO) {
        CFATAL_LOG("feature_id(%lu) != _feature_id(%lu) || invalid feature_type(%d), logid(%lu)", 
                fea.feature_id(), _feature_id, fea.feature_type(), fea.log_id());
        return false;
    }

    if (!fea.in_filter() && !fea.in_refer()) {
        CWARNING_LOG("neither in filter nor in refer, feature_id(%lu), logid(%lu)",
                _feature_id, fea.log_id());
        return false;
    }
    return true;
}

bool RatioBlackFeatureAccumulator::_set_feature(const RatioBlackNode* node, 
        FeatureValueProto* fea) const {
    if (node == NULL) {
        fea->set_filter_count(0);
        fea->set_refer_count(0);
        fea->set_last_fit_count(0);
        fea->set_valid(false);
        fea->set_last_ratio(0);
        fea->set_value("0");
        return true;
    }
    fea->set_filter_count(node->fit_cumulant);
    fea->set_refer_count(node->ref_cumulant);
    fea->set_last_fit_count(node->last_fit_cumulant);
    fea->set_valid(fea->last_fit_count() >= _remain);
    fea->set_last_ratio(node->last_ratio);
    fea->set_value(boost::lexical_cast<std::string>(node->last_ratio));
    return true;
}

bool RatioBlackFeatureAccumulator::load(PosixIoReaderInterface *reader) {
    uint64_t ver = 0LU;
    if (reader->read(&ver, sizeof(ver)) != static_cast<int>(sizeof(ver))) {
        CWARNING_LOG("load version fail");
        return false;
    }

    // version > checkpoint version return true directly
    if (ver < _version) {
        return true;
    }

    if (!_seg.deserialize(reader)) {
        CWARNING_LOG("call segment deserialize fail, feature_id(%lu)", _feature_id);
        return false;
    }
    return true;
}

bool RatioBlackFeatureAccumulator::dump(PosixIoWriterInterface *writer) const {
    if (writer->write(&_version, sizeof(_version)) 
            != static_cast<int>(sizeof(_version))) {
        CWARNING_LOG("write version(%lu) fail, feature_id(%lu)", 
                _version, _feature_id);
        return false;
    }
  
    if (!_seg.serialize(writer)) {
        CWARNING_LOG("call segment serialize fail, feature_id(%lu)", _feature_id);
        return false;
    }
    return true; 
}

} // feature_lib
} // themis
} // namespace anti

/* vim: set ts=4 sw=4: */

