// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangji<PERSON><PERSON>(<EMAIL>)
//
// @File: feature_accumulator_factory.cpp
// @Last modified: 2018-05-04 16:21:14
// @Brief:

#include "feature_accumulator_factory.h"
#include <com_log.h>
#include "segment_feature_accumulator.h"
#include "ratio_feature_accumulator.h"
#include "distribution_feature_accumulator.h"
#include "basic_feature_accumulator.h"
#include "ratio_black_feature_accumulator.h"
#include "timediff_feature_accumulator.h"
#include "segment_black_feature_accumulator.h"
#include "multi_segment_feature_accumulator.h"
#include "count_distribution_feature_accumulator.h"
#include "concentration_feature_accumulator.h"
#include "hijack_feature_accumulator.h"
#include "acp_feature_accumulator.h"
#include "distinct_feature_accumulator.h"
#include "distinct_feature_accumulator_v2.h"
#include "distinct_distribution_feature_accumulator.h"
#include "fea_deviation_feature_accumulator.h"
#include "rate_feature_accumulator.h"
#include "rate_distribution_feature_accumulator.h"
#include "fea_value_dis_accumulator.h"
#include "fea_value_rate_accumulator.h"
#include "fea_value_cdf_accumulator.h"
#include "three_level_tree_feature_accumulator.h"
#include "lru_node_feature_accumulator.hpp"
#include "intention_feature_accumulator.h"
#include "dataview_timediff_feature_accumulator.h"

namespace anti {
namespace themis {
namespace feature_lib {

DECLARE_bool(enable_fea_service_acc);
DECLARE_bool(enable_mem_pool_for_acc);

const std::unordered_set<std::string> FeatureAccumulatorFactory::FVALUE_QUERY_TYPE_SET = 
        {"fea_value_rate", "fea_value_distribution"};
const std::unordered_set<std::string> FeatureAccumulatorFactory::BASE_ACCUMULATOR_TYPE_SET =
        {"file_dict", "carespace", "original", "click_hijack_refer", "click_hijack_session",
           "click_hijack_dup_traffic", "click_hijack_resend", "click_hijack_direct", "click_black",
           "demeter_hijack_refer", "demeter_hijack_session", "demeter_hijack_resend", "multi_value",
           "demeter_hijack_dup_traffic", "demeter_hijack_direct", "demeter_extend_prob"
        };

FeatureAccumulatorInterface* FeatureAccumulatorFactory::create(const std::string& type) {
    FeatureAccumulatorInterface* obj = NULL;
    if (!FLAGS_enable_fea_service_acc && FVALUE_QUERY_TYPE_SET.count(type) > 0 || 
            BASE_ACCUMULATOR_TYPE_SET.count(type) > 0) {
        obj = new(std::nothrow) BasicFeatureAccumulator();
    } else if (type == "segment" || type == "behavior_segment") {
        obj = new(std::nothrow) SegmentFeatureAccumulator();
    } else if (type == "sum_segment") {
        obj = new(std::nothrow) SegmentFeatureAccumulator(true);
    } else if (type == "ratio" || type == "behavior_ratio") {
        obj = new(std::nothrow) RatioFeatureAccumulator();
    } else if (type == "distribution" || type == "behavior_distribution") {
        obj = new(std::nothrow) DistributionFeatureAccumulator();
    } else if (type == "auto_ratio") {
        obj = new(std::nothrow) RatioBlackFeatureAccumulator();
    } else if (type == "timediff") {
        obj = new(std::nothrow) TDAccumulator();
    } else if (type == "dataview_timediff") {
        obj = new(std::nothrow) DataViewTDAccumulator();
    } else if (type == "auto_segment") {
        obj = new(std::nothrow) SegmentBlackFeatureAccumulator();
    } else if (type == "multi_seg") {
        obj = new(std::nothrow) MultiSegmentAccumulator();
    } else if (type == "count_distribution") {
        obj = new(std::nothrow) CountDistributionFeatureAccumulator();
    } else if (type == "concentration") {
        obj = new(std::nothrow) ConcentrationFeatureAccumulator();
    } else if (type == "hijack") {
        obj = new(std::nothrow) HijackAccumulator();
    } else if (type == "acp") {
        obj = new(std::nothrow) AcpFeatureAccumulator();
    } else if (type == "distinct") {
        if (FLAGS_enable_mem_pool_for_acc) {
            obj = new(std::nothrow) DistinctFeatureAccumulatorV2();
        } else {
            obj = new(std::nothrow) DistinctFeatureAccumulator();
        }
    } else if (type == "distinct_distribution") {
        obj = new(std::nothrow) DistinctDistributionFeatureAccumulator();
    } else if (type == "fea_deviation") {
        obj = new(std::nothrow) FeaDeviationFeatureAccumulator();
    } else if (type == "rate" || type == "cpm") {
        obj = new(std::nothrow) RateFeatureAccumulator();
    } else if (type == "rate_distribution" || type == "cpm_distribution") {
        obj = new(std::nothrow) RateDistributionFeatureAccumulator();
    } else if (type == "fea_value_distribution") {
        obj = new(std::nothrow) FeaValueDisAccumulator();
    } else if (type == "fea_value_rate") {
        obj = new(std::nothrow) FeatureValueRateAccumulator();
    } else if (type == "fea_value_cdf") {
        obj = new(std::nothrow) FeatureValueCdfAccumulator();
    } else if (type == "three_level_tree") {
        obj = new(std::nothrow) ThreeLevelTreeFeatureAccumulator();
    } else if (type == "query_similar") {
        CFATAL_LOG("unsupport feature.");
        return NULL;
    } else if (type == "time_interval") {
        obj = new(std::nothrow) LRUNodeFeatureAccumulator<TimeNode>();
    } else if (type == "intention") {
        obj = new(std::nothrow) IntentionFeatureAccumulator();
    } else {
        CWARNING_LOG("invalid type(%s)", type.data());
        return NULL;
    }

    if (obj == NULL) {
        CFATAL_LOG("new type(%s) fail", type.data());
        return NULL;
    }
    return obj;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti