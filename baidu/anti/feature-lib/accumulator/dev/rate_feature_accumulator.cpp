// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangji<PERSON><PERSON>(<EMAIL>)
// 
// @File: rate_feature_accumulator.cpp
// @Last modified: 2017-03-16 14:08:46
// @Brief: 

#include "rate_feature_accumulator.h"

namespace anti {
namespace themis {
namespace feature_lib {

bool RateFeatureAccumulator::init(const comcfg::ConfigUnit& conf) {
    int64_t window_length = 0L;
    try {
        _feature_id = conf["feature_id"].to_uint64();
        _version = conf["version"].to_uint64();
        window_length = conf["window_length"].to_int64();
        _time_threshold = conf["time_threshold"].to_int64();
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    }
    const int DEFAULT_STEP_NUM = 4;
    long long step_length = 0L;
    if (conf["step_length"].get_int64(
            &step_length, window_length / DEFAULT_STEP_NUM) != 0) {
        CWARNING_LOG("feature_id(%lu) no step_length, using %ld", 
                _feature_id, window_length / DEFAULT_STEP_NUM);
    }

    if (window_length <= 0L 
            || step_length <= 0L 
            || window_length % step_length != 0) {
        CFATAL_LOG("invalid window_length(%ld) step_length(%ld)", 
                window_length, step_length);
        return false;
    }

    for (uint32_t i = 0U; i < TOTAL; ++i) {
        if (!_windows[i].init(step_length, window_length / step_length + 1, window_length)) {
            CWARNING_LOG("init window(%d) fail, please check conf", i);
            return false;
        }
    }
    char remain[1024] = {'\0'};
    if (conf["remain"].get_cstr(remain, sizeof(remain)) != 0
            || sscanf(remain, "%ld,%ld", &_remain[NUMERATOR], &_remain[DENOMINATOR]) != TOTAL) {
            CWARNING_LOG("invalid remain %s", remain);
            return false;
    }
    CWARNING_LOG("init RateFeatureAccumulator(%lu) success", _feature_id);
    return true;
}

bool RateFeatureAccumulator::update(const FeatureValueProto& fea) {
    if (!_check_feature(fea)) {
        CWARNING_LOG("call _check_feature fail");
        return false;
    }
    return _update(fea);
}

bool RateFeatureAccumulator::_update(const FeatureValueProto& fea) {
    if (fea.in_filter()) {
        int64_t filter = fea.feature_type() != FeatureValueProto::CPM ? 1L : fea.filter_count();
        if (!_windows[NUMERATOR].enter(fea.view_sign(), fea.coord(), SegmentItem(filter))) {
            CWARNING_LOG("call window[nu] enter failed, fea_id(%lu)", _feature_id);
            return false;
        }
    }

    if (fea.in_refer()) {
        if (!_windows[DENOMINATOR].enter(fea.view_sign(), fea.coord(), SegmentItem(1L))) {
            CWARNING_LOG("call windows[de] enter failed, fea_id(%lu)", _feature_id);
            return false;
        }
        
    }
    return true;
}

bool RateFeatureAccumulator::update_and_query(FeatureValueProto* fea) {
    if (fea == NULL || !_check_feature(*fea)) {
        CFATAL_LOG("input fea == NULL or call _check_feature failed");
        return false;
    }

    if (!_update(*fea)) {
        CWARNING_LOG("call _update failed, fea_id(%lu) logid(%lu)", _feature_id, fea->log_id());
        return false;
    }
    return _calculate_feature(fea);
}

bool RateFeatureAccumulator::_calculate_feature(FeatureValueProto* fea) const {
    static const SegmentItem ZERO(0L);
    const SegmentItem* nu = _windows[NUMERATOR].query_segment(fea->view_sign());
    if (nu == NULL) {
        CWARNING_LOG("find view_sign(%lu) in windows[nu] fail, fea_id(%lu)", 
                fea->view_sign(), fea->feature_id());
        nu = &ZERO;
    }

    const SegmentItem* de = _windows[DENOMINATOR].query_segment(fea->view_sign());
    if (de == NULL) {
        CWARNING_LOG("find view_sign(%lu) in windows[de] fail, fea_id(%lu)", 
                fea->view_sign(), fea->feature_id());
        de = &ZERO;
    }

    fea->set_filter_count(nu->cumulant());
    fea->set_refer_count(de->cumulant());
    // vaild = (nu or de >= remain) && (coord diff <= time_threshold)
    bool valid = nu->cumulant() >= _remain[NUMERATOR] || de->cumulant() >= _remain[DENOMINATOR];
    fea->set_valid(valid 
            && llabs(_windows[NUMERATOR].latest_coord() - _windows[DENOMINATOR].latest_coord()) 
                    <= _time_threshold);
    double rate = de->cumulant() != 0 ? 1.0 * nu->cumulant() / de->cumulant() : 1;
    fea->set_value(std::to_string(rate));
    return true;
}

bool RateFeatureAccumulator::query(FeatureValueProto* fea) const {
    if (fea == NULL || !_check_feature(*fea)) {
        CFATAL_LOG("input fea == NULL or call _check_feature failed");
        return false;
    }
    return _calculate_feature(fea);
}

bool RateFeatureAccumulator::_check_feature(const FeatureValueProto& fea) const {
    if (fea.feature_id() != _feature_id
            || (fea.feature_type() != FeatureValueProto::RATE
            && fea.feature_type() != FeatureValueProto::CPM)) {
        CFATAL_LOG("feature_id(%lu) != _feature_id(%lu) || invalid feature_type(%d), logid(%lu)", 
                fea.feature_id(), _feature_id, fea.feature_type(), fea.log_id());
        return false;
    }

    if (!fea.in_filter() && !fea.in_refer()) {
        CWARNING_LOG("neither in filter nor in refer, feature_id(%lu), logid(%lu)",
                _feature_id, fea.log_id());
        return false;
    }

    if (fea.in_filter() 
            && fea.feature_type() == FeatureValueProto::CPM
            && !fea.has_filter_count()) {
        CWARNING_LOG("feature(%lu) has no filter count, logid(%lu)", _feature_id, fea.log_id());
        return false;
    }
    return true;
}

bool RateFeatureAccumulator::load(PosixIoReaderInterface *reader) {
    uint64_t ver = 0LU;
    if (reader->read(&ver, sizeof(ver)) != static_cast<int>(sizeof(ver))) {
        CWARNING_LOG("load version fail");
        return false;
    }

    // version > checkpoint version return true directly
    if (ver < _version) {
        return true;
    }

    for (uint32_t i = 0; i < TOTAL; ++i) {
        if (!_windows[i].deserialize(reader)) {
            CWARNING_LOG("call windows[%u] deserialize fail, feature_id(%lu)", 
                    i, _feature_id);
            return false;
        }
    }
    return true;
}

bool RateFeatureAccumulator::dump(PosixIoWriterInterface *writer) const {
    if (writer->write(&_version, sizeof(_version)) 
            != static_cast<int>(sizeof(_version))) {
        CWARNING_LOG("write version(%lu) fail, feature_id(%lu)", 
                _version, _feature_id);
        return false;
    }
    
    for (uint32_t i = 0; i < TOTAL; ++i) {
        if (!_windows[i].serialize(writer)) {
            CWARNING_LOG("call window[%u] serialize fail, feature_id(%lu)", 
                    i, _feature_id);
            return false;
        }
    }
    return true; 
}

}  // feature_lib
}  // themis
}  // anti

/* vim: set ts=4 sw=4: */

