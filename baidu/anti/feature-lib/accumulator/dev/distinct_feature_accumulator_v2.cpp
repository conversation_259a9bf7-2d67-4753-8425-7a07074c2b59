// Copyright 2025 Baidu Inc. All Right Reserved.
// Author <PERSON> (<EMAIL>)
// 
// brief: V2版本的DistinctFeatureAccumulator实现

#include "distinct_feature_accumulator_v2.h"
#include "boost/lexical_cast.hpp"
#include <algorithm>
#include <exception>

namespace anti {
namespace themis {
namespace feature_lib {


bool DistinctFeatureAccumulatorV2::init(const comcfg::ConfigUnit& conf) {
    int64_t window_length = 0L;
    try {
        _feature_id = conf["feature_id"].to_uint64();
        _version = conf["version"].to_uint64();
        window_length = conf["window_length"].to_int64();
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    }

    const int DEFAULT_STEP_NUM = 4;
    long long step_length = 0L;
    if (conf["step_length"].get_int64(&step_length, window_length / DEFAULT_STEP_NUM) != 0) {
        CWARNING_LOG("feature_id(%lu) no step_length, using %ld", 
                _feature_id, window_length / DEFAULT_STEP_NUM);
    }

    if (window_length <= 0L 
            || step_length <= 0L 
            || window_length % step_length != 0) {
        CFATAL_LOG("invalid window_length(%ld) step_length(%ld)", 
                window_length, step_length);
        return false;
    }

    // 1. 初始化内存池 - 这是V2版本的核心优化
    if (!_init_memory_pool()) {
        CWARNING_LOG("init memory pool failed, feature_id(%lu)", _feature_id);
        return false;
    }

    // 2. 初始化滑动窗口，使用外部内存池模式
    // 与一期不同，内存池现在由FeatureAccumulator管理而非SlidingWindow自己创建
    const int CACHE_STEP_NUM = 1;
    int max_step_num = CACHE_STEP_NUM + window_length / step_length;
    if (!_pool_window.init(step_length, max_step_num, window_length, &_mem_pool)) {
        CWARNING_LOG("call pool window init(%ld, %d, %ld) fail",
                step_length, max_step_num, window_length);
        return false;
    }

    CWARNING_LOG("init DistinctFeatureAccumulatorV2(%lu) success", _feature_id);
    return true;
}

bool DistinctFeatureAccumulatorV2::_init_memory_pool() {
    std::vector<uint32_t> slab_sizes;
    
    // 1. 收集DistinctItemV2需要的slab大小
    DistinctItem::append_required_slab_sizes(slab_sizes);
    
    // 2. 收集SlidingWindowV2需要的slab大小
    SlidingWindow::append_required_slab_sizes(slab_sizes);
    
    // 3. 排序并去重
    std::sort(slab_sizes.begin(), slab_sizes.end());
    slab_sizes.erase(std::unique(slab_sizes.begin(), slab_sizes.end()), slab_sizes.end());
    
    // 4. 创建内存池
    int ret = _mem_pool.create(slab_sizes.data(), 
                              static_cast<uint32_t>(slab_sizes.size()), 
                              FLAGS_pool_mempool_block_item_num);
    if (ret != 0) {
        CWARNING_LOG("create memory pool failed, ret=%d", ret);
        return false;
    }
    
    return true;
}

void DistinctFeatureAccumulatorV2::uninit() {
    _pool_window.uninit();
    _mem_pool.clear();
    _feature_id = 0LU;
    _version = 0LU;
}

bool DistinctFeatureAccumulatorV2::update(
        const FeatureValueProto& fea) {
    if (!_check_feature(fea)) {
        CFATAL_LOG("call _check_feature fail");
        return false;
    }
    DistinctItem item(fea.data_view_sign(), 1L, &_mem_pool);
    std::lock_guard<std::mutex> locker(_mutex);
    return _pool_window.enter(fea.view_sign(), fea.coord(), item);
}

bool DistinctFeatureAccumulatorV2::update_and_query(FeatureValueProto* fea) {
    if (fea == NULL || !_check_feature(*fea)) {
        CFATAL_LOG("input fea == NULL or call _check_feature fail");
        return false;
    }

    DistinctItem item(fea->data_view_sign(), 1L, &_mem_pool);
    const DistinctItem* res = NULL;
    std::lock_guard<std::mutex> locker(_mutex);
    if (!_pool_window.enter(fea->view_sign(), fea->coord(), item, &res)) {
        CWARNING_LOG("enter sliding window fail");
        return false;
    }

    return _fillup_feature(res, fea);
}

bool DistinctFeatureAccumulatorV2::query(FeatureValueProto* fea) const {
    if (fea == NULL || !_check_feature(*fea)) {
        CFATAL_LOG("input fea == NULL or call _check_feature fail");
        return false;
    }
    std::lock_guard<std::mutex> locker(_mutex);
    const DistinctItem* item = _pool_window.query_segment(fea->view_sign(), fea->coord());
    return item == NULL ? false : _fillup_feature(item, fea);
}

bool DistinctFeatureAccumulatorV2::_check_feature(
        const FeatureValueProto& fea) const {
    if (fea.feature_id() != _feature_id
            || fea.feature_type() != FeatureValueProto::DISTINCT) {
        CFATAL_LOG("feature_id(%lu) != _feature_id(%lu) || invalid feature_type(%d), logid(%lu)", 
                fea.feature_id(), _feature_id, fea.feature_type(), fea.log_id());
        return false;
    }
    return true;
}

bool DistinctFeatureAccumulatorV2::_fillup_feature(
        const DistinctItem* item,
        FeatureValueProto* fea) const {
    fea->set_valid(true);
    if (item == NULL) {
        fea->set_value("0");
        return true;
    }
    
    // distinct_num()返回map中不同key的数量
    int64_t distinct_num = item->distinct_num();
    fea->set_value(boost::lexical_cast<std::string>(distinct_num));
    
    if (!FLAGS_need_set_distinct_data_view_signs) {
        return true;
    }
    
    uint32_t count = 0;
    fea->clear_data_view_signs();
    // 使用traverse遍历所有data_view_sign
    // 虽然顺序可能与原版不同，但对于distinct统计来说顺序不影响正确性
    item->map().traverse([&](const uint64_t& key, const int64_t& value) {
        if (count < static_cast<uint32_t>(FLAGS_max_distinct_data_view_signs_num)) {
            fea->add_data_view_signs(std::to_string(key));
            ++count;
        }
    });
    
    return true;
}

bool DistinctFeatureAccumulatorV2::load(PosixIoReaderInterface *reader) {
    uint64_t ver = 0LU;
    if (reader->read(&ver, sizeof(ver)) != static_cast<int>(sizeof(ver))) {
        CWARNING_LOG("load version fail");
        return false;
    }

    // version > checkpoint version return true directly
    if (ver < _version) {
        return true;
    }
    std::lock_guard<std::mutex> locker(_mutex);
    
    // 直接反序列化 V2 的滑动窗口对象（只传 reader）
    if (!_pool_window.deserialize(reader)) {
        CWARNING_LOG("call pool window deserialize fail, feature_id(%lu)", _feature_id);
        return false;
    }
    return true;
}

bool DistinctFeatureAccumulatorV2::dump(PosixIoWriterInterface *writer) const {
    if (writer->write(&_version, sizeof(_version)) 
            != static_cast<int>(sizeof(_version))) {
        CWARNING_LOG("write version(%lu) fail, feature_id(%lu)", 
                _version, _feature_id);
        return false;
    }
    std::lock_guard<std::mutex> locker(_mutex);
    if (!_pool_window.serialize(writer)) {
        CWARNING_LOG("call window serialize fail, feature_id(%lu)", _feature_id);
        return false;
    }
    return true;
}

bool DistinctFeatureAccumulatorV2::query(
        const FValQRequestProto& request,
        FValQResponseProto* response) const {
    if (response == NULL || !request.has_view_sign()) {
        CFATAL_LOG("request or response invalid, feture_id(%lu)", _feature_id);
        return false;
    }
    std::lock_guard<std::mutex> locker(_mutex);
    const DistinctItem* item = _pool_window.query_segment(request.view_sign());
    if (!item) {
        CDEBUG_LOG("query view_sign(%lu) fail, feature_id(%lu)", request.view_sign(), _feature_id);
        return true;
    }
    
    if (!request.has_max_value_num()) {
        response->add_value(boost::lexical_cast<std::string>(item->distinct_num()));
        CDEBUG_LOG("query succ, feature_id(%lu), request(%s), response(%s)", 
                _feature_id, request.ShortDebugString().data(), response->ShortDebugString().data());
    } else {
        uint32_t count = 0;
        // 使用traverse接口遍历map
        // 注意：遍历顺序与 std::unordered_map 不同，但是这里只是遍历查找
        item->map().traverse([&](const uint64_t& key, const int64_t& value) {
            if (count < request.max_value_num()) {
                response->add_value(std::to_string(key));
                ++count;
            }
        });
    }
    return true;
}


void DistinctFeatureAccumulatorV2::print_monitor_log() const {
    try {
        // 1. 从SlidingWindowV2获取元素统计和滑动窗口开销
        bsl::var::Dict window_dict;
        bsl::ResourcePool rp;
        _pool_window.monitor(window_dict, rp);
        
        // 直接访问字段，参考SlidingWindowV2模式
        uint64_t total_elements = window_dict["TOTAL_ELEMENTS"].to_uint64();
        uint64_t window_elements = window_dict["WINDOW_ELEMENTS"].to_uint64();
        uint64_t segment_elements = window_dict["SEGMENT_ELEMENTS"].to_uint64();
        uint64_t sliding_window_overhead = window_dict["SLIDING_WINDOW_OVERHEAD"].to_uint64();
        
        // 2. 从内存池获取数据内存和内存池管理开销，参考SlidingWindowV2模式
        bsl::var::Dict pool_stats;
        _mem_pool.monitor(pool_stats, rp);
        uint64_t data_mem = pool_stats["ACTUAL_MEM_USED"].to_uint64();
        uint64_t pool_mgmt_overhead = pool_stats["POOL_MANAGEMENT_OVERHEAD"].to_uint64();
        
        // 3. 累积器自身开销
        uint64_t accumulator_overhead = sizeof(DistinctFeatureAccumulatorV2);
        
        // 4. 计算总内存使用
        uint64_t total_overhead = pool_mgmt_overhead + sliding_window_overhead + accumulator_overhead;
        uint64_t total_mem = data_mem + total_overhead;
        
        // 5. 计算平均每个元素的内存占用
        double avg_mem_per_element = total_elements > 0 ? 
            static_cast<double>(total_mem) / total_elements : 0.0;
        
        // 6. 打印监控日志（保持与一期相同的格式）
        CNOTICE_LOG("Feature: ID=%lu, Type=POOL_DISTINCT_V2, Elements=%lu (Window:%lu, Segment:%lu), "
                   "DataMem=%luMB, PoolMgmt=%luMB, SlidingWindow=%luMB, Accumulator=%luB, "
                   "TotalMem=%luMB, MemPerElement=%.2fB",
                   _feature_id, total_elements, window_elements, segment_elements,
                   data_mem / (1024 * 1024),
                   pool_mgmt_overhead / (1024 * 1024),
                   sliding_window_overhead / (1024 * 1024),
                   accumulator_overhead,
                   total_mem / (1024 * 1024),
                   avg_mem_per_element);
    } catch (const std::exception& e) {
        CWARNING_LOG("Failed to print monitor log for feature_id(%lu): %s", _feature_id, e.what());
    } catch (...) {
        CWARNING_LOG("Failed to print monitor log for feature_id(%lu): unknown exception", _feature_id);
    }
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti