// Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
// @Author: langwen<PERSON>(<EMAIL>)
// 
// @File: fea_value_cdf_accumulator.h
// @Brief:

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_FEA_VALUE_CDF_ACCUMULATOR_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_FEA_VALUE_CDF_ACCUMULATOR_H

#include <cstdint>
#include <cdf.h>
#include "extend_accumulator_base.h"

namespace anti {
namespace themis {
namespace feature_lib {

class ViewItem {
public:
    ViewItem() : _view_val(INT64_MAX), _appear_count(0) {};
    ~ViewItem() {}
    template<typename Archive>
    bool serialize(Archive* ar) const {
        if (!ar) {
            return false;
        }
        return anti::themis::common_lib::t_write(_view_val, ar)
                && anti::themis::common_lib::t_write(_appear_count, ar);
    }
    template<typename Archive>
    bool deserialize(Archive* ar) {
        if (!ar) {
            return false;
        }
        return anti::themis::common_lib::t_read(&_view_val, ar)
                && anti::themis::common_lib::t_read(&_appear_count, ar);
    }
    int64_t get_view_val() const { return _view_val; }
    int64_t set_view_val(const int64_t view_val) { 
        _view_val = view_val;
        return _view_val;
    }
    void increase_appear_count() { ++_appear_count; }
    void clear_appear_count() { _appear_count = 0; }
    uint32_t get_appear_count() { return _appear_count; }
private:
    // not view sign
    int64_t _view_val;
    uint32_t _appear_count;
};

typedef GroupNode<ViewItem> GroupViewItem;
typedef ExtendAccumulatorBase<GroupViewItem> FeatureValueCdfAccBase;

class FeatureValueCdfAccumulator : public FeatureValueCdfAccBase {
public:
    virtual bool update(const FeatureValueProto& fea) override;
    virtual bool update_and_query(FeatureValueProto* fea) override;
    virtual bool query(FeatureValueProto* fea) const override; 
    virtual void sync(std::vector<std::shared_ptr<FeatureValueProto>>* feas) override; 
    virtual bool load(PosixIoReaderInterface *reader) override;
    virtual bool dump(PosixIoWriterInterface *writer) const override;

private:
    virtual bool _init(const comcfg::ConfigUnit& conf) override;
    bool _check_fea(const FeatureValueProto& fea) const;
    std::shared_ptr<GroupViewItem> _enter_window(
            const uint64_t view_sign, 
            const uint64_t coord);
    void _triger_query(const FeatureValueProto& fea, const GroupViewItem& node);
    uint64_t _get_extend_view_sign(const FeatureValueProto& fea) const;
private:
    typedef std::shared_ptr<FValQueryTransProto> ValueQuery;
    typedef std::unordered_map<uint64_t, ValueQuery> ValueQueryMaps;

    ValueQueryMaps _val_query_map;
    anti::themis::common_lib::BatchCumulativeDistribution<> _dis;
    struct ExtendConf {
        uint64_t fea_id;
    };
    ExtendConf _extend_conf;
    struct CdfConf {
        uint32_t step;
        double ratio;
    };
    CdfConf _cdf_conf;
};


}
}
}
#endif

