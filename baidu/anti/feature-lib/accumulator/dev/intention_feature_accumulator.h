// Copyright (c) 2021 Baidu.com, Inc. All Rights Reserved
// @Author: longfei(<EMAIL>)

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_INTENTION_FEATURE_ACCUMULATOR_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_INTENTION_FEATURE_ACCUMULATOR_H

#include <mutex>
#include <session_segment.hpp>
#include "feature_accumulator_interface.h"
#include "intention_node.h"

namespace anti {
namespace themis {
namespace feature_lib {

typedef anti::themis::common_lib::SessionSegment<IntentionNode> SessionSegment;

class IntentionFeatureAccumulator : public FeatureAccumulatorInterface {
public:
    IntentionFeatureAccumulator() : 
            _feature_id(0LU), 
            _version(0LU),
            _segment_len(0LU) {}

    virtual ~IntentionFeatureAccumulator() {
        uninit();
    }

    uint64_t feature_id() const {
        return _feature_id;
    }
    bool init(const comcfg::ConfigUnit& conf);
    void uninit();

    virtual bool update(const FeatureValueProto& fea);
    virtual bool update_and_query(FeatureValueProto* fea);
    virtual bool query(FeatureValueProto* fea) const;

    bool load(PosixIoReaderInterface *reader);
    bool dump(PosixIoWriterInterface *writer) const;

private:
    enum ProcessStringRet {
        SPLIT_FAIL,
        OVER_RANGE,
        PROCESS_OK
    };

    bool _check_feature(const FeatureValueProto& fea) const;
    bool _fillup_feature(const IntentionNode* ptr, FeatureValueProto* fea) const;

    uint64_t _feature_id;
    uint64_t _version;
    int64_t _segment_len;
    uint64_t _session_len;
    float _threshold;

    SessionSegment _session_segment;
    mutable std::mutex _mutex;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_INTENTION_FEATURE_ACCUMULATOR_H

/* vim: set ts=4 sw=4: */
