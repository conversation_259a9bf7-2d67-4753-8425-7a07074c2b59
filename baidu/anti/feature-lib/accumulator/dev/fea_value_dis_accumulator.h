// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: feature_value_distribution_accumulator.h
// @Last modified: 2018-01-02 13:54:45
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_FEA_VALUE_DIS_ACCUMULATOR_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_FEA_VALUE_DIS_ACCUMULATOR_H

#include <item.h>
#include "distribution_distance.h"
#include "extend_accumulator_base.h"

namespace anti {
namespace themis {
namespace feature_lib {

typedef anti::themis::common_lib::ArrayItem<double, 10> DistributionItem; 
typedef GroupNode<DistributionItem> GroupDistributionItem;
typedef ExtendAccumulatorBase<GroupDistributionItem> FeatureValueDisAccBase;

class FeaValueDisAccumulator : public FeatureValueDisAccBase {
public:
    FeaValueDisAccumulator() : 
            _acc_value_type(ERROR) {}
    virtual ~FeaValueDisAccumulator() {}
    
    virtual bool query(FeatureValueProto* fea) const;
    virtual bool update_and_query(FeatureValueProto* fea);
    virtual bool update(const FeatureValueProto& fea);
    virtual void sync(std::vector<std::shared_ptr<FeatureValueProto>>* feas);
    virtual bool query(const FValQRequestProto& request, FValQResponseProto* response) const;

private:
    virtual bool _init(const comcfg::ConfigUnit& conf); 
    virtual void _uninit() {
        _fea_reqs.clear();
        _items_reqs.clear();
        _extend_fea_reqs.clear();
        _intervals.clear();
    } 
    
private:
    typedef GroupNode<DistributionItem> GNode;
    typedef std::shared_ptr<GNode> GNodePtr;
    GNodePtr _query_view_node(uint64_t view_sign);
    const GNodePtr _query_view_node(uint64_t view_sign) const;
    GNodePtr _enter_window(uint64_t view_sign, uint64_t coord);
    typedef std::shared_ptr<DistributionItem> DistributionItemPtr;
    DistributionItemPtr _calculate_dis(
            const std::shared_ptr<FValQueryTransProto>& fea_trans);

    std::shared_ptr<FValQueryTransProto> _convert_request(
            const std::shared_ptr<FValQueryTransProto>& req);
    bool _check_fea(const FeatureValueProto& fea) const; 
    
    int64_t _get_expiration(uint64_t fid, uint64_t view_sign) {
        return  _window_conf.expiration == 0 ? 
                0 : 
                _window_conf.expiration + (fid ^ view_sign) % _window_conf.expiration;
    }
    
    void _triger_items_query(const FeatureValueProto& fea);
    void _triger_extend_fea_query(
            int64_t view_sign, 
            const std::shared_ptr<FValQueryTransProto>& req);
    void _complete_fea(
            const std::shared_ptr<FValQueryTransProto>& req,
            FeatureValueProto* fea);
    void _sync(
            uint64_t view_sign,
            const std::shared_ptr<FValQueryTransProto>& fea_trans);

    bool _distance_query(
            const FValQRequestProto& request,
            const DistributionItemPtr& item,
            FValQResponseProto* response) const { /* no implement */ return false; }

    bool _bucket_query(
            const FValQRequestProto& request,
            const DistributionItemPtr& item,
            FValQResponseProto* response) const;
    
private:
    DistributionDistance _distance;
    std::vector<double> _intervals;
    enum AccValueType {
        CLICK_VALUE = 0,
        UNIQ_VALUE = 1,
        ERROR = 2
    };
    AccValueType _acc_value_type;
    // _extend_fids[0] : level 1 view -> extend fid
    // _extend_fids[1] : level 2 view -> extend fid
    std::vector<uint64_t> _extend_fids;
    typedef std::unordered_map<uint64_t, std::shared_ptr<FValQueryTransProto>> FVQMaps;
    mutable FVQMaps _fea_reqs;
    FVQMaps _items_reqs;
    FVQMaps _extend_fea_reqs;
    uint32_t _max_req_num;
};

inline const FValQRequestProto* fetch_request(
        const FeatureValueProto& fea,
        uint64_t feature_id) {
    if (!fea.has_fea_serv_trans() || fea.fea_serv_trans().infos_size() == 0) {
        return NULL;
    }
    
    for (int i = 0; i < fea.fea_serv_trans().infos_size(); ++i) {
        if (fea.fea_serv_trans().infos(i).has_request() 
                && fea.fea_serv_trans().infos(i).request().has_feature_id()
                && fea.fea_serv_trans().infos(i).request().feature_id() == feature_id) {
            return &fea.fea_serv_trans().infos(i).request();
        }
    }
    return NULL; 
}


} // namespace feature_lib
} // namespace themis 
} // namespace anti

#endif // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_FEA_VALUE_DIS_ACCUMULATOR_H

/* vim: set ts=4 sw=4: */

