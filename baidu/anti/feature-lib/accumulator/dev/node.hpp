// Copyright 2021 Baidu Inc. All Right Reserved.
// Author longfei04 (<EMAIL>)
// 
// brief: 

#pragma once

#include <stdio.h>
#include <boost/lexical_cast.hpp>
#include <nlpc_client.h>
#include <gflags/gflags.h>
//#include <nlpc/ver_1_0_0/erniesim_light_input.h>
//#include <nlpc/ver_1_0_0/erniesim_light_output.h>
#include <com_log.h>
#include "nlpc_client_manager.h"
#include "utils.h"

namespace anti {
namespace themis {
namespace feature_lib {
/*
typedef ::nlpc::ver_1_0_0::erniesim_light_inputPtr ErniesimLightInputPtr;
typedef ::nlpc::ver_1_0_0::erniesim_light_input ErniesimLightInput;
typedef ::nlpc::ver_1_0_0::erniesim_light_outputPtr ErniesimLightOutputPtr;
typedef ::nlpc::ver_1_0_0::erniesim_light_output ErniesimLightOutput;
typedef ::nlpc::ver_1_0_0::text_pairPtr TextPairPtr;
typedef ::nlpc::ver_1_0_0::text_pair TextPair;
typedef ::drpc::NLPCClient NLPCClient;

DEFINE_int64(NLPC_CALL_RETRY, 3, "nlpc call try times");

class QueryNode {
public:
    QueryNode(uint64_t view_sign, std::string query, int64_t coord) : 
        _view_sign(view_sign), _query(query), _coord(coord) {};

    QueryNode() :
        _view_sign(0), _query(""), _coord(0) {};

    uint64_t key() const {
        return _view_sign;
    }

    int64_t coord() const {
        return _coord;
    }

    std::string value() const {
        return _query;
    }

    void update(const std::string& query, const uint64_t& coord) {
        _query = query;
        _coord = coord;
    }

    template<typename Archive>
    bool serialize(Archive* ar) const;

    template<typename Archive>
    bool deserialize(Archive* ar);

    bool calc(std::shared_ptr<QueryNode> r_node, std::string* value) {
        NLPCClient* client = common_lib::NLPCClientManagerSingleton::instance().
                                     get_nlpc_client("nlpc_erniesim_light_100");
        if (client == NULL) {
            CFATAL_LOG("fail to get nlpc_client!");
            return false;
        }

        ErniesimLightInputPtr input_ptr;
        ErniesimLightOutputPtr output_ptr;
        try {
            input_ptr = ::sofa::create<ErniesimLightInput>();
            output_ptr = ::sofa::create<ErniesimLightOutput>();
        }
        catch (::sofa::ExceptionPtr& ex) {
            CFATAL_LOG("create input and output failed !!![%s]", ex->what().c_str());
            return false;
        }

        TextPairPtr text_pair = ::sofa::create<TextPair>();
 
        input_ptr->inputs().clear();
        text_pair->set_text_a(_query);
        text_pair->set_text_b(r_node->value());
        input_ptr->inputs().push_back(text_pair);

        std::string str_input;
        // 发送之前需要将输入序列化成输入字符串
        if (!input_ptr->to_binary(&str_input)) {
            CFATAL_LOG("serialize failed, input_str:[%s:%s]",
                    _query.c_str(), r_node->value().c_str());
            return false;
        }
        // sync method call
        std::string str_output;
        int32_t ret, i = 0;
        do {
            ret = client->call_method(str_input, str_output);
            ++i;
        } while(ret != 0 && i < FLAGS_NLPC_CALL_RETRY);

        if (ret != 0) {
            CFATAL_LOG("call nlpc method failed, ret:%d, input_str[%s:%s]",
                    ret, _query.c_str(), r_node->value().c_str());
            return false;
        }
        // 反序列化输出字符串
        if (!output_ptr->from_binary(str_output)) {
            CFATAL_LOG("deserialize failed");
            return false;
        }

        if (output_ptr->results().size() != input_ptr->inputs().size()) {
            CFATAL_LOG("input size != output size.!");
            return false;
        }

        if (output_ptr->results()[0]->retcode() != 0) {
            auto input = input_ptr->inputs();
            CWARNING_LOG("retcode != 0, ret:%d, input query[%s:%s]",
                    output_ptr->results()[0]->retcode(),
                    input[0]->text_a().c_str(),
                    input[0]->text_b().c_str());
        }

        *value =  std::to_string(output_ptr->results()[0]->cosine_score());
        return true;
    }

private:
    uint64_t _view_sign;
    std::string _query;
    int64_t _coord;
};
*/

class TimeNode {
public:
    TimeNode(uint64_t view_sign, std::string timestamp, int64_t coord) : 
        _view_sign(view_sign), _timestamp(timestamp), _coord(coord) {};

    TimeNode() :
        _view_sign(0), _timestamp(""), _coord(0) {};

    uint64_t key() const {
        return _view_sign;
    }

    int64_t coord() const {
        return _coord;
    }

    std::string value() const {
        return _timestamp;
    }

    void set_value(const std::string& timestamp) {
        _timestamp = timestamp;
    }

    void set_coord(uint64_t coord) {
        _coord = coord;
    }

    void update(const std::string& timestamp, const uint64_t& coord) {
        _timestamp = timestamp;
        _coord = coord;
    }

    template<typename Archive>
    bool serialize(Archive* ar) const;

    template<typename Archive>
    bool deserialize(Archive* ar);

    bool calc(const std::shared_ptr<TimeNode>& r_node, std::string* value) {
        uint64_t l_node_time, r_node_time;
        try {
            l_node_time = boost::lexical_cast<uint64_t>(_timestamp);
            r_node_time = boost::lexical_cast<uint64_t>(r_node->value());
        } catch (const boost::bad_lexical_cast& e) {
            CFATAL_LOG("fail to cast string to uint64_t, l_node value(%s), r_node value(%s)",
                    _timestamp.c_str(), r_node->value().c_str());
            return false;
        }
        int64_t diff = l_node_time - r_node_time;
        *value = std::to_string(diff);
        return true;
    }

private:
    uint64_t _view_sign;
    std::string _timestamp;
    int64_t _coord;
};
/*
template<typename Archive>
bool QueryNode::serialize(Archive* ar) const {
    if (!ar) {
        return false;
    }
    uint64_t str_size = _query.size();
    DUMP_VAR(_view_sign, ar, uint64_t);
    DUMP_VAR(_coord, ar, int64_t);
    DUMP_VAR(str_size, ar, uint64_t);

    if (str_size != ar->write(_query.c_str(), str_size)) {
        CFATAL_LOG("fail to write query string, query:%s", _query.c_str());
        return false;
    }
    return true;
}

template<typename Archive>
bool QueryNode::deserialize(Archive* ar) {
    if (!ar) {
        return false;
    }
    uint64_t str_size;
    LOAD_VAR(&_view_sign, ar, uint64_t);
    LOAD_VAR(&_coord, ar, int64_t);
    LOAD_VAR(&str_size, ar, uint64_t);

    char buf[str_size + 1] = {0};
    if (str_size != ar->read(buf, str_size)) {
        CFATAL_LOG("fail to read query string.");
        return false;
    }
    _query.assign(buf);
    return true;
}
*/
template<typename Archive>
bool TimeNode::serialize(Archive* ar) const {
    if (!ar) {
        return false;
    }
    uint64_t str_size = _timestamp.size();
    DUMP_VAR(_view_sign, ar, uint64_t);
    DUMP_VAR(_coord, ar, int64_t);
    DUMP_VAR(str_size, ar, uint64_t);

    if (str_size != ar->write(_timestamp.c_str(), str_size)) {
        CFATAL_LOG("fail to write timestamp string, timestamp:%s", _timestamp.c_str());
        return false;
    }
    return true;
}

template<typename Archive>
bool TimeNode::deserialize(Archive* ar) {
    if (!ar) {
        return false;
    }
    uint64_t str_size;
    LOAD_VAR(&_view_sign, ar, uint64_t);
    LOAD_VAR(&_coord, ar, int64_t);
    LOAD_VAR(&str_size, ar, uint64_t);

    char buf[str_size + 1] = {0};
    if (str_size != ar->read(buf, str_size)) {
        CFATAL_LOG("fail to read query string.");
        return false;
    }
    _timestamp.assign(buf);
    return true;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

