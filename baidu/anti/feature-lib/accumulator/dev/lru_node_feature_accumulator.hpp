// Copyright 2021 Baidu Inc. All Right Reserved.
// Author longfei04 (<EMAIL>)
// 
// brief: 

#pragma once

#include <stdio.h>
#include <boost/lexical_cast.hpp>
#include "feature_accumulator_interface.h"
#include "node.hpp"
#include <gflags/gflags.h>
#include "boost/lexical_cast.hpp"

namespace anti {
namespace themis {
namespace feature_lib {

template<typename Node>
class LRUNodeFeatureAccumulator : public FeatureAccumulatorInterface {
public:
    typedef std::shared_ptr<Node> NodePtr;

    LRUNodeFeatureAccumulator() : 
            _feature_id(0LU), 
            _version(0LU),
            _window_len(0LU) {}

    virtual ~LRUNodeFeatureAccumulator() {
        uninit();
    }

    uint64_t feature_id() const {
        return _feature_id;
    }
    bool init(const comcfg::ConfigUnit& conf);
    void uninit();

    virtual bool update(const FeatureValueProto& fea);
    virtual bool update_and_query(FeatureValueProto* fea);
    virtual bool query(FeatureValueProto* fea) const;

    bool load(PosixIoReaderInterface *reader);
    bool dump(PosixIoWriterInterface *writer) const;

private:
    bool _check_feature(const FeatureValueProto& fea) const;
    uint64_t _feature_id;
    uint64_t _version;
    int64_t _window_len;
    uint64_t _max_item;
    typedef anti::themis::common_lib::ThreadSafeLRUWindow<uint64_t, Node> ThreadSafeLRUWindow;
    typedef std::shared_ptr<ThreadSafeLRUWindow> ThreadSafeLRUWindowPtr;
    ThreadSafeLRUWindowPtr _window;
};

template<typename Node>
bool LRUNodeFeatureAccumulator<Node>::init(const comcfg::ConfigUnit& conf) {
    try {
        _feature_id = conf["feature_id"].to_uint64();
        _version = conf["version"].to_uint64();
        _window_len = conf["window_length"].to_int64();
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    }
    _max_item = LLONG_MAX;
    if (conf["max_num"].selfType() != comcfg::CONFIG_ERROR_TYPE) {
        _max_item = conf["max_num"].to_int64();
    }
    if (_window_len <= 0L) {
        CFATAL_LOG("invalid window_len(%ld)", _window_len);
        return false;
    }
    _window.reset(new(std::nothrow) 
            ThreadSafeLRUWindow(_window_len, _max_item));
    if (_window == NULL) {
        CFATAL_LOG("create LRUWindow fail, feature_id(%lu)", feature_id());
        return false;
    }

    CWARNING_LOG("init LRUNodeFeatureAccumulator<Node>(%lu) success", _feature_id);
    return true;
}

template<typename Node>
void LRUNodeFeatureAccumulator<Node>::uninit() {
    _feature_id = 0LU;
    _version = 0LU;
    _window_len = 0L;
    _window.reset();
}

template<typename Node>
bool LRUNodeFeatureAccumulator<Node>::update(
        const FeatureValueProto& fea) {
    if (!_check_feature(fea)) {
        CFATAL_LOG("call _check_feature fail, feature_id:%lu, logid:%lu", 
                _feature_id, fea.log_id());
        return false;
    }
 
    if (_window == NULL) {
        CFATAL_LOG("window is NULL.");
        return false;
    }
 
    NodePtr node = _window->find(fea.view_sign());
    if (node != NULL) {
        node->update(fea.data_view_value(), fea.coord());
    } else {
        node.reset(new(std::nothrow) Node(fea.view_sign(), fea.data_view_value(), fea.coord()));
    }
    _window->insert(node);
    CDEBUG_LOG("update succ");
    return true;
}

template<typename Node>
bool LRUNodeFeatureAccumulator<Node>::update_and_query(FeatureValueProto* fea) {
    if (!fea || !_check_feature(*fea)) {
        CFATAL_LOG("call _check_feature fail");
        return false;
    }
    if (_window == NULL) {
        CFATAL_LOG("window is NULL.");
        return false;
    }

    NodePtr r_node;
    r_node.reset(new(std::nothrow) Node(fea->view_sign(), fea->data_view_value(), fea->coord()));
    std::string value = "";
    NodePtr node = _window->find(fea->view_sign());
    if (node != NULL) {
        if (!node->calc(r_node, &value)) {
            CWARNING_LOG("cal value fail.");
            return false;
        }
        fea->set_valid(true);
        fea->set_value(value);
        node->update(fea->data_view_value(), fea->coord());
    } else {
        node = r_node;
        fea->set_valid(false);
        fea->set_value("-1");
    }
    _window->insert(node);
    CDEBUG_LOG("update_and_query succ, output valid:%d, ooutput value:%s, feature_type:%d",
            fea->valid(), fea->value().c_str(), fea->feature_type());
    return true;
}

template<typename Node>
bool LRUNodeFeatureAccumulator<Node>::query(FeatureValueProto* fea) const {
    if (fea == NULL || !_check_feature(*fea)) {
        CFATAL_LOG("input fea == NULL or call _check_feature fail");
        return false;
    }
    if (_window == NULL) {
        CFATAL_LOG("window is NULL.");
        return false;
    }

    NodePtr r_node;
    r_node.reset(new(std::nothrow) Node(fea->view_sign(), fea->data_view_value(), fea->coord()));
    NodePtr node = _window->find(fea->view_sign());
    if (node == NULL) {
        fea->set_value("-1");
        fea->set_valid(false);
        return true;
    }

    std::string value = "";
    if (!node->calc(r_node, &value)) {
        CWARNING_LOG("cal value fail.");
        return false;
    }
    fea->set_valid(true);
    fea->set_value(value);
    CDEBUG_LOG("query succ, output valid:%d, value:%s, feature_type:%d",
            fea->valid(), fea->value().c_str(), fea->feature_type());
    return true;
}

template<typename Node>
bool LRUNodeFeatureAccumulator<Node>::_check_feature(
        const FeatureValueProto& fea) const {
    if (fea.feature_id() != _feature_id
            || (fea.feature_type() != FeatureValueProto::QUERY_SIMILAR 
                   && fea.feature_type() != FeatureValueProto::TIME_INTERVAL)) {
        CFATAL_LOG("feature_id(%lu) != _feature_id(%lu) || invalid feature_type(%d), logid(%lu)", 
                fea.feature_id(), _feature_id, fea.feature_type(), fea.log_id());
        return false;
    }
    return true;
}

template<typename Node>
bool LRUNodeFeatureAccumulator<Node>::load(PosixIoReaderInterface *reader) {
    if (!reader) {
        CFATAL_LOG("writer ptr invalid, feature_id(%lu)", _feature_id);
        return false;
    }
    if (_window == NULL) {
        CFATAL_LOG("window ptr is null, call init first please, feature_id(%lu)", 
                _feature_id);
        return false;
    }
    uint64_t ver = 0LU;
    if (reader->read(&ver, sizeof(ver)) != static_cast<int>(sizeof(ver))) {
        CWARNING_LOG("load version fail, feature_id(%lu)", _feature_id);
        return false;
    }
    // version > checkpoint version return true directly
    if (ver < _version) {
        return true;
    }
    
    if (!_window->deserialize(reader)) {
        CWARNING_LOG("load window fail, feature_id(%lu)", _feature_id);
        return false;
    }
    return true;
}

template<typename Node>
bool LRUNodeFeatureAccumulator<Node>::dump(PosixIoWriterInterface *writer) const {
    if (!writer) {
        CFATAL_LOG("writer ptr invalid, feature_id(%lu)", _feature_id);
        return false;
    }

    if (_window == NULL) {
        CFATAL_LOG("window ptr is null, call init first please, feature_id(%lu)", 
                _feature_id);
        return false;
    }

    if (writer->write(&_version, sizeof(_version)) 
                != static_cast<int>(sizeof(_version))) {
        CWARNING_LOG("write version(%lu) fail, feature_id(%lu)", 
                _version, _feature_id);
        return false;
    }
    
    if (!_window->serialize(writer)) {
        CWARNING_LOG("dump window fail, feature_id(%lu)", _feature_id);
        return false;
    }
    return true;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

