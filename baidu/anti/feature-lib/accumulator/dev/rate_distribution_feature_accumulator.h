// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: rate_distribution_feature_accumulator.h
// @Last modified: 2017-03-16 12:08:40
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_RATE_DISTRIBUTION_FEATURE_ACCUMULATOR_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_RATE_DISTRIBUTION_FEATURE_ACCUMULATOR_H

#include "feature_accumulator_interface.h"
#include <item.h>
#include <sliding_window.h>

namespace anti {
namespace themis {
namespace feature_lib {

class RateDistributionFeatureAccumulator : public FeatureAccumulatorInterface {
public:
    RateDistributionFeatureAccumulator() : 
            _feature_id(0LU),
            _version(0LU),
            _time_threshold(0L) {
        memset(&_remain, 0, sizeof(int64_t) * TOTAL);
    }
    virtual ~RateDistributionFeatureAccumulator() {
        uninit();
    }

    virtual bool init(const comcfg::ConfigUnit& conf);
    virtual void uninit() {
        _dwindow.uninit();
        _intervals.clear();
        _time_threshold = 0L;
        for (uint32_t i = 0; i < TOTAL; ++i) {
            _remain[i] = 0L;
            _swindows[i].uninit();
        }
        _version = 0LU;
        _feature_id = 0LU;
    }

    virtual uint64_t feature_id() const {
        return _feature_id;
    }
    
    virtual bool update(const FeatureValueProto& fea);
    virtual bool update_and_query(FeatureValueProto* fea);
    virtual bool query(FeatureValueProto* fea) const;

    virtual bool load(PosixIoReaderInterface *reader);
    virtual bool dump(PosixIoWriterInterface *writer) const;

private:
    typedef anti::themis::common_lib::SegmentItem SegmentItem;
    typedef anti::themis::common_lib::SlidingWindow<SegmentItem> SegmentSlidingWindow;
    const static int MAX_BUCKET_NUM = 10;
    typedef anti::themis::common_lib::ArrayItem<int64_t, MAX_BUCKET_NUM> DistributionItem; 
    typedef anti::themis::common_lib::SlidingWindow<DistributionItem> DistributionSlidingWindow;

    bool _init_intervals(const std::string& points);
    bool _check_feature(const FeatureValueProto& fea) const;

    // @return: the rate of numerator and denominator
    //           < 0 means invalid rate
    double _query_segment(const FeatureValueProto& fea) const;
    // update and query segment
    double _update_segment(const FeatureValueProto& fea);
    // @return: the bucket index of rate
    //           < 0 means invalid bucket index
    int64_t _get_bucket_idx(double rate) const;

    // @brief: update distribution using last bucket idx and current bucket idx
    //         1. cur_idx < 0 (invalid) : only slide to fea.coord and query
    //         2. cur_idx == last_idx : the same as above
    //         3. last_idx < 0 && cur_idx >= 0 : update and query distribution
    //                                           bucket[cur_idx].count +=1 
    //         4. else (last_idx >=0 && cur_idx >= 0 && last_idx != cur_idx)
    //              means jump bucket from last_idx to cur_idx
    //              update and query distribution
    //              bucket[cur_idx].count +=1 and bucket[last_idx].count -=1
    const DistributionItem* _update_distribution(
            int64_t last_idx, 
            int64_t cur_idx, 
            const FeatureValueProto& fea);
    void _set_feature(
            int64_t cur_idx,
            const DistributionItem* di,
            FeatureValueProto* fea) const;

private:
    uint64_t _feature_id;
    uint64_t _version;

    enum Rate {
        NUM = 0,                                  // numerator
        DEN = 1,                                  // denominator
        TOTAL = 2
    };
    SegmentSlidingWindow _swindows[TOTAL];
    int64_t _remain[TOTAL];
    int64_t _time_threshold;

    std::vector<std::pair<double, double> > _intervals;
    DistributionSlidingWindow _dwindow;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_RATE_DISTRIBUTION_FEATURE_ACCUMULATOR_H

/* vim: set ts=4 sw=4: */

