// Copyright 2016 Baidu Inc. All Right Reserved.
// Author <PERSON> (<EMAIL>)
// 
// brief: 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_DISTINCT_DISTRIBUTION_FEATURE_ACCUMULATOR_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_DEV_DISTINCT_DISTRIBUTION_FEATURE_ACCUMULATOR_H

#include "feature_accumulator_interface.h"
#include <item.h>
#include <sliding_window.h>
#include "distribution_distance.h"

namespace anti {
namespace themis {
namespace feature_lib {

class DistinctDistributionFeatureAccumulator : public FeatureAccumulatorInterface {
public:
    DistinctDistributionFeatureAccumulator() : 
            _feature_id(0LU), 
            _version(0LU) {
    }

    virtual ~DistinctDistributionFeatureAccumulator() {
        uninit();
    }
    virtual uint64_t feature_id() const {
        return _feature_id;
    }
    virtual bool init(const comcfg::ConfigUnit& conf);
    virtual void uninit();

    virtual bool update(const FeatureValueProto& fea);
    virtual bool update_and_query(FeatureValueProto* fea);
    virtual bool query(FeatureValueProto* fea) const;

    virtual bool load(PosixIoReaderInterface *reader);
    virtual bool dump(PosixIoWriterInterface *writer) const;

private:
    const static int MAX_BUCKET_NUM = 10;
    typedef anti::themis::common_lib::ArrayItem<int64_t, MAX_BUCKET_NUM> DistributionItem; 
    typedef anti::themis::common_lib::DistinctItem DistinctItem;
    typedef anti::themis::common_lib::SlidingWindow<DistinctItem> SegmentSlidingWindow;
    typedef anti::themis::common_lib::SlidingWindow<DistributionItem> DistributionSlidingWindow;

    bool _init_intervals(const std::string& points);
    bool _get_interval_idx(int64_t cumulant, uint32_t* idx, bool jump_essential) const;
    bool _check_feature(const FeatureValueProto& fea) const;
    bool _fillup_feature(
            const DistributionItem* item, 
            uint32_t idx,
            FeatureValueProto* fea) const;
    bool _update(const FeatureValueProto& fea, const DistributionItem** item, uint32_t* idx);

    uint64_t _feature_id;
    uint64_t _version;
    SegmentSlidingWindow _seg_window;
    DistributionSlidingWindow _dis_window;
    std::vector<std::pair<int64_t, int64_t> > _intervals;
    std::shared_ptr<DistributionDistance> _distribution_distance;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif
