// Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
// @Author: ch<PERSON><PERSON><PERSON><PERSON>(ch<PERSON><PERSON><PERSON><PERSON>@baidu.com)
// 
// @File: checkpoint_manager.cpp
// @Last modified: 2017-05-11 15:11:20
// @Brief: 

#include "checkpoint_manager.h"
#include <unistd.h>
#include <stdio.h>
#include <thread>
#include <fstream>
#include <set>
#include <future>
#include <gflags/gflags.h>
#include <sys/syscall.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>
#include "posix_themis_io.h"
#include "time_utils.h"
#include "feature_util.h"

const uint32_t MAX_BUF_SIZE = 512;
const std::string ckpt_prefix = "ckpt";
const std::string desc_name = "describe";
DEFINE_int32(ckpt_num, 10, "thread num of dump");

namespace anti {
namespace themis {
namespace feature_lib {

using Timer = anti::themis::io_lib::Timer;

bool CheckpointManager::dump(const CoordMap &coord_map, const AccMap &acc_map) {
    Timer timer;
    auto fea_num = acc_map.size();
    auto queue = _push_feas(coord_map, acc_map);
    std::vector<std::future<std::vector<DescInfo>>> res;
    for (int32_t i = 0; i < FLAGS_ckpt_num; ++i) {
        std::string file_name = get_file_name(_checkpoint_path, ckpt_prefix, _version_id,
                _record_type, std::to_string(i));
        std::future<std::vector<DescInfo>> thread_dump_res = std::async(
                std::launch::async, &CheckpointManager::_thread_dump, this, 
                file_name, queue); 
        res.emplace_back(std::move(thread_dump_res));
    }
    std::vector<DescInfo> desc_res;
    for (auto &i : res) {
        auto thread_desc_res = i.get();    
        desc_res.insert(desc_res.end(), thread_desc_res.begin(), thread_desc_res.end());
    }
    if (desc_res.size() != fea_num) {
        CFATAL_LOG("dump features failed! total feature num[%d]", acc_map.size());
        return false;
    }
    if (!_dump_desc(desc_res)) {
        CFATAL_LOG("dump desc failed!");
        return false; 
    }
    if (!_cover_ckpt()) {
        CFATAL_LOG("mv ckpt or desc failed!");
        return false;
    }
    CWARNING_LOG("dump features success! success feature num[%d], time[%lf]s", 
            acc_map.size(), static_cast<double>(timer.elapse_us()) / 1000000);
    return true;
}

std::vector<DescInfo> CheckpointManager::_thread_dump(
        const std::string &ckpt_name,
        std::shared_ptr<ThreadSafeQueue<FeaInfo>> queue) {
    if (!queue) {
        CFATAL_LOG("invalid queue shared_ptr");
        return std::vector<DescInfo>();
    }
    PosixThemisIoWriter tmp;
    auto writer = &tmp;
    std::string ckpt_name_tmp = ckpt_name + ".tmp";
    std::vector<DescInfo> res;
    struct stat t;
    if (stat(ckpt_name_tmp.c_str(), &t) == 0 && 
            remove(ckpt_name_tmp.c_str()) == -1) {
        CFATAL_LOG("remove[%s] failed", ckpt_name_tmp.c_str());
        return std::vector<DescInfo>();
    }
    if (writer->open(ckpt_name_tmp.c_str()) != 0) {
        CFATAL_LOG("open file[%s]", ckpt_name_tmp.c_str());
        return std::vector<DescInfo>();
    }

    do {
        off_t begin_off = writer->offset();
        auto fea_info = queue->pop();
        if (!fea_info) {
            CWARNING_LOG("queue is empty, usleep 50");
            usleep(50);
            continue;
        }
        if (writer->write(&fea_info->fea_id, sizeof(fea_info->fea_id)) != sizeof(fea_info->fea_id) 
                || writer->write(&fea_info->coord, sizeof(fea_info->coord)) != sizeof(fea_info->coord)
                || !fea_info->fea_ptr->dump(writer)
                || !writer->flush()) {
            CFATAL_LOG("dump fea[%ld] failed!", fea_info->fea_id);
            return std::vector<DescInfo>();
        }
        CWARNING_LOG("dump fea[%ld] success", fea_info->fea_id);

        if (_use_record_type) {
            res.emplace_back(std::move(DescInfo(
                    fea_info->fea_id,
                    _record_type.c_str(),
                    ckpt_name.c_str(),
                    begin_off,
                    writer->offset() - begin_off)));
        } else {
            res.emplace_back(std::move(DescInfo(
                    fea_info->fea_id,
                    ckpt_name.c_str(),
                    begin_off,
                    writer->offset() - begin_off)));
        }
    } while (!queue->empty());

    pid_t tid = syscall(SYS_gettid); 
    CWARNING_LOG("thread[%d] dumps feas finish, close tid[%d]", tid, tid);
    return res; 
}

bool CheckpointManager::_dump_desc(const std::vector<DescInfo> &desc_infos) {
    std::string desc_tmp_file = get_file_name(_checkpoint_path, desc_name, _version_id) + ".tmp";
    struct stat t;
    if (stat(desc_tmp_file.c_str(), &t) == 0 
            && remove(desc_tmp_file.c_str()) == -1) {
        CFATAL_LOG("remove file[%s] failed!", desc_tmp_file.c_str());
    }

    std::ofstream out(desc_tmp_file.c_str());
    if (!out.is_open()) {
        CFATAL_LOG("dump desc ostream open failed! file[%s]", desc_tmp_file.c_str());
        return false;
    }

    if (_use_record_type) {
        std::string buf;
        std::string desc_file = get_file_name(_checkpoint_path, desc_name, _version_id);
        std::ifstream in(desc_file.c_str());
        while (getline(in, buf)) {
            DescInfo desc_info;
            if (!desc_info.deserialize(buf)) {
                CFATAL_LOG("desc file deserialize failed!");
                in.close();
                return false;
            }
            if (_record_type.compare(desc_info.record_type) == 0) {
                //the record type is manager owned, skip
                continue;
            }
            out << buf << "\n";
        }
    }

    for (const auto &i : desc_infos) {
        out << i.serialize();
    }
    out.close();
    CWARNING_LOG("desc.tmp[%s] file write success!", desc_tmp_file.c_str());
    return true;
}

bool CheckpointManager::_cover_ckpt() {
    std::string ckpt_name_tmp;
    for (int32_t i = 0; i < FLAGS_ckpt_num; ++ i) {
        std::string ckpt_name = get_file_name(_checkpoint_path, ckpt_prefix, 
                _version_id, _record_type, std::to_string(i));
        ckpt_name_tmp = ckpt_name + ".tmp";
        if (rename(ckpt_name_tmp.c_str(), ckpt_name.c_str()) != 0) {
            CFATAL_LOG("mv %s %s fail", ckpt_name_tmp.c_str(), ckpt_name.c_str()); 
            return false;
        }
    }

    std::string desc_file = get_file_name(_checkpoint_path, desc_name, _version_id);
    std::string desc_tmp_file = desc_file + ".tmp";
    if (rename(desc_tmp_file.c_str(), desc_file.c_str()) != 0) {
        CFATAL_LOG("mv %s %s fail", desc_tmp_file.c_str(), desc_file.c_str()); 
        return false;
    }
    return true;
}

std::shared_ptr<ThreadSafeQueue<FeaInfo>> CheckpointManager::_push_feas(
        const CoordMap &coord_map, 
        const AccMap &acc_map) { 
    std::shared_ptr<ThreadSafeQueue<FeaInfo>> queue( 
            new(std::nothrow) ThreadSafeQueue<FeaInfo>());
    if (!queue) {
        CFATAL_LOG("invalid queue ptr");
        return NULL;
    }
    for (const auto &i : acc_map) {
        uint64_t fea_id = i.first;
        int64_t coord = 0;
        auto coord_map_it = coord_map.find(fea_id);
        if (coord_map_it != coord_map.end()) {
            coord = coord_map_it->second;
        }
        auto fea_ptr = i.second;
        queue->push(FeaInfo(fea_id, coord, fea_ptr));
    }
    return queue;
}


bool CheckpointManager::load(CoordMap *coord_map, AccMap *acc_map) {
    if (coord_map == NULL || acc_map == NULL) {
        CFATAL_LOG("input coord_map ptr or acc_map ptr is NULL");
        return false;
    }

    Timer timer;
    FileDescsMap file_descs_map;
    if (!_load_desc(&file_descs_map)) {
        CFATAL_LOG("load describe failed!");
        return false;
    }
    if (file_descs_map.size() == 0) {
        CWARNING_LOG("start accumulator with a empty describe!");
        return true;
    }

    std::vector<std::future<std::vector<FeaInfo>>> res_vec;
    for (auto &i : file_descs_map) {
        std::future<std::vector<FeaInfo>> res = std::async(
                std::launch::async, &CheckpointManager::_thread_load, this, 
                i.first, i.second, acc_map); 
        res_vec.emplace_back(std::move(res));
    }
    for (auto &res : res_vec) {
        auto thread_feas = res.get();
        if (thread_feas.size() == 0) {
            CFATAL_LOG("a thread loading failed!");
            return false;
        }
        for (auto &fea : thread_feas) {
            coord_map->emplace(fea.fea_id, fea.coord);
        }
    }
    CWARNING_LOG("load all features success! time[%lf]s", 
            static_cast<double>(timer.elapse_us()) / 1000000);
    return true;
}

bool CheckpointManager::_fea_load(
        const DescInfo &desc,
        PosixIoReaderInterface *reader,
        FeaInfo *fea_info) {
    if (reader->read(&fea_info->fea_id, sizeof(fea_info->fea_id)) != sizeof(fea_info->fea_id)
            || fea_info->fea_id != desc.fea_id) {
        CWARNING_LOG("err feaid[%lu], desc_fea_id[%lu]", fea_info->fea_id, desc.fea_id);
        return false;
    }
    if (reader->read(&fea_info->coord, sizeof(fea_info->coord)) != sizeof(fea_info->coord)) {
        CWARNING_LOG("err coord[%lu]", fea_info->coord);
        return false;
    }
    if (!(fea_info->fea_ptr)->load(reader)) {
        CWARNING_LOG("reader load failed!");
        return false;
    }
    return true;
}

std::vector<FeaInfo> CheckpointManager::_thread_load(
        const std::string &ckpt_name,
        const std::vector<DescInfo> descs,
        AccMap *acc_map) {
    PosixThemisIoReader tmp;
    auto reader = &tmp;
    std::vector<FeaInfo> load_res;
    
    if (reader->open(ckpt_name.c_str()) != 0) {
        CFATAL_LOG("reader open[%s] failed!", ckpt_name.c_str());
        return std::vector<FeaInfo>();
    }

    for (auto &desc : descs) {
        FeaInfo fea_info;
        auto acc_map_it = acc_map->find(desc.fea_id);
        if (acc_map_it == acc_map->end()) {
            CWARNING_LOG("feaid[%ld] not in conf, skip!", desc.fea_id);
            continue;
        }
        fea_info.fea_ptr = acc_map_it->second;
        if (!reader->seek(desc.off_set)) {
            CFATAL_LOG("ckpt[%s] seek[%d] failed!", 
                    ckpt_name.c_str(), desc.off_set);
            return std::vector<FeaInfo>();
        }
        if (!_fea_load(desc, reader, &fea_info)) {
            CFATAL_LOG("reopen and read feaid[%lu] failed!, offset[%d], length[%d]", 
                    desc.fea_id, desc.off_set, desc.length);
            return std::vector<FeaInfo>();
        }
        CWARNING_LOG("load fea[%ld] success!", desc.fea_id);
        load_res.emplace_back(fea_info);
    }
    pid_t tid = syscall(SYS_gettid); 
    CWARNING_LOG("thread[%d] loads feas finish, close tid[%d]", tid, tid);
    return load_res;
}

bool CheckpointManager::_load_desc(FileDescsMap *file_descs_map) {
    std::string desc_file = get_file_name(_checkpoint_path, desc_name, _version_id); 
    CWARNING_LOG("describe file name [%s]", desc_file.c_str());
    std::ifstream in(desc_file.c_str());
    if (!in.is_open()) {
        CWARNING_LOG("on describe file[%s]! start with no status!"
                , desc_file.c_str());
        return true;
    }
    std::string buf;
    std::unordered_set<uint64_t> feaid_set;
    while (getline(in, buf)) {
        DescInfo desc_info;
        if (!desc_info.deserialize(buf)) {
            CFATAL_LOG("desc file deserialize failed!");
            in.close();
            return false;
        }
        if (_use_record_type) {
            if (_record_type.compare(desc_info.record_type) != 0) {
                //the record type is not manager owned, skip
                continue;
            }
        }
        if (feaid_set.find(desc_info.fea_id) != feaid_set.end()) {
            CFATAL_LOG("multi describe feaid[%ld], repeated line[%s]", 
            desc_info.fea_id, desc_info.file.c_str());
            in.close();
            return false;
        }
        feaid_set.insert(desc_info.fea_id);
        if (file_descs_map->find(desc_info.file) == file_descs_map->end()) {
            file_descs_map->emplace(desc_info.file, std::vector<DescInfo>());
        }
        (*file_descs_map)[desc_info.file].emplace_back(desc_info);
    }
    CWARNING_LOG("desc[%s] load success!", desc_file.c_str());
    in.close();
    return true;
}

} // feature_lib
} // themis
} // anti

/* vim: set ts=4 sw=4: */

