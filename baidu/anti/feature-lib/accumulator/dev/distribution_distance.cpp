// Copyright 2016 Baidu Inc. All Right Reserved.
// Author houruijie (<EMAIL>)
//
// brief:

#include <string>
#include <float.h>
#include <math.h>
#include <com_log.h>
#include <boost/algorithm/string.hpp>
#include <boost/lexical_cast.hpp>
#include "distribution_distance_calculate.h"
#include "distribution_distance.h"


namespace anti {
namespace themis {
namespace feature_lib {

bool DistributionDistance::init(const comcfg::ConfigUnit& conf) {
    // distance configure
    try {
        int count_threshold = conf["count_threshold"].to_int64();
        std::string sta_pro(conf["stand_prob"].to_cstr());
        std::string func_type(conf["func_type"].to_cstr());
        // The default value of max_distance_magnify is 1.0. 
        // It means that pc will not to be magnified. 
        double max_distance_magnify = 1.0;
        const double MAX_DISTANCE_MAGNIFY_DEFAULT = 1.0; 
        conf["max_distance_magnify"].get_double(
                &max_distance_magnify, 
                MAX_DISTANCE_MAGNIFY_DEFAULT);
        if (max_distance_magnify < MAX_DISTANCE_MAGNIFY_DEFAULT) {
            CWARNING_LOG("Max_distance_magnify[%lf] is smaller than 1.0 and the value will be 1.0"
                    , max_distance_magnify);
            max_distance_magnify = MAX_DISTANCE_MAGNIFY_DEFAULT;
        }
        if (!init(func_type, sta_pro, count_threshold, max_distance_magnify)) {
            CWARNING_LOG("create distance failed: %s", func_type.data());
            return false;
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("feature value conf for model not find: %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std::exception : %s", e.what());
        return false;
    }
    return true;
}

bool DistributionDistance::init(
        const std::string& dis_type,
        const std::string& sta_pro, 
        int count_threshold, 
        double max_distance_magnify) {
    if (!_split_bucket(sta_pro)) {
        CWARNING_LOG("split bucket failed: %s", sta_pro.data());
        return false;
    }

    if (!_create_type(dis_type)) {
        CWARNING_LOG("create distance type: %s", sta_pro.data());
        return false;
    }

    _count_threshold = count_threshold;
    _max_distance_magnify = max_distance_magnify;
    return true;
}

bool DistributionDistance::_create_type(const std::string& dis_type) {
    if (dis_type == "max_diff") {
        _distance_operator = max_diff;
    } else if (dis_type == "chi_square_test") {
        _distance_operator = chi_square_test;
    } else if (dis_type == "chi_square_dis") {
        _distance_operator = chi_square_dis;
    } else if (dis_type == "kl_divergence") {
        _distance_operator = kl_divergence;
    } else {
        CWARNING_LOG("invalid func type(%s)", dis_type.data());
        return false;
    }
    return true;
}

bool DistributionDistance::_split_bucket(const std::string& sta_pro) {
    std::vector<std::string> sta_strs;
    boost::algorithm::split(
            sta_strs, sta_pro, boost::is_any_of(", "), boost::token_compress_on);
    for (uint32_t i = 0U; i < sta_strs.size(); ++i) {
        double prob = 0.0;
        try {
            prob = boost::lexical_cast<double>(sta_strs[i]);
        } catch (const boost::bad_lexical_cast& e) {
            CWARNING_LOG("probability(%u) is NULL,", i);
            return false;
        }        
        if (prob < 0.0) {
            CWARNING_LOG("probability(%u) is less than zero", i);
            return false;
        }
        _stand_prob.push_back(prob);
    }
    return true;
}

bool DistributionDistance::magnify_dis(const std::vector<double>& pb, std::vector<double>* pc)  const {
    if (_max_distance_magnify - 1.0 < DBL_EPSILON) {
        return true;
    }
    double factor = 1.0;
    for (size_t i = 0; i < pc->size(); ++i) {
        // if empty bucket or pb/pc > max_distance_magnify
        if ((*pc)[i] < DBL_EPSILON || pb[i] / (*pc)[i] >= _max_distance_magnify){
            factor = _max_distance_magnify;
            break;
        }
        factor = std::max(pb[i] / (*pc)[i], factor);
    }
    for (size_t i = 0; i < pc->size(); ++i) {
        (*pc)[i] *= factor;
    }
    return true;
}

bool DistributionDistance::calculate(FeatureValueProto* fea) const {
    if (fea ==  NULL || !fea->has_bucket_idx()) {
        CFATAL_LOG("input fea is NULL or no bucket_idx");
        return false;
    }
    if (fea->bucket_idx() >= _stand_prob.size() 
            || fea->buckets_size() != (int)_stand_prob.size()) {
        CFATAL_LOG("invalid fea, bucket idx(%u), bucket size(%u), stand_pb size(%u)",
                fea->bucket_idx(), fea->buckets_size(), _stand_prob.size());
        return false;
    }

    fea->set_valid(true);
    int64_t cur_count = 0L;
    for (int i = 0; i < fea->buckets_size(); ++i) {
        if (fea->buckets(i).idx() >= _stand_prob.size()) {
            CWARNING_LOG("invalid bucket idx(%u) >= _stand_prob.size(%u)",
                    fea->buckets(i).idx(), _stand_prob.size());
            return false;
        }
        cur_count += fea->buckets(i).count();
    }
    std::vector<double> pc(_stand_prob.size(), 0.0);
    if (cur_count != 0) {
        for (int i = 0; i < fea->buckets_size(); ++i) {
            pc[fea->buckets(i).idx()] = 1.0 * fea->buckets(i).count() / cur_count;
        }
    }
    if (!magnify_dis(_stand_prob, &pc)) {
        CWARNING_LOG("magnify_distance_fail, please check!");
        return false;
    }
    double dist = DBL_EPSILON;
    uint32_t idx = fea->bucket_idx();
    if (_distance_operator && _stand_prob[idx] > DBL_EPSILON) {
        dist = (_distance_operator)(_stand_prob, pc, idx);
    }
    fea->set_value(boost::lexical_cast<std::string>(dist));
    fea->set_valid(cur_count >= _count_threshold);
    fea->set_in_anomaly_care(pc[idx] >= _stand_prob[idx]);
    return true;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */
