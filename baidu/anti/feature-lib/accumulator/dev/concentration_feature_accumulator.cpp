// Copyright 2015 Baidu Inc. All Right Reserved.
// Author <PERSON> (lijie<PERSON>@baidu.com)
// 
// brief: 

#include "concentration_feature_accumulator.h"
#include <gflags/gflags.h>
#include "boost/lexical_cast.hpp"
#include <stdlib.h>

namespace anti {
namespace themis {
namespace feature_lib {

bool ConcentrationFeatureAccumulator::init(const comcfg::ConfigUnit& conf) {
    int64_t window_len = 0L;
    try {
        _feature_id = conf["feature_id"].to_uint64();
        _version = conf["version"].to_uint64();
        _top = conf["top"].to_uint64();
        _remain = conf["remain"].to_int64();
        window_len = conf["window_length"].to_int64();
        const uint32_t DEFAULT_REMAIN_DENOMINATOR = 0U;
        uint32_t remain_numerator = DEFAULT_REMAIN_DENOMINATOR;
        conf["remain_numerator"].get_uint32(&remain_numerator, DEFAULT_REMAIN_DENOMINATOR);
        _remain_numerator = remain_numerator != DEFAULT_REMAIN_DENOMINATOR;
        const uint32_t DEFAULT_ACC_MAIN_VIEW = 0U;
        uint32_t acc_main_view = DEFAULT_ACC_MAIN_VIEW;
        conf["acc_main_view"].get_uint32(&acc_main_view, DEFAULT_ACC_MAIN_VIEW);
        _acc_main_view = acc_main_view != DEFAULT_ACC_MAIN_VIEW;
        // 是否获取top信息，包括rank, value,count
        const uint32_t DEFAULT_NEED_TOP_DATA_VIEWS = 0U;
        uint32_t need_top_data_views = DEFAULT_NEED_TOP_DATA_VIEWS;
        conf["need_top_data_views"].get_uint32(&need_top_data_views, 
                    DEFAULT_NEED_TOP_DATA_VIEWS);
        _need_top_data_views = need_top_data_views != DEFAULT_NEED_TOP_DATA_VIEWS;
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    }
    const int DEFAULT_STEP_NUM = 4;
    long long step_length = 0L;
    if (conf["step_length"].get_int64(&step_length, window_len / DEFAULT_STEP_NUM) != 0) {
        CWARNING_LOG("feature_id(%lu) no step_length, using %ld", 
                _feature_id, window_len / DEFAULT_STEP_NUM);
    }

    if (window_len <= 0L 
            || step_length <= 0L 
            || window_len % step_length != 0) {
        CFATAL_LOG("invalid window_len(%ld) step_length(%ld)", 
                window_len, step_length);
        return false;
    }

    const int CACHE_STEP_NUM = 1;
    int max_step_num = CACHE_STEP_NUM + window_len / step_length;
    if (!_window.init(step_length, max_step_num, window_len)) {
        CWARNING_LOG("call window init(%ld, %ld, %ld) fail",
                step_length, max_step_num, window_len);
        return false;
    }
    if (!_segment.init(window_len)) {
        CWARNING_LOG("call segment init(%ld) fail", window_len);
        return false;
    }

    CWARNING_LOG("init ConcentrationFeatureAccumulator(%lu) success", _feature_id);
    return true;
}

void ConcentrationFeatureAccumulator::uninit() {
    _window.uninit();
    _feature_id = 0LU;
    _version = 0LU;
    _top = 0LU;
    _remain = 0L;
    _remain_numerator = false;
    _acc_main_view = false;
    _need_top_data_views = false;
}

bool ConcentrationFeatureAccumulator::update(
        const FeatureValueProto& fea) {
    if (!_check_feature(fea)) {
        CFATAL_LOG("call _check_feature fail");
        return false;
    }
    if (fea.has_data_view_sign()) {
        ConItem item;
        if (!_init_con_item(&item, fea)) {
            CWARNING_LOG("init_con_item failed! feature_id=%lu", _feature_id);
            return false;        
        }
        std::lock_guard<std::mutex> locker(_mutex);
        return _window.enter(fea.view_sign(), fea.coord(), item);
    } else if (_acc_main_view) {
        std::lock_guard<std::mutex> locker(_mutex);
        return _segment.enter(SegLimitNode(fea.view_sign(), fea.coord()), NULL);
    }
    CFATAL_LOG("invalid fea(%lu) value_sign(%lu)", feature_id(), fea.view_sign());
    return false;
}

bool ConcentrationFeatureAccumulator::_init_con_item(ConItem* item, const FeatureValueProto& fea) {
    if (item == NULL) {
        CFATAL_LOG("input item==NULL or fea == NULL or call _check_feature fail");
        return false;
    } 
    if (_need_top_data_views) {
        if (!fea.has_data_view_value()) {
            CWARNING_LOG("need_top_data_views,fea should has data_view_value!");
            return false;
        }
        if (!item->init(_top, 1LU, fea.data_view_sign(), fea.data_view_value())) {
            CWARNING_LOG("init ConItem failed.");
            return false;
        }
        return true;
    }
    return item->init(_top, 1LU, fea.data_view_sign());
}

bool ConcentrationFeatureAccumulator::update_and_query(FeatureValueProto* fea) {
    if (fea == NULL || !_check_feature(*fea)) {
        CFATAL_LOG("input fea == NULL or call _check_feature fail");
        return false;
    }
    if (fea->has_data_view_sign()) {
        ConItem item;
        if (!_init_con_item(&item, *fea)) {
            CWARNING_LOG("init ConItem failed, feature_id = %lu", _feature_id);
            return false;
        }
        const ConItem* res = NULL;
        const SegLimitNode* seg_res = NULL;
        std::lock_guard<std::mutex> locker(_mutex);
        if (!_window.enter(fea->view_sign(), fea->coord(), item, &res)) {
            CWARNING_LOG("enter sliding window fail");
            return false;
        }
        if (_acc_main_view) {
            seg_res = _segment.query_segment(fea->view_sign());
        }
        return _fillup_feature(res, seg_res, fea);
    } else if (_acc_main_view) {
        std::lock_guard<std::mutex> locker(_mutex);
        if (!_segment.enter(SegLimitNode(fea->view_sign(), fea->coord()), NULL)) {
            CWARNING_LOG("enter click segment fail");
            return false;
        }
        return _fillup_feature(NULL, NULL, fea);
    }
    CFATAL_LOG("invalid fea(%lu) value_sign(%lu)", feature_id(), fea->view_sign());
    return false;
}

bool ConcentrationFeatureAccumulator::query(FeatureValueProto* fea) const {
    if (fea == NULL || !_check_feature(*fea)) {
        CFATAL_LOG("input fea == NULL or call _check_feature fail");
        return false;
    }
    if (fea->has_data_view_sign()) {
        const SegLimitNode* seg_node = NULL;
        std::lock_guard<std::mutex> locker(_mutex);
        const ConItem* node = _window.query_segment(fea->view_sign(), fea->coord());
        if (_acc_main_view) {
            seg_node = _segment.query_segment(fea->view_sign());
        }
        return node == NULL ? false : _fillup_feature(node, seg_node, fea);
    } else if (!_acc_main_view) {
        CFATAL_LOG("invalid fea(%lu) value_sign(%lu)", feature_id(), fea->view_sign());
    }
    return false;
}

bool ConcentrationFeatureAccumulator::query(
        const FValQRequestProto& request, 
        FValQResponseProto* response) const {
    if (response == NULL || !request.has_view_sign()) {
        CFATAL_LOG("request or response invalid, feture_id(%lu)", _feature_id);
        return false;
    }
    std::lock_guard<std::mutex> locker(_mutex);
    const ConItem* item = _window.query_segment(request.view_sign());
    if (item == NULL) {
        CDEBUG_LOG("query view_sign(%lu) fail, feature_id(%lu)", request.view_sign(), _feature_id);
        return true;
    }
    int64_t cumulant = _acc_main_view ? 
            _segment.query_segment(request.view_sign())->cumulant : item->cumulant();
    double topk_ratio = cumulant != 0 ? item->kcumulant() * 1.0 / cumulant : 1.0;
    response->add_value(std::to_string(topk_ratio));
    return true;
}

bool ConcentrationFeatureAccumulator::_check_feature(
        const FeatureValueProto& fea) const {
    if (fea.feature_id() != _feature_id
            || fea.feature_type() != FeatureValueProto::CONCENTRATION) {
        CFATAL_LOG("feature_id(%lu) != _feature_id(%lu) || invalid feature_type(%d), logid(%lu)", 
                fea.feature_id(), _feature_id, fea.feature_type(), fea.log_id());
        return false;
    }
    return true;
}

bool ConcentrationFeatureAccumulator::_fillup_feature(
        const ConItem* item,
        const SegLimitNode* seg_item,
        FeatureValueProto* fea) const {
    if (item == NULL) {
        fea->set_filter_count(0);
        fea->set_refer_count(0);
        fea->set_valid(false);
        fea->set_value("0");
        fea->set_in_filter(false);
        return true;
    }
    int64_t cumulant = _acc_main_view && seg_item != NULL ? seg_item->cumulant : item->cumulant();
    fea->set_filter_count(item->kcumulant());
    fea->set_refer_count(cumulant);
    fea->set_in_filter(item->in_topk(fea->data_view_sign()));
    fea->set_valid(_remain_numerator ? 
            fea->filter_count() > _remain : fea->refer_count() > _remain);

    double topk_ratio = cumulant != 0 ? item->kcumulant() * 1.0 / cumulant : 1.0;
    fea->set_value(std::to_string(topk_ratio));
    // 获取top信息。目前为评估平台所需
    if (_need_top_data_views) {
        std::vector<ValueCumNode> topk_vec;
        if (!item->get_topk_value_cum(&topk_vec)) {
            CWARNING_LOG("get_topk_Value_Cum faild. feature_id = %lu", fea->feature_id());
            return false;        
        }
        uint64_t rank = 0LU;
        for (auto vec_it = topk_vec.begin(); vec_it != topk_vec.end(); ++vec_it, ++rank) {
            FeatureValueProto::ViewCountProto* view_count =
                fea->add_top_data_views();
            view_count->set_rank(rank);
            view_count->set_count(vec_it->value());
            view_count->set_value(vec_it->key());
        }
    }
    return true;
}

bool ConcentrationFeatureAccumulator::load(PosixIoReaderInterface *reader) {
    uint64_t ver = 0LU;
    if (reader->read(&ver, sizeof(ver)) != static_cast<int>(sizeof(ver))) {
        CWARNING_LOG("load version fail");
        return false;
    }

    // version > checkpoint version return true directly
    if (ver < _version) {
        return true;
    }
    std::lock_guard<std::mutex> locker(_mutex);
    if (!_window.deserialize(reader)) {
        CWARNING_LOG("call window deserialize fail, feature_id(%lu)", _feature_id);
        return false;
    }
    if (_acc_main_view && !_segment.deserialize(reader)) {
        CWARNING_LOG("call segment deserialize fail, feature_id(%lu)", _feature_id);
        return false;
    }
    return true;
}

bool ConcentrationFeatureAccumulator::dump(PosixIoWriterInterface *writer) const {
    if (writer->write(&_version, sizeof(_version)) != sizeof(_version)) {
        CWARNING_LOG("write version(%lu) fail, feature_id(%lu)", _version, _feature_id);
        return false;
    }
    std::lock_guard<std::mutex> locker(_mutex);
    if (!_window.serialize(writer)) {
        CWARNING_LOG("call window serialize fail, feature_id(%lu)", _feature_id);
        return false;
    }
    if (_acc_main_view && !_segment.serialize(writer)) {
        CWARNING_LOG("call segment serialize fail, feature_id(%lu)", _feature_id);
        return false;
    }
    return true;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti
