// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
//
// @File: distribution_feature_accumulator.cpp
// @Last modified: 2018-01-23 21:01:25
// @Brief:

#include "distribution_feature_accumulator.h"
#include <algorithm>
#include <boost/algorithm/string.hpp>
#include <boost/lexical_cast.hpp>

namespace anti {
namespace themis {
namespace feature_lib {

DECLARE_bool(enable_mem_pool_for_acc);

bool DistributionFeatureAccumulator::_init(const comcfg::ConfigUnit& conf) {
    int64_t window_length = 0L;
    try {
        _feature_id = conf["feature_id"].to_uint64();
        _version = conf["version"].to_uint64();
        window_length = conf["window_length"].to_int64();
        std::string points(conf["interval_endpoints"].to_cstr());
        _bucket_num = std::count(points.begin(), points.end(), ',') + 2;
        uint32_t simple_dis = 0U;
        conf["simple_distribution"].get_uint32(&simple_dis, 0U);
        _simple_distribution = simple_dis == 1U ? true : false;
        if (_bucket_num > MAX_BUCKET_NUM) {
            CWARNING_LOG("_bucket_num(%d) is greater than MAX_BUCKET_NUM(%d)",
                    _bucket_num, MAX_BUCKET_NUM);
            return false;
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std::Exception : %s", e.what());
        return false;
    }
    _distribution_distance.reset(new(std::nothrow) DistributionDistance());
    if (!_distribution_distance || 
            !_distribution_distance->init(conf)) {
        _distribution_distance.reset();
        CWARNING_LOG("new _distribution_distance or init failed! feaid [%lu]", _feature_id);
    }
    const int DEFAULT_MODE = 0;
    int mode = 0;
    if (conf["click_mode"].get_int32(&mode, DEFAULT_MODE)) {
        CWARNING_LOG("feature_id(%lu) no click_mode, using %d",
                _feature_id, DEFAULT_MODE);
    }
    _click_mode = mode != 0 ? true : false;

    const int DEFAULT_STEP_NUM = 4;
    long long step_length = 0L;
    if (conf["step_length"].get_int64(&step_length, window_length / DEFAULT_STEP_NUM) != 0) {
        CWARNING_LOG("feature_id(%lu) no step_length, using %ld",
                _feature_id, window_length / DEFAULT_STEP_NUM);
    }

    if (window_length <= 0L
            || step_length <= 0L
            || window_length % step_length != 0) {
        CFATAL_LOG("invalid window_length(%ld) step_length(%ld)",
                window_length, step_length);
        return false;
    }

    // 决定是否使用内存池
    _use_mem_pool = FLAGS_enable_mem_pool_for_acc;

    if (!_click_mode) {
        const int CACHE_STEP_NUM = 1;
        int max_step_num = CACHE_STEP_NUM + window_length / step_length;

        if (_use_mem_pool) {
            if (!_pool_window.init(step_length, max_step_num, window_length)) {
                CWARNING_LOG("call pool window init(%ld, %ld, %ld) fail", step_length, max_step_num, window_length);
                return false;
            }
        } else {
            if (!_std_window.init(step_length, max_step_num, window_length)) {
                CWARNING_LOG("call window init(%ld, %ld, %ld) fail", step_length, max_step_num, window_length);
                return false;
            }
        }
    } else {
        if (!_seg.init(window_length)) {
            CWARNING_LOG("call click segment init fail, seg_len=%ld",
                    window_length);
            return false;
        }
    }
    CWARNING_LOG("init DistributionFeatureAccumulator(%lu) success", _feature_id);
    return true;
}

void DistributionFeatureAccumulator::_uninit() {
    if (!_click_mode) {
        if (_use_mem_pool) {
            _pool_window.uninit();
        } else {
            _std_window.uninit();
        }
    } else {
        _seg.uninit();
    }
    _feature_id = 0LU;
    _version = 0LU;
    _bucket_num = 0;
    _click_mode = false;
    _use_mem_pool = false;
}

bool DistributionFeatureAccumulator::_update(const FeatureValueProto& fea) {
    if (!_check_feature(fea)) {
        CFATAL_LOG("call _check_feature fail");
        return false;
    }

    if (_click_mode) {
        return _seg.enter(
                DistributionLimitNode(fea.view_sign(), fea.coord(), fea.bucket_idx(), 1L),
                NULL);
    }

    DistributionItem di;
    di[static_cast<int>(fea.bucket_idx())] = 1L;
    if (_use_mem_pool) {
        return _pool_window.enter(fea.view_sign(), fea.coord(), di);
    } else {
        return _std_window.enter(fea.view_sign(), fea.coord(), di);
    }
}

bool DistributionFeatureAccumulator::_update_and_query(FeatureValueProto* fea) {
    if (fea == NULL || !_check_feature(*fea)) {
        CFATAL_LOG("input fea == NULL or call _check_feature fail");
        return false;
    }

    if (_click_mode) {
        const DistributionLimitNode* res = NULL;
        if (!_seg.enter(
                DistributionLimitNode(fea->view_sign(), fea->coord(), fea->bucket_idx(), 1L),
                &res)) {
            CWARNING_LOG("call seg enter fail, feature_id(%lu), coord(%ld)",
                    _feature_id, fea->coord());
            return false;
        }
        // dpstat(unreasonable):
        //     1. update coord
        //     2. check threshold
        //     3. update each bucket count
        // themis:
        //     1. update coord and each bucket count
        //     2. fea.buckets[cur_idx] -= 1
        _set_feature(res, fea);
        if (!_simple_distribution) {
            uint32_t cur_idx = fea->bucket_idx();
            fea->mutable_buckets(cur_idx)->set_count(res->buckets[cur_idx] - 1L);
        }
        return true;
        // return _set_feature(res, fea);
    }

    DistributionItem in_di;
    in_di[static_cast<int>(fea->bucket_idx())] = 1L;
    const DistributionItem* di = NULL;
    bool enter_success = false;
    if (_use_mem_pool) {
        enter_success = _pool_window.enter(fea->view_sign(), fea->coord(), in_di, &di);
    } else {
        enter_success = _std_window.enter(fea->view_sign(), fea->coord(), in_di, &di);
    }
    if (!enter_success) {
        CWARNING_LOG("call window enter fail, feature_id(%lu), coord(%ld)", _feature_id, fea->coord());
        return false;
    }
    return _set_feature(di, fea);
}

bool DistributionFeatureAccumulator::_query(FeatureValueProto* fea) const {
    if (fea == NULL || !_check_feature(*fea)) {
        CFATAL_LOG("input fea == NULL or call _check_feature fail");
        return false;
    }

    if (_click_mode) {
        return _set_feature(_seg.query_segment(fea->view_sign()), fea);
    }
    const DistributionItem* di = NULL;
    if (_use_mem_pool) {
        di = _pool_window.query_segment(fea->view_sign(), fea->coord());
    } else {
        di = _std_window.query_segment(fea->view_sign(), fea->coord());
    }
    return di == NULL ? false : _set_feature(di, fea);
}

bool DistributionFeatureAccumulator::_set_feature(
        const DistributionItem* item,
        FeatureValueProto* fea) const {
    if (item == NULL) {
        for (int i = 0; i < _bucket_num; ++i) {
            FeatureValueProto::BucketProto* bucket = fea->add_buckets();
            bucket->set_idx(i);
            bucket->set_count(0);
        }
        return true;
    }

    for (int i = 0; i < _bucket_num; ++i) {
        FeatureValueProto::BucketProto* bucket = fea->add_buckets();
        bucket->set_idx(i);
        bucket->set_count((*item)[i]);
    }

    if (_distribution_distance.get() != NULL && !_distribution_distance->calculate(fea)) {
        CWARNING_LOG("distribution distance calculate error, feature_id(%lu).",
                fea->feature_id());
        return false;
    }
    return true;
}

bool DistributionFeatureAccumulator::_set_feature(
        const DistributionLimitNode* node,
        FeatureValueProto* fea) const {
    if (node == NULL) {
        for (int i = 0; i < _bucket_num; ++i) {
            FeatureValueProto::BucketProto* bucket = fea->add_buckets();
            bucket->set_idx(i);
            bucket->set_count(0);
        }
        return true;
    }

    for (int i = 0; i < _bucket_num; ++i) {
        FeatureValueProto::BucketProto* bucket = fea->add_buckets();
        bucket->set_idx(i);
        bucket->set_count(node->buckets[i]);
    }

    if (_distribution_distance.get() != NULL && !_distribution_distance->calculate(fea)) {
        CWARNING_LOG("distribution distance calculate error, feature_id(%lu).", 
                fea->feature_id());
        return false;
    }

    return true;
}

bool DistributionFeatureAccumulator::_check_feature(const FeatureValueProto& fea) const {
    if (fea.feature_id() != _feature_id
        || (fea.feature_type() != FeatureValueProto::DISTRIBUTION
            && fea.feature_type() != FeatureValueProto::BEHAVIOR_DISTRIBUTION)) {
        CFATAL_LOG("feature_id(%lu) != _feature_id(%lu) or invalid feature_type(%d), logid(%lu)",
                fea.feature_id(), _feature_id, fea.feature_type(), fea.log_id());
        return false;
    }

    if (!fea.has_bucket_idx() || fea.bucket_idx() >= static_cast<uint32_t>(_bucket_num)) {
        CWARNING_LOG("no bucket_idx or bucket_idx(%u) > _bucket_num(%d) "
                "feature_id(%lu), log_id(%lu)", fea.bucket_idx(), _bucket_num,
                fea.feature_id(), fea.log_id());
        return false;
    }
    return true;
}

bool DistributionFeatureAccumulator::_load(PosixIoReaderInterface *reader) {
    uint64_t ver = 0LU;
    if (reader->read(&ver, sizeof(ver)) != static_cast<int>(sizeof(ver))) {
        CWARNING_LOG("load version fail");
        return false;
    }

    // version > checkpoint version return true directly
    if (ver < _version) {
        return true;
    }

    if (_click_mode) {
        if (!_seg.deserialize(reader)) {
            CWARNING_LOG("call segment deserialize fail, feature_id(%lu)", _feature_id);
            return false;
        }
    } else {
        bool deserialize_success = false;
        if (_use_mem_pool) {
            deserialize_success = _pool_window.deserialize(reader);
        } else {
            deserialize_success = _std_window.deserialize(reader);
        }
        if (!deserialize_success) {
            CWARNING_LOG("call window deserialize fail, feature_id(%lu)", _feature_id);
            return false;
        }
    }
    return true;
}

bool DistributionFeatureAccumulator::_dump(PosixIoWriterInterface *writer) const {
    if (writer->write(&_version, sizeof(_version)) 
            != static_cast<int>(sizeof(_version))) {
        CWARNING_LOG("write version(%lu) fail, feature_id(%lu)", 
                _version, _feature_id);
        return false;
    }

    if (_click_mode) {
        if (!_seg.serialize(writer)) {
            CWARNING_LOG("call segment serialize fail, feature_id(%lu)", _feature_id);
            return false;
        }
    } else {
        bool serialize_success = false;
        if (_use_mem_pool) {
            serialize_success = _pool_window.serialize(writer);
        } else {
            serialize_success = _std_window.serialize(writer);
        }
        if (!serialize_success) {
            CWARNING_LOG("call window serialize fail, feature_id(%lu)", _feature_id);
            return false;
        }
    }
    return true;
}

void DistributionFeatureAccumulator::print_monitor_log() const {
    if (_click_mode) {
        // SEG模式：无内存统计
        uint64_t element_count = _seg.window_view_sign_size();
        CNOTICE_LOG("Feature: ID=%lu, Type=DISTRIBUTION, Elements=%lu, Memory=N/A", _feature_id, element_count);

    } else if (_use_mem_pool) {
        // POOL_WINDOW模式：详细内存统计
        bsl::var::Dict pool_dict;
        bsl::ResourcePool rp;
        _pool_window.monitor(pool_dict, rp);

        // === 元素统计 ===
        uint64_t total_elements = pool_dict["TOTAL_ELEMENTS"].to_uint64();
        uint64_t segment_elements = pool_dict["SEGMENT_ELEMENTS"].to_uint64();
        uint64_t window_elements = pool_dict["WINDOW_ELEMENTS"].to_uint64();
        
        // === 内存统计 ===
        uint64_t data_mem = pool_dict["DATA_MEM"].to_uint64();
        uint64_t pool_mgmt_overhead = pool_dict["POOL_MGMT_OVERHEAD"].to_uint64();
        uint64_t sliding_window_overhead = pool_dict["SLIDING_WINDOW_OVERHEAD"].to_uint64();
        uint64_t accumulator_overhead = sizeof(DistributionFeatureAccumulator);
        
        uint64_t total_overhead = pool_mgmt_overhead + sliding_window_overhead + accumulator_overhead;
        uint64_t total_mem = data_mem + total_overhead;
        
        double avg_mem_per_element = total_elements > 0 ? 
            static_cast<double>(total_mem) / total_elements : 0.0;

        // === 第一行：简洁格式 ===
        CNOTICE_LOG(
            "Feature: ID=%lu, Type=DISTRIBUTION, Elements=%lu(Seg=%lu,Win=%lu), "
            "Memory(Total=%lu,Data=%lu,Overhead=%lu,AvgPerElement=%.2f)",
            _feature_id, total_elements, segment_elements, window_elements,
            total_mem, data_mem, total_overhead, avg_mem_per_element);

        // === 第二行：详细格式 ===
        CNOTICE_LOG(
            "Feature: ID=%lu, Details: Coords[SegmentLen=%ld,OldestCoord=%ld,LatestCoord=%ld],"
            "SegmentHashMap[Size=%lu,BucketCount=%lu,LoadFactor=%.3f],"
            "MemPool[NodeNum=%lu,DataMem=%lu,MgmtOverhead=%lu]",
            _feature_id,
            pool_dict["SEGMENT_LENGTH"].to_int64(),
            pool_dict["OLDEST_COORD"].to_int64(), 
            pool_dict["LATEST_COORD"].to_int64(),
            pool_dict["SEGMENT_SIZE"].to_uint64(),
            pool_dict["SEGMENT_BUCKET_COUNT"].to_uint64(),
            pool_dict["SEGMENT_LOAD_FACTOR"].to_double(),
            total_elements,
            data_mem,
            pool_mgmt_overhead);

    } else {
        // STD_WINDOW模式：无内存统计
        uint64_t segment_size = _std_window.segment_view_sign_size();
        uint64_t window_size = _std_window.window_view_sign_size();
        uint64_t element_count = segment_size + window_size;

        CNOTICE_LOG("Feature: ID=%lu, Type=DISTRIBUTION, Elements=%lu, Memory=N/A", _feature_id, element_count);
    }
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti
