// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: <PERSON>(<EMAIL>)
// 
// @Brief: 

#include <vector>
#include <gtest/gtest.h>
#include "concentration_feature_accumulator.h"
#include "posix_themis_io.h"
#include "ckpt_test_util.h"

namespace anti {
namespace themis {
namespace feature_lib {

class ConcentrationFeatureAccumulatorTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
        ASSERT_TRUE(_conf.load("./conf", "accumulator_test.conf") == 0);
    
        int64_t coords[] = {115L, 135L, 155L, 175L, 175L};
        std::string data_value_list[] = {"value_0", "value_1", "value_0", "value_3", "value_4"};
        for (uint32_t i = 0U; i < 5U; ++i) {
            FeatureValueProto fea;
            fea.set_feature_id(120LU);
            fea.set_view_sign(123456LU);
            fea.set_feature_type(FeatureValueProto::CONCENTRATION);
            fea.set_joinkey(119LU);
            fea.set_log_id(120LU);
            fea.set_log_time(1111111111LU);
            fea.set_coord(coords[i]);
            fea.set_data_view_sign(2222222LU);
            fea.set_data_view_value(data_value_list[i]);
            _feas.push_back(fea);
        }
        _old_fea = _feas[0];
        _old_fea.set_coord(0L);

        FeatureValueProto fea;
        fea.set_feature_id(123456789LU);
        _inv_feas.push_back(fea);

        fea.set_feature_id(120LU);
        fea.set_feature_type(FeatureValueProto::RATIO);
        _inv_feas.push_back(fea);
    }
    virtual void TearDown() {
        _feas.clear();
        _inv_feas.clear();
    }

private:
    typedef anti::themis::common_lib::ConItem ConcentrationItem;
    ConcentrationFeatureAccumulator _obj;
    comcfg::Configure _conf;

    std::vector<FeatureValueProto> _feas;
    FeatureValueProto _old_fea;
    std::vector<FeatureValueProto> _inv_feas;
};

TEST_F(ConcentrationFeatureAccumulatorTestSuite, construction_case) {
    EXPECT_EQ(0LU, _obj.feature_id());
    EXPECT_EQ(0LU, _obj._version);
    EXPECT_FALSE(_obj._remain_numerator);
    EXPECT_FALSE(_obj._acc_main_view);
    EXPECT_FALSE(_obj._need_top_data_views);
}

TEST_F(ConcentrationFeatureAccumulatorTestSuite, init_case) {
    uint32_t num = _conf["invalid_concentration"]["feature"].size();
    ASSERT_LE(1U, num);
    for (uint32_t i = 0U; i < num; ++i) {
        EXPECT_FALSE(_obj.init(_conf["invalid_concentration"]["feature"][i]));
        _obj.uninit();
    }

    num = _conf["valid_concentration"]["feature"].size();
    ASSERT_LE(3U, num);
    bool acc_main_view[] = {false, true, true};
    bool need_top_data_views[] = {false, false, true};
    for (uint32_t i = 0U; i < num; ++i) {
        ASSERT_TRUE(_obj.init(_conf["valid_concentration"]["feature"][i]));
        EXPECT_EQ(120LU, _obj.feature_id());
        EXPECT_EQ(1LU, _obj._version);
        EXPECT_TRUE(_obj._top > 0U);
        EXPECT_FALSE(_obj._remain_numerator);
        EXPECT_EQ(acc_main_view[i], _obj._acc_main_view);
        EXPECT_EQ(need_top_data_views[i], _obj._need_top_data_views);
        _obj.uninit();
    }
    EXPECT_EQ(0LU, _obj.feature_id());
    EXPECT_EQ(0LU, _obj._version);
    EXPECT_EQ(0LU, _obj._top);
    EXPECT_FALSE(_obj._remain_numerator);
    EXPECT_FALSE(_obj._acc_main_view);
    EXPECT_FALSE(_obj._need_top_data_views);
}

TEST_F(ConcentrationFeatureAccumulatorTestSuite, test_update_old_node) {
    ASSERT_TRUE(_obj.init(_conf["valid_concentration"]["feature"][0]));
    ASSERT_TRUE(_obj.update(_feas[4]));
    const ConcentrationItem* item = _obj._window.query_segment(
            static_cast<uint64_t>(_feas[4].view_sign()));
    
    ASSERT_FALSE(_obj.update(_old_fea));
    ASSERT_EQ(1U, item->cumulant());
    FeatureValueProto feat = _feas[1];
    ASSERT_TRUE(_obj.query(&feat));
    EXPECT_TRUE(feat.in_filter());
    EXPECT_EQ(1U, feat.filter_count());
    EXPECT_EQ(1.0, feat.filter_count() * 1.0 / feat.refer_count());
    _obj.uninit();
    EXPECT_EQ(0U, feat.top_data_views_size());
}

TEST_F(ConcentrationFeatureAccumulatorTestSuite, test_update_top1) {
    ASSERT_TRUE(_obj.init(_conf["valid_concentration"]["feature"][0]));
    // invalid fea
    EXPECT_FALSE(_obj.query(NULL));

    for (uint32_t i = 0U; i< _inv_feas.size(); ++i) {
        EXPECT_FALSE(_obj.update(_inv_feas[i]));
        EXPECT_FALSE(_obj.query(&_inv_feas[i]));
    }
    
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(_feas[i]));
    }
    
    ASSERT_FALSE(_obj.update(_old_fea));
    FeatureValueProto feat = _feas[1];
    ASSERT_TRUE(_obj.query(&feat));
    EXPECT_TRUE(feat.in_filter());
    EXPECT_EQ(5U, feat.filter_count());
    EXPECT_EQ(1U, feat.filter_count() * 1.0 / feat.refer_count());

    std::vector<FeatureValueProto> other_feas;
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        feat = _feas[i];
        feat.set_data_view_sign(33333U);
        other_feas.push_back(feat);
    }
    feat = _feas[1];
    for (uint32_t i = 0U; i < other_feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(other_feas[i]));
        ASSERT_TRUE(_obj.query(&other_feas[i]));
        EXPECT_FALSE(other_feas[i].in_filter());
        EXPECT_EQ(5U, other_feas[i].filter_count());
        EXPECT_EQ(5.0 / (5.0 + i + 1), 
                other_feas[i].filter_count() * 1.0 / other_feas[i].refer_count());
    }

    for (uint32_t i = 0U; i < other_feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(other_feas[i]));
        ASSERT_TRUE(_obj.query(&other_feas[i]));
        EXPECT_TRUE(other_feas[i].in_filter());
        EXPECT_EQ(5U + i + 1, other_feas[i].filter_count());
        EXPECT_EQ((5.0 + i + 1) / (10.0 + i + 1), 
                other_feas[i].filter_count() * 1.0 / other_feas[i].refer_count());
    }
    
    ASSERT_FALSE(_obj.update(_old_fea));
    // query
    feat = _feas[1];
    ASSERT_TRUE(_obj.query(&feat));
    EXPECT_EQ(10U, feat.filter_count());
    EXPECT_EQ(2.0 / 3.0, feat.filter_count() * 1.0 / feat.refer_count());
    _obj.uninit();
}

TEST_F(ConcentrationFeatureAccumulatorTestSuite, test_update_top3) {
    ASSERT_TRUE(_obj.init(_conf["valid_concentration"]["feature"][1]));
    // invalid fea
    EXPECT_FALSE(_obj.query(NULL));

    for (uint32_t i = 0U; i< _inv_feas.size(); ++i) {
        EXPECT_FALSE(_obj.update(_inv_feas[i]));
        EXPECT_FALSE(_obj.query(&_inv_feas[i]));
    }
    
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(_feas[i]));
    }
    
    ASSERT_FALSE(_obj.update(_old_fea));
    FeatureValueProto feat = _feas[1];
    ASSERT_TRUE(_obj.query(&feat));
    EXPECT_TRUE(feat.in_filter());
    EXPECT_EQ(5U, feat.filter_count());
    EXPECT_EQ(1.0, feat.filter_count() * 1.0 / feat.refer_count());

    std::vector<FeatureValueProto> other_feas[5];
    for (uint32_t i = 0U; i < 5U; ++i) {
        for (uint32_t j = 0U; j <= i; ++j) {
            feat = _feas[i];
            feat.set_data_view_sign(33333U + i);
            other_feas[i].push_back(feat);
        }
    }
    feat = _feas[1];
    for (uint32_t i = 0U; i < 5U; ++i) {
        for (uint32_t j = 0U; j < other_feas[i].size(); ++j) {
            ASSERT_TRUE(_obj.update(other_feas[i][j]));
        }
    }
    ASSERT_TRUE(_obj.query(&feat));
    EXPECT_TRUE(feat.in_filter());
    EXPECT_EQ(14U, feat.filter_count());
    EXPECT_EQ(14.0 / 20.0, feat.filter_count() * 1.0 / feat.refer_count());

    _obj.uninit();
}

TEST_F(ConcentrationFeatureAccumulatorTestSuite, test_update_top3_with_need_top_data_views) {
    ASSERT_TRUE(_obj.init(_conf["valid_concentration"]["feature"][2]));
    EXPECT_TRUE(_obj._need_top_data_views);
    // invalid fea
    EXPECT_FALSE(_obj.query(NULL));
    
    uint64_t data_sign_list[] = {1111111U, 2222222U, 1111111U, 4444444U, 5555555U}; 
    std::vector<FeatureValueProto> other_feas;
    FeatureValueProto feat;
    for (uint32_t i = 0U; i < 5U; ++i) {
        feat = _feas[i];
        feat.set_data_view_sign(data_sign_list[i]);
        other_feas.push_back(feat);
        EXPECT_EQ(data_sign_list[i], feat.data_view_sign());
    }

    for (uint32_t i = 0U; i < other_feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(other_feas[i]));
    }
    feat = other_feas[0];
    EXPECT_EQ(1111111U, feat.data_view_sign());
    EXPECT_EQ("value_0", feat.data_view_value());
    ASSERT_TRUE(_obj.query(&feat));
    EXPECT_TRUE(feat.in_filter());
    EXPECT_EQ(4U, feat.filter_count());
    EXPECT_EQ(4.0 / 5.0, feat.filter_count() * 1.0 / feat.refer_count());
    EXPECT_EQ(3U, feat.top_data_views_size());
    // count相同时，利用value比较大小。
    for (int rank = 0; rank < feat.top_data_views_size(); rank++) {
        if (feat.top_data_views(rank).rank() == 0) {
            EXPECT_EQ(2U, feat.top_data_views(rank).count());
            EXPECT_EQ("value_0", feat.top_data_views(rank).value());
        } 
        if (feat.top_data_views(rank).rank() == 1) {
            EXPECT_EQ(1U, feat.top_data_views(rank).count());
            EXPECT_EQ("value_4", feat.top_data_views(rank).value());
        }
        if (feat.top_data_views(rank).rank() == 2) {
            EXPECT_EQ(1U, feat.top_data_views(rank).count());
            EXPECT_EQ("value_3", feat.top_data_views(rank).value());
        }
    }
    _obj.uninit();
}

TEST_F(ConcentrationFeatureAccumulatorTestSuite, test_get_top) {
    ASSERT_TRUE(_obj.init(_conf["valid_concentration"]["feature"][2]));
    EXPECT_TRUE(_obj._need_top_data_views);
    _obj._need_top_data_views = false;
    
    uint64_t data_sign_list[] = {1111111U, 2222222U, 1111111U, 4444444U, 5555555U}; 
    std::vector<FeatureValueProto> other_feas;
    FeatureValueProto feat;
    for (uint32_t i = 0U; i < 5U; ++i) {
        feat = _feas[i];
        feat.set_data_view_sign(data_sign_list[i]);
        other_feas.push_back(feat);
        EXPECT_EQ(data_sign_list[i], feat.data_view_sign());
    }

    for (uint32_t i = 0U; i < other_feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(other_feas[i]));
    }
    feat = other_feas[0];
    EXPECT_EQ(1111111U, feat.data_view_sign());
    EXPECT_EQ("value_0", feat.data_view_value());
    ASSERT_TRUE(_obj.query(&feat));
    EXPECT_TRUE(feat.in_filter());
    EXPECT_EQ(4U, feat.filter_count());
    EXPECT_EQ(4.0 / 5.0, feat.filter_count() * 1.0 / feat.refer_count());
    EXPECT_EQ(0U, feat.top_data_views_size());

    _obj._need_top_data_views = true;
    feat = other_feas[0];
    EXPECT_EQ(1111111U, feat.data_view_sign());
    EXPECT_EQ("value_0", feat.data_view_value());
    ASSERT_TRUE(_obj.query(&feat));
    EXPECT_EQ(3U, feat.top_data_views_size());
    for (int rank = 0; rank < feat.top_data_views_size(); rank++) {
        if (feat.top_data_views(rank).rank() == 0) {
            EXPECT_EQ(2U, feat.top_data_views(rank).count());
            EXPECT_EQ("_", feat.top_data_views(rank).value());
        } 
        if (feat.top_data_views(rank).rank() == 1) {
            EXPECT_EQ(1U, feat.top_data_views(rank).count());
            EXPECT_EQ("_", feat.top_data_views(rank).value());
        }
        if (feat.top_data_views(rank).rank() == 2) {
            EXPECT_EQ(1U, feat.top_data_views(rank).count());
            EXPECT_EQ("_", feat.top_data_views(rank).value());
        }
    }

    feat = other_feas[0];
    ASSERT_TRUE(_obj.query(&feat));
    EXPECT_EQ(3U, feat.top_data_views_size());
    for (int rank = 0; rank < feat.top_data_views_size(); rank++) { 
        if (feat.top_data_views(rank).rank() == 0) {
            EXPECT_EQ(2U, feat.top_data_views(rank).count());
            EXPECT_EQ("_", feat.top_data_views(rank).value());
        } 
        if (feat.top_data_views(rank).rank() == 1) {
            EXPECT_EQ(1U, feat.top_data_views(rank).count());
            EXPECT_EQ("_", feat.top_data_views(rank).value());
        }
        if (feat.top_data_views(rank).rank() == 2) {
            EXPECT_EQ(1U, feat.top_data_views(rank).count());
            EXPECT_EQ("_", feat.top_data_views(rank).value());
        }
    }
    _obj.uninit();
}

TEST_F(ConcentrationFeatureAccumulatorTestSuite, test_update_and_query_top3) {
    ASSERT_TRUE(_obj.init(_conf["valid_concentration"]["feature"][1]));
    // invalid fea
    EXPECT_FALSE(_obj.query(NULL));

    for (uint32_t i = 0U; i< _inv_feas.size(); ++i) {
        EXPECT_FALSE(_obj.update_and_query(&_inv_feas[i]));
    }
    
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update_and_query(&_feas[i]));
        EXPECT_EQ(i + 1U, _feas[i].filter_count());
        EXPECT_EQ(1.0, _feas[i].filter_count() * 1.0 / _feas[i].refer_count());
    }

    ASSERT_FALSE(_obj.update_and_query(&_old_fea));

    FeatureValueProto feat;
    std::vector<FeatureValueProto> other_feas[5];
    for (uint32_t i = 0U; i < 5U; ++i) {
        for (uint32_t j = 0U; j <= i; ++j) {
            feat = _feas[i];
            feat.set_data_view_sign(33333U + i);
            other_feas[i].push_back(feat);
        }
    }
    feat = _feas[1];
    uint64_t count = 5U;
    uint64_t total = 5U;
    for (uint64_t i = 0U; i < 5U; ++i) {
        for (uint64_t j = 0U; j < other_feas[i].size(); ++j) {
            ASSERT_TRUE(_obj.update_and_query(&other_feas[i][j]));
            total++;
            if (i < 2) {
                count++;
            } else {
                if (j + 1 > other_feas[i - 2].size()) {
                    count++;
                }
            }
            EXPECT_EQ(count, other_feas[i][j].filter_count());
            EXPECT_EQ(count * 1.0 / total, 
                    other_feas[i][j].filter_count() * 1.0 / other_feas[i][j].refer_count());
        }
    }

    ASSERT_TRUE(_obj.query(&feat));
    EXPECT_TRUE(feat.in_filter());
    EXPECT_EQ(14U, feat.filter_count());
    EXPECT_EQ(14.0 / 20.0, feat.filter_count() * 1.0 / feat.refer_count());
    _obj.uninit();
}

TEST_F(ConcentrationFeatureAccumulatorTestSuite, test_dump_and_load) {
    ASSERT_TRUE(_obj.init(_conf["valid_concentration"]["feature"][1]));
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update_and_query(&_feas[i]));
    }

    ASSERT_TRUE(_obj.query(&_feas[4]));
    remove("./concentration_ckpt_themis.dat");
    auto in_filter = _feas[4].in_filter();
    auto filter_count = _feas[4].filter_count();
    DOING_DUMP();
    _obj.uninit();


    ASSERT_TRUE(_obj.init(_conf["valid_concentration"]["feature"][1]));
    DOING_LOAD();
    ASSERT_TRUE(_obj.query(&_feas[4]));
    ASSERT_EQ(in_filter, _feas[4].in_filter());
    ASSERT_EQ(filter_count, _feas[4].filter_count());
    remove("./concentration_ckpt_themis.dat");
}

TEST_F(ConcentrationFeatureAccumulatorTestSuite, test_rpc_query) {
    ASSERT_TRUE(_obj.init(_conf["valid_concentration"]["feature"][0]));
    for (uint32_t j = 0U; j < _feas.size(); ++j) {
        ASSERT_TRUE(_obj.update_and_query(&_feas[j]));
    }
    
    FValQRequestProto req;
    req.set_feature_id(120LU);
    req.set_view_sign(123456LU);
    ASSERT_FALSE(_obj.query(req, NULL));
    FValQResponseProto res;
    ASSERT_TRUE(_obj.query(req, &res));
    EXPECT_EQ(1, res.value_size());
    EXPECT_STREQ("1.000000", res.value(0).data());
    _obj.uninit();

    ASSERT_TRUE(_obj.init(_conf["valid_concentration"]["feature"][1]));
    _feas[0].clear_data_view_sign();
    _feas[1].clear_data_view_sign();
    for (uint32_t j = 0U; j < _feas.size(); ++j) {
        ASSERT_TRUE(_obj.update_and_query(&_feas[j]));
    }
    res.clear_value();
    ASSERT_TRUE(_obj.query(req, &res));
    EXPECT_EQ(1, res.value_size());
    EXPECT_STREQ("1.500000", res.value(0).data());
    _obj.uninit();
}

TEST_F(ConcentrationFeatureAccumulatorTestSuite, test_main_view) {
    ASSERT_TRUE(_obj.init(_conf["valid_concentration"]["feature"][1]));
    FeatureValueProto feat;
    std::vector<FeatureValueProto> other_feas;
    for (uint32_t i = 0; i < _feas.size(); ++i) {
        feat = _feas[i];
        if (i % 2 == 0) {
            feat.clear_data_view_sign();
        }
        other_feas.emplace_back(feat);
    }
    bool valid[] = {false, true, false, true, false};
    bool in_filter[] = {false, true, false, true, false};
    int64_t filter_count[] = {0, 1, 0, 2, 0};
    int64_t refer_count[] = {0, 1, 0, 2, 0};
    std::string value[] = {"0", "1.000000", "0", "1.000000", "0"};
    for (uint32_t i = 0; i < other_feas.size(); ++i) {
        ASSERT_TRUE(_obj.update_and_query(&other_feas[i]));
        EXPECT_EQ(valid[i], other_feas[i].valid());
        EXPECT_EQ(in_filter[i], other_feas[i].in_filter());
        EXPECT_EQ(filter_count[i], other_feas[i].filter_count());
        EXPECT_EQ(refer_count[i], other_feas[i].refer_count());
        EXPECT_STREQ(value[i].data(), other_feas[i].value().data());
    }
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

