// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: rate_feature_accumulator_test.cpp
// @Last modified: 2017-03-16 14:33:52
// @Brief: 

#include <vector>
#include <gtest/gtest.h>
#include "rate_feature_accumulator.h"
#include "posix_themis_io.h" 
#include "ckpt_test_util.h"

namespace anti {
namespace themis {
namespace feature_lib {

class RateFeatureAccumulatorTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
        ASSERT_TRUE(_conf.load("./conf", "accumulator_test.conf") == 0);

        bool fil[] = {true, false, false, false, true, false};
        bool ref[] = {true, true, true, true, false, true};
        for (uint32_t i = 0U; i < 6U; ++i) {
            FeatureValueProto fea;
            fea.set_feature_id(888LU);
            fea.set_view_sign(123456LU);
            if (i == 0) {
                fea.set_filter_count(100);
                fea.set_feature_type(FeatureValueProto::CPM);
            } else {
                fea.set_feature_type(FeatureValueProto::RATE);
            }
            fea.set_joinkey(119LU);
            fea.set_log_id(120LU);
            fea.set_log_time(1111111111LU);
            fea.set_coord(i);
            fea.set_in_filter(fil[i]);
            fea.set_in_refer(ref[i]);
            _feas.push_back(fea);
        }
        _inv_fea = _feas[0];
        _inv_fea.clear_filter_count();
    }
    virtual void TearDown() {
        _feas.clear();
    }

private:
    RateFeatureAccumulator _obj;
    comcfg::Configure _conf;

    std::vector<FeatureValueProto> _feas;
    FeatureValueProto _inv_fea;
};

TEST_F(RateFeatureAccumulatorTestSuite, construction_case) {
    EXPECT_EQ(0LU, _obj.feature_id());
    EXPECT_EQ(0LU, _obj._version);
    EXPECT_EQ(0L, _obj._time_threshold);
}

TEST_F(RateFeatureAccumulatorTestSuite, init_case) {
    for (uint32_t i = 0U; i < _conf["invalid_kpi"]["feature"].size(); ++i) {
        EXPECT_FALSE(_obj.init(_conf["invalid_kpi"]["feature"][i]));
        _obj.uninit();
    }
    ASSERT_TRUE(_obj.init(_conf["valid_kpi"]["feature"]));
    EXPECT_EQ(888LU, _obj.feature_id());
    EXPECT_EQ(1LU, _obj._version);
    EXPECT_EQ(2L, _obj._remain[RateFeatureAccumulator::NUMERATOR]);
    EXPECT_EQ(3L, _obj._remain[RateFeatureAccumulator::DENOMINATOR]);
    EXPECT_EQ(3, _obj._time_threshold);
    _obj.uninit();
}

TEST_F(RateFeatureAccumulatorTestSuite, _check_feature_fail) {
    _obj._feature_id = 888LU;
    ASSERT_FALSE(_obj._check_feature(_inv_fea));

    _inv_fea.set_in_filter(false);
    _inv_fea.set_in_refer(false);
    ASSERT_FALSE(_obj._check_feature(_inv_fea));

    _inv_fea.set_feature_id(111LU);
    ASSERT_FALSE(_obj._check_feature(_inv_fea));
}

TEST_F(RateFeatureAccumulatorTestSuite, multi_update_and_one_query_case) {
    ASSERT_TRUE(_obj.init(_conf["valid_kpi"]["feature"]));
    ASSERT_FALSE(_obj.update(_inv_fea));
    ASSERT_FALSE(_obj.query(NULL));

    for (uint32_t i = 0; i < 6; ++i) {
        ASSERT_TRUE(_obj.update(_feas[i]));
    }
    FeatureValueProto q = _feas.back();
    ASSERT_TRUE(_obj.query(&q));
    EXPECT_EQ(101L, q.filter_count());
    EXPECT_EQ(5L, q.refer_count());
    EXPECT_TRUE(q.valid());
    EXPECT_STREQ("20.200000", q.value().data());

    // no update before
    q.set_view_sign(654LU);
    ASSERT_TRUE(_obj.query(&q));
    EXPECT_EQ(0L, q.filter_count());
    EXPECT_EQ(0L, q.refer_count());
    EXPECT_FALSE(q.valid());
    EXPECT_STREQ("1.000000", q.value().data());
    _obj.uninit();
}

TEST_F(RateFeatureAccumulatorTestSuite, update_and_query_case) {
    ASSERT_TRUE(_obj.init(_conf["valid_kpi"]["feature"]));
    _obj._remain[RateFeatureAccumulator::NUMERATOR] = 101L;
    ASSERT_FALSE(_obj.update_and_query(NULL));
    FeatureValueProto f = _feas.back();
    bool exp_valid[] = {false, false, true, true, true, true};
    int64_t exp_fil[] = {100L, 100L, 100L, 100L, 101L, 101L};
    int64_t exp_ref[] = {1L, 2L, 3L, 4L, 4L, 5L};
    const char* exp_val[] = {
            "100.000000", "50.000000", "33.333333", 
            "25.000000", "25.250000", "20.200000"};
    for (uint32_t i = 0; i < 6; ++i) {
        ASSERT_TRUE(_obj.update_and_query(&_feas[i]));
        EXPECT_EQ(exp_valid[i], _feas[i].valid());
        EXPECT_EQ(exp_fil[i], _feas[i].filter_count());
        EXPECT_EQ(exp_ref[i], _feas[i].refer_count());
        EXPECT_STREQ(exp_val[i], _feas[i].value().data());
    }

    f.set_coord(1000000L);
    ASSERT_TRUE(_obj.update_and_query(&f));
    EXPECT_FALSE(f.valid());
    EXPECT_EQ(101L, f.filter_count());
    EXPECT_EQ(1L, f.refer_count());
    EXPECT_STREQ("101.000000", f.value().data());

    // update de failed
    f.set_coord(0L);
    ASSERT_FALSE(_obj.update_and_query(&f));

    // update nu failed
    f.set_in_filter(true);
    f.set_in_refer(false);
    f.set_coord(2000000L);
    ASSERT_TRUE(_obj.update_and_query(&f));
    f.set_coord(0L);
    ASSERT_FALSE(_obj.update_and_query(&f));
    _obj.uninit();
}

TEST_F(RateFeatureAccumulatorTestSuite, dump_load_sliding_window_case) {
    ASSERT_TRUE(_obj.init(_conf["valid_kpi"]["feature"]));
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(_feas[i]));
    }
    DOING_DUMP();
    _obj.uninit();
    
    ASSERT_TRUE(_obj.init(_conf["valid_kpi"]["feature"]));
    DOING_LOAD();
    ASSERT_TRUE(_obj.query(&_feas[5]));
    EXPECT_EQ(101L,  _feas[5].filter_count());
    EXPECT_EQ(5L, _feas[5].refer_count());
    EXPECT_EQ(true, _feas[5].valid());
    EXPECT_STREQ("20.200000", _feas[5].value().data());
    _obj.uninit();
    
}

}  // feature_lib
}  // themis
}  // anti

/* vim: set ts=4 sw=4: */

