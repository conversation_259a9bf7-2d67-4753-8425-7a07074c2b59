// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: ch<PERSON><PERSON><PERSON><PERSON>(ch<PERSON><PERSON><PERSON><PERSON>@baidu.com)
// 
// @File: three_level_tree_feature_accumulator_test.cpp
// @Last modified: 2018-06-01 11:45:18
// @Brief: 

#include <vector>
#include <gtest/gtest.h>
#include "three_level_tree_feature_accumulator.h"
#include "posix_themis_io.h" 
#include "ckpt_test_util.h"

namespace anti {
namespace themis {
namespace feature_lib {

class ThreeLevelTreeFeatureAccumulatorTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
        ASSERT_TRUE(_conf.load("./conf", "three_level_test.conf") == 0);
        int64_t tree_coords[] = {0L, 1L, 2L, 3L, 4L, 5L, 6L, 14L, 18L, 19L, 40L, 41L};
        uint64_t tree_data_signs[] =     {1L, 1L, 2L, 2L, 3L, 3L, 8L, 8L, 8L, 9L, 9L, 9L};
        uint64_t tree_cumulate_signs[] = {1L, 2L, 2L, 1L, 3L, 4L, 8L, 8L, 8L, 9L, 9L, 9L};
        for (int i = 0; i < 12; i++) {
            FeatureValueProto fea;
            fea.set_feature_id(1234LU);
            fea.set_view_sign(123LU);
            fea.set_feature_type(FeatureValueProto::THREE_LEVEL_TREE);
            fea.set_joinkey(119LU);
            fea.set_log_id(120LU);
            fea.set_log_time(1111111111LU);
            fea.set_coord(tree_coords[i]);
            fea.set_data_view_sign(tree_data_signs[i]);
            fea.set_cumulate_view_sign(tree_cumulate_signs[i]);
            _tree_feas.push_back(fea);
        }
    }
    virtual void TearDown() {}

private:
    std::vector<FeatureValueProto> _tree_feas;
    comcfg::Configure _conf;
    ThreeLevelTreeFeatureAccumulator _obj;
};

TEST_F(ThreeLevelTreeFeatureAccumulatorTestSuite, test_1) {
    ASSERT_TRUE(_obj.init(_conf["feature"][0]));
    EXPECT_FALSE(_obj.update_and_query(NULL));
    for (int i = 0; i < 12; i++) {
        ASSERT_TRUE(_obj.update_and_query(&_tree_feas[i]));
        CWARNING_LOG("FEA value[%s]", _tree_feas[i].value().c_str());
    }
    FeatureValueProto fea;
    fea.set_feature_id(1234LU); 
    fea.set_view_sign(123LU); 
    fea.set_feature_type(FeatureValueProto::THREE_LEVEL_TREE); 
    fea.set_joinkey(119LU); 
    fea.set_log_id(120LU);
    fea.set_log_time(1111111111LU);
    fea.set_coord(1);
    ASSERT_TRUE(_obj.query(&fea)); 
    EXPECT_EQ("3", fea.value());
    _obj.uninit();
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti



/* vim: set ts=4 sw=4: */

