// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// @Author: <PERSON>(<EMAIL>)
// 
// @Brief: 

#include <vector>
#include <gtest/gtest.h>
#include <file_dict_manager.h>
#include "acp_feature_accumulator.h"
#include "posix_themis_io.h"
#include "ckpt_test_util.h"

using anti::themis::common_lib::FileDictManagerSingleton;

namespace anti {
namespace themis {
namespace feature_lib {

class AcpFeatureAccumulatorTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
        ASSERT_TRUE(_conf.load("./conf", "accumulator_test.conf") == 0);

        int64_t coords[] = {115L, 135L, 155L, 175L, 175L};
        for (uint32_t i = 0U; i < 5U; ++i) {
            FeatureValueProto fea;
            fea.set_feature_id(130LU);
            fea.set_view_sign(123456LU);
            fea.set_feature_type(FeatureValueProto::ACP);
            fea.set_joinkey(119LU);
            fea.set_log_id(120LU);
            fea.set_log_time(1111111111LU);
            fea.set_coord(coords[i]);
            fea.set_data_view_sign(2222222LU);
            fea.mutable_acp_field()->set_price(400L);
            _feas.push_back(fea);
        }

        FeatureValueProto fea;
        fea.set_feature_id(123456789LU);
        _inv_feas.push_back(fea);

        fea.set_feature_id(130LU);
        fea.set_feature_type(FeatureValueProto::RATIO);
        _inv_feas.push_back(fea);
    }
    virtual void TearDown() {
        _feas.clear();
        _inv_feas.clear();
    }

private:
    AcpFeatureAccumulator _obj;
    comcfg::Configure _conf;

    std::vector<FeatureValueProto> _feas;
    std::vector<FeatureValueProto> _inv_feas;
};

TEST_F(AcpFeatureAccumulatorTestSuite, construction_case) {
    EXPECT_EQ(0LU, _obj.feature_id());
    EXPECT_EQ(0LU, _obj._version);
    EXPECT_EQ(0L, _obj._window_len);
    EXPECT_EQ(0L, _obj._conf.premium_threshold);
    EXPECT_EQ(0L, _obj._conf.tol);
    EXPECT_EQ(0L, _obj._conf.init_residual);
    EXPECT_EQ(0L, _obj._acp);
}

TEST_F(AcpFeatureAccumulatorTestSuite, init_case) {
    ASSERT_TRUE(FileDictManagerSingleton::instance().init("./conf", "file_dict_manager.conf"));
    uint32_t num = _conf["invalid_acp"]["feature"].size();
    ASSERT_LE(1U, num);
    for (uint32_t i = 0U; i < num; ++i) {
        EXPECT_FALSE(_obj.init(_conf["invalid_acp"]["feature"][i]));
        _obj.uninit();
    }

    num = _conf["valid_acp"]["feature"].size();
    ASSERT_LE(2U, num);
    for (uint32_t i = 0U; i < num; ++i) {
        ASSERT_TRUE(_obj.init(_conf["valid_acp"]["feature"][i]));
        EXPECT_EQ(130LU, _obj.feature_id());
        EXPECT_EQ(1LU, _obj._version);
        EXPECT_TRUE(_obj._window_len > 0U);
        EXPECT_TRUE(_obj._conf.premium_threshold >= 0L);
        EXPECT_TRUE(_obj._conf.tol > 0U);
        _obj.uninit();
    }
    EXPECT_EQ(0LU, _obj.feature_id());
    EXPECT_EQ(0LU, _obj._version);
    EXPECT_EQ(0L, _obj._window_len);
    EXPECT_EQ(0L, _obj._conf.premium_threshold);
    EXPECT_EQ(0L, _obj._conf.tol);
    FileDictManagerSingleton::instance().uninit();
}

TEST_F(AcpFeatureAccumulatorTestSuite, test_get_confidence) {
    ASSERT_TRUE(FileDictManagerSingleton::instance().init("./conf", "file_dict_manager.conf"));
    ASSERT_TRUE(_obj.init(_conf["valid_acp"]["feature"][0]));
    double confidence = 0.0;
    ASSERT_TRUE(_obj._get_confidence(1u, &confidence));
    
    EXPECT_EQ(3860, confidence);

    _obj.uninit();
    FileDictManagerSingleton::instance().uninit();
}

TEST_F(AcpFeatureAccumulatorTestSuite, test_get_premium) {
    ASSERT_TRUE(FileDictManagerSingleton::instance().init("./conf", "file_dict_manager.conf"));
    ASSERT_TRUE(_obj.init(_conf["valid_acp"]["feature"][0]));
    double premium = 0.0;
    double confidence = 0.0;
    ASSERT_TRUE(_obj._get_confidence(1u, &confidence));
    ASSERT_TRUE(_obj._get_premium(confidence, 400, 1u, &premium));
    EXPECT_EQ(400, premium);
    ASSERT_TRUE(_obj._get_premium(confidence, 4000, 1u, &premium));
    EXPECT_EQ(3960, premium);

    _obj.uninit();
    FileDictManagerSingleton::instance().uninit();
}

TEST_F(AcpFeatureAccumulatorTestSuite, test_update) {
    ASSERT_TRUE(FileDictManagerSingleton::instance().init("./conf", "file_dict_manager.conf"));
    ASSERT_TRUE(_obj.init(_conf["valid_acp"]["feature"][0]));
    // invalid fea
    EXPECT_FALSE(_obj.query(NULL));

    for (uint32_t i = 0U; i< _inv_feas.size(); ++i) {
        EXPECT_FALSE(_obj.update(_inv_feas[i]));
        EXPECT_FALSE(_obj.query(&_inv_feas[i]));
    }
    
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(_feas[i]));
    }
    
    FeatureValueProto feat = _feas[1];
    ASSERT_TRUE(_obj.query(&feat));
    EXPECT_TRUE(feat.valid());
    EXPECT_EQ(-8660.0, feat.mutable_acp_field()->residual());

    std::vector<FeatureValueProto> other_feas;
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        feat = _feas[i];
        feat.set_view_sign(33333U);
        feat.mutable_acp_field()->set_price(450L);
        other_feas.push_back(feat);
    }
    feat = other_feas[1];
    for (uint32_t i = 0U; i < other_feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(other_feas[i]));
    }
    ASSERT_TRUE(_obj.query(&feat));
    EXPECT_TRUE(feat.valid());
    EXPECT_EQ(-8410, feat.mutable_acp_field()->residual());

    for (uint32_t i = 0U; i < other_feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(other_feas[i]));
    }
    ASSERT_TRUE(_obj.query(&feat));
    EXPECT_TRUE(feat.valid());
    EXPECT_EQ(-13120, feat.mutable_acp_field()->residual());
    _obj.uninit();
    FileDictManagerSingleton::instance().uninit();
}

TEST_F(AcpFeatureAccumulatorTestSuite, test_node) {
    AcpNode node;
    node.index = 4U;
    node.timestamp = 111LU;
    node.charge = 0.33;
    node.residual = 0.66;
    remove("./node.test");

    PosixIoWriterInterface *writer = new(std::nothrow) PosixThemisIoWriter();
    ASSERT_TRUE(writer != NULL);
    // ASSERT_TRUE(writer->init(NULL));
    ASSERT_EQ(writer->open("./node.test"), 0);
    ASSERT_TRUE(node.serialize(writer));
    ASSERT_EQ(writer->close(), 0);
    delete writer;
    AcpNode node_load;
    PosixIoReaderInterface *reader = new(std::nothrow) PosixThemisIoReader();
    ASSERT_TRUE(reader != NULL);
    // ASSERT_TRUE(reader->init(NULL));
    ASSERT_EQ(reader->open("./node.test"), 0);
    ASSERT_TRUE(node_load.deserialize(reader));
    ASSERT_EQ(node.index, node_load.index);
    ASSERT_EQ(node.timestamp, node_load.timestamp);
    ASSERT_DOUBLE_EQ(node.charge, node_load.charge);
    ASSERT_DOUBLE_EQ(node.residual, node_load.residual);
    ASSERT_EQ(reader->close(), 0);
    remove("./node.test");
    delete reader;
}

TEST_F(AcpFeatureAccumulatorTestSuite, test_themis_interface_dump_load) {
    ASSERT_TRUE(FileDictManagerSingleton::instance().init("./conf", "file_dict_manager.conf"));
    ASSERT_TRUE(_obj.init(_conf["valid_acp"]["feature"][0]));
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(_feas[i]));
    }
    remove("./data/acp_ckpt_themis.dat");
    ASSERT_TRUE(_obj.query(&_feas[4]));
    auto coord = _feas[4].coord();
    DOING_DUMP();
    _obj.uninit();

    ASSERT_TRUE(_obj.init(_conf["valid_acp"]["feature"][0]));
    DOING_LOAD();
    ASSERT_TRUE(_obj.query(&_feas[4]));
    CWARNING_LOG("before dump[%d], after load[%d]", coord, _feas[4].coord());
    ASSERT_EQ(coord, _feas[4].coord());
    FileDictManagerSingleton::instance().uninit();
    // check
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

