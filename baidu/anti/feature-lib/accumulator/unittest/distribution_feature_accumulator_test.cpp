// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangji<PERSON><PERSON>(<EMAIL>)
// 
// @File: distribution_feature_accumulator_test.cpp
// @Last modified: 2017-12-28 17:41:37
// @Brief: 

#include <vector>
#include <gtest/gtest.h>
#include <gflags/gflags.h>
#include <boost/lexical_cast.hpp>
#include "distribution_feature_accumulator.h"
#include "posix_themis_io.h" 
#include "ckpt_test_util.h"

namespace anti {
namespace themis {
namespace feature_lib {

DECLARE_bool(enable_mem_pool_for_acc);
// 参数化测试
class DistributionFeatureAccumulatorTestSuite : public ::testing::TestWithParam<bool> {
protected:
    virtual void SetUp() {
        // 根据参数设置gflag
        bool use_mem_pool = GetParam();
        FLAGS_enable_mem_pool_for_acc = use_mem_pool;
        
        ASSERT_TRUE(_conf.load("./conf", "accumulator_test.conf") == 0);

        int64_t coords[] = {0L, 1L, 2L, 3L, 3L, 3L, 3L};
        uint32_t bucket_idx[] = {0U, 1U, 2U, 0U, 1U, 2U, 0U};
        for (uint32_t i = 0U; i < 7U; ++i) {
            FeatureValueProto fea;
            fea.set_feature_id(100LU);
            fea.set_view_sign(123456LU);
            fea.set_feature_type(FeatureValueProto::DISTRIBUTION);
            fea.set_joinkey(119LU);
            fea.set_log_id(120LU);
            fea.set_log_time(1111111111LU);
            fea.set_coord(coords[i]);
            fea.set_bucket_idx(bucket_idx[i]);
            _feas.push_back(fea);
        }

        _old_fea = _feas[0];
        _old_fea.set_coord(0L);

        FeatureValueProto fea;
        fea.set_feature_id(123456789LU);
        _inv_feas.push_back(fea);

        fea.set_feature_id(100LU);
        fea.set_feature_type(FeatureValueProto::RATIO);
        _inv_feas.push_back(fea);

        fea.set_feature_type(FeatureValueProto::DISTRIBUTION);
        _inv_feas.push_back(fea);

        fea.set_bucket_idx(1000LU);
        _inv_feas.push_back(fea);
    }
    virtual void TearDown() {
        _feas.clear();
        _inv_feas.clear();
    }
    void set_fea(uint64_t coord, uint32_t bucket, FeatureValueProto *fea) {
        ASSERT_TRUE(fea != NULL);
        fea->set_feature_id(100LU);
        fea->set_view_sign(123456LU);
        fea->set_joinkey(119LU);
        fea->set_log_id(120LU);
        fea->set_feature_type(FeatureValueProto::DISTRIBUTION);
        fea->set_log_time(1111111111LU);
        fea->set_coord(coord);
        fea->set_bucket_idx(bucket);
        return;
    }

    void set_feas(
            std::vector<int64_t> coords, 
            std::vector<uint32_t> buckets, 
            std::vector<FeatureValueProto> *feas) {
        ASSERT_TRUE(feas != NULL);
        ASSERT_EQ(coords.size(), buckets.size());
        for (uint32_t i = 0; i < coords.size(); ++i) {
            FeatureValueProto fea;
            set_fea(coords[i], buckets[i], &fea);
            feas->push_back(fea);
        }
        return;
    }

protected:
    DistributionFeatureAccumulator _obj;
    comcfg::Configure _conf;
    std::vector<FeatureValueProto> _feas;
    FeatureValueProto _old_fea;
    std::vector<FeatureValueProto> _inv_feas;
};

// 实例化参数化测试：false=StdSlidingWindow, true=PoolSlidingWindow
INSTANTIATE_TEST_CASE_P(
    WindowTypeTest,
    DistributionFeatureAccumulatorTestSuite,
    ::testing::Values(false, true)
);

TEST_P(DistributionFeatureAccumulatorTestSuite, construction_case) {
    EXPECT_EQ(0LU, _obj.feature_id());
    EXPECT_EQ(0LU, _obj._version);
    EXPECT_FALSE(_obj._click_mode);
}

TEST_P(DistributionFeatureAccumulatorTestSuite, init_case) {
    uint32_t num = _conf["invalid_dis"]["feature"].size();
    ASSERT_LE(4U, num);
    for (uint32_t i = 0U; i < num; ++i) {
        EXPECT_FALSE(_obj.init(_conf["invalid_dis"]["feature"][i]));
        _obj.uninit();
    }

    num = _conf["valid_dis"]["feature"].size();
    ASSERT_LE(2, num);
    _obj.uninit();
    for (uint32_t i = 0U; i < num; ++i) {
        ASSERT_TRUE(_obj.init(_conf["valid_dis"]["feature"][i]));
        EXPECT_EQ(100LU, _obj.feature_id());
        EXPECT_EQ(2LU, _obj._version);
        EXPECT_EQ(3, _obj._bucket_num);
        _obj.uninit();
    }
    EXPECT_EQ(0LU, _obj.feature_id());
    EXPECT_EQ(0LU, _obj._version);
    EXPECT_FALSE(_obj._click_mode);
}

TEST_P(DistributionFeatureAccumulatorTestSuite, multi_update_and_one_query_sliding_window_case) {
    ASSERT_TRUE(_obj.init(_conf["valid_dis"]["feature"][0]));
    // invalid fea
    EXPECT_FALSE(_obj.query(NULL));
    for (uint32_t i = 0U; i< _inv_feas.size(); ++i) {
        EXPECT_FALSE(_obj.update(_inv_feas[i]));
        EXPECT_FALSE(_obj.query(&_inv_feas[i]));
    }

    // multi update and one query
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(_feas[i]));
    }
    // update old fea
    ASSERT_FALSE(_obj.update(_old_fea));
   
    // query fea
    ASSERT_TRUE(_obj.query(&_feas[6]));
    ASSERT_EQ(3, _feas[6].buckets_size());
    int64_t exp_num[] = {2, 1, 2};
    for (int i = 0; i < _feas[6].buckets_size(); ++i) {
        EXPECT_EQ(static_cast<uint32_t>(i), _feas[6].buckets(i).idx());
        EXPECT_EQ(exp_num[i], _feas[6].buckets(i).count());
    }

    // view sign no update before 
    FeatureValueProto fea = _feas[6];
    fea.clear_buckets();
    fea.set_view_sign(987LU);
    ASSERT_FALSE(_obj.query(&fea));
    _obj.uninit();
}

TEST_P(DistributionFeatureAccumulatorTestSuite, multi_update_and_one_query_click_segment_case) {
    _conf["valid_dis"]["feature"][0].add_unit("click_mode", "1");
    ASSERT_TRUE(_obj.init(_conf["valid_dis"]["feature"][0]));
    EXPECT_TRUE(_obj._click_mode);
    EXPECT_TRUE(_obj._seg._state_ok);

    // multi update and one query
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(_feas[i]));
    }
   
    // query fea
    ASSERT_TRUE(_obj.query(&_feas[6]));
    ASSERT_EQ(3, _feas[6].buckets_size());
    int64_t exp_num[] = {2, 1, 2};
    for (int i = 0; i < _feas[6].buckets_size(); ++i) {
        EXPECT_EQ(static_cast<uint32_t>(i), _feas[6].buckets(i).idx());
        EXPECT_EQ(exp_num[i], _feas[6].buckets(i).count());
    }

    // view sign no update before 
    FeatureValueProto fea = _feas[6];
    fea.clear_buckets();
    fea.set_view_sign(987LU);
    ASSERT_TRUE(_obj.query(&fea));
    ASSERT_EQ(3, fea.buckets_size());
    for (int i = 0; i < fea.buckets_size(); ++i) {
        EXPECT_EQ(static_cast<uint32_t>(i), fea.buckets(i).idx());
        EXPECT_EQ(0L, fea.buckets(i).count());
    }
    _obj.uninit();
}

TEST_P(DistributionFeatureAccumulatorTestSuite, calculate_distance_case) {
    _feas.clear();
    int64_t coords[] = {0L, 1L, 2L, 3L, 3L, 3L, 3L};
    uint32_t bucket_idx[] = {0U, 1U, 2U, 0U, 1U, 2U, 0U};
    for (uint32_t i = 0U; i < 7U; ++i) {
        FeatureValueProto fea;
        fea.set_feature_id(100LU);
        fea.set_view_sign(123456LU);
        fea.set_feature_type(FeatureValueProto::DISTRIBUTION);
        fea.set_joinkey(119LU);
        fea.set_log_id(120LU);
        fea.set_log_time(1111111111LU);
        fea.set_coord(coords[i]);
        fea.set_bucket_idx(bucket_idx[i]);
        _feas.push_back(fea);
    }

    ASSERT_TRUE(_obj.init(_conf["valid_multi_dis"]["feature"][0]));
    const char*  exp_value_max_diff[7] = {"4", "0.24999999999999994", "4", \
            "1.4999999999999998", "-0.16666666666666677", "4", "1"};
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update_and_query(&_feas[i]));
        EXPECT_STREQ(exp_value_max_diff[i], _feas[i].value().c_str());
    }

    _feas.clear();
    for (uint32_t i = 0U; i < 7U; ++i) {
        FeatureValueProto fea;
        fea.set_feature_id(101LU);
        fea.set_view_sign(123456LU);
        fea.set_feature_type(FeatureValueProto::DISTRIBUTION);
        fea.set_joinkey(119LU);
        fea.set_log_id(120LU);
        fea.set_log_time(1111111111LU);
        fea.set_coord(coords[i]);
        fea.set_bucket_idx(bucket_idx[i]);
        _feas.push_back(fea);
    }

    _obj.uninit();
    ASSERT_TRUE(_obj.init(_conf["valid_multi_dis"]["feature"][1]));
    const char*  exp_value_chi_test[7] = {"40365.549784219431", "61599.459823410805", \
            "81128.906315419415", "40365.549784219431", "615.82042099460693", \
            "342.30775754955181", "851.18102063519848"};
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update_and_query(&_feas[i]));
        EXPECT_STREQ(exp_value_chi_test[i], _feas[i].value().c_str());
    }

    _feas.clear();
    for (uint32_t i = 0U; i < 7U; ++i) {
        FeatureValueProto fea;
        fea.set_feature_id(102LU);
        fea.set_view_sign(123456LU);
        fea.set_feature_type(FeatureValueProto::DISTRIBUTION);
        fea.set_joinkey(119LU);
        fea.set_log_id(120LU);
        fea.set_log_time(1111111111LU);
        fea.set_coord(coords[i]);
        fea.set_bucket_idx(bucket_idx[i]);
        _feas.push_back(fea);
    }

    _obj.uninit();
    ASSERT_TRUE(_obj.init(_conf["valid_multi_dis"]["feature"][2]));
    const char*  exp_value_chi_dis[7] = {"3.7000000000000006", "1.2", "8.6999999999999993", \
            "3.7000000000000006", "0.57499999999999996", "0.64444444444444426", \
            "0.73124999999999996"};
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update_and_query(&_feas[i]));
        EXPECT_STREQ(exp_value_chi_dis[i], _feas[i].value().c_str());
    }

    _feas.clear();
    for (uint32_t i = 0U; i < 7U; ++i) {
        FeatureValueProto fea;
        fea.set_feature_id(103LU);
        fea.set_view_sign(123456LU);
        fea.set_feature_type(FeatureValueProto::DISTRIBUTION);
        fea.set_joinkey(119LU);
        fea.set_log_id(120LU);
        fea.set_log_time(1111111111LU);
        fea.set_coord(coords[i]);
        fea.set_bucket_idx(bucket_idx[i]);
        _feas.push_back(fea);
    }

    _obj.uninit();
    ASSERT_TRUE(_obj.init(_conf["valid_multi_dis"]["feature"][3]));
    const char*  exp_value_kl_divergence[7] = {"2.3219280948873622", "1.3219280948873622", \
            "3.3219280948873622", "2.3219280948873622", "0.82192809488736218", \
            "0.73696559416620588", "0.82192809488736218"};
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update_and_query(&_feas[i]));
        EXPECT_STREQ(exp_value_kl_divergence[i], _feas[i].value().c_str());
    }
}

TEST_P(DistributionFeatureAccumulatorTestSuite, update_and_query_sliding_window_case) {
    ASSERT_TRUE(_obj.init(_conf["valid_dis"]["feature"][0]));
    // invalid fea
    EXPECT_FALSE(_obj.update_and_query(NULL));
    for (uint32_t i = 0U; i< _inv_feas.size(); ++i) {
        EXPECT_FALSE(_obj.update_and_query(&_inv_feas[i]));
    }

    uint32_t exp_num[][3] = {{1U, 0U, 0U},
            {1U, 1U, 0U}, {0U, 1U, 1U}, {1U, 0U, 1U},
            {1U, 1U, 1U}, {1U, 1U, 2U}, {2U, 1U, 2U}
    };

    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update_and_query(&_feas[i]));
        ASSERT_EQ(3, _feas[i].buckets_size());
        for (uint32_t j = 0U; j < 3U; ++j) {
            EXPECT_EQ(j, _feas[i].buckets(j).idx());
            EXPECT_EQ(exp_num[i][j], _feas[i].buckets(j).count());
        }
    }

    // old fea
    ASSERT_FALSE(_obj.update_and_query(&_old_fea));

    // view sign no update before
    FeatureValueProto fea = _feas[1];
    fea.clear_buckets();
    fea.set_view_sign(987LU);
    ASSERT_TRUE(_obj.update_and_query(&fea));
    for (int i = 0; i < fea.buckets_size(); ++i) {
        EXPECT_EQ(static_cast<uint32_t>(i), fea.buckets(i).idx());
        EXPECT_EQ(0L, fea.buckets(i).count());
    }
    _obj.uninit();
}

TEST_P(DistributionFeatureAccumulatorTestSuite, weak_anomaly_case) {
    _conf["valid_dis"]["feature"][0].add_unit("weak_abnormity_threshold", "0.5");
    _conf["valid_dis"]["feature"][0].add_unit("incr_step", "8");
    ASSERT_TRUE(_obj.init(_conf["valid_dis"]["feature"][0]));
    // invalid fea
    EXPECT_FALSE(_obj.update_and_query(NULL));
    for (uint32_t i = 0U; i < _inv_feas.size(); ++i) {
        EXPECT_FALSE(_obj.update_and_query(&_inv_feas[i]));
    }

    uint32_t exp_num[][3] = {{1U, 0U, 0U},
            {1U, 1U, 0U}, {0U, 1U, 1U}, {1U, 0U, 1U},
            {1U, 1U, 1U}, {1U, 1U, 2U}, {2U, 1U, 2U}
    };
    std::vector<uint32_t> buckets = {
            0L, 0L, 0L, 0L, 0L, 1L, 2L, 2L, 
            0L, 0L, 0L, 0L, 1L, 1L, 2L, 2L};
    std::vector<bool> in_anomaly_care_list = {
            true, true, true, true, true, false, true, true, 
            true, true, true, true, false, false, true, true};
    std::vector<int64_t> coords(buckets.size(), 99L);
    std::vector<FeatureValueProto> feas;
    set_feas(coords, buckets, &feas);

    for (uint32_t i = 0U; i < 8; ++i) {
        ASSERT_TRUE(_obj.update_and_query(&feas[i]));
        ASSERT_EQ(FeatureValueProto::WEAK_ANOMALY, feas[i].feature_type());

        ASSERT_FALSE(feas[i].valid());
        EXPECT_EQ(in_anomaly_care_list[i], feas[i].in_anomaly_care());
    }
    for (uint32_t i = 8U; i < feas.size(); ++i) {
        ASSERT_TRUE(_obj.update_and_query(&feas[i]));
        ASSERT_EQ(FeatureValueProto::WEAK_ANOMALY, feas[i].feature_type());
        ASSERT_TRUE(feas[i].valid());
        EXPECT_EQ(in_anomaly_care_list[i], feas[i].in_anomaly_care());
    }
    ASSERT_TRUE(_obj.query(&feas[feas.size() - 1]));
    EXPECT_EQ(10.0 / 14 + 1.0 / 8 * 2, boost::lexical_cast<double>(feas[feas.size() - 1].value())); 

    // view sign no update before
    // FeatureValueProto fea = _feas[1];
    // fea.clear_buckets();
    // fea.set_view_sign(987LU);
    // ASSERT_TRUE(_obj.update_and_query(&fea));
    // for (int i = 0; i < fea.buckets_size(); ++i) {
    //     EXPECT_EQ(static_cast<uint32_t>(i), fea.buckets(i).idx());
    //     EXPECT_EQ(0L, fea.buckets(i).count());
    // }
    _obj.uninit();
}

TEST_P(DistributionFeatureAccumulatorTestSuite, update_and_query_click_segment_case) {
    _conf["valid_dis"]["feature"][0].add_unit("click_mode", "1");
    ASSERT_TRUE(_obj.init(_conf["valid_dis"]["feature"][0]));
    
    uint32_t exp_num[][3] = {{0U, 0U, 0U}, 
            {1U, 0U, 0U}, {0U, 0U, 0U}, {0U, 0U, 1U}, 
            {1U, 0U, 1U}, {1U, 1U, 1U}, {1U, 1U, 2U}
    };
    // uint32_t exp_num[][3] = {{1U, 0U, 0U}, 
    //         {1U, 1U, 0U}, {0U, 0U, 1U}, {1U, 0U, 1U}, 
    //         {1U, 1U, 1U}, {1U, 1U, 2U}, {2U, 1U, 2U}
    // };

    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update_and_query(&_feas[i]));
        ASSERT_EQ(3, _feas[i].buckets_size());
        for (uint32_t j = 0U; j < 3U; ++j) {
            EXPECT_EQ(j, _feas[i].buckets(j).idx());
            EXPECT_EQ(exp_num[i][j], _feas[i].buckets(j).count());
        }
    }
    _obj.uninit();
}

TEST_P(DistributionFeatureAccumulatorTestSuite, update_and_query_jump_window_case) {
    ASSERT_TRUE(_obj.init(_conf["valid_dis"]["feature"][1]));
    uint32_t exp_num[][3] = {{1U, 0U, 0U}, 
            {0U, 1U, 0U}, {0U, 0U, 1U}, {1U, 0U, 0U}, 
            {1U, 1U, 0U}, {1U, 1U, 1U}, {2U, 1U, 1U}
    };
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update_and_query(&_feas[i]));
        ASSERT_EQ(3, _feas[i].buckets_size());
        for (uint32_t j = 0U; j < 3U; ++j) {
            EXPECT_EQ(j, _feas[i].buckets(j).idx());
            EXPECT_EQ(exp_num[i][j], _feas[i].buckets(j).count());
        }
    }
    _obj.uninit();
}

TEST_P(DistributionFeatureAccumulatorTestSuite, dump_load_click_segment_posix_case) {
    _conf["valid_dis"]["feature"][0].add_unit("click_mode", "1");
    ASSERT_TRUE(_obj.init(_conf["valid_dis"]["feature"][0]));
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(_feas[i]));
    }
    DOING_DUMP();
    _obj.uninit();
    
    ASSERT_TRUE(_obj.init(_conf["valid_dis"]["feature"][0]));
    DOING_LOAD();
    ASSERT_TRUE(_obj.query(&_feas[6]));
    ASSERT_EQ(3, _feas[6].buckets_size());
    int64_t exp_num[] = {2, 1, 2};
    for (int i = 0; i < _feas[6].buckets_size(); ++i) {
        EXPECT_EQ(static_cast<uint32_t>(i), _feas[6].buckets(i).idx());
        EXPECT_EQ(exp_num[i], _feas[6].buckets(i).count());
    }
    _obj.uninit();
}

TEST_P(DistributionFeatureAccumulatorTestSuite, max_distance_magnify_case) {
    
    uint32_t num_invalid = _conf["invalid_max_distance_magnify"]["feature"].size();
    ASSERT_LE(1U, num_invalid);
    EXPECT_TRUE(_obj.init(_conf["invalid_max_distance_magnify"]["feature"][0]));
    _obj.uninit();
    
    uint32_t num_valid = _conf["valid_max_distance_magnify"]["feature"].size();
    ASSERT_LE(2U, num_valid);
        
    {
        EXPECT_TRUE(_obj.init(_conf["valid_max_distance_magnify"]["feature"][0]));
        _feas.clear();

        uint64_t coords[] = {0L, 1L, 2L, 3L, 3L, 3L, 3L, 3L, 3L, 3L, 3L, 3L};
        uint32_t bucket_idx[] = {0U, 1U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 1U};
    
        for (uint32_t i = 0U; i < 11U; ++i) {
            FeatureValueProto fea;
            fea.set_feature_id(5123U);
            fea.set_view_sign(123456LU);
            fea.set_feature_type(FeatureValueProto::DISTRIBUTION);
            fea.set_joinkey(119LU);
            fea.set_log_id(120LU);
            fea.set_log_time(1111111111LU);
            fea.set_coord(coords[i]);
            fea.set_bucket_idx(bucket_idx[i]);
            _feas.push_back(fea);
        }    
    
        const char* exp_value[12] = {"41", "0", "0.5", "2", "4.5", "8", "12.5",
                "18", "24.5", "32", "32.735537190082638", "8"};
        bool exp_valid[] = {false, true, true, true, true, true, 
                true, true, true, true, true, true};
        bool exp_in_anomaly_care[] = {true, true, true, true, 
                true, true, true, true, true, true, true};
        uint32_t exp_num[][2] = {{1U, 0U}, {1U, 1U}, {2U, 1U}, {3U, 1U}, {4U, 1U}, 
                {5U, 1U}, {6U, 1U}, {7U, 1U}, {8U, 1U}, {9U, 1U}, {10U, 1U}, {10U, 2U}};
    
        for (uint32_t i = 0U; i < _feas.size(); ++i) {
            ASSERT_TRUE(_obj.update_and_query(&_feas[i]));
            for (uint32_t j = 0U; j < 2U; ++j) {
                EXPECT_EQ(j, _feas[i].buckets(j).idx());
                EXPECT_EQ(exp_num[i][j], _feas[i].buckets(j).count());
            }
            EXPECT_STREQ(exp_value[i], _feas[i].value().c_str());
            EXPECT_EQ(exp_valid[i], _feas[i].valid());
        }    
        _obj.uninit();
    }
    
    {
        EXPECT_TRUE(_obj.init(_conf["valid_max_distance_magnify"]["feature"][1]));
        _feas.clear();

        uint64_t coords[] = {0L, 1L, 2L, 3L, 3L, 3L, 3L, 3L, 3L, 3L, 3L, 3L};
        uint32_t bucket_idx[] = {0U, 1U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U};
    
        for (uint32_t i = 0U; i < 11U; ++i) {
            FeatureValueProto fea;
            fea.set_feature_id(5123U);
            fea.set_view_sign(123456LU);
            fea.set_feature_type(FeatureValueProto::DISTRIBUTION);
            fea.set_joinkey(119LU);
            fea.set_log_id(120LU);
            fea.set_log_time(1111111111LU);
            fea.set_coord(coords[i]);
            fea.set_bucket_idx(bucket_idx[i]);
            _feas.push_back(fea);
        }    
    
        const char* exp_value[12] = {"1", "0", "0.1111111111111111", "0.25", 
                "0.36000000000000004", "0.44444444444444453", 
                "0.51020408163265296", "0.5625", "0.60493827160493818", 
                "0.64000000000000012", "0.66942148760330566", "0.444"};
        bool exp_valid[] = {false, true, true, true, true, true, 
                true, true, true, true, true, true};
        bool exp_in_anomaly_care[] = {true, true, true, true, true, 
                true, true, true, true, true, true, true};
        uint32_t exp_num[][2] = {{1U, 0U}, {1U, 1U}, {2U, 1U}, {3U, 1U}, {4U, 1U}, 
                {5U, 1U}, {6U, 1U}, {7U, 1U}, {8U, 1U}, {9U, 1U}, {10U, 1U}, {10U, 2U}};
    
        for (uint32_t i = 0U; i < _feas.size(); ++i) {
            ASSERT_TRUE(_obj.update_and_query(&_feas[i]));
            for (uint32_t j = 0U; j < 2U; ++j) {
                EXPECT_EQ(j, _feas[i].buckets(j).idx());
                EXPECT_EQ(exp_num[i][j], _feas[i].buckets(j).count());
            }
            EXPECT_STREQ(exp_value[i], _feas[i].value().c_str());
            EXPECT_EQ(exp_valid[i], _feas[i].valid());
        }    
        _obj.uninit();
    }
}

TEST_P(DistributionFeatureAccumulatorTestSuite, print_monitor_log_test) {
    // Test std_window/pool_window modes based on parameter
    ASSERT_TRUE(_obj.init(_conf["valid_dis"]["feature"][0]));
    
    // 初始状态监控 - 空数据
    testing::internal::CaptureStderr();
    _obj.print_monitor_log();
    std::string empty_output = testing::internal::GetCapturedStderr();
    
    // 验证基本格式
    EXPECT_TRUE(empty_output.find("Feature: ID=100") != std::string::npos);
    EXPECT_TRUE(empty_output.find("Type=DISTRIBUTION") != std::string::npos);
    EXPECT_TRUE(empty_output.find("Elements=0") != std::string::npos);
    
    // 添加测试数据
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(_feas[i]));
    }
    
    // 有数据状态监控
    testing::internal::CaptureStderr();
    _obj.print_monitor_log();
    std::string data_output = testing::internal::GetCapturedStderr();
    
    // 验证数据状态下的输出
    EXPECT_TRUE(data_output.find("Feature: ID=100") != std::string::npos);
    EXPECT_TRUE(data_output.find("Type=DISTRIBUTION") != std::string::npos);
    
    // 根据参数验证不同模式的输出
    bool use_mem_pool = GetParam();
    if (use_mem_pool) {
        // PoolSlidingWindow模式应该有详细的内存统计
        EXPECT_TRUE(data_output.find("Memory(Total=") != std::string::npos);
        EXPECT_TRUE(data_output.find("Data=") != std::string::npos);
        EXPECT_TRUE(data_output.find("Overhead=") != std::string::npos);
        EXPECT_TRUE(data_output.find("AvgPerElement=") != std::string::npos);
    } else {
        // StdSlidingWindow模式应该显示"Memory=N/A"
        EXPECT_TRUE(data_output.find("Memory=N/A") != std::string::npos);
    }
    
    _obj.uninit();
}

TEST_P(DistributionFeatureAccumulatorTestSuite, print_monitor_log_click_mode_test) {
    // 测试 click_mode (SEG) 的监控
    _conf["valid_dis"]["feature"][0].add_unit("click_mode", "1");
    ASSERT_TRUE(_obj.init(_conf["valid_dis"]["feature"][0]));
    EXPECT_TRUE(_obj._click_mode);
    
    // 添加数据
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(_feas[i]));
    }
    
    // 捕获监控日志
    testing::internal::CaptureStderr();
    _obj.print_monitor_log();
    std::string output = testing::internal::GetCapturedStderr();
    
    // 验证SEG模式的输出格式
    EXPECT_TRUE(output.find("Feature: ID=100") != std::string::npos);
    EXPECT_TRUE(output.find("Type=DISTRIBUTION") != std::string::npos);
    EXPECT_TRUE(output.find("Memory=N/A") != std::string::npos);  // SEG模式没有内存统计
    
    _obj.uninit();
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

