// Copyright 2016 Baidu Inc. All Right Reserved.
// Author <PERSON> (<EMAIL>)
// 
// brief: 

#include <gtest/gtest.h>
#include "distinct_distribution_feature_accumulator.h"
#include "posix_themis_io.h"
#include "ckpt_test_util.h"

namespace anti {
namespace themis {
namespace feature_lib {

class DistinctDistributionFeatureAccumulatorTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
        ASSERT_TRUE(_conf.load("./conf", "accumulator_test.conf") == 0);
        // feature_id invalid
        FeatureValueProto fea1;
        fea1.set_feature_id(100LU);
        fea1.set_feature_type(FeatureValueProto::DISTINCT_DISTRIBUTION);
        _invalid_feas.push_back(fea1);
        // feature_type invalid
        FeatureValueProto fea2;
        fea2.set_feature_id(1234LU);
        fea2.set_feature_type(FeatureValueProto::DISTRIBUTION);
        _invalid_feas.push_back(fea2);
        int64_t coords[] = {0L, 1L, 2L, 3L, 4L, 5L, 6L, 14L, 18L, 19L, 40L, 41L};
        uint64_t data_signs[] = {1L, 1L, 1L, 2L, 2L, 3L, 8L, 8L, 8L, 9L,  9L,  9L};
        uint64_t cumulate_signs[] = {1L, 1L, 3L, 4L, 1L, 1L, 8L, 8L, 8L, 9L,  9L,  9L};
        for (int i = 0; i < 12; i++) {
            FeatureValueProto fea;
            fea.set_feature_id(1234LU);
            fea.set_view_sign(123LU);
            fea.set_feature_type(FeatureValueProto::DISTINCT_DISTRIBUTION);
            fea.set_joinkey(119LU);
            fea.set_log_id(120LU);
            fea.set_log_time(1111111111LU);
            fea.set_coord(coords[i]);
            fea.set_data_view_sign(data_signs[i]);
            fea.set_cumulate_view_sign(cumulate_signs[i]);
            _valid_feas.push_back(fea);
        }
        int64_t distinct_coords[] = {0L, 1L, 2L, 3L, 4L, 5L, 6L, 14L, 18L, 19L, 40L, 41L};
        uint64_t distinct_data_signs[] = {1L, 1L, 1L, 1L, 1L, 2L, 8L, 8L, 8L, 9L, 9L, 9L};
        uint64_t distinct_cumulate_signs[] = {1L, 2L, 2L, 1L, 3L, 4L, 8L, 8L, 8L, 9L, 9L, 9L};
        for (int i = 0; i < 12; i++) {
            FeatureValueProto fea;
            fea.set_feature_id(1234LU);
            fea.set_view_sign(123LU);
            fea.set_feature_type(FeatureValueProto::DISTINCT_DISTRIBUTION);
            fea.set_joinkey(119LU);
            fea.set_log_id(120LU);
            fea.set_log_time(1111111111LU);
            fea.set_coord(distinct_coords[i]);
            fea.set_data_view_sign(distinct_data_signs[i]);
            fea.set_cumulate_view_sign(distinct_cumulate_signs[i]);
            _distinct_feas.push_back(fea);
        }
    }
    virtual void TearDown() {}

private:
    std::vector<FeatureValueProto> _invalid_feas;
    std::vector<FeatureValueProto> _valid_feas;
    std::vector<FeatureValueProto> _distinct_feas;
    comcfg::Configure _conf;
    DistinctDistributionFeatureAccumulator _obj;
};

TEST_F(DistinctDistributionFeatureAccumulatorTestSuite, construction_case) {
    EXPECT_EQ(0L, _obj.feature_id());
    EXPECT_EQ(0L, _obj._version);
}

TEST_F(DistinctDistributionFeatureAccumulatorTestSuite, init_case) {
    uint32_t num = _conf["distinct_dis"]["invalid"]["feature"].size();
    ASSERT_EQ(5U, num);
    for (uint32_t i = 0; i < num; i++) {
        EXPECT_FALSE(_obj.init(_conf["distinct_dis"]["invalid"]["feature"][i]));
        _obj.uninit();
    }
    ASSERT_TRUE(_obj.init(_conf["distinct_dis"]["valid"]["feature"][0]));
    EXPECT_EQ(1234L, _obj.feature_id());
    EXPECT_EQ(1L, _obj._version);
    EXPECT_EQ(2U, _obj._intervals.size());
    _obj.uninit();
}

TEST_F(DistinctDistributionFeatureAccumulatorTestSuite, _init_intervals_case) {
    // invalid case
    EXPECT_FALSE(_obj._init_intervals(std::string("xxx")));
    _obj.uninit();
    // valid case
    ASSERT_TRUE(_obj._init_intervals(std::string("3")));
    ASSERT_EQ(2U, _obj._intervals.size());
    EXPECT_EQ(LLONG_MIN, _obj._intervals[0].first);
    EXPECT_EQ(3L, _obj._intervals[0].second);
    EXPECT_EQ(3L, _obj._intervals[1].first);
    EXPECT_EQ(LLONG_MAX, _obj._intervals[1].second);
    _obj.uninit();
}

TEST_F(DistinctDistributionFeatureAccumulatorTestSuite, _get_interval_idx_case) {
    ASSERT_TRUE(_obj.init(_conf["count_dist"]["valid"]["feature"][0]));
    // in first bucket
    uint32_t idx = 0;
    EXPECT_FALSE(_obj._get_interval_idx(2, &idx, true));
    EXPECT_EQ(0U, idx);
    // in second bucket and jump bucket
    idx = 0;
    EXPECT_TRUE(_obj._get_interval_idx(4, &idx, true));
    EXPECT_EQ(1U, idx);
    // in second bucket and NOT jump bucket
    idx = 0;
    EXPECT_FALSE(_obj._get_interval_idx(5, &idx, true));
    EXPECT_EQ(1U, idx);
}

TEST_F(DistinctDistributionFeatureAccumulatorTestSuite, update_and_query_case) {
    ASSERT_TRUE(_obj.init(_conf["distinct_dis"]["valid"]["feature"][0]));
    // invalid case
    EXPECT_FALSE(_obj.update_and_query(NULL));
    for (uint32_t i = 0; i < _invalid_feas.size(); i++) {
        EXPECT_FALSE(_obj.update_and_query(&_invalid_feas[i]));
    }
    int64_t exp_num[][2] {{1L, 0L}, {2L, 0L}, {0L, 3L}, 
            {1L, 3L}, {0L, 5L}, {1L, 5L}}; 
    uint32_t exp_bucket_idxs[] = {0L, 0L, 1L, 0L, 1L, 0L};
    for (int i = 0; i < 6; i++) {
        ASSERT_TRUE(_obj.update_and_query(&_valid_feas[i]));
        CWARNING_LOG("DEBUG_STR:%s", _valid_feas[i].ShortDebugString().c_str());
        EXPECT_EQ(exp_bucket_idxs[i], _valid_feas[i].bucket_idx());
        for (int j = 0; j < 2; j++) {
            EXPECT_EQ(exp_num[i][j], _valid_feas[i].buckets(j).count());
        }
    }
    _obj.uninit();

    ASSERT_TRUE(_obj.init(_conf["distinct_dis"]["valid"]["feature"][0]));
    for (int i = 0; i < 12; i++) {
        ASSERT_TRUE(_obj.update(_valid_feas[i]));
    }

    FeatureValueProto fea1;
    fea1.set_feature_id(1234L);
    fea1.set_feature_type(FeatureValueProto::DISTINCT_DISTRIBUTION);
    fea1.set_view_sign(123LU);
    fea1.set_data_view_sign(9LU);
    fea1.set_coord(41L);
    ASSERT_TRUE(_obj.query(&fea1));
    EXPECT_EQ(exp_bucket_idxs[0], fea1.bucket_idx());
    EXPECT_EQ(2L, fea1.buckets(0).count());
    EXPECT_EQ(0L, fea1.buckets(1).count());
    _obj.uninit();
}

TEST_F(DistinctDistributionFeatureAccumulatorTestSuite, query_and_update_distinct) {
    ASSERT_TRUE(_obj.init(_conf["distinct_dis"]["valid"]["feature"][0]));
    int64_t exp_num[][2] {{1L, 0L}, {0L, 2L}, {0L, 3L}, 
            {0L, 4L}, {0L, 5L}, {1L, 5L},
            {1L, 0L}, {1L, 0L}, {1L, 0L},
            {2L, 0L}, {1L, 0L}, {2L, 0L}}; 
    uint32_t exp_bucket_idxs[] = {0L, 1L, 1L, 1L, 1L, 0L, 
            0L, 0L, 0L, 0L, 0L, 0L};
    for (int i = 0; i < 12; i++) {
        CWARNING_LOG("BEFORE_DEBUG_STR:%s", _distinct_feas[i].ShortDebugString().c_str());
        ASSERT_TRUE(_obj.update_and_query(&_distinct_feas[i]));
        CWARNING_LOG("DEBUG_STR:%s", _distinct_feas[i].ShortDebugString().c_str());
        EXPECT_EQ(exp_bucket_idxs[i], _distinct_feas[i].bucket_idx());
        for (int j = 0; j < 2; j++) {
            EXPECT_EQ(exp_num[i][j], _distinct_feas[i].buckets(j).count());
        }
    }
    _obj.uninit();

    ASSERT_TRUE(_obj.init(_conf["distinct_dis"]["valid"]["feature"][0]));
    for (int i = 0; i < 6; i++) {
        ASSERT_TRUE(_obj.update(_distinct_feas[i]));
    }

    FeatureValueProto fea1;
    fea1.set_feature_id(1234L);
    fea1.set_feature_type(FeatureValueProto::DISTINCT_DISTRIBUTION);
    fea1.set_view_sign(123LU);
    fea1.set_data_view_sign(1LU);
    fea1.set_coord(5L);
    ASSERT_TRUE(_obj.query(&fea1));
    CWARNING_LOG("DEBUG_STR:%s", fea1.ShortDebugString().c_str());
    _obj.uninit();
}

TEST_F(DistinctDistributionFeatureAccumulatorTestSuite, dump_load_themis_io_interface_case) {
    ASSERT_TRUE(_obj.init(_conf["distinct_dis"]["valid"]["feature"][0]));
    for (uint32_t i = 0; i < 6; i++) {
        ASSERT_TRUE(_obj.update(_valid_feas[i]));
    }
    std::string type = "dist_dis";
    DOING_DUMP();
    _obj.uninit();

    ASSERT_TRUE(_obj.init(_conf["distinct_dis"]["valid"]["feature"][0]));
    DOING_LOAD();
    FeatureValueProto fea;
    fea.set_feature_id(1234L);
    fea.set_feature_type(FeatureValueProto::DISTINCT_DISTRIBUTION);
    fea.set_view_sign(123LU);
    fea.set_data_view_sign(3LU);
    fea.set_coord(5L);
    ASSERT_TRUE(_obj.query(&fea));
    EXPECT_EQ(0U, fea.bucket_idx());
    EXPECT_EQ(1L, fea.buckets(0).count());
    EXPECT_EQ(5L, fea.buckets(1).count());
    _obj.uninit(); 
    remove("./distinct_dis.ckpt");
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

