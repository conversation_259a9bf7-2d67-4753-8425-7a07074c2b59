// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// <AUTHOR>
// @brief

#include <iostream>
#include <fstream>
#include <boost/algorithm/string.hpp>
#include <boost/lexical_cast.hpp>
#include <gtest/gtest.h>
#include "fea_deviation_feature_accumulator.h"

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}

namespace anti {
namespace themis {
namespace feature_lib {

typedef FeaDeviationFeatureAccumulator::Item Item;
typedef FeaDeviationFeatureAccumulator::ItemPtr ItemPtr;

const std::string conf_path = "./conf";
const std::string conf_file = "fea_deviation.conf";

class TestFeaDeviationFeatureAccumulatorSuite : public ::testing::Test {
public:
    TestFeaDeviationFeatureAccumulatorSuite() {}
    ~TestFeaDeviationFeatureAccumulatorSuite() {}

    virtual void SetUp() {
        ASSERT_TRUE(_conf.load(conf_path.c_str(), conf_file.c_str()) == 0);
    }
    virtual void TearDown() {}

private:
    comcfg::Configure _conf;
};

TEST_F(TestFeaDeviationFeatureAccumulatorSuite, init_test) {
    FeaDeviationFeatureAccumulator obj;
    // no feature id
    EXPECT_FALSE(obj.init(_conf["feature"][2]));
    // no smooth window
    EXPECT_TRUE(obj.init(_conf["feature"][1]));
    EXPECT_EQ(1, obj._smooth_buffer->capacity());
    // ok case
    EXPECT_TRUE(obj.init(_conf["feature"][0]));
    EXPECT_EQ(3, obj._smooth_buffer->capacity());
}

TEST_F(TestFeaDeviationFeatureAccumulatorSuite, update_max_point_test) {
    FeaDeviationFeatureAccumulator obj;
    ASSERT_TRUE(obj.init(_conf["feature"][0]));
    FeaDeviationFeatureAccumulator::ItemPtrDeque feas;
    // no feas
    EXPECT_FALSE(obj._update_max_point(feas));
    // max_feas is NULL
    feas.emplace_back(ItemPtr(new Item(1, 100, 0)));
    EXPECT_TRUE(obj._update_max_point(feas));
    EXPECT_TRUE(obj._max_point != NULL);
    EXPECT_EQ(1, obj._max_point->value);
    EXPECT_EQ(100, obj._max_point->count);
    // max_feas dont need replace
    feas.emplace_back(ItemPtr(new Item(2, 90, 100)));
    EXPECT_FALSE(obj._update_max_point(feas));
    EXPECT_TRUE(obj._max_point != NULL);
    EXPECT_EQ(1, obj._max_point->value);
    EXPECT_EQ(100, obj._max_point->count);
    // max_feas need replace
    feas[1]->count = 110;
    EXPECT_TRUE(obj._update_max_point(feas));
    EXPECT_TRUE(obj._max_point != NULL);
    EXPECT_EQ(2, obj._max_point->value);
    EXPECT_EQ(110, obj._max_point->count);
    // max_feas dont need replace & no inflect
    feas.emplace_back(ItemPtr(new Item(3, 90, 210)));
    EXPECT_FALSE(obj._update_max_point(feas));
    EXPECT_TRUE(obj._max_point != NULL);
    EXPECT_EQ(2, obj._max_point->value);
    EXPECT_EQ(110, obj._max_point->count);
    // max_feas dont need replace
    feas.emplace_back(ItemPtr(new Item(4, 100, 300)));
    EXPECT_FALSE(obj._update_max_point(feas));
    EXPECT_TRUE(obj._max_point != NULL);
    EXPECT_EQ(2, obj._max_point->value);
    EXPECT_EQ(110, obj._max_point->count);
    // max_feas dont need replace when value is equal & add no inflect point
    feas.emplace_back(ItemPtr(new Item(5, 110, 400)));
    EXPECT_FALSE(obj._update_max_point(feas));
    EXPECT_TRUE(obj._max_point != NULL);
    EXPECT_EQ(2, obj._max_point->value);
    EXPECT_EQ(110, obj._max_point->count);
    // max_feas dont need relace
    feas.emplace_back(ItemPtr(new Item(6, 80, 510)));
    EXPECT_FALSE(obj._update_max_point(feas));
    feas.emplace_back(ItemPtr(new Item(7, 80, 590)));
    EXPECT_FALSE(obj._update_max_point(feas));
    feas.emplace_back(ItemPtr(new Item(8, 100, 670)));
    EXPECT_FALSE(obj._update_max_point(feas));
    EXPECT_TRUE(obj._max_point != NULL);
    EXPECT_EQ(2, obj._max_point->value);
    EXPECT_EQ(110, obj._max_point->count);
    // max_feas replace & add inflect point
    feas.emplace_back(ItemPtr(new Item(9, 100, 770)));
    EXPECT_FALSE(obj._update_max_point(feas));
    feas.emplace_back(ItemPtr(new Item(10, 150, 870)));
    EXPECT_TRUE(obj._update_max_point(feas));
    EXPECT_TRUE(obj._max_point != NULL);
    EXPECT_EQ(10, obj._max_point->value);
    EXPECT_EQ(150, obj._max_point->count);
}

TEST_F(TestFeaDeviationFeatureAccumulatorSuite, get_total_sum_test) {
    FeaDeviationFeatureAccumulator obj;
    ASSERT_TRUE(obj.init(_conf["feature"][0]));
    EXPECT_EQ(0, obj._get_total_sum(obj._smooth_feas));
    obj._smooth_feas.emplace_back(ItemPtr(new Item(10, 150, 0)));
    EXPECT_EQ(150, obj._get_total_sum(obj._smooth_feas));
    obj._smooth_feas.emplace_back(ItemPtr(new Item(11, 160, 150)));
    EXPECT_EQ(310, obj._get_total_sum(obj._smooth_feas));
}

TEST_F(TestFeaDeviationFeatureAccumulatorSuite, get_range_sum_test) {
    FeaDeviationFeatureAccumulator obj;
    ASSERT_TRUE(obj.init(_conf["feature"][0]));
    EXPECT_EQ(0, obj._get_range_sum(obj._smooth_feas, 10));
    obj._smooth_feas.emplace_back(ItemPtr(new Item(10, 150, 0)));
    auto itor = obj._smooth_feas.end();
    obj._fea_map[10] = --itor;
    obj._smooth_feas.emplace_back(ItemPtr(new Item(11, 160, 150)));
    itor = obj._smooth_feas.end();
    obj._fea_map[11] = --itor;
    obj._smooth_feas.emplace_back(ItemPtr(new Item(15, 170, 310)));
    itor = obj._smooth_feas.end();
    obj._fea_map[15] = --itor;
    EXPECT_EQ(150, obj._get_range_sum(obj._smooth_feas, 10));
    EXPECT_EQ(480, obj._get_range_sum(obj._smooth_feas, 15));
    EXPECT_EQ(0, obj._get_range_sum(obj._smooth_feas, 5));
    EXPECT_EQ(480, obj._get_range_sum(obj._smooth_feas, 100));
    EXPECT_EQ(-1, obj._get_range_sum(obj._smooth_feas, 14));
}

TEST_F(TestFeaDeviationFeatureAccumulatorSuite, get_inflect_range_sum_test) {
    FeaDeviationFeatureAccumulator obj;
    ASSERT_TRUE(obj.init(_conf["feature"][0]));
    obj._smooth_feas.emplace_back(ItemPtr(new Item(10, 150, 0)));
    EXPECT_EQ(150, obj._get_inflect_range_sum(obj._smooth_feas));
    obj._inflect_point.reset(new Item(1, 101, 200));
    EXPECT_EQ(301, obj._get_inflect_range_sum(obj._smooth_feas));
    EXPECT_EQ(301, obj._get_inflect_range_sum(obj._smooth_feas));
}

TEST_F(TestFeaDeviationFeatureAccumulatorSuite, store_cur_fea_test) {
    FeaDeviationFeatureAccumulator obj;
    ASSERT_TRUE(obj.init(_conf["feature"][0]));
    // add item not reach smooth window
    ItemPtr fea(new Item(1, 100, obj._get_total_sum(obj._smooth_feas)));
    obj._store_smooth_fea(fea);
    EXPECT_EQ(0, obj._smooth_feas.size());
    // add item not reach smooth window
    fea.reset(new Item(2, 80, obj._get_total_sum(obj._smooth_feas)));
    obj._store_smooth_fea(fea);
    EXPECT_EQ(0, obj._smooth_feas.size());
    // add item reach smooth window, add max_point
    fea.reset(new Item(3, 120, obj._get_total_sum(obj._smooth_feas)));
    obj._store_smooth_fea(fea);
    EXPECT_EQ(1, obj._smooth_feas.size());
    EXPECT_EQ(100, obj._smooth_feas[0]->count);
    EXPECT_EQ(3, obj._smooth_feas[0]->value);
    EXPECT_EQ(0, obj._smooth_feas[0]->pre_sum);
    EXPECT_TRUE(obj._max_point != NULL);
    EXPECT_TRUE(obj._max_point == obj._smooth_feas[0]);
    EXPECT_TRUE(obj._inflect_point == NULL);
    // add item, update max_point
    fea.reset(new Item(4, 160, obj._get_total_sum(obj._smooth_feas)));
    obj._store_smooth_fea(fea);
    EXPECT_EQ(2, obj._smooth_feas.size());
    EXPECT_EQ(100, obj._smooth_feas[0]->count);
    EXPECT_EQ(3, obj._smooth_feas[0]->value);
    EXPECT_EQ(0, obj._smooth_feas[0]->pre_sum);
    EXPECT_EQ(120, obj._smooth_feas[1]->count);
    EXPECT_EQ(4, obj._smooth_feas[1]->value);
    EXPECT_EQ(100, obj._smooth_feas[1]->pre_sum);
    EXPECT_TRUE(obj._max_point != NULL);
    EXPECT_TRUE(obj._max_point == obj._smooth_feas[1]);
    EXPECT_TRUE(obj._inflect_point == NULL);
    // add item, update max point
    fea.reset(new Item(5, 200, obj._get_total_sum(obj._smooth_feas)));
    obj._store_smooth_fea(fea);
    EXPECT_EQ(3, obj._smooth_feas.size());
    EXPECT_EQ(100, obj._smooth_feas[0]->count);
    EXPECT_EQ(3, obj._smooth_feas[0]->value);
    EXPECT_EQ(0, obj._smooth_feas[0]->pre_sum);
    EXPECT_EQ(120, obj._smooth_feas[1]->count);
    EXPECT_EQ(4, obj._smooth_feas[1]->value);
    EXPECT_EQ(100, obj._smooth_feas[1]->pre_sum);
    EXPECT_EQ(160, obj._smooth_feas[2]->count);
    EXPECT_EQ(5, obj._smooth_feas[2]->value);
    EXPECT_EQ(220, obj._smooth_feas[2]->pre_sum);
    EXPECT_TRUE(obj._max_point != NULL);
    EXPECT_TRUE(obj._max_point == obj._smooth_feas[2]);
    EXPECT_TRUE(obj._inflect_point == NULL);
    // add item, not update max_point nor update inflect points
    fea.reset(new Item(6, 80, obj._get_total_sum(obj._smooth_feas)));
    obj._store_smooth_fea(fea);
    EXPECT_EQ(4, obj._smooth_feas.size());
    EXPECT_EQ(146, obj._smooth_feas[3]->count);
    EXPECT_EQ(6, obj._smooth_feas[3]->value);
    EXPECT_EQ(380, obj._smooth_feas[3]->pre_sum);
    EXPECT_TRUE(obj._max_point != NULL);
    EXPECT_TRUE(obj._max_point == obj._smooth_feas[2]);
    EXPECT_TRUE(obj._inflect_point == NULL);
    // add item, not update max_point, but update inflect points
    fea.reset(new Item(7, 170, obj._get_total_sum(obj._smooth_feas)));
    obj._store_smooth_fea(fea);
    EXPECT_EQ(5, obj._smooth_feas.size());
    EXPECT_EQ(150, obj._smooth_feas[4]->count);
    EXPECT_EQ(7, obj._smooth_feas[4]->value);
    EXPECT_EQ(526, obj._smooth_feas[4]->pre_sum);
    EXPECT_TRUE(obj._max_point != NULL);
    EXPECT_TRUE(obj._max_point == obj._smooth_feas[2]);
    EXPECT_TRUE(obj._inflect_point != NULL);
    EXPECT_EQ(6, obj._inflect_point->value);
    EXPECT_EQ(146, obj._inflect_point->count);
    EXPECT_EQ(380, obj._inflect_point->pre_sum);
    // add item, update max_point, and clear inflect points
    fea.reset(new Item(8, 350, obj._get_total_sum(obj._smooth_feas)));
    obj._store_smooth_fea(fea);
    EXPECT_EQ(6, obj._smooth_feas.size());
    EXPECT_EQ(200, obj._smooth_feas[5]->count);
    EXPECT_EQ(8, obj._smooth_feas[5]->value);
    EXPECT_EQ(676, obj._smooth_feas[5]->pre_sum);
    EXPECT_TRUE(obj._max_point != NULL);
    EXPECT_TRUE(obj._max_point == obj._smooth_feas[5]);
    EXPECT_TRUE(obj._inflect_point == NULL);
    // add item, no update anything
    fea.reset(new Item(9, 20, obj._get_total_sum(obj._smooth_feas)));
    obj._store_smooth_fea(fea);
    EXPECT_EQ(7, obj._smooth_feas.size());
    EXPECT_EQ(180, obj._smooth_feas[6]->count);
    EXPECT_EQ(9, obj._smooth_feas[6]->value);
    EXPECT_EQ(876, obj._smooth_feas[6]->pre_sum);
    EXPECT_TRUE(obj._max_point != NULL);
    EXPECT_TRUE(obj._max_point == obj._smooth_feas[5]);
    EXPECT_TRUE(obj._inflect_point == NULL);
    // add item, update max & add inflect, but clear after
    fea.reset(new Item(10, 1000, obj._get_total_sum(obj._smooth_feas)));
    obj._store_smooth_fea(fea);
    EXPECT_EQ(8, obj._smooth_feas.size());
    EXPECT_EQ(456, obj._smooth_feas[7]->count);
    EXPECT_EQ(10, obj._smooth_feas[7]->value);
    EXPECT_EQ(1056, obj._smooth_feas[7]->pre_sum);
    EXPECT_TRUE(obj._max_point != NULL);
    EXPECT_TRUE(obj._max_point == obj._smooth_feas[7]);
    EXPECT_TRUE(obj._inflect_point == NULL);
}

FeatureValueProto mock_fea(int64_t value, uint64_t count) {
    FeatureValueProto feature;
    feature.set_feature_id(101);
    feature.set_view_sign(123);
    feature.set_feature_type(FeatureValueProto::FEA_DEVIATION);
    feature.set_joinkey(456);
    feature.set_log_id(1);
    feature.set_log_time(789);
    feature.set_coord(1);
    feature.set_view_name("cntname");
    feature.set_view("baiduid");
    feature.set_value("0.3#0.1#0.8");
    feature.mutable_fea_deviation_field()->set_devia_fea_value(value);
    feature.mutable_fea_deviation_field()->set_devia_fea_count(count);
    return feature;
}

TEST_F(TestFeaDeviationFeatureAccumulatorSuite, update_failed_test) {
    FeaDeviationFeatureAccumulator obj;
    ASSERT_TRUE(obj.init(_conf["feature"][0]));
    FeatureValueProto fea = mock_fea(10, 100);
    // no fea_deviation_field;
    fea.clear_fea_deviation_field();
    EXPECT_FALSE(obj.update(fea));
    // no devia_fea_value
    fea = mock_fea(10, 100);
    fea.mutable_fea_deviation_field()->clear_devia_fea_value();
    EXPECT_FALSE(obj.update(fea));
    // no devia_fea_count
    fea = mock_fea(10, 100);
    fea.mutable_fea_deviation_field()->clear_devia_fea_count();
    EXPECT_FALSE(obj.update(fea));
    // ok case
    fea = mock_fea(10, 100);
    EXPECT_TRUE(obj.update(fea));
}

TEST_F(TestFeaDeviationFeatureAccumulatorSuite, update_test) {
    FeaDeviationFeatureAccumulator obj;
    ASSERT_TRUE(obj.init(_conf["feature"][0]));
    EXPECT_TRUE(obj._cur_fea == NULL);
    FeatureValueProto fea = mock_fea(1, 20);
    EXPECT_TRUE(obj.update(fea));
    EXPECT_FALSE(obj._cur_fea == NULL);
    EXPECT_EQ(0, obj._smooth_feas.size());
    EXPECT_EQ(1, obj._fea_map.size());
    EXPECT_EQ(0, obj._get_total_sum(obj._smooth_feas));
    EXPECT_TRUE(obj._inflect_point == NULL);
    EXPECT_TRUE(obj._max_point == NULL);
    fea = mock_fea(1, 20);
    EXPECT_TRUE(obj.update(fea));
    EXPECT_FALSE(obj._cur_fea == NULL);
    EXPECT_EQ(0, obj._smooth_feas.size());
    EXPECT_EQ(1, obj._fea_map.size());
    EXPECT_EQ(0, obj._get_total_sum(obj._smooth_feas));
    EXPECT_TRUE(obj._inflect_point == NULL);
    EXPECT_TRUE(obj._max_point == NULL);
    fea = mock_fea(1, 60);
    EXPECT_TRUE(obj.update(fea));
    EXPECT_FALSE(obj._cur_fea == NULL);
    EXPECT_EQ(0, obj._smooth_feas.size());
    EXPECT_EQ(1, obj._fea_map.size());
    EXPECT_EQ(0, obj._get_total_sum(obj._smooth_feas));
    EXPECT_TRUE(obj._inflect_point == NULL);
    EXPECT_TRUE(obj._max_point == NULL);
    fea = mock_fea(2, 40);
    EXPECT_TRUE(obj.update(fea));
    EXPECT_EQ(0, obj._smooth_feas.size());
    EXPECT_EQ(2, obj._fea_map.size());
    EXPECT_EQ(0, obj._get_total_sum(obj._smooth_feas));
    EXPECT_TRUE(obj._inflect_point == NULL);
    EXPECT_TRUE(obj._max_point == NULL);
    fea = mock_fea(2, 40);
    EXPECT_TRUE(obj.update(fea));
    EXPECT_EQ(0, obj._smooth_feas.size());
    EXPECT_EQ(2, obj._fea_map.size());
    EXPECT_EQ(0, obj._get_total_sum(obj._smooth_feas));
    EXPECT_TRUE(obj._inflect_point == NULL);
    EXPECT_TRUE(obj._max_point == NULL);
    fea = mock_fea(3, 120);
    EXPECT_TRUE(obj.update(fea));
    EXPECT_EQ(0, obj._smooth_feas.size());
    EXPECT_EQ(3, obj._fea_map.size());
    EXPECT_EQ(0, obj._get_total_sum(obj._smooth_feas));
    EXPECT_TRUE(obj._inflect_point == NULL);
    EXPECT_TRUE(obj._max_point == NULL);
    fea = mock_fea(4, 40);
    EXPECT_TRUE(obj.update(fea));
    EXPECT_EQ(1, obj._smooth_feas.size());
    EXPECT_EQ(4, obj._fea_map.size());
    EXPECT_EQ(100, obj._get_total_sum(obj._smooth_feas));
    EXPECT_TRUE(obj._inflect_point == NULL);
    EXPECT_TRUE(obj._max_point != NULL);
    EXPECT_EQ(3, obj._max_point->value);
    fea = mock_fea(4, 50);
    EXPECT_TRUE(obj.update(fea));
    EXPECT_EQ(1, obj._smooth_feas.size());
    EXPECT_EQ(4, obj._fea_map.size());
    EXPECT_EQ(100, obj._get_total_sum(obj._smooth_feas));
    EXPECT_TRUE(obj._inflect_point == NULL);
    EXPECT_TRUE(obj._max_point != NULL);
    EXPECT_EQ(3, obj._max_point->value);
    fea = mock_fea(4, 30);
    EXPECT_TRUE(obj.update(fea));
    EXPECT_EQ(1, obj._smooth_feas.size());
    EXPECT_EQ(4, obj._fea_map.size());
    EXPECT_EQ(100, obj._get_total_sum(obj._smooth_feas));
    EXPECT_TRUE(obj._inflect_point == NULL);
    EXPECT_TRUE(obj._max_point != NULL);
    EXPECT_EQ(3, obj._max_point->value);
    fea = mock_fea(4, 40);
    EXPECT_TRUE(obj.update(fea));
    EXPECT_EQ(1, obj._smooth_feas.size());
    EXPECT_EQ(4, obj._fea_map.size());
    EXPECT_EQ(100, obj._get_total_sum(obj._smooth_feas));
    EXPECT_TRUE(obj._inflect_point == NULL);
    EXPECT_TRUE(obj._max_point != NULL);
    EXPECT_EQ(3, obj._max_point->value);
    fea = mock_fea(5, 130);
    EXPECT_TRUE(obj.update(fea));
    EXPECT_EQ(2, obj._smooth_feas.size());
    EXPECT_EQ(5, obj._fea_map.size());
    EXPECT_EQ(220, obj._get_total_sum(obj._smooth_feas));
    EXPECT_TRUE(obj._inflect_point == NULL);
    EXPECT_TRUE(obj._max_point != NULL);
    EXPECT_EQ(4, obj._max_point->value);
    fea = mock_fea(5, 70);
    EXPECT_TRUE(obj.update(fea));
    EXPECT_EQ(2, obj._smooth_feas.size());
    EXPECT_EQ(5, obj._fea_map.size());
    EXPECT_EQ(220, obj._get_total_sum(obj._smooth_feas));
    EXPECT_TRUE(obj._inflect_point == NULL);
    EXPECT_TRUE(obj._max_point != NULL);
    EXPECT_EQ(4, obj._max_point->value);
    fea = mock_fea(6, 80);
    EXPECT_TRUE(obj.update(fea));
    EXPECT_EQ(3, obj._smooth_feas.size());
    EXPECT_EQ(6, obj._fea_map.size());
    EXPECT_EQ(380, obj._get_total_sum(obj._smooth_feas));
    EXPECT_TRUE(obj._inflect_point == NULL);
    EXPECT_TRUE(obj._max_point != NULL);
    EXPECT_EQ(5, obj._max_point->value);
    fea = mock_fea(7, 170);
    EXPECT_TRUE(obj.update(fea));
    EXPECT_EQ(4, obj._smooth_feas.size());
    EXPECT_EQ(7, obj._fea_map.size());
    EXPECT_EQ(526, obj._get_total_sum(obj._smooth_feas));
    EXPECT_TRUE(obj._max_point != NULL);
    EXPECT_EQ(5, obj._max_point->value);
    EXPECT_TRUE(obj._inflect_point == NULL);
    fea = mock_fea(8, 100);
    EXPECT_TRUE(obj.update(fea));
    EXPECT_EQ(5, obj._smooth_feas.size());
    EXPECT_EQ(8, obj._fea_map.size());
    EXPECT_EQ(676, obj._get_total_sum(obj._smooth_feas));
    EXPECT_TRUE(obj._max_point != NULL);
    EXPECT_EQ(5, obj._max_point->value);
    EXPECT_TRUE(obj._inflect_point != NULL);
    EXPECT_EQ(6, obj._inflect_point->value);
    EXPECT_EQ(146, obj._inflect_point->count);
    EXPECT_EQ(380, obj._inflect_point->pre_sum);
    fea = mock_fea(8, 250);
    EXPECT_TRUE(obj.update(fea));
    EXPECT_EQ(5, obj._smooth_feas.size());
    EXPECT_EQ(8, obj._fea_map.size());
    EXPECT_EQ(676, obj._get_total_sum(obj._smooth_feas));
    EXPECT_TRUE(obj._max_point != NULL);
    EXPECT_EQ(5, obj._max_point->value);
    EXPECT_TRUE(obj._inflect_point != NULL);
    EXPECT_EQ(6, obj._inflect_point->value);
    EXPECT_EQ(146, obj._inflect_point->count);
    EXPECT_EQ(380, obj._inflect_point->pre_sum);
    fea = mock_fea(9, 20);
    EXPECT_TRUE(obj.update(fea));
    EXPECT_EQ(6, obj._smooth_feas.size());
    EXPECT_EQ(9, obj._fea_map.size());
    EXPECT_EQ(876, obj._get_total_sum(obj._smooth_feas));
    EXPECT_TRUE(obj._max_point != NULL);
    EXPECT_EQ(8, obj._max_point->value);
    EXPECT_TRUE(obj._inflect_point == NULL);
    fea = mock_fea(10, 100);
    EXPECT_TRUE(obj.update(fea));
    EXPECT_EQ(7, obj._smooth_feas.size());
    EXPECT_EQ(10, obj._fea_map.size());
    EXPECT_EQ(1056, obj._get_total_sum(obj._smooth_feas));
    EXPECT_TRUE(obj._max_point != NULL);
    EXPECT_EQ(8, obj._max_point->value);
    EXPECT_TRUE(obj._inflect_point == NULL);
    fea = mock_fea(10, 400);
    EXPECT_TRUE(obj.update(fea));
    EXPECT_EQ(7, obj._smooth_feas.size());
    EXPECT_EQ(10, obj._fea_map.size());
    EXPECT_EQ(1056, obj._get_total_sum(obj._smooth_feas));
    EXPECT_TRUE(obj._max_point != NULL);
    EXPECT_EQ(8, obj._max_point->value);
    EXPECT_TRUE(obj._inflect_point == NULL);
    fea = mock_fea(10, 500);
    EXPECT_TRUE(obj.update(fea));
    EXPECT_EQ(7, obj._smooth_feas.size());
    EXPECT_EQ(10, obj._fea_map.size());
    EXPECT_EQ(1056, obj._get_total_sum(obj._smooth_feas));
    EXPECT_TRUE(obj._max_point != NULL);
    EXPECT_EQ(8, obj._max_point->value);
    EXPECT_TRUE(obj._inflect_point == NULL);
    fea = mock_fea(11, 1);
    EXPECT_TRUE(obj.update(fea));
    EXPECT_EQ(8, obj._smooth_feas.size());
    EXPECT_EQ(11, obj._fea_map.size());
    EXPECT_EQ(1512, obj._get_total_sum(obj._smooth_feas));
    EXPECT_TRUE(obj._max_point != NULL);
    EXPECT_EQ(10, obj._max_point->value);
    EXPECT_TRUE(obj._inflect_point == NULL);
}

TEST_F(TestFeaDeviationFeatureAccumulatorSuite, query_failed_test) {
    FeaDeviationFeatureAccumulator obj;
    ASSERT_TRUE(obj.init(_conf["feature"][0]));
    // param illegal
    EXPECT_FALSE(obj.query(NULL));
    // no fea_deviation_field;
    FeatureValueProto fea = mock_fea(1, 100);
    fea.clear_fea_deviation_field();
    EXPECT_FALSE(obj.query(&fea));
    // no devia_fea_value
    fea = mock_fea(10, 100);
    fea.mutable_fea_deviation_field()->clear_devia_fea_value();
    EXPECT_FALSE(obj.query(&fea));
    // no devia_fea_count
    fea = mock_fea(10, 100);
    fea.mutable_fea_deviation_field()->clear_devia_fea_count();
    EXPECT_FALSE(obj.query(&fea));
    // ok case 0
    fea = mock_fea(10, 100);
    EXPECT_TRUE(obj.query(&fea));
    EXPECT_GT(0.001, fea.fea_deviation_field().devia_ratio() - 0.0000000001);
    EXPECT_GT(0.001, fea.fea_deviation_field().devia_inflect_ratio() -
            0.0000000001);
    // update some data, but 0
    fea = mock_fea(1, 20);
    ASSERT_TRUE(obj.update(fea));
    ASSERT_TRUE(obj.update(fea));
    fea = mock_fea(2, 100);
    ASSERT_TRUE(obj.update(fea));
    fea = mock_fea(3, 100);
    ASSERT_TRUE(obj.update(fea));
    fea = mock_fea(10, 100);
    EXPECT_TRUE(obj.query(&fea));
    EXPECT_GT(0.001, fea.fea_deviation_field().devia_ratio() - 1.0000000);
    EXPECT_GT(0.001, fea.fea_deviation_field().devia_inflect_ratio() -
            0.0000000001);
}

TEST_F(TestFeaDeviationFeatureAccumulatorSuite, query_test) {
    FeaDeviationFeatureAccumulator obj;
    ASSERT_TRUE(obj.init(_conf["feature"][0]));
    FeatureValueProto fea = mock_fea(1, 100);
    ASSERT_TRUE(obj.update(fea));
    fea = mock_fea(2, 80);
    ASSERT_TRUE(obj.update(fea));
    fea = mock_fea(3, 120);
    ASSERT_TRUE(obj.update(fea));
    fea = mock_fea(4, 160);
    ASSERT_TRUE(obj.update(fea));
    fea = mock_fea(5, 200);
    ASSERT_TRUE(obj.update(fea));
    fea = mock_fea(6, 80);
    ASSERT_TRUE(obj.update(fea));
    fea = mock_fea(7, 170);
    ASSERT_TRUE(obj.update(fea));
    fea = mock_fea(8, 10);
    ASSERT_TRUE(obj.update(fea));

    // test
    fea = mock_fea(1, 100);
    EXPECT_TRUE(obj.query(&fea));
    printf("%s\n", fea.ShortDebugString().c_str());
    fea = mock_fea(2, 80);
    EXPECT_TRUE(obj.query(&fea));
    printf("%s\n", fea.ShortDebugString().c_str());
    fea = mock_fea(3, 120);
    EXPECT_TRUE(obj.query(&fea));
    printf("%s\n", fea.ShortDebugString().c_str());
    fea = mock_fea(4, 160);
    EXPECT_TRUE(obj.query(&fea));
    printf("%s\n", fea.ShortDebugString().c_str());
    fea = mock_fea(5, 200);
    EXPECT_TRUE(obj.query(&fea));
    printf("%s\n", fea.ShortDebugString().c_str());
    fea = mock_fea(6, 80);
    EXPECT_TRUE(obj.query(&fea));
    printf("%s\n", fea.ShortDebugString().c_str());
    fea = mock_fea(7, 170);
    EXPECT_TRUE(obj.query(&fea));
    printf("%s\n", fea.ShortDebugString().c_str());
    fea = mock_fea(8, 350);
    EXPECT_TRUE(obj.query(&fea));
    printf("%s\n", fea.ShortDebugString().c_str());
    fea = mock_fea(9, 20);
    EXPECT_TRUE(obj.query(&fea));
    printf("%s\n", fea.ShortDebugString().c_str());
    fea = mock_fea(10, 1000);
    EXPECT_TRUE(obj.query(&fea));
    printf("%s\n", fea.ShortDebugString().c_str());
}

TEST_F(TestFeaDeviationFeatureAccumulatorSuite, open_file_test) {
    FeaDeviationFeatureAccumulator obj;
    ASSERT_TRUE(obj.init(_conf["feature"][3]));
    std::ifstream in("data/input", std::ios::in);
    ASSERT_TRUE(in.is_open());
    while (!in.eof()) {
        char buf[1000];
        in.getline(buf, 1000);
        std::string str(buf, strlen(buf));
        std::vector<std::string> vec;
        boost::split(vec, str, boost::is_any_of(" "), boost::token_compress_on);
        if (vec.size() != 3) {
            continue;
        }
        FeatureValueProto fea = mock_fea(
                boost::lexical_cast<int>(vec[2]),
                boost::lexical_cast<int>(vec[1]));
        EXPECT_TRUE(obj.update(fea));
    }
}

TEST_F(TestFeaDeviationFeatureAccumulatorSuite, dump_test) {
    FeaDeviationFeatureAccumulator obj;
    ASSERT_TRUE(obj.init(_conf["feature"][0]));
    FeatureValueProto fea = mock_fea(1, 100);
    ASSERT_TRUE(obj.update(fea));
    fea = mock_fea(2, 80);
    ASSERT_TRUE(obj.update(fea));
    fea = mock_fea(3, 120);
    ASSERT_TRUE(obj.update(fea));
    fea = mock_fea(4, 160);
    ASSERT_TRUE(obj.update(fea));
    fea = mock_fea(5, 200);
    ASSERT_TRUE(obj.update(fea));
    fea = mock_fea(6, 80);
    ASSERT_TRUE(obj.update(fea));
    fea = mock_fea(7, 170);
    ASSERT_TRUE(obj.update(fea));
    fea = mock_fea(8, 10);
    ASSERT_TRUE(obj.update(fea));
    std::string str = "";
    EXPECT_TRUE(obj._dump(&str));
    printf("%s", str.c_str());
}

TEST_F(TestFeaDeviationFeatureAccumulatorSuite, variance_test) {
    FeaDeviationFeatureAccumulator obj;
    ASSERT_TRUE(obj.init(_conf["feature"][0]));
    double variance = 0.0;
    ASSERT_FALSE(obj._get_variance(obj._original_feas, &variance));
    ASSERT_TRUE(obj.update(mock_fea(1,1)));
    ASSERT_TRUE(obj._get_variance(obj._original_feas, &variance));
    EXPECT_NEAR(variance, 0.0, 0.00001);

    ASSERT_TRUE(obj.update(mock_fea(2,1)));
    ASSERT_TRUE(obj.update(mock_fea(3,1)));
    ASSERT_TRUE(obj._get_variance(obj._original_feas, &variance));
    EXPECT_NEAR(variance, 0.66666666666, 0.00001);

    ASSERT_TRUE(obj.update(mock_fea(4,2)));
    ASSERT_TRUE(obj.update(mock_fea(5,3)));
    ASSERT_TRUE(obj._get_variance(obj._original_feas, &variance));
    EXPECT_NEAR(variance, 1.98438, 0.00001);

    ASSERT_TRUE(obj.update(mock_fea(6,4)));
    ASSERT_TRUE(obj.update(mock_fea(7,5)));
    ASSERT_TRUE(obj.update(mock_fea(8,6)));
    ASSERT_TRUE(obj._get_variance(obj._original_feas, &variance));
    EXPECT_NEAR(variance, 3.90548, 0.00001);
}

}
}
}
