// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: fea_val_rate_test.cpp
// @Last modified: 2017-12-27 09:30:07
// @Brief: 

#include "fea_value_rate_accumulator.h"
#include <gtest/gtest.h>
#include <bmock.h>
#include <archive.h>
#include "ckpt_test_util.h"

using ::testing::_;
using ::testing::Return;
using ::testing::DoAll;
using ::testing::SetArgPointee;
using ::testing::SetArgumentPointee;

namespace anti {
namespace themis {
namespace feature_lib {

BMOCK_NS_CLASS_METHOD1(anti::themis::feature_lib, FValueRPCManager, query,
        bool(const std::shared_ptr<FValQueryTransProto>&));

void resume_mock() {
    BMOCK_NS_CLASS_RESUME(anti::themis::feature_lib, FValueRPCManager, query,
            bool(const std::shared_ptr<FValQueryTransProto>&));
}

void stop_mock() {
    BMOCK_NS_CLASS_STOP(anti::themis::feature_lib, FValueRPCManager, query,
            bool(const std::shared_ptr<FValQueryTransProto>&));
}

const char* CONF_PATH = "./conf";
const char* CONF_FILE = "fea_val_rate_test.conf";
typedef anti::themis::common_lib::FileArchive FileArchive;

class FeaValueRateAccumulatorTestSuite : public ::testing::Test {
protected: 
    virtual void SetUp() {
        stop_mock();
        ASSERT_EQ(0, _conf.load(CONF_PATH, CONF_FILE));
        ASSERT_TRUE(_obj.init(_conf["valid"][0]));
        _obj._rpc_mgr.reset(new (std::nothrow) FValueRPCManager);
    }
    virtual void TearDown() {}

private:
    comcfg::Configure _conf;
    FeatureValueRateAccumulator _obj;
};

TEST_F(FeaValueRateAccumulatorTestSuite, RateItem_case) {
    RateItem obj;
    obj[NUMERATOR] = RateItem::Item(1, 2);
    obj[DENOMINATOR] = RateItem::Item(3, 4);
    
    const char* file = "tmp_lru_node.ckpt";
    std::string cmd = "rm -f ";
    cmd += file;
    system(cmd.c_str());
    // serialize
    FileArchive ar;
    ASSERT_TRUE(ar.open_w(file));
    ASSERT_TRUE(obj.serialize<FileArchive>(&ar));
    ar.close();

    // deserialize
    RateItem obj1;
    FileArchive ar1;
    ASSERT_TRUE(ar1.open_r(file));
    ASSERT_TRUE(obj1.deserialize<FileArchive>(&ar1));
    EXPECT_EQ(1, obj1[NUMERATOR].first);
    EXPECT_EQ(2, obj1[NUMERATOR].second);
    EXPECT_EQ(3, obj1[DENOMINATOR].first);
    EXPECT_EQ(4, obj1[DENOMINATOR].second);
    ar1.close();
}

TEST_F(FeaValueRateAccumulatorTestSuite, ctr_case) {
    FeatureValueRateAccumulator obj;
    ASSERT_EQ(0, obj._time_threshold);
}

TEST_F(FeaValueRateAccumulatorTestSuite, init_case) {
    for (uint32_t i = 0; i < _conf["invalid"].size(); ++i) {
        CWARNING_LOG("current : i:%lu", i);
        FeatureValueRateAccumulator obj;
        ASSERT_FALSE(obj._init(_conf["invalid"][i]));
    }
    FeatureValueRateAccumulator obj;
    ASSERT_TRUE(obj.init(_conf["valid"][0]));
    EXPECT_EQ(100, obj._time_threshold);
    EXPECT_EQ(123U, obj._ext_conf[NUMERATOR].ext_id);
    EXPECT_EQ(50, obj._ext_conf[NUMERATOR].remain);
    EXPECT_EQ(321U, obj._ext_conf[DENOMINATOR].ext_id);
    EXPECT_EQ(100, obj._ext_conf[DENOMINATOR].remain);
}

FeatureValueProto mock_fea() {
    FeatureValueProto fea;
    fea.set_feature_id(111);
    fea.set_view_sign(555);
    fea.set_coord(1001);
    fea.set_feature_type(FeatureValueProto::FEATURE_VALUE_EXTEND);
    auto info = fea.mutable_fea_serv_trans()->add_infos();
    info->mutable_request()->set_view_sign(567);
    info->mutable_request()->set_feature_id(123);
    info = fea.mutable_fea_serv_trans()->add_infos();
    info->mutable_request()->set_view_sign(789);
    info->mutable_request()->set_feature_id(321);
    return fea;
}

TEST_F(FeaValueRateAccumulatorTestSuite, _enter_window_case) {
    FeatureValueProto fea = mock_fea();
    ASSERT_TRUE(!_obj._window->find(fea.view_sign()));
    _obj._enter_window(fea.view_sign(), fea.coord());
    auto node = _obj._window->find(fea.view_sign());
    ASSERT_FALSE(!node);
    _obj._enter_window(fea.view_sign(), fea.coord());
}

TEST_F(FeaValueRateAccumulatorTestSuite, _triger_query_case) {
    FeatureValueProto fea = mock_fea();
    _obj._triger_query(fea);
    ASSERT_EQ(1U, _obj._requests.size());
    auto iter = _obj._requests.find(fea.view_sign());
    ASSERT_TRUE(iter != _obj._requests.end());
    ASSERT_EQ(2U, iter->second->infos_size());
    ASSERT_EQ(567U, iter->second->infos(0).request().view_sign());
    ASSERT_EQ(123U, iter->second->infos(0).request().feature_id());
    ASSERT_EQ(789U, iter->second->infos(1).request().view_sign());
    ASSERT_EQ(321U, iter->second->infos(1).request().feature_id());
}

TEST_F(FeaValueRateAccumulatorTestSuite, update_invalid_fea_case) {
    FeatureValueProto fea;
    EXPECT_FALSE(_obj.update(fea));
}

TEST_F(FeaValueRateAccumulatorTestSuite, update_succ_no_extend_case) {
    FeatureValueProto fea = mock_fea();
    _obj._window_conf.expiration = 10000;
    EXPECT_TRUE(_obj.update(fea));
    EXPECT_TRUE(_obj._requests.empty());
}

TEST_F(FeaValueRateAccumulatorTestSuite, update_succ_extend_case) {
    FeatureValueProto fea = mock_fea();
    _obj._window_conf.expiration = 0;
    ASSERT_TRUE(_obj.update(fea));
    EXPECT_EQ(1U, _obj._requests.size());
    ASSERT_TRUE(_obj.update(fea));
    EXPECT_EQ(1U, _obj._requests.size());
}

std::shared_ptr<FValQueryTransProto> mock_req() {
    std::shared_ptr<FValQueryTransProto> req(new (std::nothrow) FValQueryTransProto);
    auto info = req->add_infos();
    info->mutable_request()->set_view_sign(567);
    info->mutable_request()->set_feature_id(123);
    info->mutable_response()->add_value("500");
    info->mutable_response()->set_coord(1000);
    info = req->add_infos();
    info->mutable_request()->set_view_sign(789);
    info->mutable_request()->set_feature_id(321);
    info->mutable_response()->add_value("1000");
    info->mutable_response()->set_coord(2000);
    return req;
}

TEST_F(FeaValueRateAccumulatorTestSuite, sync_fail_case) {
    auto req = mock_req();
    req->mutable_infos(0)->clear_response();
    _obj._requests[123] = req;
    _obj.sync(NULL);
    _obj._requests.clear();
    _obj._requests[123] = mock_req();
    _obj.sync(NULL);
}

TEST_F(FeaValueRateAccumulatorTestSuite, sync_succ_case) {
    auto req = mock_req();
    FeatureValueProto fea = mock_fea();
    _obj._requests[fea.view_sign()] = req;
    auto node = _obj._enter_window(fea.view_sign(), fea.coord());
    ASSERT_FALSE(!node);
    _obj.sync(NULL);
    ASSERT_FALSE(!node->value());
    auto rate = node->value();
    EXPECT_EQ(500, (*rate)[NUMERATOR].first);
    EXPECT_EQ(1000, (*rate)[NUMERATOR].second);
    EXPECT_EQ(1000, (*rate)[DENOMINATOR].first);
    EXPECT_EQ(2000, (*rate)[DENOMINATOR].second);
    EXPECT_TRUE(_obj._requests.empty());
}

TEST_F(FeaValueRateAccumulatorTestSuite, query_invalid_fea_case) {
    ASSERT_FALSE(_obj.query(NULL));
    FeatureValueProto fea = mock_fea();
    ASSERT_FALSE(_obj.query(&fea));
}

TEST_F(FeaValueRateAccumulatorTestSuite, update_and_query_succ_but_invalid_case) {
    resume_mock();
    auto req = mock_req();
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(anti::themis::feature_lib, FValueRPCManager, query), 
            query(_))
            .WillOnce(DoAll(SetArgPointee<0>(*req), Return(true)));
    _obj._window_conf.expiration = 0;
    _obj._time_threshold = 0;
    FeatureValueProto fea = mock_fea();
    ASSERT_TRUE(_obj.update(fea));
    _obj.sync(NULL);
    ASSERT_TRUE(_obj.query(&fea));
    EXPECT_EQ(500, fea.filter_count());
    EXPECT_EQ(1000, fea.refer_count());
    CWARNING_LOG("value:%s", fea.value().data());
    EXPECT_FALSE(fea.valid());
}

} // namespace feature_lib
} // namespace themis
} // namespace anti

/* vim: set ts=4 sw=4: */

