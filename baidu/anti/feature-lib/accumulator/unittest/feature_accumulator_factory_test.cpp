// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangji<PERSON><PERSON>(<EMAIL>)
// 
// @File: feature_accumulator_factory_test.cpp
// @Last modified: 2017-10-31 17:29:32
// @Brief: 

#include <gtest/gtest.h>
#include "feature_accumulator_factory.h"
#include "segment_feature_accumulator.h"
#include "ratio_feature_accumulator.h"
#include "distribution_feature_accumulator.h"
#include "basic_feature_accumulator.h"
#include "ratio_black_feature_accumulator.h"
#include "segment_black_feature_accumulator.h"
#include "count_distribution_feature_accumulator.h"
#include "rate_feature_accumulator.h"
#include "rate_distribution_feature_accumulator.h"
#include "fea_value_dis_accumulator.h"
#include "fea_value_rate_accumulator.h"

namespace anti {
namespace themis {
namespace feature_lib {

DECLARE_bool(enable_fea_service_acc);

TEST(FeatureAccumulatorFactoryTestSuite, create_case) {
    FeatureAccumulatorInterface* obj = NULL;
    EXPECT_TRUE(FeatureAccumulatorFactory::create("xxxx") == NULL);
    
    obj = FeatureAccumulatorFactory::create("segment");
    EXPECT_TRUE(dynamic_cast<SegmentFeatureAccumulator*>(obj) != NULL);
    delete obj;
    obj = NULL;

    obj = FeatureAccumulatorFactory::create("behavior_segment");
    EXPECT_TRUE(dynamic_cast<SegmentFeatureAccumulator*>(obj) != NULL);
    delete obj;
    obj = NULL;

    obj = FeatureAccumulatorFactory::create("sum_segment");
    EXPECT_TRUE(dynamic_cast<SegmentFeatureAccumulator*>(obj) != NULL);
    delete obj;
    obj = NULL;

    obj = FeatureAccumulatorFactory::create("ratio");
    EXPECT_TRUE(dynamic_cast<RatioFeatureAccumulator*>(obj) != NULL);
    delete obj;
    obj = NULL;

    obj = FeatureAccumulatorFactory::create("behavior_ratio");
    EXPECT_TRUE(dynamic_cast<RatioFeatureAccumulator*>(obj) != NULL);
    delete obj;
    obj = NULL;

    obj = FeatureAccumulatorFactory::create("distribution");
    EXPECT_TRUE(dynamic_cast<DistributionFeatureAccumulator*>(obj) != NULL);
    delete obj;
    obj = NULL;

    obj = FeatureAccumulatorFactory::create("behavior_distribution");
    EXPECT_TRUE(dynamic_cast<DistributionFeatureAccumulator*>(obj) != NULL);
    delete obj;
    obj = NULL;

    obj = FeatureAccumulatorFactory::create("file_dict");
    EXPECT_TRUE(dynamic_cast<BasicFeatureAccumulator*>(obj) != NULL);
    delete obj;
    obj = NULL;

    obj = FeatureAccumulatorFactory::create("carespace");
    EXPECT_TRUE(dynamic_cast<BasicFeatureAccumulator*>(obj) != NULL);
    delete obj;
    obj = NULL;

    obj = FeatureAccumulatorFactory::create("original");
    EXPECT_TRUE(dynamic_cast<BasicFeatureAccumulator*>(obj) != NULL);
    delete obj;
    obj = NULL;

    obj = FeatureAccumulatorFactory::create("auto_ratio");
    EXPECT_TRUE(dynamic_cast<RatioBlackFeatureAccumulator*>(obj) != NULL);
    delete obj;
    obj = NULL;

    obj = FeatureAccumulatorFactory::create("auto_segment");
    EXPECT_TRUE(dynamic_cast<SegmentBlackFeatureAccumulator*>(obj) != NULL);
    delete obj;
    obj = NULL;

    obj = FeatureAccumulatorFactory::create("count_distribution");
    EXPECT_TRUE(dynamic_cast<CountDistributionFeatureAccumulator*>(obj) != NULL);
    delete obj;
    obj = NULL;

    obj = FeatureAccumulatorFactory::create("rate");
    EXPECT_TRUE(dynamic_cast<RateFeatureAccumulator*>(obj) != NULL);
    delete obj;
    obj = NULL;

    obj = FeatureAccumulatorFactory::create("cpm");
    EXPECT_TRUE(dynamic_cast<RateFeatureAccumulator*>(obj) != NULL);
    delete obj;
    obj = NULL;

    obj = FeatureAccumulatorFactory::create("rate_distribution");
    EXPECT_TRUE(dynamic_cast<RateDistributionFeatureAccumulator*>(obj) != NULL);
    delete obj;
    obj = NULL;

    obj = FeatureAccumulatorFactory::create("cpm_distribution");
    EXPECT_TRUE(dynamic_cast<RateDistributionFeatureAccumulator*>(obj) != NULL);
    delete obj;
    obj = NULL;

    obj = FeatureAccumulatorFactory::create("fea_value_distribution");
    EXPECT_TRUE(dynamic_cast<FeaValueDisAccumulator*>(obj) != NULL);
    delete obj;
    obj = NULL;

    obj = FeatureAccumulatorFactory::create("fea_value_rate");
    EXPECT_TRUE(dynamic_cast<FeatureValueRateAccumulator*>(obj) != NULL);
    delete obj;
    obj = NULL;
}

TEST(FeatureAccumulatorFactoryTestSuite, create_disable_fs_acc_case) {
    FeatureAccumulatorInterface* obj = NULL;
    FLAGS_enable_fea_service_acc = false;

    obj = FeatureAccumulatorFactory::create("fea_value_distribution");
    EXPECT_TRUE(dynamic_cast<BasicFeatureAccumulator*>(obj) != NULL);
    EXPECT_TRUE(dynamic_cast<FeaValueDisAccumulator*>(obj) == NULL);
    delete obj;
    obj = NULL;

    obj = FeatureAccumulatorFactory::create("fea_value_rate");
    EXPECT_TRUE(dynamic_cast<BasicFeatureAccumulator*>(obj) != NULL);
    EXPECT_TRUE(dynamic_cast<FeatureValueRateAccumulator*>(obj) == NULL);
    delete obj;
    obj = NULL;

    FLAGS_enable_fea_service_acc = true;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

