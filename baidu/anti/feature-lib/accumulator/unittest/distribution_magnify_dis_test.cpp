// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: changqing(<EMAIL>)
// 
// @File: distribution_feature_accumulator_test.cpp
// @Last modified: 2017-12-28 17:41:37
// @Brief: 

#include <vector>
#include <gtest/gtest.h>
#include <boost/lexical_cast.hpp>
#include "distribution_distance.cpp"
#include "posix_themis_io.h" 
#include "ckpt_test_util.h"

namespace anti {
namespace themis {
namespace feature_lib {

class DistributionDistanceTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {}
    virtual void TearDown() {}
    
private:
    DistributionDistance _obj;
};

TEST_F(DistributionDistanceTestSuite, magnify_dis_case) {
    {
        int count_threshold = 2;
        std::string func_type = "chi_square_dis";
        std::string sta_pro = "0.25,0.25,0.25,0.25";
        double max_distance_magnify = 5.0;
        _obj.init(func_type, sta_pro, count_threshold, max_distance_magnify);
        std::vector<double> pb = {0.25, 0.25, 0.25, 0.25};
        std::vector<double> pc = {0.1, 0.1, 0.05, 0.75};
        std::vector<double> pc_expr = {0.5, 0.5, 0.25, 3.75};
        EXPECT_TRUE(_obj.magnify_dis(pb, &pc));
        for (uint i = 0; i < pc_expr.size(); ++i) {
            EXPECT_DOUBLE_EQ(pc_expr[i], pc[i]);
        }
    }

    {
        int count_threshold = 2;
        std::string func_type = "chi_square_dis";
        std::string sta_pro = "0.25,0.25,0.25,0.25"; 
        double max_distance_magnify = 5.0;
        _obj.init(func_type, sta_pro, count_threshold, max_distance_magnify);
        std::vector<double> pb = {0.25, 0.25, 0.25, 0.25};
        std::vector<double> pc = {0.01, 0.01, 0.23, 0.75};
        std::vector<double> pc_expr = {0.05, 0.05, 1.1500000000000001, 3.75};
        EXPECT_TRUE(_obj.magnify_dis(pb, &pc));
        for (uint i = 0; i < pc_expr.size(); ++i) {
            EXPECT_DOUBLE_EQ(pc_expr[i], pc[i]);
        }
    }

    {
        int count_threshold = 2;
        std::string func_type = "chi_square_dis";
        std::string sta_pro = "0.25,0.25,0.25,0.25"; 
        double max_distance_magnify = 1.0;
        _obj.init(func_type, sta_pro, count_threshold, max_distance_magnify);
        std::vector<double> pb = {0.25, 0.25, 0.25, 0.25};
        std::vector<double> pc = {0.1, 0.1, 0.05, 0.75};
        std::vector<double> pc_expr = {0.1, 0.1, 0.05, 0.75};
        EXPECT_TRUE(_obj.magnify_dis(pb, &pc));
        for (uint i = 0; i < pc_expr.size(); ++i) {
            EXPECT_DOUBLE_EQ(pc_expr[i], pc[i]);
        }
    }

    {
        int count_threshold = 2;
        std::string func_type = "chi_square_dis";
        std::string sta_pro = "0.25,0.25,0.25,0.25"; 
        double max_distance_magnify = 1.0;
        _obj.init(func_type, sta_pro, count_threshold, max_distance_magnify);
        std::vector<double> pb = {0.25, 0.25, 0.25, 0.25};
        std::vector<double> pc = {0.01, 0.01, 0.23, 0.75};
        std::vector<double> pc_expr = {0.01, 0.01, 0.23, 0.75};
        EXPECT_TRUE(_obj.magnify_dis(pb, &pc));
        for (uint i = 0; i < pc_expr.size(); ++i) {
            EXPECT_DOUBLE_EQ(pc_expr[i], pc[i]);
        }
    }
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

