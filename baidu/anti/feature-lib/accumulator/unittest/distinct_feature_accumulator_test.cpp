// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// @Author: <PERSON>(<EMAIL>)
// 
// @Brief: 

#include <vector>
#include <gtest/gtest.h>
#include "boost/lexical_cast.hpp"
#include "distinct_feature_accumulator.h"
#include "posix_themis_io.h"
#include "ckpt_test_util.h"

namespace anti {
namespace themis {
namespace feature_lib {

DECLARE_bool(need_set_distinct_data_view_signs);
class DistinctFeatureAccumulatorTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
        ASSERT_TRUE(_conf.load("./conf", "accumulator_test.conf") == 0);

        int64_t coords[] = {1L, 2L, 3L, 4L, 5L};
        for (uint32_t i = 0U; i < 5U; ++i) {
            FeatureValueProto fea;
            fea.set_feature_id(140LU);
            fea.set_view_sign(123456LU);
            fea.set_feature_type(FeatureValueProto::DISTINCT);
            fea.set_joinkey(119LU);
            fea.set_log_id(120LU);
            fea.set_log_time(1111111111LU);
            fea.set_coord(coords[i]);
            fea.set_data_view_sign(222222LU + i);
            _feas.push_back(fea);
        }
        _old_fea = _feas[0];
        _old_fea.set_coord(0L);

        FeatureValueProto fea;
        fea.set_feature_id(123456789LU);
        _inv_feas.push_back(fea);

        fea.set_feature_id(140LU);
        fea.set_feature_type(FeatureValueProto::RATIO);
        _inv_feas.push_back(fea);
    }
    virtual void TearDown() {
        _feas.clear();
        _inv_feas.clear();
    }

private:
    typedef anti::themis::common_lib::DistinctItem DistinctItem;
    DistinctFeatureAccumulator _obj;
    comcfg::Configure _conf;

    std::vector<FeatureValueProto> _feas;
    FeatureValueProto _old_fea;
    std::vector<FeatureValueProto> _inv_feas;
};

TEST_F(DistinctFeatureAccumulatorTestSuite, construction_case) {
    EXPECT_EQ(0LU, _obj.feature_id());
    EXPECT_EQ(0LU, _obj._version);
}

TEST_F(DistinctFeatureAccumulatorTestSuite, init_case) {
    uint32_t num = _conf["invalid_distinct"]["feature"].size();
    ASSERT_LE(1U, num);
    for (uint32_t i = 0U; i < num; ++i) {
        EXPECT_FALSE(_obj.init(_conf["invalid_distinct"]["feature"][i]));
        _obj.uninit();
    }

    num = _conf["valid_distinct"]["feature"].size();
    ASSERT_LE(1U, num);
    ASSERT_TRUE(_obj.init(_conf["valid_distinct"]["feature"][0]));
    EXPECT_EQ(140LU, _obj.feature_id());
    EXPECT_EQ(1LU, _obj._version);
    _obj.uninit();

    EXPECT_EQ(0LU, _obj.feature_id());
    EXPECT_EQ(0LU, _obj._version);
}

TEST_F(DistinctFeatureAccumulatorTestSuite, test_update_old_node) {
    ASSERT_TRUE(_obj.init(_conf["valid_distinct"]["feature"][0]));
    ASSERT_TRUE(_obj.update(_feas[4]));
    const DistinctItem* item = _obj._window.query_segment(
            static_cast<uint64_t>(_feas[4].view_sign()));
    
    ASSERT_FALSE(_obj.update(_old_fea));
    ASSERT_EQ(1U, item->distinct_num());
    FeatureValueProto feat = _feas[4];
    ASSERT_TRUE(_obj.query(&feat));
    EXPECT_EQ(1U, boost::lexical_cast<int64_t>(feat.value()));
    _obj.uninit();
}

TEST_F(DistinctFeatureAccumulatorTestSuite, test_update_slide_case) {
    ASSERT_TRUE(_obj.init(_conf["valid_distinct"]["feature"][0]));
    // invalid fea
    EXPECT_FALSE(_obj.query(NULL));

    for (uint32_t i = 0U; i< _inv_feas.size(); ++i) {
        EXPECT_FALSE(_obj.update(_inv_feas[i]));
        EXPECT_FALSE(_obj.query(&_inv_feas[i]));
    }
    
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(_feas[i]));
    }
    
    ASSERT_FALSE(_obj.update(_old_fea));
    FeatureValueProto feat = _feas[4];
    ASSERT_TRUE(_obj.query(&feat));
    EXPECT_EQ(3U, boost::lexical_cast<int64_t>(feat.value()));

    std::vector<FeatureValueProto> other_feas;
    for (uint32_t i = 2U; i < _feas.size(); ++i) {
        feat = _feas[i];
        feat.set_data_view_sign(33333LU + i);
        other_feas.push_back(feat);
    }
    feat = _feas[4];
    for (uint32_t i = 0U; i < other_feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(other_feas[i]));
        ASSERT_TRUE(_obj.query(&feat));
        EXPECT_EQ(3U + i + 1U, boost::lexical_cast<int64_t>(feat.value()));
    }

    for (uint32_t i = 2U; i < other_feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(other_feas[i]));
        ASSERT_TRUE(_obj.query(&feat));
        EXPECT_EQ(6U, boost::lexical_cast<int64_t>(feat.value()));
    }
    
    ASSERT_FALSE(_obj.update(_old_fea));
    // query
    feat = _feas[4];
    ASSERT_TRUE(_obj.query(&feat));
    EXPECT_EQ(6U, boost::lexical_cast<int64_t>(feat.value()));
    _obj.uninit();
}

TEST_F(DistinctFeatureAccumulatorTestSuite, test_update_and_query) {
    ASSERT_TRUE(_obj.init(_conf["valid_distinct"]["feature"][0]));
    // invalid fea
    EXPECT_FALSE(_obj.query(NULL));

    for (uint32_t i = 0U; i< _inv_feas.size(); ++i) {
        EXPECT_FALSE(_obj.update_and_query(&_inv_feas[i]));
    }
    
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(_feas[i]));
    }
    
    ASSERT_FALSE(_obj.update(_old_fea));
    FeatureValueProto feat = _feas[4];
    ASSERT_TRUE(_obj.query(&feat));
    EXPECT_EQ(3U, boost::lexical_cast<int64_t>(feat.value()));

    std::vector<FeatureValueProto> other_feas;
    for (uint32_t i = 2U; i < _feas.size(); ++i) {
        feat = _feas[i];
        feat.set_data_view_sign(33333LU + i);
        other_feas.push_back(feat);
    }
    feat = _feas[1];
    for (uint32_t i = 0U; i < other_feas.size(); ++i) {
        ASSERT_TRUE(_obj.update_and_query(&other_feas[i]));
        EXPECT_EQ(3U + i + 1U, boost::lexical_cast<int64_t>(other_feas[i].value()));
    }

    for (uint32_t i = 0U; i < other_feas.size(); ++i) {
        ASSERT_TRUE(_obj.update_and_query(&other_feas[i]));
        EXPECT_EQ(6U, boost::lexical_cast<int64_t>(other_feas[i].value()));
    }
    
    ASSERT_FALSE(_obj.update(_old_fea));
    // query
    feat = _feas[4];
    ASSERT_TRUE(_obj.query(&feat));
    EXPECT_EQ(6U, boost::lexical_cast<int64_t>(feat.value()));
    _obj.uninit();
}

TEST_F(DistinctFeatureAccumulatorTestSuite, test_dump_and_load) {
    ASSERT_TRUE(_obj.init(_conf["valid_distinct"]["feature"][0]));

    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(_feas[i]));
    }
    
    FeatureValueProto feat = _feas[4];
    ASSERT_TRUE(_obj.query(&feat));
    EXPECT_EQ(3U, boost::lexical_cast<int64_t>(feat.value()));
    std::string type = "dist";
    DOING_DUMP();
    _obj.uninit();
    ASSERT_TRUE(_obj.init(_conf["valid_distinct"]["feature"][0]));
    DOING_LOAD();
    FeatureValueProto feat2 = _feas[4];
    ASSERT_TRUE(_obj.query(&feat2));
    EXPECT_EQ(3U, boost::lexical_cast<int64_t>(feat2.value()));
    _obj.uninit();
}

TEST_F(DistinctFeatureAccumulatorTestSuite, test_query_ext_fea) {
    ASSERT_TRUE(_obj.init(_conf["valid_distinct"]["feature"][0]));
    // case: input invalid
    FValQRequestProto request;
    ASSERT_FALSE(_obj.query(request, NULL));
    // case:  query from window fail
    request.set_view_sign(0U);
    FValQResponseProto response;
    ASSERT_TRUE(_obj.query(request, &response));
    EXPECT_EQ(0, response.value_size());
    
    ASSERT_TRUE(_obj.update(_feas[4]));
    // case: succ
    FValQRequestProto request1;
    request1.set_view_sign(_feas[4].view_sign());
    ASSERT_TRUE(_obj.query(request1, &response));
    const DistinctItem* item = _obj._window.query_segment(
            static_cast<uint64_t>(_feas[4].view_sign()));
    std::string exp(boost::lexical_cast<std::string>(item->distinct_num()));
    ASSERT_EQ(1, response.value_size());
    EXPECT_STREQ(exp.data(), response.value(0).data());
}

TEST_F(DistinctFeatureAccumulatorTestSuite, test_query_ext_fea_with_data_view_signs) {
    ASSERT_TRUE(_obj.init(_conf["valid_distinct"]["feature"][0]));
    ASSERT_TRUE(_obj.update(_feas[4]));
    FeatureValueProto feat = _feas[4];
    // close flag
    ASSERT_TRUE(_obj.query(&feat));
    ASSERT_EQ(0, feat.data_view_signs_size());

    // open flag
    FLAGS_need_set_distinct_data_view_signs = true;
    ASSERT_TRUE(_obj.update(_feas[4]));
    ASSERT_TRUE(_obj.query(&feat));
    ASSERT_EQ(1, feat.data_view_signs_size());
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

