// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: basic_feature_accumulator_test.cpp
// @Last modified: 2017-03-16 14:25:14
// @Brief: 

#include <gtest/gtest.h>
#include "basic_feature_accumulator.h"

namespace anti {
namespace themis {
namespace feature_lib {

class BasicFeatureAccumulatorTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {}
    virtual void TearDown() {}

private:
    BasicFeatureAccumulator _obj;
};

TEST_F(BasicFeatureAccumulatorTestSuite, construction_case) {
    EXPECT_EQ(0LU, _obj.feature_id());
}

TEST_F(BasicFeatureAccumulatorTestSuite, init_case) {
    comcfg::Configure conf;
    ASSERT_TRUE(conf.load("./conf", "accumulator_test.conf") == 0);
    EXPECT_FALSE(_obj.init(conf["invalid"]["feature"][0]));
    _obj.uninit();

    ASSERT_TRUE(_obj.init(conf["valid"]["feature"][0]));
    EXPECT_EQ(100LU, _obj.feature_id());
    _obj.uninit();
}

TEST_F(BasicFeatureAccumulatorTestSuite, update_and_query_case) {
    FeatureValueProto fea;
    EXPECT_TRUE(_obj.update(fea));
    EXPECT_TRUE(_obj.update_and_query(&fea));
    EXPECT_TRUE(_obj.query(&fea));
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

