// Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
// @Author: ch<PERSON><PERSON><PERSON><PERSON>(ch<PERSON><PERSON><PERSON><PERSON>@baidu.com)
// 
// @File: checkpoint_manager_test.cpp
// @Last modified: 2017-04-13 15:15:02
// @Brief: 

#include "checkpoint_manager.h"
#include <bmock.h>
#include <gmock/gmock.h>
#include <fstream>
#include <gtest/gtest.h>
#include "feature_accumulator_interface.h"
#include "feature_accumulator_factory.h"
#include "posix_themis_io.h"

namespace anti {
namespace themis {
namespace feature_lib {

using ::testing::_;
using ::testing::Return;
using ::testing::DoAll;
using ::testing::SetArgPointee;
using ::testing::SetArgumentPointee;

const std::string ckpt_path = "./ckpt/";

// mock 
// BMOCK_NS_CLASS_METHOD1(anti::themis::feature_lib, FeatureAccumulatorInterface,
//         dump, bool(PosixIoReaderInterface*));
// 
// void dump_resume_module_mocks() {
//     BMOCK_NS_CLASS_RESUME(anti::themis::feature_lib, FeatureAccumulatorInterface,
//             dump, bool(_));
// }
// 
// void stop_module_mocks() {
//     BMOCK_NS_CLASS_STOP(anti::themis::feature_lib, FeatureAccumulatorInterface,
//             dump, bool(_));
// }
//
class MockFeatureAccumulatorInterface : public FeatureAccumulatorInterface {
public:
    MOCK_CONST_METHOD0(feature_id, uint64_t());
    MOCK_METHOD1(init, bool(const comcfg::ConfigUnit&));
    MOCK_METHOD0(uninit, void());
    MOCK_METHOD1(update, bool(const FeatureValueProto&));
    MOCK_METHOD1(update_and_query, bool(FeatureValueProto*));
    MOCK_CONST_METHOD1(query, bool(FeatureValueProto*));
    MOCK_METHOD1(load, bool(const std::string&));
    MOCK_CONST_METHOD1(dump, bool(const std::string&));
    MOCK_CONST_METHOD1(dump, bool(PosixIoWriterInterface *writer));
    MOCK_METHOD1(load, bool(PosixIoReaderInterface *reader));

};

class CheckpointManagerTestSuite : public ::testing::Test {
protected:
    CheckpointManagerTestSuite() : _obj("./ckpt/") {}
    virtual void SetUp() {
        // stop_module_mocks();
    }
    virtual void TearDown() {
        // stop_module_mocks();
    }
    using CoordMap = std::unordered_map<uint64_t, int64_t>;
    using AccMap = std::unordered_map<uint64_t, 
            std::shared_ptr<FeatureAccumulatorInterface>>;
    using FileDescsMap = std::unordered_map<std::string,
            std::vector<DescInfo>>;
private:
    CheckpointManager _obj;
    MockFeatureAccumulatorInterface _mock;
};

TEST_F(CheckpointManagerTestSuite, clear_case) {
}

TEST_F(CheckpointManagerTestSuite, _dump_load_desc_case) {
    std::vector<DescInfo> desc_infos;
    for (uint64_t i = 0; i < 9; ++i) {
        std::string file = "./ckpt/ckpt_" + std::to_string(i); 
        off_t off = 0;
        size_t le = 0;
        DescInfo desc(i, file.c_str(), off, le); 
        desc_infos.emplace_back(std::move(desc));
    }
    ASSERT_TRUE(_obj._dump_desc(desc_infos));
    rename("./ckpt/describe.tmp", "./ckpt/describe");

    FileDescsMap file_descs_map;
    ASSERT_TRUE(_obj._load_desc(&file_descs_map));
    ASSERT_TRUE(file_descs_map.find("./ckpt/ckpt_8") != file_descs_map.end());
    ASSERT_TRUE(file_descs_map.find("./ckpt/ckpt_xxx") == file_descs_map.end());
    ASSERT_EQ(file_descs_map["./ckpt/ckpt_8"].size(), 1);
    ASSERT_EQ(file_descs_map["./ckpt/ckpt_8"][0].fea_id, 8);
    ASSERT_EQ(file_descs_map["./ckpt/ckpt_8"][0].file, "./ckpt/ckpt_8");
    ASSERT_EQ(file_descs_map["./ckpt/ckpt_8"][0].off_set, 0);
    ASSERT_EQ(file_descs_map["./ckpt/ckpt_8"][0].length, 0);

    DescInfo desc(0, "./ckpt/ckpt_1", 0, 0);
    desc_infos.emplace_back(std::move(desc));
    ASSERT_TRUE(_obj._dump_desc(desc_infos));
    file_descs_map.clear();
    rename("./ckpt/describe.tmp", "./ckpt/describe");
    ASSERT_FALSE(_obj._load_desc(&file_descs_map));

    file_descs_map.clear();
    std::ofstream out("./ckpt/describe");
    out << "xxxxxxxxxxxxxxxxxx";
    out.close();
    ASSERT_FALSE(_obj._load_desc(&file_descs_map));
}

std::shared_ptr<FeatureAccumulatorInterface> make_seg(uint64_t fea_id) {
    comcfg::Configure conf;
    conf.load("./conf", "ckpt_make_seg_test.conf");
    auto unit_conf = conf["valid"]["feature"][0].add_unit(
            "feature_id", std::to_string(fea_id).c_str());
    auto seg_ptr = FeatureAccumulatorFactory::create("segment");
    std::shared_ptr<FeatureAccumulatorInterface> ptr(seg_ptr);
    ptr->init(conf["valid"]["feature"][0]);

    std::vector<FeatureValueProto> feas;
    int64_t coords[] = {0L, 1L, 2L, 3L, 3L};
    std::string dv[] = {"123", "xxx", "456", "789", "0"};
    for (uint32_t i = 0U; i < 5U; ++i) {
        FeatureValueProto fea;
        fea.set_feature_id(fea_id);
        fea.set_view_sign(123456LU);
        fea.set_feature_type(FeatureValueProto::SEGMENT);
        fea.set_joinkey(119LU);
        fea.set_log_id(120LU);
        fea.set_log_time(1111111111LU);
        fea.set_coord(coords[i]);
        fea.set_data_view_value(dv[i]);
        feas.push_back(fea);
    }
    for (uint32_t i = 0U; i < feas.size(); ++i) {
        ptr->update(feas[i]);
    }
    return ptr; 
}

std::shared_ptr<FeatureAccumulatorInterface> empty_seg(uint64_t fea_id) {
    comcfg::Configure conf;
    conf.load("./conf", "ckpt_make_seg_test.conf");
    auto unit_conf = conf["valid"]["feature"][0].add_unit(
            "feature_id", std::to_string(fea_id).c_str());
    auto seg_ptr = FeatureAccumulatorFactory::create("segment");
    std::shared_ptr<FeatureAccumulatorInterface> ptr(seg_ptr);
    ptr->init(conf["valid"]["feature"][0]);
    return ptr;
}

TEST_F(CheckpointManagerTestSuite, _thread_dump_load_test) {
    auto queue_ptr = std::shared_ptr<ThreadSafeQueue<FeaInfo>>(
            new(std::nothrow) ThreadSafeQueue<FeaInfo>());
    for (uint64_t i = 0; i < 100; ++i) {
        auto seg = make_seg(i);
        FeaInfo feainfo;
        feainfo.fea_id = i;
        feainfo.coord = i + 10000;
        feainfo.fea_ptr = seg;
        queue_ptr->push(feainfo);
    }

    auto dump_res = _obj._thread_dump("./ckpt/thread_dump_case", queue_ptr);

    ASSERT_EQ(dump_res.size(), 100);

    ASSERT_TRUE(_obj._dump_desc(dump_res));

    FileDescsMap file_descs_map;
    rename("./ckpt/describe.tmp", "./ckpt/describe");
    ASSERT_TRUE(_obj._load_desc(&file_descs_map));
    ASSERT_EQ(file_descs_map.size(), 1U);
    ASSERT_EQ(file_descs_map["./ckpt/thread_dump_case"].size(), 100U);

    AccMap acc_map;
    for (uint64_t i = 0; i < 100; ++i) {
        auto seg = empty_seg(i);
        acc_map.insert(std::pair<uint64_t, 
                std::shared_ptr<FeatureAccumulatorInterface>>(i, seg));
    }   
    rename("./ckpt/thread_dump_case.tmp", "./ckpt/thread_dump_case");
    auto load_res = _obj._thread_load(
            "./ckpt/thread_dump_case", 
            file_descs_map["./ckpt/thread_dump_case"],
            &acc_map);
    ASSERT_EQ(load_res.size(), 100);

    for (uint64_t i = 0; i < 100; ++i) {
        ASSERT_EQ(load_res[i].fea_id, i);
        ASSERT_EQ(load_res[i].coord, i + 10000);
        auto i_fea_ptr = load_res[i].fea_ptr;
        FeatureValueProto fea;
        fea.set_feature_id(i);
        fea.set_view_sign(123456LU);
        fea.set_feature_type(FeatureValueProto::SEGMENT);
        fea.set_coord(3L);
        ASSERT_TRUE(i_fea_ptr->query(&fea));
        EXPECT_EQ(3L,fea.cur_seg_count());
        EXPECT_EQ(2L, fea.last_seg_count());
    }
    // remove("./ckpt/thread_dump_case");
}

TEST_F(CheckpointManagerTestSuite, _describe_file_test) {
    FileDescsMap file_descs_map;
    AccMap acc_map;
    system("cp ./ckpt/describe_invalid ./ckpt/describe");
    ASSERT_TRUE(_obj._load_desc(&file_descs_map));
    ASSERT_EQ(file_descs_map.size(), 1U);
    ASSERT_EQ(file_descs_map["./ckpt/thread_dump_case"].size(), 3U);
    for (uint64_t i = 0; i < 3; ++i) {
        auto seg = empty_seg(i);
        acc_map.insert(std::pair<uint64_t, 
                std::shared_ptr<FeatureAccumulatorInterface>>(i, seg));
    }   
    auto load_res = _obj._thread_load(
            "./ckpt/thread_dump_case", 
            file_descs_map["./ckpt/thread_dump_case"],
            &acc_map);
    ASSERT_EQ(load_res.size(), 3);

    file_descs_map.clear();
    acc_map.clear();
    system("cp ./ckpt/describe_valid ./ckpt/describe");
    rename("./ckpt/thread_dump_case.tmp", "./ckpt/thread_dump_case");
    ASSERT_TRUE(_obj._load_desc(&file_descs_map));
    ASSERT_EQ(file_descs_map.size(), 1U);
    ASSERT_EQ(file_descs_map["./ckpt/thread_dump_case"].size(), 50U);

    for (uint64_t i = 0; i < 100; ++i) {
        auto seg = empty_seg(i);
        acc_map.insert(std::pair<uint64_t, 
                std::shared_ptr<FeatureAccumulatorInterface>>(i, seg));
    }   
    load_res = _obj._thread_load(
            "./ckpt/thread_dump_case", 
            file_descs_map["./ckpt/thread_dump_case"],
            &acc_map);
    ASSERT_EQ(load_res.size(), 50);

    for (uint64_t i = 0; i < 50; ++i) {
        EXPECT_EQ(load_res[i].fea_id, i + 50);
        EXPECT_EQ(load_res[i].coord, i + 50 + 10000);
        auto i_fea_ptr = load_res[i].fea_ptr;
        FeatureValueProto fea;
        fea.set_feature_id(i + 50);
        fea.set_view_sign(123456LU);
        fea.set_feature_type(FeatureValueProto::SEGMENT);
        fea.set_coord(3L);
        ASSERT_TRUE(i_fea_ptr->query(&fea));
        EXPECT_EQ(3L,fea.cur_seg_count());
        EXPECT_EQ(2L, fea.last_seg_count());
    }
    // remove("./ckpt/thread_dump_case");
}

TEST_F(CheckpointManagerTestSuite, dump_load_test) {
    CoordMap coord_map;
    AccMap acc_map;
    for (uint64_t i = 0; i < 100; ++i) {
        auto seg = make_seg(i);
        FeaInfo feainfo;
        feainfo.fea_id = i;
        feainfo.coord = i + 10000;
        feainfo.fea_ptr = seg;
        coord_map.insert(std::pair<uint64_t, int64_t>(
                feainfo.fea_id, feainfo.coord));
        acc_map.insert(std::pair<uint64_t, 
                std::shared_ptr<FeatureAccumulatorInterface>>(feainfo.fea_id, seg));
    }
    ASSERT_TRUE(_obj.dump(coord_map, acc_map));
    for (auto &i : coord_map) {
        i.second = 0;
    }
    for (auto &i : coord_map) {
        ASSERT_EQ(i.second, 0);
    }
    for (auto &i : acc_map) {
        auto seg = empty_seg(i.first);
        i.second = seg;
    }
    for (uint64_t i = 0; i < 100; ++i) {
        auto i_fea_ptr = acc_map[i];
        FeatureValueProto fea;
        fea.set_feature_id(i);
        fea.set_view_sign(123456LU);
        fea.set_feature_type(FeatureValueProto::SEGMENT);
        fea.set_coord(3L);
        ASSERT_FALSE(i_fea_ptr->query(&fea));
    }

    ASSERT_TRUE(_obj.load(&coord_map, &acc_map));

    for (uint64_t i = 0; i < 100; ++i) {
        auto i_fea_ptr = acc_map[i];
        FeatureValueProto fea;
        fea.set_feature_id(i);
        fea.set_view_sign(123456LU);
        fea.set_feature_type(FeatureValueProto::SEGMENT);
        fea.set_coord(3L);
        ASSERT_TRUE(i_fea_ptr->query(&fea));
        EXPECT_EQ(3L,fea.cur_seg_count());
        EXPECT_EQ(2L, fea.last_seg_count());
    }

}

TEST_F(CheckpointManagerTestSuite, dump_load_erase_fea_test) {
    CoordMap coord_map;
    AccMap acc_map;
    AccMap erase_acc_map;
    for (uint64_t i = 0; i < 100; ++i) {
        auto seg = make_seg(i);
        auto seg2 = empty_seg(i);
        FeaInfo feainfo;
        feainfo.fea_id = i;
        feainfo.coord = i + 10000;
        feainfo.fea_ptr = seg;
        coord_map.insert(std::pair<uint64_t, int64_t>(
                feainfo.fea_id, feainfo.coord));
        acc_map.insert(std::pair<uint64_t, 
                std::shared_ptr<FeatureAccumulatorInterface>>(feainfo.fea_id, seg));
        if (i < 99) {
            erase_acc_map.insert(std::pair<uint64_t, 
                    std::shared_ptr<FeatureAccumulatorInterface>>(feainfo.fea_id, seg2));
        }
    }
    ASSERT_EQ(erase_acc_map.size(), 99);
    ASSERT_TRUE(_obj.dump(coord_map, acc_map));
    for (auto &i : coord_map) {
        i.second = 0;
    }
    for (auto &i : coord_map) {
        ASSERT_EQ(i.second, 0);
    }
    for (uint64_t i = 0; i < 99; ++i) {
        auto i_fea_ptr = erase_acc_map[i];
        FeatureValueProto fea;
        fea.set_feature_id(i);
        fea.set_view_sign(123456LU);
        fea.set_feature_type(FeatureValueProto::SEGMENT);
        fea.set_coord(3L);
        ASSERT_FALSE(i_fea_ptr->query(&fea));
    }

    ASSERT_TRUE(_obj.load(&coord_map, &erase_acc_map));

    for (uint64_t i = 0; i < 99; ++i) {
        auto i_fea_ptr = erase_acc_map[i];
        FeatureValueProto fea;
        fea.set_feature_id(i);
        fea.set_view_sign(123456LU);
        fea.set_feature_type(FeatureValueProto::SEGMENT);
        fea.set_coord(3L);
        ASSERT_TRUE(i_fea_ptr->query(&fea));
        EXPECT_EQ(3L,fea.cur_seg_count());
        EXPECT_EQ(2L, fea.last_seg_count());
    }
    for (uint64_t i = 99; i < 100; ++i) {
        ASSERT_TRUE(erase_acc_map.find(i) == erase_acc_map.end());
    }
}

TEST_F(CheckpointManagerTestSuite, no_desc_case) {
    int ret = remove("./ckpt/describe");
    CoordMap coord_map;
    AccMap acc_map;
    ASSERT_TRUE(_obj.load(&coord_map, &acc_map));
    system("touch ./ckpt/describe");
    ASSERT_TRUE(_obj.load(&coord_map, &acc_map));
}

TEST_F(CheckpointManagerTestSuite, _remove_case) {
    int ret = 0;
    ret = remove("./ckpt/test_file");
    CWARNING_LOG("file not exit, ret[%d]", ret);
    system("touch ./ckpt/test_file");
    struct stat t;
    ret = stat("./ckpt/test_file", &t);
    ASSERT_EQ(ret, 0);
    ret = remove("./ckpt/test_file");
    CWARNING_LOG("file not exit, ret[%d]", ret);
    remove("./ckpt/test_file");
}


} // feature_lib
} // themis
} // anti

/* vim: set ts=4 sw=4: */

