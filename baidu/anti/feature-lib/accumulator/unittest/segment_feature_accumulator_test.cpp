// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangji<PERSON><PERSON>(<EMAIL>)
// 
// @File: segment_feature_accumulator_test.cpp
// @Last modified: 2017-11-22 11:47:47
// @Brief: 

#include <vector>
#include <gtest/gtest.h>
#include <gflags/gflags.h>
#include "segment_feature_accumulator.h"
#include "posix_themis_io.h" 
#include "ckpt_test_util.h"

namespace anti {
namespace themis {
namespace feature_lib {

DECLARE_bool(enable_mem_pool_for_acc);

// 参数化测试
class SegmentFeatureAccumulatorTestSuite : public ::testing::TestWithParam<bool> {
protected:
    virtual void SetUp() {
        // 根据参数设置gflag
        bool use_mem_pool = GetParam();
        FLAGS_enable_mem_pool_for_acc = use_mem_pool;
        
        ASSERT_TRUE(_conf.load("./conf", "accumulator_test.conf") == 0);

        int64_t coords[] = {0L, 1L, 2L, 3L, 3L};
        std::string dv[] = {"123", "xxx", "456", "789", "0"};
        for (uint32_t i = 0U; i < 5U; ++i) {
            FeatureValueProto fea;
            fea.set_feature_id(100LU);
            fea.set_view_sign(123456LU);
            fea.set_feature_type(FeatureValueProto::SEGMENT);
            fea.set_joinkey(119LU);
            fea.set_log_id(120LU);
            fea.set_log_time(1111111111LU);
            fea.set_coord(coords[i]);
            fea.set_data_view_value(dv[i]);
            _feas.push_back(fea);
        }
        _old_fea = _feas[0];
        _old_fea.set_coord(0L);

        FeatureValueProto fea;
        fea.set_feature_id(123456789LU);
        _inv_feas.push_back(fea);

        fea.set_feature_id(100LU);
        fea.set_feature_type(FeatureValueProto::RATIO);
        _inv_feas.push_back(fea);
    }
    virtual void TearDown() {
        _feas.clear();
        _inv_feas.clear();
    }

protected:
    SegmentFeatureAccumulator _obj;
    comcfg::Configure _conf;
    std::vector<FeatureValueProto> _feas;
    FeatureValueProto _old_fea;
    std::vector<FeatureValueProto> _inv_feas;
};

// 实例化参数化测试：false=StdSlidingWindow, true=PoolSlidingWindow
INSTANTIATE_TEST_CASE_P(
    WindowTypeTest,
    SegmentFeatureAccumulatorTestSuite,
    ::testing::Values(false, true)
);

TEST_P(SegmentFeatureAccumulatorTestSuite, construction_case) {
    EXPECT_EQ(0LU, _obj.feature_id());
    EXPECT_EQ(0LU, _obj._version);
    EXPECT_FALSE(_obj._click_mode);
    EXPECT_FALSE(_obj._add_data_view);
}

TEST_P(SegmentFeatureAccumulatorTestSuite, _get_cumulant_case) {
    SegmentFeatureAccumulator obj(true);
    EXPECT_TRUE(obj._add_data_view);

    EXPECT_EQ(123, obj._get_cumulant(_feas[0]));
    EXPECT_EQ(1, obj._get_cumulant(_feas[1]));
}

TEST_P(SegmentFeatureAccumulatorTestSuite, init_case) {
    uint32_t num = _conf["invalid"]["feature"].size();
    ASSERT_LE(3U, num);
    for (uint32_t i = 0U; i < num; ++i) {
        EXPECT_FALSE(_obj.init(_conf["invalid"]["feature"][i]));
        _obj.uninit();
    }

    num = _conf["valid"]["feature"].size();
    ASSERT_LE(2, num);
    for (uint32_t i = 0U; i < num; ++i) {
        ASSERT_TRUE(_obj.init(_conf["valid"]["feature"][i]));
        EXPECT_EQ(100LU, _obj.feature_id());
        EXPECT_EQ(2LU, _obj._version);
        _obj.uninit();
    }
    EXPECT_EQ(0LU, _obj.feature_id());
    EXPECT_EQ(0LU, _obj._version);
    EXPECT_FALSE(_obj._click_mode);
}

TEST_P(SegmentFeatureAccumulatorTestSuite, multi_update_and_one_query_sliding_window_case) {
    ASSERT_TRUE(_obj.init(_conf["valid"]["feature"][0]));
    // invalid fea
    EXPECT_FALSE(_obj.query(NULL));
    for (uint32_t i = 0U; i< _inv_feas.size(); ++i) {
        EXPECT_FALSE(_obj.update(_inv_feas[i]));
        EXPECT_FALSE(_obj.query(&_inv_feas[i]));
    }

    // multi update and one query
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(_feas[i]));
    }
    // update old fea
    ASSERT_FALSE(_obj.update(_old_fea));
    // query fea
    ASSERT_TRUE(_obj.query(&_feas[4]));
    EXPECT_EQ(3L,  _feas[4].cur_seg_count());
    EXPECT_EQ(2L, _feas[4].last_seg_count());

    // view sign no update before 
    FeatureValueProto fea = _feas[4];
    fea.set_view_sign(987LU);
    ASSERT_FALSE(_obj.query(&fea));

    FValQRequestProto req;
    req.set_feature_id(100);
    req.set_view_sign(987);
    ASSERT_FALSE(_obj.query(req, NULL));
    FValQResponseProto res;
    ASSERT_TRUE(_obj.query(req, &res));
    EXPECT_EQ(1, res.value_size());
    EXPECT_STREQ("0", res.value(0).data());
    res.clear_value();
    req.set_view_sign(123456LU);
    ASSERT_TRUE(_obj.query(req, &res));
    EXPECT_EQ(1, res.value_size());
    EXPECT_STREQ("3", res.value(0).data());
    EXPECT_EQ(3, res.coord());

    _obj.uninit();
}

TEST_P(SegmentFeatureAccumulatorTestSuite, multi_update_and_one_query_click_segment_case) {
    _conf["valid"]["feature"][0].add_unit("click_mode", "1");
    ASSERT_TRUE(_obj.init(_conf["valid"]["feature"][0]));
    EXPECT_TRUE(_obj._click_mode);
    EXPECT_TRUE(_obj._seg._state_ok);
    // multi update and one query
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(_feas[i]));
    }
    // query fea
    ASSERT_TRUE(_obj.query(&_feas[4]));
    EXPECT_EQ(3L,  _feas[4].cur_seg_count());
    EXPECT_EQ(2L, _feas[4].last_seg_count());
    
    // view sign no update before 
    FeatureValueProto fea = _feas[4];
    fea.set_view_sign(987LU);
    ASSERT_TRUE(_obj.query(&fea));
    EXPECT_EQ(0L, fea.cur_seg_count());
    EXPECT_EQ(0L, fea.last_seg_count());
    
    FValQRequestProto req;
    req.set_feature_id(100);
    req.set_view_sign(987);
    ASSERT_FALSE(_obj.query(req, NULL));
    FValQResponseProto res;
    ASSERT_TRUE(_obj.query(req, &res));
    ASSERT_EQ(1, res.value_size());
    EXPECT_STREQ("0", res.value(0).data());
    res.clear_value();
    
    req.set_view_sign(123456LU);
    ASSERT_TRUE(_obj.query(req, &res));
    EXPECT_EQ(1, res.value_size());
    EXPECT_STREQ("3", res.value(0).data());
    EXPECT_EQ(3, res.coord());

    _obj.uninit();
}

TEST_P(SegmentFeatureAccumulatorTestSuite, update_and_query_sliding_window_case) {
    ASSERT_TRUE(_obj.init(_conf["valid"]["feature"][0]));
    // invalid fea
    EXPECT_FALSE(_obj.update_and_query(NULL));
    for (uint32_t i = 0U; i< _inv_feas.size(); ++i) {
        EXPECT_FALSE(_obj.update_and_query(&_inv_feas[i]));
    }
    
    int64_t exp_last[] = {1L, 1L, 2L, 2L, 2L};
    int64_t exp_cur[] = {1L, 2L, 2L, 2L, 3L};
    const char* exp_value[] = {"1", "2", "2", "2", "3"};
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update_and_query(&_feas[i]));
        EXPECT_EQ(exp_last[i], _feas[i].last_seg_count());
        EXPECT_EQ(exp_cur[i], _feas[i].cur_seg_count());
        EXPECT_STREQ(exp_value[i], _feas[i].value().c_str());
        EXPECT_TRUE(_feas[i].valid());
    }

    // old fea
    ASSERT_FALSE(_obj.update_and_query(&_old_fea));

    // in cache step not in segment fea
    FeatureValueProto fea = _feas[1];
    fea.set_view_sign(987LU);
    ASSERT_TRUE(_obj.update_and_query(&fea));
    EXPECT_EQ(0L, fea.cur_seg_count());
    EXPECT_EQ(1L, fea.last_seg_count());
    _obj.uninit();
}

TEST_P(SegmentFeatureAccumulatorTestSuite, update_and_query_click_segment_case) {
    _conf["valid"]["feature"][0].add_unit("click_mode", "1");
    ASSERT_TRUE(_obj.init(_conf["valid"]["feature"][0]));
    EXPECT_TRUE(_obj._click_mode);
    EXPECT_TRUE(_obj._seg._state_ok);
    // invalid fea
    EXPECT_FALSE(_obj.update_and_query(NULL));
    for (uint32_t i = 0U; i< _inv_feas.size(); ++i) {
        EXPECT_FALSE(_obj.update_and_query(&_inv_feas[i]));
    }
    
    // segment_len is 2
    int64_t exp_last[] = {0L, 0L, 2L, 2L, 2L};
    int64_t exp_cur[] = {1L, 2L, 1L, 2L, 3L};
    const char* exp_value[] = {"1", "2", "1", "2", "3"};
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update_and_query(&_feas[i]));
        EXPECT_EQ(exp_last[i], _feas[i].last_seg_count());
        EXPECT_EQ(exp_cur[i], _feas[i].cur_seg_count());
        EXPECT_TRUE(_feas[i].valid());
        EXPECT_STREQ(exp_value[i], _feas[i].value().c_str());
    }
    _obj.uninit();
}

TEST_P(SegmentFeatureAccumulatorTestSuite, update_and_query_jump_window_case) {
    ASSERT_TRUE(_obj.init(_conf["valid"]["feature"][1]));
    int64_t exp_last[] = {0L, 1L, 1L, 1L, 1L};
    int64_t exp_cur[] = {1L, 1L, 1L, 1L, 2L};
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update_and_query(&_feas[i]));
        EXPECT_EQ(exp_last[i], _feas[i].last_seg_count());
        EXPECT_EQ(exp_cur[i], _feas[i].cur_seg_count());
    }
    _obj.uninit();
}

TEST_P(SegmentFeatureAccumulatorTestSuite, dump_load_click_segment_case) {
    _conf["valid"]["feature"][0].add_unit("click_mode", "1");
    ASSERT_TRUE(_obj.init(_conf["valid"]["feature"][0]));
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(_feas[i]));
    }
    DOING_DUMP();
    _obj.uninit();
    
    ASSERT_TRUE(_obj.init(_conf["valid"]["feature"][0]));
    DOING_LOAD();
    ASSERT_TRUE(_obj.query(&_feas[4]));
    EXPECT_EQ(3L,  _feas[4].cur_seg_count());
    EXPECT_EQ(2L, _feas[4].last_seg_count());
}

TEST_P(SegmentFeatureAccumulatorTestSuite, print_monitor_log_test) {
    // Test std_window/pool_window modes based on parameter
    ASSERT_TRUE(_obj.init(_conf["valid"]["feature"][0]));
    
    // 初始状态监控 - 空数据
    testing::internal::CaptureStderr();
    _obj.print_monitor_log();
    std::string empty_output = testing::internal::GetCapturedStderr();
    
    // 验证基本格式
    EXPECT_TRUE(empty_output.find("Feature: ID=100") != std::string::npos);
    EXPECT_TRUE(empty_output.find("Type=SEGMENT") != std::string::npos);
    EXPECT_TRUE(empty_output.find("Elements=0") != std::string::npos);
    
    // 添加测试数据
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(_feas[i]));
    }
    
    // 有数据状态监控
    testing::internal::CaptureStderr();
    _obj.print_monitor_log();
    std::string data_output = testing::internal::GetCapturedStderr();
    
    // 验证数据状态下的输出
    EXPECT_TRUE(data_output.find("Feature: ID=100") != std::string::npos);
    EXPECT_TRUE(data_output.find("Type=SEGMENT") != std::string::npos);
    
    // 根据参数验证不同模式的输出
    bool use_mem_pool = GetParam();
    if (use_mem_pool) {
        // PoolSlidingWindow模式应该有详细的内存统计
        EXPECT_TRUE(data_output.find("Memory(Total=") != std::string::npos);
        EXPECT_TRUE(data_output.find("Data=") != std::string::npos);
        EXPECT_TRUE(data_output.find("Overhead=") != std::string::npos);
        EXPECT_TRUE(data_output.find("AvgPerElement=") != std::string::npos);
    } else {
        // StdSlidingWindow模式应该显示"Memory=N/A"
        EXPECT_TRUE(data_output.find("Memory=N/A") != std::string::npos);
    }
    
    _obj.uninit();
}

TEST_P(SegmentFeatureAccumulatorTestSuite, print_monitor_log_click_mode_test) {
    // 测试 click_mode (SEG) 的监控
    _conf["valid"]["feature"][0].add_unit("click_mode", "1");
    ASSERT_TRUE(_obj.init(_conf["valid"]["feature"][0]));
    
    // 添加数据
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(_feas[i]));
    }
    
    // 捕获监控日志
    testing::internal::CaptureStderr();
    _obj.print_monitor_log();
    std::string output = testing::internal::GetCapturedStderr();
    
    // 验证SEG模式的输出格式
    EXPECT_TRUE(output.find("Feature: ID=100") != std::string::npos);
    EXPECT_TRUE(output.find("Type=SEGMENT") != std::string::npos);
    EXPECT_TRUE(output.find("Memory=N/A") != std::string::npos);  // SEG模式没有内存统计
    
    _obj.uninit();
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */
