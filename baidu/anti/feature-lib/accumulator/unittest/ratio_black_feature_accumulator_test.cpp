// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: ratio_black_feature_accumulator_test.cpp
// @Last modified: 2017-03-16 14:34:35
// @Brief: 

#include <vector>
#include <gtest/gtest.h>
#include "ratio_black_feature_accumulator.h"
#include "posix_themis_io.h" 
#include "ckpt_test_util.h"

namespace anti {
namespace themis {
namespace feature_lib {

class RatioBlackFeatureAccumulatorTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
        ASSERT_TRUE(_conf.load("./conf", "accumulator_test.conf") == 0);

        int64_t coords[] = {0L, 1L, 2L, 3L, 4L, 5L, 6L};
        bool fil[] = {true, false, false, false, true, false, true};
        bool ref[] = {true, true, true, true, true, true, false};
        for (uint32_t i = 0U; i < 7U; ++i) {
            FeatureValueProto fea;
            fea.set_feature_id(100LU);
            fea.set_view_sign(123456LU);
            fea.set_feature_type(FeatureValueProto::AUTO_RATIO);
            fea.set_joinkey(119LU);
            fea.set_log_id(120LU);
            fea.set_log_time(1111111111LU);
            fea.set_coord(coords[i]);
            fea.set_in_filter(fil[i]);
            fea.set_in_refer(ref[i]);
            _feas.push_back(fea);
        }
        FeatureValueProto fea;
        fea.set_feature_id(123456789LU);
        _inv_feas.push_back(fea);

        fea.set_feature_id(100LU);
        fea.set_feature_type(FeatureValueProto::SEGMENT);
        _inv_feas.push_back(fea);

        fea.set_feature_type(FeatureValueProto::RATIO);
        _inv_feas.push_back(fea);
    }

    virtual void TearDown() {
        _inv_feas.clear();
        _feas.clear();
    }
private:
    RatioBlackFeatureAccumulator _obj;
    comcfg::Configure _conf;

    std::vector<FeatureValueProto> _feas;
    std::vector<FeatureValueProto> _inv_feas;

};

TEST_F(RatioBlackFeatureAccumulatorTestSuite, init_case) {
    uint32_t num = _conf["invalid_ratio_black"]["feature"].size();
    ASSERT_LE(2U, num);
    for (uint32_t i = 0U; i < num; ++i) {
        EXPECT_FALSE(_obj.init(_conf["invalid_ratio_black"]["feature"][i]));
        _obj.uninit();
    }

    num = _conf["valid_ratio_black"]["feature"].size();
    ASSERT_LE(1, num);
    ASSERT_TRUE(_obj.init(_conf["valid_ratio_black"]["feature"][0]));
    EXPECT_EQ(100LU, _obj.feature_id());
    EXPECT_EQ(2LU, _obj._version);
    EXPECT_EQ(1L, _obj._remain);
    _obj.uninit();
}

TEST_F(RatioBlackFeatureAccumulatorTestSuite, update_and_query_case) {
    ASSERT_TRUE(_obj.init(_conf["valid_ratio_black"]["feature"][0]));
    EXPECT_TRUE(_obj._seg._state_ok);
    EXPECT_EQ(2, _obj._seg._segment_len);
    EXPECT_EQ(1L, _obj._remain);

    // invalid fea
    EXPECT_FALSE(_obj.update_and_query(NULL));
    for (uint32_t i = 0U; i< _inv_feas.size(); ++i) {
        EXPECT_FALSE(_obj.update_and_query(&_inv_feas[i]));
    }
    int64_t exp_fil[] = {1L, 1L, 0L, 0L, 1L, 1L, 1L};
    int64_t exp_ref[] = {1L, 2L, 1l, 2L, 1L, 2L, 0L};
    int64_t exp_last_fit[] = {0L, 0L, 1L, 1L, 0L, 0L, 1L};
    bool exp_valid[] = {false, false, true, true, false, false, true};
    const char* exp_value[] = {"0", "0", "0.5", "0.5", "0", "0", "0.5"};
    double exp_last_ratio[] = {0, 0, 0.5, 0.5, 0, 0, 0.5};

    ASSERT_TRUE(_obj.init(_conf["valid_ratio_black"]["feature"][0]));
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update_and_query(&_feas[i]));
        EXPECT_EQ(exp_fil[i], _feas[i].filter_count());
        EXPECT_EQ(exp_ref[i], _feas[i].refer_count());
        EXPECT_EQ(exp_last_fit[i], _feas[i].last_fit_count());
        EXPECT_EQ(exp_valid[i], _feas[i].valid());
        EXPECT_DOUBLE_EQ(exp_last_ratio[i], _feas[i].last_ratio());
        EXPECT_STREQ(exp_value[i], _feas[i].value().c_str());
    }
    
    _obj.uninit();
}

TEST_F(RatioBlackFeatureAccumulatorTestSuite, multi_update_and_one_query_case) {
    ASSERT_TRUE(_obj.init(_conf["valid_ratio_black"]["feature"][0]));
    EXPECT_TRUE(_obj._seg._state_ok);
    EXPECT_EQ(2, _obj._seg._segment_len);
    EXPECT_EQ(1L, _obj._remain);

    // invalid fea
    EXPECT_FALSE(_obj.query(NULL));
    for (uint32_t i = 0U; i< _inv_feas.size(); ++i) {
        EXPECT_FALSE(_obj.query(&_inv_feas[i]));
        EXPECT_FALSE(_obj.update(_inv_feas[i]));
    }

    // multi update and one query
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(_feas[i]));
    }

    ASSERT_TRUE(_obj.query(&_feas[6]));
    EXPECT_EQ(1L,  _feas[6].filter_count());
    EXPECT_EQ(0L, _feas[6].refer_count());
    EXPECT_EQ(1L, _feas[6].last_fit_count());
    EXPECT_DOUBLE_EQ(0.5, _feas[6].last_ratio());
    EXPECT_STREQ("0.5", _feas[6].value().c_str());
    EXPECT_TRUE(_feas[6].valid());
}

TEST_F(RatioBlackFeatureAccumulatorTestSuite, dump_load_case) {
    ASSERT_TRUE(_obj.init(_conf["valid_ratio_black"]["feature"][0]));
    EXPECT_TRUE(_obj._seg._state_ok);
    EXPECT_EQ(2, _obj._seg._segment_len);
    EXPECT_EQ(1L, _obj._remain);

    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(_feas[i]));
    }
    DOING_DUMP();
    _obj.uninit();

    ASSERT_TRUE(_obj.init(_conf["valid_ratio_black"]["feature"][0])); 
    DOING_LOAD();
    ASSERT_TRUE(_obj.query(&_feas[5]));
    EXPECT_EQ(2, _obj._seg._segment_len);

}

TEST_F(RatioBlackFeatureAccumulatorTestSuite, check_feature_case) {
    ASSERT_TRUE(_obj.init(_conf["valid_ratio_black"]["feature"][0]));
    EXPECT_TRUE(_obj._seg._state_ok);
    EXPECT_EQ(2, _obj._seg._segment_len);
    EXPECT_EQ(1L, _obj._remain);

    FeatureValueProto fea;
    fea.set_feature_id(100L);
    fea.set_in_filter(false);
    fea.set_in_refer(false);
    EXPECT_FALSE(_obj._check_feature(fea));
    fea.set_feature_type(FeatureValueProto::AUTO_RATIO);
    fea.clear_feature_id();
    EXPECT_FALSE(_obj._check_feature(fea));
    fea.set_feature_id(100L);
    EXPECT_FALSE(_obj._check_feature(fea));

    _obj.uninit();
}

TEST_F(RatioBlackFeatureAccumulatorTestSuite, set_feature_case) {
    ASSERT_TRUE(_obj.init(_conf["valid_ratio_black"]["feature"][0]));
    
    // node NULL case
    FeatureValueProto fea;
    fea.set_filter_count(100);
    fea.set_refer_count(10);
    ASSERT_TRUE(_obj._set_feature(NULL, &fea));
    EXPECT_EQ(0L, fea.filter_count());
    EXPECT_EQ(0L, fea.refer_count());
    EXPECT_EQ(0L, fea.last_fit_count());
    EXPECT_FALSE(fea.valid());
    EXPECT_STREQ("0", fea.value().c_str());
    EXPECT_DOUBLE_EQ(0, fea.last_ratio());

    anti::themis::common_lib::RatioBlackNode node;
    node.fit_cumulant = 1L;
    node.ref_cumulant = 3L;
    node.last_fit_cumulant = 2L;
    node.last_ratio = 0.5;
    ASSERT_TRUE(_obj._set_feature(&node, &fea));
    EXPECT_EQ(1L, fea.filter_count());
    EXPECT_EQ(3L, fea.refer_count());
    EXPECT_EQ(2L, fea.last_fit_count());
    EXPECT_DOUBLE_EQ(0.5, fea.last_ratio());
    EXPECT_TRUE(fea.valid());
    EXPECT_STREQ("0.5", fea.value().c_str());
}

} // feature_lib
} // namespace themis
} // namespace anti

/* vim: set ts=4 sw=4: */

