// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangji<PERSON><PERSON>(<EMAIL>)
// 
// @File: ratio_feature_accumulator_test.cpp
// @Last modified: 2017-12-27 20:19:35
// @Brief: 

#include <vector>
#include <gtest/gtest.h>
#include <gflags/gflags.h>
#include <boost/lexical_cast.hpp>
#include "ratio_feature_accumulator.h"
#include "posix_themis_io.h" 
#include "ckpt_test_util.h"

namespace anti {
namespace themis {
namespace feature_lib {
    
DECLARE_bool(enable_mem_pool_for_acc);

// 参数化测试
class RatioFeatureAccumulatorTestSuite : public ::testing::TestWithParam<bool> {
protected:
    virtual void SetUp() {
        // 根据参数设置gflag
        bool use_mem_pool = GetParam();
        FLAGS_enable_mem_pool_for_acc = use_mem_pool;
        
        ASSERT_TRUE(_conf.load("./conf", "accumulator_test.conf") == 0);

        int64_t coords[] = {0L, 1L, 2L, 3L, 3L, 3L};
        bool fil[] = {true, false, false, false, true, true};
        bool ref[] = {true, true, true, true, false, true};
        for (uint32_t i = 0U; i < 6U; ++i) {
            FeatureValueProto fea;
            fea.set_feature_id(100LU);
            fea.set_view_sign(123456LU);
            fea.set_feature_type(FeatureValueProto::RATIO);
            fea.set_joinkey(119LU);
            fea.set_log_id(120LU);
            fea.set_log_time(1111111111LU);
            fea.set_coord(coords[i]);
            fea.set_in_filter(fil[i]);
            fea.set_in_refer(ref[i]);
            _feas.push_back(fea);
        }

        int64_t weak_check_coords[] = {0, 0, 0};
        bool weak_check_fil[] = {false, false, false};
        bool weak_check_ref[] = {false, false, false};
        for (uint32_t i = 0; i < 3; ++i) {
            FeatureValueProto fea;
            fea.set_feature_id(100LU);
            fea.set_view_sign(123456LU);
            fea.set_feature_type(FeatureValueProto::RATIO);
            fea.set_joinkey(119LU);
            fea.set_log_id(120LU);
            fea.set_log_time(1111111111LU);
            fea.set_coord(weak_check_coords[i]);
            fea.set_in_filter(weak_check_fil[i]);
            fea.set_in_refer(weak_check_ref[i]);
            _weak_check_feas.push_back(fea);
        }
        _old_fea = _feas[0];
        _old_fea.set_coord(0L);

        FeatureValueProto fea;
        fea.set_feature_id(123456789LU);
        _inv_feas.push_back(fea);

        fea.set_feature_id(100LU);
        fea.set_feature_type(FeatureValueProto::SEGMENT);
        _inv_feas.push_back(fea);

        fea.set_feature_type(FeatureValueProto::RATIO);
        _inv_feas.push_back(fea);
    }
    virtual void TearDown() {
        _feas.clear();
        _inv_feas.clear();
    }

    void set_fea(uint64_t coord, bool fil, bool ref, FeatureValueProto *fea) {
        ASSERT_TRUE(fea != NULL);
        fea->set_feature_id(100LU);
        fea->set_view_sign(123456LU);
        fea->set_feature_type(FeatureValueProto::RATIO);
        fea->set_joinkey(119LU);
        fea->set_log_id(120LU);
        fea->set_log_time(1111111111LU);
        fea->set_coord(coord);
        fea->set_in_filter(fil);
        fea->set_in_refer(ref);
        return;
    }

    void set_feas(
            std::vector<int64_t> coords, 
            std::vector<int64_t> fil, 
            std::vector<int64_t> ref,
            std::vector<FeatureValueProto> *feas) {
        ASSERT_TRUE(feas != NULL);
        ASSERT_EQ(coords.size(), fil.size());
        ASSERT_EQ(coords.size(), ref.size());
        for (uint32_t i = 0; i < coords.size(); ++i) {
            FeatureValueProto fea;
            set_fea(coords[i], fil[i], ref[i], &fea);
            feas->push_back(fea);
        }
        return;
    }

protected:
    RatioFeatureAccumulator _obj;
    comcfg::Configure _conf;
    std::vector<FeatureValueProto> _feas;
    FeatureValueProto _old_fea;
    std::vector<FeatureValueProto> _inv_feas;
    std::vector<FeatureValueProto> _weak_check_feas;
};

// 实例化参数化测试：false=StdSlidingWindow, true=PoolSlidingWindow
INSTANTIATE_TEST_CASE_P(
    WindowTypeTest,
    RatioFeatureAccumulatorTestSuite,
    ::testing::Values(false, true)
);

TEST_P(RatioFeatureAccumulatorTestSuite, construction_case) {
    EXPECT_EQ(0LU, _obj.feature_id());
    EXPECT_EQ(0LU, _obj._version);
    EXPECT_FALSE(_obj._click_mode);
    EXPECT_FALSE(_obj._remain_numerator);
    EXPECT_EQ(0L, _obj._remain);
}

TEST_P(RatioFeatureAccumulatorTestSuite, init_case) {
    uint32_t num = _conf["invalid"]["feature"].size();
    ASSERT_LE(3U, num);
    for (uint32_t i = 0U; i < num; ++i) {
        EXPECT_FALSE(_obj.init(_conf["invalid"]["feature"][i]));
        _obj.uninit();
    }

    num = _conf["valid"]["feature"].size();
    ASSERT_LE(2, num);
    for (uint32_t i = 0U; i < num; ++i) {
        ASSERT_TRUE(_obj.init(_conf["valid"]["feature"][i]));
        EXPECT_EQ(100LU, _obj.feature_id());
        EXPECT_EQ(2LU, _obj._version);
        EXPECT_EQ(1L, _obj._remain);
        EXPECT_TRUE(_obj._remain_numerator);
        _obj.uninit();
    }
    EXPECT_EQ(0LU, _obj.feature_id());
    EXPECT_EQ(0LU, _obj._version);
    EXPECT_FALSE(_obj._click_mode);
    EXPECT_FALSE(_obj._remain_numerator);
}

TEST_P(RatioFeatureAccumulatorTestSuite, weak_check_case) {
    ASSERT_TRUE(_obj.init(_conf["valid"]["feature"][2]));
    ASSERT_TRUE(_obj.update(_feas[0]));
    ASSERT_TRUE(_obj.update(_feas[0]));
    ASSERT_TRUE(_obj._weak_check);

    ASSERT_TRUE(_obj.query(&_feas[0]));
    EXPECT_EQ(_feas[0].filter_count(), 2);
    EXPECT_EQ(_feas[0].refer_count(), 2);
    EXPECT_TRUE(_feas[0].valid());

    ASSERT_TRUE(_obj.query(&_weak_check_feas[0]));
    EXPECT_EQ(_weak_check_feas[0].filter_count(), 2);
    EXPECT_EQ(_weak_check_feas[0].refer_count(), 2);
    EXPECT_TRUE(_weak_check_feas[0].valid());

    ASSERT_TRUE(_obj.update(_weak_check_feas[1]));
    EXPECT_TRUE(_obj.query(&_weak_check_feas[1]));
    EXPECT_EQ(_weak_check_feas[1].filter_count(), 2);
    EXPECT_EQ(_weak_check_feas[1].refer_count(), 2);
    EXPECT_TRUE(_weak_check_feas[1].valid());

    ASSERT_TRUE(_obj.update_and_query(&_weak_check_feas[2]));
    EXPECT_EQ(_weak_check_feas[2].filter_count(), 2);
    EXPECT_EQ(_weak_check_feas[2].refer_count(), 2);
    EXPECT_TRUE(_weak_check_feas[2].valid());

    _obj._weak_check = false;
    EXPECT_FALSE(_obj.update(_weak_check_feas[2]));
}

TEST_P(RatioFeatureAccumulatorTestSuite, multi_update_and_one_query_sliding_window_case) {
    ASSERT_TRUE(_obj.init(_conf["valid"]["feature"][0]));
    // invalid fea
    EXPECT_FALSE(_obj.query(NULL));
    for (uint32_t i = 0U; i< _inv_feas.size(); ++i) {
        EXPECT_FALSE(_obj.update(_inv_feas[i]));
        EXPECT_FALSE(_obj.query(&_inv_feas[i]));
    }

    // multi update and one query
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(_feas[i]));
    }
    // update old fea
    ASSERT_FALSE(_obj.update(_old_fea));
    // query fea
    ASSERT_TRUE(_obj.query(&_feas[5]));
    EXPECT_EQ(2L,  _feas[5].filter_count());
    EXPECT_EQ(3L, _feas[5].refer_count());

    // view sign no update before 
    FeatureValueProto fea = _feas[5];
    fea.set_view_sign(987LU);
    ASSERT_FALSE(_obj.query(&fea));
    _obj.uninit();
}

TEST_P(RatioFeatureAccumulatorTestSuite, multi_update_and_one_query_click_segment_case) {
    _conf["valid"]["feature"][0].add_unit("click_mode", "1");
    ASSERT_TRUE(_obj.init(_conf["valid"]["feature"][0]));
    // multi update and one query
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(_feas[i]));
    }
    // query fea
    ASSERT_TRUE(_obj.query(&_feas[5]));
    EXPECT_EQ(2L,  _feas[5].filter_count());
    EXPECT_EQ(3L, _feas[5].refer_count());

    // view sign no update before 
    FeatureValueProto fea = _feas[5];
    fea.set_view_sign(987LU);
    ASSERT_TRUE(_obj.query(&fea));
    EXPECT_EQ(0L, fea.filter_count());
    EXPECT_EQ(0L, fea.refer_count());
    _obj.uninit();
}

TEST_P(RatioFeatureAccumulatorTestSuite, update_and_query_sliding_window_case) {
    ASSERT_TRUE(_obj.init(_conf["valid"]["feature"][0]));
    // invalid fea
    EXPECT_FALSE(_obj.update_and_query(NULL));
    for (uint32_t i = 0U; i< _inv_feas.size(); ++i) {
        EXPECT_FALSE(_obj.update_and_query(&_inv_feas[i]));
    }
    
    int64_t exp_fil[] = {1L, 1L, 0L, 0L, 1L, 2L};
    int64_t exp_ref[] = {1L, 2L, 2L, 2L, 2L, 3L};
    bool exp_valid[] = {false, false, false, false, false, true};
    const char*  exp_value[] = {"1", "0.5", "0", "0", "0.5", "0.66666666666666663"};
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update_and_query(&_feas[i]));
        EXPECT_EQ(exp_fil[i], _feas[i].filter_count());
        EXPECT_EQ(exp_ref[i], _feas[i].refer_count());
        EXPECT_EQ(exp_valid[i], _feas[i].valid());
        EXPECT_STREQ(exp_value[i], _feas[i].value().c_str());
    }

    // old fea
    ASSERT_FALSE(_obj.update_and_query(&_old_fea));

    // in cache step not in segment fea
    FeatureValueProto fea = _feas[1];
    fea.set_view_sign(987LU);
    ASSERT_TRUE(_obj.update_and_query(&fea));
    EXPECT_EQ(0L, fea.filter_count());
    EXPECT_EQ(0L, fea.refer_count());
    EXPECT_FALSE(fea.valid());
    EXPECT_STREQ("0", fea.value().c_str());
    _obj.uninit();

    ASSERT_TRUE(_obj.init(_conf["valid"]["feature"][0]));
    EXPECT_EQ(1L, _obj._remain);
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update_and_query(&_feas[i]));
        EXPECT_EQ(exp_fil[i], _feas[i].filter_count());
        EXPECT_EQ(exp_ref[i], _feas[i].refer_count());
        EXPECT_STREQ(exp_value[i], _feas[i].value().c_str());
    }
    EXPECT_FALSE(_feas[0].valid());
    EXPECT_FALSE(_feas[1].valid());
    EXPECT_TRUE(_feas[5].valid());
    _obj.uninit();
}

TEST_P(RatioFeatureAccumulatorTestSuite, update_and_query_click_segment_case) {
    _conf["valid"]["feature"][0].add_unit("click_mode", "1");
    ASSERT_TRUE(_obj.init(_conf["valid"]["feature"][0]));
    EXPECT_TRUE(_obj._click_mode);
    EXPECT_TRUE(_obj._seg._state_ok);
    EXPECT_EQ(_obj._seg._segment_len, 2);
    EXPECT_EQ(_obj._remain, 1L);
    // invalid fea
    EXPECT_FALSE(_obj.update_and_query(NULL));
    for (uint32_t i = 0U; i< _inv_feas.size(); ++i) {
        EXPECT_FALSE(_obj.update_and_query(&_inv_feas[i]));
    }
    
    // segment length is 2
    int64_t exp_fil[] = {1L, 1L, 0L, 0L, 1L, 2L};
    int64_t exp_ref[] = {1L, 2L, 1L, 2L, 2L, 3L};
    const char*  exp_value[] = {"1", "0.5", "0", "0", "0.5", "0.66666666666666663"};
    bool exp_valid[] = {false, false, false, false, false, true};
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update_and_query(&_feas[i]));
        EXPECT_EQ(exp_fil[i], _feas[i].filter_count());
        EXPECT_EQ(exp_ref[i], _feas[i].refer_count());
        EXPECT_EQ(exp_valid[i], _feas[i].valid());
        EXPECT_STREQ(exp_value[i], _feas[i].value().c_str());
    }
    _obj.uninit();

    // test 0
    ASSERT_TRUE(_obj.init(_conf["valid"]["feature"][0]));
    FeatureValueProto fea;
    fea.set_feature_id(100LU);
    fea.set_view_sign(123456LU);
    fea.set_feature_type(FeatureValueProto::RATIO);
    fea.set_joinkey(119LU);
    fea.set_log_id(120LU);
    fea.set_log_time(1111111111LU);
    fea.set_coord(0L);
    fea.set_in_filter(true);
    fea.set_in_refer(false);
    ASSERT_TRUE(_obj.update_and_query(&fea));
    EXPECT_EQ(1L, fea.filter_count());
    EXPECT_EQ(0L, fea.refer_count());
    EXPECT_FALSE(fea.valid());
    EXPECT_STREQ("1", fea.value().c_str());
    _obj.uninit();

    ASSERT_TRUE(_obj.init(_conf["valid"]["feature"][0]));
    ASSERT_EQ(1L, _obj._remain);
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update_and_query(&_feas[i]));
        EXPECT_EQ(exp_fil[i], _feas[i].filter_count());
        EXPECT_EQ(exp_ref[i], _feas[i].refer_count());
        EXPECT_STREQ(exp_value[i], _feas[i].value().c_str());
    }
    EXPECT_FALSE(_feas[0].valid());
    EXPECT_FALSE(_feas[1].valid());
    EXPECT_TRUE(_feas[5].valid());
    _obj.uninit();

}

TEST_P(RatioFeatureAccumulatorTestSuite, update_and_query_jump_window_case) {
    ASSERT_TRUE(_obj.init(_conf["valid"]["feature"][1]));
    int64_t exp_fil[] = {1L, 0L, 0L, 0L, 1L, 2L};
    int64_t exp_ref[] = {1L, 1L, 1L, 1L, 1L, 2L};
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update_and_query(&_feas[i]));
        EXPECT_EQ(exp_fil[i], _feas[i].filter_count());
        EXPECT_EQ(exp_ref[i], _feas[i].refer_count());
    }
    _obj.uninit();
}

TEST_P(RatioFeatureAccumulatorTestSuite, dump_load_click_segment_case) {
    _conf["valid"]["feature"][0].add_unit("click_mode", "1");
    ASSERT_TRUE(_obj.init(_conf["valid"]["feature"][0]));
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(_feas[i]));
    }
    DOING_DUMP();
    _obj.uninit();
    
    ASSERT_TRUE(_obj.init(_conf["valid"]["feature"][0]));
    DOING_LOAD();
    ASSERT_TRUE(_obj.query(&_feas[5]));
    EXPECT_EQ(2L,  _feas[5].filter_count());
    EXPECT_EQ(3L, _feas[5].refer_count());
    _obj.uninit();

}

TEST_P(RatioFeatureAccumulatorTestSuite, weak_anomaly_case) {
    ASSERT_TRUE(_obj.init(_conf["valid_ratio_weak_anomaly"]));
    ASSERT_TRUE(!_obj._weak_ab_mgr);
    _obj.uninit();
    _conf["valid_ratio_weak_anomaly"].add_unit("weak_abnormity_threshold", "0.5");
    _conf["valid_ratio_weak_anomaly"].add_unit("incr_step", "8");
    ASSERT_TRUE(_obj.init(_conf["valid_ratio_weak_anomaly"]));
    ASSERT_FALSE(!_obj._weak_ab_mgr);
    std::vector<int64_t> coords = {0L, 1L, 2L, 5L, 8L, 25L, 25L, 49L, 99L, 99L, 99L, 99L};
    std::vector<int64_t> fil = {1L, 0L, 0L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L};
    std::vector<int64_t> ref = {1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L};
    std::vector<double> e = {1.0 / 8, 1.0 / 8, 1.0 / 8, 1.0 / 8, 2.0 / 8, 3.0 / 8, 4.0 / 8,
            5.0 / 8, 6.0 / 8, 7.0 / 8, 1.0, 9.0 / 8};
    std::vector<FeatureValueProto> feas;
    set_feas(coords, fil, ref, &feas);
    for (uint32_t i = 0; i < 8; ++i) {
        ASSERT_TRUE(_obj.update_and_query(&feas[i]));
        ASSERT_EQ(FeatureValueProto::WEAK_ANOMALY, feas[i].feature_type());
        ASSERT_FALSE(feas[i].valid());
        ASSERT_EQ(fil[i], feas[i].in_anomaly_care());
        ASSERT_EQ(e[i], boost::lexical_cast<double>(feas[i].value()));
    }
    for (uint32_t i = 8; i < 12; ++i) {
        ASSERT_TRUE(_obj.update_and_query(&feas[i]));
        ASSERT_EQ(FeatureValueProto::WEAK_ANOMALY, feas[i].feature_type());
        ASSERT_TRUE(feas[i].valid());
        ASSERT_EQ(fil[i], feas[i].in_anomaly_care());
        ASSERT_EQ(e[i], boost::lexical_cast<double>(feas[i].value()));
    }
}

TEST_P(RatioFeatureAccumulatorTestSuite, weak_anomaly_case_more) {
    ASSERT_TRUE(_obj.init(_conf["valid_ratio_weak_anomaly"]));
    ASSERT_TRUE(!_obj._weak_ab_mgr);
    _obj.uninit();
    _conf["valid_ratio_weak_anomaly"].add_unit("weak_abnormity_threshold", "0.5");
    _conf["valid_ratio_weak_anomaly"].add_unit("incr_step", "8");
    ASSERT_TRUE(_obj.init(_conf["valid_ratio_weak_anomaly"]));
    ASSERT_FALSE(!_obj._weak_ab_mgr);
    std::vector<int64_t> coords(40, 99L);
    std::vector<int64_t> fil = {
            1L, 0L, 0L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 
            1L, 1L, 0L, 0L, 0L, 0L, 1L, 1L, 0L, 0L, 
            0L, 0L, 1L, 1L, 1L, 1L, 1L, 0L, 1L, 1L,
            0L, 0L, 0L, 0L, 0L, 1L, 1L, 0L, 0L, 0L};
    std::vector<int64_t> ref(40, 1L);
    std::vector<FeatureValueProto> feas;
    set_feas(coords, fil, ref, &feas);
    for (uint32_t i = 0; i < 8; ++i) {
        ASSERT_TRUE(_obj.update_and_query(&feas[i]));
        ASSERT_EQ(FeatureValueProto::WEAK_ANOMALY, feas[i].feature_type());
        ASSERT_FALSE(feas[i].valid());
        ASSERT_EQ(fil[i], feas[i].in_anomaly_care());
    }
    for (uint32_t i = 8; i < 40; ++i) {
        ASSERT_TRUE(_obj.update_and_query(&feas[i]));
        ASSERT_EQ(FeatureValueProto::WEAK_ANOMALY, feas[i].feature_type());
        ASSERT_TRUE(feas[i].valid());
        ASSERT_EQ(fil[i], feas[i].in_anomaly_care());
    }
    ASSERT_TRUE(_obj.query(&feas[39]));
    EXPECT_EQ(20.0 / 40, boost::lexical_cast<double>(feas[39].value()));
    FeatureValueProto last;
    set_fea(99L, true, true, &last);
    ASSERT_TRUE(_obj.update_and_query(&last));
    ASSERT_EQ(FeatureValueProto::WEAK_ANOMALY, last.feature_type());
    ASSERT_TRUE(last.valid());
    EXPECT_EQ(20.0 / 40 + 1.0 / 8, boost::lexical_cast<double>(last.value()));
}

TEST_P(RatioFeatureAccumulatorTestSuite, print_monitor_log_test) {
    // Test std_window/pool_window modes based on parameter
    ASSERT_TRUE(_obj.init(_conf["valid"]["feature"][0]));
    
    // 初始状态监控 - 空数据
    testing::internal::CaptureStderr();
    _obj.print_monitor_log();
    std::string empty_output = testing::internal::GetCapturedStderr();
    
    // 验证基本格式
    EXPECT_TRUE(empty_output.find("Feature: ID=100") != std::string::npos);
    EXPECT_TRUE(empty_output.find("Type=RATIO") != std::string::npos);
    EXPECT_TRUE(empty_output.find("Elements=0") != std::string::npos);
    
    // 添加测试数据
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(_feas[i]));
    }
    
    // 有数据状态监控
    testing::internal::CaptureStderr();
    _obj.print_monitor_log();
    std::string data_output = testing::internal::GetCapturedStderr();
    
    // 验证数据状态下的输出
    EXPECT_TRUE(data_output.find("Feature: ID=100") != std::string::npos);
    EXPECT_TRUE(data_output.find("Type=RATIO") != std::string::npos);
    
    // 根据参数验证不同模式的输出
    bool use_mem_pool = GetParam();
    if (use_mem_pool) {
        // PoolSlidingWindow模式应该有详细的内存统计
        EXPECT_TRUE(data_output.find("Memory(Total=") != std::string::npos);
        EXPECT_TRUE(data_output.find("Data=") != std::string::npos);
        EXPECT_TRUE(data_output.find("Overhead=") != std::string::npos);
        EXPECT_TRUE(data_output.find("AvgPerElement=") != std::string::npos);
    } else {
        // StdSlidingWindow模式应该显示"Memory=N/A"
        EXPECT_TRUE(data_output.find("Memory=N/A") != std::string::npos);
    }
    
    _obj.uninit();
}

TEST_P(RatioFeatureAccumulatorTestSuite, print_monitor_log_click_mode_test) {
    // 测试 click_mode (SEG) 的监控
    _conf["valid"]["feature"][0].add_unit("click_mode", "1");
    ASSERT_TRUE(_obj.init(_conf["valid"]["feature"][0]));
    
    // 添加数据
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(_feas[i]));
    }
    
    // 捕获监控日志
    testing::internal::CaptureStderr();
    _obj.print_monitor_log();
    std::string output = testing::internal::GetCapturedStderr();
    
    // 验证SEG模式的输出格式
    EXPECT_TRUE(output.find("Feature: ID=100") != std::string::npos);
    EXPECT_TRUE(output.find("Type=RATIO") != std::string::npos);
    EXPECT_TRUE(output.find("Memory=N/A") != std::string::npos);  // SEG模式没有内存统计
    
    _obj.uninit();
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */
