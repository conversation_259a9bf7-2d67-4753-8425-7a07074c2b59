// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @brief 

#include <gtest/gtest.h>
#include <com_log.h>
#include "timediff_feature_accumulator.h"
#include "posix_themis_io.h" 
#include "ckpt_test_util.h"

namespace anti {
namespace themis {
namespace feature_lib {

const char* g_conf_path = "./conf";
const char* g_conf_file = "timediff_feature.conf";

const int32_t kDefaultFeatureId = 103;
const int64_t kMaxDiff = 0x1FFFFFFF;

FeatureValueProto mock_feature(uint64_t sign, int64_t time) {
    FeatureValueProto fea;
    fea.set_feature_type(FeatureValueProto::TIME_DIFF);
    fea.set_feature_id(kDefaultFeatureId);
    fea.set_view_sign(sign);
    fea.set_log_time(time);
    return fea;
}

class TDAccumulatorTestSuite : public ::testing::Test {
public:
    virtual void SetUp() {}
    virtual void TearDown() {}

private:
    TDAccumulator _obj;
};

TEST_F(TDAccumulatorTestSuite, init) {
    comcfg::Configure cfg;
    ASSERT_TRUE(cfg.load(g_conf_path, g_conf_file) == 0);

    //printf("%s\n", cfg.to_cstr());
    auto& acc_conf = cfg["timediff"];
    //printf("%s\n", acc_conf.to_cstr());

    ASSERT_TRUE(_obj.init(acc_conf));

    EXPECT_EQ(_obj._feature_id, 101);
    EXPECT_EQ(_obj._version, 1);
}

TEST_F(TDAccumulatorTestSuite, insert_and_query) {
    _obj._goe_list.reset(
            new (std::nothrow) TDAccumulator::GOEList(100, 10));
    _obj._feature_id = kDefaultFeatureId;

    // mock feature
    FeatureValueProto fea[] = {mock_feature(1, 10000),
                               mock_feature(1, 30000),
                               mock_feature(1, 20000)};

    for (int32_t i = 0; i < 3; ++i) {
        ASSERT_TRUE(_obj.update(fea[i]));
    }

    int64_t target[] = {kMaxDiff, 10, 10};
    for (int32_t i = 0; i < 3; ++i) {
        ASSERT_TRUE(_obj.query(&fea[i]));
        EXPECT_EQ(target[i], fea[i].pre_feat_distance());
        ASSERT_TRUE(fea[i].neighbor_feat_distances_size() == 0);
    }
}

TEST_F(TDAccumulatorTestSuite, insert_and_query_with_neighbor_feat_distances) {
    _obj._goe_list.reset(
            new (std::nothrow) TDAccumulator::GOEList(100, 10));
    _obj._feature_id = kDefaultFeatureId;
    _obj._range = 60;

    // mock feature
    FeatureValueProto fea[] = {mock_feature(1, 10000),
                               mock_feature(1, 30000),
                               mock_feature(1, 20000)};

    for (int32_t i = 0; i < 3; ++i) {
        ASSERT_TRUE(_obj.update(fea[i]));
    }

    int64_t target[] = {kMaxDiff, 10, 10};
    for (int32_t i = 0; i < 3; ++i) {
        ASSERT_TRUE(_obj.query(&fea[i]));
        EXPECT_EQ(target[i], fea[i].pre_feat_distance());
    }

    ASSERT_TRUE(fea[0].neighbor_feat_distances_size() == 0);
    ASSERT_TRUE(fea[1].neighbor_feat_distances_size() == 2);
    ASSERT_TRUE(fea[2].neighbor_feat_distances_size() == 1);
    ASSERT_TRUE(fea[1].neighbor_feat_distances(0) == 10);
    ASSERT_TRUE(fea[1].neighbor_feat_distances(1) == 20);
    ASSERT_TRUE(fea[2].neighbor_feat_distances(0) == 10);
}

// -- same sign, feature list too long --
TEST_F(TDAccumulatorTestSuite, insert_and_query_remove_very_old_feature) {
    _obj._goe_list.reset(
            new (std::nothrow) TDAccumulator::GOEList(100, 3));
    _obj._feature_id = kDefaultFeatureId;

    // mock feature
    FeatureValueProto fea[] = {mock_feature(1, 1000),
                               mock_feature(1, 3000),
                               mock_feature(1, 10000),
                               mock_feature(1, 102000)};

    for (int32_t i = 0; i < 3; ++i) {
        ASSERT_TRUE(_obj.update(fea[i]));
    }

    {
        int64_t target[] = {kMaxDiff, 2, 7};
        for (int32_t i = 0; i < 3; ++i) {
            ASSERT_TRUE(_obj.query(&fea[i]));
            EXPECT_EQ(target[i], fea[i].pre_feat_distance());
        }
    }

    ASSERT_TRUE(_obj.update(fea[3]));
    {
        int64_t target[] = {kMaxDiff, kMaxDiff, 7, 92};
        for (int32_t i = 0; i < 4; ++i) {
            ASSERT_TRUE(_obj.query(&fea[i]));
            EXPECT_EQ(target[i], fea[i].pre_feat_distance());
        }
    }
}

TEST_F(TDAccumulatorTestSuite, insert_and_query_remove_very_old_feature_list) {
    _obj._goe_list.reset(
            new (std::nothrow) TDAccumulator::GOEList(100, 3));
    _obj._feature_id = kDefaultFeatureId;

    // mock feature
    FeatureValueProto fea[] = {mock_feature(1, 1000),
                               mock_feature(1, 3000),
                               mock_feature(1, 10000),
                               mock_feature(2, 112000)};

    for (int32_t i = 0; i < 4; ++i) {
        ASSERT_TRUE(_obj.update(fea[i]));
    }

    ASSERT_TRUE(_obj.query(&fea[0]));
    ASSERT_TRUE(_obj.query(&fea[3]));

    EXPECT_EQ(fea[3].pre_feat_distance(), kMaxDiff); 
}

TEST_F(TDAccumulatorTestSuite, dump_and_load) {
    _obj._goe_list.reset(
            new (std::nothrow) TDAccumulator::GOEList(100, 3));
    _obj._feature_id = kDefaultFeatureId;
    _obj._version = 1;

    // mock feature
    FeatureValueProto fea[] = {mock_feature(1, 1000),
                               mock_feature(1, 3000),
                               mock_feature(1, 10000)};
                               

    for (int32_t i = 0; i < 3; ++i) {
        ASSERT_TRUE(_obj.update(fea[i]));
    }

    {
        int64_t target[] = {kMaxDiff, 2, 7};
        for (int32_t i = 0; i < 3; ++i) {
            ASSERT_TRUE(_obj.query(&fea[i]));
            EXPECT_EQ(target[i], fea[i].pre_feat_distance());
        }
    }

    DOING_DUMP();
    _obj.uninit();

    {
        int64_t target[] = {kMaxDiff, 2, 7};
        _obj.uninit();
        _obj._version = 1;
        _obj._goe_list.reset(
                new (std::nothrow) TDAccumulator::GOEList(100, 3));
        DOING_LOAD();
        ASSERT_EQ(_obj._version, 1);

        for (int32_t i = 0; i < 3; ++i) {
            ASSERT_TRUE(_obj.query(&fea[i]));
            EXPECT_EQ(target[i], fea[i].pre_feat_distance());
        }
    }

    system("rm ./test.dat");
}

}
}
}
