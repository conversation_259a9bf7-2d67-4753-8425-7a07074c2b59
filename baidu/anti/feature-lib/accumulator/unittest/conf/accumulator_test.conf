[invalid]
[.@feature]

[.@feature]
feature_id : 1
version : 1
window_length : 9

[.@feature]
feature_id : 1
version : 1
window_length : 5
step_length : 2

[valid]
[.@feature]
# sliding window
feature_id : 100
version : 2
window_length : 2
step_length : 1
remain : 1
remain_numerator : 1

[.@feature]
# jump window
feature_id : 100
version : 2
window_length : 1
step_length : 1
remain : 1
remain_numerator : 1

[.@feature]
# sliding window
feature_id : 100
version : 2
window_length : 2
step_length : 1
remain : 1
remain_numerator : 1
weak_check : 1

[invalid_dis]
[.@feature]

[.@feature]
feature_id : 1
version : 1
window_length : 9
interval_endpoints : 5

[.@feature]
feature_id : 1
version : 1
window_length : 5
step_length : 2
interval_endpoints : 5

[.@feature]
feature_id : 1
version : 1
window_length : 1
step_length : 1
interval_endpoints : 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18

[valid_dis]
[.@feature]
# sliding window
feature_id : 100
func_type   : max_diff
version : 2
window_length : 2
step_length : 1
stand_prob  : 0.2,0.4,0.1
interval_endpoints : 5,10
count_threshold : 1

[.@feature]
# jump window
feature_id : 100
func_type   : max_diff
version : 2
window_length : 1
step_length : 1
interval_endpoints : 5,10
stand_prob  : 0.2,0.4,0.1
count_threshold : 1

[valid_multi_dis]
[.@feature]
# sliding window
feature_id : 100
func_type   : max_diff
version : 2
window_length : 2
step_length : 1
stand_prob  : 0.2,0.4,0.1
interval_endpoints : 5,10
count_threshold : 1

[.@feature]
# sliding window
feature_id : 101
func_type   : chi_square_test
version : 2
window_length : 1
step_length : 1
stand_prob  : 0.2,0.4,0.1
interval_endpoints : 5,9
count_threshold : 1

[.@feature]
# sliding window
feature_id : 102
func_type   : chi_square_dis
version : 2
window_length : 1
step_length : 1
stand_prob  : 0.2,0.4,0.1
interval_endpoints : 5,9
count_threshold : 1

[.@feature]
# sliding window
feature_id : 103
func_type   : kl_divergence
version : 2
window_length : 1
step_length : 1
stand_prob  : 0.2,0.4,0.1
interval_endpoints : 5,9
count_threshold : 1

[invalid_ratio_black]
[.@feature]

[.@feature]
feature_id : 1
version: 1

[.@feature]
feature_id : 1
version: 1
window_length : 2

[valid_ratio_black]

[.@feature]
feature_id : 100
version : 2
window_length : 2
remain : 1

[invalid_seglimit_black]
[.@feature]

[.@feature]
feature_id : 1
version: 1

[.@feature]
feature_id : 1
version: 1
window_length : 2

[.@feature]
feature_id : 1
version: 1
window_length : 2
upper_bound : 1000

[.@feature]
feature_id : 1
version: 1
window_length : 2
upper_bound : 1000
discard : 1

[.@feature]
feature_id : 1
version: 1
window_length : 10000
upper_bound : -1
lamda : 0.5
discard:1

[.@feature]
feature_id : 1
version: 1
window_length : 10000
upper_bound : 1000
lamda : 0
discard:1

[.@feature]
feature_id : 1
version: 1
window_length : 10000
upper_bound : 1000
lamda : 0.5
discard:-1

[valid_seglimit_black]
[.@feature]
feature_id : 1
version: 1
window_length : 10000
upper_bound : 1000
lamda : 0.5
discard:1
max_seg_num : 20

[.@feature]
feature_id : 1
version: 1
window_length : 10000
upper_bound : 1000
lamda : 0.5
discard:1
max_seg_num : 5

[count_dist]
[.invalid]
[..@feature]

[..@feature]
feature_id : 1

[..@feature]
feature_id : 2
version : 1

[..@feature]
feature_id : 3
version : 1
window_length : 10

[..@feature]
feature_id : 4
version : 1
window_length : 10
interval_endpoints : xxx

[.valid]
[..@feature]
feature_id : 111
version : 1
window_length : 10
interval_endpoints : 3
func_type   : max_diff
count_threshold : 1
stand_prob  : 0.2, 0.8

[invalid_concentration]
[.@feature]
feature_id : 120
version : 1
window_length : 10

[valid_concentration]
[.@feature]
feature_id : 120
version : 1
window_length : 100
step_length : 20
top : 1
remain : 0

[.@feature]
feature_id : 120
version : 1
window_length : 100
step_length : 20
top : 3
remain : 0
acc_main_view : 1

[.@feature]
feature_id : 120
version : 1
window_length : 100
step_length : 20
top : 3
remain : 0
acc_main_view : 1
need_top_data_views : 1

[invalid_acp]
[.@feature]
feature_id : 130
version : 1
window_length : 10

[valid_acp]
[.@feature]
feature_id : 130
version : 1
window_length : 10
premium_threshold : 2000
tol : 2.2
init : 2000
file_key : acp_test

[.@feature]
feature_id : 130
version : 1
window_length : 10
premium_threshold : 2000
tol : 1.96
init : 2000
file_key : acp_test

[invalid_distinct]
[.@feature]

[valid_distinct]
[.@feature]
feature_id : 140
version : 1
window_length : 3
step_length : 1

[distinct_dis]
[.invalid]
[..@feature]

[..@feature]
feature_id : 1

[..@feature]
feature_id : 2
version : 1

[..@feature]
feature_id : 3
version : 1
window_length : 10

[..@feature]
feature_id : 4
version : 1
window_length : 10
interval_endpoints : xxx

[.valid]
[..@feature]
feature_id : 1234
version : 1
window_length : 6
interval_endpoints : 1
func_type   : max_diff
count_threshold : 1
stand_prob  : 0.2, 0.8

[invalid_kpi]
[.@feature]
feature_id : xxx

[.@feature]
# no step_length and window_length % 4 != 0
feature_id : 777
version : 1
window_length : 10
step_length : 7
time_threshold : 2

[.@feature]
feature_id : 777
version : 1
window_length : 10
step_length : 5
time_threshold : 3
remain: 2

[valid_kpi]
[.feature]
feature_id : 888
version : 1
window_length : 16
time_threshold : 3
remain: 2,3

[invalid_session]
[.@feature]
feature_id : xxx

[.@feature]
feature_id : 888
version : 1
window_length : 8
time_threshold : 3
remain: yyy

[.@feature]
feature_id : 888
version : 1
window_length : 8
time_threshold : 3
remain: 2,3
interval_endpoints : xxx

[valid_session]
[.feature]
feature_id : 888
version : 1
window_length : 8
time_threshold : 3
remain: 2,3
interval_endpoints : 2

[valid_ratio_weak_anomaly]
feature_id : 100
version : 2
window_length : 100
remain : 6
remain_numerator : 1


[invalid_max_distance_magnify]
[.@feature]
feature_id : 5123
feature_type : distribution
func_type : chi_square_dis
view_level   : click_level
view : cntname
data_view: cdtime_diff
max_distance_magnify : xxxxx
interval_endpoints: 4
stand_prob : 0.5,0.5
count_threshold : 2
version : 1
click_mode : 0
window_type  : time
window_length : 1000
step_length : 250

[valid_max_distance_magnify]
[.@feature]
feature_id : 5123
feature_type : distribution
func_type : chi_square_dis
view_level   : click_level
view : cntname
data_view: cdtime_diff
max_distance_magnify : 5.0
interval_endpoints: 4
stand_prob : 0.5,0.5
count_threshold : 2
version : 1
click_mode : 0
window_type  : time
window_length : 1000
step_length : 250

[.@feature]
feature_id : 5123
feature_type : distribution
func_type : chi_square_dis
view_level   : click_level
view : cntname
data_view: cdtime_diff
interval_endpoints: 4
stand_prob : 0.5,0.5
count_threshold : 2
version : 1
click_mode : 0
window_type  : time
window_length : 1000
step_length : 250


[invalid_similar]
[.@feature]

[.@feature]
feature_id : 1
version : 1

[.@feature]
feature_id : 1
version : 1
window_length : 0

[valid_similar]
[.@feature]
feature_id : 100
version : 2
window_length : 5


[invalid_intention]
[.@feature]

[.@feature]
feature_id : 1
version : 1

[.@feature]
feature_id : 1
version : 1
window_length : 0

[valid_intention]
[.@feature]
feature_id : 100
version : 2
similar_threshold : 0.5
window_length : 2000
session_len : 1000
max_en_char : 20
max_cn_char : 20
