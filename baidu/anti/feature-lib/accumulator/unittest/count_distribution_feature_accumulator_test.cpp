// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: count_distribution_feature_accumulator_test.cpp
// @Last modified: 2017-03-16 14:26:21
// @Brief: 
#include <gtest/gtest.h>
#include "count_distribution_feature_accumulator.h"
#include "posix_themis_io.h"
#include "ckpt_test_util.h"

namespace anti {
namespace themis {
namespace feature_lib {

class CountDistributionFeatureAccumulatorTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
        ASSERT_TRUE(_conf.load("./conf", "accumulator_test.conf") == 0);
        // feature_id invalid
        FeatureValueProto fea1;
        fea1.set_feature_id(100LU);
        fea1.set_feature_type(FeatureValueProto::COUNT_DISTRIBUTION);
        _invalid_feas.push_back(fea1);
        // feature_type invalid
        FeatureValueProto fea2;
        fea2.set_feature_id(111LU);
        fea2.set_feature_type(FeatureValueProto::DISTRIBUTION);
        _invalid_feas.push_back(fea2);
        int64_t coords[] = {0L, 1L, 2L, 3L, 4L, 5L, 6L, 14L, 18L, 19L, 40L, 41L};
        for (int i = 0; i < 12; i++) {
            FeatureValueProto fea;
            fea.set_feature_id(111LU);
            fea.set_view_sign(123LU);
            fea.set_feature_type(FeatureValueProto::COUNT_DISTRIBUTION);
            fea.set_joinkey(119LU);
            fea.set_log_id(120LU);
            fea.set_log_time(1111111111LU);
            fea.set_coord(coords[i]);
            if (i == 5) {
                fea.set_data_view_sign(222LU);
            } else if (i == 6) {
                fea.set_data_view_sign(333LU);
            } else {
                fea.set_data_view_sign(444LU);
            }
            _valid_feas.push_back(fea);
        }
    }
    virtual void TearDown() {}

private:
    std::vector<FeatureValueProto> _invalid_feas;
    std::vector<FeatureValueProto> _valid_feas;
    comcfg::Configure _conf;
    CountDistributionFeatureAccumulator _obj;
};

TEST_F(CountDistributionFeatureAccumulatorTestSuite, construction_case) {
    EXPECT_EQ(0L, _obj.feature_id());
    EXPECT_EQ(0L, _obj._version);
}

TEST_F(CountDistributionFeatureAccumulatorTestSuite, init_case) {
    uint32_t num = _conf["count_dist"]["invalid"]["feature"].size();
    ASSERT_EQ(5U, num);
    for (uint32_t i = 0; i < num; i++) {
        EXPECT_FALSE(_obj.init(_conf["count_dist"]["invalid"]["feature"][i]));
        _obj.uninit();
    }
    ASSERT_TRUE(_obj.init(_conf["count_dist"]["valid"]["feature"][0]));
    EXPECT_EQ(111L, _obj.feature_id());
    EXPECT_EQ(1L, _obj._version);
    EXPECT_EQ(2U, _obj._intervals.size());
    _obj.uninit();
}

TEST_F(CountDistributionFeatureAccumulatorTestSuite, _init_intervals_case) {
    // invalid case
    EXPECT_FALSE(_obj._init_intervals(std::string("xxx")));
    _obj.uninit();
    // valid case
    ASSERT_TRUE(_obj._init_intervals(std::string("3")));
    ASSERT_EQ(2U, _obj._intervals.size());
    EXPECT_EQ(LLONG_MIN, _obj._intervals[0].first);
    EXPECT_EQ(3L, _obj._intervals[0].second);
    EXPECT_EQ(3L, _obj._intervals[1].first);
    EXPECT_EQ(LLONG_MAX, _obj._intervals[1].second);
    _obj.uninit();
}

TEST_F(CountDistributionFeatureAccumulatorTestSuite, _get_interval_idx_case) {
    ASSERT_TRUE(_obj.init(_conf["count_dist"]["valid"]["feature"][0]));
    // in first bucket
    uint32_t idx = 0;
    EXPECT_FALSE(_obj._get_interval_idx(2, &idx));
    EXPECT_EQ(0U, idx);
    // in second bucket and jump bucket
    idx = 0;
    EXPECT_TRUE(_obj._get_interval_idx(4, &idx));
    EXPECT_EQ(1U, idx);
    // in second bucket and NOT jump bucket
    idx = 0;
    EXPECT_FALSE(_obj._get_interval_idx(5, &idx));
    EXPECT_EQ(1U, idx);
}

TEST_F(CountDistributionFeatureAccumulatorTestSuite, update_and_query_case) {
    ASSERT_TRUE(_obj.init(_conf["count_dist"]["valid"]["feature"][0]));
    // invalid case
    EXPECT_FALSE(_obj.update_and_query(NULL));
    for (uint32_t i = 0; i < _invalid_feas.size(); i++) {
        EXPECT_FALSE(_obj.update_and_query(&_invalid_feas[i]));
    }
    int64_t exp_num[][2] {{0L, 0L}, {1L, 0L}, {2L, 0L}, 
            {0, 3L}, {0L, 4L}, {0L, 5L}, {1L, 5L}, 
            {0L, 0L}, {1L, 0L}, {2L, 0L}, {0L, 0L}, {1L, 0L}}; 
    uint32_t exp_bucket_idxs[] = {0L, 0L, 0L, 1L, 1L, 0L, 0L, 0L, 0L, 0L, 0L, 0L};
    for (int i = 0; i < 12; i++) {
        ASSERT_TRUE(_obj.update_and_query(&_valid_feas[i]));
        EXPECT_EQ(exp_bucket_idxs[i], _valid_feas[i].bucket_idx());
        for (int j = 0; j < 2; j++) {
            EXPECT_EQ(exp_num[i][j], _valid_feas[i].buckets(j).count());
        }
    }
    _obj.uninit();
}

TEST_F(CountDistributionFeatureAccumulatorTestSuite, multi_update_one_query_case) {
    ASSERT_TRUE(_obj.init(_conf["count_dist"]["valid"]["feature"][0]));
    // invalid case
    for (uint32_t i = 0; i < _invalid_feas.size(); i++) {
        EXPECT_FALSE(_obj.update(_invalid_feas[i]));
    }
    // valid case
    for (int i = 0; i < 5; i++) {
        ASSERT_TRUE(_obj.update(_valid_feas[i]));
    }
    // invalid query
    EXPECT_FALSE(_obj.query(NULL));
    FeatureValueProto inv_fea1;
    inv_fea1.set_feature_id(101L);
    inv_fea1.set_feature_type(FeatureValueProto::COUNT_DISTRIBUTION);
    EXPECT_FALSE(_obj.query(&inv_fea1));
    FeatureValueProto inv_fea2;
    inv_fea2.set_feature_id(111L);
    inv_fea2.set_feature_type(FeatureValueProto::DISTRIBUTION);
    EXPECT_FALSE(_obj.query(&inv_fea2));
    int64_t exp_num[][2] {{0L, 5L}, {2L, 0L}}; 
    uint32_t exp_bucket_idxs[] = {1L, 0L};
    // valid query
    FeatureValueProto va_fea1;
    va_fea1.set_feature_id(111L);
    va_fea1.set_feature_type(FeatureValueProto::COUNT_DISTRIBUTION);
    va_fea1.set_view_sign(123LU);
    va_fea1.set_data_view_sign(444LU);
    ASSERT_TRUE(_obj.query(&va_fea1));
    EXPECT_EQ(exp_bucket_idxs[0], va_fea1.bucket_idx());
    for (int j = 0; j < 2; j++) {
        EXPECT_EQ(exp_num[0][j], va_fea1.buckets(j).count());
    }
    for (int i = 5; i < 12; i++) {
        ASSERT_TRUE(_obj.update(_valid_feas[i]));
    }
    FeatureValueProto va_fea2;
    va_fea2.set_feature_id(111L);
    va_fea2.set_feature_type(FeatureValueProto::COUNT_DISTRIBUTION);
    va_fea2.set_view_sign(123LU);
    va_fea2.set_data_view_sign(444LU);
    va_fea2.set_coord(41L);
    ASSERT_TRUE(_obj.query(&va_fea2));
    EXPECT_EQ(exp_bucket_idxs[1], va_fea2.bucket_idx());
    for (int j = 0; j < 2; j++) {
        EXPECT_EQ(exp_num[1][j], va_fea2.buckets(j).count());
    }
    _obj.uninit();
}

TEST_F(CountDistributionFeatureAccumulatorTestSuite, posix_dump_and_load) {
    ASSERT_TRUE(_obj.init(_conf["count_dist"]["valid"]["feature"][0]));
    for (uint32_t i = 0; i < _valid_feas.size(); i++) {
        ASSERT_TRUE(_obj.update(_valid_feas[i]));
    }
    std::string type = "countdis"; 
    DOING_DUMP();
    _obj.uninit();

    ASSERT_TRUE(_obj.init(_conf["count_dist"]["valid"]["feature"][0]));
    // PosixIoWriterInterface *writer = new(std::nothrow) PosixThemisIoWriter();
    DOING_LOAD();
    FeatureValueProto fea;
    fea.set_feature_id(111L);
    fea.set_feature_type(FeatureValueProto::COUNT_DISTRIBUTION);
    fea.set_view_sign(123LU);
    fea.set_data_view_sign(444LU);
    fea.set_coord(41L);
    ASSERT_TRUE(_obj.query(&fea));
    EXPECT_EQ(0U, fea.bucket_idx());
    int64_t exp_num[] = {2, 0};
    for (int j = 0; j < 2; j++) {
        EXPECT_EQ(exp_num[j], fea.buckets(j).count());
    }
    _obj.uninit(); 

}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti


/* vim: set ts=4 sw=4: */

