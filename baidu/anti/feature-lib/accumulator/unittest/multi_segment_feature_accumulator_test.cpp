// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @brief 

#include <gtest/gtest.h>
#include "multi_segment_feature_accumulator.h"
#include "posix_themis_io.h" 
#include "ckpt_test_util.h"

namespace anti {
namespace themis {
namespace feature_lib {

const char* g_conf_path = "./conf";
const char* g_conf_file = "multisegment_feature.conf";

const int32_t kDefaultFeatureId = 404;

FeatureValueProto mock_feature(uint64_t sign, int64_t coord) {
    FeatureValueProto fea;
    fea.set_feature_type(FeatureValueProto::MULTI_SEG);
    fea.set_feature_id(kDefaultFeatureId);
    fea.set_view_sign(sign);
    fea.set_coord(coord);
    return fea;
}

class MultiSegAccumulatorTestSuite : public ::testing::Test {
public:
    virtual void SetUp() {
        _step_len = 100;
        _segment_len = 1000;
    }
    virtual void TearDown() {}

private:
    MultiSegmentAccumulator _obj;
    int64_t _step_len;
    int64_t _segment_len;
};

TEST_F(MultiSegAccumulatorTestSuite, init) {
    comcfg::Configure cfg; 
    ASSERT_TRUE(cfg.load(g_conf_path, g_conf_file) == 0);
    auto& acc_conf = cfg["multisegment"];

    ASSERT_TRUE(_obj.init(acc_conf));

    EXPECT_EQ(_obj._feature_id, 404);
    EXPECT_EQ(_obj._version, 1);
}

TEST_F(MultiSegAccumulatorTestSuite, insert_and_query_normal_case) {
    _obj._feature_id = kDefaultFeatureId;
    ASSERT_TRUE(_obj._segment.init(_step_len, _segment_len));

    FeatureValueProto fea[] = { mock_feature(1, 1001),
                                mock_feature(1, 1201),
                                mock_feature(1, 1301) };

    int64_t target[] = {1, 1, 1};
    for (int32_t i = 0; i < 3; ++i) {
        ASSERT_TRUE(_obj.update_and_query(&fea[i]));
        EXPECT_EQ(fea[i].cur_seg_count(), target[i]);
    }
}

TEST_F(MultiSegAccumulatorTestSuite, insert_and_query_repeated) {
    _obj._feature_id = kDefaultFeatureId;
    ASSERT_TRUE(_obj._segment.init(_step_len, _segment_len));

    FeatureValueProto fea[] = { mock_feature(1, 1001),
                                mock_feature(1, 1201),
                                mock_feature(1, 1011),
                                mock_feature(1, 1201),
                                mock_feature(1, 1101),
                                mock_feature(1, 1301) };

    int64_t target[] = {1, 1, 2, 2, 1, 1};
    for (int32_t i = 0; i < 6; ++i) {
        ASSERT_TRUE(_obj.update_and_query(&fea[i]));
        EXPECT_EQ(fea[i].cur_seg_count(), target[i]);
    }
}

TEST_F(MultiSegAccumulatorTestSuite, insert_and_query_slide) {
    _obj._feature_id = kDefaultFeatureId;
    ASSERT_TRUE(_obj._segment.init(_step_len, _segment_len));

    FeatureValueProto fea[] = { mock_feature(1, 1001),
                                mock_feature(1, 1011),
                                mock_feature(1, 11001),
                                mock_feature(1, 1001) };

    int64_t target[] = {1, 2, 1};
    for (int32_t i = 0; i < 3; ++i) {
        ASSERT_TRUE(_obj.update_and_query(&fea[i]));
        EXPECT_EQ(fea[i].cur_seg_count(), target[i]);
    }
    ASSERT_FALSE(_obj.update_and_query(&fea[3]));
}

TEST_F(MultiSegAccumulatorTestSuite, dump_and_load) {
    system("rm ./multiseg.ckpt");

    _obj._feature_id = kDefaultFeatureId;
    _obj._version = 1;
    ASSERT_TRUE(_obj._segment.init(_step_len, _segment_len));

    FeatureValueProto fea[] = { mock_feature(1, 1001),
                                mock_feature(1, 1201),
                                mock_feature(1, 1011),
                                mock_feature(1, 1201),
                                mock_feature(1, 1101),
                                mock_feature(1, 1301) };

    // -- insert and dump --
    {
        int64_t target[] = {1, 1, 2, 2, 1, 1};
        for (int32_t i = 0; i < 6; ++i) {
            ASSERT_TRUE(_obj.update_and_query(&fea[i]));
            EXPECT_EQ(fea[i].cur_seg_count(), target[i]);
        }

        DOING_DUMP();
        _obj.uninit();
    }

    // -- read and check
    {
        _obj._feature_id = kDefaultFeatureId;
        int target[] = {3, 3, 4, 4, 2, 2};
        _obj._version = 1;
        ASSERT_TRUE(_obj._segment.init(_step_len, _segment_len));
        DOING_LOAD();

        ASSERT_EQ(_obj._version, 1);
        for (int32_t i = 0; i < 6; ++i) {
            ASSERT_TRUE(_obj.update_and_query(&fea[i]));
            EXPECT_EQ(fea[i].cur_seg_count(), target[i]);
        }
        DOING_DUMP();
        _obj.uninit();
    }

    // -- read and drop old checkpoint
    {
        _obj._feature_id = kDefaultFeatureId;
        // !!! set bigger version to drop old checkpoint
        int64_t target[] = {1, 1, 2, 2, 1, 1};
        _obj._version = 2;
        ASSERT_TRUE(_obj._segment.init(_step_len, _segment_len));
        DOING_LOAD();
        for (int32_t i = 0; i < 6; ++i) {
            ASSERT_TRUE(_obj.update_and_query(&fea[i]));
            EXPECT_EQ(fea[i].cur_seg_count(), target[i]);
        }
        _obj.uninit();
    }

    system("rm ./multiseg.ckpt");
}

}
}
}
