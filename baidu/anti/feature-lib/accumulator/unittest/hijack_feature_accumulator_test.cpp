// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// @brief 

#include <string>
#include <gtest/gtest.h>
#include <com_log.h>
#include "hijack_feature_accumulator.h"
#include "posix_themis_io.h" 
#include "ckpt_test_util.h"

namespace anti {
namespace themis {
namespace feature_lib {

const char* g_conf_path = "./conf";
const char* g_conf_file = "hijack_feature.conf";

const int32_t kDefaultFeatureId = 104;
const int64_t kMaxDiff = 0x1FFFFFFF;

FeatureValueProto mock_feature(uint32_t sign, int64_t time, uint32_t page_no,
        const std::string& cn, const std::string& flow_group) {
    FeatureValueProto fea;
    fea.set_feature_type(FeatureValueProto::HIJACK);
    fea.set_feature_id(kDefaultFeatureId);
    fea.set_view_sign(sign);
    fea.set_log_time(time);

    FeatureValueProto::HijackProto hj_val;
    hj_val.set_page_no(page_no);
    hj_val.set_charge_name(cn);
    hj_val.set_flow_group(flow_group);

    fea.mutable_original_hijack_field()->CopyFrom(hj_val);
    return fea;
}

FeatureValueProto mock_feature(uint32_t sign, int64_t time, uint32_t page_no) {
    return mock_feature(sign, time, page_no, ("default-cn"), ("default-fg"));
}

class HijackAccumulatorTestSuite : public ::testing::Test {
public:
    virtual void SetUp() {}
    virtual void TearDown() {}

private:
    HijackAccumulator _obj;
};

TEST_F(HijackAccumulatorTestSuite, init) {
    comcfg::Configure cfg;
    ASSERT_TRUE(cfg.load(g_conf_path, g_conf_file) == 0);

    auto& conf = cfg["hijack"];

    ASSERT_TRUE(_obj.init(conf));

    EXPECT_EQ(_obj._feature_id, 101);
    EXPECT_EQ(_obj._version, 1);
    EXPECT_EQ(_obj._range, 33);
}

TEST_F(HijackAccumulatorTestSuite, insert_and_query_succ) {
    _obj._goe_list.reset(
            new (std::nothrow) HijackAccumulator::GOEList(100, 10));
    _obj._feature_id = kDefaultFeatureId;
    _obj._range = 100;

    // mock feature
    FeatureValueProto fea[] = { mock_feature(1, 10000, 0, "c", "f"),
                                mock_feature(1, 30000, 0, "c1", "f"),
                                mock_feature(1, 20000, 0, "c", "f") };

    for (int32_t i = 0; i < 3; ++i) {
        ASSERT_TRUE(_obj.update(fea[i]));
    }

    for (int32_t i = 0; i < 3; ++i) {
        ASSERT_TRUE(_obj.query(&fea[i])) << i;
    }

    ASSERT_EQ(fea[1].fix_hijack_field_size(), 2);
    int32_t dist[] = {10, 20};
    for (int32_t i = 0; i < fea[1].fix_hijack_field_size(); ++i) {
        auto obj = fea[1].fix_hijack_field(i);
        EXPECT_EQ(obj.charge_name(), "c");
        EXPECT_EQ(obj.flow_group(), "f");
        EXPECT_EQ(obj.distance(), dist[i]);
    }

    EXPECT_EQ(fea[2].fix_hijack_field_size(), 2);
    EXPECT_EQ(fea[0].fix_hijack_field_size(), 2);
}

TEST_F(HijackAccumulatorTestSuite, insert_and_query_out_of_range) {
    _obj._goe_list.reset(
            new (std::nothrow) HijackAccumulator::GOEList(100, 10));
    _obj._feature_id = kDefaultFeatureId;
    _obj._range = 10;

    // mock feature
    FeatureValueProto fea[] = { mock_feature(1, 10000, 0, "c", "f"),
                                mock_feature(1, 30000, 0, "c1", "f"),
                                mock_feature(1, 20000, 0, "c", "f") };

    for (int32_t i = 0; i < 3; ++i) {
        ASSERT_TRUE(_obj.update(fea[i]));
    }

    int32_t hijack_field_size[] = { 1, 1, 2 };
    for (int32_t i = 0; i < 3; ++i) {
        ASSERT_TRUE(_obj.query(&fea[i])) << i;
        EXPECT_EQ(hijack_field_size[i], fea[i].fix_hijack_field_size()) << i;
    }
}

TEST_F(HijackAccumulatorTestSuite, same_feature) {
    _obj._goe_list.reset(
            new (std::nothrow) HijackAccumulator::GOEList(100, 10));
    _obj._feature_id = kDefaultFeatureId;
    _obj._range = 1;


    FeatureValueProto fea[] = { mock_feature(1, 1445586596416, 0, "ops1012704w", "unknown"),
                                mock_feature(1, 1445586596890, 0, "ops1012585u", "unknown") };

    fea[0].set_uniq_feat_sign(4139378138U);
    fea[1].set_uniq_feat_sign(4139378148U);

    for (int32_t i = 0; i < 2; ++i) {
        ASSERT_TRUE(_obj.update(fea[i]));
    }

    int32_t hijack_field_size[] = {1, 1};
    for (int32_t i = 0; i < 2; ++i) {
        ASSERT_TRUE(_obj.query(&fea[i])) << i;
        EXPECT_EQ(hijack_field_size[i], fea[i].fix_hijack_field_size()) << i;
    }

    auto check_func = [](FeatureValueProto& lh, FeatureValueProto& rh) {
        ASSERT_STREQ(lh.fix_hijack_field(0).charge_name().c_str(),
                rh.original_hijack_field().charge_name().c_str());
        ASSERT_STREQ(lh.fix_hijack_field(0).flow_group().c_str(),
                rh.original_hijack_field().flow_group().c_str());
        ASSERT_EQ(lh.fix_hijack_field(0).page_no(),
                rh.original_hijack_field().page_no());
    };

    check_func(fea[0], fea[1]);
    check_func(fea[1], fea[0]);
    ASSERT_EQ(fea[0].fix_hijack_field(0).distance(), -1); 
    ASSERT_EQ(fea[1].fix_hijack_field(0).distance(), 0); 
}

TEST_F(HijackAccumulatorTestSuite, online_case_same_second_feature) {
    _obj._goe_list.reset(
            new (std::nothrow) HijackAccumulator::GOEList(100, 10));
    _obj._feature_id = kDefaultFeatureId;
    _obj._range = 1;


    FeatureValueProto fea[] = { mock_feature(1, 1445586596416, 0, "ops1012704w", "unknown"),
                                mock_feature(1, 1445586596890, 0, "ops1012585u", "unknown") };

    fea[0].set_uniq_feat_sign(4139378138U);
    fea[1].set_uniq_feat_sign(4139378148U);

    for (int32_t i = 0; i < 2; ++i) {
        ASSERT_TRUE(_obj.update(fea[i]));
    }

    int32_t hijack_field_size[] = {1, 1};
    for (int32_t i = 0; i < 2; ++i) {
        ASSERT_TRUE(_obj.query(&fea[i])) << i;
        EXPECT_EQ(hijack_field_size[i], fea[i].fix_hijack_field_size()) << i;
    }

    auto check_func = [](FeatureValueProto& lh, FeatureValueProto& rh) {
        ASSERT_STREQ(lh.fix_hijack_field(0).charge_name().c_str(),
                rh.original_hijack_field().charge_name().c_str());
        ASSERT_STREQ(lh.fix_hijack_field(0).flow_group().c_str(),
                rh.original_hijack_field().flow_group().c_str());
        ASSERT_EQ(lh.fix_hijack_field(0).page_no(),
                rh.original_hijack_field().page_no());
    };

    check_func(fea[0], fea[1]);
    check_func(fea[1], fea[0]);
    ASSERT_EQ(fea[0].fix_hijack_field(0).distance(), -1);  
    ASSERT_EQ(fea[1].fix_hijack_field(0).distance(), 0); 
}

TEST_F(HijackAccumulatorTestSuite, online_case_three_nodes) {
    _obj._goe_list.reset(
            new (std::nothrow) HijackAccumulator::GOEList(100, 10));
    _obj._feature_id = kDefaultFeatureId;
    _obj._range = 1;


    FeatureValueProto fea[] = { mock_feature(1, 1471390663415, 0, "ops1012015a", "unknown"),
                                mock_feature(1, 1471390664078, 0, "ops1014629q", "unknown"),
                                mock_feature(1, 1471390664472, 0, "ops1014629q", "unknown") };


    fea[0].set_uniq_feat_sign(4139378138U);
    fea[1].set_uniq_feat_sign(4139378148U);
    fea[2].set_uniq_feat_sign(4139378158U);

    for (int32_t i = 0; i < 3; ++i) {
        ASSERT_TRUE(_obj.update(fea[i]));
    }

    int32_t hijack_field_size[] = {2, 2, 2};
    for (int32_t i = 0; i < 3; ++i) {
        ASSERT_TRUE(_obj.query(&fea[i])) << i;
        EXPECT_EQ(hijack_field_size[i], fea[i].fix_hijack_field_size()) << i;
    }

    auto check_func = [](const FeatureValueProto::HijackProto& lh, const FeatureValueProto& rh) {
        ASSERT_STREQ(lh.charge_name().c_str(),
                rh.original_hijack_field().charge_name().c_str());
        ASSERT_STREQ(lh.flow_group().c_str(),
                rh.original_hijack_field().flow_group().c_str());
        ASSERT_EQ(lh.page_no(),
                rh.original_hijack_field().page_no());
    };

    check_func(fea[0].fix_hijack_field(0), fea[2]);
    check_func(fea[0].fix_hijack_field(1), fea[1]);
    check_func(fea[1].fix_hijack_field(0), fea[2]);
    check_func(fea[1].fix_hijack_field(1), fea[0]);
    check_func(fea[2].fix_hijack_field(0), fea[1]);
    check_func(fea[2].fix_hijack_field(1), fea[0]);
    ASSERT_EQ(fea[0].fix_hijack_field(0).distance(), -1);
    ASSERT_EQ(fea[0].fix_hijack_field(1).distance(), -1);
    ASSERT_EQ(fea[1].fix_hijack_field(0).distance(), -1);
    ASSERT_EQ(fea[1].fix_hijack_field(1).distance(), 1);
    ASSERT_EQ(fea[2].fix_hijack_field(0).distance(), 0);
    ASSERT_EQ(fea[2].fix_hijack_field(1).distance(), 1);
}

TEST_F(HijackAccumulatorTestSuite, dump_and_load) {
    auto init_obj = [] (HijackAccumulator* acc) {
        acc->_goe_list.reset(
               new (std::nothrow) HijackAccumulator::GOEList(100, 10));
        acc->_feature_id = kDefaultFeatureId;
        acc->_range = 100;
    };

    auto check_func = [] (HijackAccumulator* acc, FeatureValueProto* fea) {
        for (int32_t i = 0; i < 3; ++i) {
            ASSERT_TRUE(acc->query(&fea[i])) << i;
        }

        ASSERT_EQ(fea[1].fix_hijack_field_size(), 2);
        int32_t dist[] = {10, 20};
        uint32_t page_nos[] = {1, 2};
        for (int32_t i = 0; i < fea[1].fix_hijack_field_size(); ++i) {
            auto obj = fea[1].fix_hijack_field(i);
            EXPECT_EQ(obj.charge_name(), "c");
            EXPECT_EQ(obj.flow_group(), "f");
            EXPECT_EQ(obj.page_no(), page_nos[i]);
            EXPECT_EQ(obj.distance(), dist[i]);
        }

        EXPECT_EQ(fea[2].fix_hijack_field_size(), 2);
        EXPECT_EQ(fea[0].fix_hijack_field_size(), 2);
    };

    // -- dump acc --
    {
        // mock feature
        FeatureValueProto fea[] = { mock_feature(1, 10000, 2, "c", "f"),
                                    mock_feature(1, 30000, 1, "c1", "f"),
                                    mock_feature(1, 20000, 1, "c", "f") };

        init_obj(&_obj);
        for (int32_t i = 0; i < 3; ++i) {
            ASSERT_TRUE(_obj.update(fea[i]));
        }
        check_func(&_obj, fea);

        DOING_DUMP();
    }

    // -- read acc --
    {
        FeatureValueProto fea[] = { mock_feature(1, 10000, 2, "c", "f"),
                                    mock_feature(1, 30000, 1, "c1", "f"),
                                    mock_feature(1, 20000, 1, "c", "f") };

        _obj.uninit();
        init_obj(&_obj);
        DOING_LOAD();
        check_func(&_obj, fea);
    }

    system("rm ./test.dat");
}

}
}
}
