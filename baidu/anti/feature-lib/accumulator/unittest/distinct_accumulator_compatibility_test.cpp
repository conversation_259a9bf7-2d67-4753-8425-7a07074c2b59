// Copyright (c) 2025 Baidu.com, Inc. All Rights Reserved
// @Author: <PERSON> (<EMAIL>)
// 
// @Brief: DistinctFeatureAccumulator与DistinctFeatureAccumulatorV2兼容性测试

#include <vector>
#include <gtest/gtest.h>
#include "boost/lexical_cast.hpp"
#include "distinct_feature_accumulator.h"
#include "distinct_feature_accumulator_v2.h"
#include "posix_themis_io.h"
#include "ckpt_test_util.h"

namespace anti {
namespace themis {
namespace feature_lib {

DECLARE_bool(need_set_distinct_data_view_signs);
DECLARE_bool(enable_mem_pool_for_acc);

class DistinctAccumulatorCompatibilityTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
        ASSERT_TRUE(_conf.load("./conf", "accumulator_test.conf") == 0);

        // 创建多样化的测试数据
        int64_t coords[] = {1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L, 9L, 10L};
        uint64_t data_view_signs[] = {
            100001LU, 100002LU, 100003LU, 100001LU, 100004LU,  // 有重复
            100005LU, 100002LU, 100006LU, 100007LU, 100001LU   // 更多重复
        };
        
        for (uint32_t i = 0U; i < 10U; ++i) {
            FeatureValueProto fea;
            fea.set_feature_id(140LU);
            fea.set_view_sign(123456LU);
            fea.set_feature_type(FeatureValueProto::DISTINCT);
            fea.set_joinkey(119LU);
            fea.set_log_id(120LU);
            fea.set_log_time(1111111111LU);
            fea.set_coord(coords[i]);
            fea.set_data_view_sign(data_view_signs[i]);
            _feas.push_back(fea);
        }
        
        // 创建不同view_sign的数据
        for (uint32_t i = 0U; i < 5U; ++i) {
            FeatureValueProto fea;
            fea.set_feature_id(140LU);
            fea.set_view_sign(654321LU + i);  // 不同的view_sign
            fea.set_feature_type(FeatureValueProto::DISTINCT);
            fea.set_joinkey(119LU);
            fea.set_log_id(120LU);
            fea.set_log_time(1111111111LU);
            fea.set_coord(i + 1);
            fea.set_data_view_sign(200001LU + i);
            _multi_view_feas.push_back(fea);
        }

        _old_fea = _feas[0];
        _old_fea.set_coord(0L);  // 过期数据

        // 创建无效数据
        FeatureValueProto fea;
        fea.set_feature_id(123456789LU);
        _inv_feas.push_back(fea);

        fea.set_feature_id(140LU);
        fea.set_feature_type(FeatureValueProto::RATIO);
        _inv_feas.push_back(fea);
    }
    
    virtual void TearDown() {
        _feas.clear();
        _multi_view_feas.clear();
        _inv_feas.clear();
    }

private:
    DistinctFeatureAccumulator _original_obj;
    DistinctFeatureAccumulatorV2 _v2_obj;
    comcfg::Configure _conf;

    std::vector<FeatureValueProto> _feas;
    std::vector<FeatureValueProto> _multi_view_feas;
    FeatureValueProto _old_fea;
    std::vector<FeatureValueProto> _inv_feas;

    // 辅助函数：比较两个累积器的查询结果
    void compare_query_results(const FeatureValueProto& query_fea) {
        FeatureValueProto original_result = query_fea;
        FeatureValueProto v2_result = query_fea;
        
        bool original_success = _original_obj.query(&original_result);
        bool v2_success = _v2_obj.query(&v2_result);
        
        ASSERT_EQ(original_success, v2_success) 
            << "Query success status mismatch for view_sign=" << query_fea.view_sign();
        
        if (original_success) {
            EXPECT_EQ(original_result.value(), v2_result.value()) 
                << "Query result value mismatch for view_sign=" << query_fea.view_sign()
                << ", original=" << original_result.value() 
                << ", v2=" << v2_result.value();
                
            EXPECT_EQ(original_result.data_view_signs_size(), v2_result.data_view_signs_size())
                << "data_view_signs size mismatch";
        }
    }

    // 辅助函数：比较update_and_query的结果
    void compare_update_and_query_results(FeatureValueProto& fea) {
        FeatureValueProto original_fea = fea;
        FeatureValueProto v2_fea = fea;
        
        bool original_success = _original_obj.update_and_query(&original_fea);
        bool v2_success = _v2_obj.update_and_query(&v2_fea);
        
        ASSERT_EQ(original_success, v2_success) 
            << "update_and_query success status mismatch";
        
        if (original_success) {
            EXPECT_EQ(original_fea.value(), v2_fea.value()) 
                << "update_and_query result value mismatch"
                << ", original=" << original_fea.value() 
                << ", v2=" << v2_fea.value();
        }
    }
};

TEST_F(DistinctAccumulatorCompatibilityTestSuite, test_initialization_compatibility) {
    // 测试两个版本的初始化行为是否一致
    
    // 测试有效配置
    bool original_init = _original_obj.init(_conf["valid_distinct"]["feature"][0]);
    bool v2_init = _v2_obj.init(_conf["valid_distinct"]["feature"][0]);
    
    EXPECT_TRUE(original_init);
    EXPECT_TRUE(v2_init);
    EXPECT_EQ(_original_obj.feature_id(), _v2_obj.feature_id());
    
    // 测试无效配置
    _original_obj.uninit();
    _v2_obj.uninit();
    
    uint32_t num = _conf["invalid_distinct"]["feature"].size();
    for (uint32_t i = 0U; i < num; ++i) {
        bool original_invalid = _original_obj.init(_conf["invalid_distinct"]["feature"][i]);
        bool v2_invalid = _v2_obj.init(_conf["invalid_distinct"]["feature"][i]);
        
        EXPECT_EQ(original_invalid, v2_invalid) 
            << "Invalid config handling mismatch at index " << i;
        
        _original_obj.uninit();
        _v2_obj.uninit();
    }
}

TEST_F(DistinctAccumulatorCompatibilityTestSuite, test_basic_operations_compatibility) {
    // 初始化两个累积器
    ASSERT_TRUE(_original_obj.init(_conf["valid_distinct"]["feature"][0]));
    ASSERT_TRUE(_v2_obj.init(_conf["valid_distinct"]["feature"][0]));
    
    // 测试相同的操作序列
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        bool original_update = _original_obj.update(_feas[i]);
        bool v2_update = _v2_obj.update(_feas[i]);
        
        EXPECT_EQ(original_update, v2_update) 
            << "Update result mismatch at index " << i;
    }
    
    // 比较查询结果
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        compare_query_results(_feas[i]);
    }
    
    // 测试无效特征的处理
    for (uint32_t i = 0U; i < _inv_feas.size(); ++i) {
        bool original_invalid = _original_obj.update(_inv_feas[i]);
        bool v2_invalid = _v2_obj.update(_inv_feas[i]);
        
        EXPECT_EQ(original_invalid, v2_invalid) 
            << "Invalid feature handling mismatch at index " << i;
    }
    
    // 测试过期数据的处理
    bool original_old = _original_obj.update(_old_fea);
    bool v2_old = _v2_obj.update(_old_fea);
    EXPECT_EQ(original_old, v2_old) << "Old feature handling mismatch";
    
    _original_obj.uninit();
    _v2_obj.uninit();
}

TEST_F(DistinctAccumulatorCompatibilityTestSuite, test_update_and_query_compatibility) {
    ASSERT_TRUE(_original_obj.init(_conf["valid_distinct"]["feature"][0]));
    ASSERT_TRUE(_v2_obj.init(_conf["valid_distinct"]["feature"][0]));
    
    // 先进行一些基本的update操作
    for (uint32_t i = 0U; i < 5U; ++i) {
        _original_obj.update(_feas[i]);
        _v2_obj.update(_feas[i]);
    }
    
    // 测试update_and_query操作
    for (uint32_t i = 5U; i < _feas.size(); ++i) {
        compare_update_and_query_results(_feas[i]);
    }
    
    // 测试无效特征的update_and_query
    for (uint32_t i = 0U; i < _inv_feas.size(); ++i) {
        FeatureValueProto original_inv = _inv_feas[i];
        FeatureValueProto v2_inv = _inv_feas[i];
        
        bool original_result = _original_obj.update_and_query(&original_inv);
        bool v2_result = _v2_obj.update_and_query(&v2_inv);
        
        EXPECT_EQ(original_result, v2_result) 
            << "Invalid feature update_and_query mismatch";
    }
    
    _original_obj.uninit();
    _v2_obj.uninit();
}

TEST_F(DistinctAccumulatorCompatibilityTestSuite, test_serialization_compatibility) {
    ASSERT_TRUE(_original_obj.init(_conf["valid_distinct"]["feature"][0]));
    ASSERT_TRUE(_v2_obj.init(_conf["valid_distinct"]["feature"][0]));
    
    // 添加相同的数据
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        _original_obj.update(_feas[i]);
        _v2_obj.update(_feas[i]);
    }
    
    // 验证更新后的查询结果一致
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        compare_query_results(_feas[i]);
    }
    
    // 测试序列化
    std::string original_type = "original_compat";
    std::string v2_type = "v2_compat";
    
    // 序列化原版
    {
        std::string type = original_type;
        DOING_DUMP_VAR(_original_obj);
    }
    
    // 序列化V2版本
    {
        std::string type = v2_type;
        DOING_DUMP_VAR(_v2_obj);
    }
    
    // 清理并重新初始化
    _original_obj.uninit();
    _v2_obj.uninit();
    
    ASSERT_TRUE(_original_obj.init(_conf["valid_distinct"]["feature"][0]));
    ASSERT_TRUE(_v2_obj.init(_conf["valid_distinct"]["feature"][0]));
    
    // 反序列化
    {
        std::string type = original_type;
        DOING_LOAD_VAR(_original_obj);
    }
    
    {
        std::string type = v2_type;
        DOING_LOAD_VAR(_v2_obj);
    }
    
    // 验证反序列化后的查询结果一致
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        compare_query_results(_feas[i]);
    }
    
    _original_obj.uninit();
    _v2_obj.uninit();
}

TEST_F(DistinctAccumulatorCompatibilityTestSuite, test_extended_query_compatibility) {
    ASSERT_TRUE(_original_obj.init(_conf["valid_distinct"]["feature"][0]));
    ASSERT_TRUE(_v2_obj.init(_conf["valid_distinct"]["feature"][0]));
    
    // 添加数据
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        _original_obj.update(_feas[i]);
        _v2_obj.update(_feas[i]);
    }
    
    // 测试扩展查询接口
    FValQRequestProto request;
    request.set_view_sign(_feas[0].view_sign());
    
    FValQResponseProto original_response;
    FValQResponseProto v2_response;
    
    bool original_success = _original_obj.query(request, &original_response);
    bool v2_success = _v2_obj.query(request, &v2_response);
    
    EXPECT_EQ(original_success, v2_success) << "Extended query success mismatch";
    
    if (original_success) {
        EXPECT_EQ(original_response.value_size(), v2_response.value_size()) 
            << "Extended query response size mismatch";
            
        for (int i = 0; i < original_response.value_size(); ++i) {
            EXPECT_EQ(original_response.value(i), v2_response.value(i)) 
                << "Extended query response value mismatch at index " << i;
        }
    }
    
    _original_obj.uninit();
    _v2_obj.uninit();
}

TEST_F(DistinctAccumulatorCompatibilityTestSuite, test_data_view_signs_compatibility) {
    ASSERT_TRUE(_original_obj.init(_conf["valid_distinct"]["feature"][0]));
    ASSERT_TRUE(_v2_obj.init(_conf["valid_distinct"]["feature"][0]));
    
    // 测试data_view_signs功能
    FLAGS_need_set_distinct_data_view_signs = true;
    
    // 添加数据
    _original_obj.update(_feas[0]);
    _v2_obj.update(_feas[0]);
    
    // 查询并比较data_view_signs
    FeatureValueProto original_query = _feas[0];
    FeatureValueProto v2_query = _feas[0];
    
    ASSERT_TRUE(_original_obj.query(&original_query));
    ASSERT_TRUE(_v2_obj.query(&v2_query));
    
    EXPECT_EQ(original_query.data_view_signs_size(), v2_query.data_view_signs_size())
        << "data_view_signs size mismatch";
    
    for (int i = 0; i < original_query.data_view_signs_size(); ++i) {
        EXPECT_EQ(original_query.data_view_signs(i), v2_query.data_view_signs(i))
            << "data_view_signs value mismatch at index " << i;
    }
    
    // 重置flag
    FLAGS_need_set_distinct_data_view_signs = false;
    
    _original_obj.uninit();
    _v2_obj.uninit();
}

TEST_F(DistinctAccumulatorCompatibilityTestSuite, test_multi_view_compatibility) {
    ASSERT_TRUE(_original_obj.init(_conf["valid_distinct"]["feature"][0]));
    ASSERT_TRUE(_v2_obj.init(_conf["valid_distinct"]["feature"][0]));
    
    // 测试多个view_sign的情况
    for (const auto& fea : _multi_view_feas) {
        _original_obj.update(fea);
        _v2_obj.update(fea);
    }
    
    // 验证每个view_sign的查询结果
    for (const auto& fea : _multi_view_feas) {
        compare_query_results(fea);
    }
    
    _original_obj.uninit();
    _v2_obj.uninit();
}

TEST_F(DistinctAccumulatorCompatibilityTestSuite, test_edge_cases_compatibility) {
    ASSERT_TRUE(_original_obj.init(_conf["valid_distinct"]["feature"][0]));
    ASSERT_TRUE(_v2_obj.init(_conf["valid_distinct"]["feature"][0]));
    
    // 测试边界情况
    
    // 1. 空data_view_sign
    FeatureValueProto empty_data_fea;
    empty_data_fea.set_feature_id(140LU);
    empty_data_fea.set_view_sign(123456LU);
    empty_data_fea.set_feature_type(FeatureValueProto::DISTINCT);
    empty_data_fea.set_coord(1);
    empty_data_fea.set_data_view_sign(0);
    
    bool original_empty = _original_obj.update(empty_data_fea);
    bool v2_empty = _v2_obj.update(empty_data_fea);
    EXPECT_EQ(original_empty, v2_empty) << "Empty data_view_sign handling mismatch";
    
    if (original_empty) {
        compare_query_results(empty_data_fea);
    }
    
    // 2. NULL指针查询
    bool original_null = _original_obj.query(NULL);
    bool v2_null = _v2_obj.query(NULL);
    EXPECT_EQ(original_null, v2_null) << "NULL query handling mismatch";
    
    // 3. 不存在的view_sign查询
    FeatureValueProto nonexistent_fea;
    nonexistent_fea.set_feature_id(140LU);
    nonexistent_fea.set_view_sign(999999LU);  // 不存在的view_sign
    nonexistent_fea.set_feature_type(FeatureValueProto::DISTINCT);
    
    compare_query_results(nonexistent_fea);
    
    _original_obj.uninit();
    _v2_obj.uninit();
}

TEST_F(DistinctAccumulatorCompatibilityTestSuite, test_performance_consistency) {
    ASSERT_TRUE(_original_obj.init(_conf["valid_distinct"]["feature"][0]));
    ASSERT_TRUE(_v2_obj.init(_conf["valid_distinct"]["feature"][0]));
    
    // 创建大量测试数据
    std::vector<FeatureValueProto> large_dataset;
    for (int i = 0; i < 10000; ++i) {
        FeatureValueProto fea;
        fea.set_feature_id(140LU);
        fea.set_view_sign(123456LU + (i % 100));  // 100个不同的view_sign
        fea.set_feature_type(FeatureValueProto::DISTINCT);
        fea.set_coord(i + 1);
        fea.set_data_view_sign(500000LU + i);
        large_dataset.push_back(fea);
    }
    
    // 批量更新两个累积器
    for (const auto& fea : large_dataset) {
        bool original_result = _original_obj.update(fea);
        bool v2_result = _v2_obj.update(fea);
        EXPECT_EQ(original_result, v2_result) << "Large dataset update mismatch";
    }
    
    // 随机选择一些数据进行查询验证
    for (int i = 0; i < 100; i += 10) {
        compare_query_results(large_dataset[i]);
    }
    
    _original_obj.uninit();
    _v2_obj.uninit();
}

TEST_F(DistinctAccumulatorCompatibilityTestSuite, test_cross_version_checkpoint_compatibility) {
    ASSERT_TRUE(_original_obj.init(_conf["valid_distinct"]["feature"][0]));
    ASSERT_TRUE(_v2_obj.init(_conf["valid_distinct"]["feature"][0]));
    
    // 添加相同的测试数据到两个版本
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        _original_obj.update(_feas[i]);
        _v2_obj.update(_feas[i]);
    }
    
    // 验证初始状态一致
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        compare_query_results(_feas[i]);
    }
    
    // 测试1: V2版本创建checkpoint -> 原版加载
    {
        std::string v2_checkpoint_type = "v2_to_orig_test";
        
        // 先保存V2版本的期望查询结果 - 只测试窗口内的数据
        std::vector<std::string> expected_values;
        std::vector<uint32_t> valid_indices;
        
        for (uint32_t i = 0U; i < _feas.size(); ++i) {
            FeatureValueProto v2_query = _feas[i];
            if (_v2_obj.query(&v2_query)) {
                expected_values.push_back(v2_query.value());
                valid_indices.push_back(i);
            }
        }
        
        // V2版本序列化
        {
            std::string type = v2_checkpoint_type;
            DOING_DUMP_VAR(_v2_obj);
        }
        
        // 原版清理并重新初始化
        _original_obj.uninit();
        ASSERT_TRUE(_original_obj.init(_conf["valid_distinct"]["feature"][0]));
        
        // 原版加载V2的checkpoint
        {
            std::string type = v2_checkpoint_type;
            DOING_LOAD_VAR(_original_obj);
        }
        
        // 验证原版加载V2 checkpoint后的查询结果与期望值一致
        for (uint32_t j = 0U; j < valid_indices.size(); ++j) {
            uint32_t i = valid_indices[j];
            FeatureValueProto orig_query = _feas[i];
            ASSERT_TRUE(_original_obj.query(&orig_query));
            
            EXPECT_EQ(expected_values[j], orig_query.value()) 
                << "V2->Original checkpoint loading: query result mismatch for view_sign=" 
                << _feas[i].view_sign() << ", Expected=" << expected_values[j] 
                << ", Original=" << orig_query.value();
        }
    }
    
    // 测试2: 原版创建checkpoint -> V2版本加载
    {
        std::string orig_checkpoint_type = "orig_to_v2_test";
        
        // 先保存原版的期望查询结果 - 只测试窗口内的数据
        std::vector<std::string> expected_values;
        std::vector<uint32_t> valid_indices;
        
        for (uint32_t i = 0U; i < _feas.size(); ++i) {
            FeatureValueProto orig_query = _feas[i];
            if (_original_obj.query(&orig_query)) {
                expected_values.push_back(orig_query.value());
                valid_indices.push_back(i);
            }
        }
        
        // 原版序列化
        {
            std::string type = orig_checkpoint_type;
            DOING_DUMP_VAR(_original_obj);
        }
        
        // V2版本清理并重新初始化
        _v2_obj.uninit();
        ASSERT_TRUE(_v2_obj.init(_conf["valid_distinct"]["feature"][0]));
        
        // V2版本加载原版的checkpoint
        {
            std::string type = orig_checkpoint_type;
            DOING_LOAD_VAR(_v2_obj);
        }
        
        // 验证V2加载原版checkpoint后的查询结果与期望值一致
        for (uint32_t j = 0U; j < valid_indices.size(); ++j) {
            uint32_t i = valid_indices[j];
            FeatureValueProto v2_query = _feas[i];
            ASSERT_TRUE(_v2_obj.query(&v2_query));
            
            EXPECT_EQ(expected_values[j], v2_query.value()) 
                << "Original->V2 checkpoint loading: query result mismatch for view_sign=" 
                << _feas[i].view_sign() << ", Expected=" << expected_values[j] 
                << ", V2=" << v2_query.value();
        }
    }
    
    // 测试3: 加载后继续操作，验证行为一致性
    // 此时原版加载了V2的checkpoint，V2加载了原版的checkpoint，状态应该一致
    {
        // 在两个版本上执行相同的新操作
        std::vector<FeatureValueProto> new_feas;
        for (uint32_t i = 0U; i < 3U; ++i) {
            FeatureValueProto fea;
            fea.set_feature_id(140LU);
            fea.set_view_sign(987654LU);  // 新的view_sign
            fea.set_feature_type(FeatureValueProto::DISTINCT);
            fea.set_joinkey(119LU);
            fea.set_log_id(120LU);  
            fea.set_log_time(1111111111LU);
            fea.set_coord(10L + i);
            fea.set_data_view_sign(888888LU + i);
            new_feas.push_back(fea);
        }
        
        // 在两个版本上执行相同的update操作
        for (const auto& fea : new_feas) {
            bool orig_result = _original_obj.update(fea);
            bool v2_result = _v2_obj.update(fea);
            EXPECT_EQ(orig_result, v2_result) 
                << "Post-load update behavior mismatch";
        }
        
        // 验证新操作后的查询结果一致
        FeatureValueProto new_query;
        new_query.set_feature_id(140LU);
        new_query.set_view_sign(987654LU);
        new_query.set_feature_type(FeatureValueProto::DISTINCT);
        new_query.set_coord(12L);  // 使用最新的coord进行查询
        
        FeatureValueProto orig_new_query = new_query;
        FeatureValueProto v2_new_query = new_query;
        
        ASSERT_TRUE(_original_obj.query(&orig_new_query));
        ASSERT_TRUE(_v2_obj.query(&v2_new_query));
        
        EXPECT_EQ(orig_new_query.value(), v2_new_query.value())
            << "Post-load query behavior mismatch, Original=" << orig_new_query.value()
            << ", V2=" << v2_new_query.value();
    }
    
    _original_obj.uninit();
    _v2_obj.uninit();
}

TEST_F(DistinctAccumulatorCompatibilityTestSuite, test_memory_usage_comparison) {
    ASSERT_TRUE(_original_obj.init(_conf["valid_distinct"]["feature"][0]));
    ASSERT_TRUE(_v2_obj.init(_conf["valid_distinct"]["feature"][0]));
    
    // 创建大量数据来测试内存使用差异
    std::vector<FeatureValueProto> large_dataset;
    for (int i = 0; i < 1000; ++i) {
        FeatureValueProto fea;
        fea.set_feature_id(140LU);
        fea.set_view_sign(123456LU);  // 相同view_sign，测试distinct累积
        fea.set_feature_type(FeatureValueProto::DISTINCT);
        fea.set_coord(1000);  // 相同coord
        fea.set_data_view_sign(700000LU + i);  // 不同的data_view_sign
        large_dataset.push_back(fea);
    }
    
    // 两个版本添加相同数据
    for (const auto& fea : large_dataset) {
        ASSERT_TRUE(_original_obj.update(fea));
        ASSERT_TRUE(_v2_obj.update(fea));
    }
    
    // 验证结果一致
    FeatureValueProto query_fea;
    query_fea.set_feature_id(140LU);
    query_fea.set_view_sign(123456LU);
    query_fea.set_feature_type(FeatureValueProto::DISTINCT);
    query_fea.set_coord(1000);
    
    FeatureValueProto original_query = query_fea;
    FeatureValueProto v2_query = query_fea;
    
    ASSERT_TRUE(_original_obj.query(&original_query));
    ASSERT_TRUE(_v2_obj.query(&v2_query));
    
    EXPECT_EQ(original_query.value(), v2_query.value());
    EXPECT_EQ(1000, boost::lexical_cast<int64_t>(original_query.value()));
    
    // 打印内存使用监控（仅V2版本支持）
    _v2_obj.print_monitor_log();
    
    _original_obj.uninit();
    _v2_obj.uninit();
}

TEST_F(DistinctAccumulatorCompatibilityTestSuite, test_checkpoint_robustness) {
    ASSERT_TRUE(_original_obj.init(_conf["valid_distinct"]["feature"][0]));
    ASSERT_TRUE(_v2_obj.init(_conf["valid_distinct"]["feature"][0]));
    
    // 创建复杂的测试场景：多个view_sign，每个有不同数量的data_view_sign
    std::map<uint64_t, std::vector<uint64_t>> test_data_map;
    
    // view_sign 111111: 5个不同的data_view_sign
    test_data_map[111111LU] = {100001LU, 100002LU, 100003LU, 100004LU, 100005LU};
    
    // view_sign 222222: 3个不同的data_view_sign
    test_data_map[222222LU] = {200001LU, 200002LU, 200003LU};
    
    // view_sign 333333: 1个data_view_sign
    test_data_map[333333LU] = {300001LU};
    
    // 添加测试数据
    for (const auto& pair : test_data_map) {
        uint64_t view_sign = pair.first;
        const auto& data_view_signs = pair.second;
        
        for (uint64_t data_view_sign : data_view_signs) {
            FeatureValueProto fea;
            fea.set_feature_id(140LU);
            fea.set_view_sign(view_sign);
            fea.set_feature_type(FeatureValueProto::DISTINCT);
            fea.set_coord(1000);
            fea.set_data_view_sign(data_view_sign);
            
            ASSERT_TRUE(_original_obj.update(fea));
            ASSERT_TRUE(_v2_obj.update(fea));
        }
    }
    
    // 验证更新后的查询结果
    for (const auto& pair : test_data_map) {
        uint64_t view_sign = pair.first;
        size_t expected_distinct_count = pair.second.size();
        
        FeatureValueProto query_fea;
        query_fea.set_feature_id(140LU);
        query_fea.set_view_sign(view_sign);
        query_fea.set_feature_type(FeatureValueProto::DISTINCT);
        query_fea.set_coord(1000);
        
        compare_query_results(query_fea);
        
        // 验证distinct数量正确
        FeatureValueProto original_result = query_fea;
        ASSERT_TRUE(_original_obj.query(&original_result));
        EXPECT_EQ(expected_distinct_count, boost::lexical_cast<size_t>(original_result.value()));
    }
    
    // 序列化两个版本
    std::string original_type = "robust_original";
    std::string v2_type = "robust_v2";
    
    {
        std::string type = original_type;
        DOING_DUMP_VAR(_original_obj);
    }
    
    {
        std::string type = v2_type;
        DOING_DUMP_VAR(_v2_obj);
    }
    
    // 重新初始化
    _original_obj.uninit();
    _v2_obj.uninit();
    
    ASSERT_TRUE(_original_obj.init(_conf["valid_distinct"]["feature"][0]));
    ASSERT_TRUE(_v2_obj.init(_conf["valid_distinct"]["feature"][0]));
    
    // 加载checkpoint
    {
        std::string type = original_type;
        DOING_LOAD_VAR(_original_obj);
    }
    
    {
        std::string type = v2_type;
        DOING_LOAD_VAR(_v2_obj);
    }
    
    // 验证加载后的查询结果仍然一致
    for (const auto& pair : test_data_map) {
        uint64_t view_sign = pair.first;
        size_t expected_distinct_count = pair.second.size();
        
        FeatureValueProto query_fea;
        query_fea.set_feature_id(140LU);
        query_fea.set_view_sign(view_sign);
        query_fea.set_feature_type(FeatureValueProto::DISTINCT);
        query_fea.set_coord(1000);
        
        compare_query_results(query_fea);
        
        // 再次验证distinct数量
        FeatureValueProto original_result = query_fea;
        ASSERT_TRUE(_original_obj.query(&original_result));
        EXPECT_EQ(expected_distinct_count, boost::lexical_cast<size_t>(original_result.value()));
    }
    
    _original_obj.uninit();
    _v2_obj.uninit();
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */