// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiax<PERSON>(<EMAIL>)
// 
// @File: feature_accumulator_manager_test.cpp
// @Last modified: 2017-10-19 17:40:10
// @Brief: 

#include <gtest/gtest.h>
#include <bmock.h>
#include <gflags/gflags.h>
#include <com_log.h>
#include <archive.h>
#include "feature_accumulator_manager.h"
#include "feature_accumulator_factory.h"

using anti::themis::common_lib::FileArchive;
using ::testing::Return;
using ::testing::_;

namespace anti {
namespace themis {
namespace feature_lib {
typedef FeatureAccumulatorManager::FeatureAccumulatorAndCoord FeatureAccumulatorAndCoord;

class FeatureAccumulatorManagerTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
    }
    virtual void TearDown() {}

private:
    FeatureAccumulatorManager _obj;
};

TEST_F(FeatureAccumulatorManagerTestSuite, construction_case) {
    EXPECT_EQ(0LU, _obj._modify_time);
}

TEST_F(FeatureAccumulatorManagerTestSuite, init_case) {
    ASSERT_FALSE(_obj.init("xxx", "xxx"));
    _obj.uninit();

    ASSERT_TRUE(_obj.init("./conf", "feature_accumulator_manager_test.conf"));
    EXPECT_STREQ("./conf", _obj._conf_path.data());
    EXPECT_STREQ("feature_accumulator_manager_test.conf", _obj._conf_file.data());
    ASSERT_EQ(3U, _obj._acc_coords.size());
    for (uint64_t i = 100LU; i <= 102LU; ++i) {
        EXPECT_TRUE(_obj._acc_coords.find(i) != _obj._acc_coords.end());
    } 
    _obj.uninit();
}

TEST_F(FeatureAccumulatorManagerTestSuite, reload_case) {
    _obj._conf_path = "./conf";
    _obj._conf_file = "xxxxxxx";
    ASSERT_FALSE(_obj.reload());
    _obj.uninit();

    _obj._conf_file = "invalid_feature_accumulator_manager.conf";
    ASSERT_FALSE(_obj.reload());
    _obj.uninit();

    _obj._conf_file = "feature_accumulator_manager_test.conf";
    ASSERT_TRUE(_obj.reload());
    std::string conf = _obj._conf_path + "/" + _obj._conf_file;
    struct stat buf;
    ASSERT_EQ(0, stat(conf.data(), &buf));
    EXPECT_EQ(buf.st_mtime, _obj._modify_time);

    // test reload twice for the same file
    ASSERT_TRUE(_obj.reload());
    EXPECT_EQ(buf.st_mtime, _obj._modify_time);
    
    // test different conf 
    _obj._modify_time = 0;
    
    _obj._conf_file = "feature_accumulator_manager_test_modify.conf";
    ASSERT_TRUE(_obj.reload());
    _obj.uninit();
}

TEST_F(FeatureAccumulatorManagerTestSuite, _reload_case) {
    _obj._conf_path = "./conf";
    _obj._conf_file = "xxxxxxx";
    ASSERT_FALSE(_obj._reload());
    _obj.uninit();
    
    _obj._conf_file = "invalid_feature_accumulator_manager.conf";
    ASSERT_FALSE(_obj._reload());
    _obj.uninit();
    
    _obj._conf_file = "invalid_feature_accumulator_manager1.conf";
    ASSERT_FALSE(_obj._reload());
    _obj.uninit();

    _obj._conf_file = "invalid_feature_accumulator_manager2.conf";
    ASSERT_FALSE(_obj._reload());
    _obj.uninit();

    _obj._acc_coords[0] = std::make_shared<FeatureAccumulatorAndCoord>(FeatureAccumulatorAndCoord());
    _obj._acc_coords[1] = std::make_shared<FeatureAccumulatorAndCoord>(FeatureAccumulatorAndCoord());
    _obj._acc_coords[100] = std::make_shared<FeatureAccumulatorAndCoord>(FeatureAccumulatorAndCoord());
    _obj._conf_file = "feature_accumulator_manager_test.conf";
    _obj._acc_coords[0]->acc_iface.reset();
    _obj._acc_coords[1]->acc_iface.reset(FeatureAccumulatorFactory::create("segment"));
    _obj._acc_coords[100]->acc_iface.reset();
    ASSERT_TRUE(_obj._reload());
    ASSERT_EQ(3U, _obj._acc_coords.size());
    for (uint64_t i = 100LU; i <= 102LU; ++i) {
        EXPECT_TRUE(_obj._acc_coords.find(i) != _obj._acc_coords.end());
    } 
    EXPECT_TRUE(_obj._acc_coords[100]->acc_iface.get() == NULL);
    _obj.uninit();

    _obj._conf_file = "acc_mgr_multi_line_test.conf";
    _obj._acc_coords[0] = std::make_shared<FeatureAccumulatorAndCoord>(FeatureAccumulatorAndCoord());
    _obj._acc_coords[1] = std::make_shared<FeatureAccumulatorAndCoord>(FeatureAccumulatorAndCoord());
    _obj._acc_coords[100] = std::make_shared<FeatureAccumulatorAndCoord>(FeatureAccumulatorAndCoord());
    _obj._acc_coords[0]->acc_iface.reset();
    _obj._acc_coords[1]->acc_iface.reset(FeatureAccumulatorFactory::create("segment"));
    _obj._acc_coords[100]->acc_iface.reset();
    ASSERT_TRUE(_obj._reload());
    ASSERT_EQ(3U, _obj._acc_coords.size());
    for (uint64_t i = 100LU; i <= 102LU; ++i) {
        EXPECT_TRUE(_obj._acc_coords.find(i) != _obj._acc_coords.end());
    } 
    EXPECT_TRUE(_obj._acc_coords[100]->acc_iface.get() == NULL);
    _obj.uninit();

    _obj._conf_file = "acc_mgr_cmp_test.conf";
    _obj._acc_coords[0] = std::make_shared<FeatureAccumulatorAndCoord>(FeatureAccumulatorAndCoord());
    _obj._acc_coords[1] = std::make_shared<FeatureAccumulatorAndCoord>(FeatureAccumulatorAndCoord());
    _obj._acc_coords[100] = std::make_shared<FeatureAccumulatorAndCoord>(FeatureAccumulatorAndCoord());
    _obj._acc_coords[0]->acc_iface.reset();
    _obj._acc_coords[1]->acc_iface.reset(FeatureAccumulatorFactory::create("segment"));
    _obj._acc_coords[100]->acc_iface.reset();
    ASSERT_TRUE(_obj._reload());
    ASSERT_EQ(3U, _obj._acc_coords.size());
    for (uint64_t i = 100LU; i <= 102LU; ++i) {
        EXPECT_TRUE(_obj._acc_coords.find(i) != _obj._acc_coords.end());
    } 
    EXPECT_TRUE(_obj._acc_coords[100]->acc_iface.get() == NULL);
    _obj.uninit();
}

TEST_F(FeatureAccumulatorManagerTestSuite, update_and_query_by_invalid_input_case) {
    ASSERT_FALSE(_obj.query(NULL));
    ASSERT_FALSE(_obj.update_and_query(NULL));

    FeatureValueProto fea;
    fea.set_feature_id(0LU);
    ASSERT_FALSE(_obj.query(&fea));
    ASSERT_FALSE(_obj.update_and_query(&fea));
    ASSERT_FALSE(_obj.update(fea));
}

TEST_F(FeatureAccumulatorManagerTestSuite, update_and_query_by_input_pv_coord_fea_case) {
    ASSERT_TRUE(_obj.init("./conf", "feature_accumulator_manager_test.conf"));
    _obj._acc_coords[100]->coord = 1L;
    FeatureValueProto fea;
    fea.set_feature_id(100LU);
    fea.set_view_sign(123456LU);
    fea.set_feature_type(FeatureValueProto::SEGMENT);
    fea.set_joinkey(119LU);
    fea.set_log_id(120LU);
    fea.set_log_time(1111111111LU);
    fea.set_seg_type(FeatureValueProto::PV);
    fea.set_coord(0L);

    ASSERT_TRUE(_obj.update(fea));
    ASSERT_TRUE(_obj.query(&fea));
    EXPECT_EQ(1U, fea.cur_seg_count());
    EXPECT_EQ(0U, fea.last_seg_count());
    EXPECT_EQ(1L, fea.coord());
    // pv _coords remain unchanged
    EXPECT_EQ(1L, _obj._acc_coords[100]->coord);

    // fea.coord is 1L now
    ASSERT_TRUE(_obj.update_and_query(&fea));
    EXPECT_EQ(2U, fea.cur_seg_count());
    EXPECT_EQ(1U, fea.last_seg_count());
    EXPECT_EQ(2L, fea.coord());
    EXPECT_EQ(1L, _obj._acc_coords[100]->coord);
    _obj.uninit();
}

TEST_F(FeatureAccumulatorManagerTestSuite, update_and_query_by_input_time_coord_fea_case) {
    ASSERT_TRUE(_obj.init("./conf", "feature_accumulator_manager_test.conf"));
    _obj._acc_coords[100]->coord = 1L;
    FeatureValueProto fea;
    fea.set_feature_id(100LU);
    fea.set_view_sign(123456LU);
    fea.set_feature_type(FeatureValueProto::SEGMENT);
    fea.set_joinkey(119LU);
    fea.set_log_id(120LU);
    fea.set_log_time(111LU);
    fea.set_seg_type(FeatureValueProto::TIME);
    fea.set_coord(111L);

    ASSERT_TRUE(_obj.update(fea));
    ASSERT_TRUE(_obj.query(&fea));
    EXPECT_EQ(1U, fea.cur_seg_count());
    EXPECT_EQ(0U, fea.last_seg_count());
    EXPECT_EQ(111L, fea.coord());

    ASSERT_TRUE(_obj.update_and_query(&fea));
    EXPECT_EQ(2U, fea.cur_seg_count());
    EXPECT_EQ(0U, fea.last_seg_count());
    // time fea _coords changed
    EXPECT_EQ(111L, fea.coord());
    EXPECT_EQ(111L, _obj._acc_coords[100LU]->coord);
    _obj.uninit();
}

TEST_F(FeatureAccumulatorManagerTestSuite, increase_coords_case) {
    std::vector<std::pair<uint64_t, int64_t> > coords = {{100LU, 2L}, {110LU, 3L}};

    _obj.increase_coords(coords);
    ASSERT_EQ(0U, _obj._acc_coords.size());

    _obj.increase_coords(coords);
    ASSERT_EQ(0U, _obj._acc_coords.size());
}

class MockAcc : public FeatureAccumulatorInterface {
public:
    MockAcc() {}
    virtual ~MockAcc() {}
    
    virtual uint64_t feature_id() const { return 0; }
    virtual bool init(const comcfg::ConfigUnit& conf) {return true; }
    virtual void uninit() {} 
    virtual bool update(const FeatureValueProto& fea) { return true; }
    virtual bool update_and_query(FeatureValueProto* fea) { return true; }
    virtual bool query(FeatureValueProto* fea) const { return true; }
    virtual bool load(PosixIoReaderInterface *reader) { return true; }
    virtual bool dump(PosixIoWriterInterface *writer) const { return true; }
    virtual bool query(
            const FValQRequestProto& request,
            FValQResponseProto* response) const {
        return _flag;
    }

private:
    bool _flag;
};

TEST_F(FeatureAccumulatorManagerTestSuite, query_ext_fea_case) {
    // case: query invalid input
    FValQRequestProto request;
    ASSERT_FALSE(_obj.query(request, NULL));
    // case: feature_id in ext not found case
    FValQResponseProto response;
    request.set_feature_id(888);
    ASSERT_TRUE(_obj.query(request, &response));

    std::shared_ptr<MockAcc> mock_acc(
            new (std::nothrow) MockAcc());
    ASSERT_FALSE(!mock_acc);
    // case: fail, acc query fail
    _obj._acc_coords[888] = std::make_shared<FeatureAccumulatorAndCoord>();
    _obj._acc_coords[888]->acc_iface = mock_acc;
    mock_acc->_flag = false;
    ASSERT_FALSE(_obj.query(request, &response));
    // case: succ
    mock_acc->_flag = true;
    ASSERT_TRUE(_obj.query(request, &response));
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

