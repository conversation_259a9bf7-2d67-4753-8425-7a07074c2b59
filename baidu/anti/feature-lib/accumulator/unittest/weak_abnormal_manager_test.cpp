// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: ch<PERSON><PERSON><PERSON><PERSON>(ch<PERSON><PERSON><PERSON><PERSON>@baidu.com)
// 
// @File: weak_anomaly_manager_test.cpp
// @Last modified: 2017-12-27 14:53:31
// @Brief: 

#include <gtest/gtest.h>
#include <memory>
#include "ckpt_test_util.h"
#include "weak_anomaly_manager.h"

namespace anti {
namespace themis {
namespace feature_lib {

class WeakAnomalySeqTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {}
    virtual void TearDown () {}
    void add_abs(std::vector<uint32_t> list) {
        for (const auto& i : list) {
            bool ab = i != 0 ? true : false;
            _obj.add(ab);
        }
        return;
    }
private:
    WeakAnomalySeq _obj;
};

class WeakAnomalyManagerTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
        ASSERT_EQ(0, _conf.load("./conf/", "weak_abnormal_manager.conf"));
        int64_t coords[] = {0L, 1L, 3L, 4L, 5L, 8L};
        std::string values[] = {"1.1", "2.0", "0.5", "0,5", "2.0", "2.0"};
        for (uint32_t i = 0; i < 6; ++i) {
            FeatureValueProto fea;
            fea.set_feature_id(101LU);
            fea.set_view_sign(123456LU);
            fea.set_feature_type(FeatureValueProto::DISTRIBUTION);
            fea.set_joinkey(119LU);
            fea.set_log_id(120LU);
            fea.set_log_time(1111111111LU);
            fea.set_coord(coords[i]);
            fea.set_bucket_idx(0L);
            fea.set_value(values[i]);
            fea.set_in_anomaly_care(true);
            _feas.push_back(fea);
        }
    }
    virtual void TearDown() {}
    void set_fea(
            const std::string& value, 
            int64_t coord,
            FeatureValueProto* fea) {
        fea->set_feature_id(101LU);
        fea->set_view_sign(123456LU);
        fea->set_feature_type(FeatureValueProto::DISTRIBUTION);
        fea->set_joinkey(119LU);
        fea->set_log_id(120LU);
        fea->set_log_time(1111111111LU);
        fea->set_coord(coord);
        fea->set_bucket_idx(0L);
        fea->set_value(value);       
        fea->set_valid(true);
        fea->set_in_anomaly_care(true);
    }
    void set_feas(
            const std::vector<std::string>& strs,
            std::vector<int64_t> coords,
            std::vector<FeatureValueProto>* feas) {
        ASSERT_EQ(strs.size(), coords.size());
        feas->clear();
        for (uint32_t i = 0; i < strs.size(); ++i) {
            FeatureValueProto fea;
            set_fea(strs[i], coords[i], &fea);
            feas->push_back(fea);
        }
        return;
    }
    void set_feas(
            const std::vector<double>& vs,
            std::vector<int64_t> coords,
            std::vector<FeatureValueProto>* feas) {
        ASSERT_EQ(vs.size(), coords.size());
        feas->clear();
        for (uint32_t i = 0; i < vs.size(); ++i) {
            FeatureValueProto fea;
            set_fea(std::to_string(vs[i]), coords[i], &fea);
            feas->push_back(fea);
        }
        return;
    }
    void check(uint64_t view_sign, double e, double s_ratio, double l_ratio) {
        auto node_ptr = _obj._ab_window->find(view_sign);
        ASSERT_FALSE(!node_ptr);
        auto lru_node = node_ptr->value();
        EXPECT_EQ(e, lru_node.get_e());
        EXPECT_EQ(s_ratio, lru_node.get_seq()->ratio());
        auto p_l_item = _obj._l_window->query_segment(view_sign); 
        ASSERT_TRUE(p_l_item);
        EXPECT_EQ(l_ratio, static_cast<double>(p_l_item->first()) / p_l_item->second());
        return;
    }

private:
    comcfg::Configure _conf;
    WeakAnomalyManager _obj;
    std::vector<FeatureValueProto> _feas;
};

TEST_F(WeakAnomalySeqTestSuite, case_test) {
    ASSERT_FALSE(_obj.set_size(64));
    ASSERT_TRUE(_obj.set_size(8));
    ASSERT_EQ(8, _obj._seq_size);
    _obj.add(true);
    ASSERT_EQ(0x1, _obj._seq);
    ASSERT_EQ(1.0 / 1, _obj.ratio());
    _obj.add(false);
    ASSERT_EQ(0x2, _obj._seq);
    ASSERT_EQ(1.0 / 2, _obj.ratio());
    {
        _obj.reset();
        std::vector<uint32_t> list = {0, 0, 0, 0, 0, 0, 0, 0, 0};
        add_abs(list);
        ASSERT_EQ(0x0, _obj._seq);
        ASSERT_EQ(0.0, _obj.ratio());
    }
    {
        _obj.reset();
        std::vector<uint32_t> list = {1, 1, 1, 1, 1, 1, 1, 1, 1};
        add_abs(list);
        ASSERT_EQ(0x1FF, _obj._seq);
        ASSERT_EQ(1.0, _obj.ratio());
    }
    {
        _obj.reset();
        std::vector<uint32_t> list = {0, 0, 0, 1, 1, 1, 1, 1, 1};
        add_abs(list);
        ASSERT_EQ(0x3F, _obj._seq);
        ASSERT_EQ(0.75, _obj.ratio());
    }
    {
        std::vector<uint32_t> list;
        for (uint32_t i = 0; i < 100; ++i) {
            list.push_back(true);
        }
        for (uint32_t i = 0; i < 4; ++i) {
            list.push_back(false);
        }
        add_abs(list);
        ASSERT_EQ(0xFFFFFFFFFFFFFFF0, _obj._seq);
        ASSERT_EQ(0.5, _obj.ratio());
    }
}

TEST_F(WeakAnomalyManagerTestSuite, init_test) {
    ASSERT_FALSE(_obj.init(_conf["empty"]));
    ASSERT_FALSE(_obj.init(_conf["segment"]));
    ASSERT_FALSE(_obj.init(_conf["no_window"]));
    ASSERT_FALSE(_obj.init(_conf["big_incr_step"]));
    ASSERT_TRUE(_obj.init(_conf["valid"]));
}

TEST_F(WeakAnomalyManagerTestSuite, update_fea_case) {
    ASSERT_TRUE(_obj.init(_conf["valid"]));
    ASSERT_FALSE(_obj.query(NULL));
    FeatureValueProto fea;
    set_fea("1.1", 0L, &fea);
    EXPECT_FALSE(_obj.query(&fea));
    // update new node
    EXPECT_TRUE(_obj.update(fea));
    auto ab_node = _obj._weak_ab_node;
    EXPECT_EQ(_obj._e_base, ab_node.get_e());
    auto node_ptr = _obj._ab_window->find(fea.view_sign());
    ASSERT_FALSE(!node_ptr);
    WeakAnomalyNode lru_node = node_ptr->value();
    EXPECT_EQ(_obj._e_base, lru_node.get_e());

    set_fea("0.8", 1L, &fea);
    EXPECT_TRUE(_obj.update(fea));
    ab_node = _obj._weak_ab_node;
    EXPECT_EQ(0.125, ab_node.get_e());
    node_ptr = _obj._ab_window->find(fea.view_sign());
    ASSERT_FALSE(!node_ptr);
    lru_node = node_ptr->value();
    EXPECT_EQ(0.125, lru_node.get_e());
}

TEST_F(WeakAnomalyManagerTestSuite, update_feas_case) {
    ASSERT_TRUE(_obj.init(_conf["valid"]));
    std::vector<double> vs = {2.0, 2.0, 0.5, 2.0, 2.0, 2.0, 0.5, 2.0, 2.0};
    std::vector<int64_t> coords = {0L, 1L, 3L, 4L, 5L, 7L, 7L, 7L, 7L};
    std::vector<FeatureValueProto> feas;
    set_feas(vs, coords, &feas);
    for (const auto& fea : feas) {
        EXPECT_TRUE(_obj.update(fea));
    }
    FeatureValueProto fea;
    set_fea("2.0", 7L, &fea);
    auto node_ptr = _obj._ab_window->find(fea.view_sign());
    ASSERT_FALSE(!node_ptr);
    auto lru_node = node_ptr->value();
    EXPECT_EQ(5.0 / 8 + 0.125 * 2, lru_node.get_e());
    auto p_l_item = _obj._l_window->query_segment(fea.view_sign()); 
    ASSERT_TRUE(p_l_item);
    EXPECT_EQ(7, p_l_item->first());
    EXPECT_EQ(9, p_l_item->second());
    
    std::vector<double> vs2 = {2.0, 2.0};
    std::vector<int64_t> coords2 = {8L, 9L};
    set_feas(vs2, coords2, &feas);
    for (const auto& fea : feas) {
        EXPECT_TRUE(_obj.update(fea));
    }
    set_fea("2.0", 9L, &fea);
    node_ptr = _obj._ab_window->find(fea.view_sign());
    ASSERT_FALSE(!node_ptr);
    lru_node = node_ptr->value();
    EXPECT_EQ(5.0 / 8 + 0.125 * 4, lru_node.get_e());
    p_l_item = _obj._l_window->query_segment(fea.view_sign()); 
    ASSERT_TRUE(p_l_item);
    EXPECT_EQ(7, p_l_item->first());
    EXPECT_EQ(9, p_l_item->second());

    set_fea("0.5", 9L, &fea);
    EXPECT_TRUE(_obj.update(fea));
    node_ptr = _obj._ab_window->find(fea.view_sign());
    ASSERT_FALSE(!node_ptr);
    lru_node = node_ptr->value();
    EXPECT_EQ(std::max(7.0 / 10, 6.0 / 8.0), lru_node.get_e());
    p_l_item = _obj._l_window->query_segment(fea.view_sign()); 
    ASSERT_TRUE(p_l_item);
    EXPECT_EQ(7, p_l_item->first());
    EXPECT_EQ(10, p_l_item->second());
}

TEST_F(WeakAnomalyManagerTestSuite, update_feas_case2) {
    ASSERT_TRUE(_obj.init(_conf["valid"]));
    std::vector<double> vs;
    std::vector<int64_t> coords;
    for (uint32_t i = 0; i < 1000; ++i) {
        vs.push_back(0.5);
        coords.push_back(1L);
    }
    std::vector<FeatureValueProto> feas;
    set_feas(vs, coords, &feas);
    for (const auto& fea : feas) {
        EXPECT_TRUE(_obj.update(fea));
        check(fea.view_sign(), 0.0, 0.0, 0.0);
    }
    for (uint32_t i = 0; i < 8; ++i) {
        FeatureValueProto fea;
        set_fea("2.0", 1L, &fea);
        EXPECT_TRUE(_obj.update(fea)); 
        check(fea.view_sign(), 1.0 / 8 * (i + 1), 1.0 / 8 * (i + 1), 
                static_cast<double>(i + 1) / (1000 + i + 1));
    }
}

TEST_F(WeakAnomalyManagerTestSuite, query_case) {
    ASSERT_TRUE(_obj.init(_conf["valid"]));
    std::vector<double> vs = {2.0, 2.0, 0.5, 2.0, 2.0, 2.0, 0.5, 2.0};
    std::vector<int64_t> coords = {0L, 1L, 3L, 4L, 5L, 7L, 7L, 7L};
    std::vector<FeatureValueProto> feas;
    set_feas(vs, coords, &feas);
    for (const auto& fea : feas) {
        EXPECT_TRUE(_obj.update(fea));
        FeatureValueProto out;
        set_fea("0.0", 7L, &out);
        EXPECT_TRUE(_obj.query(&out));
        EXPECT_EQ(out.feature_type(), FeatureValueProto::WEAK_ANOMALY);
        EXPECT_FALSE(out.valid());
    }
    FeatureValueProto fea;
    set_fea("2.0", 7L, &fea);
    EXPECT_TRUE(_obj.update(fea));
    FeatureValueProto out; 
    set_fea("0.0", 7L, &out);
    EXPECT_TRUE(_obj.query(&out));
    EXPECT_EQ(out.feature_type(), FeatureValueProto::WEAK_ANOMALY);
    EXPECT_TRUE(out.valid());
}

TEST_F(WeakAnomalyManagerTestSuite, ckpt_case) {
    ASSERT_TRUE(_obj.init(_conf["valid"]));
    std::vector<double> vs = {2.0, 2.0, 0.5, 2.0, 2.0, 2.0, 0.5, 2.0};
    std::vector<int64_t> coords = {0L, 1L, 3L, 4L, 5L, 7L, 7L, 7L};
    std::vector<FeatureValueProto> feas;
    set_feas(vs, coords, &feas);
    for (const auto& fea : feas) {
        EXPECT_TRUE(_obj.update(fea));
        FeatureValueProto out;
        set_fea("0.0", 7L, &out);
        EXPECT_TRUE(_obj.query(&out));
        EXPECT_EQ(out.feature_type(), FeatureValueProto::WEAK_ANOMALY);
        EXPECT_FALSE(out.valid());
    }
    FeatureValueProto fea;
    set_fea("2.0", 7L, &fea);
    EXPECT_TRUE(_obj.update(fea));
    DOING_DUMP();
    _obj.uninit();
    ASSERT_TRUE(_obj.init(_conf["valid"]));
    DOING_LOAD();
    FeatureValueProto out; 
    set_fea("0.0", 7L, &out);
    EXPECT_TRUE(_obj.query(&out));
    EXPECT_EQ(out.feature_type(), FeatureValueProto::WEAK_ANOMALY);
    EXPECT_TRUE(out.valid());
    CWARNING_LOG("fea value[%s]", fea.value().c_str());
    auto node_ad = _obj._ab_window->find(fea.view_sign());
    EXPECT_EQ(7.0 / 8, node_ad->value()._e);
    EXPECT_EQ(0xBB, node_ad->value()._seq._get_seq());
    EXPECT_EQ(6.0 / 8, node_ad->value()._seq.ratio());
}

} // feature_lib
} // themis
} // anti



/* vim: set ts=4 sw=4: */

