// Copyright (c) 2021 Baidu.com, Inc. All Rights Reserved
// @Author: longfei(<EMAIL>)
// 

#include <vector>
#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <bmock.h>
#include "nlpc_client_manager.h"
#include "lru_node_feature_accumulator.hpp"
#include "posix_themis_io.h" 
#include "ckpt_test_util.h"
#include "fea_value_dis_accumulator.h"
#include <boost/lexical_cast.hpp>

using ::testing::_;
using ::testing::Return;
using ::testing::DoAll;
using ::testing::SetArgPointee;
using ::testing::SetArgPointee;
using ::testing::SetArgReferee;
using ::testing::SetArgumentPointee;

namespace anti {
namespace themis {
namespace feature_lib {
/*
BMOCK_NS_CLASS_METHOD2(anti::themis::feature_lib, QueryNode,
        calc, bool(QueryNode&, std::string*));

class LRUNodeFeatureAccumulatorTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
        ASSERT_TRUE(_conf.load("./conf", "accumulator_test.conf") == 0);

        std::string query[] = {"百度", "百度搜索", "自动驾驶", "李彦宏", "百度百科"};
        uint64_t times[] = {1623057650UL, 1623057651UL, 1623057652UL, 1623057653UL, 1623057654UL};
        uint64_t view_signs[] = {110UL, 111UL, 112UL, 113UL, 114UL};
        for (uint32_t i = 0U; i < 5U; ++i) {
            FeatureValueProto fea;
            fea.set_feature_id(100UL);
            fea.set_view_sign(view_signs[i]);
            fea.set_feature_type(FeatureValueProto::QUERY_SIMILAR);
            fea.set_joinkey(119UL);
            fea.set_log_id(120UL);
            fea.set_log_time(times[i]);
            fea.set_coord(times[i]);
            fea.set_data_view_value(query[i]);
            _feas.push_back(fea);
        }
        _old_fea = _feas[0];
        _old_fea.set_coord(0L);

        FeatureValueProto fea;
        fea.set_feature_id(123456789UL);
        _inv_feas.push_back(fea);

        fea.set_feature_id(100UL);
        fea.set_feature_type(FeatureValueProto::RATIO);
        _inv_feas.push_back(fea);
    }

    virtual void TearDown() {
        _feas.clear();
        _inv_feas.clear();
    }

private:
    LRUNodeFeatureAccumulator<QueryNode> _obj;
    comcfg::Configure _conf;

    std::vector<FeatureValueProto> _feas;
    FeatureValueProto _old_fea;
    std::vector<FeatureValueProto> _inv_feas;
};
*/
/*
 * gtest conflict to some nlpc servers, such as nlpc-ernie-sim-light
TEST_F(LRUNodeFeatureAccumulatorTestSuite, init_nlpc_case) {
    auto& nlpc_client = common_lib::NLPCClientManagerSingleton::instance();
    ASSERT_TRUE(nlpc_client.init("./conf", "nlpc_manager.conf"));
}
*/
/*
TEST_F(LRUNodeFeatureAccumulatorTestSuite, construction_case) {
    EXPECT_EQ(0UL, _obj.feature_id());
    EXPECT_EQ(0UL, _obj._version);
    EXPECT_EQ(0UL, _obj._window_len);
    _obj.uninit();
}


TEST_F(LRUNodeFeatureAccumulatorTestSuite, init_case) {
    uint32_t num = _conf["invalid_similar"]["feature"].size();
    ASSERT_LE(3U, num);
    for (uint32_t i = 0U; i < num; ++i) {
        EXPECT_FALSE(_obj.init(_conf["invalid_similar"]["feature"][i]));
        _obj.uninit();
    }

    num = _conf["valid_similar"]["feature"].size();
    ASSERT_LE(1, num);

    for (uint32_t i = 0U; i < num; ++i) {
        ASSERT_TRUE(_obj.init(_conf["valid_similar"]["feature"][i]));
        EXPECT_EQ(100UL, _obj.feature_id());
        EXPECT_EQ(2UL, _obj._version);
        _obj.uninit();
    }
}

//list  head <-> tail
//------------------------------------------------------------------------
//coord     |  1623057654  1623057653  1623057652  1623057651  1623057650
//------------------------------------------------------------------------
//view_sign |      114         113         112         111         110
//------------------------------------------------------------------------

TEST_F(LRUNodeFeatureAccumulatorTestSuite, one_update_and_one_query_case) {
    ASSERT_TRUE(_obj.init(_conf["valid_similar"]["feature"][0]));

    // invalid fea
    EXPECT_FALSE(_obj.query(NULL));
    for (uint32_t i = 0U; i< _inv_feas.size(); ++i) {
        EXPECT_FALSE(_obj.update(_inv_feas[i]));
        EXPECT_FALSE(_obj.query(&_inv_feas[i]));
    }

    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(_feas[i]));
    }

    std::string ret1 = "1.000000";
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(anti::themis::feature_lib, QueryNode,
            calc), calc(_, _))
            .WillOnce(DoAll(SetArgumentPointee<1>(ret1), Return(true)))
            .WillOnce(DoAll(SetArgumentPointee<1>(ret1), Return(true)))
            .WillOnce(DoAll(SetArgumentPointee<1>(ret1), Return(true)))
            .WillOnce(DoAll(SetArgumentPointee<1>(ret1), Return(true)))
            .WillOnce(DoAll(SetArgumentPointee<1>(ret1), Return(true)))
            .WillOnce(DoAll(SetArgumentPointee<1>(ret1), Return(true)))
            .WillOnce(DoAll(SetArgumentPointee<1>(ret1), Return(true)));

    FeatureValueProto fea, ret_fea;
    //query and find
    ret_fea = _feas[0];
    ASSERT_TRUE(_obj.query(&ret_fea));
    CWARNING_LOG("query[%s:%s], similar:%s", _feas[0].data_view_value().c_str(),
            ret_fea.data_view_value().c_str(), ret_fea.value().c_str());

    //query and find
    ret_fea = _feas[4];
    ret_fea.set_data_view_value("百度");
    ASSERT_TRUE(_obj.query(&ret_fea));
    CWARNING_LOG("query[%s:%s], similar:%s", _feas[4].data_view_value().c_str(),
            ret_fea.data_view_value().c_str(), ret_fea.value().c_str());

    //query and not find, not call _cal_query_similar_value
    ret_fea.set_view_sign(6666UL);
    ASSERT_TRUE(_obj.query(&ret_fea));
    CWARNING_LOG("query[%s], similar:%s", ret_fea.data_view_value().c_str(),
            ret_fea.value().c_str());
    EXPECT_EQ(0, strcmp("-1", ret_fea.value().c_str()));
    EXPECT_EQ(false, ret_fea.valid());

    //update without gc && find view_sign
    fea = _feas[0];
    fea.set_data_view_value("百度贴吧");
    ASSERT_TRUE(_obj.update(fea));

    //------------------------------------------------------------------------
    //coord     |  1623057650   1623057654  1623057653  1623057652  16230576511
    //------------------------------------------------------------------------
    //view_sign |      110          114         113         112         111
    //------------------------------------------------------------------------
    ret_fea = fea;
    ret_fea.set_value("");
    ASSERT_TRUE(_obj.query(&ret_fea));
    CWARNING_LOG("after update, query[%s], similar:%s", ret_fea.data_view_value().c_str(),
            ret_fea.value().c_str());
    EXPECT_EQ(0, strcmp("1.000000", ret_fea.value().c_str()));


    auto node_list = _obj._window->_window._node_list;
    for (auto it : node_list) {
        CWARNING_LOG("node view_sign[%d], query[%s], coord[%d]", it->key(), it->value().c_str(), it->coord());
    }
    auto list_front = node_list.front();
    CWARNING_LOG("front: coord[%d], query[%s], view_sign[%d]", list_front->coord(), list_front->value().c_str(), list_front->key()); 
    ASSERT_EQ(5, node_list.size());
    ASSERT_EQ(1623057650, list_front->coord());
    ASSERT_EQ("百度贴吧", list_front->value());
    ASSERT_EQ(110, list_front->key());

    //update without gc && not find view_sign
    fea = _feas[0];
    fea.set_data_view_value("百度云盘");
    fea.set_view_sign(120UL);
    ASSERT_TRUE(_obj.update(fea));

    //--------------------------------------------------------------------------------------
    //coord     |   1623057650  1623057650   1623057654  1623057653  1623057652  16230576510
    //--------------------------------------------------------------------------------------
    //view_sign |      120         110          114         113         112         111
    //--------------------------------------------------------------------------------------
    ret_fea = fea;
    ret_fea.set_value("");
    ASSERT_TRUE(_obj.query(&ret_fea));
    CWARNING_LOG("after update, query[%s], similar:%s", ret_fea.data_view_value().c_str(),
            ret_fea.value().c_str());
    EXPECT_EQ(0, strcmp("1.000000", ret_fea.value().c_str()));

    node_list = _obj._window->_window._node_list;
    list_front = node_list.front();
    ASSERT_EQ(6, node_list.size());
    ASSERT_EQ(1623057650, list_front->coord());
    ASSERT_EQ("百度云盘", list_front->value());
    ASSERT_EQ(120, list_front->key());

    //update with gc && find view_sign
    fea = _feas[0];
    fea.set_log_time(1623057657UL);
    fea.set_coord(1623057657UL);
    fea.set_data_view_value("百度知道");
    ASSERT_TRUE(_obj.update(fea));

    //                                                                          |    gc
    //--------------------------------------------------------------------------|------------
    //coord     |   1623057657  1623057650   1623057654  1623057653  1623057652 | 16230576510
    //--------------------------------------------------------------------------|------------
    //view_sign |      110         120          114         113         112     |    111
    //--------------------------------------------------------------------------|------------
    ret_fea = fea;
    ASSERT_TRUE(_obj.query(&ret_fea));
    CWARNING_LOG("after update, query[%s], similar:%s", ret_fea.data_view_value().c_str(),
            ret_fea.value().c_str());
    EXPECT_EQ(0, strcmp("1.000000", ret_fea.value().c_str()));

    //@ update view_sign 110, new coord 1623057657UL
    ret_fea.set_view_sign(110UL);
    ret_fea.set_value("");
    ASSERT_TRUE(_obj.query(&ret_fea));
    CWARNING_LOG("after gc, similar:%s", ret_fea.value().c_str(),
            ret_fea.value().c_str());
    EXPECT_EQ(0, strcmp("1.000000", ret_fea.value().c_str()));

    //@ gc view_sign 111, not call _cal_query_similar_value
    ret_fea.set_view_sign(111UL);
    ret_fea.set_value("");
    ASSERT_TRUE(_obj.query(&ret_fea));
    CWARNING_LOG("after gc, similar:%s", ret_fea.value().c_str(),
            ret_fea.value().c_str());
    EXPECT_EQ(0, strcmp("-1", ret_fea.value().c_str()));

    node_list = _obj._window->_window._node_list;
    list_front = node_list.front();
    ASSERT_EQ(5, node_list.size());
    ASSERT_EQ(1623057657, list_front->coord());
    ASSERT_EQ("百度知道", list_front->value());
    ASSERT_EQ(110, list_front->key());

    for (auto it : node_list) {
        ASSERT_TRUE(it->key() != 111);
    }


    //update with gc && not find view_sign
    fea = _feas[0];
    fea.set_log_time(1623057658UL);
    fea.set_coord(1623057658UL);
    fea.set_view_sign(121UL);
    fea.set_data_view_value("百度文库");
    ASSERT_TRUE(_obj.update(fea));

    //                                                                            |    gc
    //----------------------------------------------------------------------------|------------
    //coord     |   1623057658   1623057657  1623057650   1623057654  1623057653  | 16230576512
    //----------------------------------------------------------------------------|------------
    //view_sign |      121          110         120          114         113      |    112
    //----------------------------------------------------------------------------|------------
    ret_fea = fea;
    ASSERT_TRUE(_obj.query(&ret_fea));
    CWARNING_LOG("after update, query[%s], similar:%s", ret_fea.value().c_str(),
            ret_fea.value().c_str());
    EXPECT_EQ(0, strcmp("1.000000", ret_fea.value().c_str()));

    ret_fea.set_view_sign(112UL);
    ret_fea.set_value("");
    ASSERT_TRUE(_obj.query(&ret_fea));
    CWARNING_LOG("after gc, similar:%s", ret_fea.value().c_str());
    EXPECT_EQ(0, strcmp("-1", ret_fea.value().c_str()));
    EXPECT_EQ(false, ret_fea.valid());

    node_list = _obj._window->_window._node_list;
    list_front = node_list.front();
    ASSERT_EQ(5, node_list.size());
    ASSERT_EQ(1623057658, list_front->coord());
    ASSERT_EQ("百度文库", list_front->value());
    ASSERT_EQ(121, list_front->key());

    for (auto it : node_list) {
        ASSERT_TRUE(it->key() != 112);
    }

    _obj.uninit();
}

//list  head <-> tail
//------------------------------------------------------------------------
//coord     |  1623057654  1623057653  1623057652  1623057651  1623057650
//------------------------------------------------------------------------
//view_sign |      114         113         112         111         110
//------------------------------------------------------------------------
TEST_F(LRUNodeFeatureAccumulatorTestSuite, update_and_query_case) {
    ASSERT_TRUE(_obj.init(_conf["valid_similar"]["feature"][0]));
    // invalid fea
    EXPECT_FALSE(_obj.query(NULL));
    for (uint32_t i = 0U; i< _inv_feas.size(); ++i) {
        EXPECT_FALSE(_obj.update(_inv_feas[i]));
        EXPECT_FALSE(_obj.query(&_inv_feas[i]));
    }
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(_feas[i]));
    }

    std::string ret1 = "1.000000";
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(anti::themis::feature_lib, QueryNode,
            calc), calc(_, _))
            .WillOnce(DoAll(SetArgumentPointee<1>(ret1), Return(true)))
            .WillOnce(DoAll(SetArgumentPointee<1>(ret1), Return(true)))
            .WillOnce(DoAll(SetArgumentPointee<1>(ret1), Return(true)))
            .WillOnce(DoAll(SetArgumentPointee<1>(ret1), Return(true)))
            .WillOnce(DoAll(SetArgumentPointee<1>(ret1), Return(true)))
            .WillOnce(DoAll(SetArgumentPointee<1>(ret1), Return(true)));

    FeatureValueProto fea, ret_fea;
    //update_and_query without gc and find view_sign
    fea = _feas[0];
    fea.set_data_view_value("百度搜索");
    ASSERT_TRUE(_obj.update_and_query(&fea));
    CWARNING_LOG("query[%s:%s], similar:%s", _feas[0].data_view_value().c_str(),
            fea.data_view_value().c_str(), fea.value().c_str());

    //------------------------------------------------------------------------
    //coord     |  1623057650   1623057654  1623057653  1623057652  16230576510
    //------------------------------------------------------------------------
    //view_sign |      110          114         113         112         111
    //------------------------------------------------------------------------
    ret_fea = fea;
    ret_fea.set_value("");
    ASSERT_TRUE(_obj.query(&ret_fea));
    CWARNING_LOG("after update_and_query, query[%s], similar:%s", ret_fea.data_view_value().c_str(),
            ret_fea.value().c_str());
    EXPECT_EQ(0, strcmp("1.000000", ret_fea.value().c_str()));

    auto node_list = _obj._window->_window._node_list;
    auto list_front = node_list.front();
    ASSERT_EQ(5, node_list.size());
    ASSERT_EQ(1623057650, list_front->coord());
    ASSERT_EQ("百度搜索", list_front->value());
    ASSERT_EQ(110, list_front->key());

    //update_and_query without gc and not find view_sign
    fea = _feas[0];
    fea.set_data_view_value("百度知道");
    fea.set_view_sign(120UL);
    ASSERT_TRUE(_obj.update_and_query(&fea));
    CWARNING_LOG("query[%s], similar:%s", fea.data_view_value().c_str(),
           fea.value().c_str());
    EXPECT_EQ(0, strcmp("-1", fea.value().c_str()));
    EXPECT_EQ(false, fea.valid());

    //--------------------------------------------------------------------------------------
    //coord     |   1623057650  1623057650   1623057654  1623057653  1623057652  16230576510
    //--------------------------------------------------------------------------------------
    //view_sign |      120         110          114         113         112         111
    //--------------------------------------------------------------------------------------
    ret_fea = fea;
    ret_fea.set_value("");
    ASSERT_TRUE(_obj.query(&ret_fea));
    CWARNING_LOG("after update_and_query, query[%s], similar:%s", ret_fea.data_view_value().c_str(),
            ret_fea.value().c_str());
    EXPECT_EQ(0, strcmp("1.000000", ret_fea.value().c_str()));

    node_list = _obj._window->_window._node_list;
    list_front = node_list.front();
    ASSERT_EQ(6, node_list.size());
    ASSERT_EQ(1623057650, list_front->coord());
    ASSERT_EQ("百度知道", list_front->value());
    ASSERT_EQ(120, list_front->key());

    //update_and_query with gc and find view_sign
    fea = _feas[0];
    fea.set_log_time(1623057657UL);
    fea.set_coord(1623057657UL);
    fea.set_data_view_value("百度云盘");
    ASSERT_TRUE(_obj.update_and_query(&fea));
    CWARNING_LOG("query[%s:%s], similar:%s", _feas[1].data_view_value().c_str(),
            fea.data_view_value().c_str(), fea.value().c_str());

    //                                                                          |    gc
    //--------------------------------------------------------------------------|------------
    //coord     |   1623057657  1623057650   1623057654  1623057653  1623057652 | 16230576510
    //--------------------------------------------------------------------------|------------
    //view_sign |      110         120          114         113         112     |    111
    //--------------------------------------------------------------------------|------------

    //@ update viwe_sign 111, new coord[1623057657UL],
    ret_fea = fea;
    ret_fea.set_value("");
    ASSERT_TRUE(_obj.query(&ret_fea));
    CWARNING_LOG("after update_and_query, query[%s], similar:%s", ret_fea.data_view_value().c_str(),
            ret_fea.value().c_str());
    EXPECT_EQ(0, strcmp("1.000000", ret_fea.value().c_str()));

    //@ gc view_sign 111
    ret_fea.set_view_sign(111UL);
    ret_fea.set_value("");
    ASSERT_TRUE(_obj.query(&ret_fea));
    CWARNING_LOG("after gc, similar:%s", ret_fea.value().c_str(),
            ret_fea.value().c_str());
    EXPECT_EQ(0, strcmp("-1", ret_fea.value().c_str()));

    node_list = _obj._window->_window._node_list;
    list_front = node_list.front();
    ASSERT_EQ(5, node_list.size());
    ASSERT_EQ(1623057657, list_front->coord());
    ASSERT_EQ("百度云盘", list_front->value());
    ASSERT_EQ(110, list_front->key());

    for (auto it : node_list) {
        ASSERT_TRUE(it->key() != 111);
    }

    //update_and_query with gc and not find view_sign
    fea = _feas[0];
    fea.set_log_time(1623057658UL);
    fea.set_coord(1623057658UL);
    fea.set_view_sign(121UL);
    fea.set_data_view_value("百度贴吧");
    ASSERT_TRUE(_obj.update_and_query(&fea));
    CWARNING_LOG("query[%s], similar:%s", fea.data_view_value().c_str(),
            fea.value().c_str());
    EXPECT_EQ(0, strcmp("-1", fea.value().c_str()));
    EXPECT_EQ(false, fea.valid());

    //                                                                            |    gc
    //----------------------------------------------------------------------------|------------
    //coord     |   1623057658   1623057657  1623057650   1623057654  1623057653  | 16230576512
    //----------------------------------------------------------------------------|------------
    //view_sign |      121          110         120          114         113      |    112
    //----------------------------------------------------------------------------|------------
    ret_fea = fea;
    ret_fea.set_value("");
    ASSERT_TRUE(_obj.query(&ret_fea));
    CWARNING_LOG("after update_and_query, query[%s], similar:%s", ret_fea.data_view_value().c_str(),
            ret_fea.value().c_str());
    EXPECT_EQ(0, strcmp("1.000000", ret_fea.value().c_str()));

    // 112 gc
    ret_fea.set_view_sign(112UL);
    ret_fea.set_value("");
    ASSERT_TRUE(_obj.query(&ret_fea));
    CWARNING_LOG("after gc, similar:%s", ret_fea.value().c_str(),
            ret_fea.value().c_str());
    EXPECT_EQ(0, strcmp("-1", fea.value().c_str()));

    node_list = _obj._window->_window._node_list;
    list_front = node_list.front();
    ASSERT_EQ(5, node_list.size());
    ASSERT_EQ(1623057658, list_front->coord());
    ASSERT_EQ("百度贴吧", list_front->value());
    ASSERT_EQ(121, list_front->key());

    for (auto it : node_list) {
        ASSERT_TRUE(it->key() != 112);
    }

    _obj.uninit();
}

TEST_F(LRUNodeFeatureAccumulatorTestSuite, dump_load_click_segment_case) {
    ASSERT_TRUE(_obj.init(_conf["valid_similar"]["feature"][0]));
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(_feas[i]));
    }
    DOING_DUMP();
    _obj.uninit();

    std::string ret1 = "1.000000";
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(anti::themis::feature_lib, QueryNode,
            calc), calc(_, _))
            .WillOnce(DoAll(SetArgumentPointee<1>(ret1), Return(true)));
    
    ASSERT_TRUE(_obj.init(_conf["valid_similar"]["feature"][0]));
    DOING_LOAD();
    
    auto node_list = _obj._window->_window._node_list;
    auto list_front = node_list.front();
    ASSERT_EQ(5, node_list.size());
    ASSERT_EQ(1623057654, list_front->coord());
    ASSERT_EQ("百度百科", list_front->value());
    ASSERT_EQ(114, list_front->key());

    size_t i = 0;
    for (auto it: node_list ) {
        CWARNING_LOG("node inf view_sign[%d], value[%s], coord[%d]", it->key(), it->value().c_str(), it->coord());
        if (it->coord() == _feas[i].view_sign()) {
            ASSERT_EQ(_feas[i].coord(), it->coord());
            ASSERT_EQ(_feas[i].data_view_value(), it->value());
        }
        ++i;
    }
    
    ASSERT_TRUE(_obj.query(&_feas[4]));
    EXPECT_EQ(0, strcmp("1.000000", _feas[4].value().c_str()));
    _obj.uninit();
}
*/
}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */
