// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
// 
// @File: feature_accumulator_manager_test.cpp
// @Last modified: 2017-10-23 15:21:17
// @Brief: 

#include <gtest/gtest.h>
#include <bmock.h>
#include <gflags/gflags.h>
#include <com_log.h>
#include <archive.h>
#include "feature_accumulator_manager.h"
#include "feature_accumulator_factory.h"
#include "feature_accumulator_collector.h"

using anti::themis::common_lib::FileArchive;
using ::testing::Return;
using ::testing::_;

namespace anti {
namespace themis {
namespace feature_lib {

DECLARE_bool(enable_feature_monitor);
DECLARE_int32(monitor_period_minutes);
DECLARE_bool(enable_mem_pool_for_acc);

class FeatureAccumulatorCollectorTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
    }
    virtual void TearDown() {}

private:
    FeatureAccumulatorCollector _obj;
};

TEST_F(FeatureAccumulatorCollectorTestSuite, init_case) {
    ASSERT_FALSE(_obj.init("xxx", "xxx"));
    ASSERT_FALSE(_obj.init("./conf", "products-invalid.conf"));
    _obj._manager_map.clear();
    anti::themis::RecordType rtype = anti::themis::RecordType::CLICK_BAIDU_LOG;
    ASSERT_TRUE(_obj.init("./conf", "products.conf"));
    EXPECT_STREQ("./conf/click", _obj._get_fea_mgr(rtype)->_conf_path.data());
    EXPECT_STREQ("accumulator_feature.conf", _obj._get_fea_mgr(rtype)->_conf_file.data());
    ASSERT_EQ(2U, _obj._manager_map.size());
}

TEST_F(FeatureAccumulatorCollectorTestSuite, reload_case) {
    anti::themis::RecordType rtype = anti::themis::RecordType::CLICK_BAIDU_LOG;
    ASSERT_TRUE(_obj.init("./conf", "products.conf"));
    _obj.reload();

    _obj._get_fea_mgr(rtype)->_conf_file = "invalid_feature_accumulator_manager.conf";
    ASSERT_FALSE(_obj.reload());

    _obj._get_fea_mgr(rtype)->_conf_path = "./conf";
    _obj._get_fea_mgr(rtype)->_conf_file = "feature_accumulator_manager_test.conf";
    ASSERT_TRUE(_obj.reload());
    std::string conf = _obj._get_fea_mgr(rtype)->_conf_path + "/" +
        _obj._get_fea_mgr(rtype)->_conf_file;
    struct stat buf;
    ASSERT_EQ(0, stat(conf.data(), &buf));
    EXPECT_EQ(buf.st_mtime, _obj._get_fea_mgr(rtype)->_modify_time);
}

TEST_F(FeatureAccumulatorCollectorTestSuite, dump_case) {
    anti::themis::RecordType rtype = anti::themis::RecordType::CLICK_BAIDU_LOG;
    ASSERT_TRUE(_obj.init("./conf", "products.conf"));
    ASSERT_TRUE(_obj.dump("./ckpt"));

    ASSERT_TRUE(_obj.load("./ckpt"));
    system("rm ./ckpt/ckpt_*");
}

TEST_F(FeatureAccumulatorCollectorTestSuite, monitor_via_reload_test) {
    // 测试通过reload触发监控功能
    
    // 设置监控参数 - 启用监控且设置合理的周期
    FLAGS_enable_feature_monitor = true;
    FLAGS_monitor_period_minutes = 1;  // 设置为1分钟，但由于last_monitor_time=0，第一次会触发
    
    // 初始化collector（此时last_monitor_time = 0）
    ASSERT_TRUE(_obj.init("./conf", "products.conf"));
    
    // 第一次调用reload - 由于last_monitor_time=0，应该会触发监控
    testing::internal::CaptureStderr();
    ASSERT_TRUE(_obj.reload());
    std::string monitor_output = testing::internal::GetCapturedStderr();
    
    // 验证监控日志包含期望的内容
    EXPECT_TRUE(monitor_output.find("=== FeatureAccumulatorCollector Monitor ===") != std::string::npos);
    EXPECT_TRUE(monitor_output.find("=== End FeatureAccumulatorCollector Monitor ===") != std::string::npos);
    
    // 验证包含时间信息
    EXPECT_TRUE(monitor_output.find("Time:") != std::string::npos);
    
    // 立即第二次调用reload - 由于在周期内，不应该触发监控
    testing::internal::CaptureStderr();
    ASSERT_TRUE(_obj.reload());
    std::string no_monitor_output = testing::internal::GetCapturedStderr();
    
    // 第二次调用不应该触发监控（在周期内）
    EXPECT_TRUE(no_monitor_output.find("=== FeatureAccumulatorCollector Monitor ===") == std::string::npos);
}

TEST_F(FeatureAccumulatorCollectorTestSuite, monitor_disabled_test) {
    // 测试关闭监控功能的不同方式
    
    // 方式1: enable_feature_monitor = false
    FLAGS_enable_feature_monitor = false;
    FLAGS_monitor_period_minutes = 1;  // 即使设置了周期，但监控被禁用
    
    ASSERT_TRUE(_obj.init("./conf", "products.conf"));
    
    testing::internal::CaptureStderr();
    ASSERT_TRUE(_obj.reload());
    std::string output1 = testing::internal::GetCapturedStderr();
    
    // 监控被禁用时不应该有监控日志
    EXPECT_TRUE(output1.find("=== FeatureAccumulatorCollector Monitor ===") == std::string::npos);
}

TEST_F(FeatureAccumulatorCollectorTestSuite, monitor_period_zero_test) {
    // 测试monitor_period_minutes <= 0的情况
    FLAGS_enable_feature_monitor = true;
    FLAGS_monitor_period_minutes = 0;  // 周期为0也会禁用监控
    
    ASSERT_TRUE(_obj.init("./conf", "products.conf"));
    
    testing::internal::CaptureStderr();
    ASSERT_TRUE(_obj.reload());
    std::string output = testing::internal::GetCapturedStderr();
    
    // 周期为0时不应该有监控日志
    EXPECT_TRUE(output.find("=== FeatureAccumulatorCollector Monitor ===") == std::string::npos);
}

TEST_F(FeatureAccumulatorCollectorTestSuite, monitor_content_test) {
    // 测试监控内容的详细输出
    
    FLAGS_enable_feature_monitor = true;
    FLAGS_monitor_period_minutes = 1;
    
    ASSERT_TRUE(_obj.init("./conf", "products.conf"));
    
    // 添加一些测试数据 - 参考segment_feature_accumulator_test.cpp的构造方式
    FeatureValueProto fea;
    fea.set_feature_id(100);  // segment类型
    fea.set_view_sign(123456LU);
    fea.set_feature_type(FeatureValueProto::SEGMENT);
    fea.set_joinkey(119LU);
    fea.set_log_id(120LU);
    fea.set_log_time(1111111111LU);
    fea.set_coord(1L);
    fea.set_data_view_value("123");
    _obj.update(fea);
    
    fea.set_feature_id(101);  // ratio类型
    fea.set_feature_type(FeatureValueProto::RATIO);
    _obj.update(fea);
    
    fea.set_feature_id(102);  // distribution类型
    fea.set_feature_type(FeatureValueProto::DISTRIBUTION);
    _obj.update(fea);
    
    // 触发监控
    testing::internal::CaptureStderr();
    ASSERT_TRUE(_obj.reload());
    std::string monitor_output = testing::internal::GetCapturedStderr();
    
    // 验证监控框架信息
    EXPECT_TRUE(monitor_output.find("=== FeatureAccumulatorCollector Monitor ===") != std::string::npos);
    EXPECT_TRUE(monitor_output.find("=== End FeatureAccumulatorCollector Monitor ===") != std::string::npos);
    
    // 验证包含manager信息
    EXPECT_TRUE(monitor_output.find("Manager [") != std::string::npos);
    
    // 验证包含特征类型信息（应该包含每个特征的监控日志）
    // 由于_periodic_monitor会调用每个accumulator的print_monitor_log()
    // 我们应该能看到各种特征类型的输出
    EXPECT_TRUE(monitor_output.find("Feature: ID=") != std::string::npos);
}

TEST_F(FeatureAccumulatorCollectorTestSuite, monitor_memory_pool_modes_test) {
    // 测试不同内存池模式下的监控输出差异
    
    FLAGS_enable_feature_monitor = true;
    FLAGS_monitor_period_minutes = 1;
    
    // 测试 StdSlidingWindow 模式 (use_mem_pool = false)
    FLAGS_enable_mem_pool_for_acc = false;
    
    ASSERT_TRUE(_obj.init("./conf", "products.conf"));
    
    // 添加测试数据
    FeatureValueProto fea;
    fea.set_feature_id(100);
    fea.set_view_sign(123456LU);
    fea.set_feature_type(FeatureValueProto::SEGMENT);
    fea.set_joinkey(119LU);
    fea.set_log_id(120LU);
    fea.set_log_time(1111111111LU);
    fea.set_coord(1L);
    fea.set_data_view_value("123");
    _obj.update(fea);
    
    // 触发监控并验证StdSlidingWindow模式的输出
    testing::internal::CaptureStderr();
    ASSERT_TRUE(_obj.reload());
    std::string std_mode_output = testing::internal::GetCapturedStderr();
    
    // StdSlidingWindow模式应该显示"Memory=N/A"
    EXPECT_TRUE(std_mode_output.find("Memory=N/A") != std::string::npos);
    
    // 现在测试 PoolSlidingWindow 模式 (use_mem_pool = true)
    FLAGS_enable_mem_pool_for_acc = true;
    
    // 重新初始化以应用新的内存池设置
    FeatureAccumulatorCollector obj2;
    ASSERT_TRUE(obj2.init("./conf", "products.conf"));
    
    // 添加相同的测试数据
    obj2.update(fea);
    
    // 触发监控并验证PoolSlidingWindow模式的输出
    testing::internal::CaptureStderr();
    ASSERT_TRUE(obj2.reload());
    std::string pool_mode_output = testing::internal::GetCapturedStderr();
    
    // PoolSlidingWindow模式应该显示详细的内存统计信息，而不是"Memory=N/A"
    EXPECT_TRUE(pool_mode_output.find("=== FeatureAccumulatorCollector Monitor ===") != std::string::npos);
    EXPECT_TRUE(pool_mode_output.find("Feature: ID=") != std::string::npos);
    
    // 验证PoolSlidingWindow模式的详细内存统计（应该不是"Memory=N/A"）
    EXPECT_TRUE(pool_mode_output.find("Memory(Total=") != std::string::npos);
    EXPECT_TRUE(pool_mode_output.find("Data=") != std::string::npos);
    EXPECT_TRUE(pool_mode_output.find("Overhead=") != std::string::npos);
    EXPECT_TRUE(pool_mode_output.find("AvgPerElement=") != std::string::npos);
    
    // 确保不是显示"Memory=N/A"
    EXPECT_TRUE(pool_mode_output.find("Memory=N/A") == std::string::npos);
}

TEST_F(FeatureAccumulatorCollectorTestSuite, query_ext_fea_case) {
    // case: input invalid
    FValQRequestProto request;
    request.set_feature_id(123);
    ASSERT_FALSE(_obj.query(request, NULL));
    //no tuple  mode:
    {
        // case: get mgr fail, not init case
        FeatureAccumulatorCollector obj;
        FValQRequestProto request; 
        request.set_feature_id(0);
        FValQResponseProto response;
        ASSERT_FALSE(obj.query(request, &response));
        // case: query fail
        ASSERT_TRUE(obj.init("./conf/click/", "accumulator_feature.conf"));
        request.set_record_type(2);
        ASSERT_TRUE(obj.query(request, &response));
    }
    // case : tuple mode
    {
        // case: record type not found 
        FeatureAccumulatorCollector obj;
        ASSERT_TRUE(obj.init("./conf", "products.conf"));
        FValQRequestProto request;
        FValQResponseProto response; 
        request.set_feature_id(0);
        request.set_record_type(1000);
        ASSERT_FALSE(obj.query(request, &response));
        // case: query true
        request.set_record_type(2);
        ASSERT_TRUE(obj.query(request, &response));
    }
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

