// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: feature_accumulator_base_test.cpp
// @Last modified: 2017-10-26 17:37:19
// @Brief: 

#include "extend_accumulator_base.h"
#include <gtest/gtest.h>
#include <gflags/gflags.h>
#include <bmock.h>
#include <com_log.h>
#include <archive.h>

using ::testing::_;
using ::testing::Return;

namespace anti {
namespace themis {
namespace feature_lib {

typedef anti::themis::common_lib::FileArchive FileArchive;
const char* CONF_PATH = "./conf";
const char* CONF_FILE = "extend_fea_acc_test.conf";
class ExtendAccumulatorBaseTestSuite : public ::testing::Test {
public:
    ExtendAccumulatorBaseTestSuite() {}
    ~ExtendAccumulatorBaseTestSuite() {}

    virtual void SetUp() {
        ASSERT_EQ(0, _conf.load(CONF_PATH, CONF_FILE));
        _client.reset(new (std::nothrow) FValueRPCManager());
        ASSERT_FALSE(!_client);
    }
    virtual void TearDown() {}

private:
    comcfg::Configure _conf;
    std::shared_ptr<FValueRPCManager> _client;
};

struct GroupValue {
    GroupValue() : value(0) {}
    GroupValue(uint64_t va) : value(va) {}
    GroupValue(const GroupValue& va) : value(va.value) {}
    uint64_t value;
    
    template<typename Archive>
    bool serialize(Archive* ar) const {
        return anti::themis::common_lib::t_write(value, ar);
    }   
    template<typename Archive>
    bool deserialize(Archive* ar) {
        return anti::themis::common_lib::t_read(&value, ar);
    }   
    
    bool operator==(const GroupValue& rv) const {
        return rv.value == value;
    }   
};

// GroupNode test
TEST_F(ExtendAccumulatorBaseTestSuite, GroupNode_ctr_case) {
    {
        GroupNode<GroupValue> obj;
        EXPECT_EQ(0U, obj._view); 
        EXPECT_EQ(0U, obj._coord);
        ASSERT_TRUE(!obj._value);
    }
    {
        GroupNode<GroupValue> obj(100U, 101);
        EXPECT_EQ(100U, obj.key());
        EXPECT_EQ(101, obj.coord());
    }
}

TEST_F(ExtendAccumulatorBaseTestSuite, GroupNode_dump_and_load_case) {
    GroupNode<GroupValue> node(100, 200);
    std::shared_ptr<GroupValue> v(new (std::nothrow)GroupValue(13));
    node.set_value(v);
    const char* file = "tmp_lru_node.ckpt";
    std::string cmd = "rm -f ";
    cmd += file;
    system(cmd.c_str());
    // dump
    FileArchive ar;
    ASSERT_TRUE(ar.open_w(file));
    ASSERT_TRUE(node.serialize<FileArchive>(&ar));
    ar.close();
    // load
    FileArchive ar2;
    ASSERT_TRUE(ar2.open_r(file));
    GroupNode<GroupValue> node2;
    ASSERT_TRUE(node2.deserialize<FileArchive>(&ar2));
    EXPECT_EQ(node._view, node2._view);
    EXPECT_EQ(node._coord, node2._coord);
    EXPECT_EQ(node._latest_extend, node2._latest_extend);
    EXPECT_TRUE(*(node2.value()) == *(node.value()));
    
    ar2.close();
    system(cmd.c_str());
}

TEST_F(ExtendAccumulatorBaseTestSuite, GroupNode_dump_and_load_no_value_case) {
    GroupNode<GroupValue> node(100, 200);
    const char* file = "tmp_lru_node.ckpt";
    std::string cmd = "rm -f ";
    cmd += file;
    system(cmd.c_str());
    // dump
    FileArchive ar;
    ASSERT_TRUE(ar.open_w(file));
    ASSERT_TRUE(node.serialize<FileArchive>(&ar));
    ar.close();
    // load
    FileArchive ar2;
    ASSERT_TRUE(ar2.open_r(file));
    GroupNode<GroupValue> node2;
    ASSERT_TRUE(node2.deserialize<FileArchive>(&ar2));
    ASSERT_TRUE(!node2.value());
    EXPECT_EQ(node._view, node2._view);
    EXPECT_EQ(node._coord, node2._coord);
    EXPECT_EQ(node._latest_extend, node2._latest_extend);
    ar2.close();
    system(cmd.c_str());
}

class MockAccumulator : public ExtendAccumulatorBase<GroupNode<GroupValue>> {
public:
    MockAccumulator() : _query_flag(true), _acc_flag(true) {
        _request.reset(new (std::nothrow) FValQueryTransProto);
    }
    virtual ~MockAccumulator() {}

public:
    bool _query_flag;
    bool _acc_flag;

    virtual bool query(FeatureValueProto* fea) const {
        return true;
    }
    virtual bool update_and_query(FeatureValueProto* fea) {
        return true;
    }
    virtual bool update(const FeatureValueProto& fea) {
        return true;
    }
    virtual void sync(std::vector<std::shared_ptr<FeatureValueProto>>* feas) {return;}

private:
    virtual bool _init(const comcfg::ConfigUnit& conf) { return true; }
    std::shared_ptr<FValQueryTransProto> _request;
};

BMOCK_NS_CLASS_METHOD1(anti::themis::feature_lib, FValueRPCManager, query,
        bool(const std::shared_ptr<FValQueryTransProto>&));

void resume_mock() {
    BMOCK_NS_CLASS_RESUME(anti::themis::feature_lib, FValueRPCManager, query,
            bool(const std::shared_ptr<FValQueryTransProto>&));
}

void stop_mock() {
    BMOCK_NS_CLASS_STOP(anti::themis::feature_lib, FValueRPCManager, query,
            bool(const std::shared_ptr<FValQueryTransProto>&));
}

TEST_F(ExtendAccumulatorBaseTestSuite, ctr_case) {
    MockAccumulator obj;
    EXPECT_EQ(0U, obj._feature_id);
    EXPECT_EQ(0U, obj._version);
}

TEST_F(ExtendAccumulatorBaseTestSuite, init_case) {
    // init fail cases
    for (uint32_t i = 0; i < _conf["invalid"].size(); ++i) {
        MockAccumulator obj;
        EXPECT_FALSE(obj.init(_conf["invalid"][i]));
        CWARNING_LOG("invalid: %u", i);
    }
    // init succ case
    {
        MockAccumulator obj;
        ASSERT_TRUE(obj.init(_conf["valid"][0]));
        EXPECT_EQ(111U, obj._feature_id);
        EXPECT_EQ(1U, obj._version);
        EXPECT_EQ(100, obj._window_conf.window_length);
        EXPECT_EQ(3600, obj._window_conf.expiration);
        EXPECT_EQ(10000, obj._window_conf.max_item);
        EXPECT_EQ(100, obj._window_conf.remain);
    }
}

//TEST_F(ExtendAccumulatorBaseTestSuite, update_case) {
//    // case: not init case
//    {
//        MockAccumulator obj;
//        FeatureValueProto fea;
//        ASSERT_FALSE(obj.update(fea));
//    }
//    // case: succ, no extend case
//    {
//        MockAccumulator obj;
//        ASSERT_TRUE(obj.init(_conf["valid"][0], _client));
//        FeatureValueProto fea;
//        fea.set_feature_id(obj._feature_id);
//        obj._acc_flag = false;
//        ASSERT_TRUE(obj.update(fea));
//    } 
//    // case: succ, extend case
//    {
//        resume_mock();
//        MockAccumulator obj;
//        ASSERT_TRUE(obj.init(_conf["valid"][0], _client));
//        FeatureValueProto fea;
//        fea.set_feature_id(obj._feature_id);
//        obj._acc_flag = true;
//        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(anti::themis::feature_lib, FValueRPCManager, query), 
//                query(_))
//                .WillOnce(Return(true));
//        ASSERT_TRUE(obj.update(fea));
//    } 
//}
//
//TEST_F(ExtendAccumulatorBaseTestSuite, update_and_query_case) {
//    // case: input invalid case
//    {
//        MockAccumulator obj;
//        ASSERT_FALSE(obj.update_and_query(NULL));
//    }
//    // case: obj not init case
//    {
//        MockAccumulator obj;
//        FeatureValueProto fea;
//        fea.set_feature_id(111);
//        obj._feature_id = 111;
//        ASSERT_FALSE(obj.update_and_query(&fea));
//    }
//}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

