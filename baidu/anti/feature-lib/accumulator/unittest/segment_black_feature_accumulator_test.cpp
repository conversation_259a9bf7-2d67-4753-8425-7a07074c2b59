// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: segment_black_feature_accumulator_test.cpp
// @Last modified: 2017-03-16 14:36:43
// @Brief: 

#include <vector>
#include <gtest/gtest.h>
#include "segment_black_feature_accumulator.h"
#include "posix_themis_io.h" 
#include "ckpt_test_util.h"

namespace anti {
namespace themis {
namespace feature_lib {

class SegmentBlackFeatureAccumulatorTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
        ASSERT_TRUE(_conf.load("./conf", "accumulator_test.conf") == 0);
        FeatureValueProto fea;
        fea.set_feature_id(1L);
        fea.set_feature_type(FeatureValueProto::SEGMENT);
        _inv_feas.push_back(fea);
        fea.set_feature_id(123456L);
        fea.set_feature_type(FeatureValueProto::AUTO_SEGMENT);
        _inv_feas.push_back(fea);
    } 

    virtual void TearDown() {

    }
private:
    SegmentBlackFeatureAccumulator _obj;
    comcfg::Configure _conf;
    std::vector<FeatureValueProto>_inv_feas;
};

TEST_F(SegmentBlackFeatureAccumulatorTestSuite, construction_case) {
    EXPECT_EQ(0LU, _obj.feature_id());
    EXPECT_EQ(0LU, _obj._version);
}

TEST_F(SegmentBlackFeatureAccumulatorTestSuite, ini_case) {
    uint32_t num = _conf["invalid_seglimit_black"]["feature"].size();
    ASSERT_EQ(8, num);
    for (uint32_t i = 0; i < num; i++) {
        EXPECT_FALSE(_obj.init(_conf["invalid_seglimit_black"]["feature"][i]));
        _obj.uninit();
    }

    ASSERT_TRUE(_obj.init(_conf["valid_seglimit_black"]["feature"][0]));
    EXPECT_EQ(1LU, _obj.feature_id());
    EXPECT_EQ(1LU, _obj._version);
    const anti::themis::common_lib::SegmentInfo& info = _obj._seg.segment_info();
    EXPECT_EQ(10000L, info.segment_len);
    EXPECT_EQ(1000L, info.upper);
    EXPECT_DOUBLE_EQ(0.5, info.lamda);
    EXPECT_EQ(9L, info.segment_num);
    _obj.uninit();

    ASSERT_TRUE(_obj.init(_conf["valid_seglimit_black"]["feature"][1]));
    EXPECT_EQ(1LU, _obj.feature_id());
    EXPECT_EQ(1LU, _obj._version);
    const anti::themis::common_lib::SegmentInfo& info1 = _obj._seg.segment_info();
    EXPECT_EQ(10000L, info1.segment_len);
    EXPECT_EQ(1000L, info1.upper);
    EXPECT_DOUBLE_EQ(0.5, info1.lamda);
    EXPECT_EQ(5L, info1.segment_num);
    _obj.uninit();
}

TEST_F(SegmentBlackFeatureAccumulatorTestSuite, update_and_query_case) {
    ASSERT_TRUE(_obj.init(_conf["valid_seglimit_black"]["feature"][0]));
    const anti::themis::common_lib::SegmentInfo& info = _obj._seg.segment_info();
    int64_t segment_len = info.segment_len;

    // invalid fea
    EXPECT_FALSE(_obj.update_and_query(NULL));
    for (uint32_t i = 0U; i < _inv_feas.size(); i++) {
        EXPECT_FALSE(_obj.update_and_query(&_inv_feas[i]));
    }

    // in first segment
    FeatureValueProto fea;
    fea.set_feature_type(FeatureValueProto::AUTO_SEGMENT);
    fea.set_view_sign(0x1111);
    fea.set_feature_id(1);
    for (int32_t i = 0; i <  segment_len; i++) {
        fea.set_coord(i);
        EXPECT_TRUE(_obj.update_and_query(&fea)); 
        EXPECT_EQ(0L, fea.last_seg_count());
        EXPECT_EQ(i+1, fea.cur_seg_count());
        fea.clear_last_seg_count();
        fea.clear_cur_seg_count();
    }

    // jump one segment AND beyond upper
    fea.set_coord(segment_len + 1);
    EXPECT_TRUE(_obj.update_and_query(&fea));
    EXPECT_EQ(0L, fea.last_seg_count());
    EXPECT_EQ(501L, fea.cur_seg_count());
    FeatureValueProto fea_other;
    fea_other.set_coord(1L);
    fea_other.set_feature_type(FeatureValueProto::AUTO_SEGMENT);
    fea_other.set_view_sign(0x2222);
    fea_other.set_feature_id(1);
    EXPECT_TRUE(_obj.update_and_query(&fea_other));
    EXPECT_EQ(0L, fea_other.last_seg_count());
    EXPECT_EQ(1L, fea_other.cur_seg_count());

    // jump 3 segment
    fea.set_coord(segment_len * 4 + 10);
    EXPECT_TRUE(_obj.update_and_query(&fea));
    EXPECT_EQ(0L, fea.last_seg_count());
    EXPECT_EQ(32L, fea.cur_seg_count());

    // jump 9 segment
    fea.set_coord(segment_len * 9 + 8);
    EXPECT_TRUE(_obj.update_and_query(&fea));
    EXPECT_EQ(0L, fea.last_seg_count());
    EXPECT_EQ(2L, fea.cur_seg_count());
    
    _obj.uninit();
}

TEST_F(SegmentBlackFeatureAccumulatorTestSuite, multi_update_one_query_case) {
    ASSERT_TRUE(_obj.init(_conf["valid_seglimit_black"]["feature"][0]));
    const anti::themis::common_lib::SegmentInfo& info = _obj._seg.segment_info();
    int64_t segment_len = info.segment_len;

    // invalid features
    for (uint32_t i = 0U; i < _inv_feas.size(); i++) {
        EXPECT_FALSE(_obj.update(_inv_feas[i]));
        EXPECT_FALSE(_obj.query(&_inv_feas[i]));
    }

    FeatureValueProto fea;
    fea.set_feature_type(FeatureValueProto::AUTO_SEGMENT);
    fea.set_view_sign(0x1111);
    fea.set_feature_id(1);
    for (int32_t i = 1; i <  segment_len;) {
        fea.set_coord(i);
        ASSERT_TRUE(_obj.update(fea)); 
        i = i * 10;
    }

    FeatureValueProto fea_res = fea;
    ASSERT_TRUE(_obj.query(&fea_res));
    EXPECT_EQ(4L, fea_res.cur_seg_count());
    EXPECT_EQ(0L, fea_res.last_seg_count());

    fea.set_coord(segment_len + 2);
    ASSERT_TRUE(_obj.update(fea));
    fea.set_coord(segment_len + 5);
    ASSERT_TRUE(_obj.update(fea));  

    ASSERT_TRUE(_obj.query(&fea_res));
    EXPECT_EQ(4L, fea_res.cur_seg_count());
    EXPECT_EQ(0L, fea_res.last_seg_count());
}

TEST_F(SegmentBlackFeatureAccumulatorTestSuite, dump_load_case) {
    ASSERT_TRUE(_obj.init(_conf["valid_seglimit_black"]["feature"][0]));
    const anti::themis::common_lib::SegmentInfo& info = _obj._seg.segment_info();
    int64_t segment_len = info.segment_len;
    FeatureValueProto fea;
    fea.set_feature_type(FeatureValueProto::AUTO_SEGMENT);
    fea.set_view_sign(0x1111);
    fea.set_feature_id(1);
    for (int32_t i = 0; i <  segment_len; i++) {
        fea.set_coord(i);
        ASSERT_TRUE(_obj.update(fea)); 
    }
    FeatureValueProto fea_dump;
    fea_dump.set_view_sign(0x1111);
    fea_dump.set_feature_id(1);
    fea_dump.set_feature_type(FeatureValueProto::AUTO_SEGMENT);

    ASSERT_TRUE(_obj.query(&fea_dump));
    DOING_DUMP();
    _obj.uninit();

    FeatureValueProto fea_load;
    fea_load.set_view_sign(0x1111);
    fea_load.set_feature_id(1);
    fea_load.set_feature_type(FeatureValueProto::AUTO_SEGMENT);

    ASSERT_TRUE(_obj.init(_conf["valid_seglimit_black"]["feature"][0]));
    DOING_LOAD();
    ASSERT_TRUE(_obj.query(&fea_load));
    EXPECT_EQ(fea_dump.cur_seg_count(), fea_load.cur_seg_count());
    EXPECT_EQ(fea_dump.last_seg_count(), fea_load.last_seg_count());
    _obj.uninit();
    remove("./seg_black.ckpt");

}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti
/* vim: set ts=4 sw=4: */

