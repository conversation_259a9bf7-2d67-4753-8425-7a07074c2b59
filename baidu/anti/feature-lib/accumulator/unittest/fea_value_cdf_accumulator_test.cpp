// Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
// @Author: langwen<PERSON>(<EMAIL>)
// 
// @File: fea_val_cdf_test.cpp

#include <cstdlib>
#include <gtest/gtest.h>
#include <bmock.h>
#include <archive.h>
#include "ckpt_test_util.h"
#include "fea_value_cdf_accumulator.h"

using ::testing::_;
using ::testing::Return;
using ::testing::DoAll;
using ::testing::SetArgPointee;
using ::testing::SetArgumentPointee;

namespace anti {
namespace themis {
namespace feature_lib {

const char* CONF_PATH = "./conf";
const char* CONF_FILE = "fea_val_cdf_test.conf";

typedef anti::themis::common_lib::FileArchive FileArchive;

BMOCK_NS_CLASS_METHOD1(anti::themis::feature_lib, FValueRPCManager, query,
        bool(const std::shared_ptr<FValQueryTransProto>&));
void resume_mock() {
    BMOCK_NS_CLASS_RESUME(anti::themis::feature_lib, FValueRPCManager, query,
            bool(const std::shared_ptr<FValQueryTransProto>&));
}
void stop_mock() {
    BMOCK_NS_CLASS_STOP(anti::themis::feature_lib, FValueRPCManager, query,
            bool(const std::shared_ptr<FValQueryTransProto>&));
}
typedef anti::themis::common_lib::FileArchive FileArchive;
class  FeatureValueCdfAccumulatorTestSuite : public ::testing::Test {
protected: 
    virtual void SetUp() {
        stop_mock();
        ASSERT_EQ(0, _conf.load(_path, _file));
        ASSERT_TRUE(_obj.init(_conf["valid"][0]));
        _obj._rpc_mgr.reset(new (std::nothrow) FValueRPCManager);
    }
    virtual void TearDown() {
    }
private:
    FeatureValueCdfAccumulator _obj;
    comcfg::Configure _conf;
    const char* _path = "./conf";
    const char* _file = "fea_val_cdf_test.conf";
};

TEST_F(FeatureValueCdfAccumulatorTestSuite, init_fail_test) {
    for (uint32_t i = 0; i < _conf["invalid"].size(); ++i) {
        CWARNING_LOG("current : i:%lu", i);
        FeatureValueCdfAccumulator obj;
        ASSERT_FALSE(obj.init(_conf["invalid"][i]));
    }
}

TEST_F(FeatureValueCdfAccumulatorTestSuite, init_success_test) {
    for (uint32_t i = 0; i < _conf["valid"].size(); ++i) {
        CWARNING_LOG("current : i:%d", i);
        FeatureValueCdfAccumulator obj;
        ASSERT_TRUE(obj.init(_conf["valid"][i]));
    }
}

FeatureValueProto mock_fea() {
    FeatureValueProto fea;
    fea.set_feature_id(111);
    fea.set_view_sign(555);
    fea.set_coord(1001);
    fea.set_feature_type(FeatureValueProto::FEATURE_VALUE_EXTEND);
    auto info = fea.mutable_fea_serv_trans()->add_infos();
    info->mutable_request()->set_view_sign(666);
    info->mutable_request()->set_value("view_val");
    info->mutable_request()->set_feature_id(222);
    return fea;
}

std::shared_ptr<FValQueryTransProto> mock_req() {
    std::shared_ptr<FValQueryTransProto> req(new (std::nothrow) FValQueryTransProto);
    auto info = req->add_infos();
    info->mutable_request()->set_view_sign(666);
    info->mutable_request()->set_feature_id(222);
    info->mutable_response()->add_value("0.8");
    info->mutable_response()->set_coord(1001);
    return req;
}

TEST_F(FeatureValueCdfAccumulatorTestSuite, update_test) {
    FeatureValueCdfAccumulator obj;
    obj._rpc_mgr.reset(new (std::nothrow) FValueRPCManager);
    ASSERT_TRUE(obj.init(_conf["valid"][0]));
    auto fea = mock_fea();
    for (int i = 0; i < obj._cdf_conf.step; i++) {
        EXPECT_TRUE(obj.update(fea));
    }
    EXPECT_EQ(1, obj._val_query_map.size());
    auto node = obj._window->find(obj._get_extend_view_sign(fea));
    ASSERT_TRUE(node != nullptr);
    EXPECT_EQ(0, node->value()->get_appear_count());
}

TEST_F(FeatureValueCdfAccumulatorTestSuite, sync_query_test) {
    // test sync
    FeatureValueCdfAccumulator obj;
    obj._rpc_mgr.reset(new (std::nothrow) FValueRPCManager);
    ASSERT_TRUE(obj.init(_conf["valid"][0]));
    auto req = mock_req();
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(anti::themis::feature_lib, FValueRPCManager, query), 
            query(_))
            .WillOnce(DoAll(SetArgPointee<0>(*req), Return(true)));
    resume_mock();

    auto fea = mock_fea();
    for (int i = 0; i < obj._cdf_conf.step; i++) {
        EXPECT_TRUE(obj.update(fea));
    }
    std::vector<std::shared_ptr<FeatureValueProto>> feas;
    obj.sync(&feas);
    EXPECT_EQ(0, obj._dis._update_op_batch.size());
    EXPECT_EQ(1, obj._dis._cdf._total);
    EXPECT_EQ(1, obj._dis._cdf._distribution.size());
    EXPECT_EQ(1, obj._dis._cdf._distribution[80]);
    EXPECT_EQ(1, obj._dis._cdf._cdf_cache.size());
    EXPECT_DOUBLE_EQ(1.0, obj._dis._cdf._cdf_cache[80]);
    // test query
    EXPECT_TRUE(obj.query(&fea));
    EXPECT_STREQ("1", fea.value().data());
}

TEST_F(FeatureValueCdfAccumulatorTestSuite, dump_load_test) {
    // prepare 
    FeatureValueCdfAccumulator obj;
    obj._rpc_mgr.reset(new (std::nothrow) FValueRPCManager);
    ASSERT_TRUE(obj.init(_conf["valid"][0]));
    auto req = mock_req();
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(anti::themis::feature_lib, FValueRPCManager, query), 
            query(_))
            .WillOnce(DoAll(SetArgPointee<0>(*req), Return(true)));
    resume_mock();
    auto fea = mock_fea();
    for (int i = 0; i < obj._cdf_conf.step; i++) {
        EXPECT_TRUE(obj.update(fea));
    }
    std::vector<std::shared_ptr<FeatureValueProto>> feas;
    obj.sync(&feas);

    std::string file = "./cdf_ckpt_test";
    std::string cmd = "rm -f " + file;
    system(cmd.c_str());
    {
        PosixThemisIoWriter writer;
        ASSERT_EQ(0, writer.open(file.c_str()));
        ASSERT_TRUE(obj.dump(&writer));
    }

    FeatureValueCdfAccumulator obj_read;
    obj_read._rpc_mgr.reset(new (std::nothrow) FValueRPCManager);
    ASSERT_TRUE(obj_read.init(_conf["valid"][0]));

    PosixThemisIoReader reader;
    ASSERT_EQ(0, reader.open(file.c_str()));
    ASSERT_TRUE(obj_read.load(&reader));
    EXPECT_EQ(1, obj_read._dis._cdf._distribution.size());
    EXPECT_EQ(1, obj_read._dis._cdf._cdf_cache.size());
    EXPECT_EQ(1, obj._dis._cdf._distribution[80]);
    EXPECT_DOUBLE_EQ(1.0, obj._dis._cdf._cdf_cache[80]);
}
} // namespace feature_lib
} // namespace themis
} // namespace anti

/* vim: set ts=4 sw=4: */

