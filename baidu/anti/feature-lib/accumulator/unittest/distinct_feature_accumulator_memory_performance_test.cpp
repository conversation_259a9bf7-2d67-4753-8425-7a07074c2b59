// Copyright (c) 2025 Baidu.com, Inc. All Rights Reserved
// @Author: <PERSON> (<EMAIL>)
// 
// @Brief: 基于RSS内存增长的DistinctFeatureAccumulator性能对比测试

#include <vector>
#include <chrono>
#include <fstream>
#include <sstream>
#include <gtest/gtest.h>
#include "boost/lexical_cast.hpp"

#include "distinct_feature_accumulator.h"
#include "distinct_feature_accumulator_v2.h"
#include "posix_themis_io.h"
#include "ckpt_test_util.h"

namespace anti {
namespace themis {
namespace feature_lib {

/**
 * @brief RSS内存性能测试结果
 */
struct AccumulatorMemoryResult {
    size_t num_accumulators;
    size_t rss_growth_kb;
    size_t total_time_ms;
    bool test_successful;

    AccumulatorMemoryResult() : 
        num_accumulators(0), rss_growth_kb(0), total_time_ms(0), test_successful(false) {}
};

/**
 * @brief 获取进程RSS内存使用量(KB)
 */
inline size_t get_rss_memory() {
    std::ifstream status_file("/proc/self/status");
    if (!status_file.is_open()) {
        return 0;
    }

    std::string line;
    while (std::getline(status_file, line)) {
        if (line.find("VmRSS:") == 0) {
            std::istringstream iss(line);
            std::string label;
            size_t value{0};
            std::string unit;
            iss >> label >> value >> unit;
            return value;  // KB
        }
    }
    return 0;
}

/**
 * @brief 生成测试用的FeatureValueProto数据，填满一小时窗口
 * @param data_count 数据数量
 * @param base_view_sign 基础view_sign，用于区分不同Accumulator的数据
 */
inline std::vector<FeatureValueProto> generate_hour_window_test_data(size_t data_count, uint64_t base_view_sign = 123456LU) {
    std::vector<FeatureValueProto> feas;
    feas.reserve(data_count);
    
    const int64_t hour_span_seconds = 3600;
    const int64_t base_coord = 1000000;
    
    // 更多样化的data_view_sign，确保distinct特性
    uint64_t data_view_signs[] = {
        100001LU, 100002LU, 100003LU, 100004LU, 100005LU,
        100006LU, 100007LU, 100008LU, 100009LU, 100010LU,
        100011LU, 100012LU, 100013LU, 100014LU, 100015LU,
        100016LU, 100017LU, 100018LU, 100019LU, 100020LU
    };
    
    for (size_t i = 0; i < data_count; ++i) {
        FeatureValueProto fea;
        fea.set_feature_id(140LU);
        fea.set_view_sign(base_view_sign + (i % 100));  // 每个Accumulator使用不同范围的view_sign
        fea.set_feature_type(FeatureValueProto::DISTINCT);
        fea.set_joinkey(119LU);
        fea.set_log_id(120LU + i);
        fea.set_log_time(1111111111LU + i);
        
        int64_t coord_offset = (i * hour_span_seconds) / data_count;
        fea.set_coord(base_coord + coord_offset);
        
        // 使用更多不同的data_view_sign来测试distinct逻辑
        fea.set_data_view_sign(data_view_signs[i % 20] + i / 1000);  
        feas.push_back(fea);
    }
    
    return feas;
}

/**
 * @brief 基础测试类 - 提供配置加载
 */
class AccumulatorMemoryTestBase : public ::testing::Test {
protected:
    virtual void SetUp() {
        ASSERT_TRUE(_conf.load("./conf", "accumulator_test.conf") == 0);
    }

protected:
    comcfg::Configure _conf;
};

/**
 * @brief V1版本内存性能测试
 */
class DistinctAccumulatorV1MemoryTest : public AccumulatorMemoryTestBase {
protected:
    AccumulatorMemoryResult benchmark_v1_performance(size_t num_accumulators, size_t updates_per_acc) {
        AccumulatorMemoryResult result;
        result.num_accumulators = num_accumulators;
        
        auto total_start = std::chrono::high_resolution_clock::now();
        size_t rss_before = get_rss_memory();
        
        std::vector<DistinctFeatureAccumulator*> accumulators;
        accumulators.reserve(num_accumulators);
        
        // 创建Accumulator
        for (size_t i = 0; i < num_accumulators; ++i) {
            DistinctFeatureAccumulator* acc = new DistinctFeatureAccumulator();
            if (acc->init(_conf["valid_distinct"]["feature"][0])) {
                accumulators.push_back(acc);
            } else {
                delete acc;
            }
        }
        
        // 执行大量update操作
        auto test_data = generate_hour_window_test_data(updates_per_acc);
        for (size_t i = 0; i < accumulators.size(); ++i) {
            for (size_t j = 0; j < test_data.size(); ++j) {
                FeatureValueProto fea_copy = test_data[j];
                accumulators[i]->update_and_query(&fea_copy);
            }
        }
        
        size_t rss_after = get_rss_memory();
        auto total_end = std::chrono::high_resolution_clock::now();
        
        result.rss_growth_kb = (rss_after > rss_before) ? (rss_after - rss_before) : 0;
        result.total_time_ms = std::chrono::duration_cast<std::chrono::milliseconds>(total_end - total_start).count();
        result.test_successful = (accumulators.size() == num_accumulators);
        
        // 清理资源
        for (size_t i = 0; i < accumulators.size(); ++i) {
            delete accumulators[i];
        }
        
        return result;
    }
};

/**
 * @brief V2版本内存性能测试
 */
class DistinctAccumulatorV2MemoryTest : public AccumulatorMemoryTestBase {
protected:
    AccumulatorMemoryResult benchmark_v2_performance(size_t num_accumulators, size_t updates_per_acc) {
        AccumulatorMemoryResult result;
        result.num_accumulators = num_accumulators;
        
        auto total_start = std::chrono::high_resolution_clock::now();
        size_t rss_before = get_rss_memory();
        
        std::vector<DistinctFeatureAccumulatorV2*> accumulators;
        accumulators.reserve(num_accumulators);
        
        // 创建Accumulator
        for (size_t i = 0; i < num_accumulators; ++i) {
            DistinctFeatureAccumulatorV2* acc = new DistinctFeatureAccumulatorV2();
            if (acc->init(_conf["valid_distinct"]["feature"][0])) {
                accumulators.push_back(acc);
            } else {
                delete acc;
            }
        }
        
        // 执行大量update操作
        auto test_data = generate_hour_window_test_data(updates_per_acc);
        for (size_t i = 0; i < accumulators.size(); ++i) {
            for (size_t j = 0; j < test_data.size(); ++j) {
                FeatureValueProto fea_copy = test_data[j];
                accumulators[i]->update_and_query(&fea_copy);
            }
        }
        
        size_t rss_after = get_rss_memory();
        auto total_end = std::chrono::high_resolution_clock::now();
        
        result.rss_growth_kb = (rss_after > rss_before) ? (rss_after - rss_before) : 0;
        result.total_time_ms = std::chrono::duration_cast<std::chrono::milliseconds>(total_end - total_start).count();
        result.test_successful = (accumulators.size() == num_accumulators);
        
        // 清理资源
        for (size_t i = 0; i < accumulators.size(); ++i) {
            delete accumulators[i];
        }
        
        return result;
    }
};

/**
 * @brief V1版本大规模内存测试 - 重度累积场景
 */
TEST_F(DistinctAccumulatorV1MemoryTest, test_v1_heavy_accumulation_memory) {
    const size_t num_accumulators = 100;      // 少量Accumulator
    const size_t updates_per_acc = 10000;     // 大量数据累积
    
    auto result = benchmark_v1_performance(num_accumulators, updates_per_acc);
    
    EXPECT_TRUE(result.test_successful);
    std::cout << "V1 Heavy Accumulation: " << num_accumulators << " accumulators, " 
              << updates_per_acc << " updates each" << std::endl;
    std::cout << "  Memory: " << result.rss_growth_kb << " KB (" 
              << (result.rss_growth_kb / 1024) << " MB)" << std::endl;
    std::cout << "  Time: " << result.total_time_ms << " ms" << std::endl;
    std::cout << "  Avg per accumulator: " << (result.rss_growth_kb / num_accumulators) 
              << " KB" << std::endl;
}

/**
 * @brief V2版本大规模内存测试 - 重度累积场景
 */
TEST_F(DistinctAccumulatorV2MemoryTest, test_v2_heavy_accumulation_memory) {
    const size_t num_accumulators = 100;      // 少量Accumulator
    const size_t updates_per_acc = 10000;     // 大量数据累积
    
    auto result = benchmark_v2_performance(num_accumulators, updates_per_acc);
    
    EXPECT_TRUE(result.test_successful);
    std::cout << "V2 Heavy Accumulation: " << num_accumulators << " accumulators, " 
              << updates_per_acc << " updates each" << std::endl;
    std::cout << "  Memory: " << result.rss_growth_kb << " KB (" 
              << (result.rss_growth_kb / 1024) << " MB)" << std::endl;
    std::cout << "  Time: " << result.total_time_ms << " ms" << std::endl;
    std::cout << "  Avg per accumulator: " << (result.rss_growth_kb / num_accumulators) 
              << " KB" << std::endl;
}

/**
 * @brief V1版本超重度累积内存测试 - 模拟长时间运行
 */
TEST_F(DistinctAccumulatorV1MemoryTest, test_v1_extreme_accumulation_memory) {
    const size_t num_accumulators = 50;       // 更少Accumulator
    const size_t updates_per_acc = 50000;     // 超大量数据累积
    
    auto result = benchmark_v1_performance(num_accumulators, updates_per_acc);
    
    EXPECT_TRUE(result.test_successful);
    std::cout << "V1 Extreme Accumulation: " << num_accumulators << " accumulators, " 
              << updates_per_acc << " updates each" << std::endl;
    std::cout << "  Memory: " << result.rss_growth_kb << " KB (" 
              << (result.rss_growth_kb / 1024) << " MB)" << std::endl;
    std::cout << "  Time: " << result.total_time_ms << " ms" << std::endl;
    std::cout << "  Avg per accumulator: " << (result.rss_growth_kb / num_accumulators) 
              << " KB" << std::endl;
}

/**
 * @brief V2版本超重度累积内存测试 - 模拟长时间运行
 */
TEST_F(DistinctAccumulatorV2MemoryTest, test_v2_extreme_accumulation_memory) {
    const size_t num_accumulators = 50;       // 更少Accumulator
    const size_t updates_per_acc = 50000;     // 超大量数据累积
    
    auto result = benchmark_v2_performance(num_accumulators, updates_per_acc);
    
    EXPECT_TRUE(result.test_successful);
    std::cout << "V2 Extreme Accumulation: " << num_accumulators << " accumulators, " 
              << updates_per_acc << " updates each" << std::endl;
    std::cout << "  Memory: " << result.rss_growth_kb << " KB (" 
              << (result.rss_growth_kb / 1024) << " MB)" << std::endl;
    std::cout << "  Time: " << result.total_time_ms << " ms" << std::endl;
    std::cout << "  Avg per accumulator: " << (result.rss_growth_kb / num_accumulators) 
              << " KB" << std::endl;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti