// Copyright (c) 2025 Baidu.com, Inc. All Rights Reserved
// @Author: <PERSON> (<EMAIL>)
// 
// @Brief: DistinctFeatureAccumulatorV2 单元测试

#include <vector>
#include <gtest/gtest.h>
#include "boost/lexical_cast.hpp"
#include "distinct_feature_accumulator_v2.h"
#include "posix_themis_io.h"
#include "ckpt_test_util.h"

namespace anti {
namespace themis {
namespace feature_lib {

DECLARE_bool(need_set_distinct_data_view_signs);
DECLARE_bool(enable_mem_pool_for_acc);

class DistinctFeatureAccumulatorV2TestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
        ASSERT_TRUE(_conf.load("./conf", "accumulator_test.conf") == 0);

        int64_t coords[] = {1L, 2L, 3L, 4L, 5L};
        for (uint32_t i = 0U; i < 5U; ++i) {
            FeatureValueProto fea;
            fea.set_feature_id(140LU);
            fea.set_view_sign(123456LU);
            fea.set_feature_type(FeatureValueProto::DISTINCT);
            fea.set_joinkey(119LU);
            fea.set_log_id(120LU);
            fea.set_log_time(1111111111LU);
            fea.set_coord(coords[i]);
            fea.set_data_view_sign(222222LU + i);
            _feas.push_back(fea);
        }
        _old_fea = _feas[0];
        _old_fea.set_coord(0L);

        FeatureValueProto fea;
        fea.set_feature_id(123456789LU);
        _inv_feas.push_back(fea);

        fea.set_feature_id(140LU);
        fea.set_feature_type(FeatureValueProto::RATIO);
        _inv_feas.push_back(fea);
        
        // 确保V2版本被启用
        FLAGS_enable_mem_pool_for_acc = true;
    }
    
    virtual void TearDown() {
        _feas.clear();
        _inv_feas.clear();
    }

private:
    typedef anti::themis::common_lib::DistinctItemV2 DistinctItem;
    DistinctFeatureAccumulatorV2 _obj;
    comcfg::Configure _conf;

    std::vector<FeatureValueProto> _feas;
    FeatureValueProto _old_fea;
    std::vector<FeatureValueProto> _inv_feas;
};

TEST_F(DistinctFeatureAccumulatorV2TestSuite, construction_case) {
    EXPECT_EQ(0LU, _obj.feature_id());
    EXPECT_EQ(0LU, _obj._version);
}

TEST_F(DistinctFeatureAccumulatorV2TestSuite, init_case) {
    uint32_t num = _conf["invalid_distinct"]["feature"].size();
    ASSERT_LE(1U, num);
    for (uint32_t i = 0U; i < num; ++i) {
        EXPECT_FALSE(_obj.init(_conf["invalid_distinct"]["feature"][i]));
        _obj.uninit();
    }

    num = _conf["valid_distinct"]["feature"].size();
    ASSERT_LE(1U, num);
    ASSERT_TRUE(_obj.init(_conf["valid_distinct"]["feature"][0]));
    EXPECT_EQ(140LU, _obj.feature_id());
    EXPECT_EQ(1LU, _obj._version);
    _obj.uninit();

    EXPECT_EQ(0LU, _obj.feature_id());
    EXPECT_EQ(0LU, _obj._version);
}

TEST_F(DistinctFeatureAccumulatorV2TestSuite, test_update_old_node) {
    ASSERT_TRUE(_obj.init(_conf["valid_distinct"]["feature"][0]));
    ASSERT_TRUE(_obj.update(_feas[4]));
    const DistinctItem* item = _obj._pool_window.query_segment(
            static_cast<uint64_t>(_feas[4].view_sign()));
    
    ASSERT_FALSE(_obj.update(_old_fea));
    ASSERT_EQ(1U, item->distinct_num());
    FeatureValueProto feat = _feas[4];
    ASSERT_TRUE(_obj.query(&feat));
    EXPECT_EQ(1U, boost::lexical_cast<int64_t>(feat.value()));
    _obj.uninit();
}

TEST_F(DistinctFeatureAccumulatorV2TestSuite, test_update_slide_case) {
    ASSERT_TRUE(_obj.init(_conf["valid_distinct"]["feature"][0]));
    // invalid fea
    EXPECT_FALSE(_obj.query(NULL));

    for (uint32_t i = 0U; i< _inv_feas.size(); ++i) {
        EXPECT_FALSE(_obj.update(_inv_feas[i]));
        EXPECT_FALSE(_obj.query(&_inv_feas[i]));
    }
    
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(_feas[i]));
    }
    
    ASSERT_FALSE(_obj.update(_old_fea));
    FeatureValueProto feat = _feas[4];
    ASSERT_TRUE(_obj.query(&feat));
    EXPECT_EQ(3U, boost::lexical_cast<int64_t>(feat.value()));

    std::vector<FeatureValueProto> other_feas;
    for (uint32_t i = 2U; i < _feas.size(); ++i) {
        feat = _feas[i];
        feat.set_data_view_sign(33333LU + i);
        other_feas.push_back(feat);
    }
    feat = _feas[4];
    for (uint32_t i = 0U; i < other_feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(other_feas[i]));
        ASSERT_TRUE(_obj.query(&feat));
        EXPECT_EQ(3U + i + 1U, boost::lexical_cast<int64_t>(feat.value()));
    }

    for (uint32_t i = 2U; i < other_feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(other_feas[i]));
        ASSERT_TRUE(_obj.query(&feat));
        EXPECT_EQ(6U, boost::lexical_cast<int64_t>(feat.value()));
    }
    
    ASSERT_FALSE(_obj.update(_old_fea));
    // query
    feat = _feas[4];
    ASSERT_TRUE(_obj.query(&feat));
    EXPECT_EQ(6U, boost::lexical_cast<int64_t>(feat.value()));
    _obj.uninit();
}

TEST_F(DistinctFeatureAccumulatorV2TestSuite, test_update_and_query) {
    ASSERT_TRUE(_obj.init(_conf["valid_distinct"]["feature"][0]));
    // invalid fea
    EXPECT_FALSE(_obj.query(NULL));

    for (uint32_t i = 0U; i< _inv_feas.size(); ++i) {
        EXPECT_FALSE(_obj.update_and_query(&_inv_feas[i]));
    }
    
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(_feas[i]));
    }
    
    ASSERT_FALSE(_obj.update(_old_fea));
    FeatureValueProto feat = _feas[4];
    ASSERT_TRUE(_obj.query(&feat));
    EXPECT_EQ(3U, boost::lexical_cast<int64_t>(feat.value()));

    std::vector<FeatureValueProto> other_feas;
    for (uint32_t i = 2U; i < _feas.size(); ++i) {
        feat = _feas[i];
        feat.set_data_view_sign(33333LU + i);
        other_feas.push_back(feat);
    }
    feat = _feas[1];
    for (uint32_t i = 0U; i < other_feas.size(); ++i) {
        ASSERT_TRUE(_obj.update_and_query(&other_feas[i]));
        EXPECT_EQ(3U + i + 1U, boost::lexical_cast<int64_t>(other_feas[i].value()));
    }

    for (uint32_t i = 0U; i < other_feas.size(); ++i) {
        ASSERT_TRUE(_obj.update_and_query(&other_feas[i]));
        EXPECT_EQ(6U, boost::lexical_cast<int64_t>(other_feas[i].value()));
    }
    
    ASSERT_FALSE(_obj.update(_old_fea));
    // query
    feat = _feas[4];
    ASSERT_TRUE(_obj.query(&feat));
    EXPECT_EQ(6U, boost::lexical_cast<int64_t>(feat.value()));
    _obj.uninit();
}

TEST_F(DistinctFeatureAccumulatorV2TestSuite, test_dump_and_load) {
    ASSERT_TRUE(_obj.init(_conf["valid_distinct"]["feature"][0]));

    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(_feas[i]));
    }
    
    FeatureValueProto feat = _feas[4];
    ASSERT_TRUE(_obj.query(&feat));
    EXPECT_EQ(3U, boost::lexical_cast<int64_t>(feat.value()));
    std::string type = "dist_v2";
    DOING_DUMP();
    _obj.uninit();
    ASSERT_TRUE(_obj.init(_conf["valid_distinct"]["feature"][0]));
    DOING_LOAD();
    FeatureValueProto feat2 = _feas[4];
    ASSERT_TRUE(_obj.query(&feat2));
    EXPECT_EQ(3U, boost::lexical_cast<int64_t>(feat2.value()));
    _obj.uninit();
}

TEST_F(DistinctFeatureAccumulatorV2TestSuite, test_query_ext_fea) {
    ASSERT_TRUE(_obj.init(_conf["valid_distinct"]["feature"][0]));
    // case: input invalid
    FValQRequestProto request;
    ASSERT_FALSE(_obj.query(request, NULL));
    // case:  query from window fail
    request.set_view_sign(0U);
    FValQResponseProto response;
    ASSERT_TRUE(_obj.query(request, &response));
    EXPECT_EQ(0, response.value_size());
    
    ASSERT_TRUE(_obj.update(_feas[4]));
    // case: succ
    FValQRequestProto request1;
    request1.set_view_sign(_feas[4].view_sign());
    ASSERT_TRUE(_obj.query(request1, &response));
    const DistinctItem* item = _obj._pool_window.query_segment(
            static_cast<uint64_t>(_feas[4].view_sign()));
    std::string exp(boost::lexical_cast<std::string>(item->distinct_num()));
    ASSERT_EQ(1, response.value_size());
    EXPECT_STREQ(exp.data(), response.value(0).data());
}

TEST_F(DistinctFeatureAccumulatorV2TestSuite, test_query_ext_fea_with_data_view_signs) {
    ASSERT_TRUE(_obj.init(_conf["valid_distinct"]["feature"][0]));
    ASSERT_TRUE(_obj.update(_feas[4]));
    FeatureValueProto feat = _feas[4];
    // close flag
    ASSERT_TRUE(_obj.query(&feat));
    ASSERT_EQ(0, feat.data_view_signs_size());

    // open flag
    FLAGS_need_set_distinct_data_view_signs = true;
    ASSERT_TRUE(_obj.update(_feas[4]));
    ASSERT_TRUE(_obj.query(&feat));
    ASSERT_EQ(1, feat.data_view_signs_size());
}

TEST_F(DistinctFeatureAccumulatorV2TestSuite, test_memory_pool_functionality) {
    ASSERT_TRUE(_obj.init(_conf["valid_distinct"]["feature"][0]));
    
    // 测试内存池功能：在同一个view_sign下添加大量不同的data_view_sign
    // 这样可以测试内存池而不受滑动窗口时间限制影响
    const uint64_t test_view_sign = 123456LU;
    const int64_t base_coord = 100L;
    std::vector<FeatureValueProto> pool_test_feas;
    
    for (uint32_t i = 0; i < 1000; ++i) {
        FeatureValueProto fea;
        fea.set_feature_id(140LU);
        fea.set_view_sign(test_view_sign);  // 同一个view_sign
        fea.set_feature_type(FeatureValueProto::DISTINCT);
        fea.set_joinkey(119LU);
        fea.set_log_id(120LU);
        fea.set_log_time(1111111111LU);
        fea.set_coord(base_coord);  // 同一个coord，在窗口范围内
        fea.set_data_view_sign(500000LU + i);  // 不同的data_view_sign
        pool_test_feas.push_back(fea);
    }
    
    // 批量更新同一个view_sign的多个data_view_sign
    for (const auto& fea : pool_test_feas) {
        ASSERT_TRUE(_obj.update(fea));
    }
    
    // 查询结果，应该能看到1000个不同的data_view_sign
    FeatureValueProto query_fea;
    query_fea.set_feature_id(140LU);
    query_fea.set_view_sign(test_view_sign);
    query_fea.set_feature_type(FeatureValueProto::DISTINCT);
    query_fea.set_coord(base_coord);
    
    ASSERT_TRUE(_obj.query(&query_fea));
    EXPECT_EQ(1000, boost::lexical_cast<int64_t>(query_fea.value()));
    
    _obj.uninit();
}

TEST_F(DistinctFeatureAccumulatorV2TestSuite, test_monitor_log) {
    ASSERT_TRUE(_obj.init(_conf["valid_distinct"]["feature"][0]));
    
    // 添加一些数据
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(_feas[i]));
    }
    
    // 调用监控日志函数，应该不会崩溃
    _obj.print_monitor_log();
    
    _obj.uninit();
}

// test_thread_safety 暂时注释 - 需要确认内存池的线程安全性
// 在确保GCSlabMempool32和PoolMiniHashMap完全线程安全之前，此测试可能会死锁
/*
TEST_F(DistinctFeatureAccumulatorV2TestSuite, test_thread_safety) {
    // TODO: 需要先确认底层内存池的线程安全性
    // 如果GCSlabMempool32不是线程安全的，此测试会导致死锁或数据竞争
}
*/

TEST_F(DistinctFeatureAccumulatorV2TestSuite, test_compatibility_with_original) {
    // 测试V2版本与原版的兼容性
    ASSERT_TRUE(_obj.init(_conf["valid_distinct"]["feature"][0]));
    
    // 使用与原版测试完全相同的数据和操作序列
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(_feas[i]));
    }
    
    FeatureValueProto feat = _feas[4];
    ASSERT_TRUE(_obj.query(&feat));
    EXPECT_EQ(3U, boost::lexical_cast<int64_t>(feat.value()));
    
    // 测试序列化兼容性
    std::string type = "dist_v2_compat";
    DOING_DUMP();
    _obj.uninit();
    
    // 重新初始化并加载
    ASSERT_TRUE(_obj.init(_conf["valid_distinct"]["feature"][0]));
    DOING_LOAD();
    
    // 验证加载后的结果与原版一致
    FeatureValueProto feat2 = _feas[4];
    ASSERT_TRUE(_obj.query(&feat2));
    EXPECT_EQ(3U, boost::lexical_cast<int64_t>(feat2.value()));
    
    _obj.uninit();
}

TEST_F(DistinctFeatureAccumulatorV2TestSuite, test_edge_cases) {
    ASSERT_TRUE(_obj.init(_conf["valid_distinct"]["feature"][0]));
    
    // 测试边界情况
    
    // 1. 空的data_view_sign
    FeatureValueProto empty_data_fea;
    empty_data_fea.set_feature_id(140LU);
    empty_data_fea.set_view_sign(123456LU);
    empty_data_fea.set_feature_type(FeatureValueProto::DISTINCT);
    empty_data_fea.set_coord(1);
    empty_data_fea.set_data_view_sign(0);  // 空的data_view_sign
    
    ASSERT_TRUE(_obj.update(empty_data_fea));
    FeatureValueProto query_fea = empty_data_fea;
    ASSERT_TRUE(_obj.query(&query_fea));
    // V2版本与原版保持一致：data_view_sign为0也会被计入distinct_num
    EXPECT_EQ(1U, boost::lexical_cast<int64_t>(query_fea.value()));
    
    // 2. 相同的data_view_sign重复更新
    FeatureValueProto repeat_fea;
    repeat_fea.set_feature_id(140LU);
    repeat_fea.set_view_sign(654321LU);
    repeat_fea.set_feature_type(FeatureValueProto::DISTINCT);
    repeat_fea.set_coord(1);
    repeat_fea.set_data_view_sign(888888LU);
    
    for (int i = 0; i < 10; ++i) {
        ASSERT_TRUE(_obj.update(repeat_fea));
    }
    
    FeatureValueProto repeat_query = repeat_fea;
    ASSERT_TRUE(_obj.query(&repeat_query));
    EXPECT_EQ(1U, boost::lexical_cast<int64_t>(repeat_query.value()));  // 仍然只有1个distinct
    
    // 3. 测试coord不匹配的查询
    FeatureValueProto coord_mismatch_fea;
    coord_mismatch_fea.set_feature_id(140LU);
    coord_mismatch_fea.set_view_sign(777777LU);
    coord_mismatch_fea.set_feature_type(FeatureValueProto::DISTINCT);
    coord_mismatch_fea.set_coord(50);  // 更新时使用coord=50
    coord_mismatch_fea.set_data_view_sign(999999LU);
    
    ASSERT_TRUE(_obj.update(coord_mismatch_fea));
    
    // 用不同的coord查询应该失败
    FeatureValueProto wrong_coord_query;
    wrong_coord_query.set_feature_id(140LU);
    wrong_coord_query.set_view_sign(777777LU);
    wrong_coord_query.set_feature_type(FeatureValueProto::DISTINCT);
    wrong_coord_query.set_coord(0);  // 查询时使用coord=0，应该查不到
    
    EXPECT_FALSE(_obj.query(&wrong_coord_query));  // 应该查询失败
    
    // 用正确的coord查询应该成功
    FeatureValueProto correct_coord_query;
    correct_coord_query.set_feature_id(140LU);
    correct_coord_query.set_view_sign(777777LU);
    correct_coord_query.set_feature_type(FeatureValueProto::DISTINCT);
    correct_coord_query.set_coord(50);  // 使用正确的coord
    
    ASSERT_TRUE(_obj.query(&correct_coord_query));
    EXPECT_EQ(1U, boost::lexical_cast<int64_t>(correct_coord_query.value()));
    
    _obj.uninit();
}

TEST_F(DistinctFeatureAccumulatorV2TestSuite, test_serialization_and_coord_consistency) {
    ASSERT_TRUE(_obj.init(_conf["valid_distinct"]["feature"][0]));
    
    // 创建不同coord的测试数据
    std::vector<FeatureValueProto> coord_test_feas;
    for (int i = 0; i < 5; ++i) {
        FeatureValueProto fea;
        fea.set_feature_id(140LU);
        fea.set_view_sign(555555LU);
        fea.set_feature_type(FeatureValueProto::DISTINCT);
        fea.set_coord(100 + i * 10);  // coord: 100, 110, 120, 130, 140
        fea.set_data_view_sign(600000LU + i);
        coord_test_feas.push_back(fea);
    }
    
    // 添加数据
    for (const auto& fea : coord_test_feas) {
        ASSERT_TRUE(_obj.update(fea));
    }
    
    // 验证每个coord的查询都能成功
    for (const auto& fea : coord_test_feas) {
        FeatureValueProto query_fea = fea;
        ASSERT_TRUE(_obj.query(&query_fea));
        // 每个coord都应该只有1个distinct值
        EXPECT_EQ(1U, boost::lexical_cast<int64_t>(query_fea.value()));
    }
    
    // 序列化
    std::string type = "coord_test";
    DOING_DUMP();
    _obj.uninit();
    
    // 重新初始化并加载
    ASSERT_TRUE(_obj.init(_conf["valid_distinct"]["feature"][0]));
    DOING_LOAD();
    
    // 验证序列化后每个coord的查询仍然正确
    for (const auto& fea : coord_test_feas) {
        FeatureValueProto query_fea = fea;
        ASSERT_TRUE(_obj.query(&query_fea));
        EXPECT_EQ(1U, boost::lexical_cast<int64_t>(query_fea.value()));
    }
    
    _obj.uninit();
}

TEST_F(DistinctFeatureAccumulatorV2TestSuite, test_memory_pool_stress) {
    ASSERT_TRUE(_obj.init(_conf["valid_distinct"]["feature"][0]));
    
    // 创建大量数据来压测内存池
    const int NUM_VIEW_SIGNS = 100;
    const int NUM_DATA_VIEW_SIGNS_PER_VIEW = 50;
    const int64_t BASE_COORD = 1000;
    
    std::vector<FeatureValueProto> stress_test_data;
    
    for (int view_i = 0; view_i < NUM_VIEW_SIGNS; ++view_i) {
        for (int data_i = 0; data_i < NUM_DATA_VIEW_SIGNS_PER_VIEW; ++data_i) {
            FeatureValueProto fea;
            fea.set_feature_id(140LU);
            fea.set_view_sign(800000LU + view_i);  // 不同的view_sign
            fea.set_feature_type(FeatureValueProto::DISTINCT);
            fea.set_coord(BASE_COORD);  // 相同的coord
            fea.set_data_view_sign(900000LU + view_i * NUM_DATA_VIEW_SIGNS_PER_VIEW + data_i);
            stress_test_data.push_back(fea);
        }
    }
    
    // 批量更新
    for (const auto& fea : stress_test_data) {
        ASSERT_TRUE(_obj.update(fea));
    }
    
    // 验证每个view_sign都有正确的distinct数量
    for (int view_i = 0; view_i < NUM_VIEW_SIGNS; ++view_i) {
        FeatureValueProto query_fea;
        query_fea.set_feature_id(140LU);
        query_fea.set_view_sign(800000LU + view_i);
        query_fea.set_feature_type(FeatureValueProto::DISTINCT);
        query_fea.set_coord(BASE_COORD);
        
        ASSERT_TRUE(_obj.query(&query_fea));
        EXPECT_EQ(NUM_DATA_VIEW_SIGNS_PER_VIEW, boost::lexical_cast<int64_t>(query_fea.value()));
    }
    
    // 测试监控功能
    _obj.print_monitor_log();
    
    _obj.uninit();
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */