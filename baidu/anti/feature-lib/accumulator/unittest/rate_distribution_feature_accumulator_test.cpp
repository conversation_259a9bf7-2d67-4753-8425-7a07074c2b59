// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: rate_distribution_feature_accumulator_test.cpp
// @Last modified: 2017-03-16 14:56:24
// @Brief: 

#include <float.h>
#include <vector>
#include <gtest/gtest.h>
#include "rate_distribution_feature_accumulator.h"
#include "posix_themis_io.h" 
#include "ckpt_test_util.h"

namespace anti {
namespace themis {
namespace feature_lib {

class RateDistributionFeatureAccumulatorTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
        ASSERT_TRUE(_conf.load("./conf", "accumulator_test.conf") == 0);
        for (uint32_t i = 0U; i < 6U; ++i) {
            FeatureValueProto fea;
            fea.set_feature_id(888LU);
            fea.set_view_sign(123456LU);
            fea.set_filter_count(i + 1);
            fea.set_feature_type(FeatureValueProto::CPM_DISTRIBUTION);
            fea.set_joinkey(119LU);
            fea.set_log_id(120LU);
            fea.set_log_time(1111111111LU);
            fea.set_coord(i);
            fea.set_data_view_sign(321LU);
            fea.set_in_filter(true);
            fea.set_in_refer(true);
            _feas.push_back(fea);
        }
        _inv_fea = _feas[0];
        _inv_fea.clear_data_view_sign();
    }
    virtual void TearDown() {}

private:
    RateDistributionFeatureAccumulator _obj;
    comcfg::Configure _conf;

    std::vector<FeatureValueProto> _feas;
    FeatureValueProto _inv_fea;
};

TEST_F(RateDistributionFeatureAccumulatorTestSuite, construction_case) {
    EXPECT_EQ(0LU, _obj._feature_id);
    EXPECT_EQ(0LU, _obj._version);
    EXPECT_EQ(0L, _obj._time_threshold);
    EXPECT_EQ(0L, _obj._remain[RateDistributionFeatureAccumulator::NUM]);
    EXPECT_EQ(0L, _obj._remain[RateDistributionFeatureAccumulator::DEN]);
}

TEST_F(RateDistributionFeatureAccumulatorTestSuite, init_case) {
    for (uint32_t i = 0U; i < _conf["invalid_session"]["feature"].size(); ++i) {
        EXPECT_FALSE(_obj.init(_conf["invalid_session"]["feature"][i]));
        _obj.uninit();
    }
    ASSERT_TRUE(_obj.init(_conf["valid_session"]["feature"]));
    EXPECT_EQ(888LU, _obj.feature_id());
    EXPECT_EQ(1LU, _obj._version);
    EXPECT_EQ(2L, _obj._remain[RateDistributionFeatureAccumulator::NUM]);
    EXPECT_EQ(3L, _obj._remain[RateDistributionFeatureAccumulator::DEN]);
    EXPECT_EQ(3, _obj._time_threshold);
    ASSERT_EQ(2, _obj._intervals.size());
    EXPECT_DOUBLE_EQ(0, _obj._intervals[0].first);
    EXPECT_DOUBLE_EQ(2, _obj._intervals[0].second);
    EXPECT_DOUBLE_EQ(2, _obj._intervals[1].first);
    EXPECT_DOUBLE_EQ(DBL_MAX, _obj._intervals[1].second);
    _obj.uninit();
}

TEST_F(RateDistributionFeatureAccumulatorTestSuite, _check_feature_fail) {
    _obj._feature_id = 888LU;
    ASSERT_FALSE(_obj._check_feature(_inv_fea));
    
    _inv_fea.clear_filter_count();
    ASSERT_FALSE(_obj._check_feature(_inv_fea));

    _inv_fea.set_in_filter(false);
    _inv_fea.set_in_refer(false);
    ASSERT_FALSE(_obj._check_feature(_inv_fea));

    _inv_fea.set_feature_id(111LU);
    ASSERT_FALSE(_obj._check_feature(_inv_fea));
}

TEST_F(RateDistributionFeatureAccumulatorTestSuite, multi_update_and_one_query_case) {
    ASSERT_TRUE(_obj.init(_conf["valid_session"]["feature"]));
    ASSERT_FALSE(_obj.update(_inv_fea));
    ASSERT_FALSE(_obj.query(NULL));

    for (uint32_t i = 0; i < 6; ++i) {
        ASSERT_TRUE(_obj.update(_feas[i]));
    }

    ASSERT_TRUE(_obj.query(&_feas[5]));
    EXPECT_TRUE(_feas[5].has_bucket_idx());
    EXPECT_EQ(1U, _feas[5].bucket_idx());
    int64_t ec[] = {0L, 1L};
    for (uint32_t i = 0; i < 2; ++i) {
        EXPECT_EQ(i, _feas[5].buckets(i).idx());
        EXPECT_EQ(ec[i], _feas[5].buckets(i).count());
    }
    _obj.uninit();
}

TEST_F(RateDistributionFeatureAccumulatorTestSuite, update_and_query_case) {
    ASSERT_TRUE(_obj.init(_conf["valid_session"]["feature"]));
    ASSERT_FALSE(_obj.update_and_query(NULL));
    FeatureValueProto bak = _feas[5];
    for (uint32_t i = 0; i < 6; ++i) {
        ASSERT_TRUE(_obj.update_and_query(&_feas[i]));
    }
    bool has_idx[] = {false, true, true, true, true, true};
    uint32_t buc_idx[] = {0U, 0U, 0U, 1U, 1U, 1U};
    int64_t count[][2] = {{0L, 0L}, {1L, 0L}, {1L, 0L}, {0L, 1L}, {0L, 1L}, {0L, 1L}};
    for (uint32_t i = 0; i < 6; ++i) {
        EXPECT_EQ(has_idx[i], _feas[i].has_bucket_idx());
        if (has_idx[i]) {
            EXPECT_EQ(buc_idx[i], _feas[i].bucket_idx());
            ASSERT_EQ(2U, _feas[i].buckets_size());
            for (uint32_t bi = 0; bi < 2; ++bi) {
                EXPECT_EQ(bi, _feas[i].buckets(bi).idx());
                EXPECT_EQ(count[i][bi], _feas[i].buckets(bi).count());
            }
        }
    }

    {
        FeatureValueProto t = bak;
        t.set_data_view_sign(333LU);
        ASSERT_TRUE(_obj.update_and_query(&t));
        EXPECT_TRUE(t.has_bucket_idx());
        EXPECT_EQ(1U, t.bucket_idx());
        int64_t ec[] = {0L, 2L};
        for (uint32_t i = 0; i < 2; ++i) {
            EXPECT_EQ(i, t.buckets(i).idx());
            EXPECT_EQ(ec[i], t.buckets(i).count());
        }
    }

    {
        FeatureValueProto t = bak;
        t.set_coord(1000);
        t.set_in_filter(false);
        ASSERT_TRUE(_obj.update_and_query(&t));
        EXPECT_FALSE(t.has_bucket_idx());

        t.set_in_filter(true);
        t.set_in_refer(false);
        ASSERT_TRUE(_obj.update_and_query(&t));
        EXPECT_TRUE(t.has_bucket_idx());
        int64_t ec[] = {0L, 1L};
        for (uint32_t i = 0; i < 2; ++i) {
            EXPECT_EQ(i, t.buckets(i).idx());
            EXPECT_EQ(ec[i], t.buckets(i).count());
        }
    }
    _obj.uninit();
}

TEST_F(RateDistributionFeatureAccumulatorTestSuite, _get_bucket_idx_case) {
    _obj._intervals.push_back(std::pair<double, double>(0, 5));
    EXPECT_EQ(0L, _obj._get_bucket_idx(5));
    EXPECT_EQ(-1L, _obj._get_bucket_idx(6));
}

TEST_F(RateDistributionFeatureAccumulatorTestSuite, _update_segment_case) {
    ASSERT_TRUE(_obj.init(_conf["valid_session"]["feature"]));
    FeatureValueProto f = _feas[0];
    f.set_feature_type(FeatureValueProto::RATE_DISTRIBUTION);
    ASSERT_TRUE(_obj._update_segment(f));
    const RateDistributionFeatureAccumulator::SegmentItem* r =
            _obj._swindows[0].query_segment(f.data_view_sign());
    ASSERT_TRUE(r != NULL);
    EXPECT_EQ(1L, r->cumulant());
    _obj.uninit();
}

TEST_F(RateDistributionFeatureAccumulatorTestSuite, dump_load_sliding_window_case) {
    ASSERT_TRUE(_obj.init(_conf["valid_session"]["feature"]));
    for (uint32_t i = 0U; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.update(_feas[i]));
    }
    DOING_DUMP();
    _obj.uninit();
    
    auto feas_5 = _feas[5];
    int64_t ec[] = {0L, 1L};
    ASSERT_TRUE(_obj.init(_conf["valid_session"]["feature"]));
    DOING_LOAD();
    ASSERT_TRUE(_obj.query(&feas_5));
    EXPECT_EQ(1U, feas_5.bucket_idx());
    ASSERT_EQ(2U, feas_5.buckets_size());
    for (uint32_t i = 0; i < 2; ++i) {
        EXPECT_EQ(i, feas_5.buckets(i).idx());
        EXPECT_EQ(ec[i], feas_5.buckets(i).count());
    }
    _obj.uninit();
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

