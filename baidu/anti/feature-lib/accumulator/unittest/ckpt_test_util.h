// Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
// @Author: ch<PERSON><PERSON><PERSON><PERSON>(chen<PERSON><PERSON><EMAIL>)
// 
// @File: ckpt_test_util.h
// @Last modified: 2017-12-27 14:36:28
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_UNITTEST_CKPT_TEST_UTIL_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_UNITTEST_CKPT_TEST_UTIL_H

#include "posix_io_interface.h"
#include "posix_themis_io.h"
#include <memory>
#include <string>

namespace anti {
namespace themis {
namespace feature_lib {

#ifndef DOING_DUMP
#define DOING_DUMP(obj)    \
do {    \
    PosixIoWriterInterface *writer = new(std::nothrow) PosixThemisIoWriter();    \
    ASSERT_TRUE(writer != NULL);    \
    remove("./ckpt.dat");    \
    ASSERT_EQ(writer->open("./ckpt.dat"), 0);    \
    ASSERT_TRUE(_obj.dump(writer));    \
    ASSERT_EQ(writer->close(), 0);     \
    delete writer;    \
} while (0)
#endif

#ifndef DOING_LOAD
#define DOING_LOAD()    \
do {    \
    PosixIoReaderInterface *reader = new(std::nothrow) PosixThemisIoReader();    \
    ASSERT_EQ(reader->open("./ckpt.dat"), 0);    \
    ASSERT_TRUE(_obj.load(reader));    \
    ASSERT_EQ(reader->close(), 0);    \
    delete reader;    \
    remove("./ckpt.dat");    \
} while (0)
#endif

// 扩展宏 - 支持动态文件名，用于兼容性测试
#ifndef DOING_DUMP_VAR
#define DOING_DUMP_VAR(obj)    \
do {    \
    PosixIoWriterInterface *writer = new(std::nothrow) PosixThemisIoWriter();    \
    ASSERT_TRUE(writer != NULL);    \
    std::string filename = std::string("./ckpt_") + type + ".dat";    \
    remove(filename.c_str());    \
    ASSERT_EQ(writer->open(filename.c_str()), 0);    \
    ASSERT_TRUE(obj.dump(writer));    \
    ASSERT_EQ(writer->close(), 0);     \
    delete writer;    \
} while (0)
#endif

#ifndef DOING_LOAD_VAR
#define DOING_LOAD_VAR(obj)    \
do {    \
    PosixIoReaderInterface *reader = new(std::nothrow) PosixThemisIoReader();    \
    std::string filename = std::string("./ckpt_") + type + ".dat";    \
    ASSERT_EQ(reader->open(filename.c_str()), 0);    \
    ASSERT_TRUE(obj.load(reader));    \
    ASSERT_EQ(reader->close(), 0);    \
    delete reader;    \
    remove(filename.c_str());    \
} while (0)
#endif

} // feature_lib
} // themis
} // anti

#endif // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_ACCUMULATOR_UNITTEST_CKPT_TEST_UTIL_H


/* vim: set ts=4 sw=4: */

