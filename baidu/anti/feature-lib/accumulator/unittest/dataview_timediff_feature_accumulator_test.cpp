// Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved
// @brief 

#include <gtest/gtest.h>
#include <com_log.h>
#include "dataview_timediff_feature_accumulator.h"
#include "posix_themis_io.h" 
#include "ckpt_test_util.h"

namespace anti {
namespace themis {
namespace feature_lib {

const char* g_conf_path = "./conf";
const char* g_conf_file = "dataview_timediff_feature.conf";

const int32_t kDefaultFeatureId = 103;
const int64_t kMaxDiff = 0x1FFFFFFF;

FeatureValueProto mock_feature(uint64_t sign, uint64_t dataview, uint64_t time) {
    FeatureValueProto fea;
    fea.set_feature_type(FeatureValueProto::DATAVIEW_TIME_DIFF);
    fea.set_feature_id(kDefaultFeatureId);
    fea.set_view_sign(sign);
    fea.set_data_view_sign(dataview);
    fea.set_log_time(time);
    return fea;
}

class DataViewTDAccumulatorTestSuite : public ::testing::Test {
public:
    virtual void SetUp() {}
    virtual void TearDown() {}

private:
    DataViewTDAccumulator _obj;
};

TEST_F(DataViewTDAccumulatorTestSuite, init) {
    comcfg::Configure cfg;
    ASSERT_TRUE(cfg.load(g_conf_path, g_conf_file) == 0);

    auto& acc_conf = cfg["dataview_timediff"];

    ASSERT_TRUE(_obj.init(acc_conf));

    EXPECT_EQ(_obj._feature_id, 103);
    EXPECT_EQ(_obj._version, 1);
    EXPECT_EQ(_obj._range, 1000);
    EXPECT_EQ(_obj._max_dataview_size, 50);

}

TEST_F(DataViewTDAccumulatorTestSuite, insert_and_query) {
    _obj._goe_list.reset(
            new (std::nothrow) DataViewTDAccumulator::GOEList(100, 10));
    comcfg::Configure cfg;
    ASSERT_TRUE(cfg.load(g_conf_path, g_conf_file) == 0);

    auto& acc_conf = cfg["dataview_timediff"];
    _obj.init(acc_conf);

    // mock feature
    FeatureValueProto fea[] = {mock_feature(1, 2, 1900),
                               mock_feature(1, 3, 1000),
                               mock_feature(1, 4, 1000),
                               mock_feature(1, 2, 1100),
                               mock_feature(1, 2, 3200),
                               mock_feature(1, 3, 40800),
                               mock_feature(1, 4, 40200),
                               mock_feature(1, 2, 40400),
                               mock_feature(1, 3, 40400),
                               mock_feature(1, 2, 40000)};

    for (int32_t i = 0; i < 10; ++i) {
        ASSERT_TRUE(_obj.update(fea[i]));
    }

    int64_t target[] = {0, kMaxDiff, kMaxDiff, kMaxDiff, 2, 0, kMaxDiff, 0, kMaxDiff, 37};
    for (int32_t i = 0; i < 10; ++i) {
        ASSERT_TRUE(_obj.query(&fea[i]));
        EXPECT_EQ(target[i], fea[i].pre_feat_distance());
    }
}

TEST_F(DataViewTDAccumulatorTestSuite, insert_and_query_with_neighbor_feat_distances) {
    _obj._goe_list.reset(
            new (std::nothrow) DataViewTDAccumulator::GOEList(100, 10));
    _obj._feature_id = kDefaultFeatureId;
    _obj._range = 60;
    _obj._max_dataview_size = 10;

    // mock feature
    FeatureValueProto fea[] = {mock_feature(1, 2, 10000),
                               mock_feature(1, 2, 20000),
                               mock_feature(1, 2, 30000),
                               mock_feature(1, 3, 40000),
                               mock_feature(1, 2, 50000)};

    for (int32_t i = 0; i < 5; ++i) {
        ASSERT_TRUE(_obj.update(fea[i]));
    }

    int64_t target[] = {kMaxDiff, 10, 10, kMaxDiff, kMaxDiff};
    for (int32_t i = 0; i < 5; ++i) {
        ASSERT_TRUE(_obj.query(&fea[i]));
        EXPECT_EQ(target[i], fea[i].pre_feat_distance());
    }

    ASSERT_TRUE(fea[0].neighbor_feat_distances_size() == 0);
    ASSERT_TRUE(fea[1].neighbor_feat_distances_size() == 1);
    ASSERT_TRUE(fea[2].neighbor_feat_distances_size() == 2);
    ASSERT_TRUE(fea[3].neighbor_feat_distances_size() == 0);
    ASSERT_TRUE(fea[4].neighbor_feat_distances_size() == 0);
    ASSERT_TRUE(fea[1].neighbor_feat_distances(0) == 10);
    ASSERT_TRUE(fea[2].neighbor_feat_distances(0) == 10);
    ASSERT_TRUE(fea[2].neighbor_feat_distances(1) == 20);
}

// -- same sign, feature list too long --
TEST_F(DataViewTDAccumulatorTestSuite, insert_and_query_remove_very_old_feature) {
    _obj._goe_list.reset(
            new (std::nothrow) DataViewTDAccumulator::GOEList(100, 5));
    _obj._feature_id = kDefaultFeatureId;
    _obj._range = 600;
    _obj._max_dataview_size = 10;

    // mock feature
    FeatureValueProto fea[] = {mock_feature(1, 2, 10000),
                               mock_feature(1, 2, 11000),
                               mock_feature(1, 3, 20000),
                               mock_feature(1, 2, 40000),
                               mock_feature(1, 2, 30000),
                               mock_feature(1, 2, 120000)};

    for (int32_t i = 0; i < 5; ++i) {
        ASSERT_TRUE(_obj.update(fea[i]));
    }

    {
        int64_t target[] = {kMaxDiff, 1, kMaxDiff, 10, kMaxDiff};
        for (int32_t i = 0; i < 5; ++i) {
            ASSERT_TRUE(_obj.query(&fea[i]));
            EXPECT_EQ(target[i], fea[i].pre_feat_distance());
        }
    }

    ASSERT_TRUE(_obj.update(fea[5]));

    FeatureValueProto fea1[] = {mock_feature(1, 2, 10000),
                               mock_feature(1, 2, 11000),
                               mock_feature(1, 3, 20000),
                               mock_feature(1, 2, 40000),
                               mock_feature(1, 2, 30000),
                               mock_feature(1, 2, 120000)};

    {
        int64_t target[] = {kMaxDiff, kMaxDiff, kMaxDiff, 10, kMaxDiff, 80};
        for (int32_t i = 0; i < 6; ++i) {
            ASSERT_TRUE(_obj.query(&fea1[i]));
            EXPECT_EQ(target[i], fea1[i].pre_feat_distance());
        }
    }
    
}

TEST_F(DataViewTDAccumulatorTestSuite, insert_and_query_remove_very_old_feature_list) {
    _obj._goe_list.reset(
            new (std::nothrow) DataViewTDAccumulator::GOEList(100, 3));
    _obj._feature_id = kDefaultFeatureId;
     _obj._range = 600;
    _obj._max_dataview_size = 10;

    // mock feature
    FeatureValueProto fea[] = {mock_feature(1, 2, 1000),
                               mock_feature(1, 3, 3000),
                               mock_feature(1, 2, 10000),
                               mock_feature(1, 2, 1120000)};

    for (int32_t i = 0; i < 4; ++i) {
        ASSERT_TRUE(_obj.update(fea[i]));
    }

    ASSERT_TRUE(_obj.query(&fea[0]));
    ASSERT_TRUE(_obj.query(&fea[3]));

    EXPECT_EQ(fea[3].pre_feat_distance(), kMaxDiff); 
}

TEST_F(DataViewTDAccumulatorTestSuite, dump_and_load) {
    _obj._goe_list.reset(
            new (std::nothrow) DataViewTDAccumulator::GOEList(100, 3));
    _obj._feature_id = kDefaultFeatureId;
    _obj._version = 1;
     _obj._range = 600;
    _obj._max_dataview_size = 10;

    // mock feature
    FeatureValueProto fea[] = {mock_feature(1, 2, 1900),
                               mock_feature(1, 3, 1000),
                               mock_feature(1, 4, 1000),
                               mock_feature(1, 2, 1100),
                               mock_feature(1, 2, 3200),
                               mock_feature(1, 3, 40800),
                               mock_feature(1, 4, 40200),
                               mock_feature(1, 2, 40400),
                               mock_feature(1, 3, 40400),
                               mock_feature(1, 2, 40000)};
                               
    FeatureValueProto fea1[] = {mock_feature(1, 2, 1900),
                               mock_feature(1, 3, 1000),
                               mock_feature(1, 4, 1000),
                               mock_feature(1, 2, 1100),
                               mock_feature(1, 2, 3200),
                               mock_feature(1, 3, 40800),
                               mock_feature(1, 4, 40200),
                               mock_feature(1, 2, 40400),
                               mock_feature(1, 3, 40400),
                               mock_feature(1, 2, 40000)};
    for (int32_t i = 0; i < 10; ++i) {
        ASSERT_TRUE(_obj.update(fea[i]));
    }

    {
        int64_t target[] = {0, kMaxDiff, kMaxDiff, kMaxDiff, 2, 0, kMaxDiff, 0, kMaxDiff, 37};
        for (int32_t i = 0; i < 10; ++i) {
            ASSERT_TRUE(_obj.query(&fea[i]));
            EXPECT_EQ(target[i], fea[i].pre_feat_distance());
        }
    }

    DOING_DUMP();
    _obj.uninit();

    {
        int64_t target[] = {0, kMaxDiff, kMaxDiff, kMaxDiff, 2, 0, kMaxDiff, 0, kMaxDiff, 37};
        _obj.uninit();
        _obj._version = 1;
        _obj._goe_list.reset(
                new (std::nothrow) DataViewTDAccumulator::GOEList(100, 3));
        DOING_LOAD();
        ASSERT_EQ(_obj._version, 1);

        for (int32_t i = 0; i < 10; ++i) {
            ASSERT_TRUE(_obj.query(&fea1[i]));
            EXPECT_EQ(target[i], fea1[i].pre_feat_distance());
        }
    }

    system("rm ./test.dat");
}

TEST_F(DataViewTDAccumulatorTestSuite, insert_and_query_out_of_range) {
    _obj._goe_list.reset(
            new (std::nothrow) DataViewTDAccumulator::GOEList(10000, 100));
    _obj._feature_id = kDefaultFeatureId;
     _obj._range = 50;
    _obj._max_dataview_size = 10;

    // mock feature
    FeatureValueProto fea[] = {mock_feature(1, 2, 1000),
                               mock_feature(1, 3, 3000),
                               mock_feature(1, 2, 3000),
                               mock_feature(1, 2, 10000),
                               mock_feature(1, 2, 112000)};

    for (int32_t i = 0; i < 5; ++i) {
        ASSERT_TRUE(_obj.update(fea[i]));
    }

    ASSERT_TRUE(_obj.query(&fea[0]));
    ASSERT_TRUE(_obj.query(&fea[1]));
    ASSERT_TRUE(_obj.query(&fea[2]));
    ASSERT_TRUE(_obj.query(&fea[3]));
    ASSERT_TRUE(_obj.query(&fea[4]));

    EXPECT_EQ(fea[0].pre_feat_distance(), kMaxDiff); 
    EXPECT_EQ(fea[1].pre_feat_distance(), kMaxDiff); 
    EXPECT_EQ(fea[2].pre_feat_distance(), 2); 
    EXPECT_EQ(fea[3].pre_feat_distance(), 7); 
    EXPECT_EQ(fea[4].pre_feat_distance(), kMaxDiff); 
}

TEST_F(DataViewTDAccumulatorTestSuite, insert_and_query_out_of__max_dataview_size) {
    _obj._goe_list.reset(
            new (std::nothrow) DataViewTDAccumulator::GOEList(10000, 100));
    _obj._feature_id = kDefaultFeatureId;
     _obj._range = 600;
    _obj._max_dataview_size = 3;

    // mock feature
    FeatureValueProto fea[] = {mock_feature(1, 2, 1000),
                               mock_feature(1, 2, 3000),
                               mock_feature(1, 3, 3000),
                               mock_feature(1, 4, 3000),
                               mock_feature(1, 5, 3000),
                               mock_feature(1, 6, 3000),
                               mock_feature(1, 7, 6000),
                               mock_feature(1, 7, 7000),
                               mock_feature(1, 2, 10000),
                               mock_feature(1, 2, 112000)};

    for (int32_t i = 0; i < 10; ++i) {
        ASSERT_TRUE(_obj.update(fea[i]));
    }

    for (int32_t i = 0; i < 10; ++i) {
        ASSERT_TRUE(_obj.query(&fea[i]));
    }

    EXPECT_EQ(fea[0].pre_feat_distance(), kMaxDiff); 
    EXPECT_EQ(fea[1].pre_feat_distance(), 0); 
    EXPECT_EQ(fea[2].pre_feat_distance(), 0); 
    EXPECT_EQ(fea[3].pre_feat_distance(), 0); 
    EXPECT_EQ(fea[4].pre_feat_distance(), 0); 
    EXPECT_EQ(fea[5].pre_feat_distance(), 0);
    EXPECT_EQ(fea[6].pre_feat_distance(), 3); 
    EXPECT_EQ(fea[7].pre_feat_distance(), 1); 
    EXPECT_EQ(fea[8].pre_feat_distance(), kMaxDiff); 
    EXPECT_EQ(fea[9].pre_feat_distance(), 102); 
}


}
}
}
