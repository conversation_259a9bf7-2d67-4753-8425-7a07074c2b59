// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: fea_value_dis_accumulator_test.cpp
// @Last modified: 2018-01-02 18:25:50
// @Brief: 

#include "fea_value_dis_accumulator.h"
#include <gtest/gtest.h>
#include <bmock.h>
#include <boost/lexical_cast.hpp>
#include "ckpt_test_util.h"

using ::testing::_;
using ::testing::Return;
using ::testing::DoAll;
using ::testing::SetArgPointee;
using ::testing::SetArgumentPointee;

namespace anti {
namespace themis {
namespace feature_lib {

BMOCK_NS_CLASS_METHOD1(anti::themis::feature_lib, FValueRPCManager, query,
        bool(const std::shared_ptr<FValQueryTransProto>&));

void resume_mock() {
    BMOCK_NS_CLASS_RESUME(anti::themis::feature_lib, FValueRPCManager, query,
            bool(const std::shared_ptr<FValQueryTransProto>&));
}

void stop_mock() {
    BMOCK_NS_CLASS_STOP(anti::themis::feature_lib, FValueRPCManager, query,
            bool(const std::shared_ptr<FValQueryTransProto>&));
}

const char* CONF_PATH = "./conf";
const char* CONF_FILE = "fea_val_dis_test.conf";
class FeaValueDisAccumulatorTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
        stop_mock();
        ASSERT_EQ(0, _conf.load(CONF_PATH, CONF_FILE));
        _client.reset(new (std::nothrow) FValueRPCManager());
    }

    virtual void TearDown() {}

private:
    comcfg::Configure _conf;
    std::shared_ptr<FValueRPCManager> _client;
};

TEST_F(FeaValueDisAccumulatorTestSuite, ctr_case) {
    FeaValueDisAccumulator obj; 
    EXPECT_EQ(FeaValueDisAccumulator::ERROR, obj._acc_value_type);
}

TEST_F(FeaValueDisAccumulatorTestSuite, _init_case) {
    for (uint32_t i = 0; i < _conf["invalid"].size(); ++i) {
        CWARNING_LOG("current i:%u", i);
        FeaValueDisAccumulator obj;
        ASSERT_FALSE(obj._init(_conf["invalid"][i]));
    }
    {
        FeaValueDisAccumulator obj;
        ASSERT_TRUE(obj.init(_conf["valid"][0]));
        ASSERT_EQ(2U, obj._extend_fids.size());
        EXPECT_EQ(123, obj._extend_fids[0]);
        EXPECT_EQ(111, obj._extend_fids[1]);
        EXPECT_EQ(10U, obj._max_req_num);
    }
}

TEST_F(FeaValueDisAccumulatorTestSuite, _calculate_dis_case) {
    FeaValueDisAccumulator obj;
    ASSERT_TRUE(obj.init(_conf["valid"][0]));
    std::shared_ptr<FValQueryTransProto> fea_trans(new (std::nothrow) FValQueryTransProto);
    std::string mock_vals[] = {"12", "101", "150"};
    uint64_t mock_vs[] = {111, 222, 333};
    for (int32_t i =  0; i < 3; ++i) {
        auto qv = fea_trans->add_infos(); 
        ASSERT_FALSE(!qv);
        auto request = qv->mutable_request();
        request->set_feature_id(111);
        request->set_view_sign(mock_vs[i]);
        auto response = qv->mutable_response();
        auto va = response->add_value();
        *va = mock_vals[i];
    }
    // add no response 
    fea_trans->add_infos();
    // add value invalid
    auto qv = fea_trans->add_infos();
    qv->mutable_request()->set_feature_id(111);
    qv->mutable_request()->set_view_sign(0);
    auto va = qv->mutable_response()->add_value();
    *va = "abx3";
    // case: fail, too few succ
    obj._window_conf.remain = 1000;
    ASSERT_TRUE(!obj._calculate_dis(fea_trans));
    // case: succ
    // unit value
    obj._window_conf.remain = 0;
    obj._acc_value_type = FeaValueDisAccumulator::UNIQ_VALUE;
    auto dis = obj._calculate_dis(fea_trans);
    ASSERT_FALSE(!dis);
    EXPECT_EQ(0, (*dis)[0]);
    EXPECT_EQ(1, (*dis)[1]);
    EXPECT_EQ(2, (*dis)[2]);
    EXPECT_EQ(0, (*dis)[3]);
    // click value
    obj._acc_value_type = FeaValueDisAccumulator::CLICK_VALUE;
    dis = obj._calculate_dis(fea_trans);
    ASSERT_FALSE(!dis);
    EXPECT_EQ(0, (*dis)[0]);
    EXPECT_EQ(12, (*dis)[1]);
    EXPECT_EQ(251, (*dis)[2]);
    EXPECT_EQ(0, (*dis)[3]);
}
 
TEST_F(FeaValueDisAccumulatorTestSuite, _query_and_enter_view_node_case) {
    FeaValueDisAccumulator obj;
    ASSERT_TRUE(obj.init(_conf["valid"][0]));
    const uint64_t VIEW_SIGN = 123U;
    ASSERT_TRUE(!obj._query_view_node(VIEW_SIGN));
    ASSERT_FALSE(!obj._enter_window(VIEW_SIGN, 1000));
    ASSERT_FALSE(!obj._query_view_node(VIEW_SIGN));
    ASSERT_FALSE(!obj._enter_window(VIEW_SIGN, 1010));
}

FValQueryTransProto mock_item_req(const FeatureValueProto& fea) {
    FValQueryTransProto fea_trans;
    auto re_qv = fea_trans.add_infos(); 
    re_qv->mutable_request()->set_feature_id(123);
    re_qv->mutable_request()->set_view_sign(fea.view_sign());
    auto va = re_qv->mutable_response()->add_value();
    *va = "999";
    return fea_trans;
}

FValQueryTransProto mock_ext_req() {
    FValQueryTransProto ext_req;
    auto ext_qv = ext_req.add_infos();
    ext_qv->mutable_request()->set_feature_id(111);
    ext_qv->mutable_request()->set_view_sign(999);
    auto ext_va = ext_qv->mutable_response()->add_value();
    *ext_va = "234";
    return ext_req;
}
FeatureValueProto mock_fea() {
    FeatureValueProto fea;
    fea.set_feature_id(111);
    fea.set_view_sign(555);
    fea.set_coord(1001);
    fea.set_feature_type(FeatureValueProto::FEATURE_VALUE_EXTEND);
    auto info = fea.mutable_fea_serv_trans()->add_infos();
    info->mutable_request()->set_view_sign(567);
    info->mutable_request()->set_feature_id(123);
    info = fea.mutable_fea_serv_trans()->add_infos();
    info->mutable_request()->set_view_sign(789);
    info->mutable_request()->set_feature_id(111);
    return fea;
}

TEST_F(FeaValueDisAccumulatorTestSuite, query_case) {
    resume_mock();
    FeaValueDisAccumulator obj;
    ASSERT_TRUE(obj.init(_conf["valid"][0]));
    // case: query fail, input invalid
    ASSERT_FALSE(obj.query(NULL));
    // case: view_sign not exist
    FeatureValueProto fea = mock_fea();
    ASSERT_FALSE(obj.query(&fea));
    // case: succ
    obj._enter_window(fea.view_sign(), fea.coord());
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(anti::themis::feature_lib, FValueRPCManager, query), 
            query(_))
            .WillOnce(Return(true));
    ASSERT_TRUE(obj.query(&fea));
    EXPECT_EQ(1U, obj._fea_reqs.size());
    ASSERT_TRUE(obj._fea_reqs.find(789) != obj._fea_reqs.end());
    EXPECT_EQ(789, obj._fea_reqs[789]->infos(0).request().view_sign());
    EXPECT_EQ(111, obj._fea_reqs[789]->infos(0).request().feature_id());
    ASSERT_TRUE(obj.query(&fea));
    EXPECT_EQ(1U, obj._fea_reqs.size());
    EXPECT_EQ(789, obj._fea_reqs[789]->infos(0).request().view_sign());
    EXPECT_EQ(111, obj._fea_reqs[789]->infos(0).request().feature_id());
}

TEST_F(FeaValueDisAccumulatorTestSuite, query_server_case) {
    FeaValueDisAccumulator obj;
    ASSERT_TRUE(obj.init(_conf["valid"][0]));
    const uint64_t VIEW_SIGN = 888LU;
    FValQRequestProto req;
    req.set_feature_id(_conf["valid"][0]["feature_id"].to_uint64());
    req.set_view_sign(VIEW_SIGN);

    ASSERT_FALSE(obj.query(req, NULL));
    {
        // query view sign fail
        FValQResponseProto res;
        ASSERT_TRUE(obj.query(req, &res));
    }

    auto node = obj._enter_window(VIEW_SIGN, 1000);
    double mock_count[] = {1, 2, 3, 4};
    std::shared_ptr<DistributionItem> dis(new (std::nothrow)DistributionItem());
    ASSERT_FALSE(!dis);
    for (uint32_t i = 0; i <= obj._intervals.size(); ++i) {
        (*dis)[i] = mock_count[i];
    }
    node->set_value(dis);

    {
        // distance query no implement
        FValQResponseProto res;
        ASSERT_FALSE(obj.query(req, &res));
    }

    {
        // get one bucket
        req.mutable_distpq()->set_bucket_idx(3);
        FValQResponseProto res;
        ASSERT_TRUE(obj.query(req, &res));
        ASSERT_EQ(1, res.value_size());
        ASSERT_DOUBLE_EQ(0.4, boost::lexical_cast<double>(res.value(0)));
    }
    {
        req.mutable_distpq()->set_bucket_idx(-1);
        FValQResponseProto res;
        ASSERT_TRUE(obj.query(req, &res));
        ASSERT_EQ(4, res.value_size());
        double exp[] = {1, 2, 3, 4};
        for (uint32_t i = 0; i <= obj._intervals.size(); ++i) {
            ASSERT_DOUBLE_EQ(exp[i], boost::lexical_cast<double>(res.value(i))) << i;
        }
    }

}

TEST_F(FeaValueDisAccumulatorTestSuite, sync_case) {
    resume_mock();
    FeaValueDisAccumulator obj;
    ASSERT_TRUE(obj.init(_conf["valid"][0]));
    obj._window_conf.remain = 0;
    std::shared_ptr<FValQueryTransProto> fea_trans(new (std::nothrow)FValQueryTransProto);
    FeatureValueProto fea = mock_fea();
    std::shared_ptr<FValQueryTransProto> ext_req(new (std::nothrow)FValQueryTransProto(
            mock_ext_req()));
    obj._extend_fea_reqs[fea.view_sign()] = ext_req;
    FeatureValueProto fea2 = mock_fea();
    fea.set_view_sign(987);
    std::shared_ptr<FValQueryTransProto> item_req(new (std::nothrow)FValQueryTransProto(mock_item_req(fea2)));
    obj._items_reqs[fea2.view_sign()] = item_req;
    std::vector<std::shared_ptr<FeatureValueProto>> feas;
    std::shared_ptr<FeatureValueProto> f(new (std::nothrow)FeatureValueProto(fea));
    feas.push_back(f);
    obj._enter_window(fea2.view_sign(), fea2.coord());
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(anti::themis::feature_lib, FValueRPCManager, query), 
            query(_))
            .WillOnce(DoAll(SetArgPointee<0>(mock_ext_req()), Return(true)));
    obj.sync(&feas);
    EXPECT_EQ(1U, obj._extend_fea_reqs.size());
    EXPECT_TRUE(obj._extend_fea_reqs.find(fea2.view_sign()) != obj._extend_fea_reqs.end());
    EXPECT_EQ(0U, obj._items_reqs.size());
    EXPECT_EQ(0U, obj._fea_reqs.size());
}
 
TEST_F(FeaValueDisAccumulatorTestSuite, _convert_request_case) {
    FeaValueDisAccumulator obj;
    ASSERT_TRUE(obj.init(_conf["valid"][0]));
    // case: invalid
    std::shared_ptr<FValQueryTransProto> invalid(new (std::nothrow) FValQueryTransProto);
    ASSERT_FALSE(!invalid);
    invalid->add_infos()->mutable_response();
    ASSERT_TRUE(!obj._convert_request(invalid));
    // case: succ
    std::shared_ptr<FValQueryTransProto> req(new (std::nothrow) FValQueryTransProto);
    ASSERT_FALSE(!req);
    auto res = req->add_infos()->mutable_response();
    char* mock_views[] = {"1", "12", "123"}; 
    uint64_t exp_views[] = {1, 12, 123}; 
    for (uint32_t i = 0; i < 3; ++i) {
        auto va = res->add_value();
        *va = mock_views[i];
    }
    obj._window_conf.remain = 0;
    auto fea_trans = obj._convert_request(req);
    ASSERT_FALSE(!fea_trans);
    ASSERT_EQ(3, fea_trans->infos_size());
    for (int32_t i = 0; i < fea_trans->infos_size(); ++i) {
        EXPECT_EQ(obj._extend_fids[1], fea_trans->infos(i).request().feature_id());
        EXPECT_EQ(exp_views[i], fea_trans->infos(i).request().view_sign());
    }
}
 
TEST_F(FeaValueDisAccumulatorTestSuite, _complete_fea_case) {
    FeaValueDisAccumulator obj;
    ASSERT_TRUE(obj.init(_conf["valid"][0]));
    FeatureValueProto fea = mock_fea();
    std::shared_ptr<FValQueryTransProto> req(new (std::nothrow) FValQueryTransProto);
    ASSERT_FALSE(!req);
    auto res = req->add_infos()->mutable_response();
    // case: req invalid
    obj._complete_fea(req, &fea);
    EXPECT_EQ(0, fea.buckets_size());
    // case: level 1 view_sign not exist
    auto va = res->add_value();
    obj._complete_fea(req, &fea);
    EXPECT_EQ(0, fea.buckets_size());
    // case: succ 
    auto node = obj._enter_window(fea.view_sign(), fea.coord());
    int mock_count[] = {4,10, 55, 700};
    std::shared_ptr<DistributionItem> dis(new (std::nothrow)DistributionItem());
    ASSERT_FALSE(!dis);
    for (uint32_t i = 0; i <= obj._intervals.size(); ++i) {
        (*dis)[i] = mock_count[i];
    }
    node->set_value(dis);
    obj._complete_fea(req, &fea);
    ASSERT_EQ(4, fea.buckets_size());
    for (int i = 0; i < fea.buckets_size(); ++i) {
        EXPECT_EQ(i, fea.buckets(i).idx());
        EXPECT_EQ((*dis)[i], fea.buckets(i).count());
    }
}
 

TEST_F(FeaValueDisAccumulatorTestSuite, update_case) {
    resume_mock();
    FeaValueDisAccumulator obj;
    ASSERT_TRUE(obj.init(_conf["valid"][0]));
    FeatureValueProto fea = mock_fea();
    obj._window_conf.expiration = 1000;
    // 1. no need extend
    ASSERT_TRUE(obj.update(fea));
    EXPECT_EQ(0, obj._extend_fea_reqs.size());
    EXPECT_EQ(0, obj._items_reqs.size());
    EXPECT_EQ(0, obj._fea_reqs.size());
     
    obj._window_conf.expiration = 0;
    obj._window_conf.remain = 0;
    // 2. triger items request 
    FValQueryTransProto fea_trans = mock_item_req(fea);
//    auto re_qv = fea_trans.add_infos(); 
//    ASSERT_FALSE(!re_qv);
//    re_qv->mutable_request()->set_feature_id(123);
//    re_qv->mutable_request()->set_view_sign(fea.view_sign());
//    auto va = re_qv->mutable_response()->add_value();
//    *va = "999";
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(anti::themis::feature_lib, FValueRPCManager, query), 
            query(_))
            .WillOnce(DoAll(SetArgPointee<0>(fea_trans), Return(true)));
    ASSERT_TRUE(obj.update(fea));
    EXPECT_EQ(0, obj._extend_fea_reqs.size());
    EXPECT_EQ(1, obj._items_reqs.size());
    EXPECT_EQ(0, obj._fea_reqs.size());
    // 3. triger extend request
    FValQueryTransProto ext_req = mock_ext_req();
//    auto ext_qv = ext_req.add_infos();
//    ext_qv->mutable_request()->set_feature_id(111);
//    ext_qv->mutable_request()->set_view_sign(999);
//    auto ext_va = ext_qv->mutable_response()->add_value();
//    *ext_va = "234";
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(anti::themis::feature_lib, FValueRPCManager, query), 
            query(_))
            .WillOnce(DoAll(SetArgPointee<0>(ext_req), Return(true)));
    ASSERT_TRUE(obj.update(fea));
    EXPECT_EQ(1, obj._extend_fea_reqs.size());
    EXPECT_EQ(0, obj._items_reqs.size());
    EXPECT_EQ(0, obj._fea_reqs.size());
    // 4. no need to triger
    ASSERT_TRUE(obj.update(fea));
    EXPECT_EQ(1, obj._extend_fea_reqs.size());
    EXPECT_EQ(0, obj._items_reqs.size());
    EXPECT_EQ(0, obj._fea_reqs.size());
}
 
TEST_F(FeaValueDisAccumulatorTestSuite, dump_and_load_case) {
    resume_mock();
    FeaValueDisAccumulator _obj;
    ASSERT_TRUE(_obj.init(_conf["valid"][0]));

    const uint32_t EXP_COUNT = 1000;
    std::unordered_map<uint32_t, std::shared_ptr<FValQueryTransProto>> fts;
    for (uint32_t i = 0; i < EXP_COUNT; ++i) {
        FeatureValueProto fea;
        fea.set_feature_id(111);
        fea.set_view_sign(i % 7);
        fea.set_coord(i);
        fea.set_feature_type(FeatureValueProto::FEATURE_VALUE_EXTEND);
        _obj._enter_window(fea.view_sign(), fea.coord());
    }
    DOING_DUMP();
    _obj.uninit(); 
    ASSERT_TRUE(_obj.init(_conf["valid"][0]));
    ASSERT_EQ(100, _obj._window_conf.window_length);
    DOING_LOAD();
}

} // namespace feature_lib
} // namespace themis
} // namespace anti

/* vim: set ts=4 sw=4: */

