* text=auto !eol
/BCLOUD -text
accumulator/dev/acp_feature_accumulator.cpp -text
accumulator/dev/acp_feature_accumulator.h -text
accumulator/dev/basic_feature_accumulator.h -text
accumulator/dev/checkpoint_manager.cpp -text
accumulator/dev/checkpoint_manager.h -text
accumulator/dev/concentration_feature_accumulator.cpp -text
accumulator/dev/concentration_feature_accumulator.h -text
accumulator/dev/count_distribution_feature_accumulator.cpp -text
accumulator/dev/count_distribution_feature_accumulator.h -text
accumulator/dev/distinct_distribution_feature_accumulator.cpp -text
accumulator/dev/distinct_distribution_feature_accumulator.h -text
accumulator/dev/distinct_feature_accumulator.cpp -text
accumulator/dev/distinct_feature_accumulator.h -text
accumulator/dev/distribution_distance.cpp -text
accumulator/dev/distribution_distance.h -text
accumulator/dev/distribution_feature_accumulator.cpp -text
accumulator/dev/distribution_feature_accumulator.h -text
accumulator/dev/fea_deviation_feature_accumulator.cpp -text
accumulator/dev/fea_deviation_feature_accumulator.h -text
accumulator/dev/feature_accumulator_factory.cpp -text
accumulator/dev/feature_accumulator_factory.h -text
accumulator/dev/feature_accumulator_manager.cpp -text
accumulator/dev/hijack_feature_accumulator.cpp -text
accumulator/dev/hijack_feature_accumulator.h -text
accumulator/dev/multi_segment_feature_accumulator.cpp -text
accumulator/dev/multi_segment_feature_accumulator.h -text
accumulator/dev/posix_themis_io.h -text
accumulator/dev/rate_distribution_feature_accumulator.cpp -text
accumulator/dev/rate_distribution_feature_accumulator.h -text
accumulator/dev/rate_feature_accumulator.cpp -text
accumulator/dev/rate_feature_accumulator.h -text
accumulator/dev/ratio_black_feature_accumulator.cpp -text
accumulator/dev/ratio_black_feature_accumulator.h -text
accumulator/dev/ratio_feature_accumulator.cpp -text
accumulator/dev/ratio_feature_accumulator.h -text
accumulator/dev/segment_black_feature_accumulator.cpp -text
accumulator/dev/segment_black_feature_accumulator.h -text
accumulator/dev/segment_feature_accumulator.cpp -text
accumulator/dev/segment_feature_accumulator.h -text
accumulator/dev/thread_safe_queue.h -text
accumulator/dev/timediff_feature_accumulator.cpp -text
accumulator/dev/timediff_feature_accumulator.h -text
accumulator/inc/feature_accumulator_interface.h -text
accumulator/inc/feature_accumulator_manager.h -text
accumulator/inc/posix_io_interface.h -text
accumulator/unittest/acp_feature_accumulator_test.cpp -text
accumulator/unittest/basic_feature_accumulator_test.cpp -text
accumulator/unittest/build.sh -text
accumulator/unittest/checkpoint_manager_test.cpp -text
accumulator/unittest/ckpt/describe_invalid -text
accumulator/unittest/ckpt/describe_valid -text
accumulator/unittest/ckpt_test_util.h -text
accumulator/unittest/concentration_feature_accumulator_test.cpp -text
accumulator/unittest/conf/acc_mgr_cmp_test.conf -text
accumulator/unittest/conf/acc_mgr_multi_line_test.conf -text
accumulator/unittest/conf/accumulator_test.conf -text
accumulator/unittest/conf/ckpt_make_seg_test.conf -text
accumulator/unittest/conf/fea_deviation.conf -text
accumulator/unittest/conf/feature_accumulator_manager_test.conf -text
accumulator/unittest/conf/feature_accumulator_manager_test_modify.conf -text
accumulator/unittest/conf/file_dict_manager.conf -text
accumulator/unittest/conf/hijack_feature.conf -text
accumulator/unittest/conf/invalid_feature_accumulator_manager.conf -text
accumulator/unittest/conf/invalid_feature_accumulator_manager1.conf -text
accumulator/unittest/conf/invalid_feature_accumulator_manager2.conf -text
accumulator/unittest/conf/multisegment_feature.conf -text
accumulator/unittest/conf/timediff_feature.conf -text
accumulator/unittest/count_distribution_feature_accumulator_test.cpp -text
accumulator/unittest/data/acp.data -text
accumulator/unittest/data/empty.ckpt -text
accumulator/unittest/data/input -text
accumulator/unittest/data/version0.ckpt -text
accumulator/unittest/data/version100.ckpt -text
accumulator/unittest/distinct_distribution_feature_accumulator_test.cpp -text
accumulator/unittest/distinct_feature_accumulator_test.cpp -text
accumulator/unittest/distribution_feature_accumulator_test.cpp -text
accumulator/unittest/fea_deviation_feature_accumulator_test.cpp -text
accumulator/unittest/feature_accumulator_factory_test.cpp -text
accumulator/unittest/feature_accumulator_manager_test.cpp -text
accumulator/unittest/hijack_feature_accumulator_test.cpp -text
accumulator/unittest/multi_segment_feature_accumulator_test.cpp -text
accumulator/unittest/rate_distribution_feature_accumulator_test.cpp -text
accumulator/unittest/rate_feature_accumulator_test.cpp -text
accumulator/unittest/ratio_black_feature_accumulator_test.cpp -text
accumulator/unittest/ratio_feature_accumulator_test.cpp -text
accumulator/unittest/segment_black_feature_accumulator_test.cpp -text
accumulator/unittest/segment_feature_accumulator_test.cpp -text
accumulator/unittest/timediff_feature_accumulator_test.cpp -text
/build.sh -text
/ci.xml -text
conf/feature.conf -text
conf/judge.conf -text
conf/model.conf -text
conf/policy.conf -text
extractor/dev/big_feature.cpp -text
extractor/dev/care_condition_factory.cpp -text
extractor/dev/care_condition_factory.h -text
extractor/dev/care_condition_impl.cpp -text
extractor/dev/care_condition_impl.h -text
extractor/dev/care_space.cpp -text
extractor/dev/carespace_feature_extractor.cpp -text
extractor/dev/carespace_feature_extractor.h -text
extractor/dev/click_hijack_feature_extractor.cpp -text
extractor/dev/concentration_feature_extractor.cpp -text
extractor/dev/concentration_feature_extractor.h -text
extractor/dev/count_distribution_feature_extractor.cpp -text
extractor/dev/count_distribution_feature_extractor.h -text
extractor/dev/del_feature.cpp -text
extractor/dev/demeter_base_feature_extractor.cpp -text
extractor/dev/demeter_click_common_feature_extractor.cpp -text
extractor/dev/demeter_click_common_feature_extractor.h -text
extractor/dev/demeter_extend_global_feature_extractor.cpp -text
extractor/dev/demeter_extend_global_feature_extractor.h -text
extractor/dev/demeter_fix_feature_extractor.cpp -text
extractor/dev/demeter_global_session_feature_extractor.cpp -text
extractor/dev/demeter_hijack_feature_extractor.cpp -text
extractor/dev/demeter_priority_feature_extractor.cpp -text
extractor/dev/dict_feature_extractor.cpp -text
extractor/dev/dict_feature_extractor.h -text
extractor/dev/distinct_distribution_feature_extractor.cpp -text
extractor/dev/distinct_distribution_feature_extractor.h -text
extractor/dev/distribution_feature_extractor.cpp -text
extractor/dev/distribution_feature_extractor.h -text
extractor/dev/fea_deviation_feature_extractor.cpp -text
extractor/dev/fea_deviation_feature_extractor.h -text
extractor/dev/feature_extractor_base.cpp -text
extractor/dev/feature_extractor_base.h -text
extractor/dev/feature_extractor_factory.cpp -text
extractor/dev/feature_extractor_factory.h -text
extractor/dev/feature_extractor_manager.cpp -text
extractor/dev/hijack_feature_extractor.cpp -text
extractor/dev/hijack_feature_extractor.h -text
extractor/dev/original_feature_extractor.cpp -text
extractor/dev/original_feature_extractor.h -text
extractor/dev/ratio_feature_extractor.cpp -text
extractor/dev/ratio_feature_extractor.h -text
extractor/dev/segment_feature_extractor.cpp -text
extractor/dev/segment_feature_extractor.h -text
extractor/dev/timediff_feature_extractor.cpp -text
extractor/dev/timediff_feature_extractor.h -text
extractor/inc/big_feature.h -text
extractor/inc/care_condition_interface.h -text
extractor/inc/care_space.h -text
extractor/inc/click_hijack_feature_extractor.h -text
extractor/inc/del_feature.h -text
extractor/inc/demeter_base_feature_extractor.h -text
extractor/inc/demeter_fix_feature_extractor.h -text
extractor/inc/demeter_global_session_feature_extractor.h -text
extractor/inc/demeter_hijack_feature_extractor.h -text
extractor/inc/demeter_priority_feature_extractor.h -text
extractor/inc/feature_extractor_interface.h -text
extractor/inc/feature_extractor_manager.h -text
extractor/unittest/build.sh -text
extractor/unittest/care_condition_factory_test.cpp -text
extractor/unittest/care_condition_impl_test.cpp -text
extractor/unittest/care_space_test.cpp -text
extractor/unittest/carespace_feature_extractor_test.cpp -text
extractor/unittest/click_hijack_feature_extractor_test.cpp -text
extractor/unittest/concentration_feature_extractor_test.cpp -text
extractor/unittest/conf/demeter_base_extractor_test.conf -text
extractor/unittest/conf/demeter_extend_global_test.conf -text
extractor/unittest/conf/demeter_fix_feature_extractor_test.conf -text
extractor/unittest/conf/demeter_global_session_feature_extractor_test.conf -text
extractor/unittest/conf/demeter_hijack_extractor_dict.conf -text
extractor/unittest/conf/demeter_hijack_extractor_test.conf -text
extractor/unittest/conf/empty.conf -text
extractor/unittest/conf/fea_deviation.conf -text
extractor/unittest/conf/fea_mgr_cmp_test.conf -text
extractor/unittest/conf/fea_mgr_multi_line_test.conf -text
extractor/unittest/conf/feature_extractor_manger_invalid.conf -text
extractor/unittest/conf/feature_extractor_manger_invalid1.conf -text
extractor/unittest/conf/feature_extractor_manger_test.conf -text
extractor/unittest/conf/feature_extractor_test.conf -text
extractor/unittest/conf/file_dict_manager.conf -text
extractor/unittest/conf/hijack_extractor_test.conf -text
extractor/unittest/conf/home_page.conf -text
extractor/unittest/count_distribution_feature_extractor_test.cpp -text
extractor/unittest/data/care_test.data -text
extractor/unittest/data/cnt_group.txt -text
extractor/unittest/data/empty.data -text
extractor/unittest/data/white.cnt -text
extractor/unittest/demeter_base_feature_extractor_test.cpp -text
extractor/unittest/demeter_extend_global_feature_extractor_test.cpp -text
extractor/unittest/demeter_fix_feature_extractor_test.cpp -text
extractor/unittest/demeter_global_session_feature_extractor_test.cpp -text
extractor/unittest/demeter_hijack_feature_extractor_test.cpp -text
extractor/unittest/demeter_priority_feature_extractor_test.cpp -text
extractor/unittest/dict_feature_extractor_test.cpp -text
extractor/unittest/distinct_distribution_feature_extractor_test.cpp -text
extractor/unittest/distribution_feature_extractor_test.cpp -text
extractor/unittest/fea_deviation_feature_extractor_test.cpp -text
extractor/unittest/feature_extractor_base_test.cpp -text
extractor/unittest/feature_extractor_factory_test.cpp -text
extractor/unittest/feature_extractor_manager_test.cpp -text
extractor/unittest/hijack_extractor_test.cpp -text
extractor/unittest/mock_log_record_interface.h -text
extractor/unittest/original_feature_extractor_test.cpp -text
extractor/unittest/ratio_feature_extractor_test.cpp -text
extractor/unittest/segment_feature_extractor_test.cpp -text
feature_util/dev/discretization_interface.cpp -text
feature_util/dev/distribution_distance_calculate.cpp -text
feature_util/dev/feature_util.cpp -text
feature_util/inc/discretization_interface.h -text
feature_util/inc/distribution_distance_calculate.h -text
feature_util/inc/feature_util.h -text
feature_util/unittest/conf/discretization.conf -text
feature_util/unittest/conf/parse_list_test.conf -text
feature_util/unittest/discretization_interface_test.cpp -text
feature_util/unittest/feature_util_test.cpp -text
judge/dev/judge.cpp -text
judge/inc/judge.h -text
judge/unittest/build.sh -text
judge/unittest/conf/invalid_judge.conf -text
judge/unittest/conf/invalid_judge1.conf -text
judge/unittest/conf/judge_test.conf -text
judge/unittest/judge_test.cpp -text
policy/dev/acp_limit_rule.cpp -text
policy/dev/acp_limit_rule.h -text
policy/dev/boolean_rule.cpp -text
policy/dev/boolean_rule.h -text
policy/dev/click_hijack_policy.cpp -text
policy/dev/combine_rule.cpp -text
policy/dev/combine_rule.h -text
policy/dev/demeter_click_black_rule.cpp -text
policy/dev/demeter_click_black_rule.h -text
policy/dev/demeter_extend_prob_rule.cpp -text
policy/dev/demeter_extend_prob_rule.h -text
policy/dev/distribution_limit_rule.cpp -text
policy/dev/distribution_limit_rule.h -text
policy/dev/dnn_policy.cpp -text
policy/dev/dnn_policy.h -text
policy/dev/fea_deviation_rule.cpp -text
policy/dev/fea_deviation_rule.h -text
policy/dev/gbdt_policy.cpp -text
policy/dev/gbdt_policy.h -text
policy/dev/hijack_policy.cpp -text
policy/dev/hijack_policy.h -text
policy/dev/lr_policy.cpp -text
policy/dev/lr_policy.h -text
policy/dev/model_policy.cpp -text
policy/dev/model_policy.h -text
policy/dev/policy_factory.cpp -text
policy/dev/policy_factory.h -text
policy/dev/policy_manager.cpp -text
policy/dev/ratio_black_limit_rule.cpp -text
policy/dev/ratio_black_limit_rule.h -text
policy/dev/ratio_limit_rule.cpp -text
policy/dev/ratio_limit_rule.h -text
policy/dev/rule_factory.cpp -text
policy/dev/rule_factory.h -text
policy/dev/rule_interface.cpp -text
policy/dev/rule_interface.h -text
policy/dev/rule_policy.cpp -text
policy/dev/rule_policy.h -text
policy/dev/segment_limit_rule.cpp -text
policy/dev/segment_limit_rule.h -text
policy/dev/threshold_limit_rule.cpp -text
policy/dev/threshold_limit_rule.h -text
policy/dev/time_diff_rule.cpp -text
policy/dev/time_diff_rule.h -text
policy/inc/click_hijack_policy.h -text
policy/inc/feature_manager.h -text
policy/inc/policy_interface.h -text
policy/inc/policy_manager.h -text
policy/unittest/acp_limit_rule_test.cpp -text
policy/unittest/boolean_rule_test.cpp -text
policy/unittest/build.sh -text
policy/unittest/click_hijack_policy_test.cpp -text
policy/unittest/combine_rule_test.cpp -text
policy/unittest/conf/add_a_layer.py -text
policy/unittest/conf/click_hijack_policy_dict.conf -text
policy/unittest/conf/click_hijack_policy_test.conf -text
policy/unittest/conf/demeter_policy_prob_rule_ok.conf -text
policy/unittest/conf/dnn.desp -text
policy/unittest/conf/dnn.fea.stat -text
policy/unittest/conf/dnn.weight -text
policy/unittest/conf/fea_deviation.conf -text
policy/unittest/conf/file_dict_manager.conf -text
policy/unittest/conf/gbdt.20001.model -text
policy/unittest/conf/invalid_policy_manager.conf -text
policy/unittest/conf/invalid_policy_manager1.conf -text
policy/unittest/conf/invalid_policy_manager2.conf -text
policy/unittest/conf/lr.fea.weight -text
policy/unittest/conf/man_label.org -text
policy/unittest/conf/model.conf -text
policy/unittest/conf/model_policy_test.conf -text
policy/unittest/conf/pctr_man.txt -text
policy/unittest/conf/pol_mgr_cmp_test.conf -text
policy/unittest/conf/pol_mgr_multi_line_test.conf -text
policy/unittest/conf/policy_manager_test.conf -text
policy/unittest/conf/rule_policy_test.conf -text
policy/unittest/conf/rule_test.conf -text
policy/unittest/conf/time_diff_rule_test.conf -text
policy/unittest/data/cnt_group.txt -text
policy/unittest/data/empty.data -text
policy/unittest/data/gray_test.data -text
policy/unittest/data/search_policy_gray.data -text
policy/unittest/data/search_policy_gray_normal.data -text
policy/unittest/data/search_policy_prob.data -text
policy/unittest/demeter_extend_prob_rule_test.cpp -text
policy/unittest/distribution_limit_rule_test.cpp -text
policy/unittest/dnn_policy_test.cpp -text
policy/unittest/fea_deviation_rule_test.cpp -text
policy/unittest/fealib_gensign_test.cpp -text
policy/unittest/feature_manager_test.cpp -text
policy/unittest/gbdt_policy_test.cpp -text
policy/unittest/hijack_policy_test.cpp -text
policy/unittest/lr_policy_test.cpp -text
policy/unittest/model_policy_test.cpp -text
policy/unittest/policy_factory_test.cpp -text
policy/unittest/policy_manager_test.cpp -text
policy/unittest/ratio_black_limit_rule_test.cpp -text
policy/unittest/ratio_limit_rule_test.cpp -text
policy/unittest/rule_factory_test.cpp -text
policy/unittest/rule_interface_test.cpp -text
policy/unittest/rule_policy_test.cpp -text
policy/unittest/segment_limit_rule_test.cpp -text
policy/unittest/threshold_limit_rule_test.cpp -text
policy/unittest/time_diff_rule_test.cpp -text
