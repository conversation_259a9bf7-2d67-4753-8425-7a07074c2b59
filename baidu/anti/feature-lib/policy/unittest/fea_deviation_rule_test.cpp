// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// <AUTHOR>
// @brief

#include <gtest/gtest.h>
#include "fea_deviation_rule.h"

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}

namespace anti {
namespace themis {
namespace feature_lib {

const std::string conf_path = "./conf";
const std::string conf_file = "fea_deviation.conf";

class TestThresholdBaseSuite : public ::testing::Test {
public:
    TestThresholdBaseSuite() {}
    ~TestThresholdBaseSuite() {}

    virtual void SetUp() {
        ASSERT_TRUE(_conf.load(conf_path.c_str(), conf_file.c_str()) == 0);
    }
    virtual void TearDown() {}

private:
    comcfg::Configure _conf;
};

TEST_F(TestThresholdBaseSuite, sample_test) {
    ThresholdBase obj;
    // no threshold
    EXPECT_FALSE(obj.init(_conf["rule"][3]));
    // ok case
    EXPECT_TRUE(obj.init(_conf["rule"][2]));
    // get_threshold
    FeatureValueProto proto;
    EXPECT_GT(0.00001, obj.get_threshold(proto) - 0.97);
}

class TestThresholdInflectionSuite : public ::testing::Test {
public:
    TestThresholdInflectionSuite() {}
    ~TestThresholdInflectionSuite() {}

    virtual void SetUp() {
        ASSERT_TRUE(_conf.load(conf_path.c_str(), conf_file.c_str()) == 0);
    }
    virtual void TearDown() {}

private:
    comcfg::Configure _conf;
};

TEST_F(TestThresholdInflectionSuite, init_test) {
    ThresholdInflection obj;
    // no threshold
    EXPECT_FALSE(obj.init(_conf["rule"][3]));
    // no remain
    EXPECT_FALSE(obj.init(_conf["rule"][4]));
    // ok case
    EXPECT_TRUE(obj.init(_conf["rule"][0]));
}

FeatureValueProto mock_fea(double ratio, double inflect_ratio) {
    FeatureValueProto feature;
    feature.set_feature_id(101);
    feature.set_view_sign(123);
    feature.set_feature_type(FeatureValueProto::FEA_DEVIATION);
    feature.set_joinkey(456);
    feature.set_log_id(1);
    feature.set_log_time(789);
    feature.set_coord(1);
    feature.set_view_name("cntname");
    feature.set_view("baiduid");
    feature.set_value("0.3#0.1#0.8");
    feature.mutable_fea_deviation_field()->set_devia_fea_value(1);
    feature.mutable_fea_deviation_field()->set_devia_fea_count(2);
    feature.mutable_fea_deviation_field()->set_devia_ratio(ratio);
    feature.mutable_fea_deviation_field()->set_devia_inflect_ratio(inflect_ratio);
    return feature;
}

TEST_F(TestThresholdInflectionSuite, get_threshold_test) {
    ThresholdInflection obj;
    ASSERT_TRUE(obj.init(_conf["rule"][0]));
    FeatureValueProto fea = mock_fea(0.1, 0.6);
    EXPECT_GT(0.000001, obj.get_threshold(fea) - 0.97);
    fea = mock_fea(0.1, 0.9);
    EXPECT_GT(0.000001, obj.get_threshold(fea) - 0.9);
    fea = mock_fea(0.1, 0.98);
    EXPECT_GT(0.000001, obj.get_threshold(fea) - 0.97);
}

class TestFeaDeviationRuleSuite : public ::testing::Test {
public:
    TestFeaDeviationRuleSuite() {}
    ~TestFeaDeviationRuleSuite() {}

    virtual void SetUp() {
        ASSERT_TRUE(_conf.load(conf_path.c_str(), conf_file.c_str()) == 0);
    }
    virtual void TearDown() {}

private:
    comcfg::Configure _conf;
};

TEST_F(TestFeaDeviationRuleSuite, init_test) {
    FeaDeviationRule obj;
    // illegal type
    EXPECT_FALSE(obj._init(_conf["rule"][5]));
    // init failed
    EXPECT_FALSE(obj._init(_conf["rule"][4]));
    // base case 1
    EXPECT_TRUE(obj._init(_conf["rule"][1]));
    // base case 2
    EXPECT_TRUE(obj._init(_conf["rule"][2]));
    // inflection case
    EXPECT_TRUE(obj._init(_conf["rule"][0]));
}

TEST_F(TestFeaDeviationRuleSuite, check_test) {
    FeaDeviationRule obj;
    EXPECT_TRUE(obj._init(_conf["rule"][0]));
    FeatureValueProto fea = mock_fea(0.8, 0.9);
    bool hit = false;
    EXPECT_TRUE(obj._check(fea, &hit));
    EXPECT_FALSE(hit);
    fea = mock_fea(0.92, 0.9);
    EXPECT_TRUE(obj._check(fea, &hit));
    EXPECT_TRUE(hit);
    fea = mock_fea(0.92, 1);
    EXPECT_TRUE(obj._check(fea, &hit));
    EXPECT_FALSE(hit);
}

TEST_F(TestFeaDeviationRuleSuite, check_test_for_value_remain) {
    FeaDeviationRule obj;
    EXPECT_TRUE(obj._init(_conf["rule"][6]));
    FeatureValueProto fea = mock_fea(0.8, 0.9);
    bool hit = false;
    EXPECT_TRUE(obj._check(fea, &hit));
    EXPECT_FALSE(hit);
    fea = mock_fea(0.92, 0.9);
    EXPECT_TRUE(obj._check(fea, &hit));
    EXPECT_FALSE(hit);
    fea = mock_fea(0.92, 0.9);
    fea.mutable_fea_deviation_field()->set_devia_fea_value(60);
    EXPECT_TRUE(obj._check(fea, &hit));
    EXPECT_TRUE(hit);
}
TEST_F(TestFeaDeviationRuleSuite, test_variance_lower_bound) {
    FeaDeviationRule obj;
    EXPECT_TRUE(obj._init(_conf["rule"][7]));
    FeatureValueProto fea = mock_fea(0.98, 0.98);
    fea.mutable_fea_deviation_field()->set_variance(0.1);
    bool hit = false;
    EXPECT_TRUE(obj._check(fea, &hit));
    EXPECT_FALSE(hit);
    fea.mutable_fea_deviation_field()->set_variance(0.3);
    EXPECT_TRUE(obj._check(fea, &hit));
    EXPECT_TRUE(hit);
    
}

}
}
}
