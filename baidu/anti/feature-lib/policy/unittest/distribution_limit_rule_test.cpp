// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: distribution_limit_rule_test.cpp
// @Last modified: 2017-11-21 18:09:28
// @Brief: 

#include <float.h>
#include <gtest/gtest.h>
#include <sign_util.h>
#include <file_dict_manager.h>
#include "distribution_limit_rule.h"

using anti::baselib::SignUtil;
using anti::themis::common_lib::FileDictManagerSingleton;

namespace anti {
namespace themis {
namespace feature_lib {

class DistributionLimitRuleTestSuite : public ::testing::Test {
public:
    virtual void SetUp() {}
    virtual void TearDown() {}

private:
    DistributionLimitRule _obj;
};

TEST_F(DistributionLimitRuleTestSuite, construction_case) {
    EXPECT_DOUBLE_EQ(0.0, _obj._threshold);
    EXPECT_EQ(0L, _obj._count_threshold);
    EXPECT_EQ(NULL, _obj._distance);
}

TEST_F(DistributionLimitRuleTestSuite, init_case) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "rule_test.conf"));

    uint32_t num = conf["distribution"]["invalid"].size();
    ASSERT_LE(4U, num);
    for (uint32_t i = 0U; i < num; ++i) {
        EXPECT_FALSE(_obj._init(conf["distribution"]["invalid"][i]));
        _obj.uninit();
    }

    num = conf["distribution"]["valid"].size();
    ASSERT_LE(4U, num);
    double (*exp[4]) (
            const std::vector<double>& pb,
            const std::vector<double>& pc,
            uint32_t bucket_idx,
            double threshold) = 
    {
        DistributionLimitRule::_max_diff,
        DistributionLimitRule::_chi_square_test,
        DistributionLimitRule::_chi_square_dis,
        DistributionLimitRule::_kl_divergence
    };

    for (uint32_t i = 0U; i < num; ++i) {
        ASSERT_TRUE(_obj._init(conf["distribution"]["valid"][i]));
        EXPECT_DOUBLE_EQ(0.5, _obj._threshold);
        EXPECT_EQ(10L, _obj._count_threshold);
        ASSERT_EQ(2U, _obj._stand_prob.size());
        EXPECT_DOUBLE_EQ(0.1, _obj._stand_prob[0]);
        EXPECT_DOUBLE_EQ(0.9, _obj._stand_prob[1]);
        EXPECT_EQ(exp[i], _obj._distance);
        _obj.uninit();
    }
}

TEST_F(DistributionLimitRuleTestSuite, _max_diff_case) {
    std::vector<double> pb = {0.5, 0.5};
    std::vector<double> pc = {0.7, 0.3};
    EXPECT_DOUBLE_EQ(-1.3, _obj._max_diff(pb, pc, 0U, 0.75));
    EXPECT_DOUBLE_EQ(-1.7, _obj._max_diff(pb, pc, 1U, 0.75));
    EXPECT_DOUBLE_EQ(DBL_MIN_EXP, _obj._max_diff(pb, pc, 1U, 1.0));
}

TEST_F(DistributionLimitRuleTestSuite, _chi_square_test_case) {
    std::vector<double> pb = {0.15, 0.25, 0.35, 0.25};
    std::vector<double> pc = {0.1, 0.2, 0.3, 0.4};
    EXPECT_FLOAT_EQ(0, _obj._chi_square_test(pb, pc, 1U, 5));
    double exp[] = {-0.21646892, -0.20425081, -0.17054249, -0.030697314};
    for (uint32_t i = 0U; i < 4; ++i) {
        EXPECT_FLOAT_EQ(exp[i], _obj._chi_square_test(pb, pc, i, 100));
    }
}

TEST_F(DistributionLimitRuleTestSuite, _chi_square_dis_case) {
    std::vector<double> pb = {0.15, 0.25, 0.35, 0.25};
    std::vector<double> pc = {0.1, 0.2, 0.3, 0.4};
    for (uint32_t i = 0U; i < 3; ++i) {
        EXPECT_FLOAT_EQ(0, _obj._chi_square_dis(pb, pc, i, 0.01));
    }
    EXPECT_FLOAT_EQ(0, _obj._chi_square_dis(pb, pc, 3, 0.005));
    double exp[] = {-0.82203001, -0.91891164, -1.0034853, -0.75232261};
    for (uint32_t i = 0U; i < 4; ++i) {
        EXPECT_FLOAT_EQ(exp[i], _obj._chi_square_dis(pb, pc, i, 0.3));
    }
}

TEST_F(DistributionLimitRuleTestSuite, _kl_divergence) {
    std::vector<double> pb = {0.15, 0.25, 0.35, 0.25};
    std::vector<double> pc = {0.1, 0.2, 0.3, 0.4};
    for (uint32_t i = 0U; i < 3; ++i) {
        EXPECT_FLOAT_EQ(0, _obj._kl_divergence(pb, pc, i, 0.01));
    }
    EXPECT_FLOAT_EQ(0, _obj._kl_divergence(pb, pc, 3, 0.005));
    double exp[] = {-1, -1, -1, -1};
    for (uint32_t i = 0U; i < 4; ++i) {
        EXPECT_FLOAT_EQ(exp[i], _obj._kl_divergence(pb, pc, i, 0.3));
    }
}

TEST_F(DistributionLimitRuleTestSuite, _check_by_invalid_input_case) {

    std::string func_type = "max_diff";
    uint64_t count_threshold = 1;
    double max_distance_magnify = 5.0;
    std::string sta_pro = "0.5,0.5";

    _obj._distribution_distance = new(std::nothrow) DistributionDistance();
    _obj._distribution_distance->init(
            func_type,
            sta_pro,
            count_threshold,
            max_distance_magnify);

    FeatureValueProto fea;
    fea.set_feature_type(FeatureValueProto::RATIO);

    EXPECT_TRUE(_obj._check(fea, NULL));

    fea.set_feature_type(FeatureValueProto::DISTRIBUTION);
    EXPECT_TRUE(_obj._check(fea, NULL));

    fea.set_bucket_idx(1U);
    EXPECT_TRUE(_obj._check(fea, NULL));

    _obj._stand_prob.push_back(0.5);
    _obj._stand_prob.push_back(0.5);
    EXPECT_TRUE(_obj._check(fea, NULL));

    bool hit = false;
    _obj._distance = _obj._max_diff;
    fea.add_buckets()->set_idx(100);
    EXPECT_FALSE(_obj._check(fea, &hit));

    fea.clear_buckets();
    ASSERT_TRUE(_obj._check(fea, &hit));
    EXPECT_FALSE(hit);
}

TEST_F(DistributionLimitRuleTestSuite, _check_by_valid_input_case) {
    ASSERT_TRUE(FileDictManagerSingleton::instance().init(
            "./conf", "file_dict_manager.conf"));
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "rule_test.conf"));
    ASSERT_TRUE(_obj.init(conf["distribution"]["valid"][0]));

    uint64_t sign = 0LU;
    SignUtil::create_sign_md64("123", &sign);
    EXPECT_NE(0LU, sign);
    
    // 1. in gray && count < count_threshold && distance > threshold
    // 2. in gray && count > count_threshold && distance > threshold
    // 3. not in gray && count > count_threshold && distance < threshold && pc > pb
    // 4. not in gray && count > count_threshold && pc < pb
    uint64_t view_signs[] = {sign, sign, 0LU, 0LU};
    uint32_t buc_idx[] = {0U, 0U, 1U, 1U};
    int64_t buc_num[][2] = {{1L, 2L}, {70L, 30L}, {9L, 91L}, {60L, 40L}};
    bool exps[] = {false, true, false, false};
    for (uint32_t i = 0; i < 4U; ++i) {
        FeatureValueProto fea;
        fea.set_feature_type(FeatureValueProto::DISTRIBUTION);
        fea.set_view_sign(view_signs[i]);
        fea.set_bucket_idx(buc_idx[i]);
        for (uint32_t j = 0; j < 2U; ++j) {
            FeatureValueProto::BucketProto* b = fea.add_buckets();
            b->set_idx(j);
            b->set_count(buc_num[i][j]);
        }
        bool hit = false;
        ASSERT_TRUE(_obj._check(fea, &hit));
        EXPECT_EQ(exps[i], hit);
    }
    // 5. max_diff && in gray && threshold is 1.0 && test count_distribution
    SignUtil::create_sign_md64("456", &sign);
    FeatureValueProto fea;
    fea.set_feature_type(FeatureValueProto::COUNT_DISTRIBUTION);
    fea.set_view_sign(sign);
    fea.set_bucket_idx(0U);
    for (uint32_t j = 0; j < 2U; ++j) {
        FeatureValueProto::BucketProto* b = fea.add_buckets();
        b->set_idx(j);
        b->set_count(buc_num[1][j]);
    }
    bool hit = false;
    ASSERT_TRUE(_obj._check(fea, &hit));
    EXPECT_FALSE(hit);

    _obj.uninit();
    FileDictManagerSingleton::instance().uninit();
}

TEST_F(DistributionLimitRuleTestSuite, max_distance_magnify_case) {
    ASSERT_TRUE(FileDictManagerSingleton::instance().init(
            "./conf", "file_dict_manager.conf"));                
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "rule_test.conf"));
    {
        ASSERT_TRUE(_obj.init(conf["max_distance_magnify"]["valid"][0]));
    
        uint64_t sign = 0LU;
        SignUtil::create_sign_md64("123", &sign);
        EXPECT_NE(0LU, sign);
        uint64_t view_signs[] = {sign, sign, sign, sign, sign, 
                sign, sign, sign, sign, sign, sign, sign};
        uint32_t buc_idx[] = {0U, 1U, 0U, 0U, 0U, 0U, 
                0U, 0U, 0U, 0U, 0U, 1U};
        uint32_t buc_num[][2] = {{1U, 0U}, {1U, 1U}, {2U, 1U}, 
                {3U, 1U}, {4U, 1U}, {5U, 1U}, {6U, 1U}, {7U, 1U}, 
                {8U, 1U}, {9U, 1U}, {10U, 1U}, {10U, 2U}};
        bool exps[] = {false, false, false, false, false, 
            true, true, true, true, true, true, false};
        for (uint32_t i = 0; i < 12U; ++i) {
            FeatureValueProto fea;
            fea.set_feature_type(FeatureValueProto::DISTRIBUTION);
            fea.set_view_sign(view_signs[i]);
            fea.set_bucket_idx(buc_idx[i]);
            for (uint32_t j = 0; j < 2U; ++j) {
                FeatureValueProto::BucketProto* b = fea.add_buckets();
                b->set_idx(j);
                b->set_count(buc_num[i][j]);
            }
            bool hit = false;
            ASSERT_TRUE(_obj._check(fea, &hit));
            EXPECT_EQ(exps[i], hit);
        }
        _obj.uninit();
    }

    {
        ASSERT_TRUE(_obj.init(conf["max_distance_magnify"]["valid"][1]));
    
        uint64_t sign = 0LU;
        SignUtil::create_sign_md64("123", &sign);
        EXPECT_NE(0LU, sign);
        uint64_t view_signs[] = {sign, sign, sign, sign, sign, 
                sign, sign, sign, sign, sign, sign, sign};
        uint32_t buc_idx[] = {0U, 1U, 0U, 0U, 0U, 
                0U, 0U, 0U, 0U, 0U, 0U, 1U};
        uint32_t buc_num[][2] = {{1U, 0U}, {1U, 1U}, {2U, 1U}, {3U, 1U}, 
                {4U, 1U}, {5U, 1U}, {6U, 1U}, {7U, 1U}, {8U, 1U}, {9U, 1U}, 
                {10U, 1U}, {10U, 2U}};
        bool exps[] = {false, false, false, false, false, 
                false, false, false, false, true, true, false};
        for (uint32_t i = 0; i < 12U; ++i) {
            FeatureValueProto fea;
            fea.set_feature_type(FeatureValueProto::DISTRIBUTION);
            fea.set_view_sign(view_signs[i]);
            fea.set_bucket_idx(buc_idx[i]);
            for (uint32_t j = 0; j < 2U; ++j) {
                FeatureValueProto::BucketProto* b = fea.add_buckets();
                b->set_idx(j);
                b->set_count(buc_num[i][j]);
            }
            bool hit = false;
            ASSERT_TRUE(_obj._check(fea, &hit));
            EXPECT_EQ(exps[i], hit);
        }
        _obj.uninit();
    }
    FileDictManagerSingleton::instance().uninit();
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

