// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// @brief 

#include <gtest/gtest.h>
#include "hijack_policy.h"

namespace anti {
namespace themis {
namespace feature_lib {

class HijackPolicyTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
        _policy._feature_id = 101;
        _policy._threshold = 1;
        _policy._hijack_delta = 9200000;

        _fea.set_feature_type(FeatureValueProto::HIJACK);
        _fea.set_log_time(101010);
        _fea.set_feature_id(101);
    }
    virtual void TearDOwn() {}
private:
    HijackPolicy _policy;
    FeatureValueProto _fea;
};

FeatureValueProto::HijackProto mock_hj_proto(
        const std::string& cn, const std::string& group,
        int32_t dist, uint32_t page_no) {
    FeatureValueProto::HijackProto obj;
    obj.set_charge_name(cn);
    obj.set_flow_group(group);
    obj.set_distance(dist);
    obj.set_page_no(page_no);
    return obj;
}

TEST_F(HijackPolicyTestSuite, no_hit_single_node) {
    _fea.mutable_original_hijack_field()->CopyFrom(mock_hj_proto("c", "f", 0, 0));

    PolicyResultProto result;
    ASSERT_TRUE(_policy.hit(_fea, &result));

    ASSERT_FALSE(result.hit());
}

TEST_F(HijackPolicyTestSuite, hit_hijacked) {
    _fea.mutable_original_hijack_field()->CopyFrom(mock_hj_proto("c", "f", 0, 0));
    _fea.add_fix_hijack_field()->CopyFrom(mock_hj_proto("c1", "f", -1, 0));

    PolicyResultProto result;
    ASSERT_TRUE(_policy.hit(_fea, &result));

    ASSERT_TRUE(result.hit());
    ASSERT_TRUE(result.hijack_fix_res().exist_hijack());
    ASSERT_EQ(result.hijack_fix_res().log_time(), _fea.log_time());
    ASSERT_EQ(result.policy_id(), _policy._policy_id + _policy._hijack_delta);
}

TEST_F(HijackPolicyTestSuite, not_hit_hijack_other) {
    _fea.mutable_original_hijack_field()->CopyFrom(mock_hj_proto("c", "f", 0, 0));
    _fea.add_fix_hijack_field()->CopyFrom(mock_hj_proto("c1", "f1", 1, 0));

    PolicyResultProto result;
    ASSERT_TRUE(_policy.hit(_fea, &result));

    ASSERT_FALSE(result.hit());
    ASSERT_TRUE(result.hijack_fix_res().exist_hijack());
    EXPECT_TRUE(result.pass_through());
    EXPECT_EQ(result.hijack_fix_res().fix_cn(), "c1");
    EXPECT_EQ(result.hijack_fix_res().fix_flow_group(), "f1");
}

TEST_F(HijackPolicyTestSuite, hit_repeated_search_head_node) {
    _fea.mutable_original_hijack_field()->CopyFrom(mock_hj_proto("c", "f", 0, 0));
    _fea.add_fix_hijack_field()->CopyFrom(mock_hj_proto("c", "f", -1, 0));

    PolicyResultProto result;
    ASSERT_TRUE(_policy.hit(_fea, &result));

    ASSERT_TRUE(result.hit());
    ASSERT_EQ(result.policy_id(), _policy.policy_id());
}

TEST_F(HijackPolicyTestSuite, not_hit_no_repeated_search_tail_node) {
    _fea.mutable_original_hijack_field()->CopyFrom(mock_hj_proto("c", "f", 0, 0));
    _fea.add_fix_hijack_field()->CopyFrom(mock_hj_proto("c", "f", 1, 0));

    PolicyResultProto result;
    ASSERT_TRUE(_policy.hit(_fea, &result));

    ASSERT_FALSE(result.hit());
}

TEST_F(HijackPolicyTestSuite, not_hit_repeated_search_tail_node_diff_pn) {
    _fea.mutable_original_hijack_field()->CopyFrom(mock_hj_proto("c", "f", 0, 1));
    _fea.add_fix_hijack_field()->CopyFrom(mock_hj_proto("c", "f", 1, 0));

    PolicyResultProto result;
    ASSERT_TRUE(_policy.hit(_fea, &result));

    ASSERT_TRUE(result.hit());
    ASSERT_EQ(result.policy_id(), _policy._policy_id);
}

// continued hijack
TEST_F(HijackPolicyTestSuite, continued_hijack_case_1) {
    _fea.mutable_original_hijack_field()->CopyFrom(mock_hj_proto("c", "f", 0, 0));
    _fea.add_fix_hijack_field()->CopyFrom(mock_hj_proto("c1", "f1", -1, 0));
    _fea.add_fix_hijack_field()->CopyFrom(mock_hj_proto("c2", "f2", 1, 0));

    PolicyResultProto result;
    ASSERT_TRUE(_policy.hit(_fea, &result));

    ASSERT_TRUE(result.hit());
    ASSERT_FALSE(result.pass_through());
    ASSERT_STREQ(result.hijack_fix_res().fix_cn().c_str(), "c2"); 
    ASSERT_STREQ(result.hijack_fix_res().fix_flow_group().c_str(), "f2");
    ASSERT_EQ(result.policy_id(), _policy.policy_id() + _policy._hijack_delta);
}

TEST_F(HijackPolicyTestSuite, continued_hijack_case_2) {
    _fea.mutable_original_hijack_field()->CopyFrom(mock_hj_proto("c", "f", 0, 0));
    _fea.add_fix_hijack_field()->CopyFrom(mock_hj_proto("c1", "f1", -1, 0));
    _fea.add_fix_hijack_field()->CopyFrom(mock_hj_proto("c2", "f2", 1, 0));
    _fea.add_fix_hijack_field()->CopyFrom(mock_hj_proto("c3", "f3", 2, 0));

    PolicyResultProto result;
    ASSERT_TRUE(_policy.hit(_fea, &result));

    ASSERT_TRUE(result.hit());
    ASSERT_FALSE(result.pass_through());
    ASSERT_STREQ(result.hijack_fix_res().fix_cn().c_str(), "c3"); 
    ASSERT_STREQ(result.hijack_fix_res().fix_flow_group().c_str(), "f3");
    ASSERT_EQ(result.policy_id(), _policy.policy_id() + _policy._hijack_delta);
}

TEST_F(HijackPolicyTestSuite, one_hijack_one_repeated_search) {
    _fea.mutable_original_hijack_field()->CopyFrom(mock_hj_proto("c", "f", 0, 0));
    _fea.add_fix_hijack_field()->CopyFrom(mock_hj_proto("c", "f", -1, 0));
    _fea.add_fix_hijack_field()->CopyFrom(mock_hj_proto("c2", "f2", 1, 0));

    PolicyResultProto result;
    ASSERT_TRUE(_policy.hit(_fea, &result));

    EXPECT_TRUE(result.hit());
    EXPECT_TRUE(result.pass_through());
    ASSERT_STREQ(result.hijack_fix_res().fix_cn().c_str(), "c2"); 
    ASSERT_STREQ(result.hijack_fix_res().fix_flow_group().c_str(), "f2");
    ASSERT_EQ(result.policy_id(), _policy.policy_id() + _policy._hijack_delta);
}

TEST_F(HijackPolicyTestSuite, no_hijack_one_repeated_search_case1) {
    _fea.mutable_original_hijack_field()->CopyFrom(mock_hj_proto("c", "f", 0, 0));
    _fea.add_fix_hijack_field()->CopyFrom(mock_hj_proto("c1", "f1", -2, 0));
    _fea.add_fix_hijack_field()->CopyFrom(mock_hj_proto("c", "f", 1, 0));

    PolicyResultProto result;
    ASSERT_TRUE(_policy.hit(_fea, &result));

    EXPECT_FALSE(result.hit());
    EXPECT_FALSE(result.pass_through());
}

TEST_F(HijackPolicyTestSuite, no_hijack_one_repeated_search_case2) {
    _fea.mutable_original_hijack_field()->CopyFrom(mock_hj_proto("c", "f", 0, 1));
    _fea.add_fix_hijack_field()->CopyFrom(mock_hj_proto("c1", "f1", -2, 0));
    _fea.add_fix_hijack_field()->CopyFrom(mock_hj_proto("c", "f", 1, 0));

    PolicyResultProto result;
    ASSERT_TRUE(_policy.hit(_fea, &result));

    EXPECT_TRUE(result.hit());
    ASSERT_FALSE(result.hijack_fix_res().exist_hijack());
    EXPECT_FALSE(result.pass_through());
    ASSERT_TRUE(result.policy_id() == _policy._policy_id);
}

TEST_F(HijackPolicyTestSuite, no_hijack_one_repeated_search_case3) {
    _fea.mutable_original_hijack_field()->CopyFrom(mock_hj_proto("c", "f", 0, 0));
    _fea.add_fix_hijack_field()->CopyFrom(mock_hj_proto("c", "f", -1, 0));
    _fea.add_fix_hijack_field()->CopyFrom(mock_hj_proto("c1", "f1", 2, 0));

    PolicyResultProto result;
    ASSERT_TRUE(_policy.hit(_fea, &result));

    EXPECT_TRUE(result.hit());
    ASSERT_FALSE(result.hijack_fix_res().exist_hijack());
    ASSERT_TRUE(result.policy_id() == _policy._policy_id);
}

TEST_F(HijackPolicyTestSuite, no_hijack_one_repeated_search_case4) {
    _fea.mutable_original_hijack_field()->CopyFrom(mock_hj_proto("c", "f", 0, 0));
    _fea.add_fix_hijack_field()->CopyFrom(mock_hj_proto("c", "f", -1, 1));
    _fea.add_fix_hijack_field()->CopyFrom(mock_hj_proto("c1", "f1", 2, 0));

    PolicyResultProto result;
    ASSERT_TRUE(_policy.hit(_fea, &result));

    EXPECT_FALSE(result.hit());
    EXPECT_FALSE(result.pass_through());
}

TEST_F(HijackPolicyTestSuite, hit_hijack_but_out_of_range) {
    _fea.mutable_original_hijack_field()->CopyFrom(mock_hj_proto("c", "f", 0, 0));
    _fea.add_fix_hijack_field()->CopyFrom(mock_hj_proto("c1", "f", -3, 0));
    _fea.add_fix_hijack_field()->CopyFrom(mock_hj_proto("c2", "f", -2, 0));
    _fea.add_fix_hijack_field()->CopyFrom(mock_hj_proto("c3", "f", 3, 0));
    _fea.add_fix_hijack_field()->CopyFrom(mock_hj_proto("c1", "f1", 4, 0));

    PolicyResultProto result;
    ASSERT_TRUE(_policy.hit(_fea, &result));

    EXPECT_FALSE(result.hit());
    EXPECT_FALSE(result.pass_through());
}

TEST_F(HijackPolicyTestSuite, one_hijack_one_repeated) {
    _fea.mutable_original_hijack_field()->CopyFrom(mock_hj_proto("c", "f", 0, 0));
    _fea.add_fix_hijack_field()->CopyFrom(mock_hj_proto("c", "f", -1, 0));
    _fea.add_fix_hijack_field()->CopyFrom(mock_hj_proto("c2", "f2", 1, 0));

    PolicyResultProto result;
    ASSERT_TRUE(_policy.hit(_fea, &result));

    ASSERT_TRUE(result.hit());
    ASSERT_TRUE(result.pass_through());
    ASSERT_STREQ(result.hijack_fix_res().fix_cn().c_str(), "c2"); 
    ASSERT_STREQ(result.hijack_fix_res().fix_flow_group().c_str(), "f2");
    ASSERT_EQ(result.policy_id(), _policy.policy_id() + _policy._hijack_delta);
}

}
}
}
