// Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
// <AUTHOR>
// @brief

#include <gtest/gtest.h>
#include "significance_ratio_rule.h"

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}

namespace anti {
namespace themis {
namespace feature_lib {

class TestSignificanceRatioRuleSuite : public ::testing::Test {
public:
    TestSignificanceRatioRuleSuite() {}
    ~TestSignificanceRatioRuleSuite() {}

    virtual void SetUp() {}
    virtual void TearDown() {}
private:
};

TEST_F(TestSignificanceRatioRuleSuite, sample_test) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "significance_rule_test.conf"));

    SignificanceRatioRule _obj;
    ASSERT_TRUE(_obj.init(conf["case1"]));
    _obj._cmp = ThresholdLimitRule::_greater;

    FeatureValueProto fea;
    fea.set_feature_type(FeatureValueProto::RATIO);
    fea.set_filter_count(37);
    fea.set_refer_count(263);
    fea.set_in_filter(true);
    fea.set_valid(true);

    bool hit = false;
    EXPECT_TRUE(_obj._check(fea, &hit));
    EXPECT_TRUE(hit);
    fea.set_filter_count(23);
    fea.set_refer_count(275);
    EXPECT_TRUE(_obj._check(fea, &hit));
    EXPECT_FALSE(hit);
}

}
}
}
