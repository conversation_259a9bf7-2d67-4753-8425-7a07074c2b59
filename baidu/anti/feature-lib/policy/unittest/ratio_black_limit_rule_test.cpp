// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: ratio_black_imit_rule_test.cpp
// @Last modified: 2015-09-22 17:07:05
// @Brief: 

#include <gtest/gtest.h>
#include <sign_util.h>
#include <file_dict_manager.h>
#include "ratio_black_limit_rule.h"

using anti::baselib::SignUtil;
using anti::themis::common_lib::FileDictManagerSingleton;

namespace anti {
namespace themis {
namespace feature_lib {

class RatioBlackLimitRuleTestSuite : public ::testing::Test {
public:
    virtual void SetUp() {}
    virtual void TearDown() {}

private:
    RatioBlackLimitRule _obj;
};

TEST_F(RatioBlackLimitRuleTestSuite, construction_case) {
    EXPECT_DOUBLE_EQ(0.0, _obj._threshold);
    EXPECT_TRUE(_obj._mode);
}

TEST_F(RatioBlackLimitRuleTestSuite, init_case) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "rule_test.conf"));

    uint32_t num = conf["ratio_black"]["invalid"].size();
    EXPECT_EQ(3U, num);
    for (uint32_t i = 0; i < num; i++) {
        EXPECT_FALSE(_obj._init(conf["ratio_black"]["invalid"][i]));
        _obj.uninit();
    }

    num = conf["ratio_black"]["valid"].size();
    EXPECT_EQ(2U, num);
    for (uint32_t i = 0; i < num; i++) {
        EXPECT_TRUE(_obj._init(conf["ratio_black"]["valid"][i]));
        EXPECT_DOUBLE_EQ(0.5, _obj._threshold);
        _obj.uninit();
    }
}

TEST_F(RatioBlackLimitRuleTestSuite, _check_invalid_case) {
    FeatureValueProto fea;
    fea.set_feature_type(FeatureValueProto::RATIO);
    EXPECT_FALSE(_obj._check(fea, NULL));

    fea.set_feature_type(FeatureValueProto::AUTO_RATIO); 
    EXPECT_FALSE(_obj._check(fea, NULL));

    bool hit = false;
    fea.set_in_filter(false);
    fea.set_in_refer(false);
    EXPECT_FALSE(_obj._check(fea, &hit));
}

TEST_F(RatioBlackLimitRuleTestSuite, _check_valid_case) {
    ASSERT_TRUE(FileDictManagerSingleton::instance().init(
            "./conf", "file_dict_manager.conf"));
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "rule_test.conf"));
    uint64_t sign = 0LU;
    SignUtil::create_sign_md64("78", &sign);
    EXPECT_NE(0LU, sign);
    
    // 1. in gray && in filter && valid && > threshold
    // 2. in gray && in refer && valid && > threshold
    // 3. NOT in gray && in filter && valid && > threshold
    // 4. NOT in gray && in refer && valid && > threshold
    // 5. NOT in gray && in filter && in refer && invalid && > threshold
    // 6. NOT in gray && in filter && in refer && valid && < threshold
    uint64_t view_signs[] = {sign, sign, 0LU, 0LU, 0LU, 0LU};
    bool in_fil[] = {true, false, true, false, true, true};
    bool in_ref[] = {false, true, false, true, true, true};
    bool valids[] = {true, true, true, true, false, true};
    double last_ratios[] = {0.2, 0.2, 0.6, 0.6, 0.6, 0.2};
    bool exp_filter_hits[] = {true, false, true, false, false, false};
    FeatureValueProto fea;
    fea.set_feature_type(FeatureValueProto::AUTO_RATIO);
        
    // filter mode
    ASSERT_TRUE(_obj.init(conf["ratio_black"]["valid"][0]));
    bool hit;
    for (uint32_t i = 0; i < 6U; i++) {
        fea.set_view_sign(view_signs[i]);
        fea.set_in_filter(in_fil[i]);
        fea.set_in_refer(in_ref[i]);
        fea.set_valid(valids[i]);
        fea.set_last_ratio(last_ratios[i]);
        EXPECT_TRUE(_obj._check(fea, &hit));
        EXPECT_EQ(exp_filter_hits[i], hit);
    }
    _obj.uninit();

    // refer mode
    bool exp_ref_hits[] = {false, true, false, true, false, false};
    ASSERT_TRUE(_obj.init(conf["ratio_black"]["valid"][1]));
    for (uint32_t i = 0; i < 6U; i++) {
        fea.set_view_sign(view_signs[i]);
        fea.set_in_filter(in_fil[i]);
        fea.set_in_refer(in_ref[i]);
        fea.set_valid(valids[i]);
        fea.set_last_ratio(last_ratios[i]);
        EXPECT_TRUE(_obj._check(fea, &hit));
        EXPECT_EQ(exp_ref_hits[i], hit);
    }
    _obj.uninit();
}

} // feature_lib
} // themis
} // anti

/* vim: set ts=4 sw=4: */

