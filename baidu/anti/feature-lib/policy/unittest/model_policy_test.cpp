// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: houruijie
// 
// @Brief: 

#include <memory>
#include <set>
#include <fstream>
#include <cstdio>
#include <algorithm>
#include <gtest/gtest.h>
#include <boost/algorithm/string.hpp>
#include <boost/lexical_cast.hpp>
#include <gmock/gmock.h>
#include <string>
#include <unordered_map>
#include "model_policy.h"
#include "gbdt_policy.h"
#include "feature_manager.h"

namespace anti {
namespace themis {
namespace feature_lib {

class MockModelBase : public ModelPolicy{
public:
    virtual bool predict(const FeaVecMap& fea_vec, PolicyResultProto* result) const {
        result->set_hit(true);
        return true;
    }
    virtual bool load_model(const std::string& model_path, const std::string& conf_file) {
        return true;
    }
};

class ModelPolicyTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {}
    virtual void TearDOwn() {}
private:
    MockModelBase _obj;
};

/************** multi view feature test start ****************/
typedef std::shared_ptr<FeatureValueProto> FeaPtr;

FeaPtr make_feature(const std::string& value, uint32_t feaid) {
    FeaPtr fea_ptr(new FeatureValueProto());
    fea_ptr->set_feature_id(feaid);
    fea_ptr->set_log_id(123LU);
    fea_ptr->set_valid(true);
    fea_ptr->set_value(value);
    return fea_ptr;
}

FeaPtr make_multi_value_feature(uint32_t feaid) {
    FeaPtr fea_ptr(new FeatureValueProto());
    fea_ptr->set_feature_id(feaid);
    fea_ptr->set_log_id(123LU);
    fea_ptr->set_valid(true);
    fea_ptr->set_feature_type(FeatureValueProto::MULTI_VALUE);
    return fea_ptr;
}

bool load_multi_view_data(
        FeatureManager* feature_manager,
        double* predict_result) {
    if (feature_manager == nullptr || 
            predict_result == nullptr) {
        return false;
    }

    std::fstream file("./data/multi_view_gbdt.data");
    // format : logid \t SingleViewFea*527 \t MultiViewFea*2 \t predictRes
    std::string line;
    std::vector<std::string> item_vec;
    if (getline(file, line)) {
        boost::split(item_vec, line, 
                boost::is_any_of("\t"), 
                boost::token_compress_on);
    }
    if (item_vec.size() != 561) {
        CWARNING_LOG("item_vec size(%d) != 561", item_vec.size());
        return false;
    }

    uint32_t feaid = 90000;
    // single view feature * 527
    for (uint32_t i = 1; i < 528; i++) {
        FeaPtr fea_ptr = make_feature(item_vec[i], feaid++);
        feature_manager->insert(fea_ptr);
    }
    // multi view feature * 2
    FeaPtr fea_ptr = make_multi_value_feature(feaid++);
    for (uint32_t i = 528; i < 544; i++) {
        fea_ptr->add_values(item_vec[i]);
    }
    feature_manager->insert(fea_ptr);
    fea_ptr = make_multi_value_feature(feaid++);
    for (uint32_t i = 544; i < 560; i++) {
        fea_ptr->add_values(item_vec[i]);
    }
    feature_manager->insert(fea_ptr);

    try {
        *predict_result = boost::lexical_cast<double>(item_vec.back());
    } catch (const boost::bad_lexical_cast& e) {
        CWARNING_LOG("%s\n", e.what());
        return false;
    }
    file.close();
    return true;
}

bool load_multi_view_data(
        FeaVecMap* slot2feaval,
        double* predict_result) {
    if (slot2feaval == nullptr || 
            predict_result == nullptr) {
        return false;
    }

    std::fstream file("./data/multi_view_gbdt.data");
    // format : logid \t SingleViewFea*527 \t MultiViewFea*2 \t predictRes
    std::string line;
    std::vector<std::string> item_vec;
    if (getline(file, line)) {
        boost::split(item_vec, line, 
                boost::is_any_of("\t"), 
                boost::token_compress_on);
    }
    if (item_vec.size() != 561) {
        CWARNING_LOG("item_vec size(%d) != 561", item_vec.size());
        return false;
    }

    for (uint32_t i = 1; i < 560; i++) {
        (*slot2feaval)[i - 1] = item_vec[i];
    }

    try {
        *predict_result = boost::lexical_cast<double>(item_vec.back());
    } catch (const boost::bad_lexical_cast& e) {
        CWARNING_LOG("%s\n", e.what());
        return false;
    }
    file.close();
    return true;
}

TEST_F(ModelPolicyTestSuite, expend_multi_value_fea_case) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "model_policy_test.conf"));
    ASSERT_TRUE(_obj.init(conf["policy"][5]));

    EXPECT_EQ(37, _obj._fea_num);
    _obj.uninit();
    _obj._fea_num = 0;
    _obj._slot_num = 0;

    ASSERT_TRUE(_obj.init(conf["policy"][6]));
    EXPECT_EQ(7, _obj._fea_num);
    _obj.uninit();
}

TEST_F(ModelPolicyTestSuite, join_slot2feaval_case) {
    comcfg::Configure conf;
    GBDTPolicy gbdt;
    ASSERT_EQ(0, conf.load("./conf", "model_policy_test.conf"));
    ASSERT_TRUE(gbdt.init(conf["policy"][4]));

    FeatureManager feature_manager;
    double predict_val;
    ASSERT_TRUE(load_multi_view_data(&feature_manager, &predict_val));

    // join_slot2feaval_res should be equal to slot2feaval
    FeaVecMap join_slot2feaval_res;
    FeaVecMap slot2feaval;
    ASSERT_TRUE(gbdt.join_slot2feaval(feature_manager, &join_slot2feaval_res));
    ASSERT_EQ(559, join_slot2feaval_res.size());

    ASSERT_TRUE(load_multi_view_data(&slot2feaval, &predict_val));
    ASSERT_EQ(559, slot2feaval.size());

    std::vector<std::string> vec1;
    std::vector<std::string> vec2;
    for (uint32_t slot = 0; slot < 559; ++slot) {
        ASSERT_TRUE(join_slot2feaval_res.find(slot) != join_slot2feaval_res.end());
        ASSERT_TRUE(slot2feaval.find(slot) != slot2feaval.end());
        vec1.push_back(join_slot2feaval_res[slot]);
        vec2.push_back(slot2feaval[slot]);
    }
    ASSERT_EQ(vec1.size(), vec2.size());
    for (uint32_t i = 0; i < vec1.size(); i++) {
        EXPECT_EQ(vec1[i], vec2[i]);
    }
}

/************** multi view feature test end ****************/

TEST_F(ModelPolicyTestSuite, construction_case) {
    EXPECT_EQ(0LU, _obj.policy_id()); 
} 

TEST_F(ModelPolicyTestSuite, init_case) { 
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "model_policy_test.conf"));
    uint32_t num = conf["invalid"].size();
    for (uint32_t i = 0U; i < num; i++) {
        EXPECT_FALSE(_obj.init(conf["invalid"][i]));
        _obj.uninit();
    }

    ASSERT_TRUE(_obj.init(conf["policy"][0]));
    EXPECT_EQ(20001LU, _obj.policy_id());
    EXPECT_TRUE(_obj._model_rule == NULL);
    _obj.uninit();

    ASSERT_TRUE(_obj.init(conf["policy"][2]));
    EXPECT_EQ(12345LU, _obj.policy_id());
    EXPECT_TRUE(_obj._model_rule != NULL);
    _obj.uninit();
}

TEST_F(ModelPolicyTestSuite, gen__case) {
    comcfg::Configure conf;

    std::string invalid = "1;";
    ASSERT_FALSE(_obj.load_slot2feaid(invalid));

    invalid = ",1;";
    ASSERT_FALSE(_obj.load_slot2feaid(invalid));

    std::string valid = "0,10000;1,10001";
    ASSERT_TRUE(_obj.load_slot2feaid(valid));

    EXPECT_EQ(2, _obj._slot2feaid.size());
    EXPECT_EQ(10000LU, _obj._slot2feaid[0]);
    EXPECT_EQ(10001LU, _obj._slot2feaid[1]);

    FeatureManager fm;
    std::shared_ptr<FeatureValueProto> fea1(new FeatureValueProto());
    fea1->set_feature_id(10000LU);
    fea1->set_log_id(123LU);
    fea1->set_valid(true);
    fea1->set_value("0.4");
    fm.insert(fea1);

    std::shared_ptr<FeatureValueProto> fea2(new FeatureValueProto());
    fea2->set_feature_id(10001LU);
    fea2->set_log_id(123LU);
    fea2->set_valid(true);
    fea2->set_value("0.2");
    fm.insert(fea2);

    _obj._policy_id = 10000LU;
    ASSERT_FALSE(_obj.join_slot2feaval(fm, NULL));

    FeaVecMap slot2feaval;
    _obj._fea_num = 2;

    EXPECT_EQ(10000LU, _obj._slot2feaid[0]);
    EXPECT_EQ(10001LU, _obj._slot2feaid[1]);
    ASSERT_TRUE(_obj.join_slot2feaval(fm, &slot2feaval));

    ASSERT_STREQ("0.4", slot2feaval[0].data());
    ASSERT_STREQ("0.2", slot2feaval[1].data());

    _obj._slot2feaid[2] = 10002LU;
    ASSERT_TRUE(_obj.join_slot2feaval(fm, &slot2feaval));
}

TEST_F(ModelPolicyTestSuite, predict_case) {
    FeatureManager fm;
    PolicyResultProto prp;
    ASSERT_TRUE(_obj.detect(fm, &prp));
}

TEST_F(ModelPolicyTestSuite, _care_case) {
    FeatureManager fm;
    EXPECT_TRUE(_obj._care(fm));

    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "model_policy_test.conf"));
    ASSERT_TRUE(_obj.init(conf["policy"][2]));
    _obj._model_rule->_rules.push_back(NULL);
    EXPECT_FALSE(_obj._care(fm));
    _obj.uninit();
}

TEST_F(ModelPolicyTestSuite, detect_case) {
    FeatureManager fm;
    ASSERT_FALSE(_obj.detect(fm, NULL));

    PolicyResultProto prp;
    _obj._policy_id = 888UL;
    ASSERT_TRUE(_obj.detect(fm, &prp));
    EXPECT_EQ(888UL, prp.policy_id());
    EXPECT_TRUE(prp.hit());

    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "model_policy_test.conf"));
    ASSERT_TRUE(_obj.init(conf["policy"][2]));
    ASSERT_TRUE(_obj.detect(fm, &prp));
    EXPECT_EQ(12345UL, prp.policy_id());
    EXPECT_FALSE(prp.hit());
    _obj.uninit();
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

