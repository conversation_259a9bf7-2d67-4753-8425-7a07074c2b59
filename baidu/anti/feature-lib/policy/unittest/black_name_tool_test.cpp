// Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
// @Author: longfei(<EMAIL>)
// 

#include <gtest/gtest.h>
#include "black_name_tool.h"

namespace anti {
namespace themis {
namespace feature_lib {

class BlackNameToolTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {}
    virtual void TearDown() {}

private:
    BlackNameTool  _obj;
};

TEST_F(BlackNameToolTestSuite, init_fail_case) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "black_name_tool_test.conf"));
    uint32_t num = conf["invalid"].size();
    for (uint32_t i = 0U; i < num; ++i) {
        EXPECT_FALSE(_obj.init(conf["invalid"][i]));
        _obj.uninit();
    }
}

TEST_F(BlackNameToolTestSuite, init_succ_case) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "black_name_tool_test.conf"));
    uint32_t num = conf["valid"].size();
    ASSERT_EQ(6, num);

    ASSERT_TRUE(_obj.init(conf["valid"][0]));
    EXPECT_EQ(111LU, _obj._policy_id);
    EXPECT_EQ(0U, _obj._blk_feas.size());
    _obj.uninit();

    ASSERT_TRUE(_obj.init(conf["valid"][1]));
    EXPECT_EQ(111LU, _obj._policy_id);
    EXPECT_EQ(0U, _obj._blk_feas.size());
    _obj.uninit();

    ASSERT_TRUE(_obj.init(conf["valid"][2]));
    EXPECT_EQ(111LU, _obj._policy_id);
    EXPECT_EQ(2U, _obj._blk_feas.size());
    _obj.uninit();

    ASSERT_TRUE(_obj.init(conf["valid"][3]));
    EXPECT_EQ(6666666LU, _obj._policy_id);
    EXPECT_EQ(2U, _obj._blk_feas.size());
    _obj.uninit();
}

TEST_F(BlackNameToolTestSuite, add_feature_result_case) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "black_name_tool_test.conf"));

    FeatureManager fm;
    PolicyResultProto result;

    //result is NULL
    EXPECT_FALSE(_obj.add_feature_result(fm, NULL));

    // _blk_fea is empty
    _obj._blk_feas.clear();
    EXPECT_TRUE(_obj.add_feature_result(fm, &result));

    std::shared_ptr<FeatureValueProto> fea1(new FeatureValueProto());
    fea1->set_feature_id(111LU);
    fea1->set_log_time(0);
    fm.insert(fea1);
    std::shared_ptr<FeatureValueProto> fea2(new FeatureValueProto());
    fea2->set_feature_id(222LU);
    fea2->set_log_time(0);
    fm.insert(fea2);
    std::shared_ptr<FeatureValueProto> fea3(new FeatureValueProto());
    fea3->set_feature_id(333LU);
    fea3->set_log_time(0);
    fm.insert(fea3);

    ASSERT_TRUE(_obj.init(conf["valid"][2]));
    ASSERT_TRUE(_obj.add_feature_result(fm, &result)); 
    EXPECT_EQ(2U, result.hit_fea_size());
    auto feas = result.hit_fea();
    for (const auto& fea : feas) {
        if (fea.feature_id() == 111) {
            EXPECT_EQ(3600, fea.black_timespan());
        } else if (fea.feature_id() == 222) {
            EXPECT_EQ(7200, fea.black_timespan());
        }
    } 
    _obj.uninit();
    result.Clear();

    ASSERT_TRUE(_obj.init(conf["valid"][3]));
    ASSERT_TRUE(_obj.add_feature_result(fm, &result)); 
    EXPECT_EQ(2U, result.hit_fea_size());
    for (const auto& fea : feas) {
        if (fea.feature_id() == 111) {
            EXPECT_EQ(3600, fea.black_timespan());
        } else if (fea.feature_id() == 222) {
            EXPECT_EQ(7200, fea.black_timespan());
        }

    } 
    _obj.uninit();
    result.Clear();

    ASSERT_TRUE(_obj.init(conf["valid"][4]));
    ASSERT_TRUE(_obj.add_feature_result(fm, &result)); 
    EXPECT_EQ(2U, result.hit_fea_size());
    for (const auto& fea : feas) {
        if (fea.feature_id() == 111) {
            EXPECT_EQ(3600, fea.black_timespan());
        } else if (fea.feature_id() == 222) {
            EXPECT_EQ(7200, fea.black_timespan());
        }

    } 
    _obj.uninit();
    result.Clear();

    ASSERT_TRUE(_obj.init(conf["valid"][5]));
    ASSERT_TRUE(_obj.add_feature_result(fm, &result)); 
    EXPECT_EQ(0U, result.hit_fea_size());
    _obj.uninit();
    result.Clear();
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

