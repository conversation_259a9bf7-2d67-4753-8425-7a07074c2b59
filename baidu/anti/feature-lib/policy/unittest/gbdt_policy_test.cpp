// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: houruijie
// 
// @Brief: 

#include <memory>
#include <iostream>
#include <cstdio>
#include <vector>
#include <cstring>
#include <fstream>
#include <boost/algorithm/string.hpp>
#include <boost/lexical_cast.hpp>
#include <gtest/gtest.h>
#include "policy_result.pb.h"
#include "gbdt_policy.h"
#include "policy_factory.h"

namespace anti {
namespace themis {
namespace feature_lib {

class GBDTPolicyTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {}
    virtual void TearDOwn() {}
private:
    GBDTPolicy _obj;
};

/**************** test for multi view start *******************/
typedef std::shared_ptr<FeatureValueProto> FeaPtr;
typedef anti::themis::feature_lib::PolicyResultProto PolicyResultProto;

FeaPtr make_feature(const std::string& value, uint32_t feaid) {
    FeaPtr fea_ptr(new FeatureValueProto());
    fea_ptr->set_feature_id(feaid);
    fea_ptr->set_log_id(123LU);
    fea_ptr->set_valid(true);
    fea_ptr->set_value(value);
    return fea_ptr;
}

FeaPtr make_multi_value_feature(uint32_t feaid) {
    FeaPtr fea_ptr(new FeatureValueProto());
    fea_ptr->set_feature_id(feaid);
    fea_ptr->set_log_id(123LU);
    fea_ptr->set_valid(true);
    fea_ptr->set_feature_type(FeatureValueProto::MULTI_VALUE);
    return fea_ptr;
}

bool load_multi_view_data(
        std::vector<FeatureManager>* feature_manager_vec,
        std::vector<double>* predict_result_vec) {
    if (feature_manager_vec == nullptr || 
            predict_result_vec == nullptr) {
        return false;
    }

    std::fstream file("./data/multi_view_gbdt.data");
    // format : logid \t SingleViewFea*527 \t MultiViewFea*2 \t predictRes
    std::string line;
    std::vector<std::string> item_vec;
    while (getline(file, line)) {
        boost::split(item_vec, line, 
                boost::is_any_of("\t"), 
                boost::token_compress_on);
        if (item_vec.size() != 561) {
            CWARNING_LOG("item_vec size(%d) != 561", item_vec.size());
            return false;
        }

        FeatureManager feature_manager;
        uint32_t feaid = 90000;
        // single view feature * 527
        for (uint32_t i = 1; i < 528; i++) {
            FeaPtr fea_ptr = make_feature(item_vec[i], feaid++);
            feature_manager.insert(fea_ptr);
        }
        // multi view feature * 2
        FeaPtr fea_ptr = make_multi_value_feature(feaid++);
        for (uint32_t i = 528; i < 544; i++) {
            fea_ptr->add_values(item_vec[i]);
        }
        feature_manager.insert(fea_ptr);
        fea_ptr = make_multi_value_feature(feaid++);
        for (uint32_t i = 544; i < 560; i++) {
            fea_ptr->add_values(item_vec[i]);
        }
        feature_manager.insert(fea_ptr);
        feature_manager_vec->push_back(feature_manager);

        try {
            double predict_result = boost::lexical_cast<double>(item_vec.back());
            predict_result_vec->push_back(predict_result);
        } catch (const boost::bad_lexical_cast& e) {
            CWARNING_LOG("%s\n", e.what());
            return false;
        }
    }
    file.close();
    return true;
}

bool load_multi_view_data(
        FeaVecMap* slot2feaval,
        double* predict_result) {
    if (slot2feaval == nullptr || 
            predict_result == nullptr) {
        return false;
    }

    std::fstream file("./data/multi_view_gbdt.data");
    // format : logid \t SingleViewFea*527 \t MultiViewFea*2 \t predictRes
    std::string line;
    std::vector<std::string> item_vec;
    if (getline(file, line)) {
        boost::split(item_vec, line, 
                boost::is_any_of("\t"), 
                boost::token_compress_on);
    }
    if (item_vec.size() != 561) {
        CWARNING_LOG("item_vec size(%d) != 561", item_vec.size());
        return false;
    }

    for (uint32_t i = 1; i < 560; i++) {
        (*slot2feaval)[i - 1] = item_vec[i];
    }

    try {
        *predict_result = boost::lexical_cast<double>(item_vec.back());
    } catch (const boost::bad_lexical_cast& e) {
        CWARNING_LOG("%s\n", e.what());
        return false;
    }
    file.close();
    return true;
}

TEST_F(GBDTPolicyTestSuite, multi_view_init) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "model_policy_test.conf"));

    ASSERT_TRUE(_obj.init(conf["policy"][4]));
    ASSERT_EQ(900001LU, _obj.policy_id());
    ASSERT_TRUE(_obj._model_rule == NULL);
    EXPECT_EQ(559, _obj._fea_num);
    EXPECT_EQ(529, _obj._slot2feaid.size());
}

TEST_F(GBDTPolicyTestSuite, multi_view_detect) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "model_policy_test.conf"));
    ASSERT_TRUE(_obj.init(conf["policy"][4]));

    std::vector<FeatureManager> feature_manager_vec;
    std::vector<double> predict_val_vec;
    ASSERT_TRUE(load_multi_view_data(&feature_manager_vec, &predict_val_vec));
    ASSERT_EQ(90, feature_manager_vec.size());
    ASSERT_EQ(90, predict_val_vec.size());

    for (uint32_t i = 0; i < 90; ++i) {
        PolicyResultProto result;
        ASSERT_TRUE(_obj.detect(feature_manager_vec[i], &result));
        EXPECT_TRUE(abs(predict_val_vec[i] - result.value()) < 0.0001);
        if (abs(predict_val_vec[i] - result.value()) >= 0.0001) {
            CWARNING_LOG("%lf vs %lf", predict_val_vec[i], result.value());
        }
    }
}
/**************** test for multi view end *******************/

TEST_F(GBDTPolicyTestSuite, init_case) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "model_policy_test.conf"));

    ASSERT_FALSE(_obj.load_model("./conf", "error.conf"));

    _obj._policy_id = 10000;
    ASSERT_FALSE(_obj.load_model("./conf", "model.conf"));

    _obj._policy_id = 10001;
    ASSERT_FALSE(_obj.load_model("./conf", "model.conf"));

    _obj._policy_id = 20001;
    ASSERT_TRUE(_obj.load_model("./conf", "model.conf"));
    ASSERT_DOUBLE_EQ(0.3, _obj._threshold);

    _obj.uninit();
}

TEST_F(GBDTPolicyTestSuite, normalize_case) {
    FeaVecMap fi;
    fi[0] = "0.3";
    fi[1] = "100";
    fi[2] = "-9";

    const std::shared_ptr<FeatureValueProto> fea_ptr(new FeatureValueProto());
    FeatureValueProto* fea = NULL;
    fea = fea_ptr.get();
    fea->set_feature_id(90001LU);
    fea->set_feature_type(FeatureValueProto::RATIO);

    std::string value = GBDTPolicy::DEFAULT_GBDT_VALUE;
    ASSERT_FALSE(_obj._normalize(fea, 0, "0", NULL));
    _obj._fea_num = 1;
    ASSERT_FALSE(_obj._normalize(fea, 3, "0.3", &value));

    _obj._fea_num = 1;
    ASSERT_TRUE(_obj._normalize(fea, 0, "0.3", &value));
    ASSERT_EQ("0.3", value);
    ASSERT_TRUE(_obj._normalize(fea, 0, "", &value));
    ASSERT_EQ(GBDTPolicy::DEFAULT_GBDT_VALUE, value);
}

TEST_F(GBDTPolicyTestSuite, _get_fea_list) {
    FeaVecMap fi;
    fi[0] = "0.3";
    fi[1] = "100";
    fi[2] = "-9";
    fi[3] = "-";

    ASSERT_FALSE(_obj._get_fea_list(fi, NULL));

    double fea_list[ModelPolicy::MAX_FEATURE_NUM];
    _obj._fea_num = 4;
    ASSERT_TRUE(_obj._get_fea_list(fi, fea_list));

    ASSERT_DOUBLE_EQ(0.3, fea_list[0]);
    ASSERT_EQ(100, fea_list[1]);
    ASSERT_EQ(-9, fea_list[2]);
    ASSERT_EQ(0, fea_list[3]);
}

TEST_F(GBDTPolicyTestSuite, predict) {
    FeaVecMap fi;
    fi[0] = "0.238";
    fi[1] = "-0.165";
    fi[2] = "0.338";

    PolicyResultProto result;
    const FeatureManager feas;

    ASSERT_FALSE(_obj.predict(fi, NULL));
    ASSERT_FALSE(_obj.predict(fi, &result));

    _obj._policy_id = 20001;
    ASSERT_TRUE(_obj.load_model("./conf", "model.conf"));
    ASSERT_TRUE(_obj.predict(fi, &result));
    ASSERT_DOUBLE_EQ(0.24487614631652832, result.value());
    ASSERT_FALSE(result.hit());

    fi[0] = "0.238";
    fi[1] = "5.612";
    fi[2] = "1.758";
    ASSERT_TRUE(_obj.predict(fi, &result));
    ASSERT_DOUBLE_EQ(0.5942995548248291, result.value());
    ASSERT_TRUE(result.hit());
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti
/* vim: set ts=4 sw=4: */
