// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// @Author: lijie(<EMAIL>)
// 
// @File: acp_limit_rule_test.cpp

#include <gtest/gtest.h>
#include <sign_util.h>
#include "acp_limit_rule.h"

using anti::baselib::SignUtil;

namespace anti {
namespace themis {
namespace feature_lib {

class AcpLimitRuleTestSuite : public ::testing::Test {
public:
    virtual void SetUp() {}
    virtual void TearDown() {}

private:
    AcpLimitRule _obj;
};

TEST_F(AcpLimitRuleTestSuite, construction_case) {
    EXPECT_DOUBLE_EQ(0.0, _obj._threshold);
}

TEST_F(AcpLimitRuleTestSuite, init_case) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "rule_test.conf"));

    EXPECT_FALSE(_obj._init(conf["acp"]["invalid"]));
    _obj.uninit();

    ASSERT_TRUE(_obj._init(conf["acp"]["valid"][0]));
    EXPECT_DOUBLE_EQ(2.0, _obj._threshold);
    _obj.uninit();

    ASSERT_TRUE(_obj._init(conf["acp"]["valid"][1]));
    EXPECT_DOUBLE_EQ(10.0, _obj._threshold);
    _obj.uninit();
}

TEST_F(AcpLimitRuleTestSuite, _check_by_invalid_input_case) {
    FeatureValueProto fea;
    bool hit;
    fea.set_feature_type(FeatureValueProto::SEGMENT);
    EXPECT_FALSE(_obj._check(fea, &hit));

    fea.set_feature_type(FeatureValueProto::ACP);
    EXPECT_FALSE(_obj._check(fea, &hit));

    fea.set_valid(true);
    EXPECT_FALSE(_obj._check(fea, &hit));

    fea.mutable_acp_field()->set_residual(1.0);
    EXPECT_FALSE(_obj._check(fea, NULL));
}

TEST_F(AcpLimitRuleTestSuite, _check_by_valid_input_case) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "rule_test.conf"));
    ASSERT_TRUE(_obj.init(conf["acp"]["valid"][1]));

    uint64_t sign = 0LU;
    SignUtil::create_sign_md64("123", &sign);
    EXPECT_NE(0LU, sign);
    
    uint64_t view_signs[] = {sign, 0LU, 0LU, 0LU};
    double residual[] = {2.0, 5.4, 31, 25};
    bool exps[] = {false, false, true, true};
    for (uint32_t i = 0; i < 4U; ++i) {
        FeatureValueProto fea;
        fea.set_feature_type(FeatureValueProto::ACP);
        fea.set_view_sign(view_signs[i]);
        fea.mutable_acp_field()->set_residual(residual[i]);
        fea.set_valid(true);
        bool hit = false;
        ASSERT_TRUE(_obj._check(fea, &hit));
        EXPECT_EQ(exps[i], hit);
    }
    _obj.uninit();
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

