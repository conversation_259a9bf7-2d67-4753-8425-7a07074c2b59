// Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
// @Author: xiarenjie
//
// @Brief:

#include <memory>
#include <iostream>
#include <cstdio>
#include <vector>
#include <cstring>
#include <fstream>
#include <boost/algorithm/string.hpp>
#include <boost/lexical_cast.hpp>
#include <gtest/gtest.h>
#include "policy_result.pb.h"
#include "policy_factory.h"
#include "xgboost_policy.h"

namespace anti {
namespace themis {
namespace feature_lib {

class XGBPolicyTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {}
    virtual void TearDOwn() {}
private:
    XGBPolicy _obj;
};

typedef std::shared_ptr<FeatureValueProto> FeaPtr;
typedef anti::themis::feature_lib::PolicyResultProto PolicyResultProto;

/**************** test for multi view start *******************/
FeaPtr make_feature(const std::string& value, uint32_t feaid) {
    FeaPtr fea_ptr(new FeatureValueProto());
    fea_ptr->set_feature_id(feaid);
    fea_ptr->set_log_id(123LU);
    fea_ptr->set_valid(true);
    fea_ptr->set_value(value);
    return fea_ptr;
}

FeaPtr make_multi_value_feature(uint32_t feaid) {
    FeaPtr fea_ptr(new FeatureValueProto());
    fea_ptr->set_feature_id(feaid);
    fea_ptr->set_log_id(123LU);
    fea_ptr->set_valid(true);
    fea_ptr->set_feature_type(FeatureValueProto::MULTI_VALUE);
    return fea_ptr;
}

bool load_multi_view_data(
        std::vector<FeatureManager>* feature_manager_vec,
        std::vector<double>* predict_result_vec) {
    if (feature_manager_vec == nullptr ||
            predict_result_vec == nullptr) {
        return false;
    }

    std::fstream file("./data/multi_view_xgboost.data");
    // format : logid \t SingleViewFea*527 \t MultiViewFea*2 \t predictRes
    std::string line;
    std::vector<std::string> item_vec;
    while (getline(file, line)) {
        boost::split(item_vec, line,
                boost::is_any_of("\t"),
                boost::token_compress_on);
        if (item_vec.size() != 561) {
            CWARNING_LOG("item_vec size(%d) != 561", item_vec.size());
            return false;
        }

        FeatureManager feature_manager;
        uint32_t feaid = 90000;
        // single view feature * 527
        for (uint32_t i = 1; i < 528; i++) {
            FeaPtr fea_ptr = make_feature(item_vec[i], feaid++);
            feature_manager.insert(fea_ptr);
        }
        // multi view feature * 2
        FeaPtr fea_ptr = make_multi_value_feature(feaid++);
        for (uint32_t i = 528; i < 544; i++) {
            fea_ptr->add_values(item_vec[i]);
        }
        feature_manager.insert(fea_ptr);
        fea_ptr = make_multi_value_feature(feaid++);
        for (uint32_t i = 544; i < 560; i++) {
            fea_ptr->add_values(item_vec[i]);
        }
        feature_manager.insert(fea_ptr);
        feature_manager_vec->push_back(feature_manager);

        try {
            double predict_result = boost::lexical_cast<double>(item_vec.back());
            predict_result_vec->push_back(predict_result);
        } catch (const boost::bad_lexical_cast& e) {
            CWARNING_LOG("%s\n", e.what());
            return false;
        }
    }
    file.close();
    return true;
}

TEST_F(XGBPolicyTestSuite, multi_view_init) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "model_policy_test.conf"));

    ASSERT_TRUE(_obj.init(conf["policy"][7]));
    ASSERT_EQ(40000LU, _obj.policy_id());
    ASSERT_TRUE(_obj._model_rule == NULL);
    EXPECT_EQ(559, _obj._fea_num);
    EXPECT_EQ(529, _obj._slot2feaid.size());
    EXPECT_EQ("0", _obj._missing_value);
}

TEST_F(XGBPolicyTestSuite, multi_view_init_nan) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "model_policy_test.conf"));

    ASSERT_TRUE(_obj.init(conf["policy"][8]));
    ASSERT_EQ(4000001LU, _obj.policy_id());
    ASSERT_TRUE(_obj._model_rule == NULL);
    EXPECT_EQ(559, _obj._fea_num);
    EXPECT_EQ(529, _obj._slot2feaid.size());
    EXPECT_EQ("NAN", _obj._missing_value);
}

TEST_F(XGBPolicyTestSuite, multi_view_detect) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "model_policy_test.conf"));
    ASSERT_TRUE(_obj.init(conf["policy"][7]));

    std::vector<FeatureManager> feature_manager_vec;
    std::vector<double> predict_val_vec;
    ASSERT_TRUE(load_multi_view_data(&feature_manager_vec, &predict_val_vec));
    ASSERT_EQ(10, feature_manager_vec.size());
    ASSERT_EQ(10, predict_val_vec.size());

    for (uint32_t i = 0; i < 10; ++i) {
        PolicyResultProto result;
        ASSERT_TRUE(_obj.detect(feature_manager_vec[i], &result));
        EXPECT_TRUE(abs(predict_val_vec[i] - result.value()) < 0.0001);
        if (abs(predict_val_vec[i] - result.value()) >= 0.0001) {
            CWARNING_LOG("%lf vs %lf", predict_val_vec[i], result.value());
        }
    }
}
/**************** test for multi view end *******************/

TEST_F(XGBPolicyTestSuite, load_model) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "model_policy_test.conf"));

    ASSERT_FALSE(_obj.load_model("./conf", "error.conf"));

    _obj._policy_id = 40002;
    ASSERT_FALSE(_obj.load_model("./conf", "model.conf"));

    _obj._policy_id = 40001;
    ASSERT_FALSE(_obj.load_model("./conf", "model.conf"));

    _obj._policy_id = 40000;
    ASSERT_TRUE(_obj.load_model("./conf", "model.conf"));
    ASSERT_FLOAT_EQ(0.2, _obj._threshold);
    ASSERT_EQ("0", _obj._missing_value);

    _obj._policy_id = 4000001;
    ASSERT_TRUE(_obj.load_model("./conf", "model.conf"));
    ASSERT_FLOAT_EQ(0.2, _obj._threshold);
    ASSERT_EQ("0", _obj._missing_value);
}

TEST_F(XGBPolicyTestSuite, _get_fea_list) {
    FeaVecMap fi;
    fi[0] = "0.3";
    fi[1] = "100";
    fi[2] = "-9";
    fi[3] = "-";

    ASSERT_FALSE(_obj._get_fea_list(fi, NULL));

    float fea_list[ModelPolicy::MAX_FEATURE_NUM];
    _obj._fea_num = 4;
    ASSERT_TRUE(_obj._get_fea_list(fi, fea_list));

    ASSERT_FLOAT_EQ(0.3, fea_list[0]);
    ASSERT_FLOAT_EQ(100, fea_list[1]);
    ASSERT_FLOAT_EQ(-9, fea_list[2]);
    ASSERT_FLOAT_EQ(0, fea_list[3]);
}

TEST_F(XGBPolicyTestSuite, _normalize) {
    FeaVecMap fi;
    fi[0] = "0.3";
    fi[1] = "100";
    fi[2] = "-9";

    _obj._policy_id = 40000;
    ASSERT_TRUE(_obj.load_model("./conf", "model.conf"));
    ASSERT_FLOAT_EQ(0.2, _obj._threshold);
    ASSERT_EQ("0", _obj._missing_value);

    const std::shared_ptr<FeatureValueProto> fea_ptr(new FeatureValueProto());
    FeatureValueProto* fea = NULL;
    fea = fea_ptr.get();
    fea->set_feature_id(40000LU);
    fea->set_feature_type(FeatureValueProto::RATIO);

    //std::string value = XGBPolicy::DEFAULT_XGB_VALUE;
    std::string value = _obj._missing_value;
    ASSERT_EQ("0", value);
    ASSERT_FALSE(_obj._normalize(fea, 0, "0", NULL));
    _obj._fea_num = 1;
    ASSERT_FALSE(_obj._normalize(fea, 3, "0.3", &value));

    _obj._fea_num = 1;
    ASSERT_TRUE(_obj._normalize(fea, 0, "0.3", &value));
    ASSERT_EQ("0.3", value);
    ASSERT_TRUE(_obj._normalize(fea, 0, "", &value));
    ASSERT_EQ("0", value);
}

TEST_F(XGBPolicyTestSuite, _normalize_nan) {
    // missing_value = "NAN"
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "model_policy_test.conf"));

    ASSERT_TRUE(_obj.init(conf["policy"][8]));
    ASSERT_EQ(4000001LU, _obj.policy_id());
    ASSERT_FLOAT_EQ(0.2, _obj._threshold);
    ASSERT_EQ("NAN", _obj._missing_value);

    const std::shared_ptr<FeatureValueProto> fea_ptr(new FeatureValueProto());
    FeatureValueProto* fea_nan = NULL;
    fea_nan = fea_ptr.get();
    fea_nan->set_feature_id(4000001LU);
    fea_nan->set_feature_type(FeatureValueProto::RATIO);
    
    std::string value = "";
    ASSERT_FALSE(_obj._normalize(fea_nan, 0, "0", NULL));
    _obj._fea_num = 1;
    ASSERT_FALSE(_obj._normalize(fea_nan, 3, "0.3", &value));

    _obj._fea_num = 1;
    ASSERT_TRUE(_obj._normalize(fea_nan, 0, "0.3", &value));
    ASSERT_EQ("0.3", value);
    ASSERT_TRUE(_obj._normalize(fea_nan, 0, "", &value));
    ASSERT_EQ("NAN", value);
}

TEST_F(XGBPolicyTestSuite, predict) {
    FeaVecMap fi;
    fi[0] = "1.0000";
    fi[1] = "2.0000";
    fi[2] = "3.0000";

    PolicyResultProto result;
    const FeatureManager feas;

    ASSERT_FALSE(_obj.predict(fi, NULL));
    ASSERT_FALSE(_obj.predict(fi, &result));
    
    _obj._fea_num = 3;
    _obj._policy_id = 40000;
    ASSERT_TRUE(_obj.load_model("./conf", "model.conf"));
    ASSERT_TRUE(_obj.predict(fi, &result));
    ASSERT_FLOAT_EQ(0.21774099767208099, result.value());
    ASSERT_TRUE(result.hit());

    fi[0] = "2.0000";
    fi[1] = "4.0000";
    fi[2] = "6.0000";
    ASSERT_TRUE(_obj.predict(fi, &result));
    ASSERT_FLOAT_EQ(0.02151944488286972, result.value());
    ASSERT_FALSE(result.hit());

    // test
    _obj._fea_num = 150;
    _obj._policy_id = 30000;
    ASSERT_TRUE(_obj.load_model("./conf", "model.conf"));

    float test_sample[150] = {0,0,0,0,0.175439,0,0,0,0,1,0,0,0,0,0.769230783,
            0,0,0,0.79780221,0,0,0.0217391308,0,0,0,0,0.0791208819,0,0,0,0,0,
            0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,384,0,0,0,0,0,0,0,0,0,0,
            0.111111112,0.0153846154,0,0,0.0131868133,0,0,0,0,0,0,0,0,0,0,0,0,
            0,0,0,0.793407023,455,0,0,0,0.5,0.793406606,0,0,0,0,0,0,0,0,
            0.097826086,0,0,0,0,0,0,0,0,0.674725294,0,0,0,0,0,0,0,0,0,0,0,0,
            0.79780221,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,1,0,0,1,1,0,0,0,0,0,0,1,1};
    for (int i = 0; i < 150; i++) {
        fi[i] = std::to_string(test_sample[i]);
    }
    ASSERT_TRUE(_obj.predict(fi, &result));
    ASSERT_FLOAT_EQ(0.96884066, result.value());

    // test_nan
    
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "model_policy_test.conf"));

    ASSERT_TRUE(_obj.init(conf["policy"][9]));
    ASSERT_EQ(9960009LU, _obj.policy_id());
    ASSERT_FALSE(_obj._model_rule == NULL);
    EXPECT_EQ(157, _obj._fea_num);
    EXPECT_EQ(157, _obj._slot2feaid.size());
    EXPECT_EQ("NAN", _obj._missing_value);

    float test_nan_sample[157] = {0.051345,0.011708,0.670732,0.5,0.036667,0.0,0.071667,132847.0,1.0,1.0,0.5,2509.0,NAN,NAN,65.0,0.188333,NAN,0.111111,NAN,0.087041,0.04294,20199.0,NAN,421.0,NAN,0.035817,NAN,NAN,0.025,0.666667,0.044528,0.137811,0.025532,16253.0,0.137614,NAN,0.083333,66.0,NAN,NAN,0.068129,-0.046992,3.0,1.0,0.081261,0.525432,358316638.0,120.0,0.039144,NAN,1438.0,0.25,1072078.0,NAN,NAN,0.036585,NAN,0.022452,1.0,0.181003,0.000889,0.022491,0.145745,0.111111,0.121951,45.0,82.0,0.111111,0.428173,0.03125,32.0,0.091408,45.0,NAN,0.008841,1.0,-0.062074,0.083363,0.101339,NAN,0.001302,0.432615,0.736903,0.666667,1.0,0.003116,0.097979,0.012204,0.04162,0.121725,0.316237,0.072875,104.0,73125.0,0.221196,1.0,0.5,0.016667,1920696.0,NAN,0.065784,0.074018,0.024801,0.68269,0.065017,0.036667,0.011667,1.0,0.66263,NAN,0.04798,0.035925,600.0,0.019441,0.111111,45.0,2.0,1.0,1.0,0.041667,NAN,0.058994,2585.0,NAN,1.0,0.0637,0.019057,NAN,0.000761,NAN,0.724747,0.001302,NAN,NAN,0.193772,0.0009,0.111111,NAN,0.044959,0.505222,NAN,0.5,0.161248,24.0,NAN,NAN,0.083333,NAN,NAN,0.086484,NAN,0.026881,1.0,0.070464,0.008333,0.028189,0.02567};
    for (int i = 0; i < 157; i++) {
        fi[i] = std::to_string(test_nan_sample[i]);
    }
    ASSERT_TRUE(_obj.predict(fi, &result));
    ASSERT_FLOAT_EQ(0.00044314409, result.value());
    
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti
/* vim: set ts=4 sw=4: */
