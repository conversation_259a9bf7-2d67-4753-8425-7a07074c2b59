
#xgboost valid
[30000]
threshold : 0.5
model_file : ./conf/fc_wise_lu_base_model.9960010.model

#xgboost valid
[40000]
threshold : 0.2
model_file : ./conf/xgboost.40000.model

#xgboost valid
[4000001]
threshold : 0.2
model_file : ./conf/xgboost.4000001.model

#xgboost invalid threshold
[40001]
threshold : none
model_file : ./conf/xgboost.40000.model

# xgboost invalid model_file
[40002]
threshold : 0.2
model_file : ./conf/none

# gbdt valid
[20001]
threshold : 0.3
model_file : ./conf/gbdt.20001.model

# gbdt invalid threshold
[10000]
threshold : none
model_file : ./conf/gbdt.20001.model

# gbdt invalid model_file
[10001]
threshold : none
model_file : ./conf/none

# dnn
[20000]
threshold : 0.5
model_struct_file: ./conf/dnn.desp
normalize_file : ./conf/dnn.fea.stat
model_weight_file: ./conf/dnn.weight

# lr
[30001]
threshold :
model_file : ./conf/lr.fea.weight

# lr
[30002]
threshold : 0.3
model_file : done

# lr
[30003]
threshold : 0.3
decimal_places : 3
model_file : ./conf/lr.fea.weight

#ltr gbdt
[50000]
threshold: 1.4
model_file: ./data/demo_model.bgm
missing_value: 0.0
default_value: 0.1
invalid_value: 0.2

[50001]
threshold: 1
model_file: ./data/demo_model.bgm
missing_value: 3
default_value: null
invalid_value: -1

[50002]
threshold: 1
model_file: ./conf/not_exist_model.bgm
missing_value: 3
default_value: -1
invalid_value: -1
