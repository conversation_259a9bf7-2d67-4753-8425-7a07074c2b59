[@file]
file_key  : cnt_map
file_type : cnmap
file_path : ./data/cnt_group.txt

[SELECTOR]
[.@INVALID]

[.@INVALID]
session_time : 3600
priority_range : xxxxxxxxx

[.@VALID]
session_time : 3600

[.@VALID]
session_time : 3600
log_type : 1

[.@VALID]
session_time : 3600
log_type : 1
@select : xxx
priority_range : 123,922337203685477580

[SESSION_POLICY_BASE]
[.@INVALID]

[.@INVALID]
policy_id : 123

[.@INVALID]
policy_id : 123
feature_id : 123

[.@INVALID]
policy_id : 123
feature_id : 123
dict : xxx

[.@INVALID]
policy_id : 123
feature_id : 123
dict : cnt_map

[.@VALID]
policy_id : 123
feature_id : 123
dict : cnt_map
session_time : 3600

[.@VALID]
policy_id : 123
feature_id : 123
dict : cnt_map
session_time : 3600
@select : dt.query_sign
[..@rule]
feature_id : 123456789
rule_type  : boolean
condition  : true


[SESSION_DUP_TRAFFIC]
[.@INVALID]

[.@INVALID]
hijack_time : -1

[.@VALID]
hijack_time : 3600

[SESSION_DIRECT]
[.@INVALID]
min_priority : xxx

[.@INVALID]
fix_type : xxx

[.@VALID]

[.@VALID]
min_priority : 100

[.@VALID]
min_priority : 100
fix_type: cnt

[.@VALID]
min_priority : 100
fix_type: group

[SESSION_PASS_THROUGH]
[.@INVALID]

[.@INVALID]
feature_id : 1000

[.@INVALID]
policy_id : 1000
@feature_id : 1000
copy_info : xxx

[.@INVALID]
policy_id : 1000
@feature_id : 1000
copy_info : xxx,

[.@VALID]
policy_id : 1000
@feature_id : 1000
@feature_id : 1001

[.@VALID]
policy_id : 1000
@feature_id : 1000
copy_info : session.uid,anti_uid

