#edit-mode: -*- python -*-
#with_cost = get_config_arg('with_cost', bool, True)
from config_parser import *

#learning_rate = 0.3
#learning_rate_decay_ratio = 10
#learning_rate_decay_step = 10
##fea_hidden_weight0_range = 0.01
#momentum = 0.5
##hidden_weight0_range = 0.01
#
#fea_hidden_weight0_range = 0.14535 #0.14535 ############ 117-120
##fea_hidden_weight0_range = 0.1820691 ############ 117-64
##fea_hidden_weight0_range = 0.1375770 ############ 117-200
#
## no use 
#hidden_weight0_range = 0.01
#
#flilter_fea_pv = 10
#filter_fea_click = 1
#
#decay_ratio = 0.1
#step_size = 1.0
#
##for adadelta
#adadelta_decay_rate = 0.95
#adadelta_constant = 1E-8
##adadelta_constant = 0.000009
#momentum_stop_step =  0.000001
#momentum_lr_decay_step = 2000000
#c2_value = 0 #################
#c1_value = 0 #####################
#
network_name = "demo"
skip_input = "skip"
cas_input = "cas" 

set_default(skip_input)
Layer(
    name = skip_input,
    layer_type = "data", 
    )
Inputs(skip_input)

#slot_input_layers = []
#slot_hid_layers = []

#datafile=open("slots.list", "r")
#for slotline in datafile:
#    (sslot, sname, ssize)=slotline.split("\t");
#    slot=int(sslot)
#    slot_input_name = "slot_"+str(slot)
#    Layer( name= slot_input_name, layer_type="data")
#    Inputs(slot_input_name)

#    map_slot(slot, slot_input_name)

#    slot_hidden_name = "slot_hidden_"+str(slot)
#    Layer( name= slot_hidden_name, 
#        layer_type="fc",
#        size = int(ssize),
#        active_type = "tanh", 
#        inputs = slot_input_name,
#        trunc = True
#        )
#    slot_input_layers.append(slot_input_name)
#    slot_hid_layers.append(slot_hidden_name)

#print slot_input_layers,
#print slot_hid_layers,
#Layer(
#    name = "hid3",
#    layer_type = "fc",
#    size = 31,
#    active_type = "tanh",
#    inputs = skip_input,
#    trunc = True
##    )
#

Layer(
    name = "hid1",
    layer_type = "fc",
    size = 256,
    active_type = "tanh",
    inputs = [skip_input],
    trunc = True
    )

Layer(
    name = "hid2",
    layer_type = "fc",
    size = 128,
    active_type = "tanh",
    inputs = ["hid1"],
    trunc = True
    )

Layer(
    name = "hid3",
    layer_type = "fc",
    size = 64,
    active_type = "tanh",
    inputs = ["hid2"],
    trunc = True
    )
Layer(
    name = "output_" + network_name,
    layer_type = "logistic_loss",
    #layer_type = "square_loss",
    size = 1,
    active_type = "sigmoid",
    inputs = ["hid3"],
    trunc = True
    )
Outputs("output_"+network_name)
