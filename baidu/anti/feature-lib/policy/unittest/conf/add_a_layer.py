########################################################################
#
# Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
#
########################################################################
"""
This module provide a toolkit to read params from console,and will be call in dnn_param_config.h in c++

Authors: <AUTHORS>
Date:    2015/02/06
"""

def Layer(name, layer_type, inputs=None, size=0, trunc=False, active_type=""):
    """ read params from console
    Args:
        name:input name
        layer_type:
        inputs:
        size:
        trunc:
        active_type:
        
    Returns:
        call c++ function to create a new DNN::Layer
    """
    if inputs is None:
        inputs = []
    if type(inputs) != list:    
        inputs = [inputs]

    if len(inputs) == 0:
        input_list = []
    else:
        input_list = []
        for item in inputs:
            if type(item) == str:
                input_list.append(item)
            else:
                print "should provide string type of input layer name"

    new_layer(name, layer_type, size, active_type, input_list, trunc)
