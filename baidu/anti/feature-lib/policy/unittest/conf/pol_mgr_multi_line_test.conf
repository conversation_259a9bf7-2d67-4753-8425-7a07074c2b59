@pol_list:123456,123457
@pol_list:123458, 123459

[@policy]
policy_id : 123456
policy_type : rule
[.@rule]
feature_id : 1111
rule_type : segment
gray_file : empty_gray
threshold : 20.0
punish_m : 10
punish_k : 1.0

[@policy]
policy_id : 123457
policy_type : rule
[.@rule]
feature_id : 1112
rule_type : ratio
gray_file : empty_gray
threshold : 0.2
remain : 10

[@policy]
policy_id : 123458
policy_type : rule
[.@rule]
feature_id : 1113
rule_type : distribution
gray_file : empty_gray
threshold : 2
count_threshold : 10
stand_prob : 0.3, 0.7
func_type : max_diff

[@policy]
policy_id : 123459
policy_type : rule
[.@rule]
feature_id : 1114
rule_type : segment
gray_file : empty_gray
threshold : 10.0
punish_m : 10
punish_k : 1.0
[.@rule]
feature_id : 1115
rule_type : ratio
gray_file : empty_gray
threshold : 0.5
remain : 10

# test policy_id not in policy_list
[@policy]
policy_id : 123460
policy_type : rule
[.@rule]
feature_id : 1114
rule_type : segment
gray_file : empty_gray
threshold : 10.0
punish_m : 10
punish_k : 1.0
[.@rule]
feature_id : 1115
rule_type : ratio
gray_file : empty_gray
threshold : 0.5
remain : 10


