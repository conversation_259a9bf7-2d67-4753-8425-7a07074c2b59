[base]
[.@invalid]
#miss feature_id

[.@invalid]
#no file key
feature_id : 123
gray_file : xxxxx

[.@invalid]
#invalid file key
feature_id : 123
gray_file : invalid_file

[.@valid]
feature_id : 123
gray_file : gray_test

[.@valid]
feature_id : 456

[segment]
[.invalid]
feature_id : 1111
gray_file : gray_test

[.@valid]
feature_id : 1111
gray_file : gray_test
threshold : 20.0
punish_m : 10
punish_k : 1.0

[.@valid]
feature_id : 1111
gray_file : gray_test
threshold : 10.0

[ratio]
[.invalid]
feature_id : 1112
gray_file : gray_test

[.@valid]
feature_id : 1112
gray_file : gray_test
threshold : > 0.2
remain : 10

[.@valid]
feature_id : 1112
gray_file : gray_test
threshold : < 0.3
remain : 10

[.@valid]
feature_id : 1112
gray_file : gray_test
threshold : > 1.1
simple_ratio : 1
remain : 10

[.@valid]
feature_id : 1112
gray_file : gray_test
threshold : > 1.1
simple_ratio : 1
filter_mode : 1
remain : 10

[distribution]
[.@invalid]
feature_id : 1113
gray_file : gray_test

[.@invalid]
feature_id : 1113
gray_file : gray_test
threshold : 0.5
count_threshold : 10
stand_prob : 0, 1

[.@invalid]
feature_id : 1113
gray_file : gray_test
threshold : 0.5
count_threshold : 10
stand_prob : xxxxx

[.@invalid]
feature_id : 1113
gray_file : gray_test
threshold : 0.5
count_threshold : 10
stand_prob : 0.1, 0.9
func_type : xxxxx

[.@valid]
feature_id : 1113
gray_file : gray_test
threshold : 0.5
count_threshold : 10
stand_prob : 0.1, 0.9
func_type : max_diff

[.@valid]
feature_id : 1113
gray_file : gray_test
threshold : 0.5
count_threshold : 10
stand_prob : 0.1, 0.9
func_type : chi_square_test

[.@valid]
feature_id : 1113
gray_file : gray_test
threshold : 0.5
count_threshold : 10
stand_prob : 0.1, 0.9
func_type : chi_square_dis

[.@valid]
feature_id : 1113
gray_file : gray_test
threshold : 0.5
count_threshold : 10
stand_prob : 0.1, 0.9
func_type : kl_divergence

[boolean]
[.@invalid]
# miss condition
feature_id : 1113

[.@invalid]
# invalid condition
feature_id : 1113
condition  : xxxxx

[.@valid]
feature_id : 1113
condition  : true

[.@valid]
feature_id : 1113
condition  : false

[ratio_black]
[.@invalid]
feature_id : 1113

[.@invalid] 
feature_id : 1113
threshold : 0.5 

[.@invalid] 
feature_id : 1113
threshold : 0.5 
mode: xxxx

[.@valid]
feature_id : 1113
threshold : 0.5
mode: filter
gray_file : gray_test

[.@valid]
feature_id : 1113
threshold : 0.5
mode: refer
gray_file : gray_test

[acp]
[.@invalid]
# miss threshold
feature_id : 1123

[.@invalid]
# invalid threshold
feature_id : 1123
threshold  : xxxxx

[.@valid]
feature_id : 1123
threshold  : 2

[.@valid]
feature_id : 1113
threshold  : 10.0

[threshold]
[.weak_anomaly]
feature_id : 1123
threshold  : > 1

[.@invalid]
# miss threshold
feature_id : 1123

[.@invalid]
# invalid threshold
feature_id : 1123
threshold  : + 1.0

[.@invalid]
# invalid threshold
feature_id : 1123
threshold  : xxxxx

[.@valid]
feature_id : 1123
threshold  : 1

[.@valid]
feature_id : 1123
threshold  : > 2

[.@valid]
feature_id : 1123
threshold  : *> 3.0

[.@valid]
feature_id : 1123
threshold  : < 4

[.@valid]
feature_id : 1123
threshold  : *< 5

# combine rule test
[combine]
[.@invalid]
# miss expression
feature_id : 1133
threshold  : 1000 

[.@invalid]
# invalid threshold
feature_id : 1133
expression : 100 + 101
threshold  : xxxxx

[.@invalid]
# invalid expression
feature_id : 1133
expression : 100
threshold  : xxxxx

[.@invalid]
# invalid expression
feature_id : 1133
expression : 100 $ 123
threshold  : xxxxx

[.@valid]
feature_id : 1133
expression : 100 / 101
threshold  : 1

[.@valid]
feature_id : 1133
expression : 100 / 101
threshold  : *> 2

[.@valid]
feature_id : 1133
expression : 100 / 101
threshold  : < 3.0

[.@valid]
feature_id : 1133
expression : 100 / 101
threshold  : *< 4.0

[.@valid]
feature_id : 1133
expression : 100 + 101
threshold  : *< 4.0

[.@valid]
feature_id : 1133
expression : 100 - 101
threshold  : *< 4.0

[.@valid]
feature_id : 1133
expression : 100 * 101
threshold  : *< 4.0


[max_distance_magnify]
[.@valid]
#[@policy]
#policy_id   : 50311
#policy_type : rule
#[.@rule]
feature_id  : 50311
rule_type   : distribution
func_type   : chi_square_dis
stand_prob  : 0.5,0.5
simple_distribution:1
count_threshold : 2
threshold:0.4

[.@valid]
#[@policy]
#policy_id   : 50311
#policy_type : rule
#[.@rule]
feature_id  : 50311
rule_type   : distribution
func_type   : chi_square_dis
stand_prob  : 0.5,0.5
simple_distribution:1
count_threshold : 2
threshold:30
max_distance_magnify : 5.0

[significance_distribution_case]
[.@valid]
feature_id:5121
rule_type:significance_distribution
mu:0.18,0.25,0.27,0.29
sigma:0.08,0.09,0.09,0.09
significance:0.01,260199,0.7
