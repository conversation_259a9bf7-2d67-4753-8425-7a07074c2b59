// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: feature_manager_test.cpp
// @Last modified: 2015-07-24 11:33:27
// @Brief: 

#include <gtest/gtest.h>
#include "feature_manager.h"

namespace anti {
namespace themis {
namespace feature_lib {

class FeatureManagerTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
        uint64_t fea_id[] = {1UL, 1UL, 2UL};
        for (uint32_t i = 0U; i < 3U; ++i) {
            std::shared_ptr<FeatureValueProto> fea(new FeatureValueProto());
            fea->set_feature_id(fea_id[i]);
            _feas.push_back(fea);
        }
    }
    virtual void TearDown() {}

private:
    FeatureManager _obj;
    std::vector<std::shared_ptr<FeatureValueProto> > _feas;
};

TEST_F(FeatureManagerTestSuite, inline_function_case) {
    std::shared_ptr<FeatureValueProto> fea;
    EXPECT_FALSE(_obj.insert(fea));
    EXPECT_FALSE(_obj.query(1UL, NULL));

    for (uint32_t i = 0; i < _feas.size(); ++i) {
        ASSERT_TRUE(_obj.insert(_feas[i]));
    }

    std::vector<std::shared_ptr<FeatureValueProto> > f;
    ASSERT_TRUE(_obj.query(1UL, &f));
    ASSERT_EQ(2U, f.size());

    ASSERT_TRUE(_obj.query(2UL, &f));
    ASSERT_EQ(1U, f.size());
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

