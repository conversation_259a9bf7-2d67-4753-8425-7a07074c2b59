// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: policy_manager_test.cpp
// @Last modified: 2018-03-23 19:22:23
// @Brief: 

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <file_dict_manager.h>
#include "policy_manager.h"

using anti::themis::common_lib::FileDictManagerSingleton;
using ::testing::_;
using ::testing::Return;
using ::testing::DoAll;
using ::testing::SetArgPointee;

namespace anti {
namespace themis {
namespace feature_lib {

class PolicyManagerTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {}
    virtual void TearDown() {}

private:
    PolicyManager _obj;
};

TEST_F(PolicyManagerTestSuite, init_by_invalid_conf_case) {
    EXPECT_FALSE(_obj.init("xx", "xxxx"));
    _obj.uninit();

    EXPECT_FALSE(_obj.init("./conf", "invalid_policy_manager.conf"));
    _obj.uninit();

    EXPECT_FALSE(_obj.init("./conf", "invalid_policy_manager1.conf"));
    _obj.uninit();

    EXPECT_FALSE(_obj.init("./conf", "invalid_policy_manager2.conf"));
    _obj.uninit();
}

TEST_F(PolicyManagerTestSuite, init_by_valid_conf_case) {
    ASSERT_TRUE(FileDictManagerSingleton::instance().init(
            "./conf", "file_dict_manager.conf"));
    ASSERT_TRUE(_obj.init("./conf", "policy_manager_test.conf"));
    EXPECT_EQ(4U, _obj._policies.size());
    const uint64_t exp_id[] = {123456, 123457, 123458, 123459};
    for (uint32_t i = 0; i < _obj._policies.size(); ++i) {
        EXPECT_EQ(exp_id[i], _obj._policies[i]->policy_id());
    }
    _obj.uninit();
    
    ASSERT_TRUE(_obj.init("./conf", "pol_mgr_multi_line_test.conf"));
    ASSERT_EQ(4U, _obj._policies.size());
    for (uint32_t i = 0; i < _obj._policies.size(); ++i) {
        EXPECT_EQ(exp_id[i], _obj._policies[i]->policy_id());
    }
    _obj.uninit();

    ASSERT_TRUE(_obj.init("./conf", "pol_mgr_cmp_test.conf"));
    ASSERT_EQ(4U, _obj._policies.size());
    for (uint32_t i = 0; i < _obj._policies.size(); ++i) {
        EXPECT_EQ(exp_id[i], _obj._policies[i]->policy_id());
    }
    _obj.uninit();
    FileDictManagerSingleton::instance().uninit();
}

TEST_F(PolicyManagerTestSuite, detect_by_invalid_input_case) {
    FeatureManager fm;
    EXPECT_FALSE(_obj.detect(&fm, NULL));

    _obj._policies.push_back(NULL);
    std::vector<PolicyResultProto> results;
    EXPECT_FALSE(_obj.detect(&fm, &results));
    _obj.uninit();
}

TEST_F(PolicyManagerTestSuite, detect_by_exp_case) {
    ASSERT_TRUE(FileDictManagerSingleton::instance().init(
            "./conf", "file_dict_manager.conf"));
    ASSERT_TRUE(_obj.init("./conf", "exp_policy_manager_test.conf"));
    ASSERT_EQ(3, _obj._exp.exps().size());
    FeatureManager fm;
    { 
        std::shared_ptr<FeatureValueProto> fea(new FeatureValueProto());
        fea->set_feature_id(123LU);
        fea->set_feature_type(FeatureValueProto::SEGMENT);
        fea->set_log_id(8888888LU);
        fea->set_value("0.3");
        fm.insert(fea);
    }
    { 
        std::shared_ptr<FeatureValueProto> fea(new FeatureValueProto());
        fea->set_feature_id(456LU);
        fea->set_feature_type(FeatureValueProto::SEGMENT);
        fea->set_log_id(8888888LU);
        fea->set_value("0.4");
        fm.insert(fea);
    }
    std::vector<PolicyResultProto> results;
    ASSERT_TRUE(_obj.detect(&fm, &results));
    EXPECT_EQ(5U, fm._feas.size());
    ASSERT_EQ(2U, results.size());
    uint64_t exp_pids[] = {999, 1000};
    for (uint32_t i = 0; i < 2U; ++i) {
        EXPECT_EQ(exp_pids[i], results[i].policy_id());
        EXPECT_TRUE(results[i].hit());
    }
    _obj.uninit();
    FileDictManagerSingleton::instance().uninit();
}

TEST_F(PolicyManagerTestSuite, detect_by_valid_input_case) {
    ASSERT_TRUE(FileDictManagerSingleton::instance().init(
            "./conf", "file_dict_manager.conf"));
    ASSERT_TRUE(_obj.init("./conf", "policy_manager_test.conf"));
    ASSERT_EQ(2, _obj._exp.exps().size());

    FeatureManager fm;
    // invalid feature
    {
        std::shared_ptr<FeatureValueProto> fea(new FeatureValueProto());
        fea->set_feature_id(1111LU);
        fea->set_feature_type(FeatureValueProto::SEGMENT);
        fea->set_log_id(8888888LU);
        fm.insert(fea);
    }

    // valid features 
    {
        std::shared_ptr<FeatureValueProto> fea(new FeatureValueProto());
        fea->set_feature_id(1112LU);
        fea->set_feature_type(FeatureValueProto::RATIO);
        fea->set_in_filter(true);
        fea->set_filter_count(100L);
        fea->set_refer_count(160L);
        fea->set_log_id(8888888LU);
        fm.insert(fea);
    }
    {
        std::shared_ptr<FeatureValueProto> fea(new FeatureValueProto());
        fea->set_feature_id(1113LU);
        fea->set_feature_type(FeatureValueProto::DISTRIBUTION);
        fea->set_bucket_idx(0U);
        fea->add_buckets()->set_idx(0U);
        fea->mutable_buckets(0)->set_count(10);
        fea->add_buckets()->set_idx(1U);
        fea->mutable_buckets(1)->set_count(90);
        fea->set_log_id(8888888LU);
        fm.insert(fea);
    }
    { 
        std::shared_ptr<FeatureValueProto> fea(new FeatureValueProto());
        fea->set_feature_id(1114LU);
        fea->set_feature_type(FeatureValueProto::SEGMENT);
        fea->set_log_id(8888888LU);
        fea->set_last_seg_count(0L);
        fea->set_cur_seg_count(15L);
        fm.insert(fea);
    }
    { 
        std::shared_ptr<FeatureValueProto> fea(new FeatureValueProto());
        fea->set_feature_id(1115LU);
        fea->set_feature_type(FeatureValueProto::RATIO);
        fea->set_in_filter(true);
        fea->set_filter_count(100L);
        fea->set_refer_count(160L);
        fea->set_log_id(8888888LU);
        fm.insert(fea);
    }

    std::vector<PolicyResultProto> results;
    ASSERT_TRUE(_obj.detect(&fm, &results));
    ASSERT_EQ(2U, results.size());
    uint64_t exp_pids[] = {123457, 123459};
    for (uint32_t i = 0; i < 2U; ++i) {
        EXPECT_EQ(exp_pids[i], results[i].policy_id());
        EXPECT_TRUE(results[i].hit());
    }
    _obj.uninit();
    FileDictManagerSingleton::instance().uninit();
}

class MockPolicyInterface : public PolicyInterface {
public:
    MOCK_METHOD1(init, bool(const comcfg::ConfigUnit& conf));
    MOCK_METHOD0(uninit, void());
    MOCK_CONST_METHOD0(policy_id, uint64_t());
    MOCK_CONST_METHOD2(detect, bool(const FeatureManager& feas, PolicyResultProto* result));
};

TEST_F(PolicyManagerTestSuite, detect_by_no_hit_and_has_value_case) {
    MockPolicyInterface p;
    _obj._policies.push_back(&p);
    PolicyResultProto r;
    r.set_policy_id(123456UL);
    r.set_hit(false);
    r.set_value(0.5);
    EXPECT_CALL(p, detect(_, _)).WillOnce(DoAll(SetArgPointee<1>(r), Return(true)));

    FeatureManager fm;
    std::vector<PolicyResultProto> results;
    ASSERT_TRUE(_obj.detect(&fm, &results));
    ASSERT_EQ(1U, results.size());
    EXPECT_EQ(123456UL, results[0].policy_id());
    EXPECT_FALSE(results[0].hit());
    EXPECT_FLOAT_EQ(0.5, results[0].value());
    _obj._policies.clear();
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

