/***************************************************************************
 * 
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
/**
 * @file policy/unittest/session_policy_test.cpp
 * <AUTHOR>
 * @date 2017/05/23 09:59:42
 * @brief 
 *  
 **/
#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <gflags/gflags.h>
#include <file_dict_manager.h>
#include "com_log.h"
#include "session_policy.h"

using ::testing::_;
using ::testing::Return;
using ::testing::DoAll;
using ::testing::SetArgPointee;
using anti::themis::common_lib::FileDictManagerSingleton;
using anti::themis::common_lib::CnMapFileDict;

namespace anti {
namespace themis {
namespace feature_lib {

DECLARE_bool(fill_result_to_feature);

typedef std::shared_ptr<FeatureValueProto> FeaPtr;

class SelectorTestSuite : public ::testing::Test {
public:
    virtual void SetUp() {
        ASSERT_EQ(_conf.load("conf", "session_policy_test.conf"), 0);
    }
    virtual void TearDown() {}

private:
    Selector _selector;
    comcfg::Configure _conf;
};

ActionProto build_action(uint64_t log_id, int64_t log_time, int64_t log_type) {
    ActionProto action;
    action.set_log_id(log_id);
    action.set_log_time(log_time);
    action.set_log_type(log_type);
    return action;
}

ActionProto build_valid_action(uint64_t log_id, int64_t log_time, uint64_t search_id, int64_t search_time) {
    ActionProto action;
    action.set_log_id(log_id);
    action.set_log_time(log_time);
    action.set_log_type(0);
    action.mutable_search_info()->set_search_id(search_id);
    action.mutable_search_info()->set_request_time(search_time);
    return action;
}


TEST_F(SelectorTestSuite, test_init_fail) {
    const comcfg::ConfigUnit& conf = _conf["SELECTOR"]["INVALID"];
    for (uint32_t i = 0; i < conf.size(); ++i) {
        CDEBUG_LOG("test invalid %u", i);
        ASSERT_FALSE(_selector.init(conf[i]));
    }
}

TEST_F(SelectorTestSuite, test_init_succ) {
    const comcfg::ConfigUnit& conf = _conf["SELECTOR"]["VALID"];
    for (uint32_t i = 0; i < conf.size(); ++i) {
        CDEBUG_LOG("test valid %u", i);
        ASSERT_TRUE(_selector.init(conf[i]));
        if (i == 2) {
            ASSERT_EQ(123, _selector._prange.min);
            ASSERT_EQ(922337203685477580, _selector._prange.max);
        }
    }
}

TEST_F(SelectorTestSuite, test_select_fail_with_null_ptr) {
    FeatureValueProto fea;
    ASSERT_FALSE(_selector.select(fea, NULL));
}

TEST_F(SelectorTestSuite, test_select_empty) {
    FeatureValueProto fea;
    std::vector<ActionProto> actions;
    ASSERT_TRUE(_selector.select(fea, &actions));
}

TEST_F(SelectorTestSuite, test_select_time_fail_with_null_ptr) {
    FeatureValueProto fea;
    ASSERT_FALSE(_selector._select_time(fea, NULL));
    ASSERT_FALSE(_selector._select_log_type(fea, NULL));
    ASSERT_FALSE(_selector._select_field(fea, NULL));
    ASSERT_FALSE(_selector._select_priority_range(fea, NULL));
}

TEST_F(SelectorTestSuite, test_select_time_succ) {
    FeatureValueProto fea;
    fea.mutable_session()->mutable_base_action()->CopyFrom(build_valid_action(100, 200, 300, 190));
    fea.mutable_session()->add_actions()->CopyFrom(build_valid_action(10, 20, 30, 20));
    fea.mutable_session()->add_actions()->CopyFrom(build_valid_action(11, 21, 31, 21));
    fea.mutable_session()->add_actions()->CopyFrom(build_valid_action(101, 201, 301, 201));
    std::vector<int> flag(fea.session().actions_size(), 1);
    _selector._session_time = 169;
    ASSERT_TRUE(_selector._select_time(fea, &flag));
    ASSERT_EQ(flag[0], 0);
    ASSERT_EQ(flag[1], 1);
    ASSERT_EQ(flag[2], 0);
}

TEST_F(SelectorTestSuite, test_select_priority_range_succ) {
    _selector._prange.min = 10;
    _selector._prange.max = 100;
    FeatureValueProto fea;
    auto act = fea.mutable_session()->add_actions();
    act->CopyFrom(build_valid_action(10, 20, 30, 20));
    act->mutable_fix()->set_priority(5);
    act = fea.mutable_session()->add_actions();
    act->CopyFrom(build_valid_action(11, 21, 31, 21));
    act->mutable_fix()->set_priority(10);
    act = fea.mutable_session()->add_actions();
    act->CopyFrom(build_valid_action(11, 21, 31, 21));
    act->mutable_fix()->set_priority(100);
    std::vector<int> flag(fea.session().actions_size(), 1);
    ASSERT_TRUE(_selector._select_priority_range(fea, &flag));
    ASSERT_EQ(flag[0], 0);
    ASSERT_EQ(flag[1], 1);
    ASSERT_EQ(flag[2], 0);
}

TEST_F(SelectorTestSuite, test_select_search_id_succ) {
    FeatureValueProto fea;
    fea.mutable_session()->mutable_base_action()->CopyFrom(build_valid_action(100, 200, 300, 190));
    fea.mutable_session()->add_actions()->CopyFrom(build_valid_action(300, 20, 30, 20));
    fea.mutable_session()->add_actions()->CopyFrom(build_valid_action(11, 21, 31, 21));
    fea.mutable_session()->add_actions()->CopyFrom(build_valid_action(101, 201, 301, 201));
    std::vector<int> flag(fea.session().actions_size(), 1);
    ASSERT_TRUE(_selector._select_search_id(fea, &flag));
    ASSERT_EQ(flag[0], 1);
    ASSERT_EQ(flag[1], 0);
    ASSERT_EQ(flag[2], 0);
}

TEST_F(SelectorTestSuite, test_select_log_type_succ) {
    FeatureValueProto fea;
    fea.mutable_session()->add_actions()->CopyFrom(build_valid_action(10, 20, 30, 20));
    std::vector<int> flag(fea.session().actions_size(), 1);
    ASSERT_TRUE(_selector._select_log_type(fea, &flag));
    ASSERT_EQ(flag[0], 1);

    _selector._log_type = 1;
    ASSERT_TRUE(_selector._select_log_type(fea, &flag));
    ASSERT_EQ(flag[0], 0);
}

TEST_F(SelectorTestSuite, test_select_field_fail) {
    FeatureValueProto fea;
    fea.mutable_session()->add_actions()->CopyFrom(build_valid_action(10, 20, 30, 20));
    std::vector<int> flag(fea.session().actions_size(), 1);
    _selector._contrasts.push_back("xxx");
    ASSERT_FALSE(_selector._select_field(fea, &flag));
}

TEST_F(SelectorTestSuite, test_select_field_succ) {
    FeatureValueProto fea;
    fea.mutable_session()->add_actions()->CopyFrom(build_valid_action(10, 20, 30, 20));
    fea.mutable_session()->mutable_base_action()->CopyFrom(build_valid_action(10, 20, 30, 20));
    fea.mutable_session()->mutable_base_action()->set_org_cnt("xxx");
    fea.mutable_session()->mutable_base_action()->mutable_dt()->set_query_sign(123);

    fea.mutable_session()->mutable_actions(0)->set_org_cnt("xxx");
    fea.mutable_session()->mutable_actions(0)->mutable_dt()->set_query_sign(456);
    std::vector<int> flag(fea.session().actions_size(), 1);
    _selector._contrasts.push_back("org_cnt");
    _selector._contrasts.push_back("dt.query_sign");
    ASSERT_TRUE(_selector._select_field(fea, &flag));
    ASSERT_EQ(flag[0], 0);
}


TEST_F(SelectorTestSuite, test_select_only_session_time) {
    _selector._session_time = 3600;
    FeatureValueProto fea;
    std::vector<ActionProto> actions;
    ActionProto action = build_action(100, 1000, 1);
    action.mutable_search_info()->set_request_time(3700);
    fea.mutable_session()->mutable_base_action()->CopyFrom(action);

    action.set_log_time(99);
    fea.mutable_session()->add_actions()->CopyFrom(action);
    fea.mutable_session()->add_actions()->CopyFrom(action);
    action.set_log_time(3701);
    fea.mutable_session()->add_actions()->CopyFrom(action);
    ASSERT_TRUE(_selector.select(fea, &actions));
    ASSERT_EQ(actions.size(), 0);

}

TEST_F(SelectorTestSuite, test_select_session_time) {
    _selector._session_time = 3600;
    FeatureValueProto fea;
    std::vector<ActionProto> actions;
    ActionProto action = build_action(100, 1000, 1);
    action.mutable_search_info()->set_request_time(3700);
    fea.mutable_session()->mutable_base_action()->CopyFrom(action);

    action.set_log_time(100);
    fea.mutable_session()->add_actions()->CopyFrom(action);
    fea.mutable_session()->add_actions()->CopyFrom(action);
    action.set_log_time(3700);
    fea.mutable_session()->add_actions()->CopyFrom(action);
    ASSERT_TRUE(_selector.select(fea, &actions));
    ASSERT_EQ(actions.size(), 3);
}

TEST_F(SelectorTestSuite, test_select_search_id) {
    _selector._session_time = 3600;
    FeatureValueProto fea;
    std::vector<ActionProto> actions;
    ActionProto action = build_action(100, 1000, 1);
    action.mutable_search_info()->set_request_time(3700);
    action.mutable_search_info()->set_search_id(777);
    fea.mutable_session()->mutable_base_action()->CopyFrom(action);

    action.set_log_time(100);
    fea.mutable_session()->add_actions()->CopyFrom(action);
    action.set_log_time(3700);
    action.set_log_id(777);
    fea.mutable_session()->add_actions()->CopyFrom(action);
    fea.mutable_session()->add_actions()->CopyFrom(action);
    ASSERT_TRUE(_selector.select(fea, &actions));
    ASSERT_EQ(actions.size(), 2);

}

TEST_F(SelectorTestSuite, test_select_log_type) {
    _selector._session_time = 3600;
    _selector._log_type = 1;
    FeatureValueProto fea;
    std::vector<ActionProto> actions;
    ActionProto action = build_action(100, 1000, 1);
    action.mutable_search_info()->set_request_time(3700);
    fea.mutable_session()->mutable_base_action()->CopyFrom(action);

    action.set_log_time(100);
    action.set_log_type(2);
    fea.mutable_session()->add_actions()->CopyFrom(action);
    fea.mutable_session()->add_actions()->CopyFrom(action);
    action.set_log_time(3700);
    action.set_log_type(1);
    fea.mutable_session()->add_actions()->CopyFrom(action);
    ASSERT_TRUE(_selector.select(fea, &actions));
    ASSERT_EQ(actions.size(), 1);
    ASSERT_EQ(actions.front().log_time(), 3700);
}

TEST_F(SelectorTestSuite, test_select_contrast) {
    _selector._contrasts.push_back("dt.query_sign");
    _selector._session_time = 3600;
    FeatureValueProto fea;
    std::vector<ActionProto> actions;
    ActionProto action = build_action(100, 1000, 1);
    action.mutable_dt()->set_query_sign(123);
    action.mutable_search_info()->set_request_time(3700);
    fea.mutable_session()->mutable_base_action()->CopyFrom(action);

    action.set_log_time(100);
    fea.mutable_session()->add_actions()->CopyFrom(action);
    fea.mutable_session()->add_actions()->CopyFrom(action);
    action.set_log_time(3700);
    action.mutable_dt()->set_query_sign(456);
    fea.mutable_session()->add_actions()->CopyFrom(action);
    ASSERT_TRUE(_selector.select(fea, &actions));
    ASSERT_EQ(actions.size(), 2);
    ASSERT_EQ(actions[0].dt().query_sign(), 123);
    ASSERT_EQ(actions[1].dt().query_sign(), 123);
}

TEST_F(SelectorTestSuite, test_get_field_fail) {
    ActionProto action = build_action(100, 1000, 1);
    action.set_org_cnt("baidu");
    std::string value;
    ASSERT_FALSE(_selector._get_field(action, "org_cnt", NULL));
    ASSERT_FALSE(_selector._get_field(action, "x:y:z", &value));
    ASSERT_FALSE(_selector._get_field(action, "xxx", &value));
}

TEST_F(SelectorTestSuite, test_get_field_succ) {
    ActionProto action = build_action(100, 1000, 1);
    action.set_org_cnt("baidu");
    action.mutable_dt()->set_query_sign(123);
    std::string value;
    ASSERT_TRUE(_selector._get_field(action, "org_cnt", &value));
    ASSERT_EQ(value, "baidu");
    ASSERT_TRUE(_selector._get_field(action, "dt.query_sign", &value));
    ASSERT_EQ(value, "123");
}

class MockSessionPolicy : public SessionPolicyBase {
public:
    MOCK_CONST_METHOD3(_detect,
            bool(const FeatureValueProto&, const std::vector<ActionProto>&, PolicyResultProto*));
    MOCK_METHOD1(_init, bool (const comcfg::ConfigUnit&));
};

class SessionPolicyBaseTestSuite : public ::testing::Test {
public:
    virtual void SetUp() {
        ASSERT_EQ(_conf.load("conf", "session_policy_test.conf"), 0);
        ASSERT_TRUE(FileDictManagerSingleton::instance()
                .init("conf", "session_policy_test.conf"));
    }
    virtual void TearDown() {
        FileDictManagerSingleton::instance().uninit();
    }

private:
    MockSessionPolicy _policy;
    comcfg::Configure _conf;
};

TEST_F(SessionPolicyBaseTestSuite, test_init_fail) {
    const comcfg::ConfigUnit& conf = _conf["SESSION_POLICY_BASE"]["INVALID"];
    for (uint32_t i = 0; i < conf.size(); ++i) {
        CDEBUG_LOG("test invalid %u", i);
        ASSERT_FALSE(_policy.init(conf[i]));
    }
}

TEST_F(SessionPolicyBaseTestSuite, test_init_succ) {
    EXPECT_CALL(_policy, _init(_)).WillRepeatedly(Return(true));
    const comcfg::ConfigUnit& conf = _conf["SESSION_POLICY_BASE"]["VALID"];
    ASSERT_TRUE(_policy.init(conf[0]));
    ASSERT_EQ(NULL, _policy._rule);

    ASSERT_TRUE(_policy.init(conf[1]));
    ASSERT_FALSE(_policy._rule == NULL);
}

TEST_F(SessionPolicyBaseTestSuite, test_is_fea_valid) {
    FeatureValueProto fea;
    ASSERT_FALSE(_policy.is_fea_valid(fea));
    auto session = fea.mutable_session();

    session->mutable_base_action()->CopyFrom(build_action(1, 100, 0));
    ASSERT_FALSE(_policy.is_fea_valid(fea));

    session->add_actions()->CopyFrom(build_action(1, 100, 0));
    ASSERT_FALSE(_policy.is_fea_valid(fea));

    session->mutable_base_action()->mutable_search_info()->set_search_id(1);
    ASSERT_FALSE(_policy.is_fea_valid(fea));

    session->mutable_base_action()->mutable_search_info()->set_request_time(100);
    ASSERT_TRUE(_policy.is_fea_valid(fea));
}

TEST_F(SessionPolicyBaseTestSuite, test_find_partner_tag) {
    ASSERT_EQ(_policy._find_partner_tag("xxx"), "xxx");

    EXPECT_CALL(_policy, _init(_)).WillOnce(Return(true));
    const comcfg::ConfigUnit& conf = _conf["SESSION_POLICY_BASE"]["VALID"];
    ASSERT_TRUE(_policy.init(conf[0]));
    ASSERT_EQ(_policy._find_partner_tag("baidu"), "pc_6");

    ASSERT_EQ(_policy._find_partner_tag("xxx"), "xxx");
}

TEST_F(SessionPolicyBaseTestSuite, test_detect_fail_with_null_ptr) {
    FeatureManager fea_mgr;
    ASSERT_FALSE(_policy.detect(fea_mgr, NULL));
}

TEST_F(SessionPolicyBaseTestSuite, test_detect_feature_fail_with_select_fail) {
    FeatureManager fea_mgr;
    FeaPtr fea(new FeatureValueProto());
    fea->mutable_session()->mutable_base_action()->CopyFrom(build_valid_action(1, 100, 2, 99));
    fea->mutable_session()->add_actions()->CopyFrom(build_valid_action(1, 100, 2, 99));
    fea_mgr.insert(fea);
    PolicyResultProto res;
    _policy._selector._contrasts.push_back("xxx");
    
    ASSERT_FALSE(_policy.detect(fea_mgr, &res));
}

TEST_F(SessionPolicyBaseTestSuite, test_detect_feature_invalid) {
    FeatureManager fea_mgr;
    FeaPtr fea(new FeatureValueProto());
    fea_mgr.insert(fea);
    PolicyResultProto res;
    ASSERT_TRUE(_policy.detect(fea_mgr, &res));
    ASSERT_FALSE(res.hit());
    ASSERT_EQ(res.fixed_cnt_size(), 0);
}

TEST_F(SessionPolicyBaseTestSuite, test_detect_feature_fail_with_detect_fail) {
    FeatureManager fea_mgr;
    FeaPtr fea(new FeatureValueProto());
    fea->mutable_session()->mutable_base_action()->CopyFrom(build_valid_action(1, 100, 2, 99));
    fea->mutable_session()->add_actions()->CopyFrom(build_valid_action(1, 100, 2, 99));
    fea_mgr.insert(fea);
    PolicyResultProto res;
    
    EXPECT_CALL(_policy, _detect(_, _, _)).WillOnce(Return(false));
    ASSERT_FALSE(_policy.detect(fea_mgr, &res));
}

TEST_F(SessionPolicyBaseTestSuite, test_detect_feature_no_care_feature) {
    EXPECT_CALL(_policy, _init(_)).WillOnce(Return(true));
    ASSERT_TRUE(_policy.init(_conf["SESSION_POLICY_BASE"]["VALID"][1]));

    FeatureManager fea_mgr;
    FeaPtr fea(new FeatureValueProto());
    fea->set_feature_id(123);
    fea->mutable_session()->mutable_base_action()->CopyFrom(build_valid_action(1, 100, 2, 99));
    fea->mutable_session()->add_actions()->CopyFrom(build_valid_action(1, 100, 2, 99));
    fea_mgr.insert(fea);
    
    PolicyResultProto res;
    ASSERT_TRUE(_policy.detect(fea_mgr, &res));
    ASSERT_EQ(res.fixed_cnt_size(), 0);
}

TEST_F(SessionPolicyBaseTestSuite, test_detect_feature_success) {
    EXPECT_CALL(_policy, _init(_)).WillOnce(Return(true));
    ASSERT_TRUE(_policy.init(_conf["SESSION_POLICY_BASE"]["VALID"][1]));

    FeatureManager fea_mgr;
    FeaPtr fea(new FeatureValueProto());
    fea->set_feature_id(123);
    fea->mutable_session()->mutable_base_action()->CopyFrom(build_valid_action(1, 100, 2, 99));
    fea->mutable_session()->add_actions()->CopyFrom(build_valid_action(1, 100, 2, 99));
    FeaPtr care_fea(new FeatureValueProto());
    care_fea->set_feature_id(123456789);
    care_fea->set_feature_type(FeatureValueProto::CARESPACE);
    care_fea->set_condition(true);
    fea_mgr.insert(fea);
    fea_mgr.insert(care_fea);
    PolicyResultProto mock_res;
    mock_res.add_fixed_cnt()->set_cnt_name("baidu");
    
    FLAGS_fill_result_to_feature = true;
    PolicyResultProto res2;
    EXPECT_CALL(_policy, _detect(_, _, _)).WillOnce(DoAll(SetArgPointee<2>(mock_res), Return(true)));
    ASSERT_TRUE(_policy.detect(fea_mgr, &res2));
    ASSERT_EQ(res2.fixed_cnt_size(), 1);
    ASSERT_EQ(res2.fixed_cnt(0).cnt_name(), "baidu");
    ASSERT_FALSE(fea->has_session());
    ASSERT_EQ(fea->fixed_cnt_size(), 1);
    ASSERT_EQ(fea->fixed_cnt(0).cnt_name(), "baidu");
}

class SessionDupTrafficPolicyTestSuite : public ::testing::Test {
public:
    virtual void SetUp() {
        ASSERT_EQ(_conf.load("conf", "session_policy_test.conf"), 0);
    }
    virtual void TearDown() {}

private:
    SessionDupTrafficPolicy _policy;
    comcfg::Configure _conf;
};

TEST_F(SessionDupTrafficPolicyTestSuite, test_init) {
    const comcfg::ConfigUnit& conf = _conf["SESSION_DUP_TRAFFIC"]["INVALID"];
    for (uint32_t i = 0; i < conf.size(); ++i) {
        CDEBUG_LOG("test invalid %u", i);
        ASSERT_FALSE(_policy._init(conf[i]));
    }

    ASSERT_TRUE(_policy._init(_conf["SESSION_DUP_TRAFFIC"]["VALID"][0]));
    ASSERT_EQ(_policy._hijack_time, 3600000);
}

TEST_F(SessionDupTrafficPolicyTestSuite, test_detect_fail_with_null_ptr) {
    FeatureValueProto fea;
    std::vector<ActionProto> actions;
    ASSERT_FALSE(_policy._detect(fea, actions, NULL));

}

TEST_F(SessionDupTrafficPolicyTestSuite, test_detect_succ) {
    FeatureValueProto fea;
    fea.mutable_session()->mutable_base_action()->CopyFrom(build_valid_action(100, 1000, 101, 999));
    fea.mutable_session()->mutable_base_action()->set_org_cnt("xxx");
    std::vector<ActionProto> actions;
    actions.push_back(build_valid_action(100, 900, 101, 900));
    actions.push_back(build_valid_action(101, 901, 102, 901));
    actions[0].set_org_cnt("baidu");
    actions[0].mutable_dt()->set_query_sign(1);
    actions[1].set_org_cnt("xxx");
    actions[1].mutable_dt()->set_query_sign(1);

    PolicyResultProto res;
    _policy._hijack_time = 3600;
    ASSERT_TRUE(_policy._detect(fea, actions, &res));
    ASSERT_EQ(res.fixed_cnt_size(), 1);
    ASSERT_EQ(res.fixed_cnt(0).cnt_name(), "baidu");
    ASSERT_EQ(res.fixed_cnt(0).priority(), 900);
}

TEST_F(SessionDupTrafficPolicyTestSuite, test_detect_succ_double_hijack) {
    FeatureValueProto fea;
    fea.mutable_session()->mutable_base_action()->CopyFrom(build_valid_action(100, 1000, 101, 999));
    fea.mutable_session()->mutable_base_action()->set_org_cnt("yyy");
    std::vector<ActionProto> actions;
    actions.push_back(build_valid_action(100, 900, 101, 900));
    actions.push_back(build_valid_action(101, 901, 102, 901));
    actions.push_back(build_valid_action(102, 902, 103, 903));
    actions[0].set_org_cnt("baidu");
    actions[0].mutable_dt()->set_query_sign(1);
    actions[1].set_org_cnt("xxx");
    actions[1].mutable_dt()->set_query_sign(1);
    actions[2].set_org_cnt("yyy");
    actions[2].mutable_dt()->set_query_sign(1);

    PolicyResultProto res;
    _policy._hijack_time = 3600;
    ASSERT_TRUE(_policy._detect(fea, actions, &res));
    ASSERT_EQ(res.fixed_cnt_size(), 1);
    ASSERT_EQ(res.fixed_cnt(0).cnt_name(), "baidu");
    ASSERT_EQ(res.fixed_cnt(0).priority(), 900);
}

TEST_F(SessionDupTrafficPolicyTestSuite, test_detect_succ_double_hijack_two) {
    FeatureValueProto fea;
    fea.mutable_session()->mutable_base_action()->CopyFrom(build_valid_action(100, 1000, 101, 999));
    fea.mutable_session()->mutable_base_action()->set_org_cnt("yyy");
    std::vector<ActionProto> actions;
    actions.push_back(build_valid_action(100, 900, 101, 900));
    actions.push_back(build_valid_action(101, 901, 102, 901));
    actions.push_back(build_valid_action(102, 902, 103, 903));
    actions[0].set_org_cnt("yyy");
    actions[1].set_org_cnt("xxx");
    actions[2].set_org_cnt("yyy");

    PolicyResultProto res;
    _policy._hijack_time = 3600;
    ASSERT_TRUE(_policy._detect(fea, actions, &res));
    ASSERT_EQ(res.fixed_cnt_size(), 0);
}

TEST_F(SessionDupTrafficPolicyTestSuite, test_is_query_equal) {
    ActionProto action1;
    ActionProto action2;
    ASSERT_FALSE(_policy._is_query_equal(action1, action2));

    action1.mutable_dt()->set_query_sign(1);
    ASSERT_FALSE(_policy._is_query_equal(action1, action2));

    action2.mutable_dt()->set_query_sign(2);
    ASSERT_FALSE(_policy._is_query_equal(action1, action2));

    action2.mutable_dt()->set_query_sign(1);
    ASSERT_TRUE(_policy._is_query_equal(action1, action2));
}

TEST_F(SessionDupTrafficPolicyTestSuite, test_is_hijack) {
    ActionProto action1 = build_valid_action(100, 1000, 200, 100);
    ActionProto action2 = build_valid_action(101, 1001, 201, 101);
    ASSERT_FALSE(_policy._is_hijack(action1, action2));

    action1.mutable_dt()->set_query_sign(1);
    action2.mutable_dt()->set_query_sign(1);
    ASSERT_FALSE(_policy._is_hijack(action1, action1));

    _policy._hijack_time = 0;
    ASSERT_FALSE(_policy._is_hijack(action1, action2));

    _policy._hijack_time = 100;
    action1.set_org_cnt("cntname3");
    action2.set_org_cnt("cntname3");
    ASSERT_FALSE(_policy._is_hijack(action1, action2));

    ASSERT_TRUE(FileDictManagerSingleton::instance()
            .init("conf", "session_policy_test.conf"));

    _policy._cn_map = std::static_pointer_cast<const CnMapFileDict>(
            FileDictManagerSingleton::instance().get_file_dict("cnt_map"));

    action2.set_org_cnt("cntname4");
    ASSERT_FALSE(_policy._is_hijack(action1, action2));

    action2.set_org_cnt("cntname2");
    ASSERT_TRUE(_policy._is_hijack(action1, action2));

    FileDictManagerSingleton::instance().uninit();
}

TEST_F(SessionDupTrafficPolicyTestSuite, test_add_cntname_to_hijack_dict_fail_with_null_ptr) {
    ActionProto action;
    ASSERT_FALSE(_policy._add_cntname_to_hijack_dict("xxx", action, NULL));
}

TEST_F(SessionDupTrafficPolicyTestSuite, test_add_cntname_to_hijack_dict_succ) {
    ActionProto action;
    action.set_org_cnt("yyy");
    SessionHijackDict dict;
    ASSERT_TRUE(_policy._add_cntname_to_hijack_dict("xxx", action, &dict));
    ASSERT_EQ(dict["xxx"]->org_cnt(), "yyy");

    ActionProto action2;
    action2.set_org_cnt("xxx");
    ASSERT_TRUE(_policy._add_cntname_to_hijack_dict("zzz", action2, &dict));
    ASSERT_EQ(dict["xxx"]->org_cnt(), "yyy");
    ASSERT_EQ(dict["zzz"]->org_cnt(), "yyy");

    ActionProto action3;
    action3.set_org_cnt("uuu");
    ASSERT_TRUE(_policy._add_cntname_to_hijack_dict("xxx", action3, &dict));
    ASSERT_EQ(dict["xxx"]->org_cnt(), "uuu");
    ASSERT_EQ(dict["zzz"]->org_cnt(), "yyy");
}

TEST_F(SessionDupTrafficPolicyTestSuite, test_revise_cnt_name_fail_with_null_ptr) {
    ActionProto action;
    SessionHijackDict dict;
    ASSERT_FALSE(_policy._revise_cnt_name(action, dict, NULL));
}

TEST_F(SessionDupTrafficPolicyTestSuite, test_revise_cnt_name_succ) {
    ActionProto action;
    action.set_log_time(100);
    action.set_org_cnt("baidu");
    SessionHijackDict dict;
    dict["xxx"] = &action;
    ActionProto base_action;
    base_action.set_org_cnt("yyy");
    PolicyResultProto res;
    ASSERT_TRUE(_policy._revise_cnt_name(base_action, dict, &res));
    ASSERT_EQ(res.fixed_cnt_size(), 0);

    base_action.set_org_cnt("xxx");
    ASSERT_TRUE(_policy._revise_cnt_name(base_action, dict, &res));
    ASSERT_EQ(res.fixed_cnt_size(), 1);
    ASSERT_EQ(res.fixed_cnt(0).cnt_name(), "baidu");
    ASSERT_EQ(res.fixed_cnt(0).priority(), 100);
}

class SessionDirectPolicyTestSuite : public ::testing::Test {
public:
    virtual void SetUp() {
        ASSERT_EQ(_conf.load("conf", "session_policy_test.conf"), 0);
    }
    virtual void TearDown() {}

private:
    SessionDirectPolicy _policy;
    comcfg::Configure _conf;
};

TEST_F(SessionDirectPolicyTestSuite, test_init_fail) {
    const comcfg::ConfigUnit& conf = _conf["SESSION_DIRECT"]["INVALID"];
    for (uint32_t i = 0; i < conf.size(); ++i) {
        CDEBUG_LOG("test invalid %u", i);
        ASSERT_FALSE(_policy._init(conf[i]));
    }
}

TEST_F(SessionDirectPolicyTestSuite, test_init_succ) {
    ASSERT_TRUE(_policy._init(_conf["SESSION_DIRECT"]["VALID"][0]));
    ASSERT_EQ(_policy._min_priority, -1);

    ASSERT_TRUE(_policy._init(_conf["SESSION_DIRECT"]["VALID"][1]));
    ASSERT_EQ(_policy._min_priority, 100);
}

TEST_F(SessionDirectPolicyTestSuite, test_detect_fail_with_null_ptr) {
    FeatureValueProto fea;
    std::vector<ActionProto> actions;
    ASSERT_FALSE(_policy._detect(fea, actions, NULL));
}

TEST_F(SessionDirectPolicyTestSuite, test_detect_succ) {
    FeatureValueProto fea;

    std::vector<ActionProto> actions;
    actions.push_back(ActionProto());

    ActionProto action = build_valid_action(100, 200, 10, 20);
    action.mutable_fix()->set_fix_cnt("xxx");
    action.set_org_cnt("xxx");
    actions.push_back(action);

    action = build_valid_action(100, 200, 10, 20);
    action.mutable_fix()->set_fix_cnt("baidu");
    action.set_org_cnt("xxx");
    action.mutable_search_info()->add_policy_list("1001");
    actions.push_back(action);

    PolicyResultProto res;
    
    ASSERT_TRUE(_policy._detect(fea, actions, &res));
    ASSERT_EQ(res.fixed_cnt_size(), 1);
    ASSERT_EQ(res.fixed_cnt(0).cnt_name(), "baidu");
    ASSERT_EQ(res.fixed_cnt(0).priority(), 200);
    ASSERT_EQ(res.fixed_cnt(0).policy_id(), 1001);

    res.Clear();
    _policy._min_priority = -1;
    actions[2].mutable_fix()->set_priority(123);
    ASSERT_TRUE(_policy._detect(fea, actions, &res));
    ASSERT_EQ(res.fixed_cnt_size(), 1);
    ASSERT_EQ(res.fixed_cnt(0).cnt_name(), "baidu");
    ASSERT_EQ(res.fixed_cnt(0).priority(), 123);

    res.Clear();
    _policy._min_priority = 123;
    ASSERT_TRUE(_policy._detect(fea, actions, &res));
    ASSERT_EQ(res.fixed_cnt_size(), 1);
    ASSERT_EQ(res.fixed_cnt(0).cnt_name(), "baidu");
    ASSERT_EQ(res.fixed_cnt(0).priority(), 200);
}

TEST_F(SessionDirectPolicyTestSuite, test_detect_fix_group) {
    FeatureValueProto fea;

    std::vector<ActionProto> actions;
    actions.push_back(ActionProto());

    ActionProto action = build_valid_action(100, 200, 10, 20);
    action.mutable_fix()->set_fix_cnt("xxx");
    action.set_org_cnt("xxx");
    actions.push_back(action);

    action = build_valid_action(100, 200, 10, 20);
    action.mutable_fix()->set_fix_group("hao123_union");
    action.mutable_fix()->set_priority(123);
    actions.push_back(action);

    PolicyResultProto res;
    ASSERT_TRUE(_policy._init(_conf["SESSION_DIRECT"]["VALID"][3]));
    
    ASSERT_TRUE(_policy._detect(fea, actions, &res));
    ASSERT_EQ(res.fixed_cnt_size(), 1);
    ASSERT_FALSE(res.fixed_cnt(0).has_cnt_name());
    ASSERT_TRUE(res.fixed_cnt(0).has_fix_group());
    ASSERT_EQ(res.fixed_cnt(0).fix_group(), "hao123_union");
    ASSERT_EQ(res.fixed_cnt(0).priority(), 123);

}

class SessionPolicyPassThroughTestSuite : public ::testing::Test {
public:
    virtual void SetUp() {
        ASSERT_EQ(_conf.load("conf", "session_policy_test.conf"), 0);
    }
    virtual void TearDown() {}

private:
    SessionPolicyPassThrough _policy;
    comcfg::Configure _conf;
};

TEST_F(SessionPolicyPassThroughTestSuite, test_init_fail) {
    const comcfg::ConfigUnit& conf = _conf["SESSION_PASS_THROUGH"]["INVALID"];
    for (uint32_t i = 0; i < conf.size(); ++i) {
        CDEBUG_LOG("test invalid %u", i);
        ASSERT_FALSE(_policy.init(conf[i]));
    }
}

TEST_F(SessionPolicyPassThroughTestSuite, test_init_succ) {
    ASSERT_TRUE(_policy.init(_conf["SESSION_PASS_THROUGH"]["VALID"][0]));
    ASSERT_TRUE(_policy._copy_info == NULL);

    ASSERT_TRUE(_policy.init(_conf["SESSION_PASS_THROUGH"]["VALID"][1]));
    ASSERT_FALSE(_policy._copy_info == NULL);
    ASSERT_EQ(_policy._copy_info->src, "session.uid");
    ASSERT_EQ(_policy._copy_info->dst, "anti_uid");
}

TEST_F(SessionPolicyPassThroughTestSuite, test_detect) {
    FeatureManager fea_mgr;
    FeaPtr fea(new FeatureValueProto());
    fea->set_feature_id(1000);
    fea->add_fixed_cnt()->set_anti_uid(123);
    fea->add_fixed_cnt()->set_cnt_name("baidu");
    fea_mgr.insert(fea);

    _policy._feature_id_vec.push_back(1000);
    ASSERT_FALSE(_policy.detect(fea_mgr, NULL));
    PolicyResultProto res;
    ASSERT_TRUE(_policy.detect(fea_mgr, &res));
    ASSERT_EQ(res.fixed_cnt_size(), 2);
}

TEST_F(SessionPolicyPassThroughTestSuite, test_detect_with_copy_info) {
    FeatureManager fea_mgr;
    FeaPtr fea(new FeatureValueProto());
    fea->set_feature_id(1000);
    fea->mutable_session()->set_uid("123");
    fea_mgr.insert(fea);

    _policy._feature_id_vec.push_back(1000);
    _policy._copy_info = new SessionPolicyPassThrough::CopyInfo();
    _policy._copy_info->src = "session.uid";
    _policy._copy_info->dst = "anti_uid";

    PolicyResultProto res;
    ASSERT_TRUE(_policy.detect(fea_mgr, &res));
    ASSERT_EQ(res.fixed_cnt_size(), 1);
    ASSERT_EQ(res.fixed_cnt(0).anti_uid(), 123);
}

TEST_F(SessionPolicyPassThroughTestSuite, test_copy_field_fail) {
    FeatureManager fea_mgr;
    ASSERT_FALSE(_policy._copy_field(fea_mgr, NULL));
    PolicyResultProto res;
    ASSERT_FALSE(_policy._copy_field(fea_mgr, &res));

    FeaPtr fea(new FeatureValueProto());
    fea->set_feature_id(1000);
    fea->mutable_session()->set_uid("123");
    fea_mgr.insert(fea);

    _policy._feature_id_vec.push_back(1000);
    _policy._copy_info = new SessionPolicyPassThrough::CopyInfo();
    _policy._copy_info->src = "xxx";
    ASSERT_FALSE(_policy._copy_field(fea_mgr, &res));

    _policy._copy_info->src = "session.uid";
    _policy._copy_info->dst = "yyy";
    ASSERT_FALSE(_policy._copy_field(fea_mgr, &res));
}

TEST_F(SessionPolicyPassThroughTestSuite, test_copy_field_succ) {
    FeatureManager fea_mgr;
    FeaPtr fea(new FeatureValueProto());
    fea->set_feature_id(1000);
    fea_mgr.insert(fea);

    _policy._feature_id_vec.push_back(1);
    _policy._copy_info = new SessionPolicyPassThrough::CopyInfo();
    _policy._copy_info->src = "xxx";
    PolicyResultProto res;
    ASSERT_TRUE(_policy._copy_field(fea_mgr, &res));

    _policy._feature_id_vec[0] = 1000;
    _policy._copy_info->src = "session.uid";
    ASSERT_TRUE(_policy._copy_field(fea_mgr, &res));

    res.clear_fixed_cnt();
    fea->mutable_session()->set_uid("123");
    _policy._copy_info->dst = "anti_uid";
    ASSERT_TRUE(_policy._copy_field(fea_mgr, &res));
    ASSERT_EQ(res.fixed_cnt_size(), 1);
    ASSERT_EQ(res.fixed_cnt(0).anti_uid(), 123);
}

}
}
}
/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
