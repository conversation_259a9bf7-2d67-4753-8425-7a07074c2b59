// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: ratio_limit_rule_test.cpp
// @Last modified: 2016-11-14 15:24:29
// @Brief: 

#include <gtest/gtest.h>
#include <sign_util.h>
#include <file_dict_manager.h>
#include "ratio_limit_rule.h"

using anti::baselib::SignUtil;
using anti::themis::common_lib::FileDictManagerSingleton;

namespace anti {
namespace themis {
namespace feature_lib {

class RatioLimitRuleTestSuite : public ::testing::Test {
public:
    virtual void SetUp() {}
    virtual void TearDown() {}

private:
    RatioLimitRule _obj;
};

TEST_F(RatioLimitRuleTestSuite, construction_case) {
    EXPECT_DOUBLE_EQ(0.0, _obj._threshold);
    EXPECT_EQ(0L, _obj._remain);
}

TEST_F(RatioLimitRuleTestSuite, init_case) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "rule_test.conf"));

    EXPECT_FALSE(_obj._init(conf["ratio"]["invalid"]));
    _obj.uninit();

    ASSERT_TRUE(_obj._init(conf["ratio"]["valid"][0]));
    EXPECT_DOUBLE_EQ(0.2, _obj._threshold);
    EXPECT_EQ(10L, _obj._remain);
    _obj.uninit();

    ASSERT_TRUE(_obj._init(conf["ratio"]["valid"][1]));
    EXPECT_DOUBLE_EQ(0.3, _obj._threshold);
    EXPECT_EQ(10L, _obj._remain);
    _obj.uninit();

    ASSERT_TRUE(_obj._init(conf["ratio"]["valid"][2]));
    EXPECT_DOUBLE_EQ(1.1, _obj._threshold);
    EXPECT_EQ(10L, _obj._remain);
    EXPECT_FALSE(_obj._filter_mode);
    _obj.uninit();

    ASSERT_TRUE(_obj._init(conf["ratio"]["valid"][3]));
    EXPECT_DOUBLE_EQ(1.1, _obj._threshold);
    EXPECT_EQ(10L, _obj._remain);
    EXPECT_TRUE(_obj._filter_mode);
    _obj.uninit();
}

TEST_F(RatioLimitRuleTestSuite, _check_by_invalid_input_case) {
    FeatureValueProto fea;
    fea.set_feature_type(FeatureValueProto::SEGMENT);
    EXPECT_FALSE(_obj._check(fea, NULL));

    fea.set_feature_type(FeatureValueProto::RATIO);
    EXPECT_FALSE(_obj._check(fea, NULL));

    fea.set_in_filter(false);
    EXPECT_FALSE(_obj._check(fea, NULL));

    fea.set_filter_count(1);
    EXPECT_FALSE(_obj._check(fea, NULL));

    fea.set_refer_count(2);
    EXPECT_FALSE(_obj._check(fea, NULL));
}

TEST_F(RatioLimitRuleTestSuite, _check_by_valid_input_case) {
    ASSERT_TRUE(FileDictManagerSingleton::instance().init(
            "./conf", "file_dict_manager.conf"));
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "rule_test.conf"));
    // case >
    ASSERT_TRUE(_obj.init(conf["ratio"]["valid"][0]));

    uint64_t sign = 0LU;
    SignUtil::create_sign_md64("78", &sign);
    EXPECT_NE(0LU, sign);

    // 1. in gray && in filter < threshold
    // 2. in gray && in filter  > threshold
    // 3. not in gray && in filter < threshold
    // 4. not in gray && in filter > threshold
    // 5. not in gray && not in filter
    uint64_t view_signs[] = {sign, sign, 0LU, 0LU, 0LU};
    int64_t fils[] = {1, 12, 12, 30, 30};
    int64_t refs[] = {2, 10, 10, 30, 30};
    bool in_fils[] = {true, true, true, true, false};
    bool exps[] = {false, true, false, true, false};
    for (uint64_t i = 0; i < 5U; ++i) {
        FeatureValueProto fea;
        fea.set_feature_type(FeatureValueProto::RATIO);
        fea.set_view_sign(view_signs[i]);
        fea.set_in_filter(in_fils[i]);
        fea.set_filter_count(fils[i]);
        fea.set_refer_count(refs[i]);
        bool hit = false;
        ASSERT_TRUE(_obj._check(fea, &hit));
        EXPECT_EQ(exps[i], hit);
    }

    _obj.uninit();
    // case <
    bool exps2[] = {true, false, true, false, false};
    ASSERT_TRUE(_obj.init(conf["ratio"]["valid"][1]));
    for (uint64_t i = 0; i < 5U; ++i) {
        FeatureValueProto fea;
        fea.set_feature_type(FeatureValueProto::RATIO);
        fea.set_view_sign(view_signs[i]);
        fea.set_in_filter(in_fils[i]);
        fea.set_filter_count(fils[i]);
        fea.set_refer_count(refs[i]);
        bool hit = false;
        ASSERT_TRUE(_obj._check(fea, &hit));
        EXPECT_EQ(exps2[i], hit) << i;
    }

    _obj.uninit();

    bool exps3[] = {true, true, true, false, false};
    ASSERT_TRUE(_obj.init(conf["ratio"]["valid"][2]));
    for (uint64_t i = 0; i < 5U; ++i) {
        FeatureValueProto fea;
        fea.set_feature_type(FeatureValueProto::RATIO);
        fea.set_view_sign(view_signs[i]);
        fea.set_in_filter(in_fils[i]);
        fea.set_filter_count(fils[i]);
        fea.set_refer_count(refs[i]);
        fea.set_valid(true);
        bool hit = false;
        ASSERT_TRUE(_obj._check(fea, &hit));
        EXPECT_EQ(exps3[i], hit);
    }
    _obj.uninit();

    bool exps4[] = {true, true, true, false, false};
    bool in_fils_sp[] = {true, false, false, false, true};
    ASSERT_TRUE(_obj.init(conf["ratio"]["valid"][3]));
    for (uint64_t i = 0; i < 5U; ++i) {
        FeatureValueProto fea;
        fea.set_feature_type(FeatureValueProto::RATIO);
        fea.set_view_sign(view_signs[i]);
        fea.set_in_filter(in_fils_sp[i]);
        fea.set_filter_count(fils[i]);
        fea.set_refer_count(refs[i]);
        fea.set_valid(true);
        bool hit = false;
        ASSERT_TRUE(_obj._check(fea, &hit));
        EXPECT_EQ(exps4[i], hit);
    }
    _obj.uninit();

    FileDictManagerSingleton::instance().uninit();
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

