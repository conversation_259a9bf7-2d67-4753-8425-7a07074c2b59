// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: segment_limit_rule_test.cpp
// @Last modified: 2015-09-28 09:44:40
// @Brief: 

#include <gtest/gtest.h>
#include <sign_util.h>
#include <file_dict_manager.h>
#include "segment_limit_rule.h"

using anti::baselib::SignUtil;
using anti::themis::common_lib::FileDictManagerSingleton;

namespace anti {
namespace themis {
namespace feature_lib {

class SegmentLimitRuleTestSuite : public ::testing::Test {
public:
    virtual void SetUp() {}
    virtual void TearDown() {}

private:
    SegmentLimitRule _obj;
};

TEST_F(SegmentLimitRuleTestSuite, construction_case) {
    EXPECT_DOUBLE_EQ(0.0, _obj._threshold);
    EXPECT_EQ(0L, _obj._punish_m);
    EXPECT_DOUBLE_EQ(0.0, _obj._punish_k);
}

TEST_F(SegmentLimitRuleTestSuite, init_case) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "rule_test.conf"));

    EXPECT_FALSE(_obj._init(conf["segment"]["invalid"]));
    _obj.uninit();

    ASSERT_TRUE(_obj._init(conf["segment"]["valid"][0]));
    EXPECT_DOUBLE_EQ(20.0, _obj._threshold);
    EXPECT_EQ(10L, _obj._punish_m);
    EXPECT_DOUBLE_EQ(1.0, _obj._punish_k);
    _obj.uninit();

    ASSERT_TRUE(_obj._init(conf["segment"]["valid"][1]));
    EXPECT_DOUBLE_EQ(10.0, _obj._threshold);
    EXPECT_EQ(0L, _obj._punish_m);
    EXPECT_DOUBLE_EQ(0.0, _obj._punish_k);
    _obj.uninit();
}

TEST_F(SegmentLimitRuleTestSuite, _check_by_invalid_input_case) {
    FeatureValueProto fea;
    fea.set_feature_type(FeatureValueProto::RATIO);
    EXPECT_FALSE(_obj._check(fea, NULL));

    fea.set_feature_type(FeatureValueProto::SEGMENT);
    EXPECT_FALSE(_obj._check(fea, NULL));

    fea.set_last_seg_count(1);
    EXPECT_FALSE(_obj._check(fea, NULL));

    fea.set_cur_seg_count(2);
    EXPECT_FALSE(_obj._check(fea, NULL));
}

TEST_F(SegmentLimitRuleTestSuite, _check_by_valid_input_case) {
    ASSERT_TRUE(FileDictManagerSingleton::instance().init(
            "./conf", "file_dict_manager.conf"));
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "rule_test.conf"));
    ASSERT_TRUE(_obj.init(conf["segment"]["valid"][0]));

    uint64_t sign = 0LU;
    SignUtil::create_sign_md64("123", &sign);
    EXPECT_NE(0LU, sign);
    
    // 1. in gray && no punish > threshold
    // 2. not in gray && punish > threshold
    // 3. not in gray && punish < threshold
    // 4. not in gray && no punish < threshold
    uint64_t view_signs[] = {sign, 0LU, 0LU, 0LU};
    int64_t lasts[] = {2, 100, 31, 25};
    int64_t curs[] = {2, 1, 8, 19};
    bool exps[] = {true, true, false, false};
    // check segment feature
    for (uint32_t i = 0; i < 4U; ++i) {
        FeatureValueProto fea;
        fea.set_feature_type(FeatureValueProto::SEGMENT);
        fea.set_view_sign(view_signs[i]);
        fea.set_last_seg_count(lasts[i]);
        fea.set_cur_seg_count(curs[i]);
        bool hit = false;
        ASSERT_TRUE(_obj._check(fea, &hit));
        EXPECT_EQ(exps[i], hit);
    }

    // check segment black feature
    for (uint32_t i = 0; i < 4U; ++i) {
        FeatureValueProto fea;
        fea.set_feature_type(FeatureValueProto::AUTO_SEGMENT);
        fea.set_view_sign(view_signs[i]);
        fea.set_last_seg_count(lasts[i]);
        fea.set_cur_seg_count(curs[i]);
        bool hit = false;
        ASSERT_TRUE(_obj._check(fea, &hit));
        EXPECT_EQ(exps[i], hit);
    }
    
    _obj.uninit();
    FileDictManagerSingleton::instance().uninit();
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

