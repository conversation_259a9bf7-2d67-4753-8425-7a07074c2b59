// Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
// @file significance_distribution_rule_test.cpp
// <AUTHOR>
// @date 2019/01/09 14:23:23
// @brief

#include <float.h>
#include <gtest/gtest.h>
#include <sign_util.h>
#include <file_dict_manager.h>
#include "significance_distribution_rule.h"

using anti::baselib::SignUtil;
using anti::themis::common_lib::FileDictManagerSingleton;

namespace anti {
namespace themis {
namespace feature_lib {

class SignificanceDistributionRuleTestSuite : public ::testing::Test {
public:
    virtual void SetUp() {}
    virtual void TearDown() {}

private:
    SignificanceDistributionRule _obj;
};

TEST_F(SignificanceDistributionRuleTestSuite, significance_distribution_case) {
    ASSERT_TRUE(FileDictManagerSingleton::instance().init(
            "./conf", "file_dict_manager.conf"));
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "rule_test.conf"));
    {
        ASSERT_TRUE(_obj.init(conf["significance_distribution_case"]["valid"][0]));
        uint64_t sign = 0LU;
        SignUtil::create_sign_md64("123", &sign);
        EXPECT_NE(0LU, sign);
        uint64_t view_signs[] = {sign, sign, sign, sign};
        uint32_t buc_idx[] = {0U, 0U, 0U, 0U};
        uint32_t buc_num[][4U] = {{10U, 0U, 0U, 0U}, {11U, 0U, 0U, 0U},
            {3400U, 2200U, 2200U, 2200U}, {3397U, 2203U, 2200U, 2200U}};
        bool exps[] = {false, true, true, false};
        for (uint32_t i = 0; i < 3U; ++i) {
            FeatureValueProto fea;
            fea.set_feature_type(FeatureValueProto::DISTRIBUTION);
            fea.set_view_sign(view_signs[i]);
            fea.set_bucket_idx(buc_idx[i]);
            for (uint32_t j = 0; j < 4U; ++j) {
                FeatureValueProto::BucketProto* b = fea.add_buckets();
                b->set_idx(j);
                b->set_count(buc_num[i][j]);
            }
            bool hit = false;
            ASSERT_TRUE(_obj._check(fea, &hit));
            EXPECT_EQ(exps[i], hit);
        }
        _obj.uninit();
    }
    FileDictManagerSingleton::instance().uninit();
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

