// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: houruijie
// 
// @Brief: 

#include <gtest/gtest.h>
#include "af_sign.h"

namespace anti {
namespace themis {
namespace feature_lib {

class GenSignTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {}
    virtual void TearDOwn() {}
};

TEST_F(GenSignTestSuite, test_fea_sign) {
    std::vector<std::string> fea_value;
    fea_value.push_back("0.98");
    uint32_t slot = 109;
    ASSERT_EQ(13838059633644027721lu, Fealib_signature::set_anycombine_fea_value(slot, fea_value));

    fea_value.clear();
    slot = 171;
    fea_value.push_back("11801");
    ASSERT_EQ(15171122277947491795lu, Fealib_signature::set_anycombine_fea_value(slot, fea_value));

    fea_value.clear();
    slot = 0;
    fea_value.push_back("0.42");
    ASSERT_EQ(11598724834932322202lu, Fealib_signature::set_anycombine_fea_value(slot, fea_value));

    fea_value.clear();
    slot = 1;
    fea_value.push_back("-NULL-");
    ASSERT_EQ(10224381672550998925lu, Fealib_signature::set_anycombine_fea_value(slot, fea_value));

    fea_value.clear();
    slot = 2;
    fea_value.push_back("0");
    ASSERT_EQ(4800083558051493082lu, Fealib_signature::set_anycombine_fea_value(slot, fea_value));

    fea_value.clear();
    slot = 3;
    fea_value.push_back("orz");
    ASSERT_EQ(12085383654838049227lu, Fealib_signature::set_anycombine_fea_value(slot, fea_value));

    fea_value.clear();
    slot = 4;
    fea_value.push_back("-NULL-");
    ASSERT_EQ(10224384971133355684lu, Fealib_signature::set_anycombine_fea_value(slot, fea_value));
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti
/* vim: set ts=4 sw=4: */
