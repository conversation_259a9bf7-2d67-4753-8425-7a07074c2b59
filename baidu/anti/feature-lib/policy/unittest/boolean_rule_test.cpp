// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: boolean_rule_test.cpp
// @Last modified: 2015-06-05 12:20:13
// @Brief: 

#include <gtest/gtest.h>
#include "boolean_rule.h"

namespace anti {
namespace themis {
namespace feature_lib {

class BooleanRuleTestSuite : public ::testing::Test {
public:
    virtual void SetUp() {}
    virtual void TearDown() {}

private:
    BooleanRule _obj;
};

TEST_F(BooleanRuleTestSuite, construction_case) {
    EXPECT_TRUE(_obj._con);
}

TEST_F(BooleanRuleTestSuite, init_case) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "rule_test.conf"));

    uint32_t num = conf["boolean"]["invalid"].size();
    ASSERT_LE(2U, num);
    for (uint32_t i = 0U; i < 2U; ++i) {
        EXPECT_FALSE(_obj._init(conf["boolean"]["invalid"][i]));
        _obj.uninit();
    }

    num = conf["boolean"]["valid"].size();
    ASSERT_LE(2U, num);
    bool exp[] = {true, false};
    for (uint32_t i = 0U; i < 2U; ++i) {
        ASSERT_TRUE(_obj._init(conf["boolean"]["valid"][i]));
        EXPECT_EQ(exp[i], _obj._con);
        _obj.uninit();
    }
}

TEST_F(BooleanRuleTestSuite, _check_by_invalid_input_case) {
    FeatureValueProto fea;
    fea.set_feature_type(FeatureValueProto::RATIO);
    EXPECT_FALSE(_obj._check(fea, NULL));

    fea.set_feature_type(FeatureValueProto::FILE_DICT);
    EXPECT_FALSE(_obj._check(fea, NULL));

    fea.set_condition(true);
    EXPECT_FALSE(_obj._check(fea, NULL));
}

TEST_F(BooleanRuleTestSuite, _check_by_valid_input_case) {
    FeatureValueProto::FeatureType types[] = {
            FeatureValueProto::FILE_DICT,
            FeatureValueProto::FILE_DICT,
            FeatureValueProto::CARESPACE,
            FeatureValueProto::CARESPACE
    };
    bool cons[] = {true, true, false, false};
    bool obj_cons[] = {true, false, true, false};
    bool ext[] = {true, false, false, true};
    for (uint32_t i = 0U; i < 4U; ++i) {
        FeatureValueProto fea;
        fea.set_feature_type(types[i]);
        fea.set_condition(cons[i]);
        _obj._con = obj_cons[i];
        bool hit = false;
        ASSERT_TRUE(_obj._check(fea, &hit));
        EXPECT_EQ(ext[i], hit);
    }
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

