// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: policy_factory_test.cpp
// @Last modified: 2015-05-25 17:57:30
// @Brief: 

#include <gtest/gtest.h>
#include "policy_factory.h"
#include "rule_policy.h"

namespace anti {
namespace themis {
namespace feature_lib {

TEST(PolicyFactoryTestSuite, create_case) {
    EXPECT_TRUE(PolicyFactory::create("xxxxx") == NULL);
    PolicyInterface* policy = NULL;

    policy = PolicyFactory::create("rule");
    EXPECT_TRUE(dynamic_cast<PolicyInterface*>(policy) != NULL);
    delete policy;
    policy = NULL;

    policy = PolicyFactory::create("gbdt");
    EXPECT_TRUE(dynamic_cast<PolicyInterface*>(policy) != NULL);
    delete policy;
    policy = NULL;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

