// Copyright (c) 2021 Baidu.com, Inc. All Rights Reserved
// @Author: shangzhongbin
// 
// @Brief: 

#include <memory>
#include <iostream>
#include <cstdio>
#include <vector>
#include <cstring>
#include <fstream>
#include <boost/algorithm/string.hpp>
#include <boost/lexical_cast.hpp>
#include <gtest/gtest.h>
#include "policy_result.pb.h"
#include "gbdt_policy.h"
#include "policy_factory.h"
// add for new gbdt
#include "ltr_gbdt_policy.h"

namespace anti {
namespace themis {
namespace feature_lib {

class LTRGBDTPolicyTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {}
    virtual void TearDOwn() {}
private:
    LTRGBDTPolicy _obj;
};

TEST_F(LTRGBDTPolicyTestSuite, init_case) {
    comcfg::Configure conf;

    //不存在文件
    ASSERT_FALSE(_obj.load_model("./conf", "error.conf"));

    _obj._policy_id = 50000;
    ASSERT_TRUE(_obj.load_model("./conf", "model.conf"));
    ASSERT_DOUBLE_EQ(1.4, _obj._threshold);
    ASSERT_EQ("0.0", _obj._missing_value);
    ASSERT_EQ("0.1", _obj._default_value);
    ASSERT_EQ("0.2", _obj._invalid_value);
    

    _obj._policy_id = 50001;
    ASSERT_FALSE(_obj.load_model("./conf", "model.conf"));

    _obj._policy_id = 50002;
    ASSERT_FALSE(_obj.load_model("./conf", "model.conf"));
    
    _obj._policy_id = 50003;
    ASSERT_FALSE(_obj.load_model("./conf", "model.conf"));

    _obj.uninit();
}

TEST_F(LTRGBDTPolicyTestSuite, normalize_case) {
    _obj._policy_id = 50000;
    ASSERT_TRUE(_obj.load_model("./conf", "model.conf"));

    FeaVecMap fi;
    fi[0] = "0.3";
    fi[1] = "100";
    fi[2] = "-9";

    const std::shared_ptr<FeatureValueProto> fea_ptr(new FeatureValueProto());
    FeatureValueProto* fea = NULL;
    fea = fea_ptr.get();
    fea->set_feature_id(90001LU);
    fea->set_feature_type(FeatureValueProto::RATIO);

    std::string value;
    ASSERT_FALSE(_obj._normalize(fea, 0, "0", NULL));
    _obj._fea_num = 1;
    ASSERT_FALSE(_obj._normalize(fea, 3, "0.3", &value));

    _obj._fea_num = 1;
    ASSERT_TRUE(_obj._normalize(fea, 0, "0.3", &value));
    ASSERT_EQ("0.3", value);
    ASSERT_TRUE(_obj._normalize(fea, 0, "", &value));
    ASSERT_EQ("0.0", value);
    _obj.uninit();
}

TEST_F(LTRGBDTPolicyTestSuite, _get_fea_list) {
    FeaVecMap fi;
    fi[0] = "0.3";
    fi[1] = "100";
    fi[2] = "-9";
    fi[3] = "-"; // deafault
    fi[4] = "-9";

    _obj._policy_id = 50000;
    ASSERT_TRUE(_obj.load_model("./conf", "model.conf"));

    ASSERT_FALSE(_obj._get_fea_list(fi, NULL));

    float fea_list[ModelPolicy::MAX_FEATURE_NUM];
    _obj._fea_num = 4;
    ASSERT_TRUE(_obj._get_fea_list(fi, fea_list));

    ASSERT_FLOAT_EQ(0.3, fea_list[0]);
    ASSERT_FLOAT_EQ(100, fea_list[1]);
    ASSERT_FLOAT_EQ(-9, fea_list[2]);
    ASSERT_FLOAT_EQ(0.1, fea_list[3]);
    _obj.uninit();
}

/**************** test for multi view start *******************/
typedef std::shared_ptr<FeatureValueProto> FeaPtr;
typedef anti::themis::feature_lib::PolicyResultProto PolicyResultProto;

FeaPtr make_feature(const std::string& value, uint32_t feaid) {
    FeaPtr fea_ptr(new FeatureValueProto());
    fea_ptr->set_feature_id(feaid);
    fea_ptr->set_log_id(123LU);
    fea_ptr->set_valid(true);
    fea_ptr->set_value(value);
    return fea_ptr;
}

FeaPtr make_multi_value_feature(uint32_t feaid) {
    FeaPtr fea_ptr(new FeatureValueProto());
    fea_ptr->set_feature_id(feaid);
    fea_ptr->set_log_id(123LU);
    fea_ptr->set_valid(true);
    fea_ptr->set_feature_type(FeatureValueProto::MULTI_VALUE);
    return fea_ptr;
}

bool load_multi_view_data(
        std::vector<FeatureManager>* feature_manager_vec,
        std::vector<double>* predict_result_vec) {
    if (feature_manager_vec == nullptr || 
            predict_result_vec == nullptr) {
        return false;
    }

    std::fstream file("./data/multi_view_ltrgbdt.data");
    // format : logid \t SingleViewFea*73 \t MultiViewFea*2 \t predictRes
    std::string line;
    std::vector<std::string> item_vec;
    while (getline(file, line)) {
        boost::split(item_vec, line, 
                boost::is_any_of("\t"), 
                boost::token_compress_on);
        if (item_vec.size() != 79) {
            CWARNING_LOG("item_vec size(%d) != 79", item_vec.size());
            return false;
        }

        FeatureManager feature_manager;
        uint32_t feaid = 1000;
        // single view feature * 72
        for (uint32_t i = 1; i < 73; i++) {
            FeaPtr fea_ptr = make_feature(item_vec[i], feaid++);
            if (!feature_manager.insert(fea_ptr)) {
                return false;
            }
        }
        // multi view feature * 2
        FeaPtr fea_ptr = make_multi_value_feature(feaid++);
        for (uint32_t i = 73; i < 75; i++) {
            fea_ptr->add_values(item_vec[i]);
        }
        feature_manager.insert(fea_ptr);
        fea_ptr = make_multi_value_feature(feaid++);
        for (uint32_t i = 75; i < 78; i++) {
            fea_ptr->add_values(item_vec[i]);
        }
        feature_manager.insert(fea_ptr);
        feature_manager_vec->push_back(feature_manager);

        try {
            double predict_result = boost::lexical_cast<double>(item_vec.back());
            predict_result_vec->push_back(predict_result);
        } catch (const boost::bad_lexical_cast& e) {
            CWARNING_LOG("%s\n", e.what());
            return false;
        }
    }
    file.close();
    return true;
}

TEST_F(LTRGBDTPolicyTestSuite, multi_view_init) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "model_policy_test.conf"));

    ASSERT_TRUE(_obj.init(conf["policy"][10]));
    ASSERT_EQ(50000LU, _obj.policy_id());
    ASSERT_TRUE(_obj._model_rule == NULL);
    EXPECT_EQ(77, _obj._fea_num);
    EXPECT_EQ(74, _obj._slot2feaid.size());
}

TEST_F(LTRGBDTPolicyTestSuite, multi_view_detect) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "model_policy_test.conf"));
    ASSERT_TRUE(_obj.init(conf["policy"][10]));

    std::vector<FeatureManager> feature_manager_vec;
    std::vector<double> predict_val_vec;
    ASSERT_TRUE(load_multi_view_data(&feature_manager_vec, &predict_val_vec));
    ASSERT_EQ(49, feature_manager_vec.size());
    ASSERT_EQ(49, predict_val_vec.size());
    
    for (uint32_t i = 0; i < 49; ++i) {
        PolicyResultProto result;
        ASSERT_EQ(74, feature_manager_vec[i]._feas.size());
        ASSERT_TRUE(_obj.detect(feature_manager_vec[i], &result));
        EXPECT_TRUE(abs(predict_val_vec[i] - result.value()) < 0.01);
        if (abs(predict_val_vec[i] - result.value()) >= 0.01) {
            CWARNING_LOG("%lf vs %lf", predict_val_vec[i], result.value());
        }
    }
}
/**************** test for multi view end *******************/

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti
/* vim: set ts=4 sw=4: */
