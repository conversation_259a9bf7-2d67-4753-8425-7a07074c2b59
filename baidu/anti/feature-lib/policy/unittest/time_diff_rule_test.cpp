// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// @Author: wujun08(<EMAIL>)
//
// @created date: 2016-04-18 14:38:01
// @brief 

#include <gtest/gtest.h>
#include "time_diff_rule.h"

namespace anti {
namespace themis {
namespace feature_lib {

void load_conf(comcfg::Configure* conf, const std::string& path, const std::string& file) {
    ASSERT_TRUE(conf != NULL);
    ASSERT_TRUE(conf->load(path.c_str(), file.c_str()) == 0);
}

class TimeDiffRuleTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
        load_conf(&_cfg, "./conf", "time_diff_rule_test.conf");
        _fea.set_feature_type(FeatureValueProto::TIME_DIFF);
    }

    virtual void TearDown() {}

private:
    TimeDiffRule _time_diff_rule;
    FeatureValueProto _fea;
    comcfg::Configure _cfg;
    bool _hit;
};

TEST_F(TimeDiffRuleTestSuite, init_succ_with_threshold_lower) {
    ASSERT_TRUE(_time_diff_rule._init(_cfg["HAS_LOWER"]));
    EXPECT_EQ(false, _time_diff_rule._use_pre);
}

TEST_F(TimeDiffRuleTestSuite, init_succ_with_threshold_lower_not_use_pre) {
    ASSERT_TRUE(_time_diff_rule._init(_cfg["NOT_USE_PRE"]));
    EXPECT_EQ(false, _time_diff_rule._use_pre);
}

TEST_F(TimeDiffRuleTestSuite, init_succ_with_threshold_lower_use_pre) {
    ASSERT_TRUE(_time_diff_rule._init(_cfg["USE_PRE"]));
    EXPECT_EQ(true, _time_diff_rule._use_pre);
}

TEST_F(TimeDiffRuleTestSuite, init_succ_without_threshold_lower) {
    ASSERT_TRUE(_time_diff_rule._init(_cfg["NO_LOWER"]));
}

TEST_F(TimeDiffRuleTestSuite, check_hit_with_threshold_lower) {
    ASSERT_TRUE(_time_diff_rule._init(_cfg["HAS_LOWER"]));
    _fea.set_pre_feat_distance(2);
    ASSERT_TRUE(_time_diff_rule._check(_fea, &_hit));
    ASSERT_TRUE(_hit);
}

TEST_F(TimeDiffRuleTestSuite, check_hit_with_neighbor_feat_distances) {
    ASSERT_TRUE(_time_diff_rule._init(_cfg["HAS_LOWER"]));
    _fea.set_pre_feat_distance(1);
    _fea.add_neighbor_feat_distances(1);
    _fea.add_neighbor_feat_distances(2);
    _fea.add_neighbor_feat_distances(3);
    ASSERT_TRUE(_time_diff_rule._check(_fea, &_hit));
    ASSERT_TRUE(_hit);
}

TEST_F(TimeDiffRuleTestSuite, check_no_hit_with_neighbor_feat_distances) {
    ASSERT_TRUE(_time_diff_rule._init(_cfg["HAS_LOWER"]));
    _fea.set_pre_feat_distance(1);
    _fea.add_neighbor_feat_distances(1);
    ASSERT_TRUE(_time_diff_rule._check(_fea, &_hit));
    ASSERT_FALSE(_hit);
}

TEST_F(TimeDiffRuleTestSuite, check_no_hit_with_threshold_lower) {
    ASSERT_TRUE(_time_diff_rule._init(_cfg["HAS_LOWER"]));
    _fea.set_pre_feat_distance(1);
    ASSERT_TRUE(_time_diff_rule._check(_fea, &_hit));
    ASSERT_FALSE(_hit);
}

TEST_F(TimeDiffRuleTestSuite, check_hit_without_threshold_lower) {
    ASSERT_TRUE(_time_diff_rule._init(_cfg["NO_LOWER"]));
    _fea.set_pre_feat_distance(1);
    ASSERT_TRUE(_time_diff_rule._check(_fea, &_hit));
    ASSERT_TRUE(_hit);
}
  
TEST_F(TimeDiffRuleTestSuite, check_no_hit_without_threshold_lower) {
    ASSERT_TRUE(_time_diff_rule._init(_cfg["NO_LOWER"]));
    _fea.set_pre_feat_distance(6);
    ASSERT_TRUE(_time_diff_rule._check(_fea, &_hit));
    ASSERT_FALSE(_hit);
}


TEST_F(TimeDiffRuleTestSuite, check_hit_with_neighbor_feat_distances_use_pre) {
    ASSERT_TRUE(_time_diff_rule._init(_cfg["USE_PRE"]));
    _fea.set_pre_feat_distance(1);
    _fea.add_neighbor_feat_distances(1);
    _fea.add_neighbor_feat_distances(2);
    _fea.add_neighbor_feat_distances(30);
    ASSERT_TRUE(_time_diff_rule._check(_fea, &_hit));
    ASSERT_TRUE(_hit);
}

TEST_F(TimeDiffRuleTestSuite, check_hit_with_neighbor_feat_distances_not_use_PRE) {
    ASSERT_TRUE(_time_diff_rule._init(_cfg["NOT_USE_PRE"]));
    _fea.set_pre_feat_distance(1);
    _fea.add_neighbor_feat_distances(1);
    _fea.add_neighbor_feat_distances(2);
    _fea.add_neighbor_feat_distances(30);
    ASSERT_TRUE(_time_diff_rule._check(_fea, &_hit));
    ASSERT_FALSE(_hit);
}

}
}
}
