// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// @Author: lijie(<EMAIL>)
// 
// @File: threshold_limit_rule_test.cpp

#include <gtest/gtest.h>
#include <sign_util.h>
#include "threshold_limit_rule.h"
#include "boost/lexical_cast.hpp"

using anti::baselib::SignUtil;

namespace anti {
namespace themis {
namespace feature_lib {

class ThresholdLimitRuleTestSuite : public ::testing::Test {
public:
    virtual void SetUp() {}
    virtual void TearDown() {}

private:
    ThresholdLimitRule _obj;
};

TEST_F(ThresholdLimitRuleTestSuite, construction_case) {
    EXPECT_DOUBLE_EQ(0.0, _obj._threshold);
    EXPECT_FALSE(_obj._not);
}

TEST_F(ThresholdLimitRuleTestSuite, init_case) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "rule_test.conf"));

    for (uint32_t i = 0; i < conf["threshold"]["invalid"].size(); ++i) {
        EXPECT_FALSE(_obj._init(conf["threshold"]["invalid"][i]));
        _obj.uninit();
    }

    typedef bool(*oper)(double, double);
    bool exp_not[] = {false, false, true, false, true};
    double exp_th[] = {1, 2, 3, 4, 5};
    oper exp_op[] = {
            ThresholdLimitRule::_greater, 
            ThresholdLimitRule::_greater,
            ThresholdLimitRule::_less,
            ThresholdLimitRule::_less,
            ThresholdLimitRule::_greater};
    for (uint32_t i = 0; i < conf["threshold"]["valid"].size(); ++i) {
        ASSERT_TRUE(_obj.init(conf["threshold"]["valid"][i]));
        EXPECT_EQ(1123LU, _obj._feature_id);
        EXPECT_DOUBLE_EQ(exp_th[i], _obj._threshold);
        EXPECT_EQ(exp_op[i], _obj._cmp);
        _obj.uninit();
    }
}

TEST_F(ThresholdLimitRuleTestSuite, check_by_invalid_case) {
    FeatureManager fm;
    ASSERT_FALSE(_obj.check(fm, NULL));
    bool hit = false;
    ASSERT_FALSE(_obj.check(fm, &hit));

    _obj._feature_id = 123LU;
    _obj._cmp = ThresholdLimitRule::_greater;
    std::shared_ptr<FeatureValueProto> f(new FeatureValueProto());
    f->set_feature_id(123LU);
    fm.insert(f);
    ASSERT_TRUE(_obj.check(fm, &hit));

    _obj._feature_id = 124LU;
    std::shared_ptr<FeatureValueProto> t(new FeatureValueProto());
    t->set_feature_id(124LU);
    t->set_valid(true);
    t->set_value("xxxx");
    fm.insert(t);
    ASSERT_FALSE(_obj.check(fm, &hit));
}

TEST_F(ThresholdLimitRuleTestSuite, check_by_valid_case) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "rule_test.conf"));
    FeatureManager fm;

    {
        std::shared_ptr<FeatureValueProto> t(new FeatureValueProto());
        t->set_feature_id(1123LU);
        t->set_valid(true);
        t->set_value("4.0");
        fm.insert(t);
        bool exp_hit[] = {true, true, true, false, true};
        for (uint32_t i = 0; i < conf["threshold"]["valid"].size(); ++i) {
            ASSERT_TRUE(_obj.init(conf["threshold"]["valid"][i]));
            bool hit = false;
            ASSERT_TRUE(_obj.check(fm, &hit));
            EXPECT_EQ(exp_hit[i], hit);
            _obj.uninit();
        }
    }

    {
        std::shared_ptr<FeatureValueProto> t(new FeatureValueProto());
        t->set_feature_id(1123LU);
        t->set_valid(true);
        t->set_value("1.0");
        fm.insert(t);
        bool exp_hit[] = {true, true, false, true, true};
        for (uint32_t i = 0; i < conf["threshold"]["valid"].size(); ++i) {
            ASSERT_TRUE(_obj.init(conf["threshold"]["valid"][i]));
            bool hit = false;
            ASSERT_TRUE(_obj.check(fm, &hit));
            EXPECT_EQ(exp_hit[i], hit);
            _obj.uninit();
        }
    }

    {
        std::shared_ptr<FeatureValueProto> t(new FeatureValueProto());
        t->set_feature_id(1123LU);
        t->set_valid(true);
        t->set_value("5.0");
        fm.insert(t);
        bool exp_hit[] = {true, true, false, true, true};
        for (uint32_t i = 0; i < conf["threshold"]["valid"].size(); ++i) {
            ASSERT_TRUE(_obj.init(conf["threshold"]["valid"][i]));
            bool hit = false;
            ASSERT_TRUE(_obj.check(fm, &hit));
            EXPECT_EQ(exp_hit[i], hit);
            _obj.uninit();
        }
    }
    {
        std::shared_ptr<FeatureValueProto> t(new FeatureValueProto());
        t->set_feature_id(1123LU);
        t->set_valid(true);
        t->set_value("6.0");
        fm.insert(t);
        bool exp_hit[] = {true, true, false, true, false};
        for (uint32_t i = 0; i < conf["threshold"]["valid"].size(); ++i) {
            ASSERT_TRUE(_obj.init(conf["threshold"]["valid"][i]));
            bool hit = false;
            ASSERT_TRUE(_obj.check(fm, &hit));
            EXPECT_EQ(exp_hit[i], hit);
            _obj.uninit();
        }
    }

    {
        fm.clear();
        for (uint32_t i = 0; i < conf["threshold"]["valid"].size(); ++i) {
            ASSERT_TRUE(_obj.init(conf["threshold"]["valid"][i]));
            bool hit = true;
            ASSERT_TRUE(_obj.check(fm, &hit));
            EXPECT_FALSE(hit);
            _obj.uninit();
        }
    }

    {
        fm.clear();
        ASSERT_TRUE(_obj.init(conf["threshold"]["weak_anomaly"]));
        std::shared_ptr<FeatureValueProto> t(new FeatureValueProto());
        t->set_feature_id(1123LU);
        t->set_valid(true);
        t->set_value("1.125");
        fm.insert(t);
        bool hit = true;
        ASSERT_TRUE(_obj.check(fm, &hit));
        EXPECT_TRUE(hit);
    }
    {
        fm.clear();
        ASSERT_TRUE(_obj.init(conf["threshold"]["weak_anomaly"]));
        std::shared_ptr<FeatureValueProto> t(new FeatureValueProto());
        t->set_feature_id(1123LU);
        t->set_valid(true);
        t->set_in_anomaly_care(false);
        t->set_value("1.125");
        fm.insert(t);
        bool hit = true;
        ASSERT_TRUE(_obj.check(fm, &hit));
        EXPECT_FALSE(hit);
    }
    {
        fm.clear();
        ASSERT_TRUE(_obj.init(conf["threshold"]["weak_anomaly"]));
        std::shared_ptr<FeatureValueProto> t(new FeatureValueProto());
        t->set_feature_id(1123LU);
        t->set_valid(true);
        t->set_in_anomaly_care(false);
        t->set_value("1.125");
        t->set_in_anomaly_care(true);
        fm.insert(t);
        bool hit = true;
        ASSERT_TRUE(_obj.check(fm, &hit));
        EXPECT_TRUE(hit);
    }

}


}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

