// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// @Author: wangkai<PERSON>(<EMAIL>)
// @brief 

#include <gtest/gtest.h>
#include <boost/algorithm/string.hpp>
#include <gray_file_dict.h>
#include <file_dict_manager.h>
#include "session_prob_rule.h"

using anti::themis::common_lib::FileDictManagerSingleton;

namespace anti {
namespace themis {
namespace feature_lib {

class SessionProbruleTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
    }
    virtual void TearDown() {}
};

TEST_F(SessionProbruleTestSuite, _init_ok) {
    comcfg::Configure conf;
    conf.load("./conf", "session_prob_rule_ok.conf");
    auto policy = SessionProbRule();
    ASSERT_FALSE(policy._init(conf["session_prob"]["rule"][1]));
}

void create_feature_value_proto(FeatureValueProto* fea, const std::string& policy_id_str) {
    fea->Clear();
    fea->set_feature_id(10);
    fea->set_view_sign(10);
    fea->set_feature_type(FeatureValueProto::SESSION);
    fea->set_joinkey(123);
    fea->set_log_id(123);
    fea->set_log_time(123);
    fea->set_coord(0);

    auto* action = fea->mutable_session()->add_actions(); 
    action->set_log_id(123);
    action->set_log_time(123);
    std::vector<std::string> policy_id_vec;
    boost::split(policy_id_vec, policy_id_str, boost::is_any_of(","));
    for (uint32_t i = 0; i < policy_id_vec.size(); i++) {
        action->mutable_search_info()->add_policy_list(policy_id_vec[i]);
    }
}

TEST_F(SessionProbruleTestSuite, _cal_policy_prob_ok) {
    ASSERT_TRUE(FileDictManagerSingleton::instance().init(
            "./conf", "file_dict_manager.conf"));
    comcfg::Configure conf;
    conf.load("./conf", "session_prob_rule_ok.conf");
    auto policy = SessionProbRule();
    ASSERT_TRUE(policy.init(conf["session_prob"]["rule"][0]));

    FeatureValueProto fea;
    // normal 
    create_feature_value_proto(&fea, "111,121,131");
    bool hit = false;
    double prob = 0.0;
    int32_t valid_hit_size = 0;
    ASSERT_TRUE(policy._cal_policy_prob(fea.session().actions(0).search_info(), &prob, &valid_hit_size));
    EXPECT_EQ(prob, 0.982);
    EXPECT_EQ(valid_hit_size, 3U);
    create_feature_value_proto(&fea, "111,141");
    ASSERT_TRUE(policy._cal_policy_prob(fea.session().actions(0).search_info(), &prob, &valid_hit_size));
    EXPECT_EQ(prob, 0.91);
    EXPECT_EQ(valid_hit_size, 1U);
    create_feature_value_proto(&fea, "191,141");
    ASSERT_TRUE(policy._cal_policy_prob(fea.session().actions(0).search_info(), &prob, &valid_hit_size));
    EXPECT_EQ(prob, 0);
    EXPECT_EQ(valid_hit_size, 0U);
}

TEST_F(SessionProbruleTestSuite, _check_ok) {
    comcfg::Configure conf;
    conf.load("./conf", "session_prob_rule_ok.conf");
    auto policy = SessionProbRule();
    ASSERT_TRUE(policy.init(conf["session_prob"]["rule"][0]));
    EXPECT_EQ(policy._threshold, 0.9);
    typedef anti::themis::common_lib::GrayFileDict GrayFileDict;
    EXPECT_NE((const GrayFileDict*)NULL, policy._gray.get());
    EXPECT_NE((const GrayFileDict*)NULL, policy._prob_dict.get());
    FeatureValueProto fea;
    // normal 
    create_feature_value_proto(&fea, "111,121,131");
    bool hit = false;
    ASSERT_TRUE(policy._check(fea, &hit));
    EXPECT_TRUE(hit);
    // only one
    create_feature_value_proto(&fea, "111");
    ASSERT_TRUE(policy._check(fea, &hit));
    EXPECT_FALSE(hit);
    // 141 not in
    create_feature_value_proto(&fea, "121,131,141");
    ASSERT_TRUE(policy._check(fea, &hit));
    EXPECT_FALSE(hit);

    // valid hit num not enough
    create_feature_value_proto(&fea, "111,141");
    ASSERT_TRUE(policy._check(fea, &hit));
    EXPECT_FALSE(hit);
}

TEST_F(SessionProbruleTestSuite, _check_ok_with_gray_file) {
    comcfg::Configure conf;
    conf.load("./conf", "session_prob_rule_ok.conf");
    auto policy = SessionProbRule();
    ASSERT_TRUE(policy.init(conf["session_prob"]["rule"][2]));
    typedef anti::themis::common_lib::GrayFileDict GrayFileDict;
    EXPECT_NE((const GrayFileDict*)NULL, policy._gray.get());
    EXPECT_NE((const GrayFileDict*)NULL, policy._prob_dict.get());
    FeatureValueProto fea;
    bool hit = false;
    // normal 
    create_feature_value_proto(&fea, "11,121,131");
    ASSERT_TRUE(policy._check(fea, &hit));
    EXPECT_TRUE(hit);

    // 4 valid
    create_feature_value_proto(&fea, "11,121,131,111");
    ASSERT_TRUE(policy._check(fea, &hit));
    EXPECT_FALSE(hit);

    // only one
    create_feature_value_proto(&fea, "121");
    ASSERT_TRUE(policy._check(fea, &hit));
    EXPECT_FALSE(hit);

    // only one valid
    create_feature_value_proto(&fea, "131,181,191");
    ASSERT_TRUE(policy._check(fea, &hit));
    EXPECT_TRUE(hit);

    // valid hit num not enough
    create_feature_value_proto(&fea, "181,141");
    ASSERT_TRUE(policy._check(fea, &hit));
    EXPECT_FALSE(hit);
}

}
}
}
