// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: rule_interface_test.cpp
// @Last modified: 2015-07-24 11:36:54
// @Brief: 

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <file_dict_manager.h>
#include "rule_interface.h"

using anti::themis::common_lib::FileDictManagerSingleton;
using ::testing::_;
using ::testing::Return;
using ::testing::SetArgPointee;
using ::testing::DoAll;

namespace anti {
namespace themis {
namespace feature_lib {

class MockRuleBase : public RuleBase {
public:
    MOCK_METHOD1(_init, bool(const comcfg::ConfigUnit& conf));
    MOCK_CONST_METHOD2(_check, bool(const FeatureValueProto&, bool*));

private:
    void _uninit() {}
};

class RuleBaseTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {}
    virtual void TearDown() {}

private:
    MockRuleBase _obj;
};

TEST_F(RuleBaseTestSuite, construction_case) {
    EXPECT_EQ(0LU, _obj._feature_id);
}

TEST_F(RuleBaseTestSuite, init_case) {
    ASSERT_TRUE(FileDictManagerSingleton::instance().init(
            "./conf", "file_dict_manager.conf"));
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "rule_test.conf"));
    uint32_t num = conf["base"]["invalid"].size();
    ASSERT_LE(3U, num);
    for (uint32_t i = 0U; i < num; ++i) {
        EXPECT_FALSE(_obj.init(conf["base"]["invalid"][i]));
        _obj.uninit();
    }

    num = conf["base"]["valid"].size();
    ASSERT_LE(2U, num);
    EXPECT_CALL(_obj, _init(_)).WillRepeatedly(Return(true));

    ASSERT_TRUE(_obj.init(conf["base"]["valid"][0]));
    EXPECT_EQ(123LU, _obj._feature_id);
    EXPECT_TRUE(_obj._gray.get() != NULL);
    _obj.uninit();

    ASSERT_TRUE(_obj.init(conf["base"]["valid"][1]));
    EXPECT_EQ(456LU, _obj._feature_id);
    EXPECT_TRUE(_obj._gray.get() == NULL);
    _obj.uninit();
    FileDictManagerSingleton::instance().uninit();
}

TEST_F(RuleBaseTestSuite, check_case) {
    _obj._feature_id = 111LU;
    FeatureManager fm;
    for (uint32_t i = 0; i < 5U; ++i) {
        std::shared_ptr<FeatureValueProto> fea(new FeatureValueProto());
        fea->set_feature_id(111LU);
        fea->set_log_id(123LU);
        fm.insert(fea);
    }
    // input NULL
    EXPECT_FALSE(_obj.check(fm, NULL));

    EXPECT_CALL(_obj, _check(_, _))
            .WillOnce(Return(false))
            .WillOnce(DoAll(SetArgPointee<1>(false), Return(true)))
            .WillOnce(DoAll(SetArgPointee<1>(true), Return(true)));
    bool result = false;
    EXPECT_FALSE(_obj.check(fm, &result));

    ASSERT_TRUE(_obj.check(fm, &result));
    EXPECT_TRUE(result);
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

