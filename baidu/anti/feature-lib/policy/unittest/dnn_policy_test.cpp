// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: houruijie
// 
// @Brief: 

#include "dnn_policy.h"
#include <gtest/gtest.h>
#include <com_log.h>
#include <fstream>
#include <boost/lexical_cast.hpp>
#include <boost/algorithm/string.hpp>

namespace anti {
namespace themis {
namespace feature_lib {

class DNNPolicyTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {}
    virtual void TearDOwn() {}
private:
    DNNPolicy _obj;
};

TEST_F(DNNPolicyTestSuite, init_case) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "model_policy_test.conf"));
    ASSERT_FALSE(_obj.load_model("./conf", "error.conf"));

    _obj._policy_id = 10000;
    ASSERT_FALSE(_obj.load_model("./conf", "model.conf"));

    _obj._policy_id = 10001;
    ASSERT_FALSE(_obj.load_model("./conf", "model.conf"));

    _obj._policy_id = 20000;
    ASSERT_TRUE(_obj.load_model("./conf", "model.conf"));
    ASSERT_DOUBLE_EQ(0.5, _obj._threshold);

    _obj.uninit();
}

TEST_F(DNNPolicyTestSuite, normalize_case) {
    FeaVecMap fi;
    fi[0] = "0.3";
    fi[1] = "100";
    fi[2] = "-9";

    ASSERT_FALSE(_obj._load_normalize("./conf/error"));
    ASSERT_FALSE(_obj._load_normalize("./conf/model.conf"));
    ASSERT_TRUE(_obj._load_normalize("./conf/dnn.fea.stat"));
    ASSERT_DOUBLE_EQ(0.1339078039, _obj._fea_avg[90000]);
    ASSERT_DOUBLE_EQ(0.0711564, _obj._fea_std[90000]);
    ASSERT_DOUBLE_EQ(0.0076209988, _obj._fea_avg[90002]);
    ASSERT_DOUBLE_EQ(0.0319054, _obj._fea_std[90002]);

    const std::shared_ptr<FeatureValueProto> fea_ptr(new FeatureValueProto());
    FeatureValueProto* fea = NULL;
    fea = fea_ptr.get();
    fea->set_feature_id(90001LU);

    std::string value;
    ASSERT_FALSE(_obj._normalize(fea, 0, "0", NULL));
    _obj._fea_num = 1;
    ASSERT_FALSE(_obj._normalize(fea, 2, "0.3", &value));

    _obj._fea_num = 1;
    fea->set_feature_id(90000LU);
    ASSERT_TRUE(_obj._normalize(fea, 0, "0.3", &value));
    ASSERT_EQ("2.334184923633011", value);

    _obj._fea_num = 3;
    fea->set_feature_id(90002LU);
    ASSERT_TRUE(_obj._normalize(fea, 2, "2", &value));
    ASSERT_EQ("62.44645110858977", value);

    _obj._fea_num = 3;
    fea->set_feature_id(90002LU);
    ASSERT_TRUE(_obj._normalize(fea, 2, "", &value));
    ASSERT_EQ("0", value);

    _obj._fea_num = 3;
    fea->set_feature_id(90002LU);
    ASSERT_TRUE(_obj._normalize(fea, 2, "-", &value));
    ASSERT_EQ("0", value);

}

TEST_F(DNNPolicyTestSuite, _get_fea_list) {
    FeaVecMap fi;
    fi[0] = "0.3";
    fi[1] = "100";
    fi[2] = "-9";

    ASSERT_FALSE(_obj._get_fea_list(fi, NULL));

    std::vector<std::pair<uint64_t, double>> fea_list;
    _obj._fea_num = 3;
    ASSERT_TRUE(_obj._load_normalize("./conf/dnn.fea.stat"));
    ASSERT_TRUE(_obj._get_fea_list(fi, &fea_list));

    ASSERT_EQ(0.3, fea_list[0].second);
    ASSERT_EQ(100, fea_list[1].second);
    ASSERT_EQ(-9, fea_list[2].second);
}

TEST_F(DNNPolicyTestSuite, predict) {
    FeaVecMap fi;

    PolicyResultProto result;
    const FeatureManager feas;

    ASSERT_FALSE(_obj.predict(fi, NULL));
    _obj._policy_id = 20000;
    _obj._fea_num = 220;
    ASSERT_TRUE(_obj.load_model("./conf", "model.conf"));

    std::ifstream ifs1("./conf/pctr_man.txt", std::ifstream::in);
    std::string line;
    int idx = 0;
    double pctr[ModelPolicy::MAX_FEATURE_NUM];
    while (getline(ifs1, line)) {
        double value = boost::lexical_cast<double>(line);
        pctr[idx] = value;
        idx++;
    }

    std::ifstream ifs("./conf/man_label.org");
    idx = 0;
    while (std::getline(ifs, line)) {
        std::vector<std::string> fields;
        boost::split(fields, line, boost::is_any_of(" \t"), boost::token_compress_on);
        for (int i = 2; i < 222; i++) {
            std::vector<std::string> items;
            boost::split(items, fields[i], boost::is_any_of(":"), boost::token_compress_on);
            fi[i - 2] = items[2];
        }

        ASSERT_TRUE(_obj.predict(fi, &result));

        char buf[20];
        float actual;
        snprintf(buf, 20, "%.3f", result.value());
        sscanf(buf, "%f", &actual);

        float excepted;
        snprintf(buf, 10, "%.3f", pctr[idx]);
        sscanf(buf, "%f", &excepted);
        ASSERT_FLOAT_EQ(actual, excepted);

        idx++;
    }
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */
