// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: houruijie
// 
// @Brief: 

#include "lr_policy.h"
#include <gtest/gtest.h>

namespace anti {
namespace themis {
namespace feature_lib {

class LRPolicyTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {}
    virtual void TearDOwn() {}
private:
    LRPolicy _obj;
};

TEST_F(LRPolicyTestSuite, init_case) {
    _obj._policy_id = 30001;
    ASSERT_FALSE(_obj.load_model("./conf", "model.conf"));

    _obj._policy_id = 30002;
    ASSERT_FALSE(_obj.load_model("./conf", "model.conf"));

    _obj._policy_id = 30003;
    ASSERT_TRUE(_obj.load_model("./conf", "model.conf"));
    ASSERT_DOUBLE_EQ(0.3, _obj._threshold);
    ASSERT_DOUBLE_EQ(3, _obj._decimal_places);

    _obj.uninit();
}

TEST_F(LRPolicyTestSuite, load_fea_weight) {
    ASSERT_FALSE(_obj._load_fea_weight("empty"));
    ASSERT_TRUE(_obj._load_fea_weight("./conf/lr.fea.weight"));
    ASSERT_EQ(0.1, _obj._fea_weight.find("0:-10000")->second);
    ASSERT_EQ(0.2, _obj._fea_weight.find("1:-INVALID-")->second);
    ASSERT_EQ(0.2, _obj._fea_weight.find("2:-NULL-")->second);
    ASSERT_EQ(0.4, _obj._fea_weight.find("3:0.00")->second);
    ASSERT_EQ(0.5, _obj._fea_weight.find("4:76212282.00")->second);
}

TEST_F(LRPolicyTestSuite, normalize) {
    const std::shared_ptr<FeatureValueProto> fea_ptr(new FeatureValueProto());
    FeatureValueProto* fea = NULL;
    fea = fea_ptr.get();
    fea->set_feature_id(90002LU);
    fea->set_feature_type(FeatureValueProto::RATIO);

    std::string normalized;
    ASSERT_FALSE(_obj._normalize(fea, 0, "0.3", NULL));
    _obj._fea_num = 2;
    ASSERT_FALSE(_obj._normalize(fea, 5, "0.3", &normalized));

    _obj._fea_num = 1;
    ASSERT_EQ(3, _obj._decimal_places);
    ASSERT_TRUE(_obj._normalize(fea, 0, "0.3", &normalized));
    ASSERT_STREQ("0.300", normalized.data());
    _obj._decimal_places = 3;
    ASSERT_TRUE(_obj._normalize(fea, 0, "0.3234890", &normalized));
    ASSERT_STREQ("0.323", normalized.data());
    ASSERT_TRUE(_obj._normalize(fea, 0, "0.3", &normalized));
    ASSERT_STREQ("0.300", normalized.data());
    ASSERT_TRUE(_obj._normalize(fea, 0, "1", &normalized));
    ASSERT_STREQ("1.000", normalized.data());
    fea->set_feature_type(FeatureValueProto::SEGMENT);
    ASSERT_TRUE(_obj._normalize(fea, 0, "0.3234890", &normalized));
    ASSERT_STREQ("0.323", normalized.data());
    ASSERT_TRUE(_obj._normalize(fea, 0, "1.7e-308", &normalized));
    ASSERT_STREQ("1.7e-308", normalized.data());
    ASSERT_TRUE(_obj._normalize(fea, 0, "1.7e-309", &normalized));
    ASSERT_STREQ("1.7e-309", normalized.data());
    ASSERT_TRUE(_obj._normalize(fea, 0, "2e2", &normalized));
    ASSERT_STREQ("200.000", normalized.data());
    ASSERT_TRUE(_obj._normalize(fea, 0, "0.000000", &normalized));
    ASSERT_STREQ("0.000", normalized.data());
    ASSERT_TRUE(_obj._normalize(fea, 0, "1.7e308", &normalized));
       ASSERT_STREQ("1699999999999999938830795788659981743333460743040758745027731191" \
                 "9353772917816056586433009178758470798857226246798318891916991610" \
                 "5593357174268369962062473635296474636515660464935663040684957844" \
                 "3035243678150285532727122989863863108286445132123539211232533116" \
                 "75499856875650512437415429217994623324794855339589632.000", normalized.data());
    ASSERT_TRUE(_obj._normalize(fea, 0, "2e-6", &normalized));
    ASSERT_STREQ("0.000", normalized.data());
    ASSERT_TRUE(_obj._normalize(fea, 0, "-INVALID-", &normalized));
    ASSERT_STREQ(ModelPolicy::INVALID_DEFAULT_FEA_VALUE.data(), normalized.data());
    ASSERT_TRUE(_obj._normalize(fea, 0, "-NULL-", &normalized));
    ASSERT_STREQ(LRPolicy::DEFAULT_LR_VALUE.data(), normalized.data());
    ASSERT_TRUE(_obj._normalize(fea, 0, "", &normalized));
    ASSERT_STREQ(LRPolicy::DEFAULT_LR_VALUE.data(), normalized.data());
}

TEST_F(LRPolicyTestSuite, predict) {
    FeaVecMap fi;
    fi[0] = "0.4";
    fi[1] = "0.85492";
    fi[2] = "-NULL-";

    PolicyResultProto result;

    comcfg::Configure conf;
    _obj._policy_id = 30003;
    _obj._fea_num = 3;

    _obj._slot2feaid[0] = 90001;
    _obj._slot2feaid[1] = 90002;
    _obj._slot2feaid[2] = 90003;

    ASSERT_TRUE(_obj.load_model("./conf", "model.conf"));
    ASSERT_EQ(0.1, _obj._fea_weight.find("0:-10000")->second);
    ASSERT_EQ(0.2, _obj._fea_weight.find("1:-INVALID-")->second);
    ASSERT_EQ(0.2, _obj._fea_weight.find("2:-NULL-")->second);
    ASSERT_EQ(0.4, _obj._fea_weight.find("3:0.00")->second);
    ASSERT_EQ(0.5, _obj._fea_weight.find("4:76212282.00")->second);

    ASSERT_TRUE(_obj.predict(fi, &result));
    ASSERT_DOUBLE_EQ(0.57444250583648682, result.value());
    ASSERT_TRUE(result.hit());

    fi[3] = "0.00";
    ASSERT_TRUE(_obj.predict(fi, &result));
    ASSERT_DOUBLE_EQ(0.57444250583648682, result.value());
    ASSERT_TRUE(result.hit());

    _obj._fea_num = 4;
    ASSERT_TRUE(_obj.predict(fi, &result));
    ASSERT_DOUBLE_EQ(0.66818779706954956, result.value());
    ASSERT_TRUE(result.hit());

    ASSERT_FALSE(_obj.predict(fi, NULL));
}

TEST_F(LRPolicyTestSuite, detect) {
    FeatureManager fm;
    std::shared_ptr<FeatureValueProto> fea1(new FeatureValueProto());
    fea1->set_feature_id(10000LU);
    fea1->set_log_id(123LU);
    fea1->set_valid(true);
    fea1->set_value("0.426");
    fea1->set_feature_type(FeatureValueProto::RATIO);
    fm.insert(fea1);

    std::shared_ptr<FeatureValueProto> fea2(new FeatureValueProto());
    fea2->set_feature_id(10001LU);
    fea2->set_log_id(123LU);
    fea2->set_valid(false);
    fea2->set_value("0.2");
    fea2->set_feature_type(FeatureValueProto::RATIO);
    fm.insert(fea2);

    std::shared_ptr<FeatureValueProto> fea3(new FeatureValueProto());
    fea3->set_feature_id(10002LU);
    fea3->set_log_id(123LU);
    fea3->set_valid(true);
    fea3->set_value("0");
    fea3->set_feature_type(FeatureValueProto::CARESPACE);
    fm.insert(fea3);

    std::shared_ptr<FeatureValueProto> fea4(new FeatureValueProto());
    fea4->set_feature_id(10003LU);
    fea4->set_log_id(123LU);
    fea4->set_valid(true);
    fea4->set_value("orz");
    fea4->set_feature_type(FeatureValueProto::ORIGINAL);
    fm.insert(fea4);

    std::shared_ptr<FeatureValueProto> fea5(new FeatureValueProto());
    fea5->set_feature_id(10004LU);
    fea5->set_log_id(123LU);
    fea5->set_valid(true);
    fea5->set_value("");
    fea5->set_feature_type(FeatureValueProto::ORIGINAL);
    fm.insert(fea5);

    _obj._slot2feaid.clear();
    _obj._slot2feaid[0] = 10000lu;
    _obj._slot2feaid[1] = 10001lu;
    _obj._slot2feaid[2] = 10002lu;
    _obj._slot2feaid[3] = 10003lu;
    _obj._slot2feaid[4] = 10004lu;

    _obj._threshold = 0.9;
    _obj._decimal_places = 2;
    _obj._fea_num = 5;
    _obj._slot_num = 5;

    _obj._fea_weight.clear();
    _obj._fea_weight["0:0.43"] = 0.1;
    _obj._fea_weight["1:-INVALID-"] = 0.2;
    _obj._fea_weight["2:0.00"] = -0.3;
    _obj._fea_weight["3:orz"] = 0.4;
    _obj._fea_weight["4:-NULL-"] = -0.5;

    PolicyResultProto result;

    ASSERT_TRUE(_obj.detect(fm, &result));
    ASSERT_DOUBLE_EQ(0.47502082586288452, result.value());
    ASSERT_FALSE(result.hit());

    _obj._fea_weight.clear();
    _obj._fea_weight["0:-10000"] = 0.1;
    _obj._fea_weight["1:-INVALID-"] = 0.2;
    _obj._fea_weight["2:0.00"] = -0.3;
    _obj._fea_weight["3:orz"] = 0.4;
    _obj._fea_weight["4:-NULL-"] = -0.5;

    ASSERT_TRUE(_obj.detect(fm, &result));
    ASSERT_DOUBLE_EQ(0.47502082586288452, result.value());
    ASSERT_FALSE(result.hit());
}
 
}  // namespace feature_lib
}  // namespace themis
}  // namespace anti
/* vim: set ts=4 sw=4: */
