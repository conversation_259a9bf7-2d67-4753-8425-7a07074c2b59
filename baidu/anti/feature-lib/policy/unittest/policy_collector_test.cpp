// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: policy_manager_test.cpp
// @Last modified: 2018-03-23 15:39:04
// @Brief: 

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <file_dict_manager.h>
#include "policy_collector.h"

using anti::themis::common_lib::FileDictManagerSingleton;
using ::testing::_;
using ::testing::Return;
using ::testing::DoAll;
using ::testing::SetArgPointee;

namespace anti {
namespace themis {
namespace feature_lib {

typedef anti::themis::RecordType RecordType;

class PolicyCollectorTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
         _obj._manager.reset(new PolicyManager); 
    }
    virtual void TearDown() {}

private:
    PolicyCollector _obj;
};

TEST_F(PolicyCollectorTestSuite, init_by_invalid_conf_case) {
    EXPECT_FALSE(_obj.init("xx", "xxxx"));

    EXPECT_FALSE(_obj.init("./conf", "invalid_policy_manager.conf"));

    EXPECT_FALSE(_obj.init("./conf", "invalid_policy_manager1.conf"));

    EXPECT_FALSE(_obj.init("./conf", "invalid_policy_manager2.conf"));
}

TEST_F(PolicyCollectorTestSuite, init_products_case) {
    ASSERT_TRUE(FileDictManagerSingleton::instance().init(
            "./conf", "file_dict_manager.conf"));
    EXPECT_TRUE(_obj.init("./conf", "products.conf"));
    EXPECT_EQ(_obj._manager_map.size(), 2);
    FileDictManagerSingleton::instance().uninit();
}

TEST_F(PolicyCollectorTestSuite, init_by_valid_conf_case) {
    ASSERT_TRUE(FileDictManagerSingleton::instance().init(
            "./conf", "file_dict_manager.conf"));
    ASSERT_TRUE(_obj.init("./conf", "policy_manager_test.conf"));
    EXPECT_EQ(4U, _obj._manager->_policies.size());
    const uint64_t exp_id[] = {123456, 123457, 123458, 123459};
    for (uint32_t i = 0; i < _obj._manager->_policies.size(); ++i) {
        EXPECT_EQ(exp_id[i], _obj._manager->_policies[i]->policy_id());
    }
    
    ASSERT_TRUE(_obj.init("./conf", "pol_mgr_multi_line_test.conf"));
    ASSERT_EQ(4U, _obj._manager->_policies.size());
    for (uint32_t i = 0; i < _obj._manager->_policies.size(); ++i) {
        EXPECT_EQ(exp_id[i], _obj._manager->_policies[i]->policy_id());
    }

    ASSERT_TRUE(_obj.init("./conf", "pol_mgr_cmp_test.conf"));
    ASSERT_EQ(4U, _obj._manager->_policies.size());
    for (uint32_t i = 0; i < _obj._manager->_policies.size(); ++i) {
        EXPECT_EQ(exp_id[i], _obj._manager->_policies[i]->policy_id());
    }
    FileDictManagerSingleton::instance().uninit();
}

TEST_F(PolicyCollectorTestSuite, detect_by_invalid_input_case) {
    FeatureManager fm;
    RecordType rtype;
    EXPECT_FALSE(_obj.detect(&fm, NULL, rtype));

    _obj._manager->_policies.push_back(NULL);
    std::vector<PolicyResultProto> results;
    EXPECT_FALSE(_obj.detect(&fm, &results, rtype));
}

TEST_F(PolicyCollectorTestSuite, detect_by_valid_input_case) {
    ASSERT_TRUE(FileDictManagerSingleton::instance().init(
            "./conf", "file_dict_manager.conf"));
    ASSERT_TRUE(_obj.init("./conf", "policy_manager_test.conf"));

    FeatureManager fm;
    // invalid feature
    {
        std::shared_ptr<FeatureValueProto> fea(new FeatureValueProto());
        fea->set_feature_id(1111LU);
        fea->set_feature_type(FeatureValueProto::SEGMENT);
        fea->set_log_id(8888888LU);
        fm.insert(fea);
    }

    // valid features 
    {
        std::shared_ptr<FeatureValueProto> fea(new FeatureValueProto());
        fea->set_feature_id(1112LU);
        fea->set_feature_type(FeatureValueProto::RATIO);
        fea->set_in_filter(true);
        fea->set_filter_count(100L);
        fea->set_refer_count(160L);
        fea->set_log_id(8888888LU);
        fm.insert(fea);
    }
    {
        std::shared_ptr<FeatureValueProto> fea(new FeatureValueProto());
        fea->set_feature_id(1113LU);
        fea->set_feature_type(FeatureValueProto::DISTRIBUTION);
        fea->set_bucket_idx(0U);
        fea->add_buckets()->set_idx(0U);
        fea->mutable_buckets(0)->set_count(10);
        fea->add_buckets()->set_idx(1U);
        fea->mutable_buckets(1)->set_count(90);
        fea->set_log_id(8888888LU);
        fm.insert(fea);
    }
    { 
        std::shared_ptr<FeatureValueProto> fea(new FeatureValueProto());
        fea->set_feature_id(1114LU);
        fea->set_feature_type(FeatureValueProto::SEGMENT);
        fea->set_log_id(8888888LU);
        fea->set_last_seg_count(0L);
        fea->set_cur_seg_count(15L);
        fm.insert(fea);
    }
    { 
        std::shared_ptr<FeatureValueProto> fea(new FeatureValueProto());
        fea->set_feature_id(1115LU);
        fea->set_feature_type(FeatureValueProto::RATIO);
        fea->set_in_filter(true);
        fea->set_filter_count(100L);
        fea->set_refer_count(160L);
        fea->set_log_id(8888888LU);
        fm.insert(fea);
    }

    std::vector<PolicyResultProto> results;
    RecordType rtype = RecordType::ASP_DISPLAY_LOG;
    ASSERT_TRUE(_obj.detect(&fm, &results, rtype));
    ASSERT_EQ(2U, results.size());
    uint64_t exp_pids[] = {123457, 123459};
    for (uint32_t i = 0; i < 2U; ++i) {
        EXPECT_EQ(exp_pids[i], results[i].policy_id());
        EXPECT_TRUE(results[i].hit());
    }
    FileDictManagerSingleton::instance().uninit();
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

