// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: rule_policy_test.cpp
// @Last modified: 2015-07-24 11:49:06
// @Brief: 

#include <gtest/gtest.h>
#include <file_dict_manager.h>
#include "rule_policy.h"

using anti::themis::common_lib::FileDictManagerSingleton;

namespace anti {
namespace themis {
namespace feature_lib {

class RulePolicyTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {}
    virtual void TearDown() {}

private:
    RulePolicy _obj;
};

TEST_F(RulePolicyTestSuite, construction_case) {
    EXPECT_EQ(0LU, _obj.policy_id());
}

TEST_F(RulePolicyTestSuite, init_case) {
    ASSERT_TRUE(FileDictManagerSingleton::instance().init(
            "./conf", "file_dict_manager.conf"));
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "rule_policy_test.conf"));
    uint32_t num = conf["invalid"].size();
    for (uint32_t i = 0U; i < num; ++i) {
        EXPECT_FALSE(_obj.init(conf["invalid"][i]));
        _obj.uninit();
    }

    ASSERT_TRUE(_obj.init(conf["valid"]));
    EXPECT_EQ(70032LU, _obj.policy_id());
    EXPECT_EQ(2U, _obj._rules.size());
    _obj.uninit();
    FileDictManagerSingleton::instance().uninit();
}

TEST_F(RulePolicyTestSuite, detect_case) {
    // input NULL
    FeatureManager fm;
    EXPECT_FALSE(_obj.detect(fm, NULL));

    // rule is NULL
    PolicyResultProto result;
    _obj._rules.push_back(NULL);
    EXPECT_FALSE(_obj.detect(fm, &result));
    _obj.uninit();

    ASSERT_TRUE(FileDictManagerSingleton::instance().init(
            "./conf", "file_dict_manager.conf"));
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "rule_policy_test.conf"));
    ASSERT_TRUE(_obj.init(conf["valid"]));
    
    // not meet any rule
    ASSERT_TRUE(_obj.detect(fm, &result));
    EXPECT_FALSE(result.hit());
    EXPECT_EQ(_obj.policy_id(), result.policy_id());
    result.Clear();
    
    // invalie fea
    {
        std::shared_ptr<FeatureValueProto> fea(new FeatureValueProto());
        fea->set_feature_id(1111LU);
        fm.insert(fea);
        EXPECT_FALSE(_obj.detect(fm, &result));
        result.Clear();
        fm.clear();
    }

    // meet all the rules
    {
        std::shared_ptr<FeatureValueProto> fea(new FeatureValueProto());
        fea->set_feature_id(1111LU);
        fea->set_feature_type(FeatureValueProto::SEGMENT);
        fea->set_view_sign(0LU);
        fea->set_last_seg_count(0L);
        fea->set_cur_seg_count(100L);
        fm.insert(fea);
    }
    {
        std::shared_ptr<FeatureValueProto> fea(new FeatureValueProto());
        fea->set_feature_id(1112LU);
        fea->set_feature_type(FeatureValueProto::RATIO);
        fea->set_view_sign(0LU);
        fea->set_in_filter(true);
        fea->set_filter_count(100L);
        fea->set_refer_count(100L);
        fm.insert(fea);
    }
    ASSERT_TRUE(_obj.detect(fm, &result));
    EXPECT_TRUE(result.hit());
    EXPECT_EQ(_obj.policy_id(), result.policy_id());

    _obj.uninit();
    FileDictManagerSingleton::instance().uninit();
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

