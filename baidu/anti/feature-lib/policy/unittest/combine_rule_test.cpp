// Copyright 2016 Baidu Inc. All Right Reserved.
// Author <PERSON> (<EMAIL>)
// 
// brief: 

#include <gtest/gtest.h>
#include <sign_util.h>
#include "combine_rule.h"
#include "feature_manager.h"
#include "boost/lexical_cast.hpp"

using anti::baselib::SignUtil;

namespace anti {
namespace themis {
namespace feature_lib {

typedef std::shared_ptr<FeatureValueProto> FeaPtr;

class CombineRuleTestSuite : public ::testing::Test {
public:
    virtual void SetUp() {}
    virtual void TearDown() {}

private:
    CombineRule _obj;
};

TEST_F(CombineRuleTestSuite, construction_case) {
    EXPECT_EQ(0LU, _obj._left_id);
    EXPECT_EQ(0LU, _obj._right_id);
    EXPECT_TRUE(_obj._cal == NULL);
}

TEST_F(CombineRuleTestSuite, init_case) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "rule_test.conf"));
    
    for (uint32_t i = 0; i < conf["combine"]["invalid"].size(); ++i) {
        ASSERT_FALSE(_obj._init(conf["combine"]["invalid"][i]));
        _obj.uninit();
    }

    typedef double(*oper)(const double, const double);
    oper exp_op[] = {
        CombineRule::_div,
        CombineRule::_div,
        CombineRule::_div,
        CombineRule::_div,
        CombineRule::_add,
        CombineRule::_sub,
        CombineRule::_mul
    };

    for (uint32_t i = 0; i < conf["combine"]["valid"].size(); ++i) {
        ASSERT_TRUE(_obj._init(conf["combine"]["valid"][i]));
        EXPECT_EQ(100LU, _obj._left_id);
        EXPECT_EQ(101LU, _obj._right_id);
        EXPECT_EQ(exp_op[i], _obj._cal);
        _obj.uninit();
    }
}

TEST_F(CombineRuleTestSuite, combine_case) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "rule_test.conf"));
    FeatureManager fea_manager;
    bool hit = false;
    
    {
        FeaPtr fea(new(std::nothrow) FeatureValueProto());
        fea->set_feature_id(100LU);
        fea->set_feature_type(FeatureValueProto::DISTINCT);
        fea->set_valid(true);
        fea->set_value("15");
        ASSERT_TRUE(fea_manager.insert(fea));
        hit = false;
        ASSERT_TRUE(_obj.init(conf["combine"]["valid"][0]));
        ASSERT_TRUE(_obj.check(fea_manager, &hit));
        ASSERT_FALSE(hit);
        _obj.uninit();
    }

    {
        FeaPtr fea(new(std::nothrow) FeatureValueProto());
        fea->set_feature_id(101LU);
        fea->set_feature_type(FeatureValueProto::DISTINCT);
        fea->set_valid(true);
        fea->set_value("5");
        ASSERT_TRUE(fea_manager.insert(fea));
        bool exp[] = {true, true, false, true};
        for (uint32_t i = 0; i < 4; ++i) {
            ASSERT_TRUE(_obj._init(conf["combine"]["valid"][i]));
            hit = false;
            ASSERT_TRUE(_obj.check(fea_manager, &hit));
            EXPECT_EQ(exp[i], hit);
            _obj.uninit();
        }
    }

    {
        FeaPtr fea(new(std::nothrow) FeatureValueProto());
        fea->set_feature_id(101LU);
        fea->set_feature_type(FeatureValueProto::DISTINCT);
        fea->set_valid(true);
        fea->set_value("15");
        ASSERT_TRUE(fea_manager.insert(fea));
        bool exp[] = {true, false, true, true};
        for (uint32_t i = 0; i < 4; ++i) {
            ASSERT_TRUE(_obj._init(conf["combine"]["valid"][i]));
            hit = false;
            ASSERT_TRUE(_obj.check(fea_manager, &hit));
            EXPECT_EQ(exp[i], hit);
            _obj.uninit();
        }
    }

    {
        FeaPtr fea(new(std::nothrow) FeatureValueProto());
        fea->set_feature_id(101LU);
        fea->set_feature_type(FeatureValueProto::DISTINCT);
        fea->set_valid(true);
        fea->set_value("3");
        ASSERT_TRUE(fea_manager.insert(fea));
        bool exp[] = {true, false, true, false};
        for (uint32_t i = 0; i < 4; ++i) {
            ASSERT_TRUE(_obj._init(conf["combine"]["valid"][i]));
            hit = false;
            ASSERT_TRUE(_obj.check(fea_manager, &hit));
            EXPECT_EQ(exp[i], hit);
            _obj.uninit();
        }
    }

    {
        fea_manager._feas.erase(101LU);
        FeaPtr fea(new(std::nothrow) FeatureValueProto());
        fea->set_feature_id(101LU);
        fea->set_feature_type(FeatureValueProto::DISTINCT);
        fea->set_valid(true);
        fea->set_value("xxx");
        ASSERT_TRUE(fea_manager.insert(fea));
        ASSERT_TRUE(_obj._init(conf["combine"]["valid"][0]));
        hit = false;
        ASSERT_FALSE(_obj.check(fea_manager, &hit));
    }
}

TEST_F(CombineRuleTestSuite, _check_threshold_case) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "rule_test.conf"));
    ASSERT_TRUE(_obj.init(conf["combine"]["valid"][0]));
    FeatureValueProto l;
    l.set_value("20");
    FeatureValueProto r;
    r.set_value("4");
    FeatureValueProto inv;
    inv.set_value("xxx");
    bool hit = false;
    ASSERT_FALSE(_obj._check_threshold(l, inv, &hit));
    ASSERT_TRUE(_obj._check_threshold(l, r, &hit));
}

TEST_F(CombineRuleTestSuite, _calculator_case) {
    EXPECT_DOUBLE_EQ(1.0, CombineRule::_add(0.1, 0.9));
    EXPECT_DOUBLE_EQ(0.8, CombineRule::_sub(0.9, 0.1));
    EXPECT_DOUBLE_EQ(0.09, CombineRule::_mul(0.9, 0.1));
    EXPECT_DOUBLE_EQ(9, CombineRule::_div(0.9, 0.1));
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

