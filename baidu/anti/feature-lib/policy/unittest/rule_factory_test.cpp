// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: rule_factory_test.cpp
// @Last modified: 2015-11-04 19:02:34
// @Brief: 

#include <gtest/gtest.h>
#include "rule_factory.h"
#include "segment_limit_rule.h"
#include "ratio_limit_rule.h"
#include "distribution_limit_rule.h"
#include "boolean_rule.h"
#include "ratio_black_limit_rule.h"

namespace anti {
namespace themis {
namespace feature_lib {

TEST(RuleFactoryTestSuite, create_case) {
    EXPECT_TRUE(RuleFactory::create("xxxx") == NULL);
    RuleInterface* rule = NULL;

    rule = RuleFactory::create("segment");
    EXPECT_TRUE(dynamic_cast<SegmentLimitRule*>(rule) != NULL);
    delete rule;
    rule = NULL;

    rule = RuleFactory::create("ratio");
    EXPECT_TRUE(dynamic_cast<RatioLimitRule*>(rule) != NULL);
    delete rule;
    rule = NULL;

    rule = RuleFactory::create("distribution");
    EXPECT_TRUE(dynamic_cast<DistributionLimitRule*>(rule) != NULL);
    delete rule;
    rule = NULL;

    rule = RuleFactory::create("boolean");
    EXPECT_TRUE(dynamic_cast<BooleanRule*>(rule) != NULL);
    delete rule;
    rule = NULL;

    rule = RuleFactory::create("auto_ratio");
    EXPECT_TRUE(dynamic_cast<RatioBlackLimitRule*>(rule) != NULL);
    delete rule;
    rule = NULL;

    rule = RuleFactory::create("auto_segment");
    EXPECT_TRUE(dynamic_cast<SegmentLimitRule*>(rule) != NULL);
    delete rule;
    rule = NULL;

    rule = RuleFactory::create("count_distribution");
    EXPECT_TRUE(dynamic_cast<DistributionLimitRule*>(rule) != NULL);
    delete rule;
    rule = NULL;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

