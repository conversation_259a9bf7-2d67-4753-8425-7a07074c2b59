// Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
// @brief 

#include "deepshield_policy.h"

namespace anti {
namespace themis {
namespace feature_lib {

DeepShieldPolicy::DeepShieldPolicy() {}
DeepShieldPolicy::~DeepShieldPolicy() {}

bool DeepShieldPolicy::init(const comcfg::ConfigUnit& conf) {
    try {
        _core_feature_id = conf["core_feature_id"].to_uint64();
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("comcfg::ConfigException : %s", e.what());
        return false;
    }
    return RulePolicy::init(conf);
}

void DeepShieldPolicy::uninit() {}

bool DeepShieldPolicy::detect(const FeatureManager& feas,
                              PolicyResultProto* result) const {
    if (!RulePolicy::detect(feas, result)) {
        CWARNING_LOG("judge rule of shield policy failed");
        return false;
    }

    auto core_feat_iter_range = feas.query(_core_feature_id); 
    if (core_feat_iter_range.first == core_feat_iter_range.second) {
        CWARNING_LOG("no core_feature: %d", _core_feature_id);
        return false;
    }

    auto core_feat = core_feat_iter_range.first->second;
    
    result->set_view_sign(core_feat->view_sign());
    result->set_view_value(core_feat->view());
    return true;
}

}
}
}
