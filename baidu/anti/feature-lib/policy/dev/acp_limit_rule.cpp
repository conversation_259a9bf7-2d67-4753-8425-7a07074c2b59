// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// @Author: lijie(<EMAIL>)
// 
// @File: acp_limit_rule.cpp

#include "acp_limit_rule.h"
#include <com_log.h>

namespace anti {
namespace themis {
namespace feature_lib {

bool AcpLimitRule::_init(const comcfg::ConfigUnit& conf) {
    try {
        _threshold = conf["threshold"].to_double();
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("comcfg::ConfigException : %s", e.what());
        return false;
    }
    return true;
}

bool AcpLimitRule::_check(const FeatureValueProto& fea, bool* hit) const {
    if (fea.feature_type() != FeatureValueProto::ACP
            || !fea.has_valid()
            || !fea.has_acp_field()
            || !fea.acp_field().has_residual()
            || hit == NULL) {
        CFATAL_LOG("invalid fea or input hit == NULL");
        return false;
    }
    double threshold = _threshold;
    *hit = fea.valid() && (fea.acp_field().residual() > threshold);
    return true;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

