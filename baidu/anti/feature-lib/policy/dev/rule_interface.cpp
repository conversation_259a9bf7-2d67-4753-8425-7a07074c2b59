// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: rule_interface.cpp
// @Last modified: 2015-07-24 11:01:34
// @Brief: 

#include "rule_interface.h"
#include <com_log.h>
#include <file_dict_manager.h>

using anti::themis::common_lib::FileDictManager;
using anti::themis::common_lib::FileDictManagerSingleton;

namespace anti {
namespace themis {
namespace feature_lib {

bool RuleBase::init(const comcfg::ConfigUnit& conf) {
    try {
        _feature_id = conf["feature_id"].to_uint64();
        int error_code = 0;
        const char* gray_file = conf["gray_file"].to_cstr(&error_code);
        if (error_code == 0) {
            FileDictManager::ConstDictPtr fd =
                    FileDictManagerSingleton::instance().get_file_dict(gray_file);
            if (!fd) {
                CWARNING_LOG("call get_file_dict fail, file_key(%s)", gray_file);
                return false;
            }
            _gray = std::dynamic_pointer_cast<const GrayFileDict>(fd);
            if (!_gray) {
                CWARNING_LOG("convert file_key(%s) to GrayFileDict fail", gray_file);
                return false;
            }
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("comcfg::ConfigException : %s", e.what());
        return false;
    }
    return _init(conf);
}

bool RuleBase::check(const FeatureManager& feas, bool* hit) const {
    if (hit == NULL) {
        CFATAL_LOG("input hit == NULL");
        return false;
    }
    *hit = false;
    auto range = feas.query(_feature_id);
    for (auto iter = range.first; iter != range.second; ++iter) {
        const FeatureValueProto& fea = *(iter->second.get());
        if (!_check(fea, hit)) {
            CWARNING_LOG("call _check fea fail, feature_id(%lu) logid(%lu)",
                    fea.feature_id(), fea.log_id());
            return false;
        }

        if (*hit) {
            break;
        }
    }
    return true;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

