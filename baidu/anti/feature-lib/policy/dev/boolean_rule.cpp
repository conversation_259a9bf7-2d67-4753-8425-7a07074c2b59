// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: boolean_rule.cpp
// @Last modified: 2015-06-05 12:22:47
// @Brief: 

#include "boolean_rule.h"

namespace anti {
namespace themis {
namespace feature_lib {

bool BooleanRule::_init(const comcfg::ConfigUnit& conf) {
    try {
        std::string tmp(conf["condition"].to_cstr());
        if (tmp == "true") {
            _con = true;
        } else if (tmp == "false") {
            _con = false;
        } else {
            CWARNING_LOG("invalid condition(%s)", tmp.data());
            return false;
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("comcfg::ConfigException : %s", e.what());
        return false;
    }
    return true;
}

bool BooleanRule::_check(const FeatureValueProto& fea, bool* hit) const {
    if ((fea.feature_type() != FeatureValueProto::FILE_DICT
            && fea.feature_type() != FeatureValueProto::CARESPACE
            && fea.feature_type() != FeatureValueProto::DYNAMIC_BLACK_NAME)
            || !fea.has_condition()
            || hit == NULL) {
        CFATAL_LOG("invalid fea or input hit == NULL");
        return false;
    }
    *hit = !(fea.condition() ^ _con);
    return true;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

