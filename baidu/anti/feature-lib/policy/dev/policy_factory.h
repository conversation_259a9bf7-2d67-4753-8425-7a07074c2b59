// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: policy_factory.h
// @Last modified: 2015-05-21 15:08:39
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_POLICY_FACTORY_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_POLICY_FACTORY_H

#include <string>
#include "policy_interface.h"

namespace anti {
namespace themis {
namespace feature_lib {

class PolicyFactory {
public:
    static PolicyInterface* create(const std::string& type);
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_POLICY_FACTORY_H

/* vim: set ts=4 sw=4: */

