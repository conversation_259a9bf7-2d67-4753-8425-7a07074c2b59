// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: rule_interface.h
// @Last modified: 2016-11-10 19:01:53
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_RULE_INTERFACE_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_RULE_INTERFACE_H

#include <memory>
#include <Configure.h>
#include <gray_file_dict.h>
#include "feature_value.pb.h"
#include "feature_manager.h"
#include "policy_result.pb.h"

namespace anti {
namespace themis {
namespace feature_lib {

class RuleInterface {
public:
    virtual ~RuleInterface() {}
    virtual bool init(const comcfg::ConfigUnit& conf) = 0;
    virtual bool check(const FeatureManager& feas, bool* hit) const = 0;
    virtual void uninit() = 0;
};

class RuleBase : public RuleInterface {
public:
    RuleBase() : _feature_id(0LU) {}
    virtual ~RuleBase() {}

    virtual bool init(const comcfg::ConfigUnit& conf);
    virtual void uninit() {
        _gray.reset();
        _uninit();
    }
    virtual bool check(const FeatureManager& feas, bool* hit) const;

protected:
    virtual bool _init(const comcfg::ConfigUnit& conf) = 0;
    virtual void _uninit() = 0;

    typedef anti::themis::common_lib::GrayFileDict GrayFileDict;
    std::shared_ptr<const GrayFileDict> _gray;

    uint64_t _feature_id;

private:
    virtual bool _check(const FeatureValueProto& feas, bool* hit) const = 0;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_RULE_INTERFACE_H

/* vim: set ts=4 sw=4: */

