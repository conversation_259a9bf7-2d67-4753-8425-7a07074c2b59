// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// <AUTHOR>
// @brief

#include "fea_deviation_rule.h"

namespace anti {
namespace themis {
namespace feature_lib {

bool ThresholdBase::init(const comcfg::ConfigUnit& conf) {
    try {
        _default_threshold = conf["threshold"].to_double();
        if (!_init(conf)) {
            CWARNING_LOG("_init conf failed!");
            return false;
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std::exception : %s", e.what());
        return false;
    }
    return true;
}

double ThresholdInflection::get_threshold(const FeatureValueProto& fea) {
    const auto& field = fea.fea_deviation_field();
    return field.devia_inflect_ratio() < _default_threshold && 
            field.devia_inflect_ratio() > _inflection_remain ?
            field.devia_inflect_ratio() : _default_threshold;
}

bool ThresholdInflection::_init(const comcfg::ConfigUnit& conf) {
    _inflection_remain = conf["inflection_remain"].to_double();
    return true;
}

ThresholdBase* ThresholdFactory::create(const std::string& type) {
    ThresholdBase* threshold = NULL;
    if (type == "base") {
        threshold = new (std::nothrow) ThresholdBase();
    } else if (type == "inflection") {
        threshold = new (std::nothrow) ThresholdInflection();
    } else {
        CWARNING_LOG("illegal type[%s]", type.c_str());
        return NULL;
    }
    if (threshold == NULL) {
        CFATAL_LOG("new Threshold failed[%s]!", type.c_str());
        return NULL;
    }
    return threshold;
}

bool FeaDeviationRule::_init(const comcfg::ConfigUnit& conf) {
    try {
        std::string type = "";
        if (conf["threshold_type"].selfType() == comcfg::CONFIG_ERROR_TYPE) {
            type = "base";
        } else {
            type = conf["threshold_type"].to_cstr();
        }
        _threshold.reset(ThresholdFactory::create(type));
        if (!_threshold) {
            CWARNING_LOG("create threshold failed!");
            return false;
        }
        if (!_threshold->init(conf)) {
            CWARNING_LOG("init threshold failed!");
            return false;
        }
        if (conf["value_remain"].selfType() != comcfg::CONFIG_ERROR_TYPE) {
            _value_remain = conf["value_remain"].to_int64();
        }
        if (conf["variance_lower_bound"].selfType() != comcfg::CONFIG_ERROR_TYPE) {
            _variance_lower_bound = conf["variance_lower_bound"].to_double();
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std::exception : %s", e.what());
        return false;
    }
    return true;
}

bool FeaDeviationRule::_check(const FeatureValueProto& fea, bool* hit) const {
    double threshold = _threshold->get_threshold(fea);
    const auto& field = fea.fea_deviation_field();
    if (!field.has_devia_ratio() || !field.has_devia_fea_value()) {
        CWARNING_LOG("fea do not has necessary field");
        return false;
    }
    double variance = DBL_MAX;
    if (field.has_variance()) {
        variance = field.variance();
    }
    *hit = false;
    if (field.devia_ratio() > threshold &&
            field.devia_fea_value() > _value_remain &&
            variance > _variance_lower_bound) {
        *hit = true;
    }
    return true;
}

}
}
}
