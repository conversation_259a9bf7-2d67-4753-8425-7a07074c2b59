// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// @Author: lijie(<EMAIL>)
// 
// @File: threshold_limit_rule.cpp

#include "boost/lexical_cast.hpp"
#include "threshold_limit_rule.h"
#include <com_log.h>

namespace anti {
namespace themis {
namespace feature_lib {

bool ThresholdLimitRule::_init(const comcfg::ConfigUnit& conf) {
    std::string threshold;
    try {
        threshold = conf["threshold"].to_cstr();
        size_t pos = threshold.find(' ');
        if (pos == std::string::npos) {
            _cmp = _greater;
            _threshold = conf["threshold"].to_double();
        } else {
            std::string op = threshold.substr(0, pos);
            if (op == ">") {
                _cmp = _greater;
            } else if (op == "<") {
                _cmp = _less;
            } else if (op == "*>") {
                _cmp = _less;
                _not = true;
            } else if (op == "*<") {
                _cmp = _greater;
                _not = true;
            } else {
                CFATAL_LOG("invalid threshold");
                return false;
            }
            _threshold = boost::lexical_cast<double>(threshold.substr(pos + 1));
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("comcfg::ConfigException : %s", e.what());
        return false;
    } catch (const boost::bad_lexical_cast& e) {
        CWARNING_LOG("boost::bad_lexical_cast exeption : %s", e.what());
        return false;
    }
    return true;
}

bool ThresholdLimitRule::check(const FeatureManager& feas, bool* hit) const {
    if (hit == NULL || _cmp == NULL) {
        CFATAL_LOG("invalid input hit == NULL");
        return false;
    }
    *hit = false;
    auto range = feas.query(_feature_id);
    if (range.first == range.second) {
        return true;
    }

    for (auto iter = range.first; iter != range.second; ++iter) {
        // check fea
        const FeatureValueProto& fea = *(iter->second.get());
        if (!_check(fea, hit)) {
            CWARNING_LOG("call _check fea fail, fea_id(%lu)", fea.feature_id());
            return false;
        }

        if (*hit) {
            break;
        }
    }
    *hit = *hit ^ _not;
    return true;
}

bool ThresholdLimitRule::_check(const FeatureValueProto& fea, bool* hit) const {
    if (!fea.has_valid() || !fea.has_value()) {
        CDEBUG_LOG("invalid fea, feaid(%lu)", fea.feature_id());
        return true;
    }
    // get fea value
    double fea_val = 0.0;
    try {
        fea_val = boost::lexical_cast<double>(fea.value());
    } catch (const boost::bad_lexical_cast& e) {
        CWARNING_LOG("invalid fea value, feaid(%lu)", fea.feature_id());
        return false;
    }
    // hit or not 
    *hit = fea.valid() && _cmp(fea_val, _threshold);
    if (fea.has_in_anomaly_care()) {
        *hit &= fea.in_anomaly_care(); 
    }
    return true;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

