// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @brief 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_TIME_DIFF_RULE_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_TIME_DIFF_RULE_H

#include "rule_interface.h"

namespace anti {
namespace themis {
namespace feature_lib {

class TimeDiffRule : public RuleBase {
public:
    TimeDiffRule();
    virtual ~TimeDiffRule();

private:
    virtual bool _init(const comcfg::ConfigUnit& conf);
    virtual void _uninit();
    virtual bool _check(const FeatureValueProto& feas, bool* hit) const;

    int64_t _threshold;
    int64_t _threshold_lower;
    bool _use_pre;
};

}
}
}

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_TIME_DIFF_RULE_H
