// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: ratio_black_limit_rule.h
// @Last modified: 2015-09-21 16:00:27
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_RATIO_BLACK_LIMIT_RULE_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_RATIO_BLACK_LIMIT_RULE_H

#include "rule_interface.h"

namespace anti {
namespace themis {
namespace feature_lib {

class RatioBlackLimitRule : public RuleBase {
public:
    RatioBlackLimitRule() : _threshold(0.0), _mode(true){}
    virtual ~RatioBlackLimitRule() {
        uninit();
    }

private:
    virtual bool _init(const comcfg::ConfigUnit& conf);
    virtual void _uninit() {}
    virtual bool _check(const FeatureValueProto& feas, bool* hit) const;

    double _threshold;
    // true: filter fit_care, false: filter ref_care
    bool _mode;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti


#endif // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_RATIO_BLACK_LIMIT_RULE_H
/* vim: set ts=4 sw=4: */

