// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: ratio_black_limit_rule.cpp
// @Last modified: 2015-09-22 15:49:07
// @Brief: 

#include "ratio_black_limit_rule.h"

namespace anti {
namespace themis {
namespace feature_lib {

bool RatioBlackLimitRule::_init(const comcfg::ConfigUnit& conf) {
    try {
        _threshold = conf["threshold"].to_double();
        std::string mode(conf["mode"].to_cstr());
        if (mode == "filter") {
            _mode = true;
        } else if (mode == "refer") {
            _mode = false;
        } else {
            CWARNING_LOG("invalid mode:%s", mode.c_str());
            return false;
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("comcfg::ConfigException : %s", e.what());
        return false;
    }
    return true;
}

bool RatioBlackLimitRule::_check(const FeatureValueProto& fea, bool* hit) const {
    if (fea.feature_type() != FeatureValueProto::AUTO_RATIO 
            || !fea.has_in_filter()
            || !fea.has_in_refer()
            || !fea.has_valid()
            || !fea.has_last_ratio()
            || hit == NULL) {
        CFATAL_LOG("invalid fea or intput hit ");
        return false;
    }
    double threshold = _threshold;
    double tmp = 0.0;
    if (_gray && _gray->lookup(fea.view_sign(), &tmp)) {
        // in gray
        threshold = tmp;
    }
    bool fil = fea.valid() && (_mode ? fea.in_filter() : fea.in_refer());
    *hit = fil && fea.last_ratio() > threshold; 
    return true;
}

} // namespace feature_lib
} // namespace themis
} // namespace anti

/* vim: set ts=4 sw=4: */

