// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: distribution_limit_rule.h
// @Last modified: 2016-12-28 14:34:19
// @Brief: 
// @Note: function type max_diff chi_square_test chi_square_dis

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_DISTRIBUTION_LIMIT_RULE_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_DISTRIBUTION_LIMIT_RULE_H

#include <vector>
#include "rule_interface.h"
#include "distribution_distance.h"

namespace anti {
namespace themis {
namespace feature_lib {

class DistributionLimitRule : public RuleBase {
public:
    DistributionLimitRule() : 
            _count_threshold(0L), 
            _threshold(0.0),
            _distance(NULL),
            _distribute_distance(NULL),
            _simple_distribution(false),
            _distribution_distance(NULL) {}
    virtual ~DistributionLimitRule() {
        uninit();
    }

private:
    virtual bool _init(const comcfg::ConfigUnit& conf);
    virtual void _uninit() {
        _stand_prob.clear();
        _distance = NULL;
        if (_distribution_distance) {
            delete _distribution_distance;
            _distribution_distance = NULL;
        }
    }
    virtual bool _check(const FeatureValueProto& feas, bool* hit) const;

    static double _max_diff(
            const std::vector<double>& pb,
            const std::vector<double>& pc,
            uint32_t bucket_idx,
            double threshold);

    static double _chi_square_test(
            const std::vector<double>& pb,
            const std::vector<double>& pc,
            uint32_t bucket_idx,
            double threshold);
    
    static double _chi_square_dis(
            const std::vector<double>& pb,
            const std::vector<double>& pc,
            uint32_t bucket_idx,
            double threshold);

    static double _kl_divergence(
            const std::vector<double>& pb,
            const std::vector<double>& pc,
            uint32_t bucket_idx,
            double threshold);

    static const int B_VALUE = 10000;
    int64_t _count_threshold;
    double _threshold;
    std::vector<double> _stand_prob;

    // @param pb[IN] : standard probability
    // @param pc[IN] : current probability
    // @param bucket_idx[IN] : bucket index
    // @retval: extra_b for bucket index

    // to be deleted
    double (*_distance)(
            const std::vector<double>& pb,
            const std::vector<double>& pc,
            uint32_t bucket_idx,
            double threshold);
    double (*_distribute_distance)(
            const std::vector<double>& pb, 
            const std::vector<double>& pc,
            uint32_t bucket_idx);

    bool _simple_distribution;
    DistributionDistance* _distribution_distance;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_DISTRIBUTION_LIMIT_RULE_H

/* vim: set ts=4 sw=4: */

