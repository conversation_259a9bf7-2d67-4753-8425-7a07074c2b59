// Copyright 2016 Baidu Inc. All Right Reserved.
// Author <PERSON> (<EMAIL>)
// 
// brief: combine_rule.cpp

#include "combine_rule.h"
#include <com_log.h>
#include "rule_factory.h"
#include "boost/lexical_cast.hpp"

namespace anti {
namespace themis {
namespace feature_lib {

bool CombineRule::_init(const comcfg::ConfigUnit& conf) {
    try {
        if (!ThresholdLimitRule::_init(conf)) {
            CWARNING_LOG("ThresholdLimitRule _init failed");
            return false;
        }
        char buffer[1024] = {'\0'};
        char oper = '\0';
        if (conf["expression"].get_cstr(buffer, sizeof(buffer)) != 0) {
            CWARNING_LOG("get expression failed");
            return false;
        }
        if (sscanf(buffer, "%lu %c %lu", &_left_id, &oper, &_right_id) != 3) {
            CWARNING_LOG("invalid expression [%s]", buffer);
            return false;
        }
        switch (oper) {
        case '+' : {
            _cal = CombineRule::_add;
            break;
        }
        case '-' : {
            _cal = CombineRule::_sub;
            break;
        }
        case '*' : {
            _cal = CombineRule::_mul;
            break;
        }
        case '/' : {
            _cal = CombineRule::_div;
            break;
        }
        default : 
            CWARNING_LOG("invalid expression [%s]", buffer);
            return false;
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("comcfg::ConfigException : %s", e.what());
        return false;
    }

    return true;
}

bool CombineRule::check(const FeatureManager& feas, bool* hit) const {
    if (hit == NULL || _cal == NULL) {
        CFATAL_LOG("input hit == NULL or _cal == NULL");
        return false;
    }
    auto left_range = feas.query(_left_id);
    auto right_range = feas.query(_right_id);
    *hit = false;
    if (left_range.first == left_range.second 
            || right_range.first == right_range.second) {
        return true;
    }
    for (auto l = left_range.first; l != left_range.second && !(*hit); ++l) {
        for (auto r = right_range.first; r != right_range.second && !(*hit); ++r) {
            const FeatureValueProto& left_fea = *(l->second.get());
            const FeatureValueProto& right_fea = *(r->second.get());
            if (!_check_threshold(left_fea, right_fea, hit)) {
                CWARNING_LOG("call _check_threshold fail");
                return false;
            }
            *hit = *hit && left_fea.valid() && right_fea.valid();
        }
    }
    *hit = *hit ^ _not;
    return true;
}

bool CombineRule::_check_threshold(
        const FeatureValueProto& left,
        const FeatureValueProto& right,
        bool* hit) const {
    try {
        double left_value = boost::lexical_cast<double>(left.value());
        double right_value = boost::lexical_cast<double>(right.value());
        double result_value = _cal(left_value, right_value);
        *hit =  ThresholdLimitRule::_cmp(result_value, _threshold);
    } catch (const boost::bad_lexical_cast& e) {
        CWARNING_LOG("convert fea fail, fea_id(%lu) value(%s) or fea_id(%lu) value(%s)",
                left.feature_id(), left.value().data(),
                right.feature_id(), right.value().data());
        return false;
    }
    return true;
}

}
}
}
