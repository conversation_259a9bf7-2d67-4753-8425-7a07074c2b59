// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// @Author: lijie(<EMAIL>)
// 
// @File: threshold_limit_rule.h
// @Note: usage:
//          policy.conf
//          threshold : > 10.0 or threshold : < 10.0 any fea_value means threshold
//          threshold : *> 10.0 or threshold : *< 10.0 all the fea_value mean threshold
//          *> == !(<) and *< == !(>)
//          the blank after > or < is needed 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_THRESHOLD_LIMIT_RULE_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_THRESHOLD_LIMIT_RULE_H

#include "rule_interface.h"

namespace anti {
namespace themis {
namespace feature_lib {

class ThresholdLimitRule : public RuleBase {
public:
    ThresholdLimitRule() : _cmp(NULL), _threshold(0.0), _not(false) {}
    virtual ~ThresholdLimitRule() {
        uninit();
    }

    virtual bool check(const FeatureManager& feas, bool* hit) const;

protected:
    static bool _greater(double left, double right) {
        return left > right;
    }
    static bool _less(double left, double right) {
        return left < right;
    }
    virtual bool _init(const comcfg::ConfigUnit& conf);
    virtual void _uninit() {
        _cmp = NULL;
        _threshold = 0;
        _not = false;
    }
    virtual bool _check(const FeatureValueProto& fea, bool* hit) const;

    bool (*_cmp)(double, double);
    double _threshold;
    bool _not;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_THRESHOLD_LIMIT_RULE_H

/* vim: set ts=4 sw=4: */

