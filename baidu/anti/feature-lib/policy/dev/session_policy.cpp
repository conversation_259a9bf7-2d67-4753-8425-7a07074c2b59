#include <gflags/gflags.h>
#include "session_policy.h"
#include "file_dict_manager.h"
#include "pb_reflector.h"

using anti::themis::common_lib::FileDictManagerSingleton;
using anti::themis::common_lib::CnMapValueType;
using anti::themis::common_lib::CnMapFileDict;
using anti::themis::common_lib::CN_MAP_KEY_CN;
using anti::themis::common_lib::PbReflector;
using anti::themis::common_lib::PbPath;
using std::placeholders::_1;
using std::placeholders::_2;
using std::placeholders::_3;

namespace anti {
namespace themis {
namespace feature_lib {

DEFINE_bool(fill_result_to_feature, false, "fill PolicyResultProto to feature");

const static std::string UNKNOWN_TAG = "unknown"; 
const static uint64_t SECOND_TIME_UNIT = 1000UL;

bool Selector::init(const comcfg::ConfigUnit& conf) {
    try {
        // ms
        _session_time = conf["session_time"].to_int64() * SECOND_TIME_UNIT;
        if (conf["log_type"].selfType() != comcfg::CONFIG_ERROR_TYPE) {
            _log_type = conf["log_type"].to_int64();
        }
        if (conf["priority_range"].selfType() != comcfg::CONFIG_ERROR_TYPE
                && sscanf(conf["priority_range"].to_cstr(), "%ld,%ld", 
                        &_prange.min, &_prange.max) != 2) {
            CWARNING_LOG("invalid priority_range %s", conf["priority_range"].to_cstr());
            return false;
        }
        for (uint32_t i = 0; i < conf["select"].size(); ++i) {
            _contrasts.emplace_back(conf["select"][i].to_cstr());
        }
    } catch (const comcfg::ConfigException& ex) {
        CWARNING_LOG("ConfigException:%s", ex.what());
        return false;
    }
    return true;
}

bool Selector::_select_search_id(const FeatureValueProto& fea, std::vector<int>* flag) const {
    if (!flag) {
        CFATAL_LOG("invalid args(%p)", flag);
        return false;
    }
    uint64_t search_id = fea.session().base_action().search_info().search_id();
    for (int i = 0; i < fea.session().actions_size(); ++i) {
        // find the asp log
        if (search_id == fea.session().actions(i).log_id()) {
            for (int j = i + 1; j < fea.session().actions_size(); ++j) {
                (*flag)[j] &= 0;
            }
            return true;
        }
    }
    return true;
}

bool Selector::_select_time(const FeatureValueProto& fea, std::vector<int>* flag) const {
    if (!flag) {
        CFATAL_LOG("invalid args(%p)", flag);
        return false;
    }
    int64_t search_time = fea.session().base_action().search_info().request_time();
    for (int i = 0; i < fea.session().actions_size(); ++i) {
        if (search_time - fea.session().actions(i).log_time() > _session_time
                || search_time < fea.session().actions(i).log_time()) {
            (*flag)[i] &= 0;
        }
    }
    return true;
}

bool Selector::_select_log_type(const FeatureValueProto& fea, std::vector<int>* flag) const {
    if (!flag) {
        CFATAL_LOG("invalid args(%p)", flag);
        return false;
    }
    if (_log_type == -1) {
        return true;
    }
    for (int i = 0; i < fea.session().actions_size(); ++i) {
        if (fea.session().actions(i).log_type() != _log_type) {
            (*flag)[i] &= 0;
        }
    }
    return true;
}

bool Selector::_select_priority_range(const FeatureValueProto& fea, std::vector<int>* flag) const {
    if (!flag) {
        CFATAL_LOG("invalid args(%p)", flag);
        return false;
    }
    for (int i = 0; i < fea.session().actions_size(); ++i) {
        if (!fea.session().actions(i).has_fix()
                || !fea.session().actions(i).fix().has_priority()) {
            continue;
        }
        int64_t priority = fea.session().actions(i).fix().priority();
        if (priority < _prange.min || priority >= _prange.max) {
            (*flag)[i] &= 0;
        }
    }
    return true;
}

bool Selector::_select_field(const FeatureValueProto& fea, std::vector<int>* flag) const {
    if (!flag) {
        CFATAL_LOG("invalid args(%p)", flag);
        return false;
    }
    for (const auto& constrast : _contrasts) {
        std::string base_value;
        if (!_get_field(fea.session().base_action(), constrast, &base_value)) {
            CWARNING_LOG("get base value fail");
            return false;
        }
        for (int i = 0; i < fea.session().actions_size(); ++i) {
            if ((*flag)[i] == 0) {
                continue;
            }
            std::string extend_value;
            if (!_get_field(fea.session().actions(i), constrast, &extend_value)) {
                CWARNING_LOG("get extend value fail");
                return false;
            }
            if (base_value != extend_value) {
                (*flag)[i] &= 0;
            }
        }
    }

    return true;
}

bool Selector::select(const FeatureValueProto& fea, std::vector<ActionProto>* valid_action) const {
    if (!valid_action) {
        CFATAL_LOG("invalid args, valid_action(%s)", valid_action);
        return false;
    }
    if (fea.session().actions_size() == 0) {
        return true;
    }
    std::vector<int> flag(fea.session().actions_size(), 1);
    if (!_select_log_type(fea, &flag) 
            || !_select_search_id(fea, &flag) 
            || !_select_time(fea, &flag)
            || !_select_priority_range(fea, &flag)) {
        CWARNING_LOG("select log type, search id, time or priority_range failed");
        return false;
    }
    if (!_select_field(fea, &flag)) {
        CWARNING_LOG("select field fail");
        return false;
    }
    for (size_t i = 0; i < flag.size(); ++i) {
        if (flag[i] == 1) {
            valid_action->push_back(fea.session().actions(i));
        }
    }
    return true;
}

bool Selector::_get_field(const google::protobuf::Message& msg, const std::string& full_path, std::string* value) const {
    PbPath path;
    if (!path.parse_path(full_path.c_str())) {
        CWARNING_LOG("parse path(%s)", full_path.c_str());
        return false;
    }
    if (!PbReflector::get_field(msg, path, value)) {
        CWARNING_LOG("get value fail, path(%s)", full_path.c_str());
        return false;
    }
    return true;
}

bool SessionPolicyPassThrough::init(const comcfg::ConfigUnit& conf) {
    try {
        _policy_id = conf["policy_id"].to_uint64();
        for (uint32_t i = 0; i < conf["feature_id"].size(); ++i) {
            _feature_id_vec.push_back(conf["feature_id"][i].to_uint64());
        }
        if (conf["copy_info"].selfType() != comcfg::CONFIG_ERROR_TYPE) {
            _copy_info = new (std::nothrow) CopyInfo();
            if (_copy_info == NULL) {
                CFATAL_LOG("create CopyInfo fail");
                return false;
            }
            std::string copy_info = conf["copy_info"].to_cstr();
            size_t pos = copy_info.find(',');
            if (pos == std::string::npos || pos >= copy_info.size() -1) {
                CWARNING_LOG("invalid copy_info(%s)", copy_info.c_str());
                return false;
            }
            _copy_info->src = copy_info.substr(0, pos);
            _copy_info->dst = copy_info.substr(pos + 1);
        }
        return true;
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("comcfg::ConfigException : %s", e.what());
        return false;
    }
}

bool SessionPolicyPassThrough::detect(
        const FeatureManager& fea_mgr,
        PolicyResultProto* result) const {
    if (result == NULL) {
        CFATAL_LOG("input param is NULL");
        return false;
    }
    result->set_policy_id(_policy_id);
    result->set_hit(false);
    if (_copy_info == NULL) {
        return _copy_all(fea_mgr, result);
    } else {
        return _copy_field(fea_mgr, result);
    }
}

bool SessionPolicyPassThrough::_copy_all(const FeatureManager& fea_mgr, PolicyResultProto* result) const {
    if (result == NULL) {
        CFATAL_LOG("result is null");
        return false;
    }
    for (auto feature_id : _feature_id_vec) {
        auto range = fea_mgr.query(feature_id);
        for (auto iter = range.first; iter != range.second; iter++) {
            const FeatureValueProto& fea = *(iter->second.get());
            for (int i = 0; i < fea.fixed_cnt_size(); ++i) {
                result->add_fixed_cnt()->CopyFrom(fea.fixed_cnt(i));
            }
        }
    }
    return true;
}

bool SessionPolicyPassThrough::_copy_field(const FeatureManager& fea_mgr, PolicyResultProto* result) const {
    if (result == NULL) {
        CFATAL_LOG("result is null");
        return false;
    }
    if (_feature_id_vec.empty()) {
        CWARNING_LOG("feature_id_vec is empty");
        return false;
    }
    auto range = fea_mgr.query(_feature_id_vec[0]);
    if (range.first == range.second) {
        return true;
    }
    const FeatureValueProto& fea = *(range.first->second.get());
    std::string field_val;
    if (!PbReflector::get_field(fea, _copy_info->src, &field_val)) {
        CWARNING_LOG("get field from (%s) fail", _copy_info->src.c_str());
        return false;
    }
    if (field_val.empty()) {
        return true;
    }
    if (!PbReflector::set_field_val(result->add_fixed_cnt(), _copy_info->dst, field_val)) {
        CWARNING_LOG("set value(%s) to field(%s) fail", field_val.c_str(), _copy_info->dst.c_str());
        return false;
    }
    return true;
}

bool SessionPolicyBase::init(const comcfg::ConfigUnit& conf) {
    try {
        _policy_id = conf["policy_id"].to_uint64();
        _feature_id = conf["feature_id"].to_uint64();
        const std::string dict = conf["dict"].to_cstr();
        _cn_map = std::static_pointer_cast<const CnMapFileDict>(
                FileDictManagerSingleton::instance().get_file_dict(dict));
        if (!_cn_map) {
            CWARNING_LOG("get dict(%s) fail", dict.c_str());
            return false;
        }   
        if (!_selector.init(conf)) {
            return false;
        }
        if (conf["rule"].size() != 0) {
            _rule = new (std::nothrow) RulePolicy();
            if (_rule == NULL || !_rule->init(conf)) {
                CFATAL_LOG("create or init rule fail");
                return false;
            }
        }
        return _init(conf);
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("comcfg::ConfigException : %s", e.what());
        return false;
    }
}

bool SessionPolicyBase::_care(const FeatureManager& fea_mgr) const {
    // no rule, care all
    if (_rule == NULL) {
        return true;
    }
    PolicyResultProto p;
    if (!_rule->detect(fea_mgr, &p)) {
        CWARNING_LOG("policy(%lu) call _rule detect fail", policy_id());
        return false;
    }
    return p.hit();
}

bool SessionPolicyBase::detect(
        const FeatureManager& fea_mgr,
        PolicyResultProto* result) const {
    if (result == NULL) {
        CFATAL_LOG("input param is NULL");
        return false;
    }
    result->set_policy_id(_policy_id);
    result->set_hit(false);
    if (!_care(fea_mgr)) {
        return true;
    }

    auto range = fea_mgr.query(_feature_id);
    for (auto iter = range.first; iter != range.second; iter++) {
        FeatureValueProto& fea = const_cast<FeatureValueProto&>(*(iter->second.get()));
        if (!is_fea_valid(fea)) {
            CDEBUG_LOG("invalid fea, no need filter"); 
            continue;
        }
        std::vector<ActionProto> valid_actions;
        if (!_selector.select(fea, &valid_actions)) {
            CWARNING_LOG("select feature fail");
            return false;
        }
        if (!_detect(fea, valid_actions, result)) {
            CWARNING_LOG("call _detect fail");
            return false;
        }
        if (FLAGS_fill_result_to_feature && result->fixed_cnt_size() > 0) {
            fea.clear_session();
            for (int i = 0; i < result->fixed_cnt_size(); ++i) {
                fea.add_fixed_cnt()->CopyFrom(result->fixed_cnt(i));
            }
        }
    }
    return true;
}

bool SessionPolicyBase::is_fea_valid(const FeatureValueProto& fea) const {
    do {
        if (!fea.session().has_base_action() || fea.session().actions_size() == 0) {
            break;
        }
        auto& base_action = fea.session().base_action();
        if (!base_action.has_search_info()) {
            break;
        }
        if (base_action.search_info().has_search_id()
                && base_action.search_info().has_request_time()) {
            return true;
        }
    } while (0);
    return false;
}

// when cnt is not found in dict, use cnt as partner;
std::string SessionPolicyBase::_find_partner_tag(const std::string& cnt) const {
    CnMapValueType cn_value;
    if (!_cn_map) {
        CWARNING_LOG("_cn_map is NULL");
        return cnt;
    }
    if (_cn_map->get(cnt, &cn_value, CN_MAP_KEY_CN)) {
        return cn_value.partner_tag;
    } else {
        CDEBUG_LOG("get CnMapValueType fail:%s", cnt.c_str());
        return cnt;
    }
}

// SessionDupTrafficPolicy
bool SessionDupTrafficPolicy::_init(const comcfg::ConfigUnit& conf) {
    try {
        // ms
        _hijack_time = conf["hijack_time"].to_int64() * SECOND_TIME_UNIT;
        if (_hijack_time <= 0) {
            CWARNING_LOG("invalid conf, _hijack_time %d", _hijack_time);
            return false;
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("comcfg::ConfigException : %s", e.what());
        return false;
    }
    return true;
}

bool SessionDupTrafficPolicy::_detect(
        const FeatureValueProto& fea,
        const std::vector<ActionProto>& actions,
        PolicyResultProto* result) const {
    if (result == NULL) {
        CWARNING_LOG("invalid param");
        return false;
    }

    SessionHijackDict hijack_dict;
    for (size_t i = 1; i < actions.size(); i++) {
        const ActionProto& pre_action = actions[i - 1];
        const ActionProto& cur_action = actions[i];
        if (_is_hijack(pre_action, cur_action)) {
            _add_cntname_to_hijack_dict(cur_action.org_cnt(), pre_action, &hijack_dict);
        }
    }

    const ActionProto& base_action = fea.session().base_action();
    _revise_cnt_name(base_action, hijack_dict, result);
    return true;
}

bool SessionDupTrafficPolicy::_is_query_equal(
        const ActionProto& pre,
        const ActionProto& cur) const {
    // if no query_sign in action, treate it not equal
    if (!pre.has_dt() || !pre.dt().has_query_sign()
            || !cur.has_dt() || !cur.dt().has_query_sign()) {
        CWARNING_LOG("action has no query_sign");
        return false;
    }
    return pre.dt().query_sign() == cur.dt().query_sign();
}

bool SessionDupTrafficPolicy::_is_hijack(
        const ActionProto& pre_feature,
        const ActionProto& cur_feature) const {
    if (!_is_query_equal(pre_feature, cur_feature)) {
        return false;
    }
    int64_t pre_time = pre_feature.log_time();
    int64_t cur_time = cur_feature.log_time();
    if (cur_time <= pre_time) {
        CDEBUG_LOG("cur time[%lu] le pre time[%lu]", cur_time, pre_time);
        return false;
    }
    int64_t cur_interval = (cur_time - pre_time);
    if (cur_interval >= _hijack_time) {
        return false;
    } 
    const std::string& pre_cntname = pre_feature.org_cnt();
    const std::string& cur_cntname = cur_feature.org_cnt();
    if (pre_cntname == cur_cntname) {
        return false;
    }
    std::string pre_tag = _find_partner_tag(pre_cntname);
    std::string cur_tag = _find_partner_tag(cur_cntname);
    if (pre_tag == cur_tag) {
        return false;
    }
    return true;
}

bool SessionDupTrafficPolicy::_add_cntname_to_hijack_dict(
        const std::string& hijack_cntname,
        const ActionProto& ori_hijacked,
        SessionHijackDict* hijack_dict) const {
    if (hijack_dict == NULL) {
        CFATAL_LOG("hijack dict is NULL");
        return false;
    }
    const ActionProto* first_hijacked = &ori_hijacked;
    const std::string& ori_cntname = ori_hijacked.org_cnt();
    SessionHijackDict::iterator iter = hijack_dict->find(ori_cntname);
    if (iter != hijack_dict->end()) {
        first_hijacked = iter->second;
    }

    const std::string& first_hijack_cntname = first_hijacked->org_cnt();

    if (hijack_cntname != first_hijack_cntname) {
        SessionHijackDict::iterator iter = hijack_dict->find(hijack_cntname);
        if (iter == hijack_dict->end() || 
                iter->second->org_cnt() != first_hijack_cntname ) {
            (*hijack_dict)[hijack_cntname] = first_hijacked;
        }
    }
    return true;
}

bool SessionDupTrafficPolicy::_revise_cnt_name(
        const ActionProto& base_action,
        const SessionHijackDict& hijack_dict,
        PolicyResultProto* result) const {
    if (result == NULL) {
        CWARNING_LOG("invalid param");
        return false;
    }

    const std::string& base_cnt = base_action.org_cnt();
    SessionHijackDict::const_iterator hijack_iter = hijack_dict.find(base_cnt);
    if (hijack_iter == hijack_dict.end()) {
        CDEBUG_LOG("%s has no hijack", base_cnt.c_str());
        return true;
    }
    std::string base_partner_tag = _find_partner_tag(base_cnt);
    std::string fixed_partner_tag = _find_partner_tag(hijack_iter->second->org_cnt());

    if (base_partner_tag != fixed_partner_tag) {
        result->add_fixed_cnt()->set_cnt_name(hijack_iter->second->org_cnt());
        result->mutable_fixed_cnt(0)->set_priority(hijack_iter->second->log_time());
    }
    return true;
}
SessionDirectPolicy::SessionDirectPolicy() : _min_priority(-1) {
    _fix_func = std::bind(&SessionDirectPolicy::_fix_cnt, this, _1, _2, _3);
}

bool SessionDirectPolicy::_init(const comcfg::ConfigUnit& conf) {
    try {
        if (conf["min_priority"].selfType() != comcfg::CONFIG_ERROR_TYPE) {
            _min_priority = conf["min_priority"].to_int64();
        }
        if (conf["fix_type"].selfType() != comcfg::CONFIG_ERROR_TYPE) {
            std::string fix_type = conf["fix_type"].to_cstr();
            if (fix_type == "cnt") {
                _fix_func = std::bind(&SessionDirectPolicy::_fix_cnt, this, _1, _2, _3);
            } else if (fix_type == "group") {
                _fix_func = std::bind(&SessionDirectPolicy::_fix_group, this, _1, _2, _3);
            } else {
                CWARNING_LOG("invalid fix_type:%s", fix_type.c_str());
                return false;
            }
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("comcfg::ConfigException : %s", e.what());
        return false;
    }
    return true;
}
bool SessionDirectPolicy::_detect(
        const FeatureValueProto& fea,
        const std::vector<ActionProto>& actions,
        PolicyResultProto* result) const {
    if (NULL == result) {
        CFATAL_LOG("param error, result:%p", result);
        return false;
    }
    return _fix_func(fea, actions, result);
}

bool SessionDirectPolicy::_fix_cnt(
        const FeatureValueProto& fea,
        const std::vector<ActionProto>& actions,
        PolicyResultProto* result) const {
    for (size_t i = 0; i < actions.size(); ++i) {
        if (!actions[i].has_fix() || !actions[i].fix().has_fix_cnt() || !actions[i].has_org_cnt()){
            continue;
        }
        // fix_cnt is the same with org_cnt, no need fix
        if (actions[i].fix().fix_cnt() == actions[i].org_cnt()) {
            continue;
        }
        auto* fixed_cnt = result->add_fixed_cnt();
        fixed_cnt->set_cnt_name(actions[i].fix().fix_cnt());
        if (actions[i].has_search_info() && actions[i].search_info().policy_list_size() > 0) {
            fixed_cnt->set_policy_id(atoi(actions[i].search_info().policy_list(0).c_str()));
        }
        int64_t priority = actions[i].log_time();
        // if action's priority is less than min_priority, use logtime
        if (actions[i].fix().has_priority() && actions[i].fix().priority() > _min_priority) {
            priority = actions[i].fix().priority();
        }
        result->mutable_fixed_cnt(0)->set_priority(priority);
        break;
    }
    return true;
}

bool SessionDirectPolicy::_fix_group(
        const FeatureValueProto& fea,
        const std::vector<ActionProto>& actions,
        PolicyResultProto* result) const {
    for (size_t i = 0; i < actions.size(); ++i) {
        if (!actions[i].has_fix() || !actions[i].fix().has_fix_group()){
            continue;
        }
        result->add_fixed_cnt()->set_fix_group(actions[i].fix().fix_group());
        result->mutable_fixed_cnt(0)->set_priority(actions[i].fix().priority());
        break;
    }
    return true;
}

} // end namespace feature_lib
} // end namespace themis
} // end namespace anti
