// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @brief 

#include "time_diff_rule.h"

namespace anti {
namespace themis {
namespace feature_lib {

TimeDiffRule::TimeDiffRule() : _threshold(0), _threshold_lower(-1) {}
TimeDiffRule::~TimeDiffRule() { _uninit(); }

bool TimeDiffRule::_init(const comcfg::ConfigUnit& conf) {
    try {
        _threshold = conf["threshold"].to_int64();
        if (conf["threshold_lower"].selfType() == comcfg::CONFIG_ERROR_TYPE) {
            CDEBUG_LOG("no threshold_lower option! use the default value %ld", 
                    _threshold_lower);
        } else {
            _threshold_lower = conf["threshold_lower"].to_int64();
            CDEBUG_LOG("load conf, [_threshold_lower:%ld]", _threshold_lower);
        }

        if (conf["use_pre"].selfType() == comcfg::CONFIG_ERROR_TYPE) {
            _use_pre = false;
            CDEBUG_LOG("no use_pre option! use the default value %ld", 
                    _use_pre);
        } else {
            _use_pre = conf["use_pre"].to_int64() == 0 ? false : true;
            CDEBUG_LOG("load conf, [_use_pre:%ld]", _use_pre);
        }

    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("comcfg::ConfigException : %s", e.what());
        return false;
    }
    return true;
}

void TimeDiffRule::_uninit() {}

bool TimeDiffRule::_check(const FeatureValueProto& fea, bool* hit) const {
    if ((fea.feature_type() != FeatureValueProto::TIME_DIFF && 
            fea.feature_type() != FeatureValueProto::DATAVIEW_TIME_DIFF)
            || !fea.has_pre_feat_distance()
            || hit == NULL) {
        CFATAL_LOG("invalid fea. [fea:%s]", fea.ShortDebugString().data());
        return false;
    }

    *hit = false;

    // kept for compatibility
    if (fea.pre_feat_distance() <= _threshold && 
            fea.pre_feat_distance() > _threshold_lower) {
        *hit = true;
    }

    if(!_use_pre) {
        for (auto dist : fea.neighbor_feat_distances()) {
            if (dist <= _threshold && dist > _threshold_lower) {
                *hit = true;
                break;
            }
        }
    }

    return true;
}

}
}
}
