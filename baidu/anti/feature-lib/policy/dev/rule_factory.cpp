// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangji<PERSON><PERSON>(<EMAIL>)
// 
// @File: rule_factory.cpp
// @Last modified: 2017-01-09 13:33:32
// @Brief: 

#include "rule_factory.h"
#include <com_log.h>
#include "segment_limit_rule.h"
#include "ratio_limit_rule.h"
#include "distribution_limit_rule.h"
#include "boolean_rule.h"
#include "ratio_black_limit_rule.h"
#include "time_diff_rule.h"
#include "acp_limit_rule.h"
#include "threshold_limit_rule.h"
#include "combine_rule.h"
#include "fea_deviation_rule.h"
#include "session_prob_rule.h"
#include "significance_ratio_rule.h"
#include "significance_distribution_rule.h"

namespace anti {
namespace themis {
namespace feature_lib {

RuleInterface* RuleFactory::create(const std::string& type) {
    RuleInterface* rule = NULL;
    if (type == "segment" || type == "auto_segment"
            || type == "multi_seg" || type == "behavior_segment") {
        rule = new(std::nothrow) SegmentLimitRule();
    } else if (type == "ratio" || type == "concentration" 
            || type == "behavior_ratio") {
        rule = new(std::nothrow) RatioLimitRule();
    } else if (type == "distribution" || type == "count_distribution"
            || type == "distinct_distribution" || type == "behavior_distribution"
            || type == "rate_distribution" || type == "cpm_distribution") {
        rule = new(std::nothrow) DistributionLimitRule();
    } else if (type == "boolean") {
        rule = new(std::nothrow) BooleanRule();
    } else if (type == "auto_ratio") {
        rule = new(std::nothrow) RatioBlackLimitRule();
    } else if (type == "timediff" || type == "dataview_timediff") {
        rule = new(std::nothrow) TimeDiffRule();
    } else if (type == "acp") {
        rule = new(std::nothrow) AcpLimitRule();
    } else if (type == "threshold") {
        rule = new(std::nothrow) ThresholdLimitRule();
    } else if (type == "combine") {
        rule = new(std::nothrow) CombineRule();
    } else if (type == "fea_deviation") {
        rule = new(std::nothrow) FeaDeviationRule();
    } else if (type == "session_prob") {
        rule = new(std::nothrow) SessionProbRule();
    } else if (type == "significance_ratio") {
        rule = new(std::nothrow) SignificanceRatioRule();
    } else if (type == "significance_distribution") {
        rule = new(std::nothrow) SignificanceDistributionRule();
    } else {
        CWARNING_LOG("invalid rule type(%s)", type.data());
        return NULL;
    }

    if (rule == NULL) {
        CWARNING_LOG("new rule type(%s) fail", type.data());
        return NULL;
    }
    return rule;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

