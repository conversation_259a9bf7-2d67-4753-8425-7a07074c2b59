// Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
// @file significance_distribution_rule.cpp
// <AUTHOR>
// @date 2019/01/09 14:23:23
// @brief

#include <float.h>
#include <math.h>
#include "significance_distribution_rule.h"
#include <boost/lexical_cast.hpp>
#include <boost/algorithm/string.hpp>
#include <com_log.h>
#include "feature_util.h"

namespace anti {
namespace themis {
namespace feature_lib {

bool SignificanceDistributionRule::_init(const comcfg::ConfigUnit& conf) {
    std::vector<double> sigmas;
    std::vector<double> mus;
    double significance = 0.0;
    try {
        std::string mus_str(conf["mu"].to_cstr());
        std::vector<std::string> mus_strs;
        boost::algorithm::split(
                mus_strs, mus_str, boost::is_any_of(", "), boost::token_compress_on);
        for (uint32_t i = 0U; i < mus_strs.size(); ++i) {
            double mu = boost::lexical_cast<double>(mus_strs[i]);
            if (mu < 0.0) {
                CWARNING_LOG("mu(%u) is less than zero", i);
                return false;
            }
            mus.push_back(mu);
        }

        std::string sigmas_str(conf["sigma"].to_cstr());
        std::vector<std::string> sigmas_strs;
        boost::algorithm::split(
                sigmas_strs, sigmas_str, boost::is_any_of(", "), boost::token_compress_on);
        for (uint32_t i = 0U; i < sigmas_strs.size(); ++i) {
            double sigma = boost::lexical_cast<double>(sigmas_strs[i]);
            if (sigma < 0.0) {
                CWARNING_LOG("sigma(%u) is less than zero", i);
                return false;
            }
            sigmas.push_back(sigma);
        }

        if (sigmas.size() != mus.size()){
            CWARNING_LOG("mu size(%u) is unequal to sigma size(%u)", mus.size(), sigmas.size());
            return false;
        }

        if (!analyze_significance(conf["significance"].to_cstr(),
                    &significance)) {
            CWARNING_LOG("analyze_significance from conf[%s] failed!",
                    conf["significance"].to_cstr());
            return false;
        }
        _threshold_vec.resize(mus.size());
        for (uint32_t i = 0U; i < sigmas_strs.size(); ++i){
            CDEBUG_LOG("get threshold vec failed![mu=%.f,sigma=%.f,sgnf=%.f]",
                    mus[i], sigmas[i], significance);
            if (!get_binomial_cnk_prob(mus[i], sigmas[i], significance, &_threshold_vec[i])) {
                CWARNING_LOG("get threshold vec failed![mu=%.f,sigma=%.f,sgnf=%.f]",
                             mus[i], sigmas[i], significance);
                return false;
            }
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("comcfg::ConfigException : %s", e.what());
        return false;
    } catch (const boost::bad_lexical_cast& e) {
        CWARNING_LOG("boost::bad_lexical_cast : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std::exception: %s", e.what());
        return false;
    }
    return true;
}

bool SignificanceDistributionRule::_check(const FeatureValueProto& fea, bool* hit) const {
    if ((fea.feature_type() != FeatureValueProto::DISTRIBUTION 
            && fea.feature_type() != FeatureValueProto::COUNT_DISTRIBUTION
            && fea.feature_type() != FeatureValueProto::DISTINCT_DISTRIBUTION
            && fea.feature_type() != FeatureValueProto::BEHAVIOR_DISTRIBUTION
            && fea.feature_type() != FeatureValueProto::RATE_DISTRIBUTION
            && fea.feature_type() != FeatureValueProto::CPM_DISTRIBUTION)
            || hit == NULL) {
        CDEBUG_LOG("invalid fea type or input hit == NULL, feaid(%lu)", fea.feature_id());
        return true;
    }

    if (!fea.has_bucket_idx() || fea.bucket_idx() >= _threshold_vec.size()) {
        CDEBUG_LOG("no bucket idx or bucket idx too big, feaid(%lu)", fea.feature_id());
        return true;
    }

    int64_t cur_count = 0L;
    for (int32_t i = 0; i < fea.buckets_size(); ++i) {
        if (fea.buckets(i).idx() >= _threshold_vec.size()) {
            CWARNING_LOG("invalid bucket idx(%u) >= _stand_prob.size(%u)", 
                    fea.buckets(i).idx(), _threshold_vec.size());
            return false;
        }
        cur_count += fea.buckets(i).count();
    }

    uint32_t cur_idx = fea.bucket_idx();
    const auto& cur_bucket_thresholds = _threshold_vec[cur_idx];
    uint32_t threshold_size = cur_bucket_thresholds.size() - 1;

    uint32_t threshold = cur_count <= static_cast<int64_t>(threshold_size) ? 
            cur_bucket_thresholds[cur_count] : 
            (1.0 * cur_bucket_thresholds[threshold_size]) / threshold_size * cur_count;
    
    *hit = fea.buckets(cur_idx).count() > threshold;
    return true;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

