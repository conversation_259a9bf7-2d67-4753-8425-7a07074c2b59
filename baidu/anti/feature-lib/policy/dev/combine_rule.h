// Copyright 2016 Baidu Inc. All Right Reserved.
// Author <PERSON> (<EMAIL>)
// 
// brief: combine_rule.h

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_COMBINE_RULE_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_COMBINE_RULE_H

#include <float.h>
#include "rule_interface.h"
#include "threshold_limit_rule.h"

namespace anti {
namespace themis {
namespace feature_lib {

class CombineRule : public ThresholdLimitRule {
public:
    CombineRule() : 
        _left_id(0LU), 
        _right_id(0LU), 
        _cal(NULL) {}
    virtual ~CombineRule() {
        uninit();
    }

    bool check(const FeatureManager& feas, bool* hit) const;

private:
    virtual bool _init(const comcfg::ConfigUnit& conf);
    virtual void _uninit() {
        _cal = NULL;
        _left_id = 0LU;
        _right_id = 0LU;
        ThresholdLimitRule::_uninit();
    }
    virtual bool _check(const FeatureValueProto& /*feas*/, bool* /*hit*/) const {
        return true;
    };
    bool _check_threshold(
            const FeatureValueProto& left,
            const FeatureValueProto& right,
            bool* hit) const;

    static double _add(const double left, const double right) {
        return left + right;
    }

    static double _sub(const double left, const double right) {
        return left - right;
    }

    static double _mul(const double left, const double right) {
        return left * right;
    }

    static double _div(const double left, const double right) {
        if (fabs(right) < FLT_EPSILON) {
            return DBL_MAX;
        } else {
            return left / right;
        }
    }

    uint64_t _left_id;
    uint64_t _right_id;
    double (*_cal)(const double, const double);
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_COMBINE_RULE_H
