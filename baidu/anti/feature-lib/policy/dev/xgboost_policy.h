// Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
// @Author: xiarenjie
//
// @File: xgboost_policy.h
// @Brief: XGBOOST 模型预测类

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_XGB_POLICY_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_XGB_POLICY_H

#include <unordered_map>
#include "model_policy.h"
#include "xgboost/c_api.h"

namespace anti {
namespace themis {
namespace feature_lib {

class XGBPolicy : public ModelPolicy {
public:
    XGBPolicy();
    virtual ~XGBPolicy();

    virtual bool load_model(const std::string& conf_path, const std::string& conf_file);
    virtual bool predict(const FeaVecMap& fea_vec, PolicyResultProto* result) const;

private:
    mutable uint64_t _joinkey;

    // 根据slot从0到_fea_num获取特征值，填充_fea_list中
    bool _get_fea_list(const FeaVecMap& fea_vec, float* fea_list) const;
    
    // 根据slot获取默认值
    virtual bool _normalize(
            const FeatureValueProto* fea,
            uint32_t slot,
            const std::string& value,
            std::string* normalized) const;
    
    // XGBOOST 模型句柄
    BoosterHandle _h_booster;
    
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_XGB_POLICY_H

/* vim: set ts=4 sw=4: */
