// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// @Author: wangkai<PERSON>(<EMAIL>)
// 
// @Last modified: 2017-06-07 12:08:30

#include <float.h>
#include <math.h>
#include <boost/lexical_cast.hpp>
#include <file_dict_manager.h>
#include <sign_util.h>
#include "session_prob_rule.h"

using anti::themis::common_lib::FileDictManager;
using anti::themis::common_lib::FileDictManagerSingleton;

namespace anti {
namespace themis {
namespace feature_lib {

SessionProbRule::SessionProbRule() :
    _threshold(0.0) {}

bool SessionProbRule::_init(const comcfg::ConfigUnit& conf) {
    std::string prob_file;
    try {
        _threshold = conf["threshold"].to_double();
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("comcfg::ConfigException : %s", e.what());
        return false;
    }
    if (!_init_prob_file(conf)) {
        CWARNING_LOG("init prob file failed");
        return false;
    }
    return true;

}

bool SessionProbRule::_init_prob_file(const comcfg::ConfigUnit& conf) {
    std::string prob_file;
    try {
        prob_file = conf["prob_file"].to_cstr();
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("comcfg::ConfigException : %s", e.what());
        return false;
    }
    FileDictManager::ConstDictPtr fd =
                    FileDictManagerSingleton::instance().get_file_dict(prob_file);
    if (!fd) {
        CWARNING_LOG("call get_file_dict fail, file_key(%s)", prob_file.c_str());
        return false;
    }
    _prob_dict = std::dynamic_pointer_cast<const GrayFileDict>(fd);
    if (!_prob_dict) {
        CWARNING_LOG("convert file_key(%s) to GrayFileDict fail", prob_file.c_str());
        return false;
    }

    return true;
}

bool SessionProbRule::_check(const FeatureValueProto& fea, bool* hit) const {
    if (fea.feature_type() != FeatureValueProto::SESSION || hit == NULL) {
        CWARNING_LOG("invalid feature_type %d or input hit == NULL", fea.feature_type());
        return false;
    }

    *hit = false;

    if (!_prob_dict) {
        CFATAL_LOG("prob file is not config, please check");
        return false;
    }

    if (!fea.has_session()
        || fea.session().actions_size() == 0
        || !fea.session().actions(0).has_search_info()
        || fea.session().actions(0).search_info().policy_list_size() == 0) {
        return true;
    }

    auto session = fea.session();

    if (session.actions_size() > 1) {
        CWARNING_LOG("actions size[%d] is greater than 1,only use first fea", 
                session.actions_size());
    }

    int32_t valid_hit_size = session.actions(0).search_info().policy_list_size();
    double prob = 0.0;
    if (!_cal_policy_prob(session.actions(0).search_info(), &prob, &valid_hit_size)) {
        CWARNING_LOG("cal policy prob failed");
        return false;
    }

    double threshold = _get_threshold(valid_hit_size);

    *hit = prob > threshold;

    return true;
}

double SessionProbRule::_get_threshold(int32_t policy_id_size) const {
    double threshold = _threshold;
    if (!_gray) {
        return threshold;
    }
    uint64_t sign = 0;
    anti::baselib::SignUtil::create_sign_md64(
            boost::lexical_cast<std::string>(policy_id_size), &sign);
    double policy_threshold = 0.0;
    if (_gray->lookup(sign, &policy_threshold)) {
        threshold = policy_threshold;
    }

    return threshold;
}

bool SessionProbRule::_cal_policy_prob(
        const ActionProto::SearchInfoProto& search_info,
        double* prob,
        int32_t* valid_hit) const {
    if (prob == NULL || valid_hit == NULL) {
        CWARNING_LOG("param is NULL");
        return false;
    }
    *prob = 0;
    *valid_hit = 0;
    double tmp_prob = 1.0;
    for (int32_t i = 0; i < search_info.policy_list_size(); i++) {
        uint64_t sign = 0;
        anti::baselib::SignUtil::create_sign_md64(search_info.policy_list(i), &sign);
        double policy_threshold = 0.0;
        if (!_prob_dict->lookup(sign, &policy_threshold)) {
            continue;
        }

        tmp_prob *= 1 - policy_threshold;
        ++*valid_hit;
    }

    *prob = 1 - tmp_prob;
    return true;
}

} // end namespace feature_lib
} // end namespace themis
} // end namespace anti
