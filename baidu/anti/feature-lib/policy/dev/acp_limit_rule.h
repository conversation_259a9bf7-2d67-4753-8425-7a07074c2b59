// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// @Author: lijie(lijie<PERSON>@baidu.com)
// 
// @File: acp_limit_rule.h

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_ACP_LIMIT_RULE_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_ACP_LIMIT_RULE_H

#include "rule_interface.h"

namespace anti {
namespace themis {
namespace feature_lib {

class AcpLimitRule : public RuleBase {
public:
    AcpLimitRule() : _threshold(0.0) {}
    virtual ~AcpLimitRule() {
        uninit();
    }

private:
    virtual bool _init(const comcfg::ConfigUnit& conf);
    virtual void _uninit() {}
    virtual bool _check(const FeatureValueProto& feas, bool* hit) const;

    double _threshold;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_ACP_LIMIT_RULE_H

/* vim: set ts=4 sw=4: */

