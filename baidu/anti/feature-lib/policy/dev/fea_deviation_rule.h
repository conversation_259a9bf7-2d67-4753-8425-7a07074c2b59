// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// <AUTHOR>
// @brief

#ifndef ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_FEA_DEVIATION_RULE_H
#define ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_FEA_DEVIATION_RULE_H

#include <limits.h>
#include <float.h>
#include "rule_interface.h"

namespace anti {
namespace themis {
namespace feature_lib {

class ThresholdBase {
public:
    ThresholdBase() : _default_threshold(0.00000001) {}
    virtual ~ThresholdBase() {}

    bool init(const comcfg::ConfigUnit& conf);
    virtual double get_threshold(const FeatureValueProto& /*fea*/) {
        return _default_threshold;
    }
private:
    virtual bool _init(const comcfg::ConfigUnit& conf) { return true; }
protected:
    double _default_threshold;
};

class ThresholdInflection : public ThresholdBase {
public:
    ThresholdInflection() : _inflection_remain(0.000000001) {}
    virtual ~ThresholdInflection() {}
    virtual double get_threshold(const FeatureValueProto& fea);
private:
    virtual bool _init(const comcfg::ConfigUnit& conf);
    double _inflection_remain;
};

class ThresholdFactory {
public:
    static ThresholdBase* create(const std::string& type);
};

class FeaDeviationRule : public RuleBase {
public:
    FeaDeviationRule() : _value_remain(-INT64_MAX), _variance_lower_bound(-DBL_MAX) {}
    virtual ~FeaDeviationRule() {}

protected:
    virtual bool _init(const comcfg::ConfigUnit& conf);
    virtual void _uninit() {}

private:
    virtual bool _check(const FeatureValueProto& fea, bool* hit) const;

    std::shared_ptr<ThresholdBase> _threshold;
    int64_t _value_remain;
    double _variance_lower_bound;
};
}
}
}
#endif // ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_FEA_DEVIATION_RULE_H
