// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: policy_manager.cpp
// @Last modified: 2018-03-23 16:05:02
// @Brief: 

#include <algorithm>
#include "boost/algorithm/string.hpp"
#include "policy_manager.h"
#include <com_log.h>
#include "policy_factory.h"
#include "feature_util.h"
#include <set>

namespace anti {
namespace themis {
namespace feature_lib {

typedef std::shared_ptr<FeatureValueProto> FeaPtr;

bool PolicyManager::init(
        const std::string& conf_path, 
        const std::string& conf_file) {
    comcfg::Configure conf;
    if (conf.load(conf_path.data(), conf_file.data()) != 0) {
        CFATAL_LOG("load %s/%s fail", conf_path.data(), conf_file.data());
        return false;
    }

    try {
        std::vector<std::string> policy_list;
        if (conf["policy_list"].selfType() != comcfg::CONFIG_ERROR_TYPE 
                && !parse_list(std::string(conf["policy_list"].to_cstr()), &policy_list)) {
            CFATAL_LOG("call parse_list(%s) fail", conf["policy_list"].to_cstr());
            return false;
        }
        
        if (conf["pol_list"].selfType() != comcfg::CONFIG_ERROR_TYPE 
                && !parse_list(conf, std::string("pol_list"), &policy_list)) {
            CFATAL_LOG("call parse_list(pol_list) fail");
            return false;
        }

        if (policy_list.size() == 0) {
            CFATAL_LOG("no policy_list or pol_list in conf");
            return false;
        }
        typedef std::set<std::string> UniqPolicySet;
        UniqPolicySet uniq_policy_set; 
        for (uint32_t i = 0; i < conf["policy"].size(); ++i) {
            // check policy_id
            std::string policy_id(conf["policy"][i]["policy_id"].to_cstr());
            std::vector<std::string>::iterator policy_ite = std::find(
                    policy_list.begin(), 
                    policy_list.end(),
                    policy_id);
            if (policy_ite == policy_list.end()) {
                CWARNING_LOG("policy[%s] is not in policy_list or is already exist", 
                        policy_id.c_str());
                continue;
            }
            //unique policy 
            if (uniq_policy_set.find(policy_id) != uniq_policy_set.end()) {
                CFATAL_LOG("policy[%s] has been defined", policy_id.c_str());
                continue;
            }
            uniq_policy_set.insert(policy_id);
            // init policy
            std::string type(conf["policy"][i]["policy_type"].to_cstr());
            PolicyInterface* policy = PolicyFactory::create(type);
            if (policy == NULL || !policy->init(conf["policy"][i])) {
                CFATAL_LOG("new or init policy_type(%s) fail", type.data());
                if (policy != NULL) {
                    delete policy;
                    policy = NULL;
                }
                return false;
            }
            _policies.push_back(policy);
        }
    } catch (const comcfg::ConfigException& e) {
            CWARNING_LOG("comcfg::ConfigException : %s", e.what());
            return false;
    }
    if (!_init_exps(conf)) {
        CFATAL_LOG("init exp in policy manager failed!");
        return false;
    }
    CWARNING_LOG("policy manager init success");
    return true;
}

bool PolicyManager::_init_exps(const comcfg::ConfigUnit& conf) {
    if (conf["fea_list"].selfType() == comcfg::CONFIG_ERROR_TYPE) {
        return true;
    }
    std::vector<std::string> atoms;
    std::vector<std::string> exps;
    if (!parse_list(std::string(conf["fea_list"].to_cstr()), &atoms)) {
        CFATAL_LOG("fea_list parse failed!");
        return false;
    }
    for (uint32_t i = 0; i < conf["exp"].size(); ++i) {
        exps.push_back(std::string(conf["exp"][i].to_cstr()));
    }
    return _exp.init(exps, atoms);
}

void PolicyManager::uninit() {
    for (uint32_t i = 0; i < _policies.size(); ++i) {
        if (_policies[i] != NULL) {
            _policies[i]->uninit();
            delete _policies[i];
            _policies[i] = NULL;
        }
    }
    _policies.clear();
}

bool PolicyManager::_calculate_exps(FeatureManager* feas_manager, Exp* exp) {
    if (feas_manager == NULL || exp == NULL) {
        CFATAL_LOG("invalid input!");
        return false;
    }
    std::vector<std::pair<std::string, double>> fea_value_set;
    for (const auto& atom : exp->atoms()) {
        uint64_t feaid = 0LU;
        if (sscanf(atom.c_str(), "fea(%lu)", &feaid) != 1) {
            CWARNING_LOG("invalid atom[%s], skip", atom.c_str());
            continue;
        }
        std::vector<FeaPtr> feas;
        if (!feas_manager->query(feaid, &feas)
                || feas.size() != 1) {
            CWARNING_LOG("feaid[%lu] is a multi value fea," 
                    "value doesn't uniq", feaid);
            continue;
        }
        double fea_value = 0.0;
        if (!get_fea_double_value(*feas[0], &fea_value)) {
            CWARNING_LOG("get fea[%lu] value failed!", feaid);
            continue;
        }
        fea_value_set.push_back(std::make_pair(atom, fea_value));
    }
    exp->reset(fea_value_set);
    for (const auto& x : exp->unknown()) {
        double x_res = 0.0;
        if (!exp->calculate(x, &x_res)) {
            CWARNING_LOG("[%s] calculate failed!");
            continue;
        }
        uint64_t feaid = 0LU;
        if (sscanf(x.c_str(), "fea(%lu)", &feaid) != 1) {
            CWARNING_LOG("invalid atom[%s], skip", x.c_str());
            continue;
        }
        std::shared_ptr<FeatureValueProto> fea_ptr(new FeatureValueProto());
        fea_ptr->set_feature_id(feaid);
        fea_ptr->set_value(std::to_string(x_res));
        fea_ptr->set_valid(true);
        if (!feas_manager->insert(fea_ptr)) {
            CWARNING_LOG("invalid fea_ptr!");
            return false;
        }
    }
    return true;

}

bool PolicyManager::detect(
        FeatureManager* feas_manager,
        std::vector<PolicyResultProto>* results) {
    if (feas_manager == NULL || results == NULL) {
        CFATAL_LOG("invalid input!");
        return false;
    }
    if (!_calculate_exps(feas_manager, &_exp)) {
        CWARNING_LOG("calculate exps failed!");
        return false;
    }
    return _detect(*feas_manager, results);
}

bool PolicyManager::_detect(
        const FeatureManager& feas, 
        std::vector<PolicyResultProto>* results) const {
    if (results == NULL) {
        CFATAL_LOG("input results == NULL");
        return false;
    }
    for (uint32_t i = 0; i < _policies.size(); ++i) {
        if (_policies[i] == NULL) {
            CFATAL_LOG("policy_idx(%u) is NULL", i);
            return false;
        }

        PolicyResultProto result;
        result.set_hit(false);
        if (!_policies[i]->detect(feas, &result)) {
            // one policy detect fail, only continue(not return false)
            CWARNING_LOG("call policy(%lu) detect fail", _policies[i]->policy_id());
            continue;
        }

        // rule policy only pass hit to judge
        // model policy pass all result to judge no matter hit or not
        if (result.hit() || result.has_value() ||
                (result.has_pass_through() && result.pass_through()) ||
                result.fixed_cnt_size() > 0) {
            results->push_back(result);
        }
    }
    return true;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

