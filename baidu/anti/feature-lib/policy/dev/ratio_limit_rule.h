// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: ratio_limit_rule.h
// @Last modified: 2017-12-26 15:59:03
// @Brief: filter - refer * threshold > remain

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_RATIO_LIMIT_RULE_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_RATIO_LIMIT_RULE_H

#include "rule_interface.h"
#include "threshold_limit_rule.h"

namespace anti {
namespace themis {
namespace feature_lib {

class RatioLimitRule : public ThresholdLimitRule {
public:
    RatioLimitRule() : _remain(0L), _simple_ratio(false), _filter_mode(false) {}
    virtual ~RatioLimitRule() {
        uninit();
    }

protected:
    virtual bool _init(const comcfg::ConfigUnit& conf);
    virtual void _uninit() {
        ThresholdLimitRule::_uninit();
    }

private:
    virtual bool _check(const FeatureValueProto& feas, bool* hit) const;

    int64_t _remain;
    bool _simple_ratio;
    // @brief: filter switcher
    // _filter_mode = false [default], filter numerator,
    // _filter_mode = true, filter denominator
    bool _filter_mode;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_RATIO_LIMIT_RULE_H

/* vim: set ts=4 sw=4: */

