// Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
// <AUTHOR>
// @brief

#ifndef ANTI_FEATURE_LIB_POLICY_DEV_SIGNIFICANCE_RATIO_RULE_H
#define ANTI_FEATURE_LIB_POLICY_DEV_SIGNIFICANCE_RATIO_RULE_H

#include "threshold_limit_rule.h"

namespace anti {
namespace themis {
namespace feature_lib {

class SignificanceRatioRule : public ThresholdLimitRule {
public:
    SignificanceRatioRule() : _filter_mode(0), _illegal_mu(false) {}
    virtual ~SignificanceRatioRule() {
        uninit();
    }

protected:
    virtual bool _init(const comcfg::ConfigUnit& conf);
    virtual void _uninit() {
        ThresholdLimitRule::_uninit();
    }

private:
    virtual bool _check(const FeatureValueProto& feas, bool* hit) const;

    std::vector<int32_t> _threshold_vec;
    uint32_t _filter_mode;
    bool _illegal_mu;

};

}
}
}
#endif // ANTI_FEATURE_LIB_POLICY_DEV_SIGNIFICANCE_RATIO_RULE_H
