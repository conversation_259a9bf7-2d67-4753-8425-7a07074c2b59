// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: segment_limit_rule.h
// @Last modified: 2015-05-22 17:18:38
// @Brief: 
// @Note: real threshold = gray(conf) threshold - punish
//        punish = punish_k * (last segment count - gray(conf) threshold - punish_m)

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_SEGMENT_LIMIT_RULE_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_SEGMENT_LIMIT_RULE_H

#include "rule_interface.h"

namespace anti {
namespace themis {
namespace feature_lib {

class SegmentLimitRule : public RuleBase {
public:
    SegmentLimitRule() : 
             _threshold(0.0), 
             _punish_m(0), 
             _punish_k(0.0),
             _open_sqrt(false),
             _sqrt(1.0) {}
    virtual ~SegmentLimitRule() {
        uninit();
    }

private:
    virtual bool _init(const comcfg::ConfigUnit& conf);
    virtual void _uninit() {}
    virtual bool _check(const FeatureValueProto& feas, bool* hit) const;
    inline bool _type_check(const FeatureValueProto::FeatureType type) const;

    double _threshold;
    int64_t _punish_m;
    double _punish_k;
    bool _open_sqrt;
    double _sqrt;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_SEGMENT_LIMIT_RULE_H

/* vim: set ts=4 sw=4: */

