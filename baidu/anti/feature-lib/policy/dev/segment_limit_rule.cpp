// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: segment_limit_rule.cpp
// @Last modified: 2016-10-21 12:08:30
// @Brief: 

#include "segment_limit_rule.h"
#include <float.h>
#include <com_log.h>

namespace anti {
namespace themis {
namespace feature_lib {

bool SegmentLimitRule::_init(const comcfg::ConfigUnit& conf) {
    try {
        _threshold = conf["threshold"].to_double();
        if (conf["sqrt"].selfType() == comcfg::CONFIG_ERROR_TYPE) {
            _open_sqrt = false;
            _sqrt = _threshold;
        } else {
            _open_sqrt = true;
            _sqrt = conf["sqrt"].to_double();
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("comcfg::ConfigException : %s", e.what());
        return false;
    }
    long long punish_m = 0L;
    if (conf["punish_m"].get_int64(&punish_m, 0) != 0) {
        CWARNING_LOG("get punish_m fail, using 0 instead");
    }
    _punish_m = punish_m;
    if (conf["punish_k"].get_double(&_punish_k, 0.0) != 0) {
        CWARNING_LOG("get punish_k fail, using 0 instead");
    }
    return true;
}

bool SegmentLimitRule::_check(const FeatureValueProto& fea, bool* hit) const {
    if (!_type_check(fea.feature_type())
            || !fea.has_last_seg_count()
            || !fea.has_cur_seg_count()
            || hit == NULL) {
        CFATAL_LOG("invalid fea or input hit == NULL");
        return false;
    }
    double threshold = _threshold;
    double tmp = 0.0;
    if (_gray && _gray->lookup(fea.view_sign(), &tmp)) {
        // in gray
        threshold = tmp;
    }

    if (_open_sqrt && fea.has_coord_factor()) {
        double thres_beg = _sqrt * sqrt(threshold / _sqrt);
        if (thres_beg > threshold) { thres_beg = threshold; }

        double thres_diff = threshold - thres_beg;
        threshold = 0.5 + thres_beg + thres_diff * fea.coord_factor();
    }

    if (fea.last_seg_count() - threshold - _punish_m > 0.0) {
        // punish
        threshold -= (fea.last_seg_count() - threshold) * _punish_k;
    }
    threshold = threshold > 0.0 ? threshold : 0.0;
    *hit = fea.cur_seg_count() > threshold;
    return true;
}

bool SegmentLimitRule::_type_check(
        const FeatureValueProto::FeatureType type) const {
    if (type == FeatureValueProto::SEGMENT
            || type == FeatureValueProto::AUTO_SEGMENT
            || type == FeatureValueProto::MULTI_SEG
            || type == FeatureValueProto::BEHAVIOR_SEGMENT) {
        return true;
    }
    return false;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

