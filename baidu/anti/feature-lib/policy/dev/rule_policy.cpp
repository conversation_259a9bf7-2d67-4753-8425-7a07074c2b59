// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: rule_policy.cpp
// @Last modified: 2015-05-27 11:51:03
// @Brief: 

#include "rule_policy.h"
#include <com_log.h>
#include "rule_factory.h"

namespace anti {
namespace themis {
namespace feature_lib {

bool RulePolicy::init(const comcfg::ConfigUnit& conf) {
    if (!_black_tool.init(conf)) { 
        CWARNING_LOG("fail to init black tool.");
        return false;
    }

    try {
        _policy_id = conf["policy_id"].to_uint64();
        for (uint32_t i = 0; i < conf["rule"].size(); ++i) {
            std::string type(conf["rule"][i]["rule_type"].to_cstr());
            RuleInterface* rule = RuleFactory::create(type);
            if (rule == NULL || !rule->init(conf["rule"][i])) {
                CFATAL_LOG("new or init rule fail, policy_id(%lu)", _policy_id);
                if (rule != NULL) {
                    delete rule;
                    rule = NULL;
                }
                return false;
            }
            _rules.push_back(rule);
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("comcfg::ConfigException : %s", e.what());
        return false;
    }
    CWARNING_LOG("init policy(%lu) success", _policy_id);
    return true;
}

void RulePolicy::uninit() {
    for (uint32_t i = 0; i < _rules.size(); ++i) {
        if (_rules[i] != NULL) {
            _rules[i]->uninit();
            delete _rules[i];
            _rules[i] = NULL;
        }
    }
    _rules.clear();
}

bool RulePolicy::detect(const FeatureManager& feas, PolicyResultProto* result) const {
    if (result == NULL) {
        CFATAL_LOG("input result == NULL");
        return false;
    }

    bool hit = false;
    for (uint32_t i = 0U; i < _rules.size(); ++i) {
        if (_rules[i] == NULL) {
            CFATAL_LOG("policy_id(%lu) rule_idx(%u) is NULL", _policy_id, i);
            return false;
        }

        if (!_rules[i]->check(feas, &hit)) {
            CWARNING_LOG("call policy_id(%lu) rule_idx(%u) check fail", _policy_id, i) ;
            return false;
        }

        if (!hit) {
            // it's no need to check other rules if one rule no hit
            break;
        }
    }
    result->set_hit(hit);
    result->set_policy_id(_policy_id);

    if (result->hit() && !_black_tool.add_feature_result(feas, result)) {
        CWARNING_LOG("fail add black feature to result.");
        return false;
    }
    return true;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

