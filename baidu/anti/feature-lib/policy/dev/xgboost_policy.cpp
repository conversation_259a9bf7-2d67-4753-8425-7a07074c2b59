// Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
// @Author: xiarenjie
// @Brief:

#include "Configure.h"
#include "xgboost_policy.h"
#include <string>
#include <math.h>
#include <com_log.h>
#include <boost/lexical_cast.hpp>
#include <boost/algorithm/string.hpp>

namespace anti {
namespace themis {
namespace feature_lib {


XGBPolicy::XGBPolicy() : _joinkey(0LU) {
    _h_booster = nullptr;
}

XGBPolicy::~XGBPolicy() {
    if (_h_booster != nullptr) {
        XGBoosterFree(_h_booster);
    }
}

bool XGBPolicy::load_model(const std::string& conf_path, const std::string& conf_file) {
    comcfg::Configure conf;
    if (conf.load(conf_path.data(), conf_file.data()) != 0) {
        CWARNING_LOG("model conf error: path %s, file %s.", conf_path.data(), conf_file.data());
        return false;
    }
    
    //load configure 
    std::string model_file;
    try {
        std::string policy_id = boost::lexical_cast<std::string>(_policy_id);
        _threshold = conf[policy_id.data()]["threshold"].to_float();
        model_file = conf[policy_id.data()]["model_file"].to_cstr();
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException error %s: path %s, file %s.",
                e.what(), conf_path.data(), conf_file.data());
        return false;
    } catch (boost::bad_lexical_cast& e){
        CWARNING_LOG("transfer error %s: policy_id %lu).", e.what(), _policy_id);
        return false;
    }

    //init a null xgboost model
    /*
    * int XGBoosterCreate(const DMatrixHandle dmats[],
    *                     bst_ulong len,
    *                     BoosterHandle *out)
    *
    * param dmats (matrices that are set to be cached)
    * param len (length of dmats)
    * param out (handle to the result booster)
    * 
    */
    if (XGBoosterCreate(nullptr, 0, &_h_booster) != 0) {
        CWARNING_LOG("create xgboost model error.");
        return false;
    }

    //load xgboost model: _h_booster
    if (XGBoosterLoadModel(_h_booster, model_file.data()) != 0) {
        CWARNING_LOG("load xgboost model error : [%s].", model_file.data());
        return false;
    }
    return true;
}

bool XGBPolicy::_get_fea_list(const FeaVecMap& fea_vec, float* fea_list) const {
    if (fea_list == NULL) {
        CWARNING_LOG("args fea_list is null.");
        return false;
    }

    float value = 0;
    // MAX_FEATURE_NUM=1024 defined in Model_Policy
    for (uint32_t slot = 0; slot < _fea_num && slot < MAX_FEATURE_NUM; slot++) {
        if (fea_vec.find(slot) == fea_vec.end()) {
            CWARNING_LOG("no found slot[%d] in fea_vec for xgboost.", slot);
            return false;
        }
        try {
            value = boost::lexical_cast<float>(fea_vec.find(slot)->second);
        } catch (boost::bad_lexical_cast& e) {
            value = 0;
        }
        fea_list[slot] = value;
    }

    return true;
}

// will be used in Model_Policy::join_slot2feaval()
bool XGBPolicy::_normalize(
            const FeatureValueProto* fea,
            uint32_t slot,
            const std::string& value,
            std::string* normalized) const {
    if (normalized == NULL || slot >= _fea_num) {
        CWARNING_LOG("args error: slot %d, max %d, value null?(0 is false), %d.",
                slot, MAX_FEATURE_NUM, normalized == NULL);
        return false;
    }

    if (fea != NULL) {
        _joinkey = fea->joinkey();
    }

    if (value.empty()) {
        *normalized = _missing_value;
        return true;
    }
    *normalized = value;

    return true;
}

bool XGBPolicy::predict(const FeaVecMap& fea_vec, PolicyResultProto* result) const {
    if (result == NULL) {
        CWARNING_LOG("args result is null, joinkey:[%lu].", _joinkey);
        return false;
    } 

    if (_h_booster == nullptr) {
        CWARNING_LOG("Booster Model is not init, joinkey:[%lu].", _joinkey);
        return false;
    }

    // convert fea_vec to float[dim] fea_list
    float fea_list[MAX_FEATURE_NUM];
    if (!_get_fea_list(fea_vec, fea_list)) {
        CWARNING_LOG("_get_fea_list error, joinkey:[%lu].", _joinkey);
        return false;
    }

    // convert data into DMatrix
    DMatrixHandle h_input_feas;
    if (XGDMatrixCreateFromMat((float *) fea_list, 1, _fea_num, NAN, &h_input_feas) != 0) {
        CWARNING_LOG("create xgboost matrix failed, fea_num: %d, joinkey:[%lu]", _fea_num, _joinkey);
        return false;
    }

    /* int XGBoosterPredict(BoosterHandle handle,
     *                      DMatrixHandle dmat,
     *                      int option_mask,
     *                      unsigned ntree_limit,
     *                      bst_ulong *out_len,
     *                      const float **out_result);
     *
     * param option_mask (bit-mask of options taken in prediction, possible values)
     *       0:normal prediction
     *       1:output margin instead of transformed value
     *       2:output leaf index of trees instead of leaf value, note leaf index is unique per tree
     *       4:output feature contributions to individual predictions
     *
     * param ntree_limit (limit number of trees used for prediction, this is only valid for boosted trees)
     *       when the parameter is set to 0, we will use all the trees
     *
     * param out_len (used to store length of returning result)
     * param out_result (used to set a pointer to array)
     *
     */
    bst_ulong out_len;
    const float *out_result;
    if (XGBoosterPredict(_h_booster, h_input_feas, 0, 0, &out_len, &out_result) != 0) {
        XGDMatrixFree(h_input_feas);
        CWARNING_LOG("Xgboost model predict failed, joinkey:[%lu]", _joinkey);
        return false;
    }
    
    if (out_len != 1) {
        CWARNING_LOG("predict result size: %lu is not right! should be 1, joinkey:[%lu].", out_len, _joinkey);
        XGDMatrixFree(h_input_feas);
        return false;
    }

    if (out_result[0] > _threshold) {
        result->set_hit(true);
    } else {
        result->set_hit(false);
    }
    result->set_value(out_result[0]);
    XGDMatrixFree(h_input_feas);
    return true; 
}

} // namespace feature_lib
} // namespace themis
} // namespace anti


