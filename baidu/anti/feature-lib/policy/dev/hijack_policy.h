// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @brief 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_HIJACK_POLICY_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_HIJACK_POLICY_H

#include "policy_interface.h"

namespace anti {
namespace themis {
namespace feature_lib {

class HijackPolicy : public PolicyInterface {
public:
    HijackPolicy() : 
             _policy_id(0LU),
             _feature_id(0UL),
             _threshold(0LL),
             _hijack_delta(0UL) {}
    virtual ~HijackPolicy() {
        uninit();
    }

    virtual bool init(const comcfg::ConfigUnit& conf);
    virtual void uninit();

    virtual uint64_t policy_id() const {
        return _policy_id;
    }
    virtual bool detect(
            const FeatureManager& feas, 
            PolicyResultProto* result) const;

private:
    bool hit(const FeatureValueProto& fea, PolicyResultProto* result) const;

private:
    uint64_t _policy_id;
    uint64_t _feature_id;
    int64_t _threshold;
    uint64_t _hijack_delta;
};

}
}
}

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_HIJACK_POLICY_H
