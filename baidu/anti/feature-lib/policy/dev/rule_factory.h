// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: rule_factory.h
// @Last modified: 2015-05-21 16:44:31
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_RULE_FACTORY_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_RULE_FACTORY_H

#include <string>
#include "rule_interface.h"

namespace anti {
namespace themis {
namespace feature_lib {

class RuleFactory {
public:
    static RuleInterface* create(const std::string& type);
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_RULE_FACTORY_H

/* vim: set ts=4 sw=4: */

