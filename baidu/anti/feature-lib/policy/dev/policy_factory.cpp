// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: policy_factory.cpp
// @Last modified: 2015-05-25 18:02:27
// @Brief: 

#include "policy_factory.h"
#include <com_log.h>
#include "rule_policy.h"
#include "gbdt_policy.h"
#include "xgboost_policy.h"
#include "dnn_policy.h"
#include "lr_policy.h"
#include "hijack_policy.h"
#include "session_policy.h"
#include "deepshield_policy.h"
#include "ltr_gbdt_policy.h"

namespace anti {
namespace themis {
namespace feature_lib {

PolicyInterface* PolicyFactory::create(const std::string& type) {
    PolicyInterface* obj = NULL;
    if (type == "rule") {
        obj = new(std::nothrow) RulePolicy();
    } else if (type == "xgboost") {
        obj = new(std::nothrow) XGBPolicy();
    } else if (type == "gbdt") {
        obj = new(std::nothrow) GBDTPolicy();
    } else if (type == "ltrgbdt") {
        obj = new(std::nothrow) LTRGBDTPolicy();
    }else if (type == "dnn") {
        obj = new(std::nothrow) DNNPolicy();
    } else if (type == "lr") {
        obj = new(std::nothrow) LRPolicy();
    } else if (type == "hijack") {
        obj = new(std::nothrow) HijackPolicy();
    } else if (type == "session_dup_traffic") {
        obj = new(std::nothrow) SessionDupTrafficPolicy();
    } else if (type == "session_direct") {
        obj = new(std::nothrow) SessionDirectPolicy();
    } else if (type == "session_pass_through") {
        obj = new(std::nothrow) SessionPolicyPassThrough();
    } else if (type == "deepshield") {
        obj = new(std::nothrow) DeepShieldPolicy();
    } else {
        CWARNING_LOG("invalid policy type(%s)", type.data());
        return NULL;
    }

    if (obj == NULL) {
        CFATAL_LOG("new policy type(%s) fail", type.data());
        return NULL;
    }
    return obj;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

