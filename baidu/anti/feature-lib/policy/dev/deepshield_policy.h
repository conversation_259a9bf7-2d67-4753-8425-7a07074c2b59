// Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
// @brief 

#ifndef BAIDU_ANTI_FEATURE_LIB_POLICY_DEEPSHIELD_POLICY_H
#define BAIDU_ANTI_FEATURE_LIB_POLICY_DEEPSHIELD_POLICY_H

#include "rule_policy.h"

namespace anti {
namespace themis {
namespace feature_lib {

class DeepShieldPolicy : public RulePolicy {
public:
    DeepShieldPolicy();
    virtual ~DeepShieldPolicy();

    virtual bool init(const comcfg::ConfigUnit& conf);
    virtual void uninit();

    virtual bool detect(const FeatureManager& feas,
                        PolicyResultProto* result) const;

private:
    uint64_t _core_feature_id;
};

}
}
}

#endif  // BAIDU_ANTI_FEATURE_LIB_POLICY_DEEPSHIELD_POLICY_H
