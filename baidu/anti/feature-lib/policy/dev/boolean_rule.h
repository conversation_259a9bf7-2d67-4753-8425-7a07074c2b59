// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: boolean_rule.h
// @Last modified: 2015-06-05 12:21:58
// @Brief: condition boolean or not, e.g. in file_dict, in carespace

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_BOOLEAN_RULE_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_BOOLEAN_RULE_H

#include "rule_interface.h"

namespace anti {
namespace themis {
namespace feature_lib {

class BooleanRule : public RuleBase {
public:
    BooleanRule() : _con(true) {}
    virtual ~BooleanRule() {
        uninit();
    }

private:
    virtual bool _init(const comcfg::ConfigUnit& conf);
    virtual void _uninit() {}
    virtual bool _check(const FeatureValueProto& feas, bool* hit) const;

    bool _con;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_BOOLEAN_RULE_H

/* vim: set ts=4 sw=4: */

