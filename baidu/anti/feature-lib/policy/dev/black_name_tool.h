// Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
// @Author: longfei(<EMAIL>)

#pragma once

#include <string>
#include <unordered_map>
#include <com_log.h>
#include <Configure.h>
#include "feature_manager.h"
#include "policy_result.pb.h"
#include "feature_value.pb.h"

namespace anti {
namespace themis {
namespace feature_lib {

class BlackNameTool {
public:
    bool init(const comcfg::ConfigUnit& conf);
    bool add_feature_result(const FeatureManager& feas, PolicyResultProto* result) const;
    void uninit() {
        _blk_feas.clear();
    }

private:
    uint64_t _policy_id;
    std::unordered_map<uint64_t, uint32_t> _blk_feas;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */
