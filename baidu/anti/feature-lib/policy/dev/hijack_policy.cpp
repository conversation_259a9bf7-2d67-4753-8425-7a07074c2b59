// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @brief 

#include <cmath>
#include <vector>
#include "hijack_policy.h"

namespace anti {
namespace themis {
namespace feature_lib {

bool is_same(const FeatureValueProto::HijackProto& lhs,
        const FeatureValueProto::HijackProto& rhs) {
    return lhs.charge_name() == rhs.charge_name()
            && lhs.flow_group() == rhs.flow_group();
}

void find_original_idx(const FeatureValueProto& fea, 
        int32_t threshold, 
        int32_t* original_idx) {

    auto in_range = [&] (const FeatureValueProto::HijackProto& ref_lh, 
            const FeatureValueProto::HijackProto& ref_rh, int32_t threshold) {
        // in the range and has different charge_name or flow group
        return abs(ref_lh.distance() - ref_rh.distance()) <= threshold;
    };

    for (int32_t i = *original_idx + 1; i < fea.fix_hijack_field_size(); ++i) {
        auto& original_node = fea.fix_hijack_field(*original_idx);
        auto& cur_node = fea.fix_hijack_field(i);
        if (in_range(original_node, cur_node, threshold)) {
            *original_idx = i;
        } else {
            // find the oldest node already, out of range
            break; 
        }
    }
}

bool HijackPolicy::init(const comcfg::ConfigUnit& conf) {
    try {
        _policy_id = conf["policy_id"].to_uint64();
        _feature_id = conf["feature_id"].to_uint64();
        _threshold = conf["threshold"].to_int64();
        _hijack_delta = conf["hijack_delta"].to_uint64(); 
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("comcfg::ConfigException : %s", e.what());
        return false;
    }

    CWARNING_LOG("init policy(%lu) success", _policy_id);
    return true;
}

void HijackPolicy::uninit() {}

bool HijackPolicy::detect(const FeatureManager& fea_mgr, PolicyResultProto* result) const {
    if (result == NULL) {
        CFATAL_LOG("input param is NULL");
        return false;
    }

    // set default value!
    result->set_policy_id(_policy_id);
    result->set_hit(false);

    auto range = fea_mgr.query(_feature_id);
    // on hijack policy, only check one time.
    if (range.first != range.second) {
        auto& fea = *(range.first->second.get());
        hit(fea, result);
    }
    return true;
}

bool HijackPolicy::hit(const FeatureValueProto& fea, PolicyResultProto* result) const {
    if (fea.feature_id() != _feature_id ||
            fea.feature_type() != FeatureValueProto::HIJACK) {
        CWARNING_LOG("invalid feature for hijack policy, [fea:%s]",
                fea.ShortDebugString().data());
        return false;
    }

    // function to check hijack
    auto hijack_check = [&] (const FeatureValueProto::HijackProto& ref, int32_t threshold) {
        // in the range and has different charge_name or flow group
        return abs(ref.distance()) <= threshold 
            && !is_same(ref, fea.original_hijack_field());
    };

    // pre or next are in time order
    // find the node on the left and right side of feature
    int32_t target_idx_pre = -1;
    int32_t target_idx_next = -1; 
    for (int32_t i = 0; i < fea.fix_hijack_field_size(); ++i) {
        auto& ref_field = fea.fix_hijack_field(i);
        if (ref_field.distance() >= 0) {
            target_idx_pre = i;
            break;
        }
    }

    if (target_idx_pre == -1) {
        target_idx_next = fea.fix_hijack_field_size() - 1;
    } else {
        target_idx_next = target_idx_pre - 1;
    }

    // check fea's next node to check whether the fea was hijacked or 
    // repeated search by the next node
    auto in_range = [&] (const FeatureValueProto::HijackProto& ref, int32_t threshold) {
        // in the range and has different charge_name or flow group
        return abs(ref.distance()) <= threshold;
    };

    auto hijack_fix_res = result->mutable_hijack_fix_res();
    hijack_fix_res->set_log_time(fea.log_time());
    hijack_fix_res->set_exist_hijack(false);
    if (target_idx_next >= 0) {
        if (hijack_check(fea.fix_hijack_field(target_idx_next), _threshold)) {
            // exist hijack, fea was hijacked by the node after
            result->set_hit(true);
            result->set_policy_id(result->policy_id() + _hijack_delta);
            hijack_fix_res->set_exist_hijack(true);
        } else {
            if (in_range(fea.fix_hijack_field(target_idx_next), _threshold) 
                && fea.fix_hijack_field(target_idx_next).page_no() == 
                    fea.original_hijack_field().page_no()) {
                // not be hijacked by the next, but be repeated search
                result->set_hit(true);
            }
        }
    }

    // check the node before fea to check whether the fea hijacked or 
    // repeated searched the pre node
    if (target_idx_pre >= 0) {
        if (hijack_check(fea.fix_hijack_field(target_idx_pre), _threshold)) {
            // exist hijack, fea hijacks the node before 
            if (!hijack_fix_res->exist_hijack()) {
                result->set_policy_id(result->policy_id() + _hijack_delta);
                // if exists hijack, still need pass through this feature's hit result
                result->set_pass_through(true);     
                hijack_fix_res->set_exist_hijack(true);
            }

            int32_t original_idx = target_idx_pre;
            find_original_idx(fea, _threshold, &original_idx);

            hijack_fix_res->set_fix_cn( 
                    fea.fix_hijack_field(original_idx).charge_name());
            hijack_fix_res->set_fix_flow_group(
                    fea.fix_hijack_field(original_idx).flow_group());
        } else {
            if (in_range(fea.fix_hijack_field(target_idx_pre), _threshold) 
                && fea.fix_hijack_field(target_idx_pre).page_no() != 
                    fea.original_hijack_field().page_no()) {
                // not hijack the pre, but repeat search
                result->set_hit(true);
            }
        }
    }

    if (hijack_fix_res->exist_hijack()) {
        CDEBUG_LOG("debug ---> existed hijack, sign:%lu, key:%lu", fea.view_sign(), fea.joinkey());
        return true;
    }

    return true;
}

}
}
}
