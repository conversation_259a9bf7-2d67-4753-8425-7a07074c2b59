// Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
// @Author: longfei(<EMAIL>)

#include <boost/lexical_cast.hpp>
#include <boost/algorithm/string.hpp>
#include <sstream>
#include <set>
#include "black_name_tool.h"

namespace anti {
namespace themis {
namespace feature_lib {

bool BlackNameTool::init (const comcfg::ConfigUnit& conf) {
    std::string black_conf_str = "";

    try {
        _policy_id = conf["policy_id"].to_uint64();
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException in policy_id : %ld, %s.", _policy_id, e.what());
        return false;
    }

    if (conf["black_feature_and_time"].selfType() != comcfg::CONFIG_ERROR_TYPE) {
        black_conf_str.assign(conf["black_feature_and_time"].to_cstr());
    } else {
        CDEBUG_LOG("policy[%d] not set black name.", _policy_id);
        return true;
    }

    if (black_conf_str.length() == 0) {
        CWARNING_LOG("black_feature_and_time is null, policyid:[%lu].", _policy_id);
        return true;
    }

    std::string item;
    std::istringstream iss(black_conf_str);
    while (std::getline(iss, item, ';')) {
        std::size_t pos = item.find(",");
        if (pos == std::string::npos) {
            CWARNING_LOG("split feature_id and time error for(%lu : %s).", _policy_id, item.c_str());
            return false;
        }
        try {
            uint64_t feature_id = boost::lexical_cast<uint64_t>(item.substr(0, pos));
            uint32_t timespan = boost::lexical_cast<uint32_t>(item.substr(pos + 1));
            _blk_feas[feature_id] = timespan;

        } catch (boost::bad_lexical_cast& e){
            CWARNING_LOG("trasfer int error for (%lu  : %s).", _policy_id, e.what());
            return false;
        }
    }
    return true;
}

bool BlackNameTool::add_feature_result(const FeatureManager& feas, PolicyResultProto* result) const {
    if (result == NULL) {
        CFATAL_LOG("input result is NULL.");
        return false;
    }
    if (_blk_feas.size() == 0) {
        CDEBUG_LOG("black feature is empty.");
        return true;
    }

    result->clear_hit_fea();
    for (const auto& blk_fea : _blk_feas) {
        auto range = feas.query(blk_fea.first);
        if (range.first == range.second) {
            CDEBUG_LOG("not find black name feature_id in features!");
            continue;
        }
        for (auto iter = range.first; iter != range.second; ++iter) {
            const FeatureValueProto& fea = *(iter->second.get());
            auto hit_fea = result->add_hit_fea();
            if (!hit_fea) {
                CWARNING_LOG("fail to get hit_fea from PolicyResultProto.");
                return false;
            }
            hit_fea->CopyFrom(fea);
            if (!hit_fea->has_log_time()) {
                CFATAL_LOG("feature_vlue not has log_time!");
                return false;
            }
            hit_fea->set_black_timespan(blk_fea.second + hit_fea->log_time() / 1000);
            hit_fea->set_in_black(true);
        }
    }
    return true;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

