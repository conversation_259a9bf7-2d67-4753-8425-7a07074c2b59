// Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
// @file significance_distribution_rule.h
// <AUTHOR>
// @date 2019/01/09 14:23:23
// @brief

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_SIGNIFICANCE_DISTRIBUTION_RULE_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_SIGNIFICANCE_DISTRIBUTION_RULE_H

#include <vector>
#include "rule_interface.h"

namespace anti {
namespace themis {
namespace feature_lib {

class SignificanceDistributionRule : public RuleBase {
public:
    SignificanceDistributionRule() {}
    virtual ~SignificanceDistributionRule() {
        uninit();
    }

private:
    virtual bool _init(const comcfg::ConfigUnit& conf);
    virtual void _uninit() {
        _threshold_vec.clear();
    }
    virtual bool _check(const FeatureValueProto& feas, bool* hit) const;

    std::vector<std::vector<int32_t>> _threshold_vec;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_DISTRIBUTION_LIMIT_RULE_H

/* vim: set ts=4 sw=4: */

