// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: ratio_limit_rule.cpp
// @Last modified: 2016-11-14 12:08:36
// @Brief: 

#include "ratio_limit_rule.h"
#include <com_log.h>

namespace anti {
namespace themis {
namespace feature_lib {

bool RatioLimitRule::_init(const comcfg::ConfigUnit& conf) {
    try {
        if (!ThresholdLimitRule::_init(conf)) {
            CWARNING_LOG("ThresholdLimitRule _init failed");
            return false;
        }
        // TO DO(delete) remain and simple ratio
        _remain = conf["remain"].to_double();
        uint32_t simple_ratio = 0u;
        int err = conf["simple_ratio"].get_uint32(&simple_ratio);
        _simple_ratio = (simple_ratio != 0u && err == 0);
        uint32_t filter_mode = 0u;
        // filter_mode = 0, filter numerator
        // filter_mode = 1, filter denominator
        conf["filter_mode"].get_uint32(&filter_mode, 0u);
        _filter_mode = filter_mode != 0u;
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("comcfg::ConfigException : %s", e.what());
        return false;
    }
    return true;
}

bool RatioLimitRule::_check(const FeatureValueProto& fea, bool* hit) const {
    if ((fea.feature_type() != FeatureValueProto::RATIO 
            && fea.feature_type() != FeatureValueProto::CONCENTRATION
            && fea.feature_type() != FeatureValueProto::BEHAVIOR_RATIO)
            || !fea.has_in_filter()
            || !fea.has_filter_count()
            || !fea.has_refer_count()
            || hit == NULL) {
        CFATAL_LOG("invalid fea or input hit == NULL");
        return false;
    }
    double threshold = _threshold;
    double tmp = 0.0;
    if (_gray && _gray->lookup(fea.view_sign(), &tmp)) {
        // in gray
        threshold = tmp;
    }
    if (fea.feature_type() == FeatureValueProto::CONCENTRATION &&
            fea.has_valid() && !fea.valid()) {
        *hit = false;
        return true;
    }
    if (!_simple_ratio) {
        // TO DO(delete)
        *hit = fea.in_filter() && 
            _cmp((fea.filter_count() - fea.refer_count() * threshold), _remain);
    } else {
        // _filter_mode = false and in_filter, filter numerator.
        // _filter_mode = true, filter all
        bool hit_filter = (fea.in_filter() && !_filter_mode) || _filter_mode;
        *hit = fea.valid() && hit_filter && 
                _cmp(fea.filter_count(), fea.refer_count() * threshold);
    }
    return true;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

