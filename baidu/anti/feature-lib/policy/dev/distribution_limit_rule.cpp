// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: distribution_limit_rule.cpp
// @Last modified: 2017-11-21 18:02:26
// @Brief: 

#include <float.h>
#include <math.h>
#include "distribution_limit_rule.h"
#include <boost/lexical_cast.hpp>
#include <boost/algorithm/string.hpp>
#include <com_log.h>
#include "distribution_distance_calculate.h"

namespace anti {
namespace themis {
namespace feature_lib {

bool DistributionLimitRule::_init(const comcfg::ConfigUnit& conf) {
    try {
        _threshold = conf["threshold"].to_double();
        _count_threshold = conf["count_threshold"].to_int64();
        uint32_t simple_dis = 0U;
        conf["simple_distribution"].get_uint32(&simple_dis, 0U);
        _simple_distribution = simple_dis == 1U ? true : false;
        std::string sta_pro(conf["stand_prob"].to_cstr());
        std::vector<std::string> sta_strs;
        boost::algorithm::split(
                sta_strs, sta_pro, boost::is_any_of(", "), boost::token_compress_on);
        for (uint32_t i = 0U; i < sta_strs.size(); ++i) {
            double prob = boost::lexical_cast<double>(sta_strs[i]);
            if (prob < 0.0) {
                CWARNING_LOG("probability(%u) is less than zero", i);
                return false;
            }
            _stand_prob.push_back(prob);
        }

        std::string func_type(conf["func_type"].to_cstr());
        if (_simple_distribution) {
            if (func_type == "max_diff") {
                _distribute_distance = max_diff;
            } else if (func_type == "chi_square_test") {
                _distribute_distance = chi_square_test;
            } else if (func_type == "chi_square_dis") {
                _distribute_distance = chi_square_dis;
            } else if (func_type == "kl_divergence") {
                _distribute_distance = kl_divergence;
            } else {
                CWARNING_LOG("invalid func type(%s)", func_type.data());
                return false;
            }
        } else {
            if (func_type == "max_diff") {
                _distance = _max_diff;
            } else if (func_type == "chi_square_test") {
                _distance = _chi_square_test;
            } else if (func_type == "chi_square_dis") {
                _distance = _chi_square_dis;
            } else if (func_type == "kl_divergence") {
                _distance = _kl_divergence;
            } else {
                CWARNING_LOG("invalid func type(%s)", func_type.data());
                return false;
            }
        }
        // The default value of max_distance_magnify is 1.0. It means that pc will not to be magnified.
        double max_distance_magnify = 1.0;
        const double MAX_DISTANCE_MAGNIFY_DEFAULT = 1.0;
        conf["max_distance_magnify"].get_double(&max_distance_magnify, MAX_DISTANCE_MAGNIFY_DEFAULT);
        if (max_distance_magnify < MAX_DISTANCE_MAGNIFY_DEFAULT) {
            max_distance_magnify = MAX_DISTANCE_MAGNIFY_DEFAULT;
            CWARNING_LOG("Max_distance_magnify is smaller than 1.0 and the value will be 1.0");
        }
        _distribution_distance = new(std::nothrow) DistributionDistance();
        if (!_distribution_distance || !_distribution_distance->init(
                    func_type,
                    sta_pro,
                    _count_threshold,
                    max_distance_magnify)) {
            CWARNING_LOG("create distance failed: %s", func_type.data());
            return false;
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("comcfg::ConfigException : %s", e.what());
        return false;
    } catch (const boost::bad_lexical_cast& e) {
        CWARNING_LOG("boost::bad_lexical_cast : %s", e.what());
        return false;
    }
    return true;
}

double DistributionLimitRule::_max_diff(
        const std::vector<double>& pb,
        const std::vector<double>& pc,
        uint32_t bucket_idx,
        double threshold) {
    // (pc - pb) / pc > threshold  ==>  
    return  fabs(threshold - 1.0) <= DBL_EPSILON 
            ? DBL_MIN_EXP : pc[bucket_idx] - pb[bucket_idx] / (1 - threshold);
}

double DistributionLimitRule::_chi_square_test(
        const std::vector<double>& pb,
        const std::vector<double>& pc,
        uint32_t bucket_idx,
        double threshold) {
    double s = 0.0;
    for (uint32_t i = 0; i < pb.size(); ++i) {
        if (i != bucket_idx) {
            double tmp = (pc[i] - pb[i]) / pb[i];
            tmp *= pow(1.8, pb[i] * 10);
            tmp = pow(tmp, 4);
            s += tmp;
        }
    }

    if (threshold <= s) {
        return 0;
    }
    double p = pow(threshold - s, 0.25) * pb[bucket_idx] / pow(1.8, pb[bucket_idx] * 10);
    return pc[bucket_idx] - p - pb[bucket_idx];
}

double DistributionLimitRule::_chi_square_dis(
        const std::vector<double>& pb,
        const std::vector<double>& pc,
        uint32_t bucket_idx,
        double threshold) {
    double s = 0.0;
    for (uint32_t i = 0; i < pb.size(); ++i) {
        if (i != bucket_idx) {
            double tmp = (pc[i] - pb[i]) * (pc[i] - pb[i]);
            tmp /= pc[i] + pb[i];
            s += tmp;
        }
    }

    if (threshold * 2 < s) {
        return 0;
    }

    double p1 = pow(threshold * 2 - s, 2) + 8 * pb[bucket_idx] * (threshold * 2 - s);
    double p2 = threshold * 2 - s + pow(p1, 0.5);
    return pc[bucket_idx] - pb[bucket_idx] - p2 / 2;
}

double DistributionLimitRule::_kl_divergence(
        const std::vector<double>& pb,
        const std::vector<double>& pc,
        uint32_t bucket_idx,
        double threshold) {
    double s = 0.0;
    for (uint32_t i = 0; i < pb.size(); ++i) {
        if (pc[i] <= FLT_EPSILON) {
            continue;
        }
        double tmp = log2(pc[i]) - log2(pb[i]);
        tmp *= pc[i];
        s += tmp;
    }

    if (threshold < s) {
        return 0;
    } else {
        return -1;
    }
}

bool DistributionLimitRule::_check(const FeatureValueProto& fea, bool* hit) const {
    if ((fea.feature_type() != FeatureValueProto::DISTRIBUTION 
            && fea.feature_type() != FeatureValueProto::COUNT_DISTRIBUTION
            && fea.feature_type() != FeatureValueProto::DISTINCT_DISTRIBUTION
            && fea.feature_type() != FeatureValueProto::BEHAVIOR_DISTRIBUTION
            && fea.feature_type() != FeatureValueProto::RATE_DISTRIBUTION
            && fea.feature_type() != FeatureValueProto::CPM_DISTRIBUTION)
            || hit == NULL) {
        CDEBUG_LOG("invalid fea type or input hit == NULL, feaid(%lu)", fea.feature_id());
        return true;
    }

    if (!fea.has_bucket_idx() || fea.bucket_idx() >= _stand_prob.size()) {
        CDEBUG_LOG("no bucket idx or bucket idx too big, feaid(%lu)", fea.feature_id());
        return true;
    }

    int64_t cur_count = 0L;
    for (int i = 0; i < fea.buckets_size(); ++i) {
        if (fea.buckets(i).idx() >= _stand_prob.size()) {
            CWARNING_LOG("invalid bucket idx(%u) >= _stand_prob.size(%u)", 
                    fea.buckets(i).idx(), _stand_prob.size());
            return false;
        }
        cur_count += fea.buckets(i).count();
    }
    std::vector<double> pc(_stand_prob.size(), 0.0);
    if (cur_count != 0) {
        for (int i = 0; i < fea.buckets_size(); ++i) {
            pc[fea.buckets(i).idx()] = 1.0 * fea.buckets(i).count() / cur_count;
        }
    }
    if (_distribution_distance == NULL || !_distribution_distance->magnify_dis(_stand_prob, &pc)) {
        CWARNING_LOG("magnify_distance_fail, please check!");
        return false;
    }
    double threshold = _threshold;
    double tmp = 0.0;
    if (_gray && _gray->lookup(fea.view_sign(), &tmp)) {
        // in gray
        threshold = tmp;
    }
    uint32_t idx = fea.bucket_idx();
    if (cur_count < _count_threshold || pc[idx] < _stand_prob[idx]) {
        *hit = false;
        return true;
    }
    if (_simple_distribution) {
        double fea_distribution_distance = 0.0;
        try {
            fea_distribution_distance = fea.has_value() ? 
                    boost::lexical_cast<double>(fea.value().c_str()) :
                    _distribute_distance(_stand_prob, pc, idx);
        } catch (const boost::bad_lexical_cast &e) {
            CWARNING_LOG("bad_lexical_cast[%s]", e.what());
            return false;
        }
        *hit = fea_distribution_distance > _threshold;
        return true;
    } else {
        int64_t extra_b = static_cast<int64_t>(
                _distance(_stand_prob, pc, idx, threshold) * cur_count * B_VALUE);
        *hit = extra_b > -1 * B_VALUE;
        return true;
    }
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

