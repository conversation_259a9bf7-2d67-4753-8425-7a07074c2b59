// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: rule_policy.h
// @Last modified: 2015-05-21 16:48:11
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_RULE_POLICY_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_RULE_POLICY_H

#include <vector>
#include "policy_interface.h"
#include "rule_interface.h"
#include "black_name_tool.h"

namespace anti {
namespace themis {
namespace feature_lib {

class RulePolicy : public PolicyInterface {
public:
    RulePolicy() : _policy_id(0LU) {}
    virtual ~RulePolicy() {
        uninit();
    }

    virtual bool init(const comcfg::ConfigUnit& conf);
    virtual void uninit();

    virtual uint64_t policy_id() const {
        return _policy_id;
    }
    virtual bool detect(
            const FeatureManager& feas, 
            PolicyResultProto* result) const;
private:
    uint64_t _policy_id;
    std::vector<RuleInterface*> _rules;
    BlackNameTool _black_tool;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_DEV_RULE_POLICY_H

/* vim: set ts=4 sw=4: */
