// Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
// @Author: z<PERSON><PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
// 
// @File: policy_collector.cpp
// @Last modified: 2018-03-23 15:05:50
// @Brief: 

#include <set>
#include <algorithm>
#include <gflags/gflags.h>
#include <boost/algorithm/string.hpp>
#include <com_log.h>
#include "policy_collector.h"

namespace anti {
namespace themis {
namespace feature_lib {

bool PolicyCollector::init(
        const std::string& path,
        const std::string& file) {
    comcfg::Configure conf;
    if (conf.load(path.data(), file.data()) != 0) {
        CFATAL_LOG("load %s/%s failed", path.data(), file.data());
        return false;
    }
    try {
        RecordType record_type;
        if (conf["source"].size() == 0) {
            //if source size is zero, log_col should by get only one type record
            //with any record_type.
            _manager = _init_conf(path, file, record_type);
            if (_manager == NULL) {
                CWARNING_LOG("_init_conf fail");
                return false;
            }
        }

        for (uint32_t source_id = 0; source_id < conf["source"].size(); ++source_id) {
            const auto& src_conf = conf["source"][source_id];
            std::string log_type(src_conf["log_type"].to_cstr());
            std::string conf_path(src_conf["policy_path"].to_cstr());
            std::string conf_file(src_conf["policy_file"].to_cstr());
            if (!RecordType_Parse(log_type, &record_type)) {
                CWARNING_LOG("get type fail:%s", log_type.c_str());
                return false;
            }

            PolicyManagerPtr mgr_ptr = _init_conf(conf_path, conf_file, record_type);
            if (mgr_ptr == NULL) {
                CWARNING_LOG("_init_conf multi fail %s error", log_type.c_str());
                return false;
            }

            _manager_map.insert(std::pair<RecordType,
                    PolicyManagerPtr>(record_type, mgr_ptr));
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("comcfg::ConfigException : %s", e.what());
        return false;
    }

    CWARNING_LOG("init policy collector success");
    return true;
}

PolicyManagerPtr PolicyCollector::_init_conf(const std::string& path,
        const std::string& file,
        RecordType record_type) {
    auto iter = _manager_map.find(record_type);
    if (iter != _manager_map.end()) {
        CWARNING_LOG("record type [%d] already initialized, please check config");
        return std::shared_ptr<PolicyManager>();
    }

    PolicyManagerPtr mgr_ptr(new (std::nothrow) PolicyManager());
    if (mgr_ptr == NULL) {
        CWARNING_LOG("manager allcate [%d] error", record_type);
        return std::shared_ptr<PolicyManager>();
    }

    if (!mgr_ptr->init(path, file)) {
        CWARNING_LOG("mgr init [%d] error", record_type);
        return std::shared_ptr<PolicyManager>();
    }

    return mgr_ptr;
}
    
bool PolicyCollector::detect(
        FeatureManager* feas, 
        std::vector<PolicyResultProto>* results,
        RecordType record_type) {
    if (_manager_map.size() == 0) {
        if (_manager == NULL) {
            CFATAL_LOG("_manager ptr is NULL");
            return false;
        }
        return _manager->detect(feas, results);
    }

    auto manager = _get_fea_mgr(record_type);
    if (manager == NULL) {
        CWARNING_LOG("detect get unkown record type [%d]", record_type);
        return false;
    }

    std::vector<PolicyResultProto> temp_res;
    if (!manager->detect(feas, &temp_res)) {
        CWARNING_LOG("record_type:[%d] detect fail" , record_type);
        return false;
    } else {
        for (auto it = temp_res.begin(); it != temp_res.end(); ++it) {
            it->set_record_type(record_type);
        }
        results->insert(results->end(), temp_res.begin(), temp_res.end());
    }

    return true;
}

PolicyManagerPtr PolicyCollector::_get_fea_mgr(
        RecordType record_type) {
    if (_manager_map.size() == 0) {
        return _manager;
    }
    auto iter = _manager_map.find(record_type);
    if (iter != _manager_map.end()) {
        return iter->second; 
    }
    CWARNING_LOG("record_type:[%d] do not manager get instance", record_type);
    return std::shared_ptr<PolicyManager>();
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

