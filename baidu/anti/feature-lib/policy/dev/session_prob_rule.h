// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// @Author: wangkai<PERSON>(<EMAIL>)
// @Last modified: 2017-06-09 12:08:30
//
#ifndef FEATURE_LIB_SESSION_PROB_RULE_H
#define FEATURE_LIB_SESSION_PROB_RULE_H

#include "rule_interface.h"

namespace anti {
namespace themis {
namespace feature_lib {

class SessionProbRule : public RuleBase {
public:
    SessionProbRule();
    virtual ~SessionProbRule() {
        uninit();
    }

private:
    virtual bool _init(const comcfg::ConfigUnit& conf);
    virtual bool _init_prob_file(const comcfg::ConfigUnit& conf);
    virtual void _uninit() {}
    virtual bool _check(const FeatureValueProto& feas, bool* hit) const;

    double _get_threshold(int32_t policy_id_size) const;
    virtual bool _cal_policy_prob(
            const ActionProto::SearchInfoProto& hijack, 
            double* prob,
            int32_t* valid_hit_size) const;

    double _threshold;
    std::shared_ptr<const GrayFileDict> _prob_dict;
};

} // end namespace feature_lib
} // end namespace themis
} // end namespace anti

#endif // FEATURE_LIB_SESSION_PROB_RULE_H
