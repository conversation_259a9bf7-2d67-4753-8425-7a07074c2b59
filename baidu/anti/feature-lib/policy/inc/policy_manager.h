// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yang<PERSON><PERSON><PERSON>(<EMAIL>)
// 
// @File: policy_manager.h
// @Last modified: 2018-03-23 15:02:37
// @Brief: 
// @Usage:
//     1. new FeatureManager fm && PolicyManger pm
//     2. insert features with same joinkey into fm
//     3. call pm->detect(fm, &results)
//     4. use results

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_INC_POLICY_MANAGER_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_INC_POLICY_MANAGER_H

#include <string>
#include <vector>
#include "feature_manager.h"
#include "policy_result.pb.h"
#include "policy_interface.h"
#include <Configure.h>
#include "exp.h"

namespace anti {
namespace themis {
namespace feature_lib {

// 简介：策略管理器
// 功能：负责策略的管理, 每个策略由多个rule组成, 当且仅当所有rule都满足时, 才命中策略
// 主要接口：detect()
// 输入：policy.conf 、 特征集合(FeatureManager 对象)
// 输出：PolicyResultProto
class PolicyManager {
public:
    ~PolicyManager() {
        uninit();
    }
    bool init(const std::string& conf_path, const std::string& conf_file);
    bool detect(
            FeatureManager* feas_ptr,
            std::vector<PolicyResultProto>* results); 
    void uninit();

private:
    typedef anti::themis::common_lib::Exp Exp;
    bool _detect(
            const FeatureManager& feas, 
            std::vector<PolicyResultProto>* results) const;
    bool _init_exps(const comcfg::ConfigUnit& conf);
    bool _calculate_exps(FeatureManager* feas_manager, Exp* exp);
    std::vector<PolicyInterface*> _policies;
    Exp _exp;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_INC_POLICY_MANAGER_H

/* vim: set ts=4 sw=4: */

