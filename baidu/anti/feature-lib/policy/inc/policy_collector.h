// Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
// @Author: z<PERSON><PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
// 
// @Last modified: 2018-03-23 15:05:16
// @Brief:
// @Usage: 
// @Note: 

#ifndef BAIDU_ANTI_FEATURE_LIB_POLICY_COLLECTOR_H
#define BAIDU_ANTI_FEATURE_LIB_POLICY_COLLECTOR_H

#include <string>
#include <vector>
#include <memory>
#include <Configure.h>
#include "record.pb.h"
#include "policy_manager.h"

namespace anti {
namespace themis {
namespace feature_lib {

typedef anti::themis::RecordType RecordType;
typedef std::shared_ptr<PolicyManager> PolicyManagerPtr;

class PolicyCollector {
public:
    PolicyCollector() {}
    ~PolicyCollector() {}

    bool init(
            const std::string& fea_conf_path,
            const std::string& fea_conf_file);
    bool detect(
            FeatureManager* feas,
            std::vector<PolicyResultProto>* results,
            RecordType record_type);

private:
    PolicyManagerPtr _get_fea_mgr(anti::themis::RecordType record_type);
    PolicyManagerPtr _init_conf(const std::string& path,
            const std::string& file,
            const anti::themis::RecordType record_type);
    PolicyManagerPtr _manager;
    std::map<anti::themis::RecordType, PolicyManagerPtr> _manager_map;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // BAIDU_ANTI_FEATURE_LIB_POLICY_COLLECTOR_H 

/* vim: set ts=4 sw=4: */

