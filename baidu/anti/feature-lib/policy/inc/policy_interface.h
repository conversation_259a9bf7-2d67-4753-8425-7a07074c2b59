// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: policy_interface.h
// @Last modified: 2015-05-21 16:04:20
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_INC_POLICY_INTERFACE_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_INC_POLICY_INTERFACE_H

#include <Configure.h>
#include "feature_manager.h"
#include "policy_result.pb.h"

namespace anti {
namespace themis {
namespace feature_lib {

class PolicyInterface {
public:
    virtual ~PolicyInterface() {}
    virtual bool init(const comcfg::ConfigUnit& conf) = 0;
    virtual void uninit() = 0;

    virtual uint64_t policy_id() const = 0;
    virtual bool detect(
            const FeatureManager& feas, 
            PolicyResultProto* result) const = 0;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_INC_POLICY_INTERFACE_H

/* vim: set ts=4 sw=4: */

