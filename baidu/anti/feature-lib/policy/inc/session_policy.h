// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: 
// 
// @File: click_hijack_policy.h
// @Last modified: 2017-07-21 16:44:04
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_INC_SESSION_POLICY_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_INC_SESSION_POLICY_H

#include <functional>
#include "policy_interface.h"
#include "cn_map_file_dict.h"
#include "rule_policy.h"


namespace anti {
namespace themis {
namespace feature_lib {

typedef std::map<std::string, const ActionProto*> SessionHijackDict;

class Selector {
public:
    Selector() : _session_time(-1), _log_type(-1), _prange({0L, INT64_MAX}) {}
    ~Selector() {}
    bool init(const comcfg::ConfigUnit& conf);
    bool select(const FeatureValueProto& fea, std::vector<ActionProto>* valid_action) const;

private:
    bool _select_log_type(const FeatureValueProto& fea, std::vector<int>* flag) const;
    bool _select_search_id(const FeatureValueProto& fea, std::vector<int>* flag) const;
    bool _select_time(const FeatureValueProto& fea, std::vector<int>* flag) const;
    bool _select_priority_range(const FeatureValueProto& fea, std::vector<int>* flag) const;
    bool _select_field(const FeatureValueProto& fea, std::vector<int>* flag) const;
    bool _get_field(const google::protobuf::Message& msg, const std::string& full_path, std::string* value) const;
    int64_t _session_time;
    int64_t _log_type;
    struct PriorityRange {
        int64_t min;
        int64_t max;
    };
    PriorityRange _prange;
    std::vector<std::string> _contrasts;
};

class SessionPolicyPassThrough : public PolicyInterface {
public:
    SessionPolicyPassThrough() : _policy_id(0UL), _copy_info(NULL) {}
    virtual ~SessionPolicyPassThrough() {
        uninit();
    }
    virtual bool init(const comcfg::ConfigUnit& conf);
    virtual void uninit() {
        if (_copy_info != NULL) {
            delete _copy_info;
            _copy_info = NULL;
        }
    }
    virtual uint64_t policy_id() const {
        return _policy_id;
    }
    virtual bool detect(const FeatureManager& feas, PolicyResultProto* result) const;

private:
    struct CopyInfo {
        std::string src;
        std::string dst;
    };
    bool _copy_all(const FeatureManager& fea_mgr, PolicyResultProto* res) const;
    bool _copy_field(const FeatureManager& fea_mgr, PolicyResultProto* res) const;
    int64_t _policy_id;
    std::vector<int64_t> _feature_id_vec;
    CopyInfo* _copy_info;
};

class SessionPolicyBase : public PolicyInterface {
public:
    SessionPolicyBase() : _policy_id(0UL), _feature_id(0UL), _rule(NULL) {}
    virtual ~SessionPolicyBase() {
        uninit();
    }
    virtual bool init(const comcfg::ConfigUnit& conf);
    virtual void uninit() {
        return _uninit();
    }
    virtual uint64_t policy_id() const {
        return _policy_id;
    }
    virtual bool detect(const FeatureManager& feas, PolicyResultProto* result) const;

protected:
    std::string _find_partner_tag(const std::string& cnt) const;

private:
    typedef std::shared_ptr<const common_lib::CnMapFileDict> ConstCnMapFileDictPtr;

    virtual bool _init(const comcfg::ConfigUnit& conf)  = 0;
    virtual void _uninit() {};
    virtual bool _detect(
            const FeatureValueProto& fea,
            const std::vector<ActionProto>& actions,
            PolicyResultProto* result) const = 0;

    bool is_fea_valid(const FeatureValueProto& fea) const;
    bool _care(const FeatureManager& fea_mgr) const;

    ConstCnMapFileDictPtr _cn_map;
    uint64_t _policy_id;
    uint64_t _feature_id;
    Selector _selector;
    RulePolicy* _rule;
};

class SessionDupTrafficPolicy : public SessionPolicyBase {
public:
    SessionDupTrafficPolicy() : _hijack_time(0) {}
    virtual ~SessionDupTrafficPolicy() {}

private:
    virtual bool _init(const comcfg::ConfigUnit& conf);
    virtual bool _detect(
            const FeatureValueProto& fea,
            const std::vector<ActionProto>& actions,
            PolicyResultProto* result) const;
    virtual bool _is_hijack(
            const ActionProto& pre_feature,
            const ActionProto& cur_feature) const;
    virtual bool _is_query_equal(
            const ActionProto& pre,
            const ActionProto& cur) const;
    virtual bool _revise_cnt_name(
            const ActionProto& click_fea,
            const SessionHijackDict& hijack_dict,
            PolicyResultProto* result) const;
    bool _add_cntname_to_hijack_dict(
            const std::string& hijack_cntname,
            const ActionProto& ori_hijacked,
            SessionHijackDict* hijack_dict) const;

    int64_t _hijack_time;
};

class SessionDirectPolicy : public SessionPolicyBase {
public:
    SessionDirectPolicy();
    virtual ~SessionDirectPolicy() {}

private:
    virtual bool _init(const comcfg::ConfigUnit& conf);
    virtual bool _detect(
            const FeatureValueProto& fea,
            const std::vector<ActionProto>& actions,
            PolicyResultProto* result) const;
    virtual bool _fix_cnt(
            const FeatureValueProto& fea,
            const std::vector<ActionProto>& actions,
            PolicyResultProto* result) const;
    virtual bool _fix_group(
            const FeatureValueProto& fea,
            const std::vector<ActionProto>& actions,
            PolicyResultProto* result) const;
    int64_t _min_priority;
    typedef std::function<bool(
            const FeatureValueProto&,
            const std::vector<ActionProto>&,
            PolicyResultProto*)> FixFunc;
    FixFunc _fix_func;
};

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_INC_SESSION_POLICY_H

/* vim: set ts=4 sw=4: */
