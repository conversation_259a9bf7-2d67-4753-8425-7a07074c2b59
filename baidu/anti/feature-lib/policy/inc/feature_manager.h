// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangji<PERSON><PERSON>(<EMAIL>)
// 
// @File: feature_manager.h
// @Last modified: 2015-07-24 11:09:32
// @Brief: use to maintain all the features for each log
// @Usage: 
// Prepare: insert all feature to FeatureManager
// 1. pair<const_iterator, const_iterator> fea_range = fea_set.query(fea_id)
// 2. for(cont_iterator iter = fea_range.first; iter != fea_range.second; ++iter)
// 3.     use FeatureValueProto by iter

#ifndef APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_INC_FEATURE_MANAGER_H
#define APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_INC_FEATURE_MANAGER_H

#include <vector>
#include <memory>
#include <unordered_map>
#include <com_log.h>
#include "feature_value.pb.h"

namespace anti {
namespace themis {
namespace feature_lib {

// 简介：特征管理器
// 功能：对一条日志所有的累积特征进行封装, 防止特征与策略之间接口发生变化, 以减少批量修改策略接口所带来的代价, 它是特征和策略之间的数据接口
// 主要接口：insert() / query()
// 输入：feature_id
// 输出：vector<FeaPtr> (feature_id 对应的所有 FeatureValueProto)
class FeatureManager {
public:
    typedef std::shared_ptr<FeatureValueProto> FeaPtr;
    typedef typename std::unordered_multimap<uint64_t, FeaPtr>::const_iterator FeaCIter;
    FeatureManager() {}
    virtual ~FeatureManager() {
        clear();
    }

    bool insert(const FeaPtr& fea) {
        if (!fea) {
            CFATAL_LOG("input fea is NULL");
            return false;
        }
        _feas.emplace(fea->feature_id(), fea);
        return true;
    }

    std::pair<FeaCIter, FeaCIter> query(uint64_t fea_id) const {
        return _feas.equal_range(fea_id);
    }
    bool query(uint64_t fea_id, std::vector<FeaPtr>* feas) const;

    void clear() {
        _feas.clear();
    }

private:
    typedef std::unordered_multimap<uint64_t, FeaPtr> FeaMap;
    FeaMap _feas;
};

inline bool FeatureManager::query(uint64_t fea_id, std::vector<FeaPtr>* feas) const {
    if (feas == NULL) {
        CFATAL_LOG("input feas is NULL");
        return false;
    }
    feas->clear();
    auto range = query(fea_id);
    for (auto iter = range.first; iter != range.second; ++iter) {
        feas->push_back(iter->second);
    }
    return true;
}

}  // namespace feature_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_FEATURE_LIB_POLICY_INC_FEATURE_MANAGER_H

/* vim: set ts=4 sw=4: */

