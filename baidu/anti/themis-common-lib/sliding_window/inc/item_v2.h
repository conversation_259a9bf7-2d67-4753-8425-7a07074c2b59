/***************************************************************************
 * 
 * Copyright (c) 2025 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/

/**
 * @file: sliding_window/inc/item_v2.h
 * @author: <PERSON> (<EMAIL>)
 * @date: 2025/07/29
 * @brief: V2版本的Item类，使用内存池优化
 *  
 **/

#ifndef  ANTI_THEMIS_SLIDING_WINDOW_INC_ITEM_V2_H
#define  ANTI_THEMIS_SLIDING_WINDOW_INC_ITEM_V2_H

#include <stdint.h>
#include <vector>
#include <unordered_map>
#include <stdexcept>
#include <com_log.h>
#include "pool_mini_hash_map.hpp"
#include "gc_slab_mempool_32.h"
#include "archive.h"

namespace anti {
namespace themis {
namespace common_lib {

/**
 * @brief DistinctItem的V2版本，使用PoolMiniHashMap替代std::unordered_map
 *
 * 相比原版本优化：
 * 1. 使用PoolMiniHashMap减少内存分配开销，针对小数据量优化
 * 2. Item大小从64字节降至32字节
 * 3. 支持内存池管理和监控
 */
class DistinctItemV2 {
public:
    typedef uint64_t KeyType;
    typedef int64_t ValueType;
    typedef GCSlabMempool32 MemPoolType;
    typedef PoolMiniHashMap<KeyType, ValueType, MemPoolType> MapType;

    /**
     * @brief 带内存池的构造函数
     * @param data_view_sign 数据视图签名
     * @param count 计数值
     * @param pool 内存池指针
     */
    DistinctItemV2(KeyType data_view_sign, ValueType count, MemPoolType* pool) 
        : _map(pool, 1),  // 初始桶数为1，适应大部分只有1-2个元素的场景
          _total(count) {
        // 与原版DistinctItem保持一致：无论key是什么值都插入
        _map.emplace(data_view_sign, count);
    }

    /**
     * @brief 拷贝构造函数 - 实现深拷贝
     */
    DistinctItemV2(const DistinctItemV2& other) 
        : _map(other._map),  // PoolHashMap的拷贝构造实现深拷贝
          _total(other._total) {}

    /**
     * @brief 拷贝赋值运算符
     */
    DistinctItemV2& operator=(const DistinctItemV2& other) {
        if (this != &other) {
            _map = other._map;  // PoolHashMap的赋值运算符实现深拷贝
            _total = other._total;
        }
        return *this;
    }

    /**
     * @brief 累积操作 - 合并两个DistinctItem
     */
    DistinctItemV2& operator+=(const DistinctItemV2& other) {
        _total += other._total;
        
        // 使用traverse遍历other的map，合并到当前map中
        other._map.traverse([this](const KeyType& key, const ValueType& value) {
            auto result = _map.emplace(key, value);
            if (!result.second) {
                // 键已存在，累加值
                *(result.first) += value;
            }
        });
        
        return *this;
    }

    /**
     * @brief 减法操作 - 从当前Item中减去另一个Item
     */
    DistinctItemV2& operator-=(const DistinctItemV2& other) {
        _total -= other._total;
        
        other._map.traverse([this](const KeyType& key, const ValueType& value) {
            ValueType* value_ptr = _map.find(key);
            if (value_ptr) {
                *value_ptr -= value;
                if (*value_ptr <= 0) {
                    _map.remove(key);
                }
            }
        });
        
        return *this;
    }

    /**
     * @brief 获取不同key的数量（去重计数）
     */
    ValueType distinct_num() const {
        return static_cast<ValueType>(_map.size());
    }

    /**
     * @brief 获取总计数
     */
    ValueType total() const {
        return _total;
    }

    /**
     * @brief 获取内部map的const引用
     */
    const MapType& map() const {
        return _map;
    }

    /**
     * @brief 判断是否为空（用于滑动窗口清理）
     */
    bool is_null() const {
        return _total <= 0;
    }

    /**
     * @brief 序列化接口 - 与原版std::unordered_map兼容
     */
    template<typename Archive>
    bool serialize(Archive* ar) const {
        // 1. 写入_total
        if (!t_write<ValueType, Archive>(_total, ar)) {
            CWARNING_LOG("serialize _total failed");
            return false;
        }
        
        // 2. 写入map大小
        size_t map_size = _map.size();
        if (!t_write<size_t, Archive>(map_size, ar)) {
            CWARNING_LOG("serialize map_size failed");
            return false;
        }
        
        // 3. 遍历写入所有键值对
        bool success = true;
        _map.traverse([&](const KeyType& key, const ValueType& value) {
            if (!success) return;
            
            if (!t_write<KeyType, Archive>(key, ar) || 
                !t_write<ValueType, Archive>(value, ar)) {
                CWARNING_LOG("serialize map entry failed");
                success = false;
            }
        });
        
        return success;
    }

    /**
     * @brief 反序列化接口 - 标准接口，与原版兼容
     * @param ar 存档对象
     * @note 此时对象已通过带内存池的构造函数创建，_map已有效
     */
    template<typename Archive>
    bool deserialize(Archive* ar) {
        // 1. 读取_total
        if (!t_read<ValueType, Archive>(&_total, ar)) {
            CWARNING_LOG("deserialize _total failed");
            return false;
        }
        
        // 2. 读取map大小
        size_t map_size = 0;
        if (!t_read<size_t, Archive>(&map_size, ar)) {
            CWARNING_LOG("deserialize map_size failed");
            return false;
        }
        
        // 3. 清空并重建map
        _map.clear();
        for (size_t i = 0; i < map_size; ++i) {
            KeyType key;
            ValueType value;
            if (!t_read<KeyType, Archive>(&key, ar) || 
                !t_read<ValueType, Archive>(&value, ar)) {
                CWARNING_LOG("deserialize map entry failed");
                return false;
            }
            if (!_map.emplace(key, value).second) {
                CWARNING_LOG("deserialize map emplace failed");
                return false;
            }
        }
        
        return true;
    }

    /**
     * @brief 静态方法：提供slab大小配置信息
     */
    static void append_required_slab_sizes(std::vector<uint32_t>& slab_sizes) {
        // DistinctItemV2使用的PoolMiniHashMap节点大小：
        // sizeof(VAddr) + sizeof(uint64_t) + sizeof(int64_t) = 4 + 8 + 8 = 20字节
        slab_sizes.push_back(MapType::get_node_size());
    }

private:
    MapType _map;        // 基于内存池的HashMap，存储data_view_sign -> count映射
    ValueType _total;    // 总计数
};

/**
 * @brief CumulantItem的V2版本，支持统一的deserialize接口
 * 
 * 主要用于简单累加器和测试场景，与SlidingWindowV2配套使用
 */
template<typename Number>
class CumulantItemV2 {
public:
    CumulantItemV2() : _cumulant(0) {}
    
    CumulantItemV2(Number delta) : _cumulant(delta) {}

    const CumulantItemV2& operator=(Number value) {
        _cumulant = value;
        return *this;
    }

    CumulantItemV2& operator+=(const CumulantItemV2& rv) {
        _cumulant += rv.cumulant();
        return *this;
    }

    CumulantItemV2& operator-=(const CumulantItemV2& rv) {
        _cumulant -= rv.cumulant();
        return *this;
    }

    bool is_null() const {
        return _cumulant <= 0;
    }

    Number cumulant() const {
        return _cumulant;
    }
    
    /**
     * @brief 序列化接口 - 与原版CumulantItem兼容
     */
    template<typename Archive>
    bool serialize(Archive* ar) const {
        return t_write<Number, Archive>(_cumulant, ar);
    }

    /**
     * @brief V2版本反序列化接口 - 支持内存池参数（但忽略）
     * @param ar 存档对象
     * @param pool 内存池指针（CumulantItemV2不使用，为了接口统一）
     */
    template<typename Archive>
    bool deserialize(Archive* ar, void* pool) {
        (void)pool;  // 忽略内存池参数
        return t_read<Number, Archive>(&_cumulant, ar);
    }

private:
    Number _cumulant;
};

/**
 * @brief PairCumulantItem的V2版本，支持统一的deserialize接口
 * 
 * 主要用于RatioFeatureAccumulator，与SlidingWindowV2配套使用
 */
template<typename Number>
class PairCumulantItemV2 {
public:
    PairCumulantItemV2() : _first(0), _second(0) {}
    
    PairCumulantItemV2(Number a, Number b) : _first(a), _second(b) {}

    PairCumulantItemV2& operator+=(const PairCumulantItemV2& rv) {
        _first += rv.first();
        _second += rv.second();
        return *this;
    }

    PairCumulantItemV2& operator-=(const PairCumulantItemV2& rv) {
        _first -= rv.first();
        _second -= rv.second();
        return *this;
    }

    bool is_null() const {
        return (_first <= 0) && (_second <= 0);
    }

    Number first() const {
        return _first;
    }

    Number second() const {
        return _second;
    }
    
    /**
     * @brief 序列化接口 - 与原版PairCumulantItem兼容
     */
    template<typename Archive>
    bool serialize(Archive* ar) const {
        return t_write<Number, Archive>(_first, ar) 
            && t_write<Number, Archive>(_second, ar);
    }

    /**
     * @brief V2版本反序列化接口 - 支持内存池参数（但忽略）
     * @param ar 存档对象
     * @param pool 内存池指针（PairCumulantItemV2不使用，为了接口统一）
     */
    template<typename Archive>
    bool deserialize(Archive* ar, void* pool) {
        (void)pool;  // 忽略内存池参数
        return t_read<Number, Archive>(&_first, ar) 
            && t_read<Number, Archive>(&_second, ar);
    }

private:
    Number _first;
    Number _second;
};

/**
 * @brief ArrayItem的V2版本，支持统一的deserialize接口
 * 
 * 主要用于DistributionFeatureAccumulator，与SlidingWindowV2配套使用
 */
template<typename Number, int bucket_number>
class ArrayItemV2 {
public:
    ArrayItemV2() {
        for (int i = 0; i < bucket_number; i++) {
            _count[i] = 0;
        }
    }

    ArrayItemV2& operator+=(const ArrayItemV2& rv) {
        for (int i = 0; i < bucket_number; i++) {
            _count[i] += rv[i];
        }
        return *this;
    }

    ArrayItemV2& operator-=(const ArrayItemV2& rv) {
        for (int i = 0; i < bucket_number; i++) {
            _count[i] -= rv[i];
        }
        return *this;
    }

    bool is_null() const {
        bool any = false;
        for (int i = 0; i < bucket_number; i++) {
            if (_count[i] > 0) {
                any = true;
                break;
            }
        }
        return !any;
    }

    Number& operator[](int i) {
        if (i < 0 || i >= bucket_number) {
            CWARNING_LOG("param error, i=%d bucket_number=%d", i, bucket_number);
            throw std::out_of_range("invalid index");
        }
        return _count[i];
    }

    const Number& operator[](int i) const {
        if (i < 0 || i >= bucket_number) {
            CWARNING_LOG("param error, i=%d bucket_number=%d", i, bucket_number);
            throw std::out_of_range("invalid index");
        }
        return _count[i];
    }
    
    /**
     * @brief 序列化接口 - 与原版ArrayItem兼容
     */
    template<typename Archive>
    bool serialize(Archive* ar) const {
        for (int i = 0; i < bucket_number; i++) {
            if (!t_write<Number, Archive>(_count[i], ar)) {
                return false;
            }
        }
        return true;
    }

    /**
     * @brief V2版本反序列化接口 - 支持内存池参数（但忽略）
     * @param ar 存档对象
     * @param pool 内存池指针（ArrayItemV2不使用，为了接口统一）
     */
    template<typename Archive>
    bool deserialize(Archive* ar, void* pool) {
        (void)pool;  // 忽略内存池参数
        for (int i = 0; i < bucket_number; i++) {
            if (!t_read<Number, Archive>(&_count[i], ar)) {
                return false;
            }
        }
        return true;
    }

private:
    Number _count[bucket_number];
};

/**
 * @brief 各种ItemV2的typedef定义，用于替代原版Item
 */
typedef CumulantItemV2<int64_t> SegmentItemV2;
typedef PairCumulantItemV2<int64_t> RateItemV2;
template<int bucket_number>
using DistributionItemV2 = ArrayItemV2<int64_t, bucket_number>;

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

#endif  // ANTI_THEMIS_SLIDING_WINDOW_INC_ITEM_V2_H

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */