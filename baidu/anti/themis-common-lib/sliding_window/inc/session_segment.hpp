/***************************************************************************
 * 
 * Copyright (c) 2021 Baidu.com, Inc. All Rights Reserved
 * $Id$ 
 * 
 **************************************************************************/
 
/**
 * @file session_segment.hpp
 * <AUTHOR>
 **/

#ifndef ANTI_THEMIS_COMMON_LIB_SESSION_SEGMENT_HPP
#define ANTI_THEMIS_COMMON_LIB_SESSION_SEGMENT_HPP

#include <memory>
#include "click_segment.hpp"

namespace anti {
namespace themis {
namespace common_lib { 

template<typename Node>
class SessionSegment : public ClickSegment<Node> {
public:
    SessionSegment() : 
        _session_interval(0) {
        ClickSegment<Node>();
        ClickSegment<Node>::_segment_num = 1;
    }

    ~SessionSegment() {
        ClickSegment<Node>::uninit();
        _session_interval = 0;
    }

    bool init(int64_t segment_len, int64_t session_interval);

    virtual bool enter(const Node& node, const Node** res);

    template<typename Archive>
    bool serialize_no_pod_data(Archive* ar) const; 

    template<typename Archive>
    bool deserialize_no_pod_data(Archive* ar);
protected:
    int64_t _session_interval;

    bool _update_session_node(Node* l_node, const Node& r_node);

    template<typename Archive>
    bool _serialize_nodes(Archive* ar) const;

    template<typename Archive>
    bool _deserialize_nodes(Archive* ar, int64_t node_num);
};

template<typename Node>
bool SessionSegment<Node>::init(int64_t segment_len, int64_t session_interval) {
    ClickSegment<Node>::init(segment_len);
    _session_interval = session_interval;
    return true;
}

template<typename Node>
bool SessionSegment<Node>::enter(const Node& node, const Node** res) {
    if (!ClickSegment<Node>::_state_ok) {
        CFATAL_LOG("state not ok");
        return false;
    }
    if (node.coord > ClickSegment<Node>::_latest_coord) {
        ClickSegment<Node>::_latest_coord = node.coord;
    }

    if (!ClickSegment<Node>::_gc()) {
        CFATAL_LOG("gc fail, latest_coord=%ld node_num=%ld",
                ClickSegment<Node>::_latest_coord, ClickSegment<Node>::_node_num);
        // @note: continue
    }
    Node* find = ClickSegment<Node>::_find(node.sign);
    if (find == NULL) {
        find = ClickSegment<Node>::_add_node(node);
        if (find == NULL) {
            CWARNING_LOG("add new node fail, sign=%lu coord=%ld",
                    node.sign, node.coord);
            return false;
        }
    } else {
        _update_session_node(find, node);
    }

    if (res != NULL) {
        *res = find;
    }
    return true;
}

template<typename Node>
bool SessionSegment<Node>::_update_session_node(Node* l_node, const Node& r_node) {
    if (l_node == NULL) {
        CWARNING_LOG("l_node invalid");
        return false;
    }
    int64_t update_diff = r_node.coord - l_node->update_time();
    if (update_diff > _session_interval) {
        CDEBUG_LOG("the interval between two update > session len, delete old node and add new node to head.");
        Node* in_node = new (std::nothrow) Node(r_node);
        if (in_node == NULL) {
            CFATAL_LOG("new Node fail");
            return NULL;
        }
        //find old node delete and push in_node to _que
        ClickSegment<Node>::_move_head(*in_node);
    } else {
        l_node->add(r_node, 0);
    }
    return true;
}

template<typename Node>
template<typename Archive>
bool SessionSegment<Node>::_deserialize_nodes(Archive* ar, int64_t node_num) {
    if (!ar) {
        CFATAL_LOG("Archive ptr invalid");
        return false;
    }
    for (int64_t i = 0; i < node_num; ++i) {
        std::shared_ptr<Node> node(new (std::nothrow) Node());
        if (!node->deserialize(ar)) {
            CFATAL_LOG("readin a node fail");
            return false;
        }
        enter(*node, NULL);
    }
    return true; 
}

template<typename Node>
template<typename Archive>
bool SessionSegment<Node>::_serialize_nodes(Archive* ar) const {
    if (!ar) {
        CFATAL_LOG("Archive ptr invalid");
        return false;
    }
    typedef typename ClickSegment<Node>::FeaQueue::reverse_iterator RIter;
    for (RIter iter = ClickSegment<Node>::_que->rbegin(); iter != ClickSegment<Node>::_que->rend(); ++iter) {
        if (!(*iter)->serialize(ar)) {
            CWARNING_LOG("dump an node fail");
            return false;
        }
    }
    return true;
}

template<typename Node>
template<typename Archive>
bool SessionSegment<Node>::serialize_no_pod_data(Archive* ar) const {
    if (!ClickSegment<Node>::_state_ok) {
        CFATAL_LOG("state is not ok, no data to serialize");
        return false;
    }

    if (ar == NULL) {
        CFATAL_LOG("param error");
        return false;
    }
    MemberValue mv;
    mv.magic = MAGIC;
    mv.segment_len = ClickSegment<Node>::_segment_len;
    mv.node_num = ClickSegment<Node>::_que->size();
    if (!t_write<MemberValue, Archive>(mv, ar)) {
        CFATAL_LOG("write out memver value fail");
        return false;
    }
    return _serialize_nodes(ar);
}

template<typename Node>
template<typename Archive>
bool SessionSegment<Node>::deserialize_no_pod_data(Archive* ar) {
    if (!ClickSegment<Node>::_state_ok) {
        CFATAL_LOG("state is not ok, can  deserialize");
        return false;
    }

    if (ar == NULL) {
        CFATAL_LOG("param error");
        return false;
    }
    MemberValue mv;
    if (!t_read<MemberValue, Archive>(&mv, ar)) {
        CFATAL_LOG("readin memver value error");
        return false;
    }

    if (mv.magic != MAGIC) {
        CFATAL_LOG("magic not match, readin_magic=%lu", mv.magic);
        return false;
    }

    if (mv.segment_len != ClickSegment<Node>::_segment_len) {
        CFATAL_LOG("segment_len not match, readin_seg=%ld cur_segment_len=%ld",
                mv.segment_len, ClickSegment<Node>::_segment_len);
        return false;
    }
    return _deserialize_nodes(ar, mv.node_num);
}

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

#endif  // ANTI_THEMIS_COMMON_LIB_SESSION_SEGMENT_HPP

/* vim: set ts=4 sw=4 sts=4 tw=100 */
