/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * $Id$ 
 * 
 **************************************************************************/
 
/**
 * @file sliding_window/dev/item.hpp
 * <AUTHOR>
 * @date 2015/04/09 14:15:23
 * @version $Revision$ 
 * @brief 
 *  
 **/

#include "utils.h"
#include "item.h"

namespace anti {
namespace themis {
namespace common_lib {

template<typename Number>
template<typename Archive> 
bool CumulantItem<Number>::serialize(Archive* ar) const {
    return t_write<Number, Archive>(_cumulant, ar);
}

template<typename Number>
template<typename Archive> 
bool CumulantItem<Number>::deserialize(Archive* ar) {
    return t_read<Number, Archive>(&_cumulant, ar);
}

template<typename Number>
template<typename Archive> 
bool PairCumulantItem<Number>::serialize(Archive* ar) const {
    return t_write<Number, Archive>(_first, ar) 
            && t_write<Number, Archive>(_second, ar);
}

template<typename Number>
template<typename Archive> 
bool PairCumulantItem<Number>::deserialize(Archive* ar) {
    return t_read<Number, Archive>(&_first, ar)
            && t_read<Number, Archive>(&_second, ar);
}

template<typename Number, int bucket_number>
template<typename Archive>
bool ArrayItem<Number, bucket_number>::serialize(Archive* ar) const {
    return t_write<Number[bucket_number], Archive>(_count, ar);
}

template<typename Number, int bucket_number>
template<typename Archive>
bool ArrayItem<Number, bucket_number>::deserialize(Archive* ar) {
    return t_read<Number[bucket_number], Archive>(&_count, ar);
}

template<typename key_type, typename value_type>
template<typename Archive> 
bool KeyValueItem<key_type, value_type>::serialize(Archive* ar) const {
    return t_write<key_type, Archive>(_key, ar) 
            && t_write<value_type, Archive>(_value, ar);
}

template<typename key_type, typename value_type>
template<typename Archive> 
bool KeyValueItem<key_type, value_type>::deserialize(Archive* ar) {
    return t_read<key_type, Archive>(&_key, ar)
            && t_read<value_type, Archive>(&_value, ar);
}

template<typename Number, typename Node>
template<typename Archive>
bool TopkSet<Number, Node>::serialize(Archive* ar) const {
    if (!t_write<uint64_t, Archive>(_k, ar) || 
            !t_write<Number, Archive>(_total, ar)) {
        CWARNING_LOG("serialize _k or _total failed.");
        return false;
    }
    size_t map_size = _map.size();
    size_t set_size = _k_set.size();
    if (!t_write<size_t, Archive>(map_size, ar) || 
            !t_write<size_t, Archive>(set_size, ar)) {
        CWARNING_LOG("serialize map_size or set_size failed.");
        return false;
    }
    
    for (auto iter = _map.begin(); iter != _map.end(); ++iter) {
        if (!t_write<uint64_t, Archive>(iter->first, ar) ||
                !t_write<Number, Archive>(iter->second, ar)) {
            CWARNING_LOG("serialize sign->node map failed.");
            return false;
        }
    }
    for (auto iter = _k_set.begin(); iter != _k_set.end(); ++iter) {
        if (!iter->serialize(ar)) {
            CWARNING_LOG("serialize _k_set failed.");
            return false;
        }
    }
    return true;
}

template<typename Number, typename Node>
template<typename Archive>
bool TopkSet<Number, Node>::deserialize(Archive* ar) {
    _map.clear();
    _k_set.clear();
    size_t map_size = 0u;
    size_t set_size = 0u;
    if (!t_read<uint64_t, Archive>(&_k, ar) || 
            !t_read<Number, Archive>(&_total, ar) ||
            !t_read<size_t, Archive>(&map_size, ar) ||
            !t_read<size_t, Archive>(&set_size, ar)) {
        CWARNING_LOG("deserialize _k or _total or map_size or set_size failed.");
        return false;
    }
    for (size_t i = 0; i < map_size; ++i) {
        Number value;
        uint64_t sign;
        if (!t_read<uint64_t, Archive>(&sign, ar) ||
                !t_read<Number, Archive>(&value, ar)) {
            CWARNING_LOG("deserialize sign->value map failed.");
            return false;
        }
        if (!_map.emplace(sign, value).second) {
            CWARNING_LOG("deserialize insert sign->node map failed.");
            return false;
        }
    }

    for (size_t i = 0; i < set_size; ++i) {
        Node node;
        if (!node.deserialize(ar)) {
            CWARNING_LOG("deserialize sign->node map failed.");
            return false;
        }
        if (!_k_set.insert(node).second) {
            CWARNING_LOG("deserialize _k_set failed.");
            return false;
        }
    }
    return true;
}

template<typename Number>
template<typename Archive>
bool ConcentrationItem<Number>::serialize(Archive* ar) const {
    size_t map_size = _value_map.size();
    if (!t_write<uint64_t, Archive>(_cumulant, ar) || 
            !t_write<size_t, Archive>(map_size, ar)) {
        CWARNING_LOG("serialize _cumulant or map_size failed.");
        return false;
    }
    for (auto iter = _value_map.begin();
            iter != _value_map.end(); ++iter) {
        if (!t_write<uint64_t, Archive>(iter->first, ar) || 
                !t_write<Number, Archive>(iter->second, ar)) {
            CWARNING_LOG("serialize _value_map node failed.");
            return false;
        }
    }
    if (!_topk_heap.serialize(ar)) {
        CWARNING_LOG("serialize _topk_heap failed.");
        return false;
    }
    return true;
}

template<typename Number>
template<typename Archive>
bool ConcentrationItem<Number>::deserialize(Archive* ar) {
    _value_map.clear();
    _topk_heap.clear();
    size_t map_size = 0u;
    if (!t_read<Number, Archive>(&_cumulant, ar) || 
            !t_read<size_t, Archive>(&map_size, ar)) {
        CWARNING_LOG("deserialize _cumulant or map_size failed.");
        return false;
    }
    uint64_t first = 0;
    Number second = 0;
    for (size_t i = 0; i < map_size; ++i) {
        if (!t_read<uint64_t, Archive>(&first, ar) || 
                !t_read<Number, Archive>(&second, ar)) {
            CWARNING_LOG("deserialize _value_map node failed.");
            return false;
        }
        if (!_value_map.emplace(first, second).second) {
            CWARNING_LOG("deserialize _value_map node failed.");
            return false;
        }
    }

    if (!_topk_heap.deserialize(ar)) {
        CWARNING_LOG("deserialize _topk_heap failed.");
        return false;
    }
    return true;
}

template<typename Archive>
bool DistinctItem::serialize(Archive* ar) const {
    size_t map_size = _map.size();
    if (!t_write<uint64_t, Archive>(_total, ar) || 
            !t_write<size_t, Archive>(map_size, ar)) {
        CWARNING_LOG("serialize _total or map_size failed.");
        return false;
    }
    for (auto iter = _map.begin();
            iter != _map.end(); ++iter) {
        if (!t_write<uint64_t, Archive>(iter->first, ar) || 
                !t_write<int64_t, Archive>(iter->second, ar)) {
            CWARNING_LOG("serialize _map node failed.");
            return false;
        }
    }
    return true;
}

template<typename Archive>
bool DistinctItem::deserialize(Archive* ar) {
    _map.clear();
    size_t map_size = 0u;
    if (!t_read<int64_t, Archive>(&_total, ar) || 
            !t_read<size_t, Archive>(&map_size, ar)) {
        CWARNING_LOG("deserialize _total or map_size failed.");
        return false;
    }
    uint64_t first = 0;
    int64_t second = 0;
    for (size_t i = 0; i < map_size; ++i) {
        if (!t_read<uint64_t, Archive>(&first, ar) || 
                !t_read<int64_t, Archive>(&second, ar)) {
            CWARNING_LOG("deserialize _map node failed.");
            return false;
        }
        if (!_map.emplace(first, second).second) {
            CWARNING_LOG("deserialize insert _map node failed.");
            return false;
        }
    }
    return true;
}

template <typename LeafNode>
template<typename Archive>
bool TreeItem<LeafNode>::serialize(Archive* ar) const {
    size_t leaf_key_map_size = _root_map.size();
    if (!t_write<int64_t, Archive>(_total, ar) || 
            !t_write<size_t, Archive>(leaf_key_map_size, ar)) {
        CWARNING_LOG("serialize _total or map_size failed.");
        return false;
    }
    for (auto it = _root_map.begin(); 
            it != _root_map.end();
            ++it) {
        auto& node_ptr = it->second;
        if (!t_write<LeafKey, Archive>(it->first, ar) ||
                !node_ptr->serialize(ar)) { 
            CWARNING_LOG("serialize tree_node failed.");
            return false;
        }
    }
    return true;
}

template <typename LeafNode>
template<typename Archive>
bool TreeItem<LeafNode>::deserialize(Archive* ar) {
    _root_map.clear();
    size_t leaf_key_map_size = 0;
    if (!t_read<int64_t, Archive>(&_total, ar) || 
            !t_read<size_t, Archive>(&leaf_key_map_size, ar)) {
        CWARNING_LOG("deserialize _total or map_size failed.");
        return false;
    }
    LeafKey leaf_key = 0;
    for (size_t i = 0; i < leaf_key_map_size; ++i) {
        if (!t_read<LeafKey, Archive>(&leaf_key, ar)) {
            CWARNING_LOG("deserialize leaf_key failed.");
            return false;
        }
        LeafNodePtr ptr(new (std::nothrow) LeafNode());
        if (!ptr) {
            CWARNING_LOG("LeafNodePtr new failed!");
            return false;
        }
        if (!ptr->deserialize(ar)) {
            CWARNING_LOG("LeafNode deserialize failed!");
            return false;
        }
        _root_map.emplace(leaf_key, ptr);
    }
    return true;

}

template <typename Key, typename Value>
template <typename Archive>
bool MapNode<Key, Value>::serialize(Archive* ar) const {
    if (!t_write<size_t, Archive>(_map.size(), ar)) { 
        CWARNING_LOG("serialize MapNode size failed.");
        return false;
    }
    for (auto it = _map.begin(); it != _map.end(); ++it) {
        if (!t_write<Key, Archive>(it->first, ar) || 
                !t_write<Value, Archive>(it->second, ar)) {
            CWARNING_LOG("serialize MapNode _map failed.");
            return false;
        }
    }
    return true;
}

template <typename Key, typename Value>
template <typename Archive>
bool MapNode<Key, Value>::deserialize(Archive* ar) {
    size_t map_size = 0U;
    if (!t_read<size_t, Archive>(&map_size, ar)) { 
        CWARNING_LOG("deserialize map size failed.");
        return false;
    }
    Key sign = 0;
    Value value = 0;
    for (size_t idx = 0; idx < map_size; ++idx) {
        if (!t_read<Key, Archive>(&sign, ar)
                || !t_read<Value, Archive>(&value, ar)) { 
            CWARNING_LOG("deserialize map node sign or value failed.");
            return false;
        }
        _map.emplace(sign, value);
    }
    return true;
}

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4 sts=4 tw=100 */
