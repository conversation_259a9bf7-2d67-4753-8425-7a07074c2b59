// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: click_node.hpp
// @Last modified: 2015-09-24 11:16:56
// @Brief: 

#ifndef ANTI_THEMIS_COMMON_LIB_CLICK_NODE_HPP
#define ANTI_THEMIS_COMMON_LIB_CLICK_NODE_HPP

#include <string>
#include <cmath>
#include <com_log.h>

namespace anti {
namespace themis {
namespace common_lib { 

struct SegmentInfo {
    static const uint64_t MAGIC = 0xC29FA627495AFA51;
    static const int64_t MAX_RESERVE_LEN = 128;
    int64_t segment_len;
    int64_t segment_num;
    int64_t upper;
    double lamda;
    int64_t node_num;
    uint64_t magic;
    char reserve[MAX_RESERVE_LEN];

    SegmentInfo() :
            segment_len(0L),
            segment_num(0L),
            upper(0L),
            lamda(0.0),
            node_num(0L),
            magic(MAGIC) {
            memset(reserve, 0, sizeof(char) * MAX_RESERVE_LEN);
    }

    SegmentInfo (const SegmentInfo& info) {
        segment_len = info.segment_len;
        segment_num = info.segment_num;
        node_num = info.node_num;
        upper = info.upper;
        lamda = info.lamda;
        magic = info.magic;
        memcpy(reserve, info.reserve, sizeof(info.reserve));
    }   

    SegmentInfo(int64_t seg_len, int64_t seg_num, int64_t up, double la) :
            segment_len(seg_len),
            segment_num(seg_num),
            upper(up),
            lamda(la),
            node_num(0L),
            magic(MAGIC){
            memset(reserve, 0, sizeof(char) * MAX_RESERVE_LEN); 
    }

    SegmentInfo& operator = (const SegmentInfo& info) {
        segment_len = info.segment_len;
        segment_num = info.segment_num;
        node_num = info.node_num;
        upper = info.upper;
        lamda = info.lamda;
        magic = info.magic;
        memcpy(reserve, info.reserve, sizeof(info.reserve));
        return *this;
    }   
};

template<typename T>
struct SegNodeTmpl {
    uint64_t sign;
    int64_t coord;

    int64_t cumulant;
    T last_cumulant;

    SegNodeTmpl() : 
            sign(0U),
            coord(0L),
            cumulant(0L),
            last_cumulant(0){
    }

    SegNodeTmpl(uint64_t si, int64_t co) :
            sign(si),
            coord(co),
            cumulant(1L),
            last_cumulant(0) {
    }

    void add(const SegNodeTmpl<T>& node, int64_t segment_len) {
        if (sign != node.sign) {
            CFATAL_LOG("add node's sign not match, sign_l=%lu sign_r=%lu",
                    sign, node.sign);
            return ;
        }
        if (node.coord >= coord + 2 * segment_len) {
            last_cumulant = 0;
            cumulant = 0L;
            coord = node.coord;
        } else if (node.coord >= coord + segment_len) {
            last_cumulant = cumulant;
            cumulant = 0L;
            coord += segment_len;
        }
        cumulant += node.cumulant;
        return;
    }

    void add(const SegNodeTmpl<T>& node, const SegmentInfo& info) {
        if (sign != node.sign) {
            CFATAL_LOG("add node's sign not match, sign_l=%lu sign_r=%lu",
                    sign, node.sign);
            return;
        }
        if (info.segment_len == 0 || info.segment_num < 1) {
            CFATAL_LOG("invalid segment_len[%ld] or invalid segment_num[%ld]", 
                    info.segment_len, info.segment_num);
            return;
        }
         
        int64_t cross_seg_num = (node.coord - coord) / info.segment_len;
        if (cross_seg_num < 1) {
            // in the same segment
            cumulant += node.cumulant;
        } else if (cross_seg_num >= 1 && cross_seg_num < info.segment_num) {
            // in (1, N) segment
            // jump 1 segment first
            T tmp_last = last_cumulant * 1.0 * info.lamda + cumulant;
            // jump cross_seg_num -1 segments
            // if cross_seg_num is 1 then value of pow is 1
            tmp_last *= pow(info.lamda, (cross_seg_num -1) * 1.0);
            tmp_last = tmp_last > info.upper ? info.upper : tmp_last;
            // one more for accumulator
            last_cumulant = tmp_last * info.lamda;
            cumulant = node.cumulant;
            coord = node.coord;
        } else {
            // cross_seg_num >= info.segment_num
            cumulant = node.cumulant;
            last_cumulant = 0;
            coord = node.coord;
        }
        return; 
    }
};
typedef SegNodeTmpl<int64_t> SegLimitNode;
typedef SegNodeTmpl<double> SegBlackNode;

struct RateLimitNode {
    uint64_t sign;
    int64_t coord;

    int64_t fit_cumulant;
    int64_t ref_cumulant;

    RateLimitNode() : 
            sign(0U),
            coord(0L),
            fit_cumulant(0L),
            ref_cumulant(0L) {
    }

    RateLimitNode(uint64_t si, int64_t co, int64_t fit, int64_t ref) :
            sign(si),
            coord(co),
            fit_cumulant(fit),
            ref_cumulant(ref) {
    }

    void add(const RateLimitNode& node, int64_t segment_len) {
        if (sign != node.sign) {
            CFATAL_LOG("add node's sign not match, sign_l=%lu sign_r=%lu",
                    sign, node.sign);
            return ;
        }
            
        if (node.coord >= coord + segment_len) {
            fit_cumulant = 0L;
            ref_cumulant = 0L;
            if (node.coord >= coord + 2 * segment_len) {
                coord = node.coord;
            } else {
                coord += segment_len;
            }
        }
        fit_cumulant += node.fit_cumulant;
        ref_cumulant += node.ref_cumulant;
        return ;
    }
};

struct DistributionLimitNode {
    uint64_t sign;
    int64_t coord;

    static const uint32_t MAX_BUCKET_NUM = 10U;
    int64_t buckets[MAX_BUCKET_NUM];

    DistributionLimitNode() : sign(0LU), coord(0L) {
        memset(buckets, 0, sizeof(int64_t) * MAX_BUCKET_NUM);
    }

    DistributionLimitNode(uint64_t si, int64_t co, uint32_t idx, int64_t val) :
            sign(si),
            coord(co) {
        memset(buckets, 0, sizeof(int64_t) * MAX_BUCKET_NUM);
        if (idx >= MAX_BUCKET_NUM) {
            CFATAL_LOG("invalid idx(%u) >= MAX_BUCKET_NUM(%u)", idx, MAX_BUCKET_NUM);
            return;
        }
        buckets[idx] = val;
    }

    void add(const DistributionLimitNode& node, int64_t segment_len) {
        if (sign != node.sign) {
            CFATAL_LOG("add node's sign not match, sign_l=%lu sign_r=%lu",
                    sign, node.sign);
            return ;
        }

        if (node.coord >= coord + 2 * segment_len) {
            memset(buckets, 0, sizeof(int64_t) * MAX_BUCKET_NUM);
            coord = node.coord;
        } else if (node.coord >= coord + segment_len) {
            memset(buckets, 0, sizeof(int64_t) * MAX_BUCKET_NUM);
            coord += segment_len;
        }

        for (uint32_t i = 0U; i < MAX_BUCKET_NUM; ++i) {
            buckets[i] += node.buckets[i];
        }
        return ;
    }
};

struct RatioBlackNode {
    uint64_t sign;
    int64_t coord;

    int64_t fit_cumulant;
    int64_t ref_cumulant;
    int64_t last_fit_cumulant;
    double last_ratio;

    RatioBlackNode() :
            sign(0U),
            coord(0L),
            fit_cumulant(0L),
            ref_cumulant(0L),
            last_fit_cumulant(0L),
            last_ratio(0){
    }

    RatioBlackNode(uint64_t si, int64_t co, int64_t fit, int64_t ref) : 
            sign(si),
            coord(co),
            fit_cumulant(fit),
            ref_cumulant(ref),
            last_fit_cumulant(0L),
            last_ratio(0) {
    }
    
    void add(const RatioBlackNode& node, int64_t segment_len) {
        if (sign != node.sign) {
            CFATAL_LOG("add node's sign not match, sign_l=%lu sign_r=%lu", sign, node.sign);    
            return;
        }

        if (node.coord >= coord + segment_len) {
            CTRACE_LOG("fit_cumulant:%lu, ref_cumulant:%lu", fit_cumulant, ref_cumulant);
            if (node.coord >= coord + 2 * segment_len) {
                coord = node.coord;
                last_fit_cumulant = 0;
                last_ratio = 0;
            } else {
                last_ratio = (ref_cumulant != 0) ? (fit_cumulant * 1.0 / ref_cumulant) : 0;
                last_fit_cumulant = fit_cumulant;
                coord += segment_len;
            }
            fit_cumulant = 0;
            ref_cumulant = 0;
        }
        fit_cumulant += node.fit_cumulant;
        ref_cumulant += node.ref_cumulant;
    }
};

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

#endif // ANTI_THEMIS_COMMON_LIB_CLICK_NODE_HPP
/* vim: set ts=4 sw=4: */

