// Copyright (c) 2025 Baidu.com, Inc. All Rights Reserved
// @Author: l<PERSON><PERSON><PERSON><PERSON>(<EMAIL>)

#ifndef ANTI_THEMIS_COMMON_LIB_SLIDING_WINDOW_V2_H
#define ANTI_THEMIS_COMMON_LIB_SLIDING_WINDOW_V2_H

#include <deque>
#include <list>
#include <unordered_map>
#include <com_log.h>

#include "pool_hash_map.hpp"
#include "gc_slab_mempool_32.h"
#include "continue_coordinate.h"

namespace anti {
namespace themis {
namespace common_lib {

enum { SLIDING_WINDOW_V2_SLIDE = 0xfffe, SLIDING_WINDOW_V2_SUCC, SLIDING_WINDOW_V2_FAIL };

template <typename Item>
class SlidingWindowV2 {
public:
    // 默认构造函数
    SlidingWindowV2();

    ~SlidingWindowV2();

    // 自动创建内存池的init接口 - 兼容原版SlidingWindow用法
    bool init(int64_t step_len, int max_step_num, int64_t segment_len);

    // 使用外部内存池的init接口 - 新增功能
    bool init(int64_t step_len, int max_step_num, int64_t segment_len, GCSlabMempool32* mem_pool);

    // @brief clear data and delete member
    void uninit();

    // enter and return whether it slide
    int insert(const uint64_t key, const int64_t coord, const Item& item);

    int insert(const uint64_t key, const int64_t coord, const Item& item, const Item** pp_seg_item);

    bool enter(const uint64_t key, const int64_t coord, const Item& item);

    bool enter(const uint64_t key, const int64_t coord, const Item& item, const Item** pp_seg_item);

    const Item* query_segment(const uint64_t key) const;
    const Item* query_segment(const uint64_t key, int64_t coord) const;

    Item query_last_segment(const uint64_t key) const;

    int64_t latest_coord() const {
        return _latest_coord;
    }

    int64_t step_length() const {
        return _step_len;
    }

    uint64_t window_view_sign_size();

    uint64_t segment_view_sign_size();

    int64_t segment_start_coord() const {
        return _segment_start_coord;
    }

    int32_t step_num() const {
        return _step_num;
    }

    void set_log_key_first_appear();
    int64_t query_start_coord(const uint64_t key) const;

    template <typename Archive>
    bool serialize(Archive* ar) const;

    template <typename Archive>
    bool deserialize(Archive* ar);

    void monitor(bsl::var::Dict& dict, bsl::ResourcePool& rp) const;

    int64_t sliding_segment_start_coord(int64_t coord, int64_t step_len, int64_t step_num) {
        return coord - coord % step_len - step_len * (step_num - 1);
    }

    /**
     * @brief 收集SlidingWindow所需的内存池slab大小
     * @param slab_sizes 用于收集slab大小的vector
     */
    static void append_required_slab_sizes(std::vector<uint32_t>& slab_sizes) {
        // SlidingWindowV2内部使用StepContainer (PoolHashMap)
        slab_sizes.push_back(StepContainer::get_node_size());
    }

private:
    using StepContainer = PoolHashMap<uint64_t, Item, GCSlabMempool32>;
    using WindowContainer = std::deque<StepContainer*>;
    using SignStartCoord = std::unordered_map<uint64_t, ContinueCoordinate>;
    using VAddr = typename GCSlabMempool32::TVaddr;

    bool _check_segment_arg(int64_t step_len, int max_step_num, int64_t segment_len) const;

    // 内部共用的初始化逻辑
    bool _init_common(int64_t step_len, int max_step_num, int64_t segment_len);

    bool _create_window_steps();

    bool _init_create_window_steps();

    bool _find(
            const uint64_t key,
            const int64_t coord,
            Item** p_step_item,
            Item** p_segment_item,
            int* step_pos);

    // @retval: the coord of slide, mulitple of step_len
    int64_t _slide_to_coord(int64_t coord);

    // @brief: clear data but not delete member
    void _clear();

    void _slide_one_step();

    void _move_and_clear_oldest_step();

    void _delete_first_step_in_segment();

    StepContainer* _first_step_in_segment() const;

    StepContainer* _find_step(int64_t coord) const;

    int64_t _step_start_coord(int64_t coord) const;

    bool _push_item(
            StepContainer* step,
            const uint64_t key,
            const Item& item,
            const Item** pp_item);

    const Item* _query(const StepContainer& from, const uint64_t key) const;

    bool _in_segment(const int64_t coord) const;

    template <typename Archive>
    bool _serialize_step(const StepContainer& step, Archive* ar) const;

    uint64_t _calculate_overhead_memory() const;

private:
    GCSlabMempool32* _mem_pool;  // 内存池指针
    bool _own_mem_pool;          // 标记内存池是: 自己创建/外部传入
    int64_t _step_len;
    int32_t _step_num;
    int _max_step_num;
    int64_t _segment_len;
    int64_t _oldest_coord;
    int64_t _segment_start_coord;
    int64_t _latest_coord;
    StepContainer* _segment;
    WindowContainer* _window;
    SignStartCoord* _sign_start_coord;
    static constexpr uint64_t _MAGIC_NUMBER = 0xC29FA627495AFA51;

    struct SegmentConf {
        uint64_t magic;
        int64_t step_len;
        int64_t segment_len;
        int32_t step_num;
    };
};

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

#include "sliding_window_v2.hpp"

#endif  // ANTI_THEMIS_COMMON_LIB_SLIDING_WINDOW_V2_H