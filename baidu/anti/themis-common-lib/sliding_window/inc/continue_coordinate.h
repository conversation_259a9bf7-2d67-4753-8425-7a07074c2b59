// Copyright (c) 2025 Baidu.com, Inc. All Rights Reserved
// @Author: knzeus(<EMAIL>)

#ifndef ANTI_THEMIS_COMMON_LIB_CONTINUE_COORDINATE_H
#define ANTI_THEMIS_COMMON_LIB_CONTINUE_COORDINATE_H

#include <list>
#include <cstdint>

namespace anti {
namespace themis {
namespace common_lib {

/*
 * ContinueCoordinate store list of coordinates;
 * item is like [coord, cnt] means Continue coordinates
 * [coord, coord+1,,,coord + cnt-1]
 */
class ContinueCoordinate {
public:
    ContinueCoordinate() {}

    ContinueCoordinate(int64_t coord) {
        CoordPoint point;
        point.coord = coord;
        point.next_cnt = 0;
        _coords.push_back(point);
    }

    void push(int64_t coord, int64_t step_len) {
        if (_coords.size() != 0) {
            CoordPoint& it = _coords.back();
            if (coord < it.farthest_coord(step_len)) {
                return;
            }
            if (coord == it.farthest_coord(step_len)) {
                it.next_cnt++;
                return;
            }
        }
        CoordPoint point;
        point.coord = coord;
        point.next_cnt = 0;
        _coords.push_back(point);
    }

    void erase(int64_t coord, int64_t step_len) {
        if (_coords.size() == 0u) {
            return;
        }
        if (_coords.front().coord > coord) {
            return;
        }
        if (_coords.front().next_cnt == 0) {
            _coords.pop_front();
        } else {
            _coords.begin()->coord += step_len;
            _coords.begin()->next_cnt--;
        }
    }

    int64_t first_coord() const {
        if (_coords.size() == 0u) {
            return -1;
        }
        return _coords.front().coord;
    }

private:
    struct CoordPoint {
        int64_t coord;
        int64_t next_cnt;

        int64_t farthest_coord(int64_t step_len) {
            return coord + (next_cnt + 1) * step_len;
        }
    };

    std::list<CoordPoint> _coords;
};

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

#endif  // ANTI_THEMIS_COMMON_LIB_CONTINUE_COORDINATE_H