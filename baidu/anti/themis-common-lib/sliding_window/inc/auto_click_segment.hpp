// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: auto_click_segment.hpp
// @Last modified: 2015-09-22 18:53:14
// @Brief: 

#include "click_segment.hpp" 
namespace anti {
namespace themis {
namespace common_lib {

template<typename Node> 
class AutoClickSegment : public ClickSegment<Node> {
public:
    AutoClickSegment() {}

    ~AutoClickSegment() {
        ClickSegment<Node>::uninit();
    }

    bool init(const SegmentInfo& seg_info);

    const SegmentInfo& segment_info() const {
        return _info;
    }

    template<typename Archive>
    bool serialize(Archive* ar) const;

    template<typename Archive>
    bool deserialize(Archive* ar);

protected:
    virtual bool _update_node(Node* l_node, const Node& r_noede);

protected:
    SegmentInfo _info;
};  

template<typename Node>
bool AutoClickSegment<Node>::init(const SegmentInfo& seg_info) {
    _info = seg_info;
    if(!ClickSegment<Node>::init(seg_info.segment_len)) {
        CFATAL_LOG("call ClickSegment init in AutoClickSegment init fail,"
                "segment_len[%ld],segment_num[%ld], upper[%lu], lamda[%f]", 
                seg_info.segment_len, seg_info.segment_num, seg_info.upper, seg_info.lamda);
        return false;
    }
    ClickSegment<Node>::_segment_num = seg_info.segment_num;
    return true;
}

template<typename Node>
bool AutoClickSegment<Node>::_update_node(Node* l_node, const Node& r_node) {
    if (l_node == NULL) {
        CFATAL_LOG("call l_node invalid");
        return false;
    }
    l_node->add(r_node, _info);
    return true;    
}

template<typename Node>
template<typename Archive>
bool AutoClickSegment<Node>::serialize(Archive* ar) const {  
    if (!ClickSegment<Node>::_state_ok) {
        CFATAL_LOG("state is not ok, no data to serialize");
        return false;
    }
    if (ar == NULL) {
        CFATAL_LOG("param error");
        return false;
    }
    SegmentInfo info(_info);
    info.node_num = ClickSegment<Node>::_node_num;
    if (!t_write<SegmentInfo, Archive>(info, ar)) {
        CFATAL_LOG("write out memver value fail");
        return false;
    }
    return ClickSegment<Node>::_serialize_nodes(ar);
}

template<typename Node>
template<typename Archive>
bool AutoClickSegment<Node>::deserialize(Archive* ar) {
    if (!ClickSegment<Node>::_state_ok) {
        CFATAL_LOG("state is not ok, can  deserialize");
        return false;
    }
    if (ar == NULL) {
        CFATAL_LOG("param error");
        return false;
    }
    SegmentInfo info;
    if (!t_read<SegmentInfo, Archive>(&info, ar)) {
        CFATAL_LOG("readin memver value error");
        return false;
    }
    if (info.magic != SegmentInfo::MAGIC) {
        CFATAL_LOG("magic not match, readin_magic=%lu", info.magic);
        return false;
    }
    if (info.segment_len != _info.segment_len) {
        CFATAL_LOG("segment_len not match, readin_seg=%ld,cur_segment_len=%ld",
                info.segment_len, _info.segment_len);
        return false;
    }
    if (info.segment_num != _info.segment_num) {
        CFATAL_LOG("segment_num not match, readin_num=%ld,cur_segment_num=%ld",
                info.segment_num, _info.segment_num);
        return false;
    }
    return ClickSegment<Node>::_deserialize_nodes(ar, info.node_num);
}

} // namespace common_lib
} // namespace themis
} // namespace anti

/* vim: set ts=4 sw=4: */

