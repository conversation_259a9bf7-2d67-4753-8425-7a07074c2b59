/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * $Id$ 
 * 
 **************************************************************************/
 
/**
 * @file goe_list.hpp
 * <AUTHOR>
 * @date 2015/08/25 15:39:35
 * @version $Revision$ 
 * @brief: list ordered by key
 *         streaming node cache, expired node will be delete
 *         node has coord and uniq_id.
 *  
 **/

#ifndef ANTI_THEMIS_COMMON_LIB_GOE_LIST_HPP
#define ANTI_THEMIS_COMMON_LIB_GOE_LIST_HPP

#include <string>
#include <list>
#include <unordered_map>
#include <algorithm>
#include <memory>
#include <com_log.h>
#include "utils.h"

namespace anti {
namespace themis {
namespace common_lib { 

// must has valid copy constructor function
class NodeDemo {
public:
    // required funcs
    // feature sign
    uint64_t sign() const;

    // distinguish node that with same feature_sign
    uint64_t id() const;

    // coord for stream processing. Generally pv_time or pv_coord
    int64_t coord() const;

    NodeDemo& operator+=(const NodeDemo&);

    template<typename Archive>
    bool serialize(Archive* ar) const;

    template<typename Archive>
    bool deserialize(Archive* ar);
};

// GroupOrdered, Expired, List
// group by sign, ordered by coord in group, 
// scoped ordered by max coord among groups
// e.g. 
//    <sign:1, id:101, coord:5> -> <sign:1, id:102, coord:7> ->| 
//    <sign:2, id:201, coord:2> -> <sign:2, id:202, coord:9> ->|
//    <sign:3, id:301, coord:11> ->|
//    <sign:4, id:401, coord:10> ->|
//    <sign:5, id:501, coord:12> ->|
//    ->|
template<typename Node>
class GOEList {
public:
    typedef std::list<Node> IdOrderedList;
    typedef std::shared_ptr<IdOrderedList> IdOrderedListPtr;
    typedef std::list<IdOrderedListPtr> SignLRUList;

    // expired_len: node.coord < latest_coord - expired_len, will be delete
    // id_list_max_size: beyond will be discard
    GOEList(int64_t expired_len, int64_t id_list_max_size);

    virtual ~GOEList();

    // return the first node of {sign}node
    IdOrderedListPtr query(uint64_t sign) const;

    IdOrderedListPtr insert(const Node& in_node);

    template<typename Archive>
    bool serialize(Archive* ar) const;

    template<typename Archive>
    bool deserialize(Archive* ar);

private:
    int64_t _expired_len;  // expired node will be delete
    int64_t _id_list_max_size;
    int64_t _node_num;
    int64_t _latest_coord;

    typedef typename IdOrderedList::iterator IdIter;
    typedef typename SignLRUList::iterator SignIter;

    typedef std::unordered_map<uint64_t, SignIter> SignIndex;
    typedef typename SignIndex::iterator IndexIter;

    SignLRUList* _sign_lru_list;
    SignIndex* _sign_index;

private:
    void _gc_expired_sign();

    IdOrderedListPtr _insert_new_sign(const Node& node);

    IdOrderedListPtr _insert_old_sign(SignIter id_list, const Node& node);
    IdIter _insert_node_list(IdOrderedListPtr list_ptr, const Node& node);
};

template<typename Node>
GOEList<Node>::GOEList(int64_t expired_len, int64_t id_list_max_size) :
        _expired_len(expired_len > 0 ? expired_len : 1),
        _id_list_max_size(id_list_max_size > 0 ? id_list_max_size : 32),
        _node_num(0L),
        _latest_coord(0L),
        _sign_lru_list(NULL),
        _sign_index(NULL) {
    _sign_lru_list = new (std::nothrow) SignLRUList();
    _sign_index = new (std::nothrow) SignIndex();
}

template<typename Node>
GOEList<Node>::~GOEList() {
    if (_sign_index != NULL) {
        _sign_index->clear();
        delete _sign_index;
        _sign_index = NULL;
    }
    if (_sign_lru_list != NULL) {
        _sign_lru_list->clear();
        delete _sign_lru_list;
        _sign_lru_list = NULL;
    }
}

template<typename Node>
typename GOEList<Node>::IdOrderedListPtr GOEList<Node>::query(uint64_t sign) const {
    if (_sign_lru_list == NULL || _sign_index == NULL || _node_num == 0) {
        CFATAL_LOG("zero node or not init sign_index, node_num=%ld"
                " sign_index=%p", _node_num, _sign_index);
        return NULL;
    }
    auto it = _sign_index->find(sign);
    if (it == _sign_index->end()) {
        return NULL;
    }
    return *(it->second);
}

template<typename Node>
typename GOEList<Node>::IdOrderedListPtr GOEList<Node>::insert(const Node& node) {
    if (_sign_index == NULL) {
        CFATAL_LOG("sign index is null");
        return NULL;
    }

    if (node.coord() > _latest_coord) {
        _latest_coord = node.coord();
        _gc_expired_sign();
    }

    auto it = _sign_index->find(node.sign());
    if (it == _sign_index->end()) {
        return _insert_new_sign(node);
    } else {
        return _insert_old_sign(it->second, node);
    }
}

template<typename Node>
void GOEList<Node>::_gc_expired_sign() {
    if (_sign_index == NULL || _sign_lru_list == NULL || _node_num == 0) {
        return ;
    }
    int64_t expired_coord = _latest_coord - _expired_len;
    for (auto it = _sign_lru_list->begin(); it != _sign_lru_list->end();) {
        auto list_ptr = *it;
        if (list_ptr->size() == 0u || list_ptr->front().coord() < expired_coord) {
            _sign_index->erase(list_ptr->front().sign());
            it = _sign_lru_list->erase(it);
            _node_num--;
        } else {
            break;
        }
    }
}

template<typename Node>
typename GOEList<Node>::IdOrderedListPtr GOEList<Node>::_insert_new_sign(const Node& node) {
    IdOrderedListPtr node_list(new (std::nothrow) IdOrderedList());
    // alloc list failed, return null-list directly!
    if (!node_list) {
        CFATAL_LOG("alloc node_list failed, check memory!");
        return node_list;
    }

    node_list->push_back(node);
    _sign_lru_list->push_back(node_list);
    (*_sign_index)[node.sign()] = (--_sign_lru_list->end());

    _node_num++;
    return node_list;
}

template<typename Node>
typename GOEList<Node>::IdOrderedListPtr GOEList<Node>::_insert_old_sign(SignIter ptr, const Node& node) {
    auto list_ptr = *ptr;
    auto list_ptr_iter = _insert_node_list(list_ptr, node);

    // if insert result don't reach the end of the ordered list!
    // and the size of the list is more long!
    // need gc the ordered list!
    if (list_ptr_iter != list_ptr->end() && list_ptr->size() > _id_list_max_size) {
        int64_t expired_coord = _latest_coord - _expired_len;
        for (; list_ptr_iter != list_ptr->end(); ++list_ptr_iter) {
            if (list_ptr_iter->coord() < expired_coord) {
                list_ptr->erase(list_ptr_iter, list_ptr->end());
                break;
            }
        }
    }

    // adjust LRU list
    _sign_lru_list->erase(ptr);
    _sign_lru_list->push_back(list_ptr);
    (*_sign_index)[node.sign()] = (--_sign_lru_list->end());

    return list_ptr;
}

template<typename Node>
typename GOEList<Node>::IdIter GOEList<Node>::_insert_node_list(IdOrderedListPtr list_ptr, const Node& node) {
    // list is ordered decrease!
    auto it = list_ptr->begin();
    for (; it != list_ptr->end(); ++it) {
        if (it->coord() > node.coord()) { continue; }
        else if (it->coord() == node.coord()) { (*it) += node; }
        else { list_ptr->insert(it, node); }

        break;  // after insert, break the loop!
    }
    if (it == list_ptr->end()) {
        list_ptr->insert(it, node);
        return list_ptr->end();
    } else {
        return it;
    }
}

template<typename Node>
template<typename Archive>
bool GOEList<Node>::serialize(Archive* ar) const {
    // write Cache Length
    if (!t_write<int64_t, Archive>(_expired_len, ar)) {
        CFATAL_LOG("dump expired_len failed");
        return false;
    }

    // write list_max_size
    if (!t_write<int64_t, Archive>(_id_list_max_size, ar)) {
        CFATAL_LOG("dump list max size failed");
        return false;
    }

    // write each node! 
    uint64_t total_node_num = 0;
    std::for_each(_sign_lru_list->begin(), _sign_lru_list->end(),
            [&] (IdOrderedListPtr list_ptr) {
                total_node_num += list_ptr->size();
            });
    if (!t_write<uint64_t, Archive>(total_node_num, ar)) {
        CFATAL_LOG("dump total node num failed");
        return false;
    }

    for (auto iter = _sign_lru_list->begin();
            iter != _sign_lru_list->end(); ++iter) {
        auto list_ptr = *iter;
        for (auto list_iter = list_ptr->begin();
                list_iter != list_ptr->end(); ++list_iter) {
            if (!list_iter->serialize(ar)) {
                CFATAL_LOG("dump node failed");
                return false;
            }
        }
    }

    return true;
}

template<typename Node>
template<typename Archive>
bool GOEList<Node>::deserialize(Archive* ar) {
    if (!t_read<int64_t, Archive>(&_expired_len, ar)) {
        CFATAL_LOG("read expired_len failed");
        return false;
    }

    if (!t_read<int64_t, Archive>(&_id_list_max_size, ar)) {
        CFATAL_LOG("read list max size failed");
        return false;
    }

    uint64_t total_num;
    if (!t_read<uint64_t, Archive>(&total_num, ar)) {
        CFATAL_LOG("read total_num failed");
        return false;
    }

    while (total_num != 0) {
        Node n;
        if (!n.deserialize(ar)) {
            CFATAL_LOG("read node failed");
            return false;
        }
        if (!insert(n)) {
            CFATAL_LOG("insert node failed");
            return false;
        }
        total_num--;
    }
    return true;
}

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

#endif  // ANTI_THEMIS_COMMON_LIB_GOE_LIST_HPP

/* vim: set ts=4 sw=4 sts=4 tw=100 */
