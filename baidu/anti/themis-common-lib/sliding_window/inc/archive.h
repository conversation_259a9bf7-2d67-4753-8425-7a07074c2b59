/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * $Id$ 
 * 
 **************************************************************************/
 
/**
 * @file sliding_window/dev/archive.h
 * <AUTHOR>
 * @date 2015/03/31 14:32:10
 * @version $Revision$ 
 * @brief 
 *  
 **/

#ifndef ANTI_THEMIS_COMMON_LIB_SLIDING_WINDOW_ARCHIVE_H
#define ANTI_THEMIS_COMMON_LIB_SLIDING_WINDOW_ARCHIVE_H

#include <string>

namespace anti {
namespace themis {
namespace common_lib {

int my_open(const char* file, int flags, mode_t mode);
int my_close(int fd);

// @brief: the interface of the template class of SlidingWindow
class FileArchive {
public:
    FileArchive() : _filepath(""), _fd(-1), _is_write(false) {
    }

    ~FileArchive() {
        close();
    }

    bool open_r(const char* filepath);
    bool open_w(const char* filepath);

    bool close();

    void flush();

    // @param dat: the buf to load data of read
    // @param count: the size expect to read
    // @retval: actual read data len
    int read(void* dat, size_t count); 

    // @param dat: the data need to write
    // @param count: the size need to write
    // @retval: write data len
    int write(const void* dat, size_t count); 

private:
    bool _open(const char* filepath, int flags, mode_t mode);

private:
    std::string _filepath;
    int _fd;
    bool _is_write;
};

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

#endif  // ANTI_THEMIS_COMMON_LIB_SLIDING_WINDOW_ARCHIVE_H

/* vim: set ts=4 sw=4 sts=4 tw=100 */
