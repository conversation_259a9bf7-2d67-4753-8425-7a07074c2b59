/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * $Id$ 
 * 
 **************************************************************************/
 
/**
 * @file click_segment.hpp
 * <AUTHOR>
 * @date 2015/06/29 17:28:34
 * @version $Revision$ 
 * @brief 
 *  
 **/

#ifndef ANTI_THEMIS_COMMON_LIB_CLICK_SEGMENT_HPP
#define ANTI_THEMIS_COMMON_LIB_CLICK_SEGMENT_HPP

#include <list>
#include <unordered_map>
#include <com_log.h>
#include "utils.h"
#include "click_node.hpp"

namespace anti {
namespace themis {
namespace common_lib { 

struct MemberValue {
    uint64_t magic;
    int64_t segment_len;
    int64_t node_num;
};
static const uint64_t MAGIC = 0xC29FA627495AFA51;

template<typename Node>
class ClickSegment {
public:
    ClickSegment() : 
            _idx(NULL), 
            _que(NULL), 
            _latest_coord(0L),
            _segment_len(0L),
            _node_num(0L),
            _state_ok(false),
            _segment_num(2L) {
    }

    virtual ~ClickSegment() {
        uninit();
    }
 
    virtual bool init(int64_t step_len, int max_step_num, int64_t segment_len) {
        if (step_len != segment_len) {
            CWARNING_LOG("ClickSegment is jump segment, step len will be ignore");
        }
        return init(segment_len);
    }

    virtual bool init(int64_t segment_len);

    virtual void uninit();

    virtual bool enter(const Node& node, const Node** res);

    virtual bool enter(uint64_t key, int64_t coord, Node& item) {
        return enter(key, coord, item, NULL);
    }

    virtual bool enter(uint64_t key, int64_t coord, Node& item, 
            const Node** pp_seg_item);

    virtual int64_t latest_coord() const {
        return _latest_coord;
    }

    const Node* query_segment(uint64_t key) const;
    const Node* query_segment(uint64_t key, int64_t coord) const;

    // @note: didn't impl
    Node query_last_segment(uint64_t key) const;

    template<typename Archive>
    bool serialize(Archive* ar) const; 

    template<typename Archive>
    bool deserialize(Archive* ar);

    uint64_t window_view_sign_size() {
        if (_idx == nullptr) {
            return 0;
        }
        return _idx->size();
    }

protected:
    typedef std::list<Node*> FeaQueue;
    typedef typename FeaQueue::iterator QueIter;
    typedef std::unordered_map<uint64_t, QueIter> FeaIndex;
    typedef typename FeaIndex::iterator IdxIter;

    FeaIndex* _idx;
    FeaQueue* _que;
    int64_t _latest_coord;
    int64_t _segment_len;
    int64_t _node_num;
    bool _state_ok;
    int64_t _segment_num;

protected:
    // @brief: gc the node that earlier than two segment
    bool _gc();

    // @brief: find fea(sign)'s node
    //         if not exist, return NULL
    Node* _find(uint64_t sign) const;

    // @brief: add a new node, return the node in queue
    //         if fail, return NULL
    Node* _add_node(const Node& node);

    virtual bool _update_node(Node* l_node, const Node& r_node);

    void _move_head(Node& node);

    template<typename Archive>
    bool _serialize_nodes(Archive* ar) const;

    template<typename Archive>
    bool _deserialize_nodes(Archive* ar, int64_t node_num);
};

template<typename Node>
bool ClickSegment<Node>::init(int64_t segment_len) {
    if (segment_len <= 0) {
        CFATAL_LOG("segment_len must bigger than zero, seg_len=%ld",
                segment_len);
        return false;
    }
    _latest_coord = 0L;
    _segment_len = segment_len;
    _node_num = 0L;
    _idx = new (std::nothrow) FeaIndex();
    _que = new (std::nothrow) FeaQueue();
    if (_idx == NULL || _que == NULL) {
        CFATAL_LOG("new idx or que error");
        return false;
    }
    _state_ok = true;
    return true;
}

template<typename Node>
void ClickSegment<Node>::uninit() {
    _state_ok = false;
    _latest_coord = 0L;
    _segment_len = 0L;
    _segment_num = 2L;
    _node_num = 0L;
    if (_idx != NULL) {
        _idx->clear();
        delete _idx;
        _idx = NULL;
    }
    if (_que != NULL) {
        for (QueIter iter = _que->begin();
                iter != _que->end(); ++iter) {
            delete *iter;
            *iter = NULL;
        }
        _que->clear();
        delete _que;
        _que = NULL;
    }
}

template<typename Node>
bool ClickSegment<Node>::enter(
        uint64_t sign,
        int64_t coord,
        Node& node,
        const Node** res) {
    node.sign = sign;
    node.coord = coord;
    return enter(node, res);
}

template<typename Node>
bool ClickSegment<Node>::enter(const Node& node, const Node** res) {
    if (!_state_ok) {
        CFATAL_LOG("state not ok");
        return false;
    }

    if (node.coord > _latest_coord) {
        _latest_coord = node.coord;
    }

    if (!_gc()) {
        CFATAL_LOG("gc fail, latest_coord=%ld node_num=%ld",
                _latest_coord, _node_num);
        // @note: continue
    }

    Node* find = _find(node.sign);
    if (find == NULL) {
        find = _add_node(node);
        if (find == NULL) {
            CWARNING_LOG("add new node fail, sign=%lu coord=%ld",
                    node.sign, node.coord);
            return false;
        }
    } else {
        _update_node(find, node);
        _move_head(*find);
    }

    if (res != NULL) {
        *res = find;
    }
    return true;
}

template<typename Node>
const Node* ClickSegment<Node>::query_segment(uint64_t sign) const {
    return _find(sign);
}

template<typename Node>
const Node* ClickSegment<Node>::query_segment(uint64_t sign, int64_t coord) const {
    if (coord < _latest_coord - _segment_len + 1) {
        return NULL;
    }
    return _find(sign);
}

template<typename Node>
Node* ClickSegment<Node>::_find(uint64_t sign) const {
    if (!_state_ok) {
        CFATAL_LOG("state not ok");
        return NULL;
    }
    IdxIter find = _idx->find(sign);
    if (find == _idx->end()) {
        return NULL;
    } else {
        return *(find->second);
    }
    return NULL;
}

template<typename Node>
Node* ClickSegment<Node>::_add_node(const Node& node) {
    if (!_state_ok) {
        CFATAL_LOG("state not ok");
        return NULL;
    }
    Node* in_node = new (std::nothrow) Node(node);
    if (in_node == NULL) {
        CFATAL_LOG("new Node fail");
        return NULL;
    }
    try {
        _que->push_front(in_node);
        (*_idx)[node.sign] = _que->begin();
    } catch (...) {
        CWARNING_LOG("push front fail");
        return NULL;
    }
    ++_node_num;
    return in_node;
}

template<typename Node>
void ClickSegment<Node>::_move_head(Node& node) {
    if (!_state_ok) {
        CFATAL_LOG("state not ok");
        return ;
    }
    try {
        IdxIter iter = _idx->find(node.sign);
        if (iter == _idx->end()) {
            CFATAL_LOG("node is not in idx, sign=%lu", node.sign);
            return ;
        }
        _que->erase(iter->second);
        _que->push_front(&node);
        iter->second = _que->begin();
    } catch (...) {
        CWARNING_LOG("move head fail, sign=%lu", node.sign);
        return ;
    }
    return ;
}

template<typename Node>
bool ClickSegment<Node>::_gc() {
    if (!_state_ok) {
        CFATAL_LOG("state not ok");
        return false;
    }
    while (!_que->empty()) {
        Node* node = _que->back();
        if (node == NULL) {
            CFATAL_LOG("back node is NULL");
            _que->pop_back();
            continue;
        }
        if (node->coord <= _latest_coord - _segment_num * _segment_len) {
            _idx->erase(node->sign);
            delete node;
            node = NULL;
            _que->pop_back();
            --_node_num;
        } else {
            break;
        }
    }
    return true;
}

template<typename Node>
bool ClickSegment<Node>::_update_node(Node* l_node, const Node& r_node) {
    if (l_node == NULL) {
        CWARNING_LOG("l_node invalid");
        return false;
    }
    l_node->add(r_node, _segment_len);
    return true;
}

template<typename Node>
template<typename Archive>
bool ClickSegment<Node>::_deserialize_nodes(Archive* ar, int64_t node_num) {
    int64_t err_node = 0;
    int64_t idx = 0;
    Node node;
    for (int64_t i = 0; i < node_num; i++) {
        ++idx;
        if (!t_read<Node, Archive>(&node, ar)) {
            ++err_node;
            CWARNING_LOG("readin a node error, err_node=%ld idx=%ld", 
                    err_node, idx);
            continue;
        }
        if (!enter(node, NULL)) {
            ++err_node;
            CWARNING_LOG("enter node error, err_node=%ld idx=%ld", 
                    err_node, idx);
            continue;
        }
    }
    if (err_node > 0) {
        CFATAL_LOG("deserialize node error, err_node=%ld", err_node);
        // continue
    }
    return true;
}

template<typename Node>
template<typename Archive>
bool ClickSegment<Node>::_serialize_nodes(Archive* ar) const {
    int64_t err_node = 0L;
    int64_t idx = 0L;
    typedef typename FeaQueue::reverse_iterator RIter;
    for (RIter iter = _que->rbegin(); iter != _que->rend(); ++iter) {
        ++idx;
        if (*iter == NULL) {
            continue;
        }

        if (!t_write<Node, Archive>(**iter, ar)) {
            ++err_node;
            CWARNING_LOG("write out a node fail, err_node=%ld idx=%ld", 
                    err_node, idx);
            continue;
        }
    }
    const int max_err_num = 1;
    if (err_node >= max_err_num) {
        CFATAL_LOG("err node write too many, err_node=%d", err_node);
        return false;
    }
    return true;
}

template<typename Node>
template<typename Archive>
bool ClickSegment<Node>::serialize(Archive* ar) const {
    if (!_state_ok) {
        CFATAL_LOG("state is not ok, no data to serialize");
        return false;
    }

    if (ar == NULL) {
        CFATAL_LOG("param error");
        return false;
    }
    MemberValue mv;
    mv.magic = MAGIC;
    mv.segment_len = _segment_len;
    mv.node_num = _que->size();
    if (!t_write<MemberValue, Archive>(mv, ar)) {
        CFATAL_LOG("write out memver value fail");
        return false;
    }
    return _serialize_nodes(ar);
}

template<typename Node>
template<typename Archive>
bool ClickSegment<Node>::deserialize(Archive* ar) {
    if (!_state_ok) {
        CFATAL_LOG("state is not ok, can  deserialize");
        return false;
    }

    if (ar == NULL) {
        CFATAL_LOG("param error");
        return false;
    }
    MemberValue mv;
    if (!t_read<MemberValue, Archive>(&mv, ar)) {
        CFATAL_LOG("readin memver value error");
        return false;
    }

    if (mv.magic != MAGIC) {
        CFATAL_LOG("magic not match, readin_magic=%lu", mv.magic);
        return false;
    }

    if (mv.segment_len != _segment_len) {
        CFATAL_LOG("segment_len not match, readin_seg=%ld cur_segment_len=%ld",
                mv.segment_len, _segment_len);
        return false;
    }
    return _deserialize_nodes(ar, mv.node_num);
}

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

#endif  // ANTI_THEMIS_COMMON_LIB_CLICK_SEGMENT_HPP

/* vim: set ts=4 sw=4 sts=4 tw=100 */
