/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/

/**
 * @file: sliding_window/dev/item.h
 * @author: knzeus(<EMAIL>)
 * @date: 2015/03/31 14:23:03
 * @brief: the Item class for sliding window template
 *  
 **/

#ifndef  ANTI_THEMIS_SLIDING_WINDOW_DEV_ITEM_H
#define  ANTI_THEMIS_SLIDING_WINDOW_DEV_ITEM_H

#include <stdint.h>
#include <stdexcept>
#include <com_log.h>
#include <unordered_map>
#include <unordered_set>
#include <set>
#include "sign_util.h"
#include <memory>
#include "utils.h"
#include "exp.h"

namespace anti {
namespace themis {
namespace common_lib {

////////////////////////////////////////////////////////////////

// describe the interface of Item for SlidingWindow
class ItemInterface {
public:
    ItemInterface();

    // call += in step node  when item be push in to window
    ItemInterface& operator+= (const ItemInterface& rv);

    // call -= in segment node when step slide out
    ItemInterface& operator-= (const ItemInterface& rv);

    template<typename Archive>
    bool serialize(Archive* ar) const;

    template<typename Archive>
    bool deserialize(Archive* ar);

    // if true, it's while be delete from window
    bool is_null();

};

template<typename Number>
class CumulantItem {
public:
    CumulantItem() : 
            _cumulant(0) {
    }

    CumulantItem(Number delta) : 
            _cumulant(delta) {
    }

    const CumulantItem& operator=(Number value) {
        _cumulant = value;
        return *this;
    }

    CumulantItem& operator+= (const CumulantItem& rv) {
        _cumulant += rv.cumulant();
        return *this;
    }

    CumulantItem& operator-=(const CumulantItem& rv) {
        _cumulant -= rv.cumulant();
        return *this;
    }

    bool is_null() const {
        return _cumulant <= 0;
    }

    Number cumulant() const {
        return _cumulant;
    }
    
    template<typename Archive>
    bool serialize(Archive* ar) const;

    template<typename Archive>
    bool deserialize(Archive* ar);
 
private:
    Number _cumulant;
};

typedef CumulantItem<int64_t> SegmentItem;

////////////////////////////////////////////////////////////////

template<typename Number>
class PairCumulantItem {
public:
    PairCumulantItem() : 
            _first(0), 
            _second(0) {
    }

    PairCumulantItem(Number a, Number b) : 
            _first(a), 
            _second(b) { 
    }

    PairCumulantItem& operator+= (const PairCumulantItem& rv) {
        _first += rv.first();
        _second += rv.second();
        return *this;
    }

    PairCumulantItem& operator-= (const PairCumulantItem& rv) {
        _first -= rv.first();
        _second -= rv.second();
        return *this;
    }

    bool is_null() const {
        return (_first <= 0) && (_second <= 0);
    }

    Number first() const {
        return _first;
    }

    Number second() const {
        return _second;
    }

    template<typename Archive>
    bool serialize(Archive* ar) const;

    template<typename Archive>
    bool deserialize(Archive* ar);

private:
    Number _first;
    Number _second;
};

typedef PairCumulantItem<int64_t> RateItem;

////////////////////////////////////////////////////////////////

template<typename Number, int bucket_number>
class ArrayItem {
public:
    ArrayItem() {
        for (int i = 0; i < bucket_number; i++) {
            _count[i] = 0;
        }
    }

    ArrayItem& operator+= (const ArrayItem& rv) {
        for (int i = 0; i < bucket_number; i++) {
            _count[i] += rv[i];
        }
        return *this;
    }

    ArrayItem& operator-= (const ArrayItem& rv) {
        for (int i = 0; i < bucket_number; i++) {
            _count[i] -= rv[i];
        }
        return *this;
    }

    bool is_null() const {
        bool any = false;
        for (int i = 0; i < bucket_number; i++) {
            if (_count[i] > 0) {
                any = true;
                break;
            }
        }
        return !any;
    }

    Number& operator[] (int i) {
        if (i < 0
                || i >= bucket_number) {
            CFATAL_LOG("param error, i=%d bucket_number=%d",
                    i, bucket_number);
            throw std::out_of_range("invalid index");
        }
        return _count[i];
    }

    const Number& operator[] (int i) const {
        if (i < 0
                || i >= bucket_number) {
            CFATAL_LOG("param error, i=%d bucket_number=%d",
                    i, bucket_number);
            throw std::out_of_range("invalid index");
        }
        return _count[i];
    }

    template<typename Archive>
    bool serialize(Archive* ar) const;

    template<typename Archive>
    bool deserialize(Archive* ar);

private:
    Number _count[bucket_number];
};

/////////////////////////////////////////////////////////////
template<typename key_type, typename value_type>
class KeyValueItem {
public:
    KeyValueItem() : 
            _key(0), 
            _value(0) {
    }

    KeyValueItem(key_type a, value_type b) : 
            _key(a), 
            _value(b) { 
    }

    bool operator < (const KeyValueItem& rv) const {
        return this->value() == rv.value() ? 
            this->key() > rv.key() : this->value() > rv.value();
    }

    bool is_null() const {
        return (_key <= 0) && (_value <= 0);
    }

    key_type key() const {
        return _key;
    }

    value_type value() const {
        return _value;
    }

    template<typename Archive>
    bool serialize(Archive* ar) const;

    template<typename Archive>
    bool deserialize(Archive* ar);

private:
    key_type _key;
    value_type _value;
};
typedef KeyValueItem<uint64_t, int64_t> ConNode;
typedef KeyValueItem<std::string, int64_t> ValueCumNode;

template<typename Number, typename Node>
class TopkSet {
public:
    TopkSet() :
            _k(0),
            _total(0) {
    }
    void init(uint64_t k) {
        _k = k; 
        _total = 0;
    }
    const std::set<Node>& topk_set() const {
        return _k_set;
    }

    void enter(Node node) {
        auto iter = _map.find(node.key());
        if (iter != _map.end()) {
            _k_set.erase(Node(iter->first, iter->second));
            _total -= iter->second;
            _k_set.insert(node);
            iter->second = node.value();
        } else {
            if (_k_set.size() >= _k && node.value() < _k_set.rbegin()->value()) {
                return;
            }
            _map.emplace(node.key(), node.value());
            if (!_k_set.insert(node).second) {
                return;
            }
        }
        _total += node.value();
        while (_k_set.size() > _k) {
            auto i = --(_k_set.end());
            _map.erase(i->key());
            _total -= i->value();
            _k_set.erase(i);
        }
    }
    Number total() const {
        return _total;
    }
    
    bool in_topk(uint64_t sign) const {
        return _map.find(sign) == _map.end() ? false : true;
    }
    void clear() {
        _map.clear();
        _k_set.clear();
        _total = 0;
    }

    template<typename Archive>
    bool serialize(Archive* ar) const;

    template<typename Archive>
    bool deserialize(Archive* ar);
private:
    uint64_t _k;
    Number _total;
    // <viewB sign, ConNode>
    std::unordered_map<uint64_t, Number> _map;
    std::set<Node> _k_set;
};

typedef TopkSet<int64_t, ConNode> TopkHeap; 

template<typename Number>
class ConcentrationItem {
public:
    ConcentrationItem() : 
            _cumulant(0) {
    }    
    
    bool init(uint64_t k, Number cumulant, uint64_t sign) {
        _topk_heap.init(k);
        _cumulant = cumulant;
        _topk_heap.enter(ConNode(sign, cumulant));
        return _value_map.emplace(sign, cumulant).second;
    }
    
    bool init(uint64_t k, Number cumulant, uint64_t sign, std::string view_value) {
        _str_value_map.emplace(sign, view_value);
        return init(k, cumulant, sign);
    }

    const TopkHeap& topk_heap() const {
        return _topk_heap;
    }

    const std::unordered_map<uint64_t, Number>& value_map() const {
        return _value_map;
    }

    const std::unordered_map<uint64_t, std::string>& str_value_map() const {
        return _str_value_map;
    }
   
    bool get_topk_value_cum(std::vector<ValueCumNode>* node_vec) const {
        if (node_vec == NULL) {
            CWARNING_LOG("node_vec is NULL");
            return false;    
        }
        std::set<ConNode>::iterator topk_it = _topk_heap.topk_set().begin();
        for (; topk_it != _topk_heap.topk_set().end(); topk_it++) {
            std::string data_view_value = "_";
            std::unordered_map<uint64_t, std::string>::const_iterator str_it = 
                    _str_value_map.find(topk_it->key());
            if (str_it != _str_value_map.end()) {
                data_view_value = str_it->second;
            }
            node_vec->emplace_back(ValueCumNode(data_view_value, topk_it->value())); 
        }
        return true;
    }
    
    ConcentrationItem& operator+= (const ConcentrationItem& rv) {
        for (auto iter = rv.value_map().begin(); 
                iter != rv.value_map().end(); ++iter) {
            auto i = _value_map.find(iter->first);
            if (i != _value_map.end()) {
                i->second += iter->second;       
                _topk_heap.enter(ConNode(i->first, i->second));
            } else {
                _value_map.emplace(iter->first, iter->second);
                _topk_heap.enter(ConNode(iter->first, iter->second));
            }
        }
        for (auto rv_iter = rv.str_value_map().begin();
                rv_iter != rv.str_value_map().end();
                rv_iter++) {
                auto rv_it = _str_value_map.find(rv_iter->first);
                if (rv_it == _str_value_map.end()) {
                    _str_value_map.emplace(rv_iter->first, 
                            rv_iter->second);
                }
        }
        _cumulant += rv.cumulant();
        return *this;
    }

    ConcentrationItem& operator-=(const ConcentrationItem& rv) {
        for (auto iter = rv.value_map().begin(); iter != rv.value_map().end(); ++iter) {
            auto i = _value_map.find(iter->first);
            if (i != _value_map.end()) {
                i->second -= iter->second;       
                if (i->second <= 0) {
                    _value_map.erase(i->first);
                }
            }
        }

        for (auto rv_iter = rv.str_value_map().begin(); rv_iter != rv.str_value_map().end(); ++rv_iter) {
            auto j = _str_value_map.find(rv_iter->first);
            if (j != _str_value_map.end() && 
                    _value_map.find(rv_iter->first) == _value_map.end()) {
                _str_value_map.erase(j->first);
            }
        }
        _topk_heap.clear();
        for (auto iter = _value_map.begin(); 
                iter != _value_map.end(); ++iter) {
            _topk_heap.enter(ConNode(iter->first, iter->second));
        }
        _cumulant -= rv.cumulant();
        return *this;
    }

    bool is_null() const {
        return _cumulant <= 0;
    }

    bool in_topk(uint64_t sign) const {
        return _topk_heap.in_topk(sign);
    }

    Number cumulant() const {
        return _cumulant;
    }
    Number kcumulant() const {
        return _topk_heap.total();
    }
    
    template<typename Archive>
    bool serialize(Archive* ar) const;

    template<typename Archive>
    bool deserialize(Archive* ar);
private:
    Number _cumulant;
    // <viewB sign, count> for recreate topk set
    std::unordered_map<uint64_t, Number> _value_map;
    std::unordered_map<uint64_t, std::string> _str_value_map;
    TopkHeap _topk_heap;
};

typedef ConcentrationItem<int64_t> ConItem;

////////////////////////////////////////////////////////////////

class DistinctItem {
public:
    DistinctItem() : _total(0) {}

    DistinctItem(uint64_t key, int64_t value) {
        _total = value;
        _map.emplace(key, value);
    }

    // call += in step node  when item be push in to window
    DistinctItem& operator+= (const DistinctItem& rv) {
        auto rv_map = rv.map();
        for (auto iter = rv_map.begin(); iter != rv_map.end(); ++iter) {
            auto find_iter = _map.find(iter->first);
            if (find_iter == _map.end()) {
                _map.emplace(iter->first, iter->second);
            } else {
                find_iter->second += iter->second;
            }
        }
        _total += rv.total();
        return *this;
    }

    // call -= in segment node when step slide out
    DistinctItem& operator-= (const DistinctItem& rv) {
        auto rv_map = rv.map();
        for (auto iter = rv_map.begin(); iter != rv_map.end(); ++iter) {
            auto find_iter = _map.find(iter->first);
            if (find_iter == _map.end()) {
                CFATAL_LOG("cannot find key:[%llu] in map, value:[%ld]",
                    iter->first, iter->second);
                return *this;
            } else {
                find_iter->second -= iter->second;
                if (find_iter->second <= 0) {
                    _map.erase(iter->first);
                }
            }
        } 
        _total -= rv.total();
        return *this;
    }

    const std::unordered_map<uint64_t, int64_t>& map() const {
        return _map;
    }

    int64_t total() const {
        return _total;
    }
    
    int64_t distinct_num() const {
        return _map.size(); 
    }

    template<typename Archive>
    bool serialize(Archive* ar) const;

    template<typename Archive>
    bool deserialize(Archive* ar);

    // if true, it's while be delete from window
    bool is_null() const {
        return _total <= 0;        
    }
private:
    std::unordered_map<uint64_t, int64_t> _map;
    int64_t _total;
};

// the interface of LeafNode is same as std::unordered_map
//     LeafNode node;
//     1. node.emplace(key value) 
//     2. node += rhs_node
//     3. node -= rhs_node
//     4. node.empty()
//
struct TreeItemStatisticCond {
    TreeItemStatisticCond() : 
            statistic_level(ROOT), 
            leaf_condition("") {}
    TreeItemStatisticCond(const TreeItemStatisticCond& rhs) :
            statistic_level(rhs.statistic_level),
            leaf_condition(rhs.leaf_condition) {}
    enum StatisticLevel {
        ROOT = 0,
        DISTINCT_LEAF = 1,
        LEAF = 2
    };
    StatisticLevel statistic_level;
    std::string leaf_condition;
};

template <typename LeafNode>
class TreeItem {
public:
    typedef uint64_t LeafKey;
    typedef anti::baselib::SignUtil SignUtil;
    typedef std::shared_ptr<LeafNode> LeafNodePtr;

    TreeItem() : _total(0), _condition_value(0) {}
    TreeItem(LeafKey key, LeafKey leaf_value, TreeItemStatisticCond tisc) : 
            _tisc(tisc), _condition_value(0) {
        LeafNodePtr ptr(new (std::nothrow) LeafNode(leaf_value, 1, _tisc.leaf_condition));
        _root_map.emplace(key, ptr);
        _total = 1;
        _add_leaf_value(key, leaf_value);
    }
    TreeItem(LeafKey key, LeafKey leaf_value) : _condition_value(0) {
        LeafNodePtr ptr(new (std::nothrow) LeafNode(leaf_value, 1, _tisc.leaf_condition));
        _root_map.emplace(key, ptr);
        _total = 1;
        _add_leaf_value(key, leaf_value);
    }
    TreeItem(
            std::string leaf_key_str,
            std::string leaf_value_str) : _condition_value(0) {
        LeafKey leaf_key = 0;
        LeafKey leaf_value = 0;
        SignUtil::create_sign_md64(leaf_key_str, &leaf_key);
        SignUtil::create_sign_md64(leaf_value_str, &leaf_value);
        LeafNodePtr ptr(new (std::nothrow) LeafNode(leaf_value, 1, _tisc.leaf_condition));
        _root_map.emplace(leaf_key, ptr);
        // only one value construction
        _total = 1;
        _add_leaf_value(leaf_key, leaf_value);
    }
    TreeItem(const TreeItem& rhs) {
        for (auto pair : rhs._root_map) {
            LeafNodePtr ptr(new (std::nothrow) LeafNode(*pair.second));
            _root_map.emplace(pair.first, ptr);
        }
        _leaf_map = rhs._leaf_map;
        _total = rhs._total;
        _tisc = rhs._tisc;
        _condition_value = rhs._condition_value;

    }

    // call += in step node  when item be push in to window
    TreeItem& operator+= (const TreeItem& rv) {
        const auto& map = rv.map();
        for (auto it = map.begin(); it != map.end(); ++it) {
            // ROOT 
            int64_t benchmarks = lookup(it->first);
            // DISTINCT_LEAF
            auto benchmarks_set = lookup_set(it->first); 

            auto find_it = _root_map.find(it->first);
            if (find_it == _root_map.end()) {
                // copy from rv
                LeafNodePtr ptr(new (std::nothrow) LeafNode(*it->second));
                _root_map.emplace(it->first, ptr); 
            } else {
                auto& lhs_node = *find_it->second;
                auto& rhs_node = *it->second;
                lhs_node += rhs_node;
            }
            // ROOT statistic
            if (_tisc.statistic_level == TreeItemStatisticCond::ROOT) {
                int64_t increments = lookup(it->first) - benchmarks;
                _condition_value += increments;
            } else if (_tisc.statistic_level == TreeItemStatisticCond::DISTINCT_LEAF) {
            // DISTINCT_LEAF
                auto increments_set = lookup_set(it->first);
                increments_set.erase(benchmarks_set.begin(), benchmarks_set.end());
                for (const auto& key : increments_set) {
                    if (_leaf_map.find(key) != _leaf_map.end()) {
                        _leaf_map[key]++;
                    } else {
                        _leaf_map.emplace(key, 1);
                    }
                }
                _condition_value = _leaf_map.size();
            }
        }
        _total += rv.total();
        return *this;
    }

    // call -= in segment node when step slide out
    TreeItem& operator-= (const TreeItem& rv) {
        const auto& map = rv.map();
        for (auto it = map.begin(); it != map.end(); ++it) {
            // ROOT
            int64_t benchmarks = lookup(it->first);
            // DISTINCT_LEAF
            auto benchmarks_set = lookup_set(it->first); 
            auto find_it = _root_map.find(it->first);
            if (find_it == _root_map.end()) {
                // invalid sub
                CWARNING_LOG("invalid sub, sign[%lu] in not in rhs", it->first);
            } else {
                auto& lhs_node = *find_it->second;
                auto& rhs_node = *it->second;
                lhs_node -= rhs_node;
                if (lhs_node.empty()) {
                    find_it = _root_map.erase(find_it);
                }
            }
            if (_tisc.statistic_level == TreeItemStatisticCond::ROOT) {
                int64_t increments = lookup(it->first) - benchmarks;
                _condition_value += increments;
            } else if (_tisc.statistic_level == TreeItemStatisticCond::DISTINCT_LEAF) {
                // DISTINCT_LEAF
                auto increments_set = lookup_set(it->first);
                increments_set.erase(benchmarks_set.begin(), benchmarks_set.end());
                for (const auto& key : increments_set) {
                    auto it = _leaf_map.find(key);
                    if (it != _leaf_map.end()) {
                        it->second--;
                        if (it->second == 0) {
                            _leaf_map.erase(it);
                        }
                    }
                }
                _condition_value = _leaf_map.size();
            }
        }
        _total -= rv.total();
        return *this;
    }

    const std::unordered_map<LeafKey, LeafNodePtr>& map() const {
        return _root_map;
    }

    int64_t total() const {
        return _total;
    }

    int64_t condition_value() const {
        return _condition_value;
    }

    int64_t lookup(LeafKey key) {
        if (_root_map.find(key) == _root_map.end()) {
            return 0;
        }
        int64_t count = 0;
        if (_tisc.statistic_level == TreeItemStatisticCond::ROOT) {
            if (_tisc.leaf_condition == "" 
                    || _root_map.at(key)->match(_tisc.leaf_condition)) {
                count += 1;
            }
        }
        return count;
    }
    std::unordered_set<LeafKey> lookup_set(LeafKey key) {
        if (_root_map.find(key) == _root_map.end()) {
            return std::unordered_set<LeafKey>();
        }
        return _root_map.at(key)->set(_tisc.leaf_condition);
    }

    template<typename Archive>
    bool serialize(Archive* ar) const;

    template<typename Archive>
    bool deserialize(Archive* ar);

    // if true, it's while be delete from window
    bool is_null() const {
        return _total <= 0;        
    }
private:
    void _add_leaf_value(LeafKey key, LeafKey leaf_value) {
        if (_tisc.statistic_level == TreeItemStatisticCond::ROOT) {
            if (_tisc.leaf_condition == "" 
                    || _root_map.at(key)->match(_tisc.leaf_condition)) {
                _condition_value += 1;
            }
            return;
        }
        if (_tisc.statistic_level == TreeItemStatisticCond::DISTINCT_LEAF) {
            if (_tisc.leaf_condition == "" || 
                    _root_map.at(key)->match(_tisc.leaf_condition)) {
                _leaf_map.emplace(leaf_value, 1);
                _condition_value += 1;
            }
        }
        return;
    }

    std::unordered_map<LeafKey, LeafNodePtr> _root_map;
    std::unordered_map<LeafKey, int64_t> _leaf_map;
    int64_t _total;
    TreeItemStatisticCond _tisc;
    int64_t _condition_value;
};

template <typename Key, typename Value>
class MapNode {
public:
    const std::string DISTINCT = "distinct";
    const std::string COUNT = "count";
    MapNode(Key key, Value value, std::string cond) {
        _exp.reset(new (std::nothrow) Exp());
        if (cond != "" && !_exp->append(cond, {DISTINCT, COUNT})) {
            CFATAL_LOG("invalid condition[%s]", cond.c_str());
        }
        _map.emplace(key, value);
    }
    MapNode() {}
    ~MapNode() {
        _map.clear();
    }
    std::unordered_map<Key, Value> map() {
        return _map;
    }
    void emplace(Key key, Value value) {
        _map.emplace(key, value);
    }
    uint32_t size() {
        return _map.size();
    }
    Value at(Key key) const {
        return _map.at(key);
    }
    MapNode& operator+= (MapNode& rhs) {
        const auto& map = rhs.map();
        map_union(&_map, &map);
        return *this;
    }
    MapNode& operator-= (MapNode& rhs) {
        const auto& map = rhs.map();
        map_difference(&_map, &map);
        return *this;
    }
    bool empty() const {
        return _map.empty();
    }

    template<typename Archive>
    bool serialize(Archive* ar) const;

    template<typename Archive>
    bool deserialize(Archive* ar);

    std::unordered_set<Key> set(std::string cond) {
        if (!match(cond)) return std::unordered_set<Key>();
        std::unordered_set<Key> res;
        for (const auto& pair : _map) {
            res.insert(pair.first);
        }
        return res;
    }

    bool match(std::string cond) {
        if (cond == "") return true;
        int64_t distinct = static_cast<int64_t>(_map.size());
        std::vector<std::pair<std::string, int64_t>> collector;
        collector.emplace_back(DISTINCT, distinct);
        // collector.push_back(std::make_pair(COUNT, count));
        _exp->reset(collector);
        bool res = false; 
        if (!_exp->calculate(cond, &res)) {
            CWARNING_LOG("exp[%s] calculate failed!", cond.c_str());
            return false;
        }
        return res;
    }

private:
    std::unordered_map<Key, Value> _map;
    std::shared_ptr<Exp> _exp;
};

typedef MapNode<uint64_t, int64_t> SecondaryLeafNode;
typedef TreeItem<SecondaryLeafNode> ThreeOrderTreeItem; 

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

#include "item.hpp"

#endif  // ANTI_THEMIS_SLIDING_WINDOW_DEV_ITEM_H

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
