// Copyright (c) 2025 Baidu.com, Inc. All Rights Reserved
// @Author: l<PERSON><PERSON><PERSON><PERSON>(<EMAIL>)

#include <cstdint>
#include <vector>
#include <algorithm>
#include <climits>

#include <com_log.h>
#include <gflags/gflags.h>
#include <bsl/var/Dict.h>
#include <bsl/var/Int32.h>
#include <bsl/var/Int64.h>
#include <bsl/var/Double.h>
#include <bsl/var/String.h>
#include <bsl/ResourcePool.h>
#include "pool_hash_map.hpp"
#include "utils.h"

DECLARE_int32(sliding_window_v2_mempool_block_item_num);

static constexpr uint32_t INITIAL_BUCKET_NUM = 8;

namespace anti {
namespace themis {
namespace common_lib {

template <typename Item>
SlidingWindowV2<Item>::SlidingWindowV2() :
        _mem_pool(nullptr),
        _own_mem_pool(false),
        _step_len(0),
        _step_num(0),
        _max_step_num(0),
        _segment_len(0),
        _oldest_coord(-1),
        _segment_start_coord(-1),
        _latest_coord(-1),
        _segment(nullptr),
        _window(nullptr),
        _sign_start_coord(nullptr) {}

template <typename Item>
SlidingWindowV2<Item>::~SlidingWindowV2() {
    uninit();
}

template <typename Item>
bool SlidingWindowV2<Item>::init(int64_t step_len, int max_step_num, int64_t segment_len) {
    uint32_t slab_size = StepContainer::get_node_size();
    std::vector<uint32_t> slab_sizes{slab_size};
    _mem_pool = new (::std::nothrow) GCSlabMempool32();
    if (!_mem_pool) {
        CFATAL_LOG("create memory pool failed");
        return false;
    }

    int ret = _mem_pool->create(
            slab_sizes.data(),
            static_cast<uint32_t>(slab_sizes.size()),
            FLAGS_sliding_window_v2_mempool_block_item_num);
    if (ret != 0) {
        delete _mem_pool;
        _mem_pool = nullptr;
        return false;
    }
    _own_mem_pool = true;

    if (!_init_common(step_len, max_step_num, segment_len)) {
        // _init_common失败时清理内存池
        delete _mem_pool;
        _mem_pool = nullptr;
        _own_mem_pool = false;
        return false;
    }

    return true;
}

template <typename Item>
bool SlidingWindowV2<Item>::init(
        int64_t step_len,
        int max_step_num,
        int64_t segment_len,
        GCSlabMempool32* mem_pool) {
    // 使用外部内存池
    if (!mem_pool) {
        CFATAL_LOG("external memory pool is null");
        return false;
    }

    _mem_pool = mem_pool;
    _own_mem_pool = false;

    return _init_common(step_len, max_step_num, segment_len);
}

template <typename Item>
bool SlidingWindowV2<Item>::_init_common(
        int64_t step_len,
        int max_step_num,
        int64_t segment_len) {
    if (!_check_segment_arg(step_len, max_step_num, segment_len)) {
        CFATAL_LOG("param error");
        return false;
    }

    _step_len = step_len;
    _max_step_num = max_step_num;
    _segment_len = segment_len;
    _step_num = segment_len / _step_len;

    _segment = new (::std::nothrow) StepContainer(_mem_pool, INITIAL_BUCKET_NUM);
    _window = new (::std::nothrow) WindowContainer();
    if (_segment == nullptr || _window == nullptr) {
        CFATAL_LOG("new container for sliding window fail");
        // 清理已分配的资源
        delete _segment;
        _segment = nullptr;
        delete _window;
        _window = nullptr;
        return false;
    }

    if (!_segment->is_valid()) {
        CFATAL_LOG("segment container init fail");
        // 清理已分配的资源
        delete _segment;
        _segment = nullptr;
        delete _window;
        _window = nullptr;
        return false;
    }

    _window->clear();
    if (!_create_window_steps()) {
        // 清理已分配的资源
        delete _segment;
        _segment = nullptr;
        delete _window;
        _window = nullptr;
        return false;
    }

    _oldest_coord = (_step_num - _max_step_num) * _step_len;
    _segment_start_coord = 0L;
    _latest_coord = 0L;

    return true;
}

template <typename Item>
bool SlidingWindowV2<Item>::_create_window_steps() {
    // 创建滑动窗口的所有StepContainer
    for (int step_i = 0; step_i < _max_step_num; step_i++) {
        StepContainer* step = new (::std::nothrow) StepContainer(_mem_pool, INITIAL_BUCKET_NUM);
        if (step == nullptr || !step->is_valid()) {
            CFATAL_LOG("new step fail");
            // 清理已创建的steps
            for (auto iter = _window->begin(); iter != _window->end(); ++iter) {
                if (*iter != nullptr) {
                    (*iter)->clear();
                    delete *iter;
                    *iter = nullptr;
                }
            }
            _window->clear();
            return false;
        }
        _window->push_back(step);
    }
    return true;
}

template <typename Item>
void SlidingWindowV2<Item>::set_log_key_first_appear() {
    _sign_start_coord = new (::std::nothrow) SignStartCoord();
    if (_sign_start_coord == nullptr) {
        CFATAL_LOG("alloc SignStartCoord failed");
    }
}

template <typename Item>
bool SlidingWindowV2<Item>::_check_segment_arg(
        int64_t step_len,
        int max_step_num,
        int64_t segment_len) const {
    if (step_len <= 0 || max_step_num <= 0 || (step_len * (max_step_num - 1) < segment_len) ||
        segment_len <= 0 || (segment_len % step_len != 0)) {
        CWARNING_LOG(
                "init argument error, step_len=%d max_step_num=%d "
                "segment_len=%d",
                step_len,
                max_step_num,
                segment_len);
        return false;
    }
    return true;
}

template <typename Item>
void SlidingWindowV2<Item>::uninit() {
    _step_len = 0;
    _step_num = 0;
    _max_step_num = 0;
    _segment_len = 0;
    _oldest_coord = -1;
    _segment_start_coord = -1;
    _latest_coord = -1;

    if (_segment != nullptr) {
        _segment->clear();
        delete _segment;
        _segment = nullptr;
    }

    if (_window != nullptr) {
        for (auto iter = _window->begin(); iter != _window->end(); ++iter) {
            if (*iter != nullptr) {
                (*iter)->clear();
                delete *iter;
                *iter = nullptr;
            }
        }
        _window->clear();
        delete _window;
        _window = nullptr;
    }

    if (_sign_start_coord != nullptr) {
        _sign_start_coord->clear();
        delete _sign_start_coord;
        _sign_start_coord = nullptr;
    }

    if (_own_mem_pool) {
        delete _mem_pool;
    }
    _mem_pool = nullptr;

    return;
}

template <typename Item>
uint64_t SlidingWindowV2<Item>::window_view_sign_size() {
    int64_t window_view_sign_size = 0;
    if (_window == nullptr) {
        return 0;
    }
    for (const auto& node : *_window) {
        window_view_sign_size += node->size();
    }
    return window_view_sign_size;
}

template <typename Item>
uint64_t SlidingWindowV2<Item>::segment_view_sign_size() {
    if (_segment == nullptr) {
        return 0;
    }
    return _segment->size();
}

template <typename Item>
const Item* SlidingWindowV2<Item>::_query(const StepContainer& from, const uint64_t key) const {
    const Item* result = from.find(key);
    return result;
}

template <typename Item>
const Item* SlidingWindowV2<Item>::query_segment(const uint64_t key) const {
    if (_segment == nullptr) {
        CFATAL_LOG("inner state error, _segment is NULL");
        return nullptr;
    }
    return _query(*_segment, key);
}

template <typename Item>
const Item* SlidingWindowV2<Item>::query_segment(const uint64_t key, int64_t coord) const {
    if (!_in_segment(coord)) {
        return nullptr;
    }
    return query_segment(key);
}

template <typename Item>
Item SlidingWindowV2<Item>::query_last_segment(const uint64_t key) const {
    const Item* p_item_seg = _query(*_segment, key);

    int first_step_idx = (_segment_start_coord - _oldest_coord) / _step_len;
    if (first_step_idx <= 0 || first_step_idx > _max_step_num - _step_num) {
        CFATAL_LOG(
                "the first step index is invalid, first_step_idx=%d "
                "_step_num=%d _max_step_num=%d _segment_start_coord=%ld "
                "_oldest_coord=%ld _step_len=%ld",
                first_step_idx,
                _step_num,
                _max_step_num,
                _segment_start_coord,
                _oldest_coord,
                _step_len);
        return Item();
    }

    int last_step_in_window_idx = first_step_idx + _step_num - 1;
    int step_before_window_idx = first_step_idx - 1;
    const Item* p_item_minus = _query(*(_window->at(last_step_in_window_idx)), key);
    const Item* p_item_add = _query(*(_window->at(step_before_window_idx)), key);

    Item res;
    if (p_item_seg != NULL) {
        res += *p_item_seg;
    }
    if (p_item_minus != NULL) {
        res -= *p_item_minus;
    }
    if (p_item_add != NULL) {
        res += *p_item_add;
    }

    return res;
}

template <typename Item>
int SlidingWindowV2<Item>::insert(const uint64_t key, const int64_t coord, const Item& item) {
    return insert(key, coord, item, NULL);
}

template <typename Item>
int SlidingWindowV2<Item>::insert(
        const uint64_t key,
        const int64_t coord,
        const Item& item,
        const Item** pp_seg_item) {
    bool slide = (coord >= _segment_start_coord + _segment_len) ? true : false;
    if (!enter(key, coord, item, pp_seg_item)) {
        return SLIDING_WINDOW_V2_FAIL;
    }
    return slide ? SLIDING_WINDOW_V2_SLIDE : SLIDING_WINDOW_V2_SUCC;
}

template <typename Item>
bool SlidingWindowV2<Item>::enter(const uint64_t key, const int64_t coord, const Item& item) {
    return enter(key, coord, item, NULL);
}

template <typename Item>
bool SlidingWindowV2<Item>::enter(
        const uint64_t key,
        const int64_t coord,
        const Item& item,
        const Item** pp_seg_item) {
    if (coord < _oldest_coord) {
        CWARNING_LOG(
                "too old item, coord=%ld oldest=%ld latest=%ld",
                coord,
                _oldest_coord,
                _latest_coord);
        return false;
    }
    if (coord > _latest_coord) {
        _latest_coord = coord;
    }

    // segment range is [start, end)
    if (coord >= _segment_start_coord + _segment_len) {
        _slide_to_coord(coord);
    }

    if (_window == nullptr) {
        CFATAL_LOG("inner state error, _window is NULL");
        return false;
    }
    StepContainer* step = _find_step(coord);
    if (step == nullptr) {
        CFATAL_LOG("find a NULL step, coord=%ld", coord);
        return false;
    }
    if (!_push_item(step, key, item, nullptr)) {
        CWARNING_LOG("push a item into step error, key=%llu", key);
        return false;
    }
    if (_in_segment(coord) && !_push_item(_segment, key, item, pp_seg_item)) {
        CWARNING_LOG("push a item into segment error, key=%llu", key);
        return false;
    }

    // log first appear coordinate
    if (_sign_start_coord != NULL) {
        // int64_t round_coord = _step_start_coord(coord);
        int64_t round_coord = _step_start_coord(_latest_coord);
        auto ptr = _sign_start_coord->find(key);
        if (ptr == _sign_start_coord->end()) {
            ContinueCoordinate c(round_coord);
            //_sign_start_coord[key] = c;
            _sign_start_coord->emplace(key, c);
        } else {
            ptr->second.push(round_coord, _step_len);
        }
    }

    return true;
}

template <typename Item>
int64_t SlidingWindowV2<Item>::query_start_coord(const uint64_t key) const {
    if (_sign_start_coord == NULL) {
        return segment_start_coord();
    }

    auto ptr = _sign_start_coord->find(key);
    if (ptr == _sign_start_coord->end()) {
        return segment_start_coord();
    }

    int64_t key_start = ptr->second.first_coord();
    if (key_start < 0) {
        return segment_start_coord();
    } else {
        return key_start;
    }
}

template <typename Item>
bool SlidingWindowV2<Item>::_in_segment(const int64_t coord) const {
    // 只检查下界，不限制上界
    return coord >= _segment_start_coord;
}

template <typename Item>
typename SlidingWindowV2<Item>::StepContainer* SlidingWindowV2<Item>::_find_step(
        int64_t coord) const {
    if (_window == nullptr) {
        CFATAL_LOG("inner state error, _window is NULL");
        return nullptr;
    }
    int pos = (coord - _oldest_coord) / _step_len;
    if (coord < _oldest_coord) {
        pos -= 1;
    }
    if (pos < 0 || pos >= static_cast<int>(_window->size())) {
        CFATAL_LOG("the pos of the coord is invalid, pos=%d coord=%ld", pos, coord);
        return nullptr;
    }
    return _window->at(pos);
}

template <typename Item>
int64_t SlidingWindowV2<Item>::_step_start_coord(int64_t coord) const {
    int offset = (coord - _segment_start_coord) / _step_len;
    return _segment_start_coord + offset * _step_len;
}

template <typename Item>
int64_t SlidingWindowV2<Item>::_slide_to_coord(int64_t coord) {
    if (coord < _segment_start_coord + _segment_len) {
        return 0;
    }
    // segment range is [start, end)
    int slide_step = 1 + (coord - _segment_start_coord - _segment_len) / _step_len;
    if (slide_step <= 0) {
        return 0;
    }

    int64_t slide_coord = _step_len * slide_step;
    if (slide_step < _max_step_num) {
        for (int step_i = 0; step_i < slide_step; step_i++) {
            _slide_one_step();
        }
    } else {
        _clear();
        _latest_coord = coord;
        int64_t remainder = coord % _step_len;
        _oldest_coord = coord - remainder - _step_len * (_max_step_num - 1);
        _segment_start_coord = sliding_segment_start_coord(coord, _step_len, _step_num);
    }

    return slide_coord;
}

template <typename Item>
void SlidingWindowV2<Item>::_clear() {
    if (_segment != nullptr) {
        _segment->clear();
    }
    if (_window != nullptr) {
        for (auto iter = _window->begin(); iter != _window->end(); iter++) {
            (*iter)->clear();
        }
    }
    return;
}

template <typename Item>
void SlidingWindowV2<Item>::_slide_one_step() {
    _delete_first_step_in_segment();
    _move_and_clear_oldest_step();
}

template <typename Item>
void SlidingWindowV2<Item>::_move_and_clear_oldest_step() {
    if (_window == nullptr) {
        CFATAL_LOG("inner state is error, _window is NULL");
        return;
    }
    StepContainer* oldest_step = _window->front();
    _window->pop_front();
    if (oldest_step == nullptr) {
        CFATAL_LOG("the oldest step in window is NULL");
        return;
    }
    oldest_step->clear();
    _window->push_back(oldest_step);
    _oldest_coord += _step_len;
    return;
}

template <typename Item>
void SlidingWindowV2<Item>::_delete_first_step_in_segment() {
    StepContainer* first_step = _first_step_in_segment();
    if (first_step == nullptr) {
        CFATAL_LOG("first step in segment is NULL");
        return;
    }

    if (_sign_start_coord != nullptr) {
        // 更新首次出现coord - 维护SignStartCoord一致性
        first_step->traverse([this](const uint64_t& key, const Item& value) {
            auto ptr = _sign_start_coord->find(key);
            if (ptr != _sign_start_coord->end()) {
                ptr->second.erase(_segment_start_coord, _step_len);
                if (ptr->second.first_coord() == -1) {
                    _sign_start_coord->erase(ptr);
                }
            }
        });
    }

    pool_sub(_segment, first_step);
    _segment_start_coord += _step_len;
}

template <typename Item>
typename SlidingWindowV2<Item>::StepContainer* SlidingWindowV2<Item>::_first_step_in_segment()
        const {
    if (_window == nullptr) {
        CFATAL_LOG("inner state error, _window is NULL");
        return nullptr;
    }
    int pos = (_segment_start_coord - _oldest_coord) / _step_len;
    // @note: at least over store one step
    if (pos <= 0 || pos >= static_cast<int>(_window->size())) {
        CFATAL_LOG("first step pos is invalid, pos=%d", pos);
        return nullptr;
    }
    return _window->at(pos);
}

template <typename Item>
bool SlidingWindowV2<Item>::_push_item(
        StepContainer* step,
        const uint64_t key,
        const Item& item,
        const Item** pp_item) {
    if (step == nullptr) {
        CFATAL_LOG("param error");
        return false;
    }

    auto result = step->emplace(key, item);
    if (!result.first) {
        CWARNING_LOG("push item failed. key=%llu", key);
        return false;
    }

    if (!result.second) {
        *(result.first) += item;
    }

    if (pp_item != nullptr) {
        *pp_item = result.first;
    }
    return true;
}

template <typename Item>
template <typename Archive>
bool SlidingWindowV2<Item>::serialize(Archive* ar) const {
    if (!_check_segment_arg(_step_len, _max_step_num, _segment_len)) {
        CFATAL_LOG("inner segment conf error");
        return false;
    }
    if (_window == nullptr) {
        CFATAL_LOG("inner window is null");
        return false;
    }
    for (size_t step_idx = 0; step_idx < _window->size(); step_idx++) {
        if (_window->at(step_idx) == nullptr) {
            CWARNING_LOG(
                    "step in window is null, idx=%u size=%u max_step_num=%d",
                    step_idx,
                    _window->size(),
                    _max_step_num);
            return false;
        }
    }

    int segment_start_idx = (_segment_start_coord - _oldest_coord) / _step_len;
    if (segment_start_idx < 1 || segment_start_idx >= _max_step_num ||
        segment_start_idx >= static_cast<int>(_window->size())) {
        CFATAL_LOG(
                "error coord inner status, oldest=%lld start=%lld "
                "step_len=%lld max_step_num=%d segment_len=%lld",
                _oldest_coord,
                _segment_start_coord,
                _step_len,
                _max_step_num,
                _segment_len);
        return false;
    }

    SegmentConf sc = {_MAGIC_NUMBER, _step_len, _segment_len, _step_num};
    if (!t_write<SegmentConf, Archive>(sc, ar)) {
        CFATAL_LOG("dump segment conf error");
        return false;
    }

    int64_t step_start_coord = _segment_start_coord - _step_len;
    for (int step_idx = segment_start_idx - 1; step_idx < static_cast<int>(_window->size());
         step_idx++) {
        const StepContainer* sc = _window->at(step_idx);

        size_t node_num = sc->size();
        if (!t_write<size_t, Archive>(node_num, ar)) {
            CFATAL_LOG("dump step size error, step_idx=%d size=%u", step_idx, node_num);
            return false;
        }

        if (!t_write<int64_t, Archive>(step_start_coord, ar)) {
            CFATAL_LOG(
                    "dump step start coord error, step_idx=%d coord=%lld",
                    step_idx,
                    step_start_coord);
            return false;
        }

        const StepContainer* step = _window->at(step_idx);
        if (step == nullptr) {
            CFATAL_LOG("find a NULL step, step_idx=%d", step_idx);
            return false;
        }
        if (!_serialize_step(*step, ar)) {
            CFATAL_LOG("serialize a step error, step_idx=%d", step_idx);
            return false;
        }

        step_start_coord += _step_len;
    }

    return true;
}

template <typename Item>
template <typename Archive>
bool SlidingWindowV2<Item>::_serialize_step(const StepContainer& step, Archive* ar) const {
    if (ar == nullptr) {
        CFATAL_LOG("param error");
        return false;
    }

    // 关键修改：使用traverse而不是迭代器
    bool success = true;
    step.traverse([&](const uint64_t& key, const Item& item) {
        if (!success)
            return;  // 早期退出
        if (!t_write<uint64_t, Archive>(key, ar)) {
            CFATAL_LOG("serialize key error");
            success = false;
            return;
        }
        if (!item.serialize(ar)) {
            CFATAL_LOG("serialize item error");
            success = false;
            return;
        }
    });

    return success;
}

template <typename Item>
template <typename Archive>
bool SlidingWindowV2<Item>::deserialize(Archive* ar) {
    if (ar == nullptr) {
        CFATAL_LOG("param error");
        return false;
    }
    if (_segment == nullptr || _window == nullptr) {
        CFATAL_LOG("inner state error, _segment=%p _window=%p", _segment, _window);
        return false;
    }
    if (_max_step_num != static_cast<int>(_window->size())) {
        CFATAL_LOG(
                "window size isn't match max step num, "
                "window.size=%u max_step_num=%d",
                _window->size(),
                _max_step_num);
        return false;
    }

    if (!_check_segment_arg(_step_len, _max_step_num, _segment_len)) {
        CFATAL_LOG(
                "segment conf error, step_len=%ld max_step_num=%d _segment_len=%ld",
                _step_len,
                _max_step_num,
                _segment_len);
        return false;
    }
    _clear();

    SegmentConf sc;
    if (!t_read<SegmentConf, Archive>(&sc, ar)) {
        CFATAL_LOG("load segment conf error");
        return false;
    }
    if (sc.magic != _MAGIC_NUMBER || sc.step_len != _step_len || sc.segment_len != _segment_len ||
        sc.step_num != _step_num) {
        CFATAL_LOG(
                "load segment conf error, "
                "read[%0X, %lld, %lld, %d]"
                "expected[%0X, %lld, %lld, %d]",
                sc.magic,
                sc.step_len,
                sc.segment_len,
                sc.step_num,
                _MAGIC_NUMBER,
                _step_len,
                _segment_len,
                _step_num);
        return false;
    }

    for (int step_idx = 0; step_idx < _step_num + 1; ++step_idx) {
        size_t node_num = 0U;
        int64_t coord = 0L;
        if (!t_read<size_t, Archive>(&node_num, ar)) {
            CFATAL_LOG("read node num error, step_idx=%d", step_idx);
            return false;
        }
        if (!t_read<int64_t, Archive>(&coord, ar)) {
            CFATAL_LOG("read step start coord error, step_idx=%d", step_idx);
            return false;
        }

        for (size_t node_idx = 0U; node_idx < node_num; ++node_idx) {
            uint64_t key = 0U;
            if (!t_read<uint64_t, Archive>(&key, ar)) {
                CFATAL_LOG("read key error, step_idx=%d node_idx=%u", step_idx, node_idx);
                return false;
            }

            Item item(0, 0, _mem_pool);  // 使用内存池构造Item
            if (!(item.template deserialize<Archive>(ar))) {
                CFATAL_LOG("deserialize a item error, step_idx=%d node_idx=%d", step_idx, node_idx);
                return false;
            }
            this->enter(key, coord, item);
        }
    }

    return true;
}

template <typename Item>
uint64_t SlidingWindowV2<Item>::_calculate_overhead_memory() const {
    uint64_t overhead_mem = 0;

    // 1. SlidingWindowV2对象本身
    overhead_mem += sizeof(SlidingWindowV2<Item>);

    // 2. WindowContainer (std::deque<StepContainer*>)
    if (_window) {
        overhead_mem += sizeof(WindowContainer);
        // std::deque没有capacity()方法，使用size()进行估算
        overhead_mem += _window->size() * sizeof(StepContainer*);
    }

    // 3. SignStartCoord (std::unordered_map<uint64_t, ContinueCoordinate>)
    if (_sign_start_coord) {
        overhead_mem += sizeof(SignStartCoord);
        overhead_mem += _sign_start_coord->bucket_count() * sizeof(void*);
        overhead_mem += _sign_start_coord->size() *
                (sizeof(std::pair<uint64_t, ContinueCoordinate>) + sizeof(void*));
    }

    return overhead_mem;
}

template <typename Item>
void SlidingWindowV2<Item>::monitor(bsl::var::Dict& dict, bsl::ResourcePool& rp) const {
    try {
        // === 基础数据统计 ===
        uint32_t segment_size = _segment ? _segment->size() : 0;

        uint64_t total_window_items = 0;
        if (_window) {
            for (const auto& step : *_window) {
                if (step) {
                    total_window_items += step->size();
                }
            }
        }

        uint64_t total_elements = segment_size + total_window_items;
        dict["TOTAL_ELEMENTS"] = rp.create<bsl::var::Int64>(total_elements);
        dict["SEGMENT_ELEMENTS"] = rp.create<bsl::var::Int64>(segment_size);
        dict["WINDOW_ELEMENTS"] = rp.create<bsl::var::Int64>(total_window_items);

        // === 坐标信息 ===
        dict["SEGMENT_LENGTH"] = rp.create<bsl::var::Int64>(_segment_len);
        dict["OLDEST_COORD"] = rp.create<bsl::var::Int64>(_oldest_coord);
        dict["LATEST_COORD"] = rp.create<bsl::var::Int64>(_latest_coord);
        dict["SEGMENT_START_COORD"] = rp.create<bsl::var::Int64>(_segment_start_coord);
        dict["STEP_LENGTH"] = rp.create<bsl::var::Int64>(_step_len);
        dict["STEP_NUM"] = rp.create<bsl::var::Int32>(_step_num);

        // === Segment哈希表状态 ===
        if (_segment) {
            dict["SEGMENT_SIZE"] = rp.create<bsl::var::Int64>(_segment->size());
            dict["SEGMENT_BUCKET_COUNT"] = rp.create<bsl::var::Int64>(_segment->bucket_count());
            dict["SEGMENT_LOAD_FACTOR"] = rp.create<bsl::var::Double>(_segment->current_load_factor());
        } else {
            dict["SEGMENT_SIZE"] = rp.create<bsl::var::Int64>(0);
            dict["SEGMENT_BUCKET_COUNT"] = rp.create<bsl::var::Int64>(0);
            dict["SEGMENT_LOAD_FACTOR"] = rp.create<bsl::var::Double>(0.0);
        }

        // === 收集所有PoolHashMap的开销内存 ===
        uint64_t hashmap_overhead_mem = 0;

        if (_segment) {
            bsl::var::Dict segment_stats;
            _segment->monitor(segment_stats, rp);
            hashmap_overhead_mem += segment_stats["OVERHEAD_MEM"].to_uint64();
        }

        if (_window) {
            for (const auto& step : *_window) {
                if (step) {
                    bsl::var::Dict step_stats;
                    step->monitor(step_stats, rp);
                    hashmap_overhead_mem += step_stats["OVERHEAD_MEM"].to_uint64();
                }
            }
        }

        // === 计算SlidingWindowV2自身的开销内存 ===
        uint64_t sliding_window_overhead_mem = _calculate_overhead_memory();
        uint64_t total_sliding_window_overhead = hashmap_overhead_mem + sliding_window_overhead_mem;

        // === 根据内存池所有权确定监控模式 ===
        if (_own_mem_pool && _mem_pool) {
            // TOTAL模式：SlidingWindowV2拥有内存池
            bsl::var::Dict pool_stats;
            _mem_pool->monitor(pool_stats, rp);
            
            uint64_t data_mem = pool_stats["ACTUAL_MEM_USED"].to_uint64();
            uint64_t pool_mgmt_overhead = pool_stats["POOL_MANAGEMENT_OVERHEAD"].to_uint64();

            dict["DATA_MEM"] = rp.create<bsl::var::Int64>(data_mem);
            dict["POOL_MGMT_OVERHEAD"] = rp.create<bsl::var::Int64>(pool_mgmt_overhead);
            dict["SLIDING_WINDOW_OVERHEAD"] = rp.create<bsl::var::Int64>(total_sliding_window_overhead);
            dict["TOTAL_OVERHEAD"] = rp.create<bsl::var::Int64>(pool_mgmt_overhead + total_sliding_window_overhead);

        } else {
            // AUXILIARY模式：外部提供内存池，只统计滑动窗口开销
            dict["SLIDING_WINDOW_OVERHEAD"] = rp.create<bsl::var::Int64>(total_sliding_window_overhead);
        }

    } catch (const bsl::Exception& e) {
        CFATAL_LOG("SlidingWindowV2::monitor, bsl exception occurred: %s.", e.what());
    } catch (...) {
        CFATAL_LOG("SlidingWindowV2::monitor, get monitoring info failed.");
    }
}

}  // namespace common_lib
}  // namespace themis
}  // namespace anti