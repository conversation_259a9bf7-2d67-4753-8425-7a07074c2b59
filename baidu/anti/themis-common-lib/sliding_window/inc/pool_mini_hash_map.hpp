// Copyright (c) 2025 Baidu.com, Inc. All Rights Reserved
// @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>(<EMAIL>)

#ifndef ANTI_THEMIS_COMMON_LIB_POOL_MINI_HASHMAP_HPP
#define ANTI_THEMIS_COMMON_LIB_POOL_MINI_HASHMAP_HPP

#include <functional>
#include <new>
#include <type_traits>
#include <cmath>
#include <cstdint>
#include <cstddef>
#include <com_log.h>
#include <gflags/gflags.h>
#include <bsl/var/Dict.h>
#include <bsl/var/Int32.h>
#include <bsl/var/Int64.h>
#include <bsl/var/Double.h>
#include <bsl/ResourcePool.h>

DECLARE_double(pool_hashmap_load_factor);
DECLARE_double(pool_hashmap_growth_factor);

namespace anti {
namespace themis {
namespace common_lib {

/**
 * @brief 基于内存池的HashMap，添加了桶数为 2 时的内存占用优化
 *
 * @note gflags 参数：
 *       - pool_hashmap_load_factor: 负载因子阈值
 *       - pool_hashmap_growth_factor: 扩容增长倍率
 *
 * @tparam Key 键类型
 * @tparam Value 值类型
 * @tparam MemPoolType 内存池类型，需要提供 malloc/free/mem_address接口
 */
template <typename Key, typename Value, typename MemPoolType>
class PoolMiniHashMap {
private:
    using VAddr = typename MemPoolType::TVaddr;

    struct HashNode {
        VAddr next;    // 链表下一个节点的内存池虚拟地址
        char data[0];  // 柔性数组，实际存储 Key 和 Value

        HashNode(VAddr next_addr, const Key& k, const Value& v) : next(next_addr) {
            new (data) Key(k);
            new (data + sizeof(Key)) Value(v);
        }

        ~HashNode() {
            // key和value不是HashNode的直接成员，无法RAII，需要显式析构
            key().~Key();
            value().~Value();
        }

        // 计算HashNode实际需要的内存大小, 编译期计算
        static constexpr uint32_t node_size() {
            return sizeof(VAddr) + sizeof(Key) + sizeof(Value);
        }

        Key& key() {
            return *reinterpret_cast<Key*>(data);
        }
        const Key& key() const {
            return *reinterpret_cast<const Key*>(data);
        }
        Value& value() {
            return *reinterpret_cast<Value*>(data + sizeof(Key));
        }
        const Value& value() const {
            return *reinterpret_cast<const Value*>(data + sizeof(Key));
        }
    };

    // === 内存布局 ===
    static_assert(
            offsetof(HashNode, data) == sizeof(VAddr),
            "HashNode: data field must immediately follow next field without padding");
    static_assert(
            std::is_trivially_copyable<VAddr>::value,
            "VAddr must be trivially copyable for safe memory operations");

    // === Key类型约束 ===
    static_assert(std::is_destructible<Key>::value, "Key must be destructible");
    static_assert(std::is_copy_constructible<Key>::value, "Key must be copy constructible");
    static_assert(std::is_default_constructible<std::hash<Key>>::value, "Key must be hashable");

    // === Value类型约束 ===
    static_assert(std::is_destructible<Value>::value, "Value must be destructible");
    static_assert(std::is_copy_constructible<Value>::value, "Value must be copy constructible");

    // === 运算符支持 ===
    template <typename T>
    using equality_check = decltype(std::declval<T>() == std::declval<T>());
    static_assert(
            std::is_same<bool, equality_check<Key>>::value,
            "Key must support equality comparison operator (==)");

    MemPoolType* _mem_pool;  // 内存池指针, 8Bytes
    
    // 优化的桶存储结构：2桶时直接存储，否则使用数组
    union BucketStorage {
        VAddr* array_buckets;     // 数组模式 (当桶数 > 2)
        struct {
            VAddr bucket0;        // 直接存储模式 (当桶数 = 2)
            VAddr bucket1;
        } direct_buckets;
        
        BucketStorage() {
            direct_buckets.bucket0 = MemPoolType::null();
            direct_buckets.bucket1 = MemPoolType::null();
        }
    } _bucket_storage;
    
    uint32_t _bucket_count;  // bucket数量, 4Bytes
    uint32_t _size;          // 当前存储的元素数量, 4Bytes

    /**
     * @brief 获取指定索引的桶
     */
    inline VAddr get_bucket(uint32_t index) const {
        if (_bucket_count == 2) {
            return (&_bucket_storage.direct_buckets.bucket0)[index & 1];
        } else {
            return _bucket_storage.array_buckets[index];
        }
    }

    /**
     * @brief 设置指定索引的桶
     */
    inline void set_bucket(uint32_t index, VAddr value) {
        if (_bucket_count == 2) {
            (&_bucket_storage.direct_buckets.bucket0)[index & 1] = value;
        } else {
            _bucket_storage.array_buckets[index] = value;
        }
    }

    /**
     * @brief 释放Node以及Node中的Key和Value所占用的内存，模仿delete操作
     */
    void destroy_node(VAddr addr) {
        if (addr == MemPoolType::null()) {
            return;
        }

        HashNode* node = static_cast<HashNode*>(_mem_pool->mem_address(addr));
        node->~HashNode();
        _mem_pool->free(addr);
    }

    /**
     * @brief 创建新的Node，模仿new操作
     */
    VAddr create_node(const Key& key, const Value& value, VAddr next) {
        VAddr new_addr = _mem_pool->malloc(HashNode::node_size());
        if (new_addr == MemPoolType::null()) {
            return MemPoolType::null();
        }

        HashNode* new_ptr = static_cast<HashNode*>(_mem_pool->mem_address(new_addr));
        new (new_ptr) HashNode(next, key, value);
        return new_addr;
    }

    /**
     * @brief 从虚拟地址获取HashNode指针
     */
    HashNode* get_node_ptr(VAddr addr) const {
        if (addr == MemPoolType::null()) {
            return nullptr;
        }
        return static_cast<HashNode*>(_mem_pool->mem_address(addr));
    }

    /**
     * @brief 将key映射到bucket索引，支持2的幂次位运算优化
     */
    uint32_t hash_to_bucket_with_count(const Key& key, uint32_t bucket_count) const {
        if (bucket_count == 0) {
            return 0;
        }

        size_t hash_val = std::hash<Key>{}(key);

        // 如果bucket_count是2的幂次，使用位运算优化
        if ((bucket_count & (bucket_count - 1)) == 0) {
            return static_cast<uint32_t>(hash_val & (bucket_count - 1));
        } else {
            // 兼容非2的幂次的情况
            return static_cast<uint32_t>(hash_val % bucket_count);
        }
    }

    /**
     * @brief 将 key 映射到当前 bucket index
     */
    uint32_t hash_to_bucket(const Key& key) const {
        return hash_to_bucket_with_count(key, _bucket_count);
    }

    /**
    * @brief 计算rehash时的新大小
    */
    uint32_t calculate_new_size() const {
        double growth_factor = FLAGS_pool_hashmap_growth_factor;
        if (growth_factor <= 1.0) {
            growth_factor = 1.5;
        }

        // 直接按倍率计算，不强制2的幂
        uint64_t new_size_64 = static_cast<uint64_t>(_bucket_count * growth_factor);
        
        // 防止溢出
        if (new_size_64 > UINT32_MAX) {
            return UINT32_MAX;
        }
        
        uint32_t new_size = static_cast<uint32_t>(new_size_64);
        return std::max(1U, new_size);
    }

    /**
     * @brief 检查是否需要rehash
     */
    bool need_rehash() const {
        if (_bucket_count == 0) {
            return true;
        }
        return static_cast<double>(_size + 1) / _bucket_count > FLAGS_pool_hashmap_load_factor;
    }

    /**
     * @brief 迁移单个bucket中的所有节点
     */
    void migrate_bucket_nodes(
            VAddr bucket_head,
            VAddr* new_buckets,
            uint32_t new_bucket_count) {
        VAddr current_vaddr = bucket_head;

        while (current_vaddr != MemPoolType::null()) {
            HashNode* curr_ptr = get_node_ptr(current_vaddr);
            VAddr next_vaddr = curr_ptr->next;  // 保存原始next

            // 修改Node指针，插入到新桶中
            uint32_t new_idx = hash_to_bucket_with_count(curr_ptr->key(), new_bucket_count);
            curr_ptr->next = new_buckets[new_idx];
            new_buckets[new_idx] = current_vaddr;

            current_vaddr = next_vaddr;
        }
    }

    /**
     * @brief 重新分配哈希表的桶数组，重新哈希所有节点
     * @param new_bucket_count 新的桶数量
     * @return 扩容是否成功
     *
     * @note 失败时保持原状态不变
     */
    bool rehash(uint32_t new_bucket_count) {
        // 分配新的桶数组
        VAddr* new_buckets = new (std::nothrow) VAddr[new_bucket_count];
        if (!new_buckets) {
            CFATAL_LOG(
                    "PoolMiniHashMap::rehash, memory allocation failed, requested count: %u, "
                    "total bucket memory: %lu bytes.",
                    new_bucket_count,
                    static_cast<size_t>(new_bucket_count) * sizeof(VAddr));
            return false;
        }

        // 初始化新的桶数组
        for (uint32_t i = 0; i < new_bucket_count; ++i) {
            new_buckets[i] = MemPoolType::null();
        }

        // 保存当前状态
        bool was_direct_mode = (_bucket_count == 2);
        uint32_t old_bucket_count = _bucket_count;
        VAddr* old_array_buckets = was_direct_mode ? nullptr : _bucket_storage.array_buckets;

        // 迁移节点
        if (was_direct_mode) {
            // 从直接模式迁移
            migrate_bucket_nodes(_bucket_storage.direct_buckets.bucket0, new_buckets, new_bucket_count);
            migrate_bucket_nodes(_bucket_storage.direct_buckets.bucket1, new_buckets, new_bucket_count);
        } else {
            // 从数组模式迁移
            for (uint32_t i = 0; i < old_bucket_count; ++i) {
                migrate_bucket_nodes(_bucket_storage.array_buckets[i], new_buckets, new_bucket_count);
            }
        }

        // 更新为数组模式
        _bucket_storage.array_buckets = new_buckets;
        _bucket_count = new_bucket_count;
        
        // 释放旧的桶数组
        if (old_array_buckets) {
            delete[] old_array_buckets;
        }

        return true;
    }

public:
    /********************* 静态工具方法 *********************/

    static constexpr uint32_t get_node_size() {
        return HashNode::node_size();
    }

    /********************* 构造和析构 *********************/

    explicit PoolMiniHashMap(MemPoolType* pool, uint32_t initial_bucket_count = 2) :
            _mem_pool(pool), _bucket_count(0), _size(0) {
        if (!_mem_pool) {
            CFATAL_LOG("PoolMiniHashMap::PoolMiniHashMap, memory pool pointer is null.");
            return;  // 保持对象在可析构状态
        }

        uint32_t desired_bucket_count = std::max(1U, initial_bucket_count);

        if (desired_bucket_count == 2) {
            // 使用直接模式，无需额外分配
            _bucket_count = 2;
        } else {
            // 使用数组模式
            _bucket_storage.array_buckets = new (std::nothrow) VAddr[desired_bucket_count];
            if (!_bucket_storage.array_buckets) {
                CFATAL_LOG(
                        "PoolMiniHashMap::PoolMiniHashMap, bucket array allocation failed, requested size: %u.",
                        desired_bucket_count);
                // _bucket_count保持为0，确保对象可以安全析构
                return;
            }

            _bucket_count = desired_bucket_count;
            for (uint32_t i = 0; i < _bucket_count; ++i) {
                _bucket_storage.array_buckets[i] = MemPoolType::null();
            }
        }
    }

    ~PoolMiniHashMap() {
        clear();
        if (_bucket_count > 2) {
            delete[] _bucket_storage.array_buckets;
        }
    }

    /**
     * @brief 拷贝构造函数 - 实现深拷贝，共享内存池
     */
    PoolMiniHashMap(const PoolMiniHashMap& other) 
        : _mem_pool(other._mem_pool),      // 复用相同内存池
          _bucket_count(0),
          _size(0) {
        
        if (!_mem_pool || other._bucket_count == 0) {
            // 保持对象在可析构状态
            return;
        }
        
        // 初始化桶存储结构
        if (other._bucket_count == 2) {
            // 使用直接存储模式
            _bucket_count = 2;
            // direct_buckets 在 union 构造时已经初始化为 null
        } else {
            // 使用数组模式
            _bucket_storage.array_buckets = new (std::nothrow) VAddr[other._bucket_count];
            if (!_bucket_storage.array_buckets) {
                CFATAL_LOG("PoolMiniHashMap copy constructor: bucket array allocation failed");
                return;
            }
            
            _bucket_count = other._bucket_count;
            for (uint32_t i = 0; i < _bucket_count; ++i) {
                _bucket_storage.array_buckets[i] = MemPoolType::null();
            }
        }
        
        // 深拷贝所有节点
        for (uint32_t i = 0; i < other._bucket_count; ++i) {
            VAddr current = other.get_bucket(i);
            while (current != MemPoolType::null()) {
                const HashNode* node = other.get_node_ptr(current);
                // 在新HashMap中插入节点，实现深拷贝
                // emplace会在内存池中分配新节点
                emplace(node->key(), node->value());
                current = node->next;
            }
        }
    }

    /**
     * @brief 拷贝赋值运算符 - 实现深拷贝
     */
    PoolMiniHashMap& operator=(const PoolMiniHashMap& other) {
        if (this != &other) {
            // 清空当前内容，释放所有节点到内存池
            clear();
            
            // 确保使用相同的内存池
            _mem_pool = other._mem_pool;
            
            if (!_mem_pool || other._bucket_count == 0) {
                // 重置为初始状态
                if (_bucket_count > 2) {
                    delete[] _bucket_storage.array_buckets;
                }
                _bucket_count = 0;
                _size = 0;
                return *this;
            }
            
            // 重新分配桶数组（如果大小不同）
            if (_bucket_count != other._bucket_count) {
                if (_bucket_count > 2) {
                    delete[] _bucket_storage.array_buckets;
                }
                
                if (other._bucket_count == 2) {
                    // 切换到直接存储模式
                    _bucket_count = 2;
                    // direct_buckets 会自动初始化为 null
                } else {
                    // 使用数组模式
                    _bucket_storage.array_buckets = new (std::nothrow) VAddr[other._bucket_count];
                    if (!_bucket_storage.array_buckets) {
                        CFATAL_LOG("PoolMiniHashMap assignment: bucket array allocation failed");
                        _bucket_count = 0;
                        _size = 0;
                        return *this;
                    }
                    
                    _bucket_count = other._bucket_count;
                    for (uint32_t i = 0; i < _bucket_count; ++i) {
                        _bucket_storage.array_buckets[i] = MemPoolType::null();
                    }
                }
            } else {
                // 桶数量相同，只需要清空现有桶
                for (uint32_t i = 0; i < _bucket_count; ++i) {
                    if (_bucket_count == 2) {
                        if (i == 0) {
                            _bucket_storage.direct_buckets.bucket0 = MemPoolType::null();
                        } else {
                            _bucket_storage.direct_buckets.bucket1 = MemPoolType::null();
                        }
                    } else {
                        _bucket_storage.array_buckets[i] = MemPoolType::null();
                    }
                }
            }
            
            _size = 0;
            
            // 深拷贝所有节点
            for (uint32_t i = 0; i < other._bucket_count; ++i) {
                VAddr current = other.get_bucket(i);
                while (current != MemPoolType::null()) {
                    const HashNode* node = other.get_node_ptr(current);
                    emplace(node->key(), node->value());
                    current = node->next;
                }
            }
        }
        return *this;
    }

    /********************* 状态查询 *********************/

    /**
     * @brief 检查哈希表是否处于有效状态
     */
    bool is_valid() const {
        return _mem_pool != nullptr && _bucket_count > 0;
    }

    /**
     * @brief 获取当前元素数量
     */
    uint32_t size() const {
        return _size;
    }

    /**
     * @brief 获取当前桶数量
     */
    uint32_t bucket_count() const {
        return _bucket_count;
    }

    /**
     * @brief 获取当前负载因子
     */
    float current_load_factor() const {
        return (_bucket_count == 0) ? 0.0f : static_cast<float>(_size) / _bucket_count;
    }

    /********************* 核心操作 *********************/

    /**
     * @brief 查找键对应的Value指针
     * @return 如果找到则返回Value的指针，否则返回 nullptr
     */
    Value* find(const Key& key) {
        uint32_t bucket_idx = hash_to_bucket(key);
        VAddr curr_vaddr = get_bucket(bucket_idx);

        while (curr_vaddr != MemPoolType::null()) {
            HashNode* curr_node = get_node_ptr(curr_vaddr);
            if (curr_node->key() == key) {
                return &(curr_node->value());
            }
            curr_vaddr = curr_node->next;
        }
        return nullptr;
    }

    /**
     * @brief 查找键对应的Value指针（const版本）
     */
    const Value* find(const Key& key) const {
        uint32_t bucket_idx = hash_to_bucket(key);
        VAddr curr_vaddr = get_bucket(bucket_idx);
        while (curr_vaddr != MemPoolType::null()) {
            const HashNode* curr_node = get_node_ptr(curr_vaddr);
            if (curr_node->key() == key) {
                return &(curr_node->value());
            }
            curr_vaddr = curr_node->next;
        }
        return nullptr;
    }

    /**
     * @brief 插入键值对，如果Key已存在则不更新
     * @return pair<Value*, bool> 其中 Value*指向值的位置，bool表示是否为新插入
     */
    std::pair<Value*, bool> emplace(const Key& key, const Value& value) {
        // 先查找是否已存在
        uint32_t bucket_idx = hash_to_bucket(key);
        VAddr current_vaddr = get_bucket(bucket_idx);

        while (current_vaddr != MemPoolType::null()) {
            HashNode* node_ptr = get_node_ptr(current_vaddr);
            if (node_ptr->key() == key) {
                return {&(node_ptr->value()), false};
            }
            current_vaddr = node_ptr->next;
        }

        // 检查是否需要扩容
        if (need_rehash()) {
            uint32_t new_buckets_num = calculate_new_size();
            if (!rehash(new_buckets_num)) {
                CFATAL_LOG(
                        "PoolMiniHashMap::emplace, rehash failed, old bucket count: %u, new bucket "
                        "count: %u.",
                        _bucket_count,
                        new_buckets_num);
                return {nullptr, false};
            }
            bucket_idx = hash_to_bucket(key);
        }

        // 创建新节点并插入
        VAddr old_head = get_bucket(bucket_idx);
        VAddr node_vaddr = create_node(key, value, old_head);
        if (node_vaddr == MemPoolType::null()) {
            return {nullptr, false};
        }

        set_bucket(bucket_idx, node_vaddr);
        _size++;

        HashNode* new_node = get_node_ptr(node_vaddr);
        return {&(new_node->value()), true};
    }

    /**
     * @brief 删除指定Key
     * @return 是否删除成功
     */
    bool remove(const Key& key) {
        if (_size == 0) {
            return false;
        }

        uint32_t bucket_idx = hash_to_bucket(key);
        VAddr curr_vaddr = get_bucket(bucket_idx);
        VAddr prev_vaddr = MemPoolType::null();

        while (curr_vaddr != MemPoolType::null()) {
            HashNode* curr_ptr = get_node_ptr(curr_vaddr);
            if (curr_ptr->key() == key) {
                // 修复链表结构
                if (prev_vaddr == MemPoolType::null()) {
                    set_bucket(bucket_idx, curr_ptr->next);
                } else {
                    HashNode* prev_ptr = get_node_ptr(prev_vaddr);
                    prev_ptr->next = curr_ptr->next;
                }

                destroy_node(curr_vaddr);
                _size--;
                return true;
            }
            prev_vaddr = curr_vaddr;
            curr_vaddr = curr_ptr->next;
        }
        return false;
    }

    /**
     * @brief 清空哈希表
     */
    void clear() {
        if (_bucket_count == 0) {
            return;
        }
        for (uint32_t i = 0; i < _bucket_count; ++i) {
            VAddr current_vaddr = get_bucket(i);
            while (current_vaddr != MemPoolType::null()) {
                HashNode* curr_ptr = get_node_ptr(current_vaddr);
                VAddr next_vaddr = curr_ptr->next;
                destroy_node(current_vaddr);
                current_vaddr = next_vaddr;
            }
            set_bucket(i, MemPoolType::null());
        }
        _size = 0;
    }

    /********************* 遍历操作 *********************/

    /**
     * @brief 遍历所有KV（const版本）
     */
    void traverse(std::function<void(const Key&, const Value&)> callback) const {
        if (_bucket_count == 0 || !callback) {
            return;
        }
        for (uint32_t i = 0; i < _bucket_count; ++i) {
            VAddr curr_vaddr = get_bucket(i);
            while (curr_vaddr != MemPoolType::null()) {
                const HashNode* curr_ptr = get_node_ptr(curr_vaddr);
                VAddr next_vaddr = curr_ptr->next;
                callback(curr_ptr->key(), curr_ptr->value());
                curr_vaddr = next_vaddr;
            }
        }
    }

    /**
     * @brief 遍历所有KV（可修改值）
     */
    void traverse(std::function<void(const Key&, Value&)> callback) {
        if (_bucket_count == 0 || !callback) {
            return;
        }
        for (uint32_t i = 0; i < _bucket_count; ++i) {
            VAddr curr_vaddr = get_bucket(i);
            while (curr_vaddr != MemPoolType::null()) {
                HashNode* curr_ptr = get_node_ptr(curr_vaddr);
                VAddr next_vaddr = curr_ptr->next;
                callback(curr_ptr->key(), curr_ptr->value());
                curr_vaddr = next_vaddr;
            }
        }
    }

    /********************* 监控 *********************/

    /**
     * @brief 监控
     */
    void monitor(bsl::var::Dict& dict, bsl::ResourcePool& rp) const {
        try {
            // === 开销内存 ===
            uint64_t hashmap_obj_mem = sizeof(PoolMiniHashMap<Key, Value, MemPoolType>);
            uint64_t bucket_array_mem = 0;
            
            // 只有当使用数组模式时才计算数组内存
            if (_bucket_count > 2) {
                bucket_array_mem = _bucket_count * sizeof(VAddr);
            }
            
            uint64_t total_overhead_mem = hashmap_obj_mem + bucket_array_mem;

            dict["OVERHEAD_MEM"] = rp.create<bsl::var::Int64>(total_overhead_mem);

        } catch (const bsl::Exception& e) {
            CFATAL_LOG("PoolMiniHashMap::monitor, bsl exception occurred: %s.", e.what());
        } catch (...) {
            CFATAL_LOG("PoolMiniHashMap::monitor, get monitoring info failed.");
        }
    }
};
}  // namespace common_lib
}  // namespace themis
}  // namespace anti
#endif  // ANTI_THEMIS_COMMON_LIB_POOL_MINI_HASHMAP_HPP