/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/

/**
 * @file: sliding_segemnt.h
 * @author: knzeus(<EMAIL>) 
 * @date: 2015/03/30 22:23:33
 * @brief: The component of sliding segment
 *         Support:
 *             1. seglimit/comparelimit feature cumulant
 *             2. log<PERSON>'s secondary record cache
 *             3. timediff feature cache
 *
 **/

#ifndef ANTI_THEMIS_COMMON_LIB_SLIDING_WINDOW_H
#define ANTI_THEMIS_COMMON_LIB_SLIDING_WINDOW_H

#include <deque>
#include <utility>
#include <vector>
#include <list>
#include <unordered_map>

#include "continue_coordinate.h"

namespace anti {
namespace themis {
namespace common_lib { 
/*
 * insert return
*/
enum {
    SLIDING_WINDOW_SLIDE = 0xfffe, 
    SLIDING_WINDOW_SUCC, 
    SLIDING_WINDOW_FAIL 
};

template<typename Item>
class SlidingWindow {
public:
    SlidingWindow();

    ~SlidingWindow();

    bool init(
            int64_t step_len, 
            int max_step_num, 
            int64_t segment_len);

    // @brief clear data and delete member
    void uninit();

    // enter and return whether it slide
    int insert(
            const uint64_t key, 
            const int64_t coord, 
            const Item& item);

    int insert(
            const uint64_t key, 
            const int64_t coord, 
            const Item& item, 
            const Item** pp_seg_item);

    bool enter(
            const uint64_t key, 
            const int64_t coord, 
            const Item& item);

    bool enter(
            const uint64_t key, 
            const int64_t coord, 
            const Item& item, 
            const Item** pp_seg_item);

    const Item* query_segment(const uint64_t key) const;
    const Item* query_segment(const uint64_t key, int64_t coord) const;

    Item query_last_segment(const uint64_t key) const;

    int64_t latest_coord() const {
        return _latest_coord;
    }

    int64_t step_length() const {
        return _step_len;
    }

    uint64_t window_view_sign_size();

    uint64_t segment_view_sign_size();

    int64_t segment_start_coord() const {
        return _segment_start_coord;
    }

    int32_t step_num() const {
        return _step_num;
    }

    void set_log_key_first_appear();
    int64_t query_start_coord(const uint64_t key) const;

    template<typename Archive>
    bool serialize(Archive* ar) const;

    template<typename Archive>
    bool deserialize(Archive* ar);
    int64_t sliding_segment_start_coord(
            int64_t coord,
            int64_t step_len,
            int64_t step_num) {
        return coord - coord % step_len - step_len * (step_num - 1);
    }

private:
    typedef std::unordered_map<uint64_t, Item> StepContainer;
    typedef std::deque<StepContainer*> WindowContainer;
    typedef std::unordered_map<uint64_t, ContinueCoordinate> SignStartCoord;
    typedef typename StepContainer::iterator SCIter;
    typedef typename StepContainer::const_iterator SCCIter;
    typedef typename WindowContainer::iterator WCIter;
    typedef typename WindowContainer::const_iterator WCCIter;

    bool _check_segment_arg(
            int64_t step_len, 
            int max_step_num, 
            int64_t segment_len) const;

    bool _find(
            const uint64_t key, 
            const int64_t coord, 
            Item** p_step_item, 
            Item** p_segment_item,
            int* step_pos);

    // @retval: the coord of slide, mulitple of step_len
    int64_t _slide_to_coord(int64_t coord);

    // @brief: clear data but not delete member
    void _clear();

    void _slide_one_step();

    void _move_and_clear_oldest_step();

    void _delete_first_step_in_segment();

    StepContainer* _first_step_in_segment() const;

    StepContainer* _find_step(int64_t coord) const;

    int64_t _step_start_coord(int64_t coord) const;

    bool _push_item(StepContainer* step,
            const uint64_t key,
            const Item& item,
            const Item** pp_item);

    const Item* _query(
            const StepContainer& from, 
            const uint64_t key) const;
    
    bool _in_segment(const int64_t coord) const;

    template<typename Archive>
    bool _serialize_step(const StepContainer& step, Archive* ar) const;

private:
    // Restriction: 
    //        segment_len % step_len == 0
    //        segment_len >= step_len > 0
    //        segment_len = step_len * step_num
    //        max_step_num > step_num
    int64_t _step_len;
    int _step_num;
    int _max_step_num;
    int64_t _segment_len;

    int64_t _oldest_coord;
    int64_t _segment_start_coord;
    int64_t _latest_coord;

    StepContainer* _segment;
    WindowContainer* _window;
    SignStartCoord* _sign_start_coord;
    static const uint64_t _MAGIC_NUMBER;

private:
    struct SegmentConf {
        uint64_t magic;
        int64_t step_len;
        int64_t segment_len;
        int32_t step_num;
    };
};

template<typename Item>
const uint64_t SlidingWindow<Item>::_MAGIC_NUMBER = 0xC29FA627495AFA51;

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

#include "sliding_window.hpp"

#endif  // ANTI_THEMIS_COMMON_LIB_SLIDING_WINDOW_H


