/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * $Id$ 
 * 
 **************************************************************************/
 
/**
 * @file sliding_window/dev/utils.hpp
 * <AUTHOR>
 * @date 2015/04/01 16:11:18
 * @version $Revision$ 
 * @brief 
 *  
 **/

#include <com_log.h>

namespace anti {
namespace themis {
namespace common_lib {

template<typename Key, typename Item>
void map_union(std::unordered_map<Key, Item>* lhs, 
        const std::unordered_map<Key, Item>* rhs) {
    if (lhs == NULL || rhs == NULL) {
        CFATAL_LOG("param error");
        return ;
    }
    typedef typename std::unordered_map<Key, Item>::iterator Iterator;
    typedef typename std::unordered_map<Key, Item>::const_iterator CIterator;
    for (CIterator rit = rhs->begin(); rit != rhs->end(); rit++) {
        Iterator lit = lhs->find(rit->first);
        if (lit != lhs->end()) {
            lit->second += rit->second;
        } else {
            lhs->emplace(rit->first, rit->second);
        }
    }
    return ;
}

template<typename Key, typename Item>
void sub(std::unordered_map<Key, Item>* minuend, 
        std::unordered_map<Key, Item>* subtrahend) {
    if (minuend == NULL
            || subtrahend == NULL) {
        CFATAL_LOG("param error");
        return ;
    }
    typedef typename std::unordered_map<Key, Item>::iterator Iterator;
    typedef typename std::unordered_map<Key, Item>::const_iterator CIterator;
    for (CIterator subt = subtrahend->begin();
            subt != subtrahend->end();
            subt++) {
        Iterator minu = minuend->find(subt->first);
        if (minu != minuend->end()) {
            minu->second -= subt->second;
            if (minu->second.is_null()) {
                minuend->erase(minu);
            }
        }
    }
    return ;
}

template<typename Key, typename Item>
void map_difference(std::unordered_map<Key, Item>* minuend, 
        const std::unordered_map<Key, Item>* subtrahend) {
    if (minuend == NULL
            || subtrahend == NULL) {
        CFATAL_LOG("param error");
        return ;
    }
    typedef typename std::unordered_map<Key, Item>::iterator Iterator;
    typedef typename std::unordered_map<Key, Item>::const_iterator CIterator;
    CDEBUG_LOG("subtrahend SIZE[%d]", subtrahend->size());
    for (CIterator subt = subtrahend->begin();
            subt != subtrahend->end();
            ++subt) {
        CDEBUG_LOG("key [%ld]", subt->first);
        Iterator minu = minuend->find(subt->first);
        if (minu != minuend->end()) {
            CDEBUG_LOG("minu->second[%ld], subt->second[%ld]",
                    minu->second, subt->second);
            minu->second -= subt->second;
            if (minu->second <= 0) {
                minu = minuend->erase(minu);
            }
        }
    }
    return ;
}

template<typename Key, typename Item, typename MemPoolType>
void pool_sub(PoolHashMap<Key, Item, MemPoolType>* minuend, 
              PoolHashMap<Key, Item, MemPoolType>* subtrahend) {
    if (minuend == NULL || subtrahend == NULL) {
        CFATAL_LOG("param error");
        return;
    }
    
    subtrahend->traverse([minuend](const Key& key, const Item& value) {
        Item* existing = minuend->find(key);
        if (existing) {
            *existing -= value;
            if (existing->is_null()) {
                minuend->remove(key);
            }
        }
    });
    return;
}

template<typename Node, typename Archive>
bool t_write(const Node& node, Archive* ar) {
    if (ar == NULL) {
        CFATAL_LOG("param error");
        return false;
    }
    if (static_cast<int>(sizeof(node)) != 
            ar->write(reinterpret_cast<const void*>(&node), sizeof(node))) {
        return false;
    }
    return true;
}

template<typename Node, typename Archive>
bool t_read(Node* node, Archive* ar) {
    if (node == NULL
            || ar == NULL) {
        CFATAL_LOG("param error");
        return false;
    }
    if (static_cast<int>(sizeof(Node)) != 
            ar->read(reinterpret_cast<void*>(node), sizeof(Node))) {
        return false;
    }
    return true;
}

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4 sts=4 tw=100 */
