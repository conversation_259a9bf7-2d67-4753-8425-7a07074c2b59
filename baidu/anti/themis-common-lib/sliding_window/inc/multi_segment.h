// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @brief 

#ifndef ANTI_THEMIS_COMMON_LIB_MULTI_SEGMENT_H
#define ANTI_THEMIS_COMMON_LIB_MULTI_SEGMENT_H

#include <unordered_map>
#include <memory>
#include <deque>
#include <com_log.h>
#include "utils.h"

namespace anti {
namespace themis {
namespace common_lib {

static const int32_t kMultiSegRemainStep = 2;

template<typename Item>
class MultiSegment {
public:
    MultiSegment() {}
    ~MultiSegment() { uninit(); }

    bool init(int64_t step_len, int64_t segment_len);
    void uninit();

    bool enter(const uint64_t key, const int64_t coord,
            const Item& item, const Item** result);
    const Item* query(const uint64_t key, const int64_t coord) const;
    int64_t window_view_sign_size() {
        int64_t window_view_sign_size = 0;
        for (const auto& node : _window) {
            if (node == nullptr) {
                continue;
            }
            window_view_sign_size += node->size();
        }
        return window_view_sign_size;
    }

    int64_t lastest_coord() const {
        return _latest_coord;
    }
    int64_t start_coord() const {
        return _segment_start_coord;
    }

    template<typename Archive>
    bool serialize(Archive* ar) const;

    template<typename Archive>
    bool deserialize(Archive* ar);

private:
    typedef std::unordered_map<uint64_t, Item> StepContainer;
    typedef std::shared_ptr<StepContainer> StepContainerPtr;
    typedef std::deque<StepContainerPtr> WindowContainer;

    const Item* query(const StepContainerPtr container,
        const uint64_t key) const;
    bool _push_item(StepContainerPtr ptr, const uint64_t key,
            const Item& item, const Item** acc);
    StepContainerPtr _find_step(const int64_t coord) const;
    int64_t _slide_to_coord(int64_t coord);
    void _slide_one_step();
    void _move_and_clear_oldest_step();

    template <typename Archive>
    bool _serialize_step(const StepContainer& step, Archive* ar) const;

private:
    struct SegmentConf {
        uint64_t magic;
        int64_t step_len;
        int64_t segment_len;
        int32_t step_num;
        int64_t version;
        int64_t reserved;
    };

    int64_t _step_len = 0;
    int _step_num = 0;
    int _max_step_num = 0;
    int64_t _segment_len = 0;

    int64_t _oldest_coord = 0;
    int64_t _segment_start_coord = 0;
    int64_t _latest_coord = 0;

    WindowContainer _window;
    static const uint64_t _MAGIC_NUMBER;
};

template<typename Item>
const uint64_t MultiSegment<Item>::_MAGIC_NUMBER = 0xC29FA627495AFA51;

template<typename Item>
bool MultiSegment<Item>::init(int64_t step_len, int64_t segment_len) {
    _step_len = step_len;
    _segment_len = segment_len;
    _step_num = _segment_len / _step_len;
    // actually store more steps for trace
    _max_step_num = _step_num + kMultiSegRemainStep;

    for (int32_t step_i = 0; step_i < _max_step_num; ++step_i) {
        StepContainerPtr ptr(new (std::nothrow) StepContainer());
        if (!ptr) {
            CFATAL_LOG("new step failed");
            return false;
        }
        _window.push_back(ptr);
    }

    _segment_start_coord = 0L;
    _latest_coord = 0;
    _oldest_coord = (_step_num - _max_step_num) * _step_len;

    return true;
}

template<typename Item>
void MultiSegment<Item>::uninit() {
    _segment_start_coord = 0L;
    _latest_coord = 0;
    _oldest_coord = (_step_num - _max_step_num) * _step_len;
    _window.clear();
}

template<typename Item>
const Item* MultiSegment<Item>::query(
        const StepContainerPtr container,
        const uint64_t key) const {
    auto iter = container->find(key);
    if (iter == container->end()) {
        return NULL;
    } else {
        return &(iter->second);
    }
}

template<typename Item>
const Item* MultiSegment<Item>::query(const uint64_t key,
        const int64_t coord) const {
    auto step = _find_step(coord);
    if (!step) { return NULL; }

    return query(step, key);
}

template<typename Item>
bool MultiSegment<Item>::enter(const uint64_t key,
        const int64_t coord, const Item& item,
        const Item** result) {
    if (coord < _oldest_coord) {
        CWARNING_LOG("too old item, coord=%ld oldest=%ld lastest=%ld",
                coord, _oldest_coord, _latest_coord);
        return false;
    }

    if (coord > _latest_coord) { _latest_coord = coord; }
    if (coord >= _segment_len + _segment_start_coord) {
        _slide_to_coord(coord);
    }

    StepContainerPtr step = _find_step(coord);
    if (!step) {
        CFATAL_LOG("find a NULL step, coord=%ld", coord);
        return false;
    }

    if (!_push_item(step, key, item, result)) {
        CWARNING_LOG("push a item into step error, key=%llu", key);
        return false;
    }

    return true;
}

template<typename Item>
bool MultiSegment<Item>::_push_item(StepContainerPtr step,
        const uint64_t key, const Item& item, const Item** acc) {
    if (!step) {
        CFATAL_LOG("param error");
        return false;
    }

    auto iter = step->find(key);
    Item* inner = NULL;
    if (iter == step->end()) {
        auto ires = step->emplace(key, item);
        if (!ires.second) {
            CWARNING_LOG("push item failed. key=%llu", key);
            return false;
        }
        inner = &(ires.first->second);
    } else {
        iter->second += item;
        inner = &(iter->second);
    }

    if (acc != NULL) {
        *acc = inner;
    }
    return true;
}

template<typename Item>
typename MultiSegment<Item>::StepContainerPtr
MultiSegment<Item>::_find_step(int64_t coord) const {
    int pos = (coord - _oldest_coord) / _step_len;
    if (coord < _oldest_coord) { pos -= 1; }
    if (pos < 0 || pos >= static_cast<int>(_window.size())) {
        CFATAL_LOG("the coord is invalid. pos=%d, coord=%ld",
                pos, coord);
        return StepContainerPtr();
    }
    return _window.at(pos);
}

template<typename Item>
int64_t MultiSegment<Item>::_slide_to_coord(int64_t coord) {
    if (coord < _segment_start_coord + _segment_len)  { return 0; }
    int move_distance = coord - (_segment_start_coord + _segment_len);
    int slide_step = 1 + move_distance / _step_len;
    if (slide_step <= 0) { return 0; }

    int64_t slide_coord = _step_len * slide_step;
    if (slide_step < _max_step_num) {
        for (int i = 0; i < slide_step; ++i) {
            _slide_one_step();
        }
    } else {
        // clear ALL existed data
        for (int i = 0; i < _window.size(); ++i) { _window[i]->clear(); }

        _latest_coord = coord;
        int64_t remainder = coord % _step_len;
        _oldest_coord = coord - remainder - _step_len * (_max_step_num - 1);
        _segment_start_coord = coord - remainder - _step_len * (_step_num - 1);
    }

    return slide_coord;
} 

template<typename Item>
void MultiSegment<Item>::_slide_one_step() {
    // move segment
    _segment_start_coord += _step_len;

    // clear oldest step
    _move_and_clear_oldest_step();
}

template<typename Item>
void MultiSegment<Item>::_move_and_clear_oldest_step() {
    StepContainerPtr oldest_step = _window.front();
    _window.pop_front();
    if (!oldest_step) {
        CFATAL_LOG("the oldest step in window is NULL");
        return;
    }

    oldest_step->clear();
    _window.push_back(oldest_step);
    _oldest_coord += _step_len;
    return ;
}

template<typename Item>
template<typename Archive>
bool MultiSegment<Item>::serialize(Archive* ar) const {
    // check steps valid
    for (size_t i = 0; i < _window.size(); ++i) {
        if (!_window[i]) {
            CWARNING_LOG("step in window is null. idx=%u, size=%u, max_step_num=%d",
                    i, _window.size(), _max_step_num);
            return false;
        }
    }

    int segment_start_idx = (_segment_start_coord - _oldest_coord) / _step_len;
    if (segment_start_idx < 1
            || segment_start_idx >= _max_step_num
            || segment_start_idx >= static_cast<int>(_window.size())) {
        CFATAL_LOG("error coord inner status, oldest=%lld start=%lld "
                "step_len=%lld max_step_num=%d segment_len=%lld",
                _oldest_coord, _segment_start_coord, _step_len,
                _max_step_num, _segment_len);
        return false;
    }

    SegmentConf sc = {
            _MAGIC_NUMBER,
            _step_len,
            _segment_len,
            _step_num,
            1,
            0
    };
    if (!t_write<SegmentConf, Archive>(sc, ar)) {
        CFATAL_LOG("dump segment conf error");
        return false;
    }
    int64_t step_start_coord = _segment_start_coord - _step_len;
    for (int step_idx = segment_start_idx - 1; 
            step_idx < static_cast<int>(_window.size()); 
            step_idx++) {
        auto step = _window.at(step_idx);

        size_t node_num = step->size();
        if (!t_write<size_t, Archive>(node_num, ar)) {
            CFATAL_LOG("dump step size error, step_idx=%d size=%u",
                    step_idx, node_num);
            return false;
        }

        if (!t_write<int64_t, Archive>(step_start_coord, ar)) {
            CFATAL_LOG("dump step start coord error, step_idx=%d coord=%lld",
                    step_idx, step_start_coord);
            return false;
        }

        if (!_serialize_step(*step, ar)) {
            CFATAL_LOG("serialize a step error, step_idx=%d", step_idx);
            return false;
        }

        step_start_coord += _step_len;
    }

    return true;
}

template<typename Item>
template<typename Archive>
bool MultiSegment<Item>::_serialize_step(
        const StepContainer& step, 
        Archive* ar) const {
    if (ar == NULL) {
        CFATAL_LOG("param error");
        return false;
    }
    for (auto iter = step.begin();
            iter != step.end();
            ++iter) {
        if (!t_write<uint64_t, Archive>(iter->first, ar)) {
            CFATAL_LOG("serialize key error");
            return false;
        }
        if (!(iter->second.serialize(ar))) {
            CFATAL_LOG("serialize item error");
            return false;
        }
    }

    return true;
}

template<typename Item>
template <typename Archive>
bool MultiSegment<Item>::deserialize(Archive* ar) {
    if (ar == NULL) {
        CFATAL_LOG("param error");
        return false;
    }
    if (_max_step_num != static_cast<int>(_window.size())) {
        CFATAL_LOG("window size isn't match max step num, "
                "window.size=%u max_step_num=%d",
                _window.size(), _max_step_num);
        return false;
    }

    //_clear();

    SegmentConf sc;
    if (!t_read<SegmentConf, Archive>(&sc, ar)) {
        CFATAL_LOG("load segment conf error");
        return false;
    }
    if (sc.version != 1) {
        CWARNING_LOG("Important!!! The MultiSegment drops current checkpoint!");
        return true;
    }
    if (sc.magic != _MAGIC_NUMBER
            || sc.step_len != _step_len
            || sc.segment_len != _segment_len
            || sc.step_num != _step_num) {
        CFATAL_LOG("load segment conf error, "
                "read[%0X, %lld, %lld, %d]"
                "expected[%0X, %lld, %lld, %d]",
                sc.magic, sc.step_len, sc.segment_len, sc.step_num, 
                _MAGIC_NUMBER, _step_len, _segment_len, _step_num);
        return false;
    }

    for (int step_idx = 0; step_idx < _step_num + 1; ++step_idx) {
        size_t node_num = 0U;
        int64_t coord = 0L;
        if (!t_read<size_t, Archive>(&node_num, ar)) {
            CFATAL_LOG("read node num error, step_idx=%d", step_idx);
            return false;
        }
        if (!t_read<int64_t, Archive>(&coord, ar)) {
            CFATAL_LOG("read step start coord error, step_idx=%d", step_idx);
            return false;
        }

        for (size_t node_idx = 0U; node_idx < node_num; ++node_idx) {
            uint64_t key = 0U;
            if (!t_read<uint64_t, Archive>(&key, ar)) {
                CFATAL_LOG("read key error, step_idx=%d node_idx=%u", 
                        step_idx, node_idx);
                return false;
            }

            Item item;
            if (!(item.template deserialize<Archive>(ar))) {
                CFATAL_LOG("deserialize a item error, step_idx=%d node_idx=%d", 
                        step_idx, node_idx);
                return false;
            }

            this->enter(key, coord, item, NULL);
        }
    }

    return true;
}

}
}
}

#endif  // ANTI_THEMIS_COMMON_LIB_MULTI_SEGMENT_H
