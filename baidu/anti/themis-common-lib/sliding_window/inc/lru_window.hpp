// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huang<PERSON>(<EMAIL>)
// 
// @File: lru_window.h
// @Last modified: 2017-07-17 19:58:09
// @Brief: 

#ifndef ANTI_THEMIS_SLIDING_WINDOW_DEV_LRU_WINDOW_HPP
#define ANTI_THEMIS_SLIDING_WINDOW_DEV_LRU_WINDOW_HPP

#include <functional>
#include <list>
#include <unordered_map>
#include <memory>
#include <mutex>
#include <cassert>
#include <com_log.h>
#include "utils.h"

namespace anti {
namespace themis {
namespace common_lib { 
// node demo 
// node should implement
// 1. copy constructor
// 2. KEY_TYPE  key() interface
// 3. uint64_t coord() const interface
// 4. template serialize and deserialize interface with Archive

template<typename Key, typename Message>
class NodeAdaptor {
public:
    NodeAdaptor() : _coord(0) {}
    NodeAdaptor(const Key& key, const Message& msg, int64_t coord) :
            _key(key), _msg(msg), _coord(coord) {}

    NodeAdaptor(const Key& key, const Message& msg) : NodeAdaptor(key, msg, 0) {}

    NodeAdaptor(const NodeAdaptor& node) : NodeAdaptor(node._key, node._msg) {}
    
    int64_t coord() { return _coord; }

    const Key& key() { return _key; }
    const Message& value() { return _msg; }
    template<typename Archive>
    bool serialize(Archive* ar) {
        DUMP_VAR(_key, ar, Key);
        DUMP_VAR(_coord, ar, int64_t);
        return _msg.serialize(ar);
    }
    template<typename Archive>
    bool deserialize(Archive* ar) {
        LOAD_VAR(&_key, ar, Key);
        LOAD_VAR(&_coord, ar, int64_t);
        return _msg.deserialize(ar);
    }

private:
    Key _key;
    Message _msg;
    int64_t _coord;
};

template<typename Key, typename Node, 
        typename HashFunc = std::hash<Key>, 
        typename CompareFunc = std::equal_to<Key>>
class LRUWindow {
public:
    LRUWindow(int64_t window_length, int64_t max_list_num):
            _window_length(window_length),
            _max_list_num(max_list_num),
            _latest_coord(0) {}
    LRUWindow() : LRUWindow(0, 0) {}
    ~LRUWindow() {
        _node_list.clear();
        _node_index.clear();
    }

    typedef std::shared_ptr<Node> NodePtr;
    void insert(const NodePtr& node);
    void insert(const Node& node) {
        NodePtr ptr(new (std::nothrow) Node(node));
        assert(ptr);
        return insert(ptr);
    }
    NodePtr find(const Key& key);
    const NodePtr find(const Key& key) const;

    int64_t latest_coord() const { return _latest_coord; }
    template<typename Archive>
    bool serialize(Archive* ar) const;
    template<typename Archive, typename... Args> 
    bool deserialize(Archive* ar, Args... args);
    bool register_extra_gc_func(std::function<void(const Node&)> func) {
        _extra_gc_func = func; 
        return true;
    }
private:
    typedef std::list<NodePtr> NodeList;
    typedef typename NodeList::iterator NodeListIter;
    typedef std::unordered_map<Key, NodeListIter, HashFunc, CompareFunc> NodeIndex;
    typedef typename NodeIndex::iterator NodeIndexIter;

    void _add_node(const NodePtr& node);
    void _add_node(NodeIndexIter&, const NodePtr& node);
    void _gc();

private:
    std::function<void(const Node&)> _extra_gc_func;
    int64_t _window_length;
    int64_t _max_list_num;
    int64_t _latest_coord;

    std::list<NodePtr> _node_list;
    std::unordered_map<Key, NodeListIter, HashFunc, CompareFunc> _node_index;
    // @brief :
    //  MAGIC used for checkpoint verification
    static const uint64_t MAGIC = 0x31FACD83E2E12D09;
    struct WindowConf {
        uint64_t version;
        uint64_t magic;
        uint64_t node_num;
    };
};

template<typename Key, typename Node, typename HashFunc, typename CompareFunc>
void LRUWindow<Key, Node, HashFunc, CompareFunc>::insert(const NodePtr& node) {
    auto it = _node_index.find(node->key()); 
    if (it == _node_index.end()) {
        _add_node(node);
    } else {
        _add_node(it, node);
    }

    if (node->coord() > _latest_coord) {
        _latest_coord = node->coord();
        _gc();
    }
}

template<typename Key, typename Node, typename HashFunc, typename CompareFunc> 
template<typename Archive>
bool LRUWindow<Key, Node, HashFunc, CompareFunc>::serialize(Archive* ar) const {
    if (!ar) {
        CFATAL_LOG("Archive ptr invalid");
        return false;
    }
    const uint64_t VERSION = 1U;
    WindowConf wc = {VERSION, MAGIC, _node_index.size()};
    if (!t_write<WindowConf, Archive>(wc, ar)) {
        CFATAL_LOG("dump window conf fail");
        return false;
    }
    if (!t_write(_window_length, ar) || !t_write(_max_list_num, ar)) {
        CFATAL_LOG("dump window_length and max_list_num fail");
        return false;
    }
    for (auto iter = _node_list.rbegin(); iter != _node_list.rend(); ++iter) {
        if (!(*iter)->serialize(ar)) {
            CWARNING_LOG("dump an node fail");
            return false;
        }
    }
    return true;
}

template<typename Key, typename Node, typename HashFunc, typename CompareFunc> 
template<typename Archive, typename... Args>
bool LRUWindow<Key, Node, HashFunc, CompareFunc>::deserialize(Archive* ar, Args... args) {
    if (!ar) {
        CFATAL_LOG("Archive ptr invalid");
        return false;
    }
    const uint64_t VERSION = 1U;
    WindowConf wc;
    if (!t_read<WindowConf, Archive>(&wc, ar)
            || wc.version != VERSION
            || wc.magic != MAGIC) {
        CFATAL_LOG("readin WindowConf fail or verison and magic invalid,"
                    "expect version(%lu),read version(%lu)"
                    "expect MAGIC(%lx), read MAGIC(%lx)", 
                    VERSION, wc.version, MAGIC, wc.magic);
        return false;
    }
    int64_t win_len = 0;
    if (!t_read(&win_len, ar) || win_len != _window_length) {
        CFATAL_LOG("read window_length fail or window_length(%ld) from ckpt invalid, should be(%ld)",
                win_len, _window_length) ;
        return false;
    }
    int64_t max_list = 0;
    if (!t_read(&max_list, ar) || max_list != _max_list_num) {
        CFATAL_LOG("read max_item_num fail or max_item_num(%ld) from ckpt invalid, should be(%ld)",
                max_list, _max_list_num);
        return false;
    }
    for (int64_t i = 0; i < wc.node_num; ++i) {
        NodePtr node(new (std::nothrow) Node(args...));
        if (!node->deserialize(ar)) {
            CFATAL_LOG("readin a node fail");
            return false;
        }
        insert(node);
    }
    return true; 
}

template<typename Key, typename Node, typename HashFunc, typename CompareFunc>   
std::shared_ptr<Node> LRUWindow<Key, Node, HashFunc, CompareFunc>::find(const Key& key) {
    auto iter = _node_index.find(key);
    if (iter == _node_index.end()) {
        return NULL;
    }
    return *(iter->second);
}

template<typename Key, typename Node, typename HashFunc, typename CompareFunc>   
const std::shared_ptr<Node> LRUWindow<Key, Node, HashFunc, CompareFunc>::find(const Key& key) const {
    auto iter = _node_index.find(key);
    if (iter == _node_index.end()) {
        return NULL;
    }
    return *(iter->second);
}

template<typename Key, typename Node, typename HashFunc, typename CompareFunc>
void LRUWindow<Key, Node, HashFunc, CompareFunc>::_gc() {
    while (!_node_list.empty()) {
        const auto& node = _node_list.back();
        if (_latest_coord - node->coord() <= _window_length && _node_index.size() < _max_list_num) {
            break;
        }
        if (_extra_gc_func) {
            _extra_gc_func(*node);
        }
        _node_index.erase(node->key());
        _node_list.pop_back(); 
    }
}

template<typename Key, typename Node, typename HashFunc, typename CompareFunc> 
void LRUWindow<Key, Node, HashFunc, CompareFunc>::_add_node(const NodePtr& node) {
    _node_list.push_front(node);
    _node_index[node->key()] = _node_list.begin();
}

template<typename Key, typename Node, typename HashFunc, typename CompareFunc>
void LRUWindow<Key, Node, HashFunc, CompareFunc>::_add_node(
        NodeIndexIter& it,
        const NodePtr& node) {
    _node_list.erase(it->second);
    _node_list.push_front(node);
    it->second = _node_list.begin();
}

template<typename Key, typename Node, 
        typename HashFunc = std::hash<Key>, 
        typename CompareFunc = std::equal_to<Key>>
class ThreadSafeLRUWindow {
public:
    ThreadSafeLRUWindow(int64_t window_length, int64_t max_list_num): 
            _window(window_length, max_list_num) {}
    ThreadSafeLRUWindow() : ThreadSafeLRUWindow(0, 0) {}

    typedef std::shared_ptr<Node> NodePtr;
    void insert(const NodePtr& node) {
        std::unique_lock<std::mutex> lck(_mutex);
        _window.insert(node);
    }
    void insert(const Node& node) {
        std::unique_lock<std::mutex> lck(_mutex);
        _window.insert(node);
    }
    NodePtr find(const Key& key) {
        std::unique_lock<std::mutex> lck(_mutex);
        return _window.find(key);
    }
    const NodePtr find(const Key& key) const{
        std::unique_lock<std::mutex> lck(_mutex);
        return _window.find(key);
    }

    int64_t latest_coord() const { return _window.latest_coord(); }

    template<typename Archive>
    bool serialize(Archive* ar) const {
        std::unique_lock<std::mutex> lck(_mutex);
        return _window.serialize(ar);
    }
    template<typename Archive, typename... Args> 
    bool deserialize(Archive* ar, Args... args) {
        std::unique_lock<std::mutex> lck(_mutex);
        return _window.deserialize(ar, args...);
    }
    bool register_extra_gc_func(std::function<void(const Node&)> func) {
        return _window.register_extra_gc_func(func);
    }
private:
    mutable std::mutex _mutex;
    LRUWindow<Key, Node, HashFunc, CompareFunc> _window;
};


} // namespace common_lib
} // namespace themis
} // namespace anti

#endif // ANTI_THEMIS_SLIDING_WINDOW_DEV_LRU_WINDOW_HPP

/* vim: set ts=4 sw=4: */
