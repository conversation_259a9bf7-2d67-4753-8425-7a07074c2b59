/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * $Id$ 
 * 
 **************************************************************************/
 
/**
 * @file sliding_window/dev/sliding_window.hpp
 * <AUTHOR>
 * @date 2015/03/31 20:07:09
 * @version 1.0.0.0
 * @brief The Implementation of SlidingWindow
 *  
 **/

#include <com_log.h>
#include "utils.h"

namespace anti {
namespace themis {
namespace common_lib { 

template<typename Item>
SlidingWindow<Item>::SlidingWindow() : 
        _step_len(0),
        _step_num(0),
        _max_step_num(0),
        _segment_len(0),
        _oldest_coord(-1),
        _segment_start_coord(-1),
        _latest_coord(-1),
        _segment(NULL),
        _window(NULL),
        _sign_start_coord(NULL) {
}

template<typename Item>
SlidingWindow<Item>::~SlidingWindow() {
    uninit();
}

template<typename Item>
bool SlidingWindow<Item>::init(
        int64_t step_len, 
        int max_step_num, 
        int64_t segment_len) {
    if (!_check_segment_arg(step_len, max_step_num, segment_len)) {
        CFATAL_LOG("param error");
        return false;
    }

    _step_len = step_len;
    _max_step_num = max_step_num;
    _segment_len = segment_len;
    _step_num = segment_len / _step_len;

    _segment = new (::std::nothrow) StepContainer();
    _window = new (::std::nothrow) WindowContainer();
    if (_segment == NULL
            || _window == NULL) {
        CFATAL_LOG("new container for sliding window fail");
        return false;
    }

    _window->clear();
    for (int step_i = 0; step_i < _max_step_num; step_i++) {
        StepContainer* step = new(::std::nothrow) StepContainer();
        if (step == NULL) {
            CFATAL_LOG("new step fail");
            return false;
        }
        _window->push_back(step);
    }

    _oldest_coord = (_step_num - _max_step_num) * _step_len;
    _segment_start_coord = 0L;
    _latest_coord = 0L;

    return true;
}

template<typename Item>
void SlidingWindow<Item>::set_log_key_first_appear() {
    _sign_start_coord = new (::std::nothrow) SignStartCoord();
    if (_sign_start_coord == NULL) {
        CFATAL_LOG("alloc SignStartCoord failed");
    }
}

template<typename Item>
bool SlidingWindow<Item>::_check_segment_arg(
        int64_t step_len, 
        int max_step_num, 
        int64_t segment_len) const {
    if (step_len <= 0
            || max_step_num <= 0
            || (step_len * (max_step_num - 1) < segment_len)
            || segment_len <= 0
            || (segment_len % step_len != 0)) {
        CWARNING_LOG("init argument error, step_len=%d max_step_num=%d "
                "segment_len=%d",
                step_len, max_step_num, segment_len);
        return false;
    }
    return true;
}

template<typename Item>
void SlidingWindow<Item>::uninit() {
    _step_len = 0;
    _step_num = 0;
    _max_step_num = 0;
    _segment_len = 0;
    _oldest_coord = 0;
    _segment_start_coord = 0;
    _latest_coord = 0;

    if (_segment != NULL) {
        _segment->clear();
        delete _segment;
        _segment = NULL;
    }

    if (_window != NULL) {
        for (WCIter iter = _window->begin();
                iter != _window->end();
                ++iter) {
            if (*iter != NULL) {
                (*iter)->clear();
                delete *iter;
                *iter = NULL;
            }
        }
        _window->clear();
        delete _window;
        _window = NULL;
    }

    if (_sign_start_coord != NULL) {
        _sign_start_coord->clear();
        delete _sign_start_coord;
        _sign_start_coord = NULL;
    }

    return ;
}

template<typename Item>
uint64_t SlidingWindow<Item>::window_view_sign_size() {
    int64_t window_view_sign_size = 0;
    if (_window == nullptr) {
        return 0;
    }
    for (const auto& node : *_window) {
        window_view_sign_size += node->size();
    }
    return window_view_sign_size;
}

template<typename Item>
uint64_t SlidingWindow<Item>::segment_view_sign_size() {
    if (_segment == nullptr) {
        return 0;
    }
    return _segment->size();
}

template<typename Item>
const Item* SlidingWindow<Item>::_query(
        const StepContainer& from, 
        const uint64_t key) const {
    SCCIter find_iter = from.find(key);
    if (find_iter == from.end()) {
        return NULL;
    } else {
        return &(find_iter->second);
    }
}

template<typename Item>
const Item* SlidingWindow<Item>::query_segment(
        const uint64_t key) const {
    if (_segment == NULL) {
        CFATAL_LOG("inner state error, _segment is NULL");
        return NULL;
    }
    return _query(*_segment, key);
}

template<typename Item>
const Item* SlidingWindow<Item>::query_segment(
        const uint64_t key,
        int64_t coord) const {
    if (!_in_segment(coord)) {
        return NULL;
    }
    return query_segment(key);
}

template<typename Item>
Item SlidingWindow<Item>::query_last_segment(
        const uint64_t key) const{

    const Item* p_item_seg = _query(*_segment, key);

    int first_step_idx = (_segment_start_coord - _oldest_coord) / _step_len;
    if (first_step_idx <= 0 
            || first_step_idx > _max_step_num - _step_num) {
        CFATAL_LOG("the first step index is invalid, first_step_idx=%d "
                "_step_num=%d _max_step_num=%d _segment_start_coord=%ld "
                "_oldest_coord=%d _step_len=%d",
                first_step_idx, _step_num, _max_step_num, _segment_start_coord,
                _oldest_coord, _step_len);
        return Item();
    }

    int last_step_in_window_idx = first_step_idx + _step_num - 1;
    int step_before_window_idx = first_step_idx - 1;
    const Item* p_item_minus = _query(*(_window->at(last_step_in_window_idx)), key);
    const Item* p_item_add = _query(*(_window->at(step_before_window_idx)), key);

    Item res;
    if (p_item_seg != NULL) {
        res += *p_item_seg;
    }
    if (p_item_minus != NULL) {
        res -= *p_item_minus;
    }
    if (p_item_add != NULL) {
        res += *p_item_add;
    }

    return res;
}

template<typename Item>
int SlidingWindow<Item>::insert(
        const uint64_t key, 
        const int64_t coord, 
        const Item& item) {
    return insert(key, coord, item, NULL);
}

template<typename Item>
int SlidingWindow<Item>::insert(
        const uint64_t key, 
        const int64_t coord, 
        const Item& item,
        const Item** pp_seg_item) {
    bool slide = (coord >= _segment_start_coord + _segment_len) ? true : false;
    if (!enter(key, coord, item, pp_seg_item)) {
        return SLIDING_WINDOW_FAIL;
    }
    return slide ? SLIDING_WINDOW_SLIDE : SLIDING_WINDOW_SUCC;
}

template<typename Item>
bool SlidingWindow<Item>::enter(
        const uint64_t key, 
        const int64_t coord, 
        const Item& item,
        const Item** pp_seg_item) {
    if (coord < _oldest_coord) {
        CWARNING_LOG("too old item, coord=%ld oldest=%ld latest=%ld",
                coord, _oldest_coord, _latest_coord);
        return false;
    }
    if (coord > _latest_coord) {
        _latest_coord = coord;
    }

    // segment range is [start, end)
    if (coord >= _segment_start_coord + _segment_len) {
        _slide_to_coord(coord);
    }

    if (_window == NULL) {
        CFATAL_LOG("inner state error, _window is NULL");
        return false;
    }
    StepContainer* step = _find_step(coord);
    if (step == NULL) {
        CFATAL_LOG("find a NULL step, coord=%ld", coord);
        return false;
    }
    if (!_push_item(step, key, item, NULL)) {
        CWARNING_LOG("push a item into step error, key=%llu", key);
        return false;
    }
    if (_in_segment(coord) && !_push_item(_segment, key, item, pp_seg_item)) {
        CWARNING_LOG("push a item into segment error, key=%llu", key);
        return false;
    }
    // log first appear coordinate
    if (_sign_start_coord != NULL) {
        //int64_t round_coord = _step_start_coord(coord);
        int64_t round_coord = _step_start_coord(_latest_coord);
        auto ptr = _sign_start_coord->find(key);
        if (ptr == _sign_start_coord->end()) {
            ContinueCoordinate c(round_coord);
            //_sign_start_coord[key] = c;
            _sign_start_coord->emplace(key, c);
        } else {
            ptr->second.push(round_coord, _step_len);
        }
    } 
    return true;
}

template<typename Item>
int64_t SlidingWindow<Item>::query_start_coord(const uint64_t key) const {
    if (_sign_start_coord == NULL) { return segment_start_coord(); }

    auto ptr = _sign_start_coord->find(key);
    if (ptr == _sign_start_coord->end()) { return segment_start_coord(); }

    int64_t key_start = ptr->second.first_coord();
    if (key_start < 0) { return segment_start_coord(); }
    else { return key_start; }
}

template<typename Item>
bool SlidingWindow<Item>::_in_segment(const int64_t coord) const {
    return coord >= _segment_start_coord;
}

template<typename Item>
bool SlidingWindow<Item>::_push_item(
        StepContainer* step,
        const uint64_t key,
        const Item& item,
        const Item** pp_item) {
    if (step == NULL) {
        CFATAL_LOG("param error");
        return false;
    }

    SCIter fid = step->find(key);
    Item* inner = NULL;
    if (fid == step->end()) {
        ::std::pair<SCIter, bool> i_res = 
            step->emplace(key, item);
        if (!i_res.second){
            CWARNING_LOG("push item error, key=%llu", key);
            return false;
        }
        inner = &(i_res.first->second);
    } else {
        fid->second += item;
        inner = &(fid->second);
    }

    if (pp_item != NULL && inner != NULL) {
        *pp_item = inner;
    }
    return true;
}

template<typename Item>
typename SlidingWindow<Item>::StepContainer* 
SlidingWindow<Item>::_find_step(int64_t coord) const {
    if (_window == NULL) {
        CFATAL_LOG("inner state error, _window is NULL");
        return NULL;
    }
    int pos = (coord - _oldest_coord) / _step_len;
    if (coord < _oldest_coord) { pos -= 1; }
    if (pos < 0
            || pos >= static_cast<int>(_window->size())) {
        CFATAL_LOG("the pos of the coord is invalid, pos=%d coord=%ld",
                pos, coord);
        return NULL;
    }
    return _window->at(pos);
}

template<typename Item>
int64_t SlidingWindow<Item>::_step_start_coord(int64_t coord) const {
    int offset = (coord - _segment_start_coord) / _step_len;
    return _segment_start_coord + offset * _step_len;
}

template<typename Item>
bool SlidingWindow<Item>::enter(
        const uint64_t key, 
        const int64_t coord, 
        const Item& item) {
    return enter(key, coord, item, NULL);
}

template<typename Item>
int64_t SlidingWindow<Item>::_slide_to_coord(int64_t coord) {
    if (coord < _segment_start_coord + _segment_len) { return 0; }
    // segment range is [start, end)
    int slide_step = 1 + (coord - _segment_start_coord - _segment_len) / _step_len;
    if (slide_step <= 0) {
        return 0;
    }

    int64_t slide_coord = _step_len * slide_step;
    if (slide_step < _max_step_num) {
        for (int step_i = 0; step_i < slide_step; step_i++) {
            _slide_one_step();
        }
    } else {
        _clear();
        _latest_coord = coord;
        int64_t remainder = coord % _step_len;
        _oldest_coord = coord - remainder - _step_len * (_max_step_num - 1);
        _segment_start_coord = sliding_segment_start_coord(coord, _step_len, _step_num);
    }

    return slide_coord;
}

template<typename Item>
void SlidingWindow<Item>::_clear() {
    if (_segment != NULL) {
        _segment->clear();
    }
    if (_window != NULL) {
        for (WCIter iter = _window->begin();
                iter != _window->end();
                iter++) {
            (*iter)->clear();
        }
    }
    return ;
}

template<typename Item>
void SlidingWindow<Item>::_slide_one_step() {
    _delete_first_step_in_segment();
    _move_and_clear_oldest_step();
}

template<typename Item>
void SlidingWindow<Item>::_move_and_clear_oldest_step() {
    if (_window == NULL) {
        CFATAL_LOG("inner state is error, _window is NULL");
        return ;
    }
    StepContainer* oldest_step = _window->front();
    _window->pop_front();
    if (oldest_step == NULL) {
        CFATAL_LOG("the oldest step in window is NULL");
        return ;
    }
    oldest_step->clear();
    _window->push_back(oldest_step);
    _oldest_coord += _step_len;
    return ;
}

template<typename Item>
void SlidingWindow<Item>::_delete_first_step_in_segment() {
    StepContainer* first_step = _first_step_in_segment();
    if (first_step == NULL) {
        CFATAL_LOG("first step in segment is NULL");
        return ;
    }

    if (_sign_start_coord != NULL) {
        // update sign first appear log-map!
        for (auto it = first_step->begin(); it != first_step->end(); ++it) {
            auto ptr = _sign_start_coord->find(it->first);
            if (ptr != _sign_start_coord->end()) {
                ptr->second.erase(_segment_start_coord, _step_len);
                if (ptr->second.first_coord() == -1) {
                    _sign_start_coord->erase(ptr);
                }
            }
        }
    }

    sub(_segment, first_step);
    _segment_start_coord += _step_len;
}

template<typename Item>
typename SlidingWindow<Item>::StepContainer* 
SlidingWindow<Item>::_first_step_in_segment() const {
    if (_window == NULL) {
        CFATAL_LOG("inner state error, _window is NULL");
        return NULL;
    }
    int pos = (_segment_start_coord - _oldest_coord) / _step_len;
    // @note: at least over store one step
    if (pos <= 0 || pos >= static_cast<int>(_window->size())) {
        CFATAL_LOG("first step pos is invalid, pos=%d", pos);
        return NULL;
    }
    return _window->at(pos);
}

template<typename Item>
template <typename Archive>
bool SlidingWindow<Item>::serialize(Archive* ar) const {
    if (!_check_segment_arg(_step_len, _max_step_num, _segment_len)) {
        CFATAL_LOG("inner segment conf error");
        return false;
    }
    if (_window == NULL) {
        CFATAL_LOG("inner window is null");
        return false;
    }
    for (size_t step_idx = 0; 
            step_idx < _window->size(); 
            step_idx++) {
        if (_window->at(step_idx) == NULL) {
            CWARNING_LOG("step in window is null, idx=%u size=%u max_step_num=%d",
                    step_idx, _window->size(), _max_step_num);
            return false;
        }
    }

    int segment_start_idx = (_segment_start_coord - _oldest_coord) / _step_len;
    if (segment_start_idx < 1 
            || segment_start_idx >= _max_step_num
            || segment_start_idx >= static_cast<int>(_window->size())) {
        CFATAL_LOG("error coord inner status, oldest=%lld start=%lld "
                "step_len=%lld max_step_num=%d segment_len=%lld",
                _oldest_coord, _segment_start_coord, _step_len,
                _max_step_num, _segment_len);
        return false;
    }

    SegmentConf sc = {
            _MAGIC_NUMBER,
            _step_len,
            _segment_len,
            _step_num
    };
    if (!t_write<SegmentConf, Archive>(sc, ar)) {
        CFATAL_LOG("dump segment conf error");
        return false;
    }

    int64_t step_start_coord = _segment_start_coord - _step_len;
    for (int step_idx = segment_start_idx - 1; 
            step_idx < static_cast<int>(_window->size()); 
            step_idx++) {
        const StepContainer* sc = _window->at(step_idx);

        size_t node_num = sc->size();
        if (!t_write<size_t, Archive>(node_num, ar)) {
            CFATAL_LOG("dump step size error, step_idx=%d size=%u",
                    step_idx, node_num);
            return false;
        }

        if (!t_write<int64_t, Archive>(step_start_coord, ar)) {
            CFATAL_LOG("dump step start coord error, step_idx=%d coord=%lld",
                    step_idx, step_start_coord);
            return false;
        }

        const StepContainer* step = _window->at(step_idx);
        if (step == NULL) {
            CFATAL_LOG("find a NULL step, step_idx=%d", step_idx);
            return false;
        }
        if (!_serialize_step(*step, ar)) {
            CFATAL_LOG("serialize a step error, step_idx=%d", step_idx);
            return false;
        }

        step_start_coord += _step_len;
    }

    return true;
}

template<typename Item>
template <typename Archive>
bool SlidingWindow<Item>::_serialize_step(
        const StepContainer& step, 
        Archive* ar) const {
    if (ar == NULL) {
        CFATAL_LOG("param error");
        return false;
    }
    for(SCCIter iter = step.begin();
            iter != step.end();
            ++iter) {
        if (!t_write<uint64_t, Archive>(iter->first, ar)) {
            CFATAL_LOG("serialize key error");
            return false;
        }
        if (!(iter->second.serialize(ar))) {
            CFATAL_LOG("serialize item error");
            return false;
        }
    }

    return true;
}

template<typename Item>
template <typename Archive>
bool SlidingWindow<Item>::deserialize(Archive* ar) {
    if (ar == NULL) {
        CFATAL_LOG("param error");
        return false;
    }
    if (_segment == NULL
            || _window == NULL) {
        CFATAL_LOG("inner state error, _segment=%p _window=%p",
                _segment, _window);
        return false;
    }
    if (_max_step_num != static_cast<int>(_window->size())) {
        CFATAL_LOG("window size isn't match max step num, "
                "window.size=%u max_step_num=%d",
                _window->size(), _max_step_num);
        return false;
    }

    if (!_check_segment_arg(_step_len, _max_step_num, _segment_len)) {
        CFATAL_LOG("segment conf error, step_len=%ld max_step_num=%d _segment_len=%ld",
                _step_len, _max_step_num, _segment_len);
        return false;
    }
    _clear();

    SegmentConf sc;
    if (!t_read<SegmentConf, Archive>(&sc, ar)) {
        CFATAL_LOG("load segment conf error");
        return false;
    }
    if (sc.magic != _MAGIC_NUMBER
            || sc.step_len != _step_len
            || sc.segment_len != _segment_len
            || sc.step_num != _step_num) {
        CFATAL_LOG("load segment conf error, "
                "read[%0X, %lld, %lld, %d]"
                "expected[%0X, %lld, %lld, %d]",
                sc.magic, sc.step_len, sc.segment_len, sc.step_num, 
                _MAGIC_NUMBER, _step_len, _segment_len, _step_num);
        return false;
    }

    for (int step_idx = 0; step_idx < _step_num + 1; ++step_idx) {
        size_t node_num = 0U;
        int64_t coord = 0L;
        if (!t_read<size_t, Archive>(&node_num, ar)) {
            CFATAL_LOG("read node num error, step_idx=%d", step_idx);
            return false;
        }
        if (!t_read<int64_t, Archive>(&coord, ar)) {
            CFATAL_LOG("read step start coord error, step_idx=%d", step_idx);
            return false;
        }

        for (size_t node_idx = 0U; node_idx < node_num; ++node_idx) {
            uint64_t key = 0U;
            if (!t_read<uint64_t, Archive>(&key, ar)) {
                CFATAL_LOG("read key error, step_idx=%d node_idx=%u", 
                        step_idx, node_idx);
                return false;
            }

            Item item;
            if (!(item.template deserialize<Archive>(ar))) {
                CFATAL_LOG("deserialize a item error, step_idx=%d node_idx=%d", 
                        step_idx, node_idx);
                return false;
            }
            this->enter(key, coord, item);
        }
    }

    return true;
}

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4 sts=4 tw=100 */
