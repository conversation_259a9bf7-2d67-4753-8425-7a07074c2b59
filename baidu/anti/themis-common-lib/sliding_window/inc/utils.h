/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * $Id$ 
 * 
 **************************************************************************/
 
/**
 * @file sliding_window/dev/utils.h
 * <AUTHOR>
 * @date 2015/04/01 16:06:11
 * @version $Revision$ 
 * @brief 
 *  
 **/

#ifndef ANTI_THEMIS_SLIDING_WINDOW_DEV_UTILS_H
#define ANTI_THEMIS_SLIDING_WINDOW_DEV_UTILS_H

#include <unordered_map>
#include "pool_hash_map.hpp"

namespace anti {
namespace themis {
namespace common_lib {

template<typename Key, typename Item>
void map_union(std::unordered_map<Key, Item>* lhs, 
        std::unordered_map<Key, Item>* rhs); 

template<typename Key, typename Item>
void sub(std::unordered_map<Key, Item>* minuend, 
        std::unordered_map<Key, Item>* subtrahend);

template <typename Key, typename Item, typename MemPoolType>
void pool_sub(PoolHashMap<Key, Item, MemPoolType>* minuend, 
              PoolHashMap<Key, Item, MemPoolType>* subtrahend);

template<typename Node, typename Archive>
bool t_write(const Node& node, Archive* ar);

template<typename Node, typename Archive>
bool t_read(Node* node, Archive* ar);

#define DUMP_VAR(var, ar, type)    \
do {    \
    if (!anti::themis::common_lib::t_write<type, Archive>(var, ar)) {    \
    CFATAL_LOG("dump[%s] failed!", #var);    \
    return false;    \
    }    \
} while (0)

#define LOAD_VAR(var, ar, type)    \
do {    \
    if (!anti::themis::common_lib::t_read<type, Archive>(var, ar)) {    \
    CFATAL_LOG("read[%s] failed!", #var);    \
    return false;    \
    }    \
} while (0)

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

#include "utils.hpp"

#endif  // ANTI_THEMIS_SLIDING_WINDOW_DEV_UTILS_H

/* vim: set ts=4 sw=4 sts=4 tw=100 */
