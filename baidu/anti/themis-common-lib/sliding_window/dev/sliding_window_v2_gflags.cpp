// Copyright (c) 2025 Baidu.com, Inc. All Rights Reserved
// @Author: l<PERSON><PERSON><PERSON><PERSON>(<EMAIL>)

#include <gflags/gflags.h>

// PoolHashMap的负载因子阈值，默认2.0
DEFINE_double(pool_hashmap_load_factor, 2.0, "Load factor threshold for PoolHashMap rehash");

// PoolHashMap扩容时的增长倍率，默认1.5
DEFINE_double(pool_hashmap_growth_factor, 1.5, "Growth factor for PoolHashMap rehash");

// 内存池block num
DEFINE_int32(sliding_window_v2_mempool_block_item_num, 20000, "Memory pool initialization parameters, max_block_item_num");