/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * $Id$ 
 * 
 **************************************************************************/
 
/**
 * @file sliding_window/dev/archive.cpp
 * <AUTHOR>
 * @date 2015/04/09 11:22:40
 * @version $Revision$ 
 * @brief 
 *  
 **/

#include <stdio.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <errno.h>
#include <assert.h>

#include <com_log.h>

#include "archive.h"

namespace anti {
namespace themis {
namespace common_lib {

int my_open(const char* file, int flags, mode_t mode) {
    return ::open(file, flags, mode);
}

int my_close(int fd) {
    return ::close(fd);
}

bool FileArchive::_open(const char* filepath, int flags, mode_t mode) {
    if (filepath == NULL) {
        CFATAL_LOG("param error");
        return false;
    }
    close();

    _filepath = filepath;

    _fd = my_open(filepath, flags, mode);
    if (_fd == -1) {
        char err[128] = {'\0'};
        strerror_r(errno, err, sizeof(err));
        CFATAL_LOG("open file error, file=(%s) flags=%0x, err=%d(%s)", 
                filepath, flags, errno, err);
        return false;
    }

    return true;
}

bool FileArchive::open_w(const char* filepath) {
    _is_write = true;
    return _open(filepath, O_WRONLY | O_CREAT, 0644);
}

bool FileArchive::open_r(const char* filepath) {
    _is_write = false;
    return _open(filepath, O_RDONLY, 0);
}

bool FileArchive::close() {
    if (_fd >= 0) {
        flush();

        if (-1 == my_close(_fd)) {
            CFATAL_LOG("close fd error, fd=%d", _fd);
            return false;
        }
        _fd = -1;
    }
    return true;
}

void FileArchive::flush() {
    if (_fd >= 0) {
        fsync(_fd);
    }
}

int FileArchive::read(void* dat, size_t count) {
    if (_fd == -1) {
        CFATAL_LOG("inner _fd is invalid, _fd=%d", _fd);
        return 0;
    }
    return ::read(_fd, dat, count);
}

int FileArchive::write(const void* dat, size_t count) {
    if (_fd == -1) {
        CFATAL_LOG("inner _fd is invalid, _fd=%d", _fd);
        return 0;
    }
    return ::write(_fd, dat, count);
}

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4 sts=4 tw=100 */
