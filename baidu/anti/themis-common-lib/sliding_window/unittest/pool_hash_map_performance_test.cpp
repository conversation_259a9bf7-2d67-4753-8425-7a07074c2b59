// Copyright (c) 2025 Baidu.com, Inc. All Rights Reserved
// @Author: l<PERSON><PERSON><PERSON><PERSON>(<EMAIL>)

#include "pool_hash_map_test_base.h"
#include <numeric>

/**
 * @brief PoolHashMap性能对比测试类
 * PoolHashMap与std::unordered_map的性能和内存效率对比
 */
class PoolHashMapPerformanceTest : public PoolHashMapTestBase {
protected:
    void SetUp() override {
        PoolHashMapTestBase::SetUp();
    }

    void TearDown() override {
        PoolHashMapTestBase::TearDown();
    }
};

/**
 * @brief 详细性能对比测试 (100万数据 - 插入+查询+删除)
 * 分别测试插入、查询、删除操作的性能和总耗时
 */
TEST_F(PoolHashMapPerformanceTest, test_detailed_performance_comparison_1m) {
    std::cout << "\n=== 详细性能对比测试 (100万数据 - 插入+查询+删除) ===" << std::endl;

    auto test_data = generate_test_data(1000000);
    std::cout << "数据规模: " << test_data.size() << " 个KV对" << std::endl;

    // 详细性能测试PoolHashMap
    auto pool_result = benchmark_detailed_operations(_pool_map, test_data);

    std::cout << "PoolHashMap性能报告:" << std::endl;
    std::cout << "  插入: " << pool_result.insert_time_ms << " ms, "
              << static_cast<int>(pool_result.insert_ops_per_sec) << " ops/s, "
              << "平均 " << std::fixed << std::setprecision(2)
              << (static_cast<double>(pool_result.insert_time_ms) * 1000.0 /
                  pool_result.insert_success_count)
              << " us/op" << std::endl;
    std::cout << "  查询: " << pool_result.find_time_ms << " ms, "
              << static_cast<int>(pool_result.find_ops_per_sec) << " ops/s, "
              << "平均 " << std::fixed << std::setprecision(2)
              << (static_cast<double>(pool_result.find_time_ms) * 1000.0 /
                  pool_result.find_success_count)
              << " us/op" << std::endl;
    std::cout << "  删除: " << pool_result.remove_time_ms << " ms, "
              << static_cast<int>(pool_result.remove_ops_per_sec) << " ops/s, "
              << "平均 " << std::fixed << std::setprecision(2)
              << (static_cast<double>(pool_result.remove_time_ms) * 1000.0 /
                  pool_result.remove_success_count)
              << " us/op" << std::endl;

    // 详细性能测试std::unordered_map
    auto std_result = benchmark_detailed_operations(_std_map, test_data);

    std::cout << "std::unordered_map性能报告:" << std::endl;
    std::cout << "  插入: " << std_result.insert_time_ms << " ms, "
              << static_cast<int>(std_result.insert_ops_per_sec) << " ops/s, "
              << "平均 " << std::fixed << std::setprecision(2)
              << (static_cast<double>(std_result.insert_time_ms) * 1000.0 /
                  std_result.insert_success_count)
              << " us/op" << std::endl;
    std::cout << "  查询: " << std_result.find_time_ms << " ms, "
              << static_cast<int>(std_result.find_ops_per_sec) << " ops/s, "
              << "平均 " << std::fixed << std::setprecision(2)
              << (static_cast<double>(std_result.find_time_ms) * 1000.0 /
                  std_result.find_success_count)
              << " us/op" << std::endl;
    std::cout << "  删除: " << std_result.remove_time_ms << " ms, "
              << static_cast<int>(std_result.remove_ops_per_sec) << " ops/s, "
              << "平均 " << std::fixed << std::setprecision(2)
              << (static_cast<double>(std_result.remove_time_ms) * 1000.0 /
                  std_result.remove_success_count)
              << " us/op" << std::endl;

    // 插入性能对比
    if (std_result.insert_time_ms > 0) {
        if (pool_result.insert_time_ms < std_result.insert_time_ms) {
            double insert_speedup =
                    static_cast<double>(std_result.insert_time_ms) / pool_result.insert_time_ms;
            std::cout << "✓ 插入性能提升: " << std::fixed << std::setprecision(2) << insert_speedup
                      << "x (" << pool_result.insert_time_ms << " vs " << std_result.insert_time_ms
                      << " ms)" << std::endl;
        } else {
            double insert_slowdown =
                    static_cast<double>(pool_result.insert_time_ms) / std_result.insert_time_ms;
            std::cout << "✗ 插入性能下降: " << std::fixed << std::setprecision(2) << insert_slowdown
                      << "x (" << pool_result.insert_time_ms << " vs " << std_result.insert_time_ms
                      << " ms)" << std::endl;
        }
    }

    // 查询性能对比
    if (std_result.find_time_ms > 0) {
        if (pool_result.find_time_ms < std_result.find_time_ms) {
            double find_speedup =
                    static_cast<double>(std_result.find_time_ms) / pool_result.find_time_ms;
            std::cout << "✓ 查询性能提升: " << std::fixed << std::setprecision(2) << find_speedup
                      << "x (" << pool_result.find_time_ms << " vs " << std_result.find_time_ms
                      << " ms)" << std::endl;
        } else {
            double find_slowdown =
                    static_cast<double>(pool_result.find_time_ms) / std_result.find_time_ms;
            std::cout << "✗ 查询性能下降: " << std::fixed << std::setprecision(2) << find_slowdown
                      << "x (" << pool_result.find_time_ms << " vs " << std_result.find_time_ms
                      << " ms)" << std::endl;
        }
    }

    // 删除性能对比
    if (std_result.remove_time_ms > 0) {
        if (pool_result.remove_time_ms < std_result.remove_time_ms) {
            double remove_speedup =
                    static_cast<double>(std_result.remove_time_ms) / pool_result.remove_time_ms;
            std::cout << "✓ 删除性能提升: " << std::fixed << std::setprecision(2) << remove_speedup
                      << "x (" << pool_result.remove_time_ms << " vs " << std_result.remove_time_ms
                      << " ms)" << std::endl;
        } else {
            double remove_slowdown =
                    static_cast<double>(pool_result.remove_time_ms) / std_result.remove_time_ms;
            std::cout << "✗ 删除性能下降: " << std::fixed << std::setprecision(2) << remove_slowdown
                      << "x (" << pool_result.remove_time_ms << " vs " << std_result.remove_time_ms
                      << " ms)" << std::endl;
        }
    }

    // 总体性能对比
    double pool_total_time =
            pool_result.insert_time_ms + pool_result.find_time_ms + pool_result.remove_time_ms;
    double std_total_time =
            std_result.insert_time_ms + std_result.find_time_ms + std_result.remove_time_ms;

    if (std_total_time > 0) {
        if (pool_total_time < std_total_time) {
            double overall_speedup = std_total_time / pool_total_time;
            std::cout << "✓ 总体性能提升: " << std::fixed << std::setprecision(2) << overall_speedup
                      << "x (" << std::fixed << std::setprecision(0) << pool_total_time << " vs "
                      << std_total_time << " ms)" << std::endl;
        } else {
            double overall_slowdown = pool_total_time / std_total_time;
            std::cout << "✗ 总体性能下降: " << std::fixed << std::setprecision(2)
                      << overall_slowdown << "x (" << std::fixed << std::setprecision(0)
                      << pool_total_time << " vs " << std_total_time << " ms)" << std::endl;
        }
    }

    // 基本验证
    EXPECT_EQ(pool_result.insert_success_count, test_data.size());
    EXPECT_EQ(pool_result.find_success_count, test_data.size());
    EXPECT_EQ(pool_result.remove_success_count, test_data.size());
    EXPECT_EQ(std_result.insert_success_count, test_data.size());
    EXPECT_EQ(std_result.find_success_count, test_data.size());
    EXPECT_EQ(std_result.remove_success_count, test_data.size());
}

/**
 * @brief 内存使用对比测试 (1000万数据)
 * 对比PoolHashMap与std::unordered_map的内存使用效率
 */
TEST_F(PoolHashMapPerformanceTest, test_memory_usage_comparison) {
    std::cout << "\n=== 内存使用对比测试 (1000万数据) ===" << std::endl;

    auto test_data = generate_test_data(10000000);
    std::cout << "数据规模: " << test_data.size() << " 个KV对" << std::endl;

    // 1. PoolHashMap插入数据前后RSS差值
    size_t pool_rss_before = get_rss_memory();

    for (const auto& item : test_data) {
        _pool_map->emplace(item.first, item.second);
    }

    size_t pool_rss_after = get_rss_memory();
    size_t pool_rss_diff_kb =
            (pool_rss_after > pool_rss_before) ? (pool_rss_after - pool_rss_before) : 0;

    std::cout << "PoolHashMap:" << std::endl;
    std::cout << "  元素数量: " << _pool_map->size() << ", 桶数量: " << _pool_map->bucket_count()
              << std::endl;
    std::cout << "  RSS增长: " << (pool_rss_diff_kb / 1024) << " MB" << std::endl;
    std::cout << "  内存池+HashMap对象: "
              << ((get_mempool_effective_memory(_mem_pool) + get_pool_hashmap_overhead_mem(_pool_map)) /
                  1024 / 1024)
              << " MB" << std::endl;

    // std::unordered_map内存使用
    size_t std_rss_before = get_rss_memory();

    for (const auto& item : test_data) {
        _std_map->emplace(item.first, item.second);
    }

    size_t std_rss_after = get_rss_memory();
    size_t std_rss_diff_kb =
            (std_rss_after > std_rss_before) ? (std_rss_after - std_rss_before) : 0;

    std::cout << "std::unordered_map:" << std::endl;
    std::cout << "  元素数量: " << _std_map->size() << ", 桶数量: " << _std_map->bucket_count()
              << std::endl;
    std::cout << "  RSS增长: " << (std_rss_diff_kb / 1024) << " MB" << std::endl;

    // 对比数据
    size_t theoretical_bytes = test_data.size() * (sizeof(TestKey) + sizeof(TestValue));
    std::cout << "对比数据:" << std::endl;
    std::cout << "  原始数据大小: " << (theoretical_bytes / 1024 / 1024) << " MB" << std::endl;

    // 内存效率提升对比分析
    std::cout << "\n--- 内存效率提升对比 ---" << std::endl;

    if (pool_rss_diff_kb > 0 && std_rss_diff_kb > 0) {
        // 计算额外内存消耗
        size_t pool_total_bytes = pool_rss_diff_kb * 1024;
        size_t std_total_bytes = std_rss_diff_kb * 1024;
        size_t pool_overhead_bytes = pool_total_bytes - theoretical_bytes;
        size_t std_overhead_bytes = std_total_bytes - theoretical_bytes;

        // 额外内存消耗对比
        if (pool_overhead_bytes < std_overhead_bytes) {
            double overhead_saving = static_cast<double>(std_overhead_bytes - pool_overhead_bytes) /
                    std_overhead_bytes * 100.0;
            std::cout << "✓ 额外内存消耗减少: " << std::fixed << std::setprecision(1)
                      << overhead_saving << "% (" << pool_overhead_bytes / 1024 / 1024 << " vs "
                      << std_overhead_bytes / 1024 / 1024 << " MB)" << std::endl;
        } else {
            double overhead_increase =
                    static_cast<double>(pool_overhead_bytes - std_overhead_bytes) /
                    std_overhead_bytes * 100.0;
            std::cout << "✗ 额外内存消耗增加: +" << std::fixed << std::setprecision(1)
                      << overhead_increase << "% (" << pool_overhead_bytes / 1024 / 1024 << " vs "
                      << std_overhead_bytes / 1024 / 1024 << " MB)" << std::endl;
        }

        // 单条目额外内存消耗对比
        double pool_overhead_per_item = static_cast<double>(pool_overhead_bytes) / test_data.size();
        double std_overhead_per_item = static_cast<double>(std_overhead_bytes) / test_data.size();

        if (pool_overhead_per_item < std_overhead_per_item) {
            double per_item_saving =
                    static_cast<double>(std_overhead_per_item - pool_overhead_per_item) /
                    std_overhead_per_item * 100.0;
            std::cout << "✓ 单条目额外开销减少: " << std::fixed << std::setprecision(1)
                      << per_item_saving << "% (" << std::fixed << std::setprecision(1)
                      << pool_overhead_per_item << " vs " << std_overhead_per_item << " bytes/item)"
                      << std::endl;
        } else {
            double per_item_increase =
                    static_cast<double>(pool_overhead_per_item - std_overhead_per_item) /
                    std_overhead_per_item * 100.0;
            std::cout << "✗ 单条目额外开销增加: +" << std::fixed << std::setprecision(1)
                      << per_item_increase << "% (" << std::fixed << std::setprecision(1)
                      << pool_overhead_per_item << " vs " << std_overhead_per_item << " bytes/item)"
                      << std::endl;
        }

        // 内存利用率对比（有效数据占总内存的比例）
        double pool_utilization = static_cast<double>(theoretical_bytes) / pool_total_bytes * 100.0;
        double std_utilization = static_cast<double>(theoretical_bytes) / std_total_bytes * 100.0;

        if (pool_utilization > std_utilization) {
            double utilization_improvement = pool_utilization - std_utilization;
            std::cout << "✓ 内存利用率提升: +" << std::fixed << std::setprecision(1)
                      << utilization_improvement << "% (" << pool_utilization << "% vs "
                      << std_utilization << "%)" << std::endl;
        } else {
            double utilization_decline = std_utilization - pool_utilization;
            std::cout << "✗ 内存利用率下降: -" << std::fixed << std::setprecision(1)
                      << utilization_decline << "% (" << pool_utilization << "% vs "
                      << std_utilization << "%)" << std::endl;
        }
    }

    // 基本验证
    EXPECT_EQ(_pool_map->size(), _std_map->size());
}

/**
 * @brief rehash性能测试 (100万数据)
 */
TEST_F(PoolHashMapPerformanceTest, test_rehash_performance) {
    const size_t data_count = 1000000;

    std::vector<double> rehash_times_ms;
    size_t rehash_count = 0;
    size_t last_bucket_count = _pool_map->bucket_count();
    auto total_start = std::chrono::high_resolution_clock::now();

    for (size_t i = 1; i <= data_count; ++i) {
        auto insert_start = std::chrono::high_resolution_clock::now();
        auto result = _pool_map->emplace(i, static_cast<TestValue>(i) * 100);
        auto insert_end = std::chrono::high_resolution_clock::now();

        ASSERT_TRUE(result.second);

        size_t current_bucket_count = _pool_map->bucket_count();
        if (current_bucket_count != last_bucket_count) {
            rehash_count++;
            double rehash_time_us =
                    std::chrono::duration<double, std::micro>(insert_end - insert_start).count();
            rehash_times_ms.push_back(rehash_time_us);

            std::cout << "rehash #" << rehash_count << ": 元素=" << i
                      << ", 桶数=" << last_bucket_count << "->" << current_bucket_count
                      << ", 耗时=" << std::fixed << std::setprecision(0) << rehash_time_us << "us"
                      << std::endl;

            last_bucket_count = current_bucket_count;
        }
    }

    EXPECT_EQ(data_count, _pool_map->size());
}