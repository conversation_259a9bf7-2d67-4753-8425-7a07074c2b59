/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * $Id$ 
 * 
 **************************************************************************/
 
/**
 * @file click_segment_test.cpp
 * <AUTHOR>
 * @date 2015/06/30 11:24:34
 * @version $Revision$ 
 * @brief 
 *  
 **/

#include <gtest/gtest.h>
#include "click_node.hpp"
#include "click_segment.hpp"
#include "archive.h"

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}

namespace anti {
namespace themis {
namespace common_lib {

class ClickSegmentTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
    }
    virtual void TearDown() {
    }
};

TEST_F(ClickSegmentTestSuite, SegLimitNodeCase_ctor) {
    SegLimitNode sln;
    EXPECT_EQ(sln.sign, 0U);
    EXPECT_EQ(sln.coord, 0L);
    EXPECT_EQ(sln.cumulant, 0L);
    EXPECT_EQ(sln.last_cumulant, 0L);

    SegLimitNode s(123LU, 119L);
    EXPECT_EQ(s.sign, 123LU);
    EXPECT_EQ(s.coord, 119L);
    EXPECT_EQ(s.cumulant, 1L);
    EXPECT_EQ(s.last_cumulant, 0L);
}

TEST_F(ClickSegmentTestSuite, SegLimitNodeCase_add) {
    SegLimitNode sln1;
    SegLimitNode sln2;
    sln1.coord = 1;
    sln1.cumulant = 100;
    
    sln2.coord = 2;
    sln2.cumulant = 1;
    
    sln1.add(sln2, 1000);
    EXPECT_EQ(sln1.cumulant, 101);

    sln2.sign = 2;
    sln1.add(sln2, 1000);
    EXPECT_EQ(sln1.cumulant, 101);

    sln1.sign = sln2.sign;
    sln2.coord = 2000;
    sln2.cumulant = 3;
    sln1.add(sln2, 1000);
    EXPECT_EQ(sln1.cumulant, 3);
}

TEST_F(ClickSegmentTestSuite, RateLimitNodeCase_ctor) {
    RateLimitNode rln;
    EXPECT_EQ(rln.sign, 0U);
    EXPECT_EQ(rln.coord, 0L);
    EXPECT_EQ(rln.fit_cumulant, 0L);
    EXPECT_EQ(rln.ref_cumulant, 0L);
    
    RateLimitNode r(123LU, 119L, 110L, 120L);
    EXPECT_EQ(r.sign, 123U);
    EXPECT_EQ(r.coord, 119L);
    EXPECT_EQ(r.fit_cumulant, 110L);
    EXPECT_EQ(r.ref_cumulant, 120L);
}

TEST_F(ClickSegmentTestSuite, RateLimitNodeCase_add) {
    RateLimitNode rln1;
    RateLimitNode rln2;
    rln2.fit_cumulant = 1;
    rln2.ref_cumulant = 11;
    rln1.add(rln2, 1000);
    EXPECT_EQ(rln1.fit_cumulant, 1);
    EXPECT_EQ(rln1.ref_cumulant, 11);

    rln2.sign = 2;
    rln1.add(rln2, 1000);
    EXPECT_EQ(rln1.fit_cumulant, 1);
    EXPECT_EQ(rln1.ref_cumulant, 11);

    rln1.sign = rln2.sign;
    rln2.fit_cumulant = 1;
    rln2.ref_cumulant = 3;
    rln1.add(rln2, 1000);
    EXPECT_EQ(rln1.fit_cumulant, 2);
    EXPECT_EQ(rln1.ref_cumulant, 14);

    rln2.coord = 2000;
    rln1.add(rln2, 1000);
    EXPECT_EQ(rln1.fit_cumulant, 1);
    EXPECT_EQ(rln1.ref_cumulant, 3);
}

TEST_F(ClickSegmentTestSuite, DistributionLimitNodeCase_ctor) {
    DistributionLimitNode di1;
    EXPECT_EQ(di1.sign, 0LU);
    EXPECT_EQ(di1.coord, 0L);
    for (uint32_t i = 0; i < di1.MAX_BUCKET_NUM; ++i) {
        EXPECT_EQ(di1.buckets[i], 0L);
    }

    DistributionLimitNode di2(123LU, 119L, 4U, 120L);
    EXPECT_EQ(di2.sign, 123LU);
    EXPECT_EQ(di2.coord, 119L);
    for (uint32_t i = 0; i < di2.MAX_BUCKET_NUM; ++i) {
        if (i == 4U) {
            EXPECT_EQ(di2.buckets[i], 120L);
            continue;
        }
        EXPECT_EQ(di2.buckets[i], 0L);
    }

    // invalid idx
    DistributionLimitNode di3(123LU, 119L, 100U, 120L);
    EXPECT_EQ(di3.sign, 123LU);
    EXPECT_EQ(di3.coord, 119L);
    for (uint32_t i = 0; i < di3.MAX_BUCKET_NUM; ++i) {
        EXPECT_EQ(di3.buckets[i], 0L);
    }
}

TEST_F(ClickSegmentTestSuite, DistributionLimitNodeCase_add) {
    DistributionLimitNode di1(123LU, 1L, 0U, 100L);
    DistributionLimitNode di2(123LU, 1000L, 2U, 10L);

    di1.add(di2, 1000L);
    EXPECT_EQ(di1.buckets[0], 100L);
    EXPECT_EQ(di1.buckets[2], 10L);
    EXPECT_EQ(di1.coord, 1L);

    // different signs
    di1.sign = 123456LU;
    di1.add(di2, 2000L);
    EXPECT_EQ(di1.buckets[0], 100L);
    EXPECT_EQ(di1.buckets[2], 10L);
    EXPECT_EQ(di1.coord, 1L);
    di1.sign = 123LU;

    // coord_diff >= 2 * segment_len
    di1.add(di2, 400L);
    EXPECT_EQ(di1.buckets[0], 0L);
    EXPECT_EQ(di1.buckets[2], 10L);
    EXPECT_EQ(di1.coord, 1000L);
    di1.coord = 1L;
    di1.buckets[0] = 100L;

    // coord_diff >=  segment_len && < 2 * segment_len
    di1.add(di2, 900L);
    EXPECT_EQ(di1.buckets[0], 0L);
    EXPECT_EQ(di1.buckets[2], 10L);
    EXPECT_EQ(di1.coord, 901L);
}

TEST_F(ClickSegmentTestSuite, RatioBlackNodeCase_ctor) {
    RatioBlackNode rbn1;
    EXPECT_EQ(rbn1.sign, 0U);
    EXPECT_EQ(rbn1.coord, 0U);
    EXPECT_EQ(rbn1.fit_cumulant, 0L);
    EXPECT_EQ(rbn1.ref_cumulant, 0L);
    EXPECT_EQ(rbn1.last_fit_cumulant, 0L);
    EXPECT_DOUBLE_EQ(rbn1.last_ratio, 0);

    RatioBlackNode rbn2(123U, 234L, 345L, 567L);
    EXPECT_EQ(rbn2.sign, 123U);
    EXPECT_EQ(rbn2.coord, 234L);
    EXPECT_EQ(rbn2.fit_cumulant, 345L);
    EXPECT_EQ(rbn2.ref_cumulant, 567L);
    EXPECT_EQ(rbn2.last_fit_cumulant, 0L);
    EXPECT_DOUBLE_EQ(rbn2.last_ratio, 0);
}

TEST_F(ClickSegmentTestSuite, RatioBlackNodeCase_add) {
    RatioBlackNode rbn1;
    RatioBlackNode rbn2;
    rbn2.fit_cumulant = 1;
    rbn2.ref_cumulant = 2;
    rbn2.coord = 1;
    rbn1.add(rbn2, 100);
    EXPECT_EQ(rbn1.coord, 0L);
    EXPECT_EQ(rbn1.fit_cumulant, 1L);
    EXPECT_EQ(rbn1.ref_cumulant, 2L);
    EXPECT_EQ(rbn1.last_fit_cumulant, 0L);
    EXPECT_DOUBLE_EQ(rbn1.last_ratio, 0);

    rbn2.coord = 102;
    rbn2.fit_cumulant = 0;
    rbn2.ref_cumulant = 2;
    rbn1.add(rbn2, 100);
    EXPECT_EQ(rbn1.coord, 100);
    EXPECT_EQ(rbn1.fit_cumulant, 0L);
    EXPECT_EQ(rbn1.ref_cumulant, 2L);
    EXPECT_EQ(rbn1.last_fit_cumulant, 1L);
    EXPECT_DOUBLE_EQ(rbn1.last_ratio, 0.5);

    rbn2.coord = 400;
    rbn2.fit_cumulant = 1;
    rbn2.ref_cumulant = 1;
    rbn1.fit_cumulant = 1;
    rbn1.ref_cumulant = 2;
    rbn1.add(rbn2, 100);
    EXPECT_EQ(rbn1.coord, 400L);
    EXPECT_EQ(rbn1.fit_cumulant, 1L);
    EXPECT_EQ(rbn1.ref_cumulant, 1L);
    EXPECT_EQ(rbn1.last_fit_cumulant, 0L);
    EXPECT_DOUBLE_EQ(rbn1.last_ratio, 0);

    RatioBlackNode rbn3;
    rbn3.fit_cumulant = 1L;
    rbn3.ref_cumulant = 0L;
    rbn3.coord = 0;
    RatioBlackNode rbn4;
    rbn4.fit_cumulant = 1L;
    rbn4.ref_cumulant = 1L;
    rbn4.coord = 100;
    rbn3.add(rbn4, 100);
    EXPECT_EQ(1L, rbn3.fit_cumulant);
    EXPECT_EQ(1L, rbn3.ref_cumulant);
    EXPECT_EQ(1L, rbn3.last_fit_cumulant);
    EXPECT_EQ(0L, rbn3.last_ratio);
}

TEST_F(ClickSegmentTestSuite, init_case) {
    ClickSegment<SegLimitNode> seg;
    ClickSegment<RateLimitNode> rate;
    ASSERT_TRUE(seg.init(1800));
    ASSERT_TRUE(rate.init(1800));
    ASSERT_TRUE(seg.init(1, 2, 3));
    EXPECT_EQ(seg._segment_len, 3);

    EXPECT_EQ(rate._state_ok, true);
    EXPECT_EQ(rate._latest_coord, 0);
    EXPECT_EQ(rate._node_num, 0);
    EXPECT_TRUE(rate._idx != NULL);
    EXPECT_TRUE(rate._que != NULL);
    rate.uninit();
    EXPECT_EQ(rate._state_ok, false);
    EXPECT_EQ(rate._latest_coord, 0);
    EXPECT_EQ(rate._node_num, 0);
    EXPECT_TRUE(rate._idx == NULL);
    EXPECT_TRUE(rate._que == NULL);
}

TEST_F(ClickSegmentTestSuite, seg_add_case) {
    ClickSegment<SegLimitNode> seg;
    int64_t segment_len = 100;
    ASSERT_TRUE(seg.init(segment_len));

    SegLimitNode sln;
    sln.sign = 0x1104;
    sln.cumulant = 1;

    SegLimitNode sln2;
    sln2.sign = 0x1102;
    sln2.cumulant = 2;

    for (int i = 0; i < segment_len; i++) {
        sln.coord = i;
        const SegLimitNode* res = NULL;
        ASSERT_TRUE(seg.enter(sln.sign, sln.coord, sln, &res));
        EXPECT_EQ(res->cumulant, i + 1);
        if (i == 0) {
            EXPECT_EQ(seg._node_num, 1);
        }
        const SegLimitNode* q = seg.query_segment(sln.sign);
        EXPECT_EQ(q->cumulant, res->cumulant);

        sln2.coord = i / 2;
        ASSERT_TRUE(seg.enter(sln2.sign, sln2.coord, sln2));

        EXPECT_EQ(seg._node_num, 2);
    }

    // sln fea slid segment
    sln.coord = 100;
    const SegLimitNode* res = NULL;
    ASSERT_TRUE(seg.enter(sln.sign, sln.coord, sln, &res));
    EXPECT_EQ(res->cumulant, 1);
    EXPECT_EQ(res->last_cumulant, 100);

    const SegLimitNode* q2 = seg.query_segment(sln2.sign);
    EXPECT_EQ(q2->cumulant, 200);
}
    
TEST_F(ClickSegmentTestSuite, seg_add_case_slide) {
    ClickSegment<SegLimitNode> seg;
    int64_t segment_len = 100;
    ASSERT_TRUE(seg.init(segment_len));

    SegLimitNode sln;
    sln.cumulant = 1;

    // coord 0 -> 199
    for (int i = 0; i < segment_len * 2; i++) {
        sln.sign = i;
        sln.coord = i;
        const SegLimitNode* res = NULL;
        ASSERT_TRUE(seg.enter(sln.sign, sln.coord, sln, &res));
        EXPECT_EQ(seg._node_num, i + 1);
    }
    int64_t node_num = seg._node_num;

    // coord 201
    // slide segment
    sln.sign = 0;
    sln.cumulant = 2;
    sln.coord = segment_len * 2 + 1;
    const SegLimitNode* res = NULL;
    // will gc sign = 1, move head sign = 0
    ASSERT_TRUE(seg.enter(sln.sign, sln.coord, sln, &res));
    EXPECT_EQ(seg._node_num, node_num - 1);
    EXPECT_EQ(res->cumulant, 2);
    EXPECT_EQ(res->last_cumulant, 0);

    // gc sign=1 fea
    sln.sign = 999999;
    sln.cumulant = 1;
    sln.coord = segment_len * 2 + 2;
    // coord 202
    // will gc sign = 2, add sign = 999999
    ASSERT_TRUE(seg.enter(sln.sign, sln.coord, sln, &res));
    EXPECT_EQ(seg._node_num, node_num - 1);
    EXPECT_EQ(res->cumulant, 1);
    ASSERT_TRUE(seg.enter(sln.sign, sln.coord, sln, &res));
    EXPECT_EQ(seg._node_num, node_num - 1);
    EXPECT_EQ(res->cumulant, 2);
}

TEST_F(ClickSegmentTestSuite, rate_case_1) {
    ClickSegment<RateLimitNode> rate;
    int64_t segment_len = 100;
    ASSERT_TRUE(rate.init(segment_len));

    RateLimitNode rln;
    const RateLimitNode* res = NULL;

    rln.sign = 0x1104;
    rln.coord = 1;
    rln.fit_cumulant = 1;
    rln.ref_cumulant = 2;

    ASSERT_TRUE(rate.enter(rln.sign, rln.coord, rln, &res));
    EXPECT_EQ(res->fit_cumulant, 1);
    EXPECT_EQ(res->ref_cumulant, 2);
    EXPECT_EQ(rate._node_num, 1);

    rln.fit_cumulant = 2;
    rln.ref_cumulant = 3;
    rln.coord = 100;
    ASSERT_TRUE(rate.enter(rln.sign, rln.coord, rln, &res));
    EXPECT_EQ(res->fit_cumulant, 3);
    EXPECT_EQ(res->ref_cumulant, 5);
    EXPECT_EQ(rate._node_num, 1);

    rln.fit_cumulant = 1;
    rln.ref_cumulant = 0;
    rln.coord = 101;
    ASSERT_TRUE(rate.enter(rln.sign, rln.coord, rln, &res));
    EXPECT_EQ(res->fit_cumulant, 1);
    EXPECT_EQ(res->ref_cumulant, 0);
    EXPECT_EQ(rate._node_num, 1);

    rln.coord = 200;
    ASSERT_TRUE(rate.enter(rln.sign, rln.coord, rln, &res));
    EXPECT_EQ(res->fit_cumulant, 2);
    EXPECT_EQ(res->ref_cumulant, 0);
    EXPECT_EQ(rate._node_num, 1);
}

TEST_F(ClickSegmentTestSuite, rate_case_mem) {
    ClickSegment<RateLimitNode> rate;
    int64_t segment_len = 100;
    ASSERT_TRUE(rate.init(segment_len));

    RateLimitNode rln;
    const RateLimitNode* res = NULL;

    rln.sign = 0x1104;
    rln.coord = 1;
    rln.fit_cumulant = 1;
    rln.ref_cumulant = 2;

    for (int i = 0; i < 100000; i++) {
        rln.sign = rand() % 1000;
        rln.coord = i;
        ASSERT_TRUE(rate.enter(rln.sign, rln.coord, rln, &res));
    }
    CWARNING_LOG("random 10w node, total 1000 fea, node_num=%ld", 
            rate._node_num);
    EXPECT_LT(rate._node_num, 800);
    EXPECT_GT(rate._node_num, 100);
}

TEST_F(ClickSegmentTestSuite, serialize_case) {
    ClickSegment<RateLimitNode> seg;
    int segment_len = 100;
    ASSERT_TRUE(seg.init(segment_len));

    RateLimitNode node;
    node.fit_cumulant = 1;
    node.ref_cumulant = 2;
    for (int i = 0; i < 1003; i++) {
        node.sign = i * 103;
        node.coord = i;
        ASSERT_TRUE(seg.enter(node, NULL));
    }
    int64_t node_num = seg._node_num;
    int64_t latest_coord = seg._latest_coord;

    const char* file = "tmpfile_clickseg.ckpt";
    std::string cmd = "rm -f ";
    cmd += file;
    system(cmd.c_str());

    // dump
    FileArchive ar;
    ASSERT_TRUE(ar.open_w(file));
    ASSERT_TRUE(seg.serialize<FileArchive>(&ar));
    ar.close();

    // load
    FileArchive ar2;
    ASSERT_TRUE(ar2.open_r(file));
    ClickSegment<RateLimitNode> seg2;
    segment_len = 200;
    ASSERT_TRUE(seg2.init(segment_len));
    EXPECT_FALSE(seg2.deserialize<FileArchive>(&ar2));
    seg2.uninit();
    ar2.close();

    ASSERT_TRUE(ar2.open_r(file));
    segment_len = 100;
    ASSERT_TRUE(seg2.init(segment_len));
    ASSERT_TRUE(seg2.deserialize<FileArchive>(&ar2));
    EXPECT_EQ(seg2._latest_coord, latest_coord);
    EXPECT_EQ(seg2._segment_len, segment_len);
    EXPECT_EQ(seg2._node_num, node_num);
    ar2.close();

    system(cmd.c_str());
}

TEST_F(ClickSegmentTestSuite, update_node_fail_case) {
    ClickSegment<RateLimitNode> seg;
    int segment_len = 100;
    ASSERT_TRUE(seg.init(segment_len));

    RateLimitNode* l_node = NULL;
    RateLimitNode r_node;
    EXPECT_FALSE(seg._update_node(l_node, r_node));
    seg.uninit();
}

TEST_F(ClickSegmentTestSuite, query_segment_with_coord_case) {
    ClickSegment<SegLimitNode> seg;
    int segment_len = 100;
    ASSERT_TRUE(seg.init(segment_len));
    SegLimitNode sln;
    sln.sign = 0;
    sln.cumulant = 2;
    sln.coord = 1;
    ASSERT_TRUE(seg.enter(sln.sign, sln.coord, sln));
    EXPECT_TRUE(seg.query_segment(sln.sign, -1000) == NULL);
    EXPECT_TRUE(seg.query_segment(sln.sign, sln.coord) != NULL);
    seg.uninit();
}

}  // end namespace common_lib
}  // end namespace themis
}  // end namespace anti

/* vim: set ts=4 sw=4 sts=4 tw=100 */
