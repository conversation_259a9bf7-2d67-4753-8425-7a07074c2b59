// Copyright (c) 2025 Baidu.com, Inc. All Rights Reserved
// @Author: l<PERSON><PERSON><PERSON><PERSON>(<EMAIL>)

#include "sliding_window_v2_test_base.h"

/**
 * @brief SlidingWindowV2性能测试类
 */
class SlidingWindowV2PerformanceTest : public SlidingWindowTestBase {
protected:
    void SetUp() override {
        SlidingWindowTestBase::SetUp();
    }

    void TearDown() override {
        SlidingWindowTestBase::TearDown();
    }
};

/**
 * @brief 基本性能对比测试
 */
TEST_F(SlidingWindowV2PerformanceTest, test_basic_performance_comparison) {
    auto test_data = generate_test_data(100000);  // 100K数据

    auto pool_result = benchmark_pool_operations(test_data);
    double pool_avg_us = pool_result.first;
    size_t pool_success = pool_result.second;

    auto orig_result = benchmark_orig_operations(test_data);
    double orig_avg_us = orig_result.first;
    size_t orig_success = orig_result.second;

    std::cout << "\n普通操作性能对比:" << std::endl;
    std::cout << "  SlidingWindowV2: " << std::fixed << std::setprecision(2) << pool_avg_us
              << "us/op, 成功:" << pool_success << std::endl;
    std::cout << "  原版SlidingWindow: " << std::fixed << std::setprecision(2) << orig_avg_us
              << "us/op, 成功:" << orig_success << std::endl;

    EXPECT_GT(pool_avg_us, 0);
    EXPECT_GT(orig_avg_us, 0);
    EXPECT_EQ(pool_success, orig_success);

    if (orig_avg_us > 0 && pool_avg_us > 0) {
        if (pool_avg_us < orig_avg_us) {
            double speedup = orig_avg_us / pool_avg_us;
            std::cout << "  SlidingWindowV2比原版快 " << std::fixed << std::setprecision(1)
                      << (speedup - 1) * 100 << "%" << std::endl;
        } else {
            double ratio = pool_avg_us / orig_avg_us;
            std::cout << "  SlidingWindowV2比原版慢 " << std::fixed << std::setprecision(1)
                      << (ratio - 1) * 100 << "%" << std::endl;
        }

        double ratio = pool_avg_us / orig_avg_us;
        EXPECT_LT(ratio, 2.0);
    }
}

/**
 * @brief 一致性验证测试
 */
TEST_F(SlidingWindowV2PerformanceTest, test_consistency_verification) {
    auto test_data = generate_test_data(10000);
    auto test_keys = extract_unique_keys(test_data);

    size_t pool_success = 0, orig_success = 0;
    for (const auto& data : test_data) {
        if (_pool_sw->enter(data.key, data.coord, data.item)) {
            pool_success++;
        }
        // 原版SlidingWindow需要SegmentItem类型
        SegmentItem orig_item(data.item.cumulant());
        if (_orig_sw->enter(data.key, data.coord, orig_item)) {
            orig_success++;
        }
    }

    EXPECT_EQ(pool_success, orig_success);

    bool query_ok = verify_query_consistency(*_pool_sw, *_orig_sw, test_keys);
    EXPECT_TRUE(query_ok);

    EXPECT_EQ(_pool_sw->step_length(), _orig_sw->step_length());
    EXPECT_EQ(_pool_sw->step_num(), _orig_sw->step_num());
}

/**
 * @brief 滑动场景性能测试
 */
TEST_F(SlidingWindowV2PerformanceTest, test_sliding_scenario_performance) {
    auto sliding_data = generate_test_data(50000, 1000);

    int64_t base_coord = 1000000;
    for (auto& data : sliding_data) {
        data.coord = base_coord;
        base_coord += 200;  // 快速递增，触发滑动
    }

    const int64_t slide_step_len = 100;
    const int slide_max_step_num = 5;
    const int64_t slide_segment_len = 400;

    auto pool_slide_result = benchmark_pool_operations(
            sliding_data, slide_step_len, slide_max_step_num, slide_segment_len);

    auto orig_slide_result = benchmark_orig_operations(
            sliding_data, slide_step_len, slide_max_step_num, slide_segment_len);

    std::cout << "\n快速滑动场景性能对比:" << std::endl;
    std::cout << "  SlidingWindowV2: " << std::fixed << std::setprecision(2)
              << pool_slide_result.first << "us/op" << std::endl;
    std::cout << "  原版SlidingWindow: " << std::fixed << std::setprecision(2)
              << orig_slide_result.first << "us/op" << std::endl;

    if (orig_slide_result.first > 0 && pool_slide_result.first > 0) {
        if (pool_slide_result.first < orig_slide_result.first) {
            double speedup = orig_slide_result.first / pool_slide_result.first;
            std::cout << "  SlidingWindowV2比原版快 " << std::fixed << std::setprecision(1)
                      << (speedup - 1) * 100 << "%" << std::endl;
        } else {
            double ratio = pool_slide_result.first / orig_slide_result.first;
            std::cout << "  SlidingWindowV2比原版慢 " << std::fixed << std::setprecision(1)
                      << (ratio - 1) * 100 << "%" << std::endl;
        }
    }

    EXPECT_GT(pool_slide_result.first, 0);
    EXPECT_GT(orig_slide_result.first, 0);
    EXPECT_EQ(pool_slide_result.second, orig_slide_result.second);

    TestSlidingWindow pool_slide_verify;
    OriginalSlidingWindow orig_slide_verify;

    ASSERT_TRUE(pool_slide_verify.init(slide_step_len, slide_max_step_num, slide_segment_len));
    ASSERT_TRUE(orig_slide_verify.init(slide_step_len, slide_max_step_num, slide_segment_len));

    size_t pool_verify_success = 0, orig_verify_success = 0;
    for (const auto& data : sliding_data) {
        if (pool_slide_verify.enter(data.key, data.coord, data.item)) {
            pool_verify_success++;
        }
        SegmentItem orig_item2(data.item.cumulant());
        if (orig_slide_verify.enter(data.key, data.coord, orig_item2)) {
            orig_verify_success++;
        }
    }

    EXPECT_EQ(pool_verify_success, orig_verify_success);

    pool_slide_verify.uninit();
    orig_slide_verify.uninit();
}