// Copyright (c) 2025 Baidu.com, Inc. All Rights Reserved
// @Author: <PERSON> (<EMAIL>)
//
// @Brief: PoolMiniHashMap 单元测试 - 基本功能和内存效率测试

#include <gtest/gtest.h>
#include <unordered_map>
#include <vector>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <chrono>
#include <thread>
#include <bsl/var/Dict.h>
#include <bsl/var/Int64.h>
#include <bsl/ResourcePool.h>

#include "pool_mini_hash_map.hpp"
#include "pool_hash_map.hpp"
#include "gc_slab_mempool_32.h"

using namespace anti::themis::common_lib;

class PoolMiniHashMapTest : public ::testing::Test {
protected:
    typedef uint64_t TestKey;
    typedef int64_t TestValue;
    typedef PoolMiniHashMap<TestKey, TestValue, GCSlabMempool32> TestMiniHashMap;
    typedef PoolHashMap<TestKey, TestValue, GCSlabMempool32> TestRegularHashMap;
    
    void SetUp() override {
        // 创建内存池
        _mem_pool = new GCSlabMempool32();
        ASSERT_NE(nullptr, _mem_pool);
        
        uint32_t node_size = TestMiniHashMap::get_node_size();
        std::vector<uint32_t> slabs = {node_size};
        
        int ret = _mem_pool->create(slabs.data(), slabs.size(), 50000);  // 增大内存池容量
        ASSERT_EQ(0, ret) << "内存池创建失败";
    }
    
    void TearDown() override {
        // 安全清理主内存池
        safe_delete_pool(_mem_pool);
    }

     /**
     * @brief 创建内存池
     */
    GCSlabMempool32* create_memory_pool() {
        GCSlabMempool32* pool = new GCSlabMempool32();
        if (!pool) {
            return nullptr;
        }

        uint32_t node_size = TestRegularHashMap::get_node_size();
        std::vector<uint32_t> slabs = {node_size};

        int ret = pool->create(&slabs[0], static_cast<uint32_t>(slabs.size()), 50000);
        if (ret != 0) {
            delete pool;
            return nullptr;
        }

        return pool;
    } 

    /**
     * @brief 改进后的RSS测试函数 - 包含数据插入
     */
    size_t test_rss_isolated_with_data(size_t hashmap_count, bool use_v2) {
        // 每次都创建新的内存池，避免static变量污染
        GCSlabMempool32* pool = create_memory_pool();
        
        // 记录基准RSS
        size_t rss_before = get_rss_memory();
        
        // 批量创建HashMap并插入数据
        std::vector<void*> hashmaps;
        hashmaps.reserve(hashmap_count);
        
        for (size_t i = 0; i < hashmap_count; ++i) {
            if (use_v2) {
                TestMiniHashMap* hashmap = new TestMiniHashMap(pool, 2);
                
                // 插入3-4个数据，保持在2个桶状态
                hashmap->emplace(i * 4 + 1, (i * 4 + 1) * 100);
                hashmap->emplace(i * 4 + 2, (i * 4 + 2) * 100);
                hashmap->emplace(i * 4 + 3, (i * 4 + 3) * 100);
                if (i % 2 == 0) { // 一半的HashMap插入4个数据
                    hashmap->emplace(i * 4 + 4, (i * 4 + 4) * 100);
                }
                
                hashmaps.push_back(hashmap);
            } else {
                TestRegularHashMap* hashmap = new TestRegularHashMap(pool, 2);
                
                // 插入相同的数据
                hashmap->emplace(i * 4 + 1, (i * 4 + 1) * 100);
                hashmap->emplace(i * 4 + 2, (i * 4 + 2) * 100);
                hashmap->emplace(i * 4 + 3, (i * 4 + 3) * 100);
                if (i % 2 == 0) { // 一半的HashMap插入4个数据
                    hashmap->emplace(i * 4 + 4, (i * 4 + 4) * 100);
                }
                
                hashmaps.push_back(hashmap);
            }
        }
        
        // 强制刷新RSS
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
        size_t rss_after = get_rss_memory();
        
        // 清理
        for (auto* h : hashmaps) {
            delete h;
        }
        delete pool;
        
        return (rss_after > rss_before) ? (rss_after - rss_before) : 0;
    }

protected:
    GCSlabMempool32* _mem_pool;
    
    // 辅助函数：生成测试数据
    std::vector<std::pair<TestKey, TestValue>> generate_test_data(size_t count, TestKey base_offset = 0) {
        std::vector<std::pair<TestKey, TestValue>> data;
        data.reserve(count);
        
        for (size_t i = 0; i < count; ++i) {
            TestKey key = base_offset + static_cast<TestKey>(i) + 1;
            TestValue value = static_cast<TestValue>(key) * 100;
            data.emplace_back(key, value);
        }
        
        return data;
    }
    
    // 辅助函数：获取进程RSS内存使用量(KB)
    size_t get_rss_memory() {
        std::ifstream status_file("/proc/self/status");
        if (!status_file.is_open()) {
            return 0;
        }
        
        std::string line;
        while (std::getline(status_file, line)) {
            if (line.find("VmRSS:") == 0) {
                std::istringstream iss(line);
                std::string label;
                size_t value{0};
                std::string unit;
                iss >> label >> value >> unit;
                return value;  // KB
            }
        }
        return 0;
    }
    
    // 辅助函数：创建额外的内存池（优化版本）
    GCSlabMempool32* create_additional_memory_pool(size_t capacity = 100000) {
        GCSlabMempool32* pool = new GCSlabMempool32();
        if (!pool) {
            return nullptr;
        }
        
        uint32_t node_size = TestMiniHashMap::get_node_size();
        std::vector<uint32_t> slabs = {node_size};
        
        int ret = pool->create(slabs.data(), slabs.size(), capacity);
        if (ret != 0) {
            delete pool;
            return nullptr;
        }
        
        return pool;
    }
    
    // 辅助函数：安全删除内存池
    void safe_delete_pool(GCSlabMempool32*& pool) {
        if (pool) {
            delete pool;
            pool = nullptr;
        }
    }
    
    // 辅助函数合并与模板化

    template<typename HashMapType>
    uint64_t get_hashmap_overhead_mem(HashMapType* map) {
        bsl::ResourcePool rp;
        bsl::var::Dict& dict = rp.create<bsl::var::Dict>();
        map->monitor(dict, rp);
        return dict["OVERHEAD_MEM"].to_uint64();
    }

    GCSlabMempool32* create_pool(uint32_t node_size, size_t capacity) {
        auto* pool = new GCSlabMempool32();
        if (!pool) return nullptr;
        std::vector<uint32_t> slabs = {node_size};
        if (pool->create(slabs.data(), slabs.size(), capacity) != 0) {
            delete pool;
            return nullptr;
        }
        return pool;
    }
};

TEST_F(PoolMiniHashMapTest, test_basic_construction) {
    // 测试默认构造函数
    TestMiniHashMap map1(nullptr);
    EXPECT_FALSE(map1.is_valid());
    EXPECT_EQ(0, map1.size());
    
    // 测试带内存池的构造函数
    TestMiniHashMap map2(_mem_pool, 1);
    EXPECT_TRUE(map2.is_valid());
    EXPECT_EQ(0, map2.size());
    
    // 测试不同初始桶数的构造（PoolMiniHashMap的特色：小桶数优化）
    TestMiniHashMap map3(_mem_pool, 2);
    EXPECT_TRUE(map3.is_valid());
    EXPECT_EQ(0, map3.size());
}

TEST_F(PoolMiniHashMapTest, test_basic_operations) {
    TestMiniHashMap mini_map(_mem_pool, 1);
    auto test_data = generate_test_data(4);
    
    // 测试插入操作
    for (const auto& item : test_data) {
        auto result = mini_map.emplace(item.first, item.second);
        EXPECT_TRUE(result.second) << "插入失败, key=" << item.first;
        EXPECT_NE(nullptr, result.first);
        EXPECT_EQ(item.second, *result.first);
    }
    
    EXPECT_EQ(4, mini_map.size());
    
    // 测试查找操作
    for (const auto& item : test_data) {
        const TestValue* found = mini_map.find(item.first);
        ASSERT_NE(nullptr, found) << "查找失败, key=" << item.first;
        EXPECT_EQ(item.second, *found);
    }
    
    // 测试查找不存在的key
    const TestValue* not_found = mini_map.find(999999);
    EXPECT_EQ(nullptr, not_found);
    
    // 测试重复插入
    auto dup_result = mini_map.emplace(test_data[0].first, 999999);
    EXPECT_FALSE(dup_result.second);  // 插入应该失败
    EXPECT_EQ(test_data[0].second, *dup_result.first);  // 值不变
    
    // 测试删除操作
    EXPECT_TRUE(mini_map.remove(test_data[0].first));
    EXPECT_EQ(3, mini_map.size());
    EXPECT_EQ(nullptr, mini_map.find(test_data[0].first));
    
    // 测试删除不存在的key
    EXPECT_FALSE(mini_map.remove(999999));
    EXPECT_EQ(3, mini_map.size());
    
    // 测试清空操作
    mini_map.clear();
    EXPECT_EQ(0, mini_map.size());
    
    for (const auto& item : test_data) {
        EXPECT_EQ(nullptr, mini_map.find(item.first));
    }
}

TEST_F(PoolMiniHashMapTest, test_copy_operations) {
    // 创建原始map并添加4个元素
    TestMiniHashMap original(_mem_pool, 1);
    auto test_data = generate_test_data(4);
    
    for (const auto& item : test_data) {
        original.emplace(item.first, item.second);
    }
    
    EXPECT_EQ(4, original.size());
    
    // 测试拷贝构造函数
    TestMiniHashMap copied(original);
    EXPECT_EQ(original.size(), copied.size());
    EXPECT_TRUE(copied.is_valid());
    
    // 验证所有数据都被正确拷贝
    bool all_data_copied = true;
    for (const auto& item : test_data) {
        const TestValue* found_in_original = original.find(item.first);
        const TestValue* found_in_copied = copied.find(item.first);
        
        if (!found_in_original || !found_in_copied || 
            *found_in_original != *found_in_copied || 
            *found_in_copied != item.second) {
            all_data_copied = false;
            break;
        }
    }
    EXPECT_TRUE(all_data_copied);
    
    // 验证是深拷贝：修改原map不影响拷贝的map
    original.emplace(999, 99900);
    EXPECT_NE(original.size(), copied.size());
    EXPECT_EQ(nullptr, copied.find(999));
    
    // 测试赋值运算符
    TestMiniHashMap target(_mem_pool, 2);
    target.emplace(8888, 888800);
    EXPECT_EQ(1, target.size());
    
    target = original;
    EXPECT_EQ(original.size(), target.size());
    EXPECT_EQ(nullptr, target.find(8888));  // 原有数据被清除
    EXPECT_NE(nullptr, target.find(999));   // 新数据被拷贝
    
    // 测试自赋值
    size_t original_size = original.size();
    original = original;
    EXPECT_EQ(original_size, original.size());
    EXPECT_TRUE(original.is_valid());
}

TEST_F(PoolMiniHashMapTest, test_shared_pool_memory_optimization) {
    const size_t hashmap_count = 20000;
    const size_t data_per_hashmap = 2;
    size_t estimated_nodes = hashmap_count * data_per_hashmap + hashmap_count * 2;
    auto* shared_pool = create_pool(TestMiniHashMap::get_node_size(), estimated_nodes);
    ASSERT_NE(nullptr, shared_pool);
    std::vector<TestRegularHashMap*> regular_hashmaps;
    std::vector<TestMiniHashMap*> mini_hashmaps;
    regular_hashmaps.reserve(hashmap_count);
    mini_hashmaps.reserve(hashmap_count);
    for (size_t i = 0; i < hashmap_count; ++i) {
        auto* regular_map = new TestRegularHashMap(shared_pool, 2);
        auto* mini_map = new TestMiniHashMap(shared_pool, 2);
        for (size_t j = 0; j < data_per_hashmap; ++j) {
            TestKey key = static_cast<TestKey>(i * data_per_hashmap + j + 1);
            TestValue value = key * 100;
            regular_map->emplace(key, value);
            mini_map->emplace(key, value);
        }
        regular_hashmaps.push_back(regular_map);
        mini_hashmaps.push_back(mini_map);
    }
    uint64_t regular_total_overhead = 0;
    uint64_t mini_total_overhead = 0;
    for (size_t i = 0; i < hashmap_count; ++i) {
        regular_total_overhead += get_hashmap_overhead_mem(regular_hashmaps[i]);
        mini_total_overhead += get_hashmap_overhead_mem(mini_hashmaps[i]);
    }
    uint64_t theoretical_saving_per_hashmap = 2 * sizeof(GCSlabMempool32::TVaddr);
    uint64_t theoretical_total_saving = hashmap_count * theoretical_saving_per_hashmap;
    uint64_t actual_total_saving = (regular_total_overhead > mini_total_overhead) ? 
                                   (regular_total_overhead - mini_total_overhead) : 0;
    EXPECT_GT(regular_total_overhead, mini_total_overhead);
    EXPECT_NEAR(actual_total_saving, theoretical_total_saving, theoretical_total_saving * 0.05);
    for (auto* p : regular_hashmaps) delete p;
    for (auto* p : mini_hashmaps) delete p;
    delete shared_pool;
}

TEST_F(PoolMiniHashMapTest, test_small_scale_precise_verification) {
    const size_t hashmap_count = 100;
    const size_t theoretical_saving_per_hashmap = 2 * sizeof(GCSlabMempool32::TVaddr);
    auto* shared_pool = create_pool(TestMiniHashMap::get_node_size(), hashmap_count * 4);
    ASSERT_NE(nullptr, shared_pool);
    std::vector<TestRegularHashMap*> regular_hashmaps;
    std::vector<TestMiniHashMap*> mini_hashmaps;
    for (size_t i = 0; i < hashmap_count; ++i) {
        auto* regular_map = new TestRegularHashMap(shared_pool, 2);
        auto* mini_map = new TestMiniHashMap(shared_pool, 2);
        TestKey key = static_cast<TestKey>(i + 1);
        TestValue value = key * 100;
        regular_map->emplace(key, value);
        mini_map->emplace(key, value);
        ASSERT_EQ(2U, regular_map->bucket_count());
        ASSERT_EQ(2U, mini_map->bucket_count());
        regular_hashmaps.push_back(regular_map);
        mini_hashmaps.push_back(mini_map);
    }
    uint64_t single_regular_overhead = get_hashmap_overhead_mem(regular_hashmaps[0]);
    uint64_t single_mini_overhead = get_hashmap_overhead_mem(mini_hashmaps[0]);
    uint64_t single_saving = single_regular_overhead - single_mini_overhead;
    EXPECT_EQ(single_saving, theoretical_saving_per_hashmap);
    bool all_consistent = true;
    for (size_t i = 0; i < hashmap_count; ++i) {
        if (get_hashmap_overhead_mem(regular_hashmaps[i]) != single_regular_overhead ||
            get_hashmap_overhead_mem(mini_hashmaps[i]) != single_mini_overhead) {
            all_consistent = false;
            break;
        }
    }
    EXPECT_TRUE(all_consistent);
    for (auto* p : regular_hashmaps) delete p;
    for (auto* p : mini_hashmaps) delete p;
    delete shared_pool;
}

TEST_F(PoolMiniHashMapTest, test_expansion_behavior_analysis) {
    auto* pool2 = create_pool(TestMiniHashMap::get_node_size(), 100000);
    ASSERT_NE(nullptr, pool2);
    auto* mini_map = new TestMiniHashMap(_mem_pool, 2);
    auto* regular_map = new TestRegularHashMap(pool2, 2);
    ASSERT_TRUE(mini_map->is_valid());
    ASSERT_TRUE(regular_map->is_valid());
    auto test_data = generate_test_data(6);
    for (const auto& item : test_data) {
        ASSERT_TRUE(mini_map->is_valid());
        ASSERT_TRUE(regular_map->is_valid());
        auto mini_result = mini_map->emplace(item.first, item.second);
        auto regular_result = regular_map->emplace(item.first, item.second);
        ASSERT_NE(nullptr, mini_result.first);
        ASSERT_NE(nullptr, regular_result.first);
    }
    delete mini_map;
    delete regular_map;
    delete pool2;
}

TEST_F(PoolMiniHashMapTest, test_rss_optimization_with_data) {
    std::cout << "\n=== 改进后的RSS优化测试（包含数据插入） ===" << std::endl;
    const size_t count = 800000;
    std::cout << "测试规模: " << count << " 个HashMap，每个插入3-4个数据" << std::endl;
    std::cout << "测试V1版本..." << std::endl;
    size_t rss_v1 = test_rss_isolated_with_data(count, false);
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    std::cout << "测试V2版本..." << std::endl;
    size_t rss_v2 = test_rss_isolated_with_data(count, true);
    std::cout << "V1 RSS 增长: " << rss_v1 << " KB" << std::endl;
    std::cout << "V2 RSS 增长: " << rss_v2 << " KB" << std::endl;
    if (rss_v1 > rss_v2) {
        size_t rss_saved = rss_v1 - rss_v2;
        double saving_rate = static_cast<double>(rss_saved) / rss_v1 * 100.0;
        double avg_saving = static_cast<double>(rss_saved * 1024) / count;
        std::cout << "RSS 节省: " << rss_saved << " KB (" 
                  << std::fixed << std::setprecision(1) << saving_rate << "% )" << std::endl;
        std::cout << "平均每个HashMap节省: " << std::fixed << std::setprecision(1) 
                  << avg_saving << " bytes" << std::endl;
        EXPECT_LT(rss_v2, rss_v1);
        std::cout << "✓ RSS内存优化效果确认" << std::endl;
    } else {
        std::cout << "RSS 差异不明显，可能受系统因素影响" << std::endl;
    }
    const size_t avg_data_per_hashmap = 3.5;
    const size_t total_data_count = count * avg_data_per_hashmap;
    const size_t node_size = TestRegularHashMap::get_node_size();
    const size_t theoretical_data_memory = total_data_count * node_size / 1024;
    std::cout << "\n数据相关分析:" << std::endl;
    std::cout << "  总数据条数: " << total_data_count << std::endl;
    std::cout << "  理论数据内存: " << theoretical_data_memory << " KB" << std::endl;
    std::cout << "  注: RSS测试包含HashMap对象 + 内存池 + 数据 + 系统分配开销" << std::endl;
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */