#ifndef POOL_HASH_MAP_TEST_BASE_H
#define POOL_HASH_MAP_TEST_BASE_H

#include <gtest/gtest.h>
#include <unordered_map>
#include <vector>
#include <chrono>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <gflags/gflags.h>

#include "pool_hash_map.hpp"
#include "gc_slab_mempool_32.h"

using namespace anti::themis::common_lib;

// 测试类型定义
typedef uint64_t TestKey;
typedef uint64_t TestValue;
typedef PooHashMap<TestKey, TestValue, GCSlabMempool32> TestHashMap;

// 测试常量
enum class TestConstants {
    INITIAL_BUCKET_NUM = 8,
    LARGE_BLOCK_ITEM_NUM = 100000,
};

// =============================================================================
// 简化的辅助函数
// =============================================================================

/**
 * @brief 生成测试数据
 */
inline std::vector<std::pair<TestKey, TestValue>> generate_test_data(
        size_t count,
        TestKey start_offset = 0) {
    std::vector<std::pair<TestKey, TestValue>> data;
    data.reserve(count);

    for (size_t i = 0; i < count; ++i) {
        TestKey key = start_offset + static_cast<TestKey>(i) + 1;  // 确保唯一且非零
        TestValue value = static_cast<TestValue>(key) * 100;
        data.emplace_back(key, value);
    }

    return data;
}

/**
 * @brief 获取进程RSS内存使用量(KB)
 */
inline size_t get_rss_memory() {
    std::ifstream status_file("/proc/self/status");
    if (!status_file.is_open()) {
        return 0;
    }

    std::string line;
    while (std::getline(status_file, line)) {
        if (line.find("VmRSS:") == 0) {
            std::istringstream iss(line);
            std::string label;
            size_t value{0};
            std::string unit;
            iss >> label >> value >> unit;
            return value;  // KB
        }
    }
    return 0;
}

/**
 * @brief 获取PoolHashMap的OVERHEAD_MEM(bytes)
 */
inline size_t get_pool_hashmap_overhead_mem(TestHashMap* pool_map) {
    bsl::ResourcePool rp;
    bsl::var::Dict& dict = rp.create<bsl::var::Dict>();
    pool_map->monitor(dict, rp);
    return dict["OVERHEAD_MEM"].to_uint64();
}

/**
 * @brief 获取PoolHashMap的HASHMAP_OBJ_MEM(bytes)
 */
inline size_t get_pool_hashmap_obj_mem(TestHashMap* pool_map) {
    bsl::ResourcePool rp;
    bsl::var::Dict& dict = rp.create<bsl::var::Dict>();
    pool_map->monitor(dict, rp);
    return dict["HASHMAP_OBJ_MEM"].to_uint64();
}

/**
 * @brief 获取PoolHashMap的BUCKET_ARRAY_MEM(bytes)
 */
inline size_t get_pool_hashmap_bucket_array_mem(TestHashMap* pool_map) {
    bsl::ResourcePool rp;
    bsl::var::Dict& dict = rp.create<bsl::var::Dict>();
    pool_map->monitor(dict, rp);
    return dict["BUCKET_ARRAY_MEM"].to_uint64();
}

/**
 * @brief 获取内存池的有效内存使用量(bytes) - 数据+管理开销
 */
inline size_t get_mempool_effective_memory(GCSlabMempool32* mem_pool) {
    bsl::ResourcePool rp;
    bsl::var::Dict& dict = rp.create<bsl::var::Dict>();
    mem_pool->monitor(dict, rp);
    return dict["EFFECTIVE_POOL_MEMORY"].to_uint64();
}

/**
 * @brief 详细性能测试结果
 */
struct DetailedPerformanceResult {
    double insert_ops_per_sec;
    double find_ops_per_sec;
    double remove_ops_per_sec;
    size_t insert_time_ms;
    size_t find_time_ms;
    size_t remove_time_ms;
    size_t total_time_ms;
    size_t insert_success_count;
    size_t find_success_count;
    size_t remove_success_count;
    bool test_successful;

    DetailedPerformanceResult() :
            insert_ops_per_sec(0.0),
            find_ops_per_sec(0.0),
            remove_ops_per_sec(0.0),
            insert_time_ms(0),
            find_time_ms(0),
            remove_time_ms(0),
            total_time_ms(0),
            insert_success_count(0),
            find_success_count(0),
            remove_success_count(0),
            test_successful(false) {}
};

/**
 * @brief 执行PoolHashMap详细性能测试 (插入+查询+删除)
 */
inline DetailedPerformanceResult benchmark_detailed_operations(
        TestHashMap* pool_map,
        const std::vector<std::pair<TestKey, TestValue>>& data) {
    DetailedPerformanceResult result;
    auto total_start = std::chrono::high_resolution_clock::now();

    // 1. 插入性能测试
    auto insert_start = std::chrono::high_resolution_clock::now();
    for (const auto& item : data) {
        auto insert_result = pool_map->emplace(item.first, item.second);
        if (insert_result.first != nullptr) {
            result.insert_success_count++;
        }
    }
    auto insert_end = std::chrono::high_resolution_clock::now();
    result.insert_time_ms =
            std::chrono::duration_cast<std::chrono::milliseconds>(insert_end - insert_start)
                    .count();

    // 2. 查询性能测试
    auto find_start = std::chrono::high_resolution_clock::now();
    for (const auto& item : data) {
        TestValue* found = pool_map->find(item.first);
        if (found != nullptr) {
            result.find_success_count++;
        }
    }
    auto find_end = std::chrono::high_resolution_clock::now();
    result.find_time_ms =
            std::chrono::duration_cast<std::chrono::milliseconds>(find_end - find_start).count();

    // 3. 删除性能测试
    auto remove_start = std::chrono::high_resolution_clock::now();
    for (const auto& item : data) {
        if (pool_map->remove(item.first)) {
            result.remove_success_count++;
        }
    }
    auto remove_end = std::chrono::high_resolution_clock::now();
    result.remove_time_ms =
            std::chrono::duration_cast<std::chrono::milliseconds>(remove_end - remove_start)
                    .count();

    auto total_end = std::chrono::high_resolution_clock::now();
    result.total_time_ms =
            std::chrono::duration_cast<std::chrono::milliseconds>(total_end - total_start).count();

    // 计算OPS
    if (result.insert_time_ms > 0) {
        result.insert_ops_per_sec =
                static_cast<double>(result.insert_success_count) / (result.insert_time_ms / 1000.0);
    }
    if (result.find_time_ms > 0) {
        result.find_ops_per_sec =
                static_cast<double>(result.find_success_count) / (result.find_time_ms / 1000.0);
    }
    if (result.remove_time_ms > 0) {
        result.remove_ops_per_sec =
                static_cast<double>(result.remove_success_count) / (result.remove_time_ms / 1000.0);
    }

    result.test_successful =
            (result.insert_success_count > 0 && result.find_success_count > 0 &&
             result.remove_success_count > 0);

    return result;
}

/**
 * @brief 执行std::unordered_map详细性能测试 (插入+查询+删除)
 */
inline DetailedPerformanceResult benchmark_detailed_operations(
        std::unordered_map<TestKey, TestValue>* std_map,
        const std::vector<std::pair<TestKey, TestValue>>& data) {
    DetailedPerformanceResult result;
    auto total_start = std::chrono::high_resolution_clock::now();

    // 1. 插入性能测试
    auto insert_start = std::chrono::high_resolution_clock::now();
    for (const auto& item : data) {
        auto insert_result = std_map->emplace(item.first, item.second);
        if (insert_result.second) {
            result.insert_success_count++;
        }
    }
    auto insert_end = std::chrono::high_resolution_clock::now();
    result.insert_time_ms =
            std::chrono::duration_cast<std::chrono::milliseconds>(insert_end - insert_start)
                    .count();

    // 2. 查询性能测试
    auto find_start = std::chrono::high_resolution_clock::now();
    for (const auto& item : data) {
        auto found = std_map->find(item.first);
        if (found != std_map->end()) {
            result.find_success_count++;
        }
    }
    auto find_end = std::chrono::high_resolution_clock::now();
    result.find_time_ms =
            std::chrono::duration_cast<std::chrono::milliseconds>(find_end - find_start).count();

    // 3. 删除性能测试
    auto remove_start = std::chrono::high_resolution_clock::now();
    for (const auto& item : data) {
        if (std_map->erase(item.first) > 0) {
            result.remove_success_count++;
        }
    }
    auto remove_end = std::chrono::high_resolution_clock::now();
    result.remove_time_ms =
            std::chrono::duration_cast<std::chrono::milliseconds>(remove_end - remove_start)
                    .count();

    auto total_end = std::chrono::high_resolution_clock::now();
    result.total_time_ms =
            std::chrono::duration_cast<std::chrono::milliseconds>(total_end - total_start).count();

    // 计算OPS
    if (result.insert_time_ms > 0) {
        result.insert_ops_per_sec =
                static_cast<double>(result.insert_success_count) / (result.insert_time_ms / 1000.0);
    }
    if (result.find_time_ms > 0) {
        result.find_ops_per_sec =
                static_cast<double>(result.find_success_count) / (result.find_time_ms / 1000.0);
    }
    if (result.remove_time_ms > 0) {
        result.remove_ops_per_sec =
                static_cast<double>(result.remove_success_count) / (result.remove_time_ms / 1000.0);
    }

    result.test_successful =
            (result.insert_success_count > 0 && result.find_success_count > 0 &&
             result.remove_success_count > 0);

    return result;
}

/**
 * @brief 验证两个HashMap查询结果一致性
 */
inline bool verify_query_consistency(
        TestHashMap* pool_map,
        std::unordered_map<TestKey, TestValue>* std_map,
        const std::vector<std::pair<TestKey, TestValue>>& test_data) {
    for (const auto& item : test_data) {
        TestValue* pool_found = pool_map->find(item.first);
        auto std_found = std_map->find(item.first);

        bool pool_exists = (pool_found != nullptr);
        bool std_exists = (std_found != std_map->end());

        if (pool_exists != std_exists) {
            return false;
        }
        if (pool_exists && std_exists && *pool_found != std_found->second) {
            return false;
        }
    }

    return true;
}

/**
 * @brief 验证monitor输出基本字段
 */
inline bool verify_monitor_fields(TestHashMap* pool_map) {
    bsl::ResourcePool rp;
    bsl::var::Dict& dict = rp.create<bsl::var::Dict>();
    pool_map->monitor(dict, rp);

    try {
        dict["OVERHEAD_MEM"].to_uint64();
        return true;
    } catch (...) {
        return false;
    }
}

/**
 * @brief 创建内存池 - 根据PoolHashMap Node大小定制
 */
inline GCSlabMempool32* create_memory_pool() {
    GCSlabMempool32* pool = new GCSlabMempool32();
    if (!pool) {
        return nullptr;
    }

    uint32_t node_size = TestHashMap::get_node_size();
    std::vector<uint32_t> slabs = {node_size};

    int ret = pool->create(slabs.data(), static_cast<uint32_t>(slabs.size()), static_cast<uint32_t>(TestConstants::LARGE_BLOCK_ITEM_NUM));
    if (ret != 0) {
        delete pool;
        return nullptr;
    }

    return pool;
}

/**
 * @brief PoolHashMap测试基类 - 简化版本
 */
class PoolHashMapTestBase : public ::testing::Test {
protected:
    void SetUp() override {
        _mem_pool = create_memory_pool();
        ASSERT_NE(nullptr, _mem_pool) << "内存池创建失败";

        _pool_map = new TestHashMap(_mem_pool, static_cast<uint32_t>(TestConstants::INITIAL_BUCKET_NUM));
        ASSERT_NE(nullptr, _pool_map) << "PoolHashMap创建失败";

        _std_map = new std::unordered_map<TestKey, TestValue>();
        ASSERT_NE(nullptr, _std_map) << "std::unordered_map创建失败";
    }

    void TearDown() override {
        if (_pool_map) {
            delete _pool_map;
            _pool_map = nullptr;
        }
        if (_std_map) {
            delete _std_map;
            _std_map = nullptr;
        }
        if (_mem_pool) {
            delete _mem_pool;
            _mem_pool = nullptr;
        }
    }

protected:
    GCSlabMempool32* _mem_pool;
    TestHashMap* _pool_map;
    std::unordered_map<TestKey, TestValue>* _std_map;
};

#endif  // POOL_HASH_MAP_TEST_BASE_H