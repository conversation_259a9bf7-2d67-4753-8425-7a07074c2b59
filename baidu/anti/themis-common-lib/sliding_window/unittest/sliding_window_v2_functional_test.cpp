// Copyright (c) 2025 Baidu.com, Inc. All Rights Reserved
// @Author: l<PERSON><PERSON><PERSON><PERSON>(<EMAIL>)

#include <fstream>

#include "sliding_window_v2_test_base.h"

/**
 * @brief SlidingWindowV2功能正确性测试类
 */
class SlidingWindowV2FunctionalTest : public SlidingWindowTestBase {
protected:
    void SetUp() override {
        SlidingWindowTestBase::SetUp();
    }

    void TearDown() override {
        SlidingWindowTestBase::TearDown();
    }
};

/**
 * @brief 基础操作等价性测试 (10K数据)
 */
TEST_F(SlidingWindowV2FunctionalTest, test_basic_operations_10k) {
    auto test_data = generate_test_data(10000);

    size_t pool_success = 0, orig_success = 0;
    size_t operation_failures = 0;

    for (size_t i = 0; i < test_data.size(); ++i) {
        const auto& data = test_data[i];
        bool pool_result = _pool_sw->enter(data.key, data.coord, data.item);
        SegmentItem orig_item(data.item.cumulant());
        bool orig_result = _orig_sw->enter(data.key, data.coord, orig_item);

        if (pool_result)
            pool_success++;
        if (orig_result)
            orig_success++;

        if (pool_result != orig_result) {
            operation_failures++;
            ADD_FAILURE() << "操作结果不一致: key=" << data.key;
        }
    }

    EXPECT_EQ(pool_success, orig_success);
    EXPECT_EQ(operation_failures, 0);

    auto test_keys = extract_unique_keys(test_data);
    bool query_ok = verify_query_consistency(*_pool_sw, *_orig_sw, test_keys);
    EXPECT_TRUE(query_ok);

    EXPECT_EQ(_pool_sw->step_length(), _orig_sw->step_length());
    EXPECT_EQ(_pool_sw->step_num(), _orig_sw->step_num());
}

/**
 * @brief 跨版本checkpoint兼容性测试 (10K数据)
 */
TEST_F(SlidingWindowV2FunctionalTest, test_cross_version_checkpoint_10k) {
    auto test_data = generate_test_data(10000);

    bool compatibility_ok = verify_cross_version_compatibility(test_data);
    EXPECT_TRUE(compatibility_ok);
}

/**
 * @brief 序列化功能完整性测试 (10K数据)
 */
TEST_F(SlidingWindowV2FunctionalTest, test_serialization_integrity_10k) {
    auto test_data = generate_test_data(10000);
    auto test_keys = extract_unique_keys(test_data);

    for (const auto& data : test_data) {
        _pool_sw->enter(data.key, data.coord, data.item);
    }

    std::string checkpoint_path = "./test_integrity.cpt";
    bool serialize_ok = create_checkpoint(*_pool_sw, checkpoint_path);
    ASSERT_TRUE(serialize_ok);

    TestSlidingWindow pool_loaded;
    bool load_ok = load_from_checkpoint(
            pool_loaded,
            checkpoint_path,
            static_cast<int64_t>(TestConstants::DEFAULT_STEP_LEN),
            static_cast<int>(TestConstants::DEFAULT_MAX_STEP_NUM),
            static_cast<int64_t>(TestConstants::DEFAULT_SEGMENT_LEN));
    ASSERT_TRUE(load_ok);

    bool integrity_ok = verify_query_consistency(pool_loaded, *_pool_sw, test_keys);
    EXPECT_TRUE(integrity_ok);

    pool_loaded.uninit();
    system("rm -f ./test_integrity.cpt");
}

/**
 * @brief 边界情况处理测试
 */
TEST_F(SlidingWindowV2FunctionalTest, test_edge_cases) {
    auto test_data = generate_test_data(5000);

    for (const auto& data : test_data) {
        _pool_sw->enter(data.key, data.coord, data.item);
        SegmentItem orig_item(data.item.cumulant());
        _orig_sw->enter(data.key, data.coord, orig_item);
    }

    // 测试重复坐标插入
    uint64_t test_key = test_data[test_data.size() / 2].key;
    int64_t duplicate_coord = test_data[test_data.size() / 2].coord;
    SegmentItemV2 new_item(999);

    bool pool_dup = _pool_sw->enter(test_key, duplicate_coord, new_item);
    SegmentItem orig_dup_item(new_item.cumulant());
    bool orig_dup = _orig_sw->enter(test_key, duplicate_coord, orig_dup_item);
    EXPECT_EQ(pool_dup, orig_dup);

    // 测试不存在的key查询
    uint64_t nonexistent_key = 999999999ULL;

    const SegmentItemV2* pool_null = _pool_sw->query_segment(nonexistent_key);
    const SegmentItem* orig_null = _orig_sw->query_segment(nonexistent_key);
    EXPECT_EQ(pool_null == nullptr, orig_null == nullptr);

    // 测试空容器checkpoint兼容性
    std::vector<SlidingWindowTestData> empty_data;
    bool empty_ok = verify_cross_version_compatibility(empty_data);
    EXPECT_TRUE(empty_ok);

    // 测试单元素checkpoint兼容性
    std::vector<SlidingWindowTestData> single_data = {SlidingWindowTestData(1001, 1000000, 42)};
    bool single_ok = verify_cross_version_compatibility(single_data);
    EXPECT_TRUE(single_ok);
}

/**
 * @brief 序列化生命周期测试
 */
TEST_F(SlidingWindowV2FunctionalTest, test_serialization_lifecycle) {
    auto initial_data = generate_test_data(1000, 200);

    int64_t max_coord = 0;
    for (const auto& data : initial_data) {
        if (data.coord > max_coord)
            max_coord = data.coord;
    }

    auto additional_data = generate_test_data(500, 200);
    for (auto& data : additional_data) {
        data.coord += max_coord + 100;
    }

    bool initial_ok = verify_cross_version_compatibility(initial_data);
    EXPECT_TRUE(initial_ok);

    TestSlidingWindow pool_loaded;
    if (!pool_loaded.init(static_cast<int64_t>(TestConstants::DEFAULT_STEP_LEN), static_cast<int>(TestConstants::DEFAULT_MAX_STEP_NUM), static_cast<int64_t>(TestConstants::DEFAULT_SEGMENT_LEN))) {
        FAIL() << "Pool实例初始化失败";
    }

    for (const auto& data : initial_data) {
        pool_loaded.enter(data.key, data.coord, data.item);
    }

    if (!create_checkpoint(pool_loaded, "./lifecycle.cpt")) {
        pool_loaded.uninit();
        FAIL() << "序列化失败";
    }

    OriginalSlidingWindow orig_loaded;
    if (!load_from_checkpoint(
                orig_loaded,
                "./lifecycle.cpt",
                static_cast<int64_t>(TestConstants::DEFAULT_STEP_LEN),
                static_cast<int>(TestConstants::DEFAULT_MAX_STEP_NUM),
                static_cast<int64_t>(TestConstants::DEFAULT_SEGMENT_LEN))) {
        pool_loaded.uninit();
        system("rm -f ./lifecycle.cpt");
        FAIL() << "反序列化失败";
    }

    for (const auto& data : additional_data) {
        bool pool_result = pool_loaded.enter(data.key, data.coord, data.item);
        SegmentItem orig_item(data.item.cumulant());
        bool orig_result = orig_loaded.enter(data.key, data.coord, orig_item);
        EXPECT_EQ(pool_result, orig_result);
    }

    auto all_keys = extract_unique_keys(initial_data);
    auto additional_keys = extract_unique_keys(additional_data);
    all_keys.insert(all_keys.end(), additional_keys.begin(), additional_keys.end());
    std::sort(all_keys.begin(), all_keys.end());
    all_keys.erase(std::unique(all_keys.begin(), all_keys.end()), all_keys.end());

    bool final_ok = verify_query_consistency(pool_loaded, orig_loaded, all_keys);
    EXPECT_TRUE(final_ok);

    pool_loaded.uninit();
    orig_loaded.uninit();
    system("rm -f ./lifecycle.cpt");
}

/**
 * @brief 极端坐标跳跃测试
 * 验证巨大坐标跳跃时的滑动窗口清理逻辑
 */
TEST_F(SlidingWindowV2FunctionalTest, test_extreme_coordinate_jump) {
    // 插入初始数据
    auto initial_data = generate_test_data(100, 100);
    for (const auto& data : initial_data) {
        _pool_sw->enter(data.key, 1000 + data.coord, data.item);
        SegmentItem orig_item(data.item.cumulant());
        _orig_sw->enter(data.key, 1000 + data.coord, orig_item);
    }

    EXPECT_EQ(_pool_sw->segment_start_coord(), _orig_sw->segment_start_coord());

    // 巨大跳跃：从~5000跳到1000000000
    int64_t huge_coord = 1000000000L;
    SegmentItemV2 huge_item(999);

    bool pool_result = _pool_sw->enter(12345, huge_coord, huge_item);
    SegmentItem orig_huge_item(huge_item.cumulant());
    bool orig_result = _orig_sw->enter(12345, huge_coord, orig_huge_item);

    EXPECT_EQ(pool_result, orig_result);
    EXPECT_EQ(_pool_sw->segment_start_coord(), _orig_sw->segment_start_coord());
    EXPECT_EQ(_pool_sw->latest_coord(), _orig_sw->latest_coord());

    // 验证旧数据被正确清理（应该查不到了）
    for (const auto& data : initial_data) {
        const SegmentItemV2* pool_found = _pool_sw->query_segment(data.key);
        const SegmentItem* orig_found = _orig_sw->query_segment(data.key);
        EXPECT_EQ(pool_found == nullptr, orig_found == nullptr);
    }
}

/**
 * @brief 序列化魔数校验测试
 */
TEST_F(SlidingWindowV2FunctionalTest, test_serialization_magic_validation) {
    auto test_data = generate_test_data(100);
    for (const auto& data : test_data) {
        _pool_sw->enter(data.key, data.coord, data.item);
    }

    // 创建正常checkpoint
    std::string checkpoint_path = "./test_magic.cpt";
    bool serialize_ok = create_checkpoint(*_pool_sw, checkpoint_path);
    ASSERT_TRUE(serialize_ok);

    // 故意破坏魔数
    {
        std::fstream file(checkpoint_path, std::ios::in | std::ios::out | std::ios::binary);
        ASSERT_TRUE(file.is_open());

        uint64_t corrupted_magic = 0xDEADBEEF;
        file.write(reinterpret_cast<const char*>(&corrupted_magic), sizeof(corrupted_magic));
        file.close();
    }

    // 尝试加载损坏的checkpoint
    TestSlidingWindow corrupted_sw;
    bool load_ok = load_from_checkpoint(
            corrupted_sw,
            checkpoint_path,
            static_cast<int64_t>(TestConstants::DEFAULT_STEP_LEN),
            static_cast<int>(TestConstants::DEFAULT_MAX_STEP_NUM),
            static_cast<int64_t>(TestConstants::DEFAULT_SEGMENT_LEN));
    EXPECT_FALSE(load_ok);  // 应该加载失败

    corrupted_sw.uninit();
    system("rm -f ./test_magic.cpt");
}

/**
 * @brief 截断checkpoint文件处理测试
 */
TEST_F(SlidingWindowV2FunctionalTest, test_truncated_checkpoint_handling) {
    auto test_data = generate_test_data(100);
    for (const auto& data : test_data) {
        _pool_sw->enter(data.key, data.coord, data.item);
    }

    std::string checkpoint_path = "./test_truncated.cpt";
    bool serialize_ok = create_checkpoint(*_pool_sw, checkpoint_path);
    ASSERT_TRUE(serialize_ok);

    // 截断文件到一半大小
    {
        std::ifstream original(checkpoint_path, std::ios::binary | std::ios::ate);
        std::streamsize size = original.tellg();
        original.close();

        // 截断文件
        truncate(checkpoint_path.c_str(), size / 2);
    }

    // 尝试加载截断的checkpoint
    TestSlidingWindow truncated_sw;
    bool load_ok = load_from_checkpoint(
            truncated_sw,
            checkpoint_path,
            static_cast<int64_t>(TestConstants::DEFAULT_STEP_LEN),
            static_cast<int>(TestConstants::DEFAULT_MAX_STEP_NUM),
            static_cast<int64_t>(TestConstants::DEFAULT_SEGMENT_LEN));
    EXPECT_FALSE(load_ok);  // 应该加载失败

    truncated_sw.uninit();
    system("rm -f ./test_truncated.cpt");
}

/**
 * @brief sign_start_coord功能测试
 */
TEST_F(SlidingWindowV2FunctionalTest, test_sign_start_coord_functionality) {
    _pool_sw->set_log_key_first_appear();

    uint64_t test_key = 12345;
    int64_t first_coord = 1000000;
    int64_t second_coord = 1000500;

    // 第一次出现
    SegmentItemV2 item1(10);
    bool enter1 = _pool_sw->enter(test_key, first_coord, item1);
    ASSERT_TRUE(enter1);

    int64_t start_coord1 = _pool_sw->query_start_coord(test_key);
    EXPECT_GE(start_coord1, 0);

    // 第二次出现（在同一segment内）
    SegmentItemV2 item2(20);
    bool enter2 = _pool_sw->enter(test_key, second_coord, item2);
    ASSERT_TRUE(enter2);

    int64_t start_coord2 = _pool_sw->query_start_coord(test_key);
    EXPECT_EQ(start_coord1, start_coord2);  // 应该记录的还是第一次出现的坐标

    // 测试不存在的key
    int64_t nonexistent_start = _pool_sw->query_start_coord(99999);
    EXPECT_EQ(nonexistent_start, _pool_sw->segment_start_coord());
}