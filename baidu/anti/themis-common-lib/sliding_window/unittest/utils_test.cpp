/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * $Id$ 
 * 
 **************************************************************************/
 
/**
 * @file sliding_window/unittest/utils_test.cpp
 * <AUTHOR>
 * @date 2015/04/12 20:32:54
 * @version $Revision$ 
 * @brief 
 *  
 **/

#include <gtest/gtest.h>
#include "item.h"
#include "utils.h"

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}

class UtilsTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
    }

    virtual void TearDown() {
    }
};

using anti::themis::common_lib::SegmentItem;
using anti::themis::common_lib::sub;
typedef std::unordered_map<int, SegmentItem> Utmap;

TEST_F(UtilsTestSuite, case_1) {
    Utmap m1;
    Utmap m2;
    SegmentItem si = 1;
    m1[1] = si;
    m2[1] = si;
    si = 2;
    m1[2] = si;

    sub<int, SegmentItem>(&m1, &m2);
    EXPECT_TRUE(m1.find(1) == m1.end());
    EXPECT_EQ(m1.find(2)->second.cumulant(), 2);
}

/* vim: set ts=4 sw=4 sts=4 tw=100 */
