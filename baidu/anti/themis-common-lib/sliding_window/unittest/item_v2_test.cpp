/***************************************************************************
 * 
 * Copyright (c) 2025 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/

/**
 * @file sliding_window/unittest/item_v2_test.cpp
 * <AUTHOR> (<EMAIL>)
 * @date 2025/07/30
 * @brief DistinctItemV2 单元测试
 *  
 **/

#include <gtest/gtest.h>
#include <bmock.h>

#include "archive.h"
#include "item_v2.h"
#include "gc_slab_mempool_32.h"

using anti::themis::common_lib::DistinctItemV2;
using anti::themis::common_lib::GCSlabMempool32;

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}

class ItemV2TestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
        // 创建内存池用于测试
        std::vector<uint32_t> slab_sizes;
        DistinctItemV2::append_required_slab_sizes(slab_sizes);
        
        ASSERT_EQ(0, _mem_pool.create(slab_sizes.data(), slab_sizes.size(), 1000));
    }

    virtual void TearDown() {
        // 内存池会自动清理
    }

protected:
    GCSlabMempool32 _mem_pool;
};

TEST_F(ItemV2TestSuite, test_construction) {
    // 测试默认构造函数
    DistinctItemV2 item1;
    EXPECT_TRUE(item1.is_null());
    EXPECT_EQ(0, item1.distinct_num());
    EXPECT_EQ(0, item1.total());
    
    // 使用构造函数创建带内存池的对象  
    DistinctItemV2 item_with_pool(0, 0, &_mem_pool);
    EXPECT_TRUE(item_with_pool.is_null());
    EXPECT_EQ(0, item_with_pool.distinct_num());
    EXPECT_EQ(0, item_with_pool.total());
    
    // 测试带参数的构造函数
    DistinctItemV2 item2(123456, 1, &_mem_pool);
    EXPECT_FALSE(item2.is_null());
    EXPECT_EQ(1, item2.distinct_num());
    EXPECT_EQ(1, item2.total());
    
    // 测试data_view_sign为0的构造
    DistinctItemV2 item3(0, 5, &_mem_pool);
    EXPECT_FALSE(item3.is_null());
    EXPECT_EQ(0, item3.distinct_num());  // 不添加键为0的条目
    EXPECT_EQ(5, item3.total());
}

TEST_F(ItemV2TestSuite, test_copy_operations) {
    DistinctItemV2 original(123456, 1, &_mem_pool);
    
    // 测试拷贝构造函数
    DistinctItemV2 copied(original);
    EXPECT_EQ(original.distinct_num(), copied.distinct_num());
    EXPECT_EQ(original.total(), copied.total());
    
    // 测试拷贝赋值运算符
    DistinctItemV2 assigned(0, 0, &_mem_pool);
    assigned = original;
    EXPECT_EQ(original.distinct_num(), assigned.distinct_num());
    EXPECT_EQ(original.total(), assigned.total());
}

TEST_F(ItemV2TestSuite, test_distinct_item_basic_operations) {
    DistinctItemV2 di(123456, 1, &_mem_pool);
    DistinctItemV2 di1(123456, 1, &_mem_pool);
    DistinctItemV2 di2(123456, 2, &_mem_pool);
    DistinctItemV2 di3(123457, 3, &_mem_pool);

    EXPECT_EQ(di.distinct_num(), 1);
    EXPECT_EQ(di2.distinct_num(), 1);
    EXPECT_EQ(di3.distinct_num(), 1);
    
    // 测试相同key的累加
    for (int64_t i = 0L; i < 10; ++i) {
        di += di2;
        EXPECT_EQ(di.distinct_num(), 1);
        EXPECT_EQ(di.total(), 1 + i * 2 + 2);
    }
    
    // 测试不同key的累加
    for (int64_t i = 0L; i < 10; ++i) {
        di += di3;
        EXPECT_EQ(di.distinct_num(), 2);
        EXPECT_EQ(di.total(), 21 + i * 3 + 3);
    }
    
    // 记录当前状态用于后续测试
    EXPECT_EQ(di.distinct_num(), 2);
    EXPECT_EQ(di.total(), 51);
}

TEST_F(ItemV2TestSuite, test_distinct_item_subtraction) {
    DistinctItemV2 di(123456, 1, &_mem_pool);
    DistinctItemV2 di2(123456, 2, &_mem_pool);
    DistinctItemV2 di3(123457, 3, &_mem_pool);
    
    // 先构建一个复杂的item
    for (int64_t i = 0L; i < 10; ++i) {
        di += di2;
    }
    for (int64_t i = 0L; i < 10; ++i) {
        di += di3;
    }
    
    EXPECT_EQ(di.distinct_num(), 2);
    EXPECT_EQ(di.total(), 51);
    
    // 测试减法操作
    for (int64_t i = 0L; i < 10; ++i) {
        di -= di2;
        EXPECT_EQ(di.distinct_num(), 2);
        EXPECT_EQ(di.total(), 51 - i * 2 - 2);
    }
    
    for (int64_t i = 0L; i < 10; ++i) {
        di -= di3;
        if (i == 9L) {
            EXPECT_EQ(di.distinct_num(), 1);  // di3的key被完全移除
        } else {
            EXPECT_EQ(di.distinct_num(), 2);
        }
        EXPECT_EQ(di.total(), 31 - i * 3 - 3);
    }
    
    // 最终应该只剩一个key
    EXPECT_EQ(di.distinct_num(), 1);
    EXPECT_EQ(di.total(), 1);
    
    // 继续减法直到为空
    DistinctItemV2 di1(123456, 1, &_mem_pool);
    di -= di1;
    EXPECT_EQ(di.distinct_num(), 0);
    EXPECT_EQ(di.total(), 0);
    EXPECT_TRUE(di.is_null());
}

// Mock Archive for testing serialization
class ArchiveDemo {
public:
    virtual int read(void*, size_t) {
        return 0;
    }
    virtual int write(const void*, size_t) {
        return 0;
    }
};

BMOCK_CLASS_METHOD2(ArchiveDemo, read, int(void*, size_t));
BMOCK_CLASS_METHOD2(ArchiveDemo, write, int(const void*, size_t));
using ::testing::Return;
using ::testing::_;

TEST_F(ItemV2TestSuite, test_serialize_with_mock) {
    DistinctItemV2 di(123456, 5, &_mem_pool);
    ArchiveDemo ar;
    
    // 期望的写入调用：
    // 1. _total (8 bytes)
    // 2. map_size (8 bytes for size_t)  
    // 3. key (8 bytes)
    // 4. value (8 bytes)
    // 总共: 8 + 8 + 8 + 8 = 32 bytes
    EXPECT_CALL(BMOCK_CLASS_OBJECT(ArchiveDemo, write), write(_, 8))
            .Times(4)  // _total, map_size, key, value
            .WillRepeatedly(Return(8));
    
    EXPECT_TRUE(di.serialize<ArchiveDemo>(&ar));
    
    // 测试失败情况
    EXPECT_CALL(BMOCK_CLASS_OBJECT(ArchiveDemo, write), write(_, 8))
            .WillOnce(Return(0));  // 第一次写入失败
    EXPECT_FALSE(di.serialize<ArchiveDemo>(&ar));
}

TEST_F(ItemV2TestSuite, test_deserialize_with_mock) {
    DistinctItemV2 di;
    ArchiveDemo ar;
    
    // 期望的读取调用：与写入对应
    EXPECT_CALL(BMOCK_CLASS_OBJECT(ArchiveDemo, read), read(_, 8))
            .Times(4)  // _total, map_size, key, value
            .WillRepeatedly(Return(8));
    
    EXPECT_TRUE(di.deserialize<ArchiveDemo>(&ar, &_mem_pool));
    
    // 测试失败情况 - 内存池为null
    DistinctItemV2 di2;
    EXPECT_FALSE(di2.deserialize<ArchiveDemo>(&ar, nullptr));
    
    // 测试读取失败
    DistinctItemV2 di3;
    EXPECT_CALL(BMOCK_CLASS_OBJECT(ArchiveDemo, read), read(_, 8))
            .WillOnce(Return(0));  // 第一次读取失败
    EXPECT_FALSE(di3.deserialize<ArchiveDemo>(&ar, &_mem_pool));
}

TEST_F(ItemV2TestSuite, test_real_serialization) {
    // 创建一个复杂的DistinctItemV2
    DistinctItemV2 original(123456, 1, &_mem_pool);
    DistinctItemV2 item2(123456, 1, &_mem_pool);
    DistinctItemV2 item3(123457, 3, &_mem_pool);
    DistinctItemV2 item4(123458, 2, &_mem_pool);
    
    original += item2;  // 同key累加
    original += item3;  // 不同key
    original += item4;  // 另一个不同key
    
    EXPECT_EQ(original.distinct_num(), 3);
    EXPECT_EQ(original.total(), 7);  // 1+1+3+2
    
    // 序列化到文件
    using anti::themis::common_lib::FileArchive;
    FileArchive ar;
    const char* cpt_file = "./distinct_item_v2.cpt";
    system("rm -f ./distinct_item_v2.cpt");
    ASSERT_TRUE(ar.open_w(cpt_file));
    
    ASSERT_TRUE(original.serialize<FileArchive>(&ar));
    ar.close();
    
    // 从文件反序列化
    DistinctItemV2 deserialized;
    
    FileArchive ar2;
    ASSERT_TRUE(ar2.open_r(cpt_file));
    
    ASSERT_TRUE(deserialized.deserialize<FileArchive>(&ar2, &_mem_pool));
    ar2.close();
    
    // 验证反序列化结果
    EXPECT_EQ(deserialized.distinct_num(), 3);
    EXPECT_EQ(deserialized.total(), 7);
    
    // 清理文件
    system("rm -f ./distinct_item_v2.cpt");
}

TEST_F(ItemV2TestSuite, test_memory_pool_integration) {
    // 测试大量操作确保内存池正常工作
    std::vector<DistinctItemV2> items;
    
    for (int i = 0; i < 100; ++i) {
        items.emplace_back(100000 + i, i + 1, &_mem_pool);
    }
    
    // 累加所有items
    DistinctItemV2 result(0, 0, &_mem_pool);
    for (const auto& item : items) {
        result += item;
    }
    
    EXPECT_EQ(result.distinct_num(), 100);
    EXPECT_EQ(result.total(), 100 * 101 / 2);  // 1+2+...+100 = 5050
    
    // 测试减法
    for (size_t i = 0; i < items.size() / 2; ++i) {
        result -= items[i];
    }
    
    EXPECT_EQ(result.distinct_num(), 50);
    EXPECT_EQ(result.total(), 5050 - (50 * 51 / 2));  // 剩余51-100的和
}

TEST_F(ItemV2TestSuite, test_edge_cases) {
    // 测试空item的操作
    DistinctItemV2 empty1(0, 0, &_mem_pool);
    DistinctItemV2 empty2(0, 0, &_mem_pool);
    
    EXPECT_TRUE(empty1.is_null());
    EXPECT_TRUE(empty2.is_null());
    
    empty1 += empty2;
    EXPECT_TRUE(empty1.is_null());
    EXPECT_EQ(empty1.distinct_num(), 0);
    EXPECT_EQ(empty1.total(), 0);
    
    // 测试自我累加
    DistinctItemV2 self_item(123456, 5, &_mem_pool);
    self_item += self_item;
    EXPECT_EQ(self_item.distinct_num(), 1);
    EXPECT_EQ(self_item.total(), 10);
    
    // 测试负数减法
    DistinctItemV2 item1(123456, 3, &_mem_pool);
    DistinctItemV2 item2(123456, 5, &_mem_pool);
    
    item1 -= item2;  // 3 - 5 = -2, 应该移除这个key
    EXPECT_EQ(item1.distinct_num(), 0);
    EXPECT_EQ(item1.total(), -2);
    EXPECT_TRUE(item1.is_null());
}

/* vim: set ts=4 sw=4 sts=4 tw=100 */