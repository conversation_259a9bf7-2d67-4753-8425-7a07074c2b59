// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: auto_click_segment_test.cpp
// @Last modified: 2015-11-17 16:36:28
// @Brief: 

#include <gtest/gtest.h>
#include "click_node.hpp"
#include "auto_click_segment.hpp"
#include "archive.h"

namespace anti {
namespace themis {
namespace common_lib {

class AutoClickSegmentTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
        
    }
    virtual void TearDown() {

    }
private:
    AutoClickSegment<SegLimitNode> _seg;
};

TEST_F(AutoClickSegmentTestSuite, SegmentInfo_case) {
    SegmentInfo info1;
    EXPECT_EQ(0L, info1.segment_len);
    EXPECT_EQ(0L, info1.segment_num);
    EXPECT_EQ(0L, info1.upper);
    EXPECT_EQ(0L, info1.node_num);
    EXPECT_DOUBLE_EQ(0.0, info1.lamda);
    EXPECT_EQ(0xC29FA627495AFA51, info1.magic);

    SegmentInfo info2(100L, 10L, 200L, 0.5);
    EXPECT_EQ(100L, info2.segment_len);
    EXPECT_EQ(10L, info2.segment_num);
    EXPECT_EQ(200L, info2.upper);
    EXPECT_EQ(0L, info2.node_num);
    EXPECT_DOUBLE_EQ(0.5, info2.lamda);
    EXPECT_EQ(0xC29FA627495AFA51, info2.magic);
    
    SegmentInfo info3(info2);
    EXPECT_EQ(100L, info3.segment_len);
    EXPECT_EQ(10L, info3.segment_num);
    EXPECT_EQ(200L, info3.upper);
    EXPECT_EQ(0L, info3.node_num);
    EXPECT_DOUBLE_EQ(0.5, info3.lamda);
    EXPECT_EQ(0xC29FA627495AFA51, info3.magic);

    SegmentInfo info4;
    info4 = info3;
    EXPECT_EQ(100L, info4.segment_len);
    EXPECT_EQ(10L, info4.segment_num);
    EXPECT_EQ(200L, info4.upper);
    EXPECT_EQ(0L, info4.node_num);
    EXPECT_DOUBLE_EQ(0.5, info4.lamda);
    EXPECT_EQ(0xC29FA627495AFA51, info4.magic);
}

TEST_F(AutoClickSegmentTestSuite, SegLimitNodeCase_init) {
    // init fail
    SegmentInfo invalid_info;
    ASSERT_FALSE(_seg.init(invalid_info));
    // init succ
    SegmentInfo seg_info(100L, 10L, 200L, 0.1);
    ASSERT_TRUE(_seg.init(seg_info));
    EXPECT_EQ(100L, _seg._info.segment_len);
    EXPECT_EQ(10L, _seg._info.segment_num);
    EXPECT_EQ(200L, _seg._info.upper);
    EXPECT_DOUBLE_EQ(0.1, _seg._info.lamda);
    EXPECT_EQ(0L, _seg._info.node_num);
    EXPECT_EQ(0xC29FA627495AFA51, _seg._info.magic);
}

TEST_F(AutoClickSegmentTestSuite, get_SegmentInfo_case) {
    int64_t upper =  1500L;
    double lamda = 0.1;
    int64_t segment_len = 10L; 
    int64_t segment_num = 3;
    SegmentInfo seg_info(segment_len, segment_num, upper, lamda);
    ASSERT_TRUE(_seg.init(seg_info)); 

    const SegmentInfo& info = _seg.segment_info();
    EXPECT_EQ(info.upper, _seg._info.upper);
    EXPECT_EQ(info.segment_len, _seg._info.segment_len);
    EXPECT_EQ(info.segment_num, _seg._info.segment_num);
    EXPECT_DOUBLE_EQ(info.lamda, _seg._info.lamda);
    EXPECT_EQ(info.magic, _seg._info.magic);
}

TEST_F(AutoClickSegmentTestSuite, serialize_case) {
    int64_t upper =  1500L;
    double lamda = 0.1;
    int64_t segment_len = 10L; 
    int64_t segment_num = 3;
    SegmentInfo seg_info(segment_len, segment_num, upper, lamda);
    ASSERT_TRUE(_seg.init(seg_info));
    SegLimitNode node;
    node.cumulant = 1;
    for (int i = 0; i < 1000; i++) {
        node.sign = i % 23;
        node.coord = i;
        ASSERT_TRUE(_seg.enter(node, NULL));
    }
    int64_t node_num  = _seg._info.node_num; 
    const char* file = "tmpfile_autoclickseg.ckpt";
    std::string cmd = "rm -f ";
    cmd += file;
    system(cmd.c_str());

    // dump
    FileArchive ar;
    ASSERT_TRUE(ar.open_w(file));
    ASSERT_TRUE(_seg.serialize<FileArchive>(&ar));
    ar.close();

    // load
    // segment_num NOT match
    FileArchive ar1;
    ASSERT_TRUE(ar1.open_r(file));
    AutoClickSegment<SegLimitNode> seg;
    seg_info.segment_len = 10L;
    seg_info.segment_num = 5L; 
    ASSERT_TRUE(seg.init(seg_info));
    EXPECT_FALSE(seg.deserialize<FileArchive>(&ar1));
    seg.uninit();
    ar1.close();

    // segment_len NOT match
    FileArchive ar2;
    ASSERT_TRUE(ar2.open_r(file));
    seg_info.segment_len = 20L;
    seg_info.segment_num = 3L;
    ASSERT_TRUE(seg.init(seg_info));
    EXPECT_FALSE(seg.deserialize<FileArchive>(&ar2));
    seg.uninit();
    ar2.close();

    ASSERT_TRUE(ar2.open_r(file));
    seg_info.segment_len = 10L;
    ASSERT_TRUE(seg.init(seg_info));
    EXPECT_TRUE(seg.deserialize<FileArchive>(&ar2));
    EXPECT_EQ(segment_len, seg._info.segment_len);
    EXPECT_EQ(segment_num, seg._info.segment_num);
    EXPECT_EQ(upper, seg._info.upper);
    EXPECT_DOUBLE_EQ(lamda, seg._info.lamda);
    EXPECT_EQ(node_num, seg._info.node_num);
    ar2.close();

    system(cmd.c_str());
}

TEST_F(AutoClickSegmentTestSuite, update_node_fail_case) {
    int64_t upper =  1500L;
    double lamda = 0.1;
    int64_t segment_len = 10L; 
    int64_t segment_num = 3;

    SegmentInfo seg_info(segment_len, segment_num, upper, lamda);
    ASSERT_TRUE(_seg.init(seg_info)); 

    SegLimitNode node;
    EXPECT_FALSE(_seg._update_node(NULL, node));
    _seg.uninit();
}

TEST_F(AutoClickSegmentTestSuite, serialize_and_deserialize_fail_case) {
    // state not OK
    FileArchive * ar_invalid = NULL;
    EXPECT_FALSE(_seg.serialize(ar_invalid));
    EXPECT_FALSE(_seg.deserialize(ar_invalid));
    // ar invalid
    int64_t upper =  1500L;
    double lamda = 0.1;
    int64_t segment_len = 10L; 
    int64_t segment_num = 3;
    SegmentInfo seg_info(segment_len, segment_num, upper, lamda);
    ASSERT_TRUE(_seg.init(seg_info)); 
    EXPECT_FALSE(_seg.serialize(ar_invalid));
    EXPECT_FALSE(_seg.deserialize(ar_invalid));
    // ar Not NULL but Not Open
    FileArchive ar;
    EXPECT_FALSE(_seg.serialize(&ar));
    EXPECT_FALSE(_seg.deserialize(&ar));
}

TEST_F(AutoClickSegmentTestSuite, SegBlackNodeCase_add_with_seg_info) {
    SegBlackNode sbn1;
    SegBlackNode sbn2;
    sbn1.cumulant = 1L;
    sbn2.cumulant = 1L;

    SegmentInfo info;
    info.segment_len = 10L;
    info.segment_num = 4L;
    info.upper = 1500L;
    info.lamda = 0.5;

    // test sign NOT same
    sbn1.coord = 1;
    sbn2.coord = 2;
    sbn1.sign = 0x1111;
    sbn2.sign = 0x2222;
    sbn1.last_cumulant = 1000.0;
    sbn1.add(sbn2, info);
    EXPECT_EQ(1L, sbn1.coord);
    EXPECT_DOUBLE_EQ(1000.0, sbn1.last_cumulant);
    EXPECT_EQ(1L, sbn1.cumulant);

    // test in 1 segment
    sbn1.coord = 1;
    sbn2.coord = 2;
    sbn1.sign = sbn2.sign;
    sbn1.last_cumulant = 1000.0;
    // invalid segment_len
    SegmentInfo info_invalid;
    sbn1.add(sbn2, info_invalid);
    EXPECT_EQ(1L, sbn1.coord);
    EXPECT_DOUBLE_EQ(1000.0, sbn1.last_cumulant);
    EXPECT_EQ(1L, sbn1.cumulant);
    // valid segment_len
    sbn1.add(sbn2, info);
    EXPECT_EQ(1L, sbn1.coord);
    EXPECT_DOUBLE_EQ(1000.0, sbn1.last_cumulant);
    EXPECT_EQ(2L, sbn1.cumulant);
    
    // test jump 1 segment
    sbn2.coord = 12L;
    sbn1.last_cumulant = 1000.0;
    sbn1.cumulant = 100;
    sbn1.add(sbn2, info);
    EXPECT_EQ(12L, sbn1.coord);
    EXPECT_DOUBLE_EQ(300.0, sbn1.last_cumulant);
    EXPECT_EQ(1L, sbn1.cumulant);

    // test jump 2 segment
    sbn1.coord = 1L;
    sbn2.coord = 22L;
    sbn1.last_cumulant = 1000.0;
    sbn1.cumulant = 100;
    sbn1.add(sbn2, info);
    EXPECT_EQ(22L, sbn1.coord);
    EXPECT_DOUBLE_EQ(150.0, sbn1.last_cumulant);
    EXPECT_EQ(1L, sbn1.cumulant);

    // test in 4 segment
    sbn1.coord = 1L;
    sbn2.coord = 47L;
    sbn1.last_cumulant = 1000.0;
    sbn1.add(sbn2, info);
    EXPECT_EQ(47L, sbn1.coord);
    EXPECT_DOUBLE_EQ(0.0, sbn1.last_cumulant);
    EXPECT_EQ(1L, sbn1.cumulant);

    // test cross 7 segment
    sbn1.coord = 1L;
    sbn2.coord = 79L;
    sbn1.last_cumulant = 1000L;
    sbn1.add(sbn2, info);
    EXPECT_EQ(79L, sbn1.coord);
    EXPECT_DOUBLE_EQ(0.0, sbn1.last_cumulant);
    EXPECT_EQ(1L, sbn1.cumulant);

    // test upper
    sbn1.coord = 1L;
    sbn2.coord = 12L;
    sbn1.cumulant = 1L;
    sbn2.cumulant = 1L;
    sbn1.last_cumulant = 20000L;
    sbn1.add(sbn2, info);
    EXPECT_EQ(12L, sbn1.coord);
    EXPECT_DOUBLE_EQ(750.0, sbn1.last_cumulant);
    EXPECT_EQ(1L, sbn1.cumulant);
}

TEST_F(AutoClickSegmentTestSuite, SegBlackNodeCase_enter) {
    SegBlackNode sbn;
    sbn.sign = 0x1100;
    sbn.cumulant = 1L;
    sbn.last_cumulant = 0.0;
    sbn.coord = 1L;
    int64_t segment_len = 10L;
    int64_t segment_num = 3;
    int64_t upper =  1500L;
    double lamda = 0.5;

    SegmentInfo seg_info(segment_len, segment_num, upper, lamda);
    AutoClickSegment<SegBlackNode> seg_black;
    const SegBlackNode* res = NULL;
    ASSERT_TRUE(seg_black.init(seg_info));
    for (int64_t i = 0; i < segment_len; i++) {
        sbn.coord = i;
        ASSERT_TRUE(seg_black.enter(sbn, &res));
        EXPECT_EQ(res->cumulant, i + 1);
    }

    const SegBlackNode* r1 = seg_black.query_segment(sbn.sign);
    EXPECT_EQ(segment_len, r1->cumulant);
    EXPECT_DOUBLE_EQ(0.0, r1->last_cumulant);
    EXPECT_EQ(0L, r1->coord);

    SegBlackNode sbn2;
    sbn2.sign = 0x2200;
    sbn2.sign = 100;
    sbn2.cumulant = 1;
    ASSERT_TRUE(seg_black.enter(sbn2, &res));
    EXPECT_EQ(1L, res->cumulant);
    EXPECT_DOUBLE_EQ(0.0, res->last_cumulant);
    EXPECT_EQ(0L, res->coord);

    // jump one segment
    SegBlackNode  sbn3;
    sbn3.sign = sbn.sign;
    sbn3.cumulant = 1;
    sbn3.coord = 15; 
    ASSERT_TRUE(seg_black.enter(sbn3, &res));
    EXPECT_DOUBLE_EQ(5.0, res->last_cumulant);
    EXPECT_EQ(15L, res->coord);
    EXPECT_EQ(1L, res->cumulant);

    sbn3.coord = 26;
    ASSERT_TRUE(seg_black.enter(sbn3, &res));
    EXPECT_DOUBLE_EQ(1.75, res->last_cumulant);
    EXPECT_EQ(1L, res->cumulant);
    EXPECT_EQ(26, res->coord);

    sbn3.coord = 56;
    ASSERT_TRUE(seg_black.enter(sbn3, &res));
    EXPECT_DOUBLE_EQ(0.0, res->last_cumulant);
    EXPECT_EQ(1L, res->cumulant);
    EXPECT_EQ(56L, res->coord);
}
}  // end namespace common_lib
}  // end namespace themis
}  // end namespace anti

/* vim: set ts=4 sw=4: */

