// Copyright (c) 2025 Baidu.com, Inc. All Rights Reserved
// @Author: l<PERSON><PERSON><PERSON><PERSON>(<EMAIL>)

#include "pool_hash_map_test_base.h"

/**
 * @brief PoolHashMap功能正确性测试类
 * 验证PoolHashMap与std::unordered_map的功能等价
 */
class PoolHashMapFunctionalTest : public PoolHashMapTestBase {
protected:
    void SetUp() override {
        PoolHashMapTestBase::SetUp();
    }

    void TearDown() override {
        PoolHashMapTestBase::TearDown();
    }
};

/**
 * @brief 基础操作等价性测试 (10K数据)
 * 验证PoolHashMap与std::unordered_map在插入、查找、删除操作上的行为等价
 */
TEST_F(PoolHashMapFunctionalTest, test_basic_operations_10k) {
    auto test_data = generate_test_data(10000);

    // 阶段1: 插入操作一致性测试
    size_t pool_insert_success = 0;
    size_t std_insert_success = 0;

    for (const auto& item : test_data) {
        auto pool_result = _pool_map->emplace(item.first, item.second);
        auto std_result = _std_map->emplace(item.first, item.second);

        bool pool_success = (pool_result.first != nullptr);
        bool std_success = std_result.second;

        EXPECT_EQ(pool_success, std_success) << "插入结果不一致, key=" << item.first;

        if (pool_success)
            pool_insert_success++;
        if (std_success)
            std_insert_success++;
    }

    EXPECT_EQ(pool_insert_success, std_insert_success);
    EXPECT_EQ(_pool_map->size(), _std_map->size());

    // 阶段2: 查找操作一致性测试
    bool query_ok = verify_query_consistency(_pool_map, _std_map, test_data);
    EXPECT_TRUE(query_ok);

    // 阶段3: 删除操作一致性测试 (删除50%数据)
    size_t pool_remove_success = 0;
    size_t std_remove_success = 0;

    for (size_t i = 0; i < test_data.size(); i += 2) {
        TestKey key = test_data[i].first;

        bool pool_removed = _pool_map->remove(key);
        size_t std_removed_count = _std_map->erase(key);
        bool std_removed = (std_removed_count > 0);

        EXPECT_EQ(pool_removed, std_removed);

        if (pool_removed)
            pool_remove_success++;
        if (std_removed)
            std_remove_success++;
    }

    EXPECT_EQ(pool_remove_success, std_remove_success);
    EXPECT_EQ(_pool_map->size(), _std_map->size());
}

/**
 * @brief 边界情况处理测试
 * 测试空值、重复key、不存在的key等边界情况
 */
TEST_F(PoolHashMapFunctionalTest, test_edge_cases) {
    auto test_data = generate_test_data(5000);

    // 填充基础数据
    for (const auto& item : test_data) {
        _pool_map->emplace(item.first, item.second);
        _std_map->emplace(item.first, item.second);
    }

    // 测试1: 重复ke'y插入行为
    TestKey duplicate_key = test_data[0].first;
    TestValue new_value = test_data[0].second + 999;

    auto pool_dup_result = _pool_map->emplace(duplicate_key, new_value);
    auto std_dup_result = _std_map->emplace(duplicate_key, new_value);

    EXPECT_FALSE(pool_dup_result.second);
    EXPECT_FALSE(std_dup_result.second);
    EXPECT_EQ(*(pool_dup_result.first), test_data[0].second);
    EXPECT_EQ(std_dup_result.first->second, test_data[0].second);

    // 测试2: 查找不存在的key
    TestKey non_existent_key = 999999999;
    TestValue* pool_not_found = _pool_map->find(non_existent_key);
    auto std_not_found = _std_map->find(non_existent_key);

    EXPECT_EQ(nullptr, pool_not_found);
    EXPECT_EQ(_std_map->end(), std_not_found);

    // 测试3: 删除不存在的key
    bool pool_remove_non_existent = _pool_map->remove(non_existent_key);
    size_t std_remove_non_existent = _std_map->erase(non_existent_key);

    EXPECT_FALSE(pool_remove_non_existent);
    EXPECT_EQ(0U, std_remove_non_existent);

    // 测试4: 空容器操作
    _pool_map->clear();
    _std_map->clear();

    EXPECT_EQ(0U, _pool_map->size());
    EXPECT_EQ(0U, _std_map->size());

    TestValue* empty_find = _pool_map->find(test_data[0].first);
    auto empty_std_find = _std_map->find(test_data[0].first);

    EXPECT_EQ(nullptr, empty_find);
    EXPECT_EQ(_std_map->end(), empty_std_find);
}

/**
 * @brief 综合生命周期测试 (10K数据)
 * 完整的insert->find->delete->clear 生命周期测试
 */
TEST_F(PoolHashMapFunctionalTest, test_comprehensive_lifecycle) {
    auto test_data = generate_test_data(10000);

    // 步骤1: 批量插入
    for (const auto& item : test_data) {
        auto pool_result = _pool_map->emplace(item.first, item.second);
        auto std_result = _std_map->emplace(item.first, item.second);

        ASSERT_NE(nullptr, pool_result.first);
        ASSERT_TRUE(std_result.second);
    }

    EXPECT_EQ(_pool_map->size(), _std_map->size());

    // 步骤2: 全量查找验证
    bool find_ok = verify_query_consistency(_pool_map, _std_map, test_data);
    EXPECT_TRUE(find_ok);

    // 步骤3: 分批删除（每3个删1个）
    size_t delete_count = 0;
    for (size_t i = 0; i < test_data.size(); i += 3) {
        TestKey key = test_data[i].first;

        bool pool_removed = _pool_map->remove(key);
        size_t std_removed = _std_map->erase(key);

        EXPECT_TRUE(pool_removed);
        EXPECT_EQ(1U, std_removed);

        delete_count++;
    }

    EXPECT_EQ(_pool_map->size(), _std_map->size());
    size_t expected_remaining = test_data.size() - delete_count;
    EXPECT_EQ(expected_remaining, _pool_map->size());

    // 步骤4: 验证删除后的查找结果
    size_t verification_errors = 0;
    for (size_t i = 0; i < test_data.size(); ++i) {
        TestKey key = test_data[i].first;
        bool should_be_deleted = (i % 3 == 0);

        TestValue* pool_found = _pool_map->find(key);
        auto std_found = _std_map->find(key);

        if (should_be_deleted) {
            if (pool_found != nullptr || std_found != _std_map->end()) {
                verification_errors++;
            }
        } else {
            if (pool_found == nullptr || std_found == _std_map->end()) {
                verification_errors++;
            } else if (
                    *pool_found != test_data[i].second ||
                    std_found->second != test_data[i].second) {
                verification_errors++;
            }
        }
    }

    EXPECT_EQ(0U, verification_errors);

    // 步骤5: 清空测试
    _pool_map->clear();
    _std_map->clear();

    EXPECT_EQ(0U, _pool_map->size());
    EXPECT_EQ(0U, _std_map->size());
}

/**
 * @brief 拷贝构造函数和赋值运算符测试
 * 测试深拷贝语义和独立性
 */
TEST_F(PoolHashMapFunctionalTest, test_copy_operations) {
    auto test_data = generate_test_data(1000);
    
    // 在原始map中添加数据
    for (const auto& item : test_data) {
        _pool_map->emplace(item.first, item.second);
    }
    
    EXPECT_EQ(1000, _pool_map->size());
    
    // 测试拷贝构造函数
    TestHashMap copied_map(*_pool_map);
    EXPECT_EQ(_pool_map->size(), copied_map.size());
    EXPECT_TRUE(copied_map.is_valid());
    
    // 验证所有数据都被正确拷贝
    bool all_data_copied = true;
    for (const auto& item : test_data) {
        TestValue* found_in_original = _pool_map->find(item.first);
        TestValue* found_in_copied = copied_map.find(item.first);
        
        if (!found_in_original || !found_in_copied || 
            *found_in_original != *found_in_copied || 
            *found_in_copied != item.second) {
            all_data_copied = false;
            break;
        }
    }
    EXPECT_TRUE(all_data_copied);
    
    // 验证是深拷贝：修改原map不影响拷贝的map
    _pool_map->emplace(999999, 99999999);
    EXPECT_NE(_pool_map->size(), copied_map.size());
    EXPECT_EQ(nullptr, copied_map.find(999999));
    
    // 测试赋值运算符
    GCSlabMempool32* pool2 = create_memory_pool();
    ASSERT_NE(nullptr, pool2);
    
    TestHashMap target_map(pool2, 16);
    target_map.emplace(888888, 88888888);
    EXPECT_EQ(1, target_map.size());
    
    target_map = *_pool_map;
    EXPECT_EQ(_pool_map->size(), target_map.size());
    EXPECT_EQ(nullptr, target_map.find(888888));  // 原有数据被清除
    EXPECT_NE(nullptr, target_map.find(999999));  // 新数据被拷贝
    
    // 测试自赋值
    size_t original_size = _pool_map->size();
    *_pool_map = *_pool_map;
    EXPECT_EQ(original_size, _pool_map->size());
    EXPECT_TRUE(_pool_map->is_valid());
    
    delete pool2;
}

/**
 * @brief rehash过程中数据完整性测试
 * 验证rehash过程中所有数据保持完整且可查询
 */
TEST_F(PoolHashMapFunctionalTest, test_rehash_data_integrity) {
    auto test_data = generate_test_data(1000);

    // 插入数据
    for (const auto& item : test_data) {
        _pool_map->emplace(item.first, item.second);
        _std_map->emplace(item.first, item.second);
    }

    uint32_t old_bucket_count = _pool_map->bucket_count();

    // 强制触发rehash - 插入直到bucket_count变化
    auto trigger_data = generate_test_data(1000, 10000);
    for (const auto& item : trigger_data) {
        _pool_map->emplace(item.first, item.second);
        _std_map->emplace(item.first, item.second);
        if (_pool_map->bucket_count() > old_bucket_count)
            break;
    }

    EXPECT_GT(_pool_map->bucket_count(), old_bucket_count);
    EXPECT_EQ(_pool_map->size(), _std_map->size());

    // 验证原数据完整性
    bool integrity_ok = verify_query_consistency(_pool_map, _std_map, test_data);
    EXPECT_TRUE(integrity_ok);
}

/**
 * @brief 哈希冲突处理测试
 * 验证哈希冲突场景下的数据完整性
 */
TEST_F(PoolHashMapFunctionalTest, test_hash_collision_handling) {
    // 构造易冲突的key（相同低位）
    std::vector<std::pair<TestKey, TestValue>> collision_data;
    uint32_t bucket_count = _pool_map->bucket_count();

    for (uint32_t i = 0; i < 50; ++i) {
        TestKey key = i * bucket_count + 1;  // 强制映射到同一bucket
        collision_data.emplace_back(key, key * 100);
    }

    for (const auto& item : collision_data) {
        auto result = _pool_map->emplace(item.first, item.second);
        EXPECT_NE(nullptr, result.first);
    }

    // 验证所有冲突数据都能正确查询
    for (const auto& item : collision_data) {
        TestValue* found = _pool_map->find(item.first);
        ASSERT_NE(nullptr, found);
        EXPECT_EQ(*found, item.second);
    }

    uint32_t node_num = _pool_map->size();
    EXPECT_EQ(node_num, collision_data.size());  // 确实插入了所有数据
}