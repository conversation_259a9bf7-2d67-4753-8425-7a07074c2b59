// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @brief 

#include <list>
#include <gtest/gtest.h>
#include "goe_list.hpp"
#include "archive.h"

namespace anti {
namespace themis {
namespace common_lib { 

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}

class TestNode {
public:
    TestNode() {}
    TestNode(uint64_t s, uint64_t i, int64_t c) :
            _sign(s), _id(i), _coord(c) {}
    uint64_t sign() const { return _sign; }
    uint64_t id() const { return _id; }
    int64_t coord() const { return _coord; }

    TestNode& operator+=(const TestNode& rhs) {
        return *this;
    }

    template<typename Archive>
    bool serialize(Archive* ar) const {
        t_write(_sign, ar);
        t_write(_id, ar);
        t_write(_coord, ar);
        return true;
    }

    template<typename Archive>
    bool deserialize(Archive* ar) {
        t_read(&_sign, ar);
        t_read(&_id, ar);
        t_read(&_coord, ar);
        return true;
    }

private:
    uint64_t _sign = 0u;
    uint64_t _id = 0u;
    int64_t _coord = 0;
};

TEST(CompileTest, for_compile_check_only) {
    GOEList<NodeDemo> compile_checker(100, 100);
}

TEST(ListIterator, copy_or_point) {
    std::list<int> num_list;
    num_list.push_back(1);
    num_list.push_back(2);
    num_list.push_back(3);
    num_list.push_back(4);

    auto two = num_list.begin();
    ++two;
    auto three = two;
    ++three;
    printf("two: %d, next: %d\n", *two, *three);
    num_list.erase(three);
    auto four = two;
    ++four;
    printf("two: %d, next: %d\n", *two, *four);
}

class GOEListTestSuite : public ::testing::Test {
protected:
    GOEListTestSuite() : _goe(10, 4) {}
    virtual void SetUp() {}
    virtual void TearDown() {}
private:
    GOEList<TestNode> _goe;
};

TEST_F(GOEListTestSuite, insert) {
    TestNode node1(1, 1, 1);
    TestNode node2(2, 2, 2);
    TestNode node3(3, 3, 3);
    TestNode node4(1, 1, 1);

    ASSERT_TRUE(_goe.insert(node1) != nullptr);
    ASSERT_TRUE(_goe.insert(node2) != nullptr);
    ASSERT_TRUE(_goe.insert(node3) != nullptr);
    ASSERT_TRUE(_goe.insert(node4) != nullptr);

    EXPECT_EQ(_goe._sign_lru_list->size(), 3u);
    EXPECT_EQ(_goe._node_num, 3);

    auto iter = _goe._sign_lru_list->begin();
    EXPECT_EQ((*iter)->front().sign(), 2);
    ++iter;
    EXPECT_EQ((*iter)->front().sign(), 3);
    ++iter;
    EXPECT_EQ((*iter)->front().sign(), 1);

    TestNode node5(2, 3, 3);
    ASSERT_TRUE(_goe.insert(node5) != nullptr);

    iter = _goe._sign_lru_list->begin();
    EXPECT_EQ((*iter)->front().sign(), 3);
    ++iter;
    EXPECT_EQ((*iter)->front().sign(), 1);
    ++iter;
    EXPECT_EQ((*iter)->front().sign(), 2);
}

#define INSERT(s, i, c) \
do {    \
    TestNode n(s, i, c);    \
    _goe.insert(n); \
} while (0)

inline bool check_node_ordered_ptr(GOEList<TestNode>::IdOrderedListPtr ptr,
        int* match_coord, int len) {
    //if (ptr->size() != (uint32_t)len) { return false; }
    EXPECT_EQ(ptr->size(), (uint32_t)len);
    if (ptr->size() != (uint32_t)len) { return nullptr; }
    int idx = 0;
    for (auto it = ptr->begin(); it != ptr->end(); ++it) {
        EXPECT_EQ(match_coord[idx], it->coord());
        idx++;
    }
    return true;
}

TEST_F(GOEListTestSuite, insert_and_query) {
    INSERT(1, 1, 1);
    INSERT(2, 3, 2);
    INSERT(1, 4, 5);
    INSERT(1, 2, 2);

    auto ptr = _goe.query(1);
    ASSERT_TRUE(ptr != nullptr);
    int match_coord[] = {5, 2 ,1};
    EXPECT_TRUE(check_node_ordered_ptr(ptr, match_coord, 3));
}

TEST_F(GOEListTestSuite, insert_and_remove_two_old_node) {
    INSERT(1, 1, 1);
    INSERT(1, 1, 3);
    INSERT(1, 1, 5);
    INSERT(1, 1, 2);

    {
        auto ptr = _goe.query(1);
        ASSERT_TRUE(ptr != nullptr);
        int match_coord[] = {5, 3, 2 ,1};
        check_node_ordered_ptr(ptr, match_coord, 4);
        //EXPECT_TRUE(check_node_ordered_ptr(ptr, match_coord, 4));
    }

    {
        INSERT(1, 1, 6);
        auto ptr = _goe.query(1);
        ASSERT_TRUE(ptr != nullptr);
        int match_coord[] = {6, 5, 3, 2 ,1};
        EXPECT_TRUE(check_node_ordered_ptr(ptr, match_coord, 5));
    }

    {
        INSERT(1, 1, 15);
        auto ptr = _goe.query(1);
        ASSERT_TRUE(ptr != nullptr);
        int match_coord[] = {15, 6, 5};
        EXPECT_TRUE(check_node_ordered_ptr(ptr, match_coord, 3));
    }

    {
        INSERT(1, 1, 4);
        auto ptr = _goe.query(1);
        ASSERT_TRUE(ptr != nullptr);
        int match_coord[] = {15, 6, 5, 4};
        EXPECT_TRUE(check_node_ordered_ptr(ptr, match_coord, 4));
    }
}

TEST_F(GOEListTestSuite, insert_and_gc_old_list) {
    INSERT(1, 1, 1);
    INSERT(1, 1, 3);
    INSERT(1, 1, 5);
    INSERT(2, 1, 6);

    {
        auto ptr = _goe.query(1);
        ASSERT_TRUE(ptr != nullptr);
    }

    INSERT(11, 1, 16);
    {
        auto ptr1 = _goe.query(1);
        ASSERT_TRUE(ptr1 == nullptr);
        auto ptr2 = _goe.query(2);
        ASSERT_TRUE(ptr2 != nullptr);
    }
}

TEST_F(GOEListTestSuite, unordered_insert_gc) {
    INSERT(1, 1, 1);
    INSERT(2, 1, 6);
    INSERT(3, 1, 4);
    
    // lru list (1,1) -> (2,6) -> (3,4)
    ASSERT_TRUE(_goe.query(1) != nullptr);
    ASSERT_TRUE(_goe.query(2) != nullptr);
    ASSERT_TRUE(_goe.query(3) != nullptr);

    INSERT(4, 1, 15);
    // lru list (2,6) -> (3,4) -> (4,11)
    ASSERT_TRUE(_goe.query(1) == nullptr);
    ASSERT_TRUE(_goe.query(2) != nullptr);
    ASSERT_TRUE(_goe.query(3) != nullptr);
    ASSERT_TRUE(_goe.query(4) != nullptr);
}

TEST_F(GOEListTestSuite, dump_and_load) {
    INSERT(1, 1, 1);
    INSERT(1, 1, 2);
    INSERT(2, 1, 6);
    INSERT(3, 1, 4);

    {
        FileArchive ar;
        ar.open_w("./test.dat");

        ASSERT_TRUE(_goe.serialize(&ar));
        ar.close();
    }

    {
        GOEList<TestNode> rd_goe(0, 0);
        FileArchive ar;
        ar.open_r("./test.dat");
        
        ASSERT_TRUE(rd_goe.deserialize(&ar));
        ar.close();

        ASSERT_TRUE(rd_goe.query(1) != nullptr);
        ASSERT_TRUE(rd_goe.query(2) != nullptr);
        ASSERT_TRUE(rd_goe.query(3) != nullptr);
    }

    system("rm ./test.dat");
}

}
}
}
