/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * $Id$ 
 * 
 **************************************************************************/
 
/**
 * @file sliding_window/unittest/archive_test.cpp
 * <AUTHOR>
 * @date 2015/04/10 22:36:58
 * @version $Revision$ 
 * @brief 
 *  
 **/

#include <gtest/gtest.h>
#include <bmock.h>
#include "archive.h"

using anti::themis::common_lib::FileArchive;

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}

class FileArchiveTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
    }

    virtual void TearDown() {
    }
};

BMOCK_NS_METHOD3(anti::themis::common_lib, my_open, int(const char*, int, mode_t));
BMOCK_NS_METHOD1(anti::themis::common_lib, my_close, int(int));
using ::testing::Return;
using ::testing::_;

TEST_F(FileArchiveTestSuite, case_1) {
    FileArchive ar;

    EXPECT_CALL(BMOCK_OBJECT(my_open), my_open(_, _, _))
            .WillOnce(Return(-1))
            .WillRepeatedly(Return(1));

    EXPECT_FALSE(ar._open("", O_RDONLY, 0));
    EXPECT_TRUE(ar._open("", O_RDONLY, 0));
    ar._fd = -1; // because it's mock return 1, so release it
}

TEST_F(FileArchiveTestSuite, case_2) {
    FileArchive ar;
    ar._fd = 123;

    EXPECT_CALL(BMOCK_OBJECT(my_close), my_close(_))
            .WillRepeatedly(Return(-1));

    EXPECT_FALSE(ar.close());

    ar._fd = -1;
}

/* vim: set ts=4 sw=4 sts=4 tw=100 */
