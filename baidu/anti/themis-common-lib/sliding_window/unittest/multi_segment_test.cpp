// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @brief 

#include <gtest/gtest.h>
#include "multi_segment.h"
#include "item.h"
#include "archive.h"

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}

namespace anti {
namespace themis {
namespace common_lib {

typedef MultiSegment<SegmentItem> MSegment;

class MultiSegmentTestSuite : public ::testing::Test {
public:
    virtual void SetUp() {
        ASSERT_TRUE(_mseg.init(_step_len, _segment_len));
    }
    virtual void TearDown() {}

private:
    MSegment _mseg;
    int64_t _step_len = 100;
    int64_t _segment_len = 1000;
};

TEST_F(MultiSegmentTestSuite, init) {
    MSegment mseg;
    ASSERT_TRUE(mseg.init(100, 1000));

    EXPECT_EQ(mseg._step_len, 100);
    EXPECT_EQ(mseg._segment_len, 1000);
    EXPECT_EQ(mseg._step_num, 1000 / 100);
    EXPECT_EQ(mseg._max_step_num, 1000 / 100 + kMultiSegRemainStep);

    EXPECT_EQ(mseg._window.size(), mseg._max_step_num);
    EXPECT_EQ(mseg._segment_start_coord, 0L);
    EXPECT_EQ(mseg._latest_coord, 0L);
}

TEST_F(MultiSegmentTestSuite, push_item_failed_null_step) {
    MSegment::StepContainerPtr step;
    SegmentItem item;
    MSegment mseg;

    ASSERT_FALSE(mseg._push_item(step, 1, item, NULL));
}

TEST_F(MultiSegmentTestSuite, push_item_succ_first_insert) {
    MSegment::StepContainerPtr step(new MSegment::StepContainer());
    SegmentItem item(1);
    MSegment mseg;

    ASSERT_TRUE(mseg._push_item(step, 1, item, NULL));

    EXPECT_EQ((*step)[1].cumulant(), 1);
}

TEST_F(MultiSegmentTestSuite, push_item_succ_multi_insert) {
    MSegment::StepContainerPtr step(new MSegment::StepContainer());
    SegmentItem item(1);
    MSegment mseg;

    ASSERT_TRUE(mseg._push_item(step, 1, item, NULL));
    ASSERT_TRUE(mseg._push_item(step, 1, item, NULL));

    EXPECT_EQ((*step)[1].cumulant(), 2);
}

TEST_F(MultiSegmentTestSuite, push_item_succ_return_value) {
    MSegment::StepContainerPtr step(new MSegment::StepContainer());
    SegmentItem item(1);
    MSegment mseg;

    const SegmentItem* acc = NULL;
    ASSERT_TRUE(mseg._push_item(step, 1, item, NULL));
    ASSERT_TRUE(mseg._push_item(step, 1, item, &acc));

    ASSERT_TRUE(acc != NULL);
    EXPECT_EQ(acc->cumulant(), 2);
}

TEST_F(MultiSegmentTestSuite, find_step_illegal) {
    MSegment mseg;
    ASSERT_TRUE(mseg.init(100, 1000));

    mseg._oldest_coord = 2500;
    EXPECT_FALSE((mseg._find_step(2499)));
    EXPECT_FALSE((mseg._find_step(3500 + 100 * kMultiSegRemainStep)));
}

TEST_F(MultiSegmentTestSuite, find_step_succ) {
    _mseg._oldest_coord = 2500;
    auto ptr = _mseg._find_step(2600);
    EXPECT_TRUE(ptr.get() != NULL);
}

TEST_F(MultiSegmentTestSuite, move_and_clear_oldest_step) {
    _mseg._oldest_coord = 1000;
    (_mseg._move_and_clear_oldest_step());
    EXPECT_EQ(_mseg._oldest_coord, 1000 + _step_len);
}

TEST_F(MultiSegmentTestSuite, slide_one_step) {
    _mseg._oldest_coord = 1000;
    _mseg._segment_start_coord = 1200;

    _mseg._slide_one_step();

    EXPECT_EQ(_mseg._oldest_coord, 1000 + _step_len);
    EXPECT_EQ(_mseg._segment_start_coord, 1200 + _step_len);
}

TEST_F(MultiSegmentTestSuite, slide_to_coord_do_nothing) {
    _mseg._segment_start_coord = 1000;

    EXPECT_EQ(_mseg._slide_to_coord(1000), 0);
    EXPECT_EQ(_mseg._slide_to_coord(1000 + _segment_len - 1), 0);
}

TEST_F(MultiSegmentTestSuite, slide_to_coord_just_one) {
    _mseg._segment_start_coord = 1000;

    ASSERT_EQ(_mseg._slide_to_coord(1000 + _segment_len), _step_len);
    EXPECT_EQ(_mseg._segment_start_coord, 1000 + _step_len);
}

TEST_F(MultiSegmentTestSuite, slide_to_coord_skip_two_step) {
    _mseg._segment_start_coord = 1000;

    ASSERT_EQ(_mseg._slide_to_coord(1000 + _segment_len + _step_len + 2), _step_len * 2);
    EXPECT_EQ(_mseg._segment_start_coord, 1000 + _step_len * 2);
}

TEST_F(MultiSegmentTestSuite, slide_to_coord_skip_huge) {
    _mseg._segment_start_coord = 1000;
    ASSERT_EQ(_mseg._slide_to_coord(10020), 8100);

    EXPECT_EQ(_mseg._latest_coord, 10020);
    EXPECT_EQ(_mseg._segment_start_coord, 9100);
}

TEST_F(MultiSegmentTestSuite, enter_first_coord) {
    SegmentItem item(1);
    const SegmentItem* acc = NULL;
    ASSERT_TRUE(_mseg.enter(1, 1011, item, &acc));

    EXPECT_EQ(acc->cumulant(), 1);
    EXPECT_EQ(_mseg._segment_start_coord, 100);
    EXPECT_EQ(_mseg._latest_coord, 1011);
}

TEST_F(MultiSegmentTestSuite, enter_multi_coord) {
    SegmentItem item(1);
    const SegmentItem* acc = NULL;
    ASSERT_TRUE(_mseg.enter(1, 1011, item, &acc));
    ASSERT_TRUE(_mseg.enter(1, 1022, item, &acc));
    ASSERT_TRUE(_mseg.enter(1, 1033, item, &acc));

    EXPECT_EQ(acc->cumulant(), 3);
    EXPECT_EQ(_mseg._segment_start_coord, 100);
    EXPECT_EQ(_mseg._latest_coord, 1033);
}

TEST_F(MultiSegmentTestSuite, enter_slide) {
    SegmentItem item(1);
    const SegmentItem* acc = NULL;
    ASSERT_TRUE(_mseg.enter(1, 1011, item, &acc));
    EXPECT_EQ(acc->cumulant(), 1);
    EXPECT_EQ(_mseg._segment_start_coord, 100);

    ASSERT_TRUE(_mseg.enter(1, 1122, item, &acc));
    EXPECT_EQ(acc->cumulant(), 1);
    EXPECT_EQ(_mseg._segment_start_coord, 200);

    ASSERT_TRUE(_mseg.enter(1, 1333, item, &acc));
    EXPECT_EQ(acc->cumulant(), 1);
    EXPECT_EQ(_mseg._segment_start_coord, 400);
}

TEST_F(MultiSegmentTestSuite, enter_diff_step_and_query) {
    SegmentItem item(1);
    int64_t coord[] = {1011, 1111, 1211, 1122, 1322};
    int64_t target[] = {1, 1, 1, 2, 1};

    for (int i = 0; i < 5; ++i) {
        ASSERT_TRUE(_mseg.enter(1, coord[i], item, NULL));
        EXPECT_EQ(_mseg.query(1, coord[i])->cumulant(), target[i]);
    }
}

TEST_F(MultiSegmentTestSuite, serialize_and_deserialize) {
    // build multi-segment
    SegmentItem item(1);
    int64_t coord[] = {1011, 1111, 1211, 1122, 1322};
    int64_t target[] = {1, 1, 1, 2, 1};

    for (int i = 0; i < 5; ++i) {
        ASSERT_TRUE(_mseg.enter(1, coord[i], item, NULL));
        EXPECT_EQ(_mseg.query(1, coord[i])->cumulant(), target[i]);
    }


    // dump base window
    std::string ckpt_filepath = "./sw.cpt";
    system("rm -rf ./sw.cpt");
    anti::themis::common_lib::FileArchive ar;
    ASSERT_TRUE(ar.open_w(ckpt_filepath.c_str()));
    EXPECT_TRUE(_mseg.serialize<FileArchive>(&ar));
    ar.flush();
    ASSERT_TRUE(ar.close());


    anti::themis::common_lib::FileArchive ar_read;
    ASSERT_TRUE(ar_read.open_r(ckpt_filepath.c_str()));
    MSegment rd_seg;
    ASSERT_TRUE(rd_seg.init(_step_len, _segment_len));
    ASSERT_TRUE(rd_seg.deserialize<FileArchive>(&ar_read));
    ar_read.close();

    int64_t rd_target[] = {1, 2, 1, 2, 1};

    for (int i = 0; i < 5; ++i) {
        EXPECT_EQ(_mseg.query(1, coord[i])->cumulant(), rd_target[i]);
    }

    system("rm -rf ./sw.cpt");
}

}
}
}
