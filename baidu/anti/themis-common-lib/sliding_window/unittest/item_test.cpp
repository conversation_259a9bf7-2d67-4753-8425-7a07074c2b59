/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * $Id$ 
 * 
 **************************************************************************/
 
/**
 * @file sliding_window/unittest/item_test.cpp
 * <AUTHOR>
 * @date 2015/04/11 16:33:30
 * @version $Revision$ 
 * @brief 
 *  
 **/

#include <gtest/gtest.h>
#include <bmock.h>

#include "archive.h"
#include "item.h"

using anti::themis::common_lib::SegmentItem;
using anti::themis::common_lib::RateItem;
using anti::themis::common_lib::ConItem;
using anti::themis::common_lib::ConNode;
using anti::themis::common_lib::DistinctItem;
using anti::themis::common_lib::ThreeOrderTreeItem;
using anti::themis::common_lib::TreeItemStatisticCond;
using anti::baselib::SignUtil;

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}

class ItemTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
    }

    virtual void TearDown() {
    }
};

TEST_F(ItemTestSuite, case_1) {
    SegmentItem si;
    EXPECT_TRUE(si.is_null());
    si = 1;
    EXPECT_EQ(si.cumulant(), 1);
    si = 2;
    EXPECT_EQ(si.cumulant(), 2);
    si += 8;
    EXPECT_EQ(si.cumulant(), 10);
    si -= 5;
    EXPECT_EQ(si.cumulant(), 5);
    EXPECT_FALSE(si.is_null());
}

TEST_F(ItemTestSuite, case_2) {
    RateItem ri;
    EXPECT_TRUE(ri.is_null());
    EXPECT_EQ(ri.first(), 0);
    EXPECT_EQ(ri.second(), 0);
    ri += RateItem(2, 3);
    EXPECT_EQ(ri.first(), 2);
    EXPECT_EQ(ri.second(), 3);
    EXPECT_FALSE(ri.is_null());
    ri -= RateItem(0, 2);
    EXPECT_EQ(ri.first(), 2);
    EXPECT_EQ(ri.second(), 1);
    ri -= RateItem(2, 2);
    EXPECT_EQ(ri.first(), 0);
    EXPECT_EQ(ri.second(), -1);
    EXPECT_TRUE(ri.is_null());
}

class ArchiveDemo {
public:
    virtual int read(void*, size_t) {
        return 0;
    }
    virtual int write(const void*, size_t) {
        return 0;
    }
};

BMOCK_CLASS_METHOD2(ArchiveDemo, read, int(void*, size_t));
BMOCK_CLASS_METHOD2(ArchiveDemo, write, int(const void*, size_t));
using ::testing::Return;
using ::testing::_;

TEST_F(ItemTestSuite, case_pair_item) {
    ArchiveDemo ar;
    SegmentItem si;
    RateItem ri;

    EXPECT_CALL(BMOCK_CLASS_OBJECT(ArchiveDemo, read), read(_, 8))
            .WillOnce(Return(8))
            .WillOnce(Return(8))
            .WillOnce(Return(8))
            .WillRepeatedly(Return(0));

    EXPECT_CALL(BMOCK_CLASS_OBJECT(ArchiveDemo, write), write(_, 8))
            .WillOnce(Return(8))
            .WillOnce(Return(8))
            .WillOnce(Return(8))
            .WillRepeatedly(Return(0));

    EXPECT_TRUE(si.deserialize<ArchiveDemo>(&ar));
    EXPECT_TRUE(ri.deserialize<ArchiveDemo>(&ar));
    EXPECT_FALSE(si.deserialize<ArchiveDemo>(&ar));
    EXPECT_FALSE(ri.deserialize<ArchiveDemo>(&ar));
    EXPECT_TRUE(si.serialize<ArchiveDemo>(&ar));
    EXPECT_TRUE(ri.serialize<ArchiveDemo>(&ar));
    EXPECT_FALSE(si.serialize<ArchiveDemo>(&ar));
    EXPECT_FALSE(ri.serialize<ArchiveDemo>(&ar));
}

typedef anti::themis::common_lib::ArrayItem<int64_t, 5> DistributionItem;

TEST_F(ItemTestSuite, case_array_item_1) {
    DistributionItem di;
    DistributionItem di2;

    di[0] = 2;
    di[4] = 3;
    EXPECT_EQ(di[0], 2);
    EXPECT_EQ(di[4], 3);
    EXPECT_FALSE(di.is_null());

    EXPECT_TRUE(di2.is_null());
    di2[2] = 5;

    di += di2;
    EXPECT_EQ(di[0], 2);
    EXPECT_EQ(di[1], 0);
    EXPECT_EQ(di[2], 5);
    EXPECT_EQ(di[3], 0);
    EXPECT_EQ(di[4], 3);

    EXPECT_EQ(di2[2], 5);

    di2[0] = 1;
    di2[1] = 3;
    di2[2] = 3;
    di2[4] = 2;
    di -= di2;
    EXPECT_EQ(di[0], 1);
    EXPECT_EQ(di[1], -3);
    EXPECT_EQ(di[2], 2);
    EXPECT_EQ(di[3], 0);
    EXPECT_EQ(di[4], 1);
}

TEST_F(ItemTestSuite, case_arry_item_serialize_1) {
    DistributionItem di;
    di[0] = 5;
    di[1] = 4;
    di[2] = 3;
    di[3] = 2;
    di[4] = 1;

    ArchiveDemo ar;

    EXPECT_CALL(BMOCK_CLASS_OBJECT(ArchiveDemo, write), write(_, 40))
            .WillOnce(Return(40))
            .WillRepeatedly(Return(0));
    EXPECT_TRUE(di.serialize<ArchiveDemo>(&ar));
    EXPECT_FALSE(di.serialize<ArchiveDemo>(&ar));

    EXPECT_CALL(BMOCK_CLASS_OBJECT(ArchiveDemo, read), read(_, 40))
            .WillOnce(Return(40))
            .WillRepeatedly(Return(0));
    EXPECT_TRUE(di.deserialize<ArchiveDemo>(&ar));
    EXPECT_FALSE(di.deserialize<ArchiveDemo>(&ar));
}

TEST_F(ItemTestSuite, case_really_serialize_1) {
    DistributionItem di;
    di[0] = 5;
    di[1] = 4;
    di[2] = 3;
    di[3] = 2;
    di[4] = 1;
    
    using anti::themis::common_lib::FileArchive;
    FileArchive ar;
    const char* cpt_file = "./array_item.cpt";
    system("rm -f ./array_item.cpt");
    ASSERT_TRUE(ar.open_w(cpt_file));

    ASSERT_TRUE(di.serialize<FileArchive>(&ar));
    ar.close();

    DistributionItem di2;
    FileArchive ar2;
    ASSERT_TRUE(ar2.open_r(cpt_file));

    ASSERT_TRUE(di2.deserialize<FileArchive>(&ar2));
    ar2.close();

    EXPECT_EQ(di2[0], 5);
    EXPECT_EQ(di2[1], 4);
    EXPECT_EQ(di2[2], 3);
    EXPECT_EQ(di2[3], 2);
    EXPECT_EQ(di2[4], 1);
    system("rm -f ./array_item.cpt");
}
/////////

TEST_F(ItemTestSuite, case_concentration_item_1) {
    ConItem ci;
    ConItem ci2;
    ConItem ci3;
    ConItem ci4;
    ConItem ci5;

    EXPECT_TRUE(ci.init(3, 1, 1111));
    EXPECT_TRUE(ci2.init(3, 2, 1112));
    EXPECT_TRUE(ci3.init(3, 3, 1113));
    EXPECT_TRUE(ci4.init(3, 4, 1114));
    EXPECT_TRUE(ci5.init(3, 2, 1114));

    EXPECT_EQ(ci.cumulant(), 1);
    EXPECT_EQ(ci.kcumulant(), 1);
    EXPECT_EQ(ci2.cumulant(), 2);
    EXPECT_EQ(ci2.kcumulant(), 2);
    EXPECT_FALSE(ci.is_null());

    EXPECT_FALSE(ci2.is_null());
    ci += ci2;
    EXPECT_EQ(ci.cumulant(), 3);
    EXPECT_EQ(ci.kcumulant(), 3);
    ci += ci3;
    EXPECT_EQ(ci.cumulant(), 6);
    EXPECT_EQ(ci.kcumulant(), 6);
    ci += ci4;
    EXPECT_EQ(ci.cumulant(), 10);
    EXPECT_EQ(ci.kcumulant(), 9);

    ci -= ci2;
    EXPECT_EQ(ci.cumulant(), 8);
    EXPECT_EQ(ci.kcumulant(), 8);

    ci -= ci5;
    EXPECT_EQ(ci.cumulant(), 6);
    EXPECT_EQ(ci.kcumulant(), 6);
    ci += ci2;
    EXPECT_EQ(ci.cumulant(), 8);
    EXPECT_EQ(ci.kcumulant(), 7);
    ci += ci2;
    EXPECT_EQ(ci.cumulant(), 10);
    EXPECT_EQ(ci.kcumulant(), 9);
}

TEST_F(ItemTestSuite, pair_node_serialize_1) {
    ConNode cn1(123,2);

    ArchiveDemo ar;

    EXPECT_CALL(BMOCK_CLASS_OBJECT(ArchiveDemo, write), write(_, 8))
            .WillOnce(Return(8))
            .WillOnce(Return(8))
            .WillRepeatedly(Return(0));
    EXPECT_TRUE(cn1.serialize<ArchiveDemo>(&ar));
    EXPECT_FALSE(cn1.serialize<ArchiveDemo>(&ar));

    EXPECT_CALL(BMOCK_CLASS_OBJECT(ArchiveDemo, read), read(_, 8))
            .WillOnce(Return(8))
            .WillOnce(Return(8))
            .WillRepeatedly(Return(0));
    EXPECT_TRUE(cn1.deserialize<ArchiveDemo>(&ar));
    EXPECT_FALSE(cn1.deserialize<ArchiveDemo>(&ar));
}

TEST_F(ItemTestSuite, pair_node_really_serialize_1) {
    ConItem ci;
    ConItem ci2;
    ConItem ci3;
    ConItem ci4;
    ConItem ci5;

    EXPECT_TRUE(ci.init(3, 1, 1111));
    EXPECT_TRUE(ci2.init(3, 2, 1112));
    EXPECT_TRUE(ci3.init(3, 3, 1113));
    EXPECT_TRUE(ci4.init(3, 4, 1114));
    EXPECT_TRUE(ci5.init(3, 5, 1114));
    ci += ci2;
    ci += ci3;
    ci += ci4;
    ci += ci5;
    EXPECT_EQ(ci.cumulant(), 15);
    EXPECT_EQ(ci.kcumulant(), 14);
    
    using anti::themis::common_lib::FileArchive;
    FileArchive ar;
    const char* cpt_file = "./concentration_item.cpt";
    system("rm -f ./concentration_item.cpt");
    ASSERT_TRUE(ar.open_w(cpt_file));

    ASSERT_TRUE(ci.serialize<FileArchive>(&ar));
    ar.close();

    FileArchive ar2;
    ASSERT_TRUE(ar2.open_r(cpt_file));

    ASSERT_TRUE(ci2.deserialize<FileArchive>(&ar2));
    ar2.close();

    system("rm -f ./concentration_item.cpt");
    EXPECT_EQ(ci2.cumulant(), 15);
    EXPECT_EQ(ci2.kcumulant(), 14);
}

TEST_F(ItemTestSuite, case_concentration_item_2) {
    ConItem ci;
    ConItem ci2;
    ConItem ci3;

    EXPECT_TRUE(ci.init(1, 1, 1111));
    EXPECT_TRUE(ci2.init(1, 1, 1111));
    EXPECT_TRUE(ci3.init(1, 1, 1112));

    EXPECT_EQ(ci.cumulant(), 1);
    EXPECT_EQ(ci2.cumulant(), 1);
    EXPECT_EQ(ci3.cumulant(), 1);

    for (int64_t i = 0L; i < 2; ++i) {
        ci += ci2;
        EXPECT_EQ(ci.cumulant(), i + 2);
        EXPECT_EQ(ci.kcumulant(), i + 2);
    }
    for (int64_t i = 0L; i < 3; ++i) {
        ci += ci3;
        EXPECT_EQ(ci.cumulant(), 4 + i);
        EXPECT_EQ(ci.kcumulant(), 3);
    }
    ci += ci3;
    EXPECT_EQ(ci.cumulant(), 7);
    EXPECT_EQ(ci.kcumulant(), 4);
}

TEST_F(ItemTestSuite, case_distinct_item_1) {
    DistinctItem di(123456, 1);
    DistinctItem di1(123456, 1);
    DistinctItem di2(123456, 2);
    DistinctItem di3(123457, 3);

    EXPECT_EQ(di.distinct_num(), 1);
    EXPECT_EQ(di2.distinct_num(), 1);
    EXPECT_EQ(di3.distinct_num(), 1);
    
    for (int64_t i = 0L; i < 10; ++i) {
        di += di2;
        EXPECT_EQ(di.distinct_num(), 1);
        EXPECT_EQ(di.total(), 1 + i * 2 + 2);
    }
    for (int64_t i = 0L; i < 10; ++i) {
        di += di3;
        EXPECT_EQ(di.distinct_num(), 2);
        EXPECT_EQ(di.total(), 21 + i * 3 + 3);
    }
    // test ckpt serialize
    using anti::themis::common_lib::FileArchive;
    FileArchive ar;
    const char* cpt_file = "./distinct_item.cpt";
    system("rm -f ./distinct_item.cpt");
    ASSERT_TRUE(ar.open_w(cpt_file));

    ASSERT_TRUE(di.serialize<FileArchive>(&ar));
    ar.close();

    FileArchive ar2;
    ASSERT_TRUE(ar2.open_r(cpt_file));

    DistinctItem di4;
    ASSERT_TRUE(di4.deserialize<FileArchive>(&ar2));
    ar2.close();

    system("rm -f ./distinct_item.cpt");

    EXPECT_EQ(di4.distinct_num(), 2);
    EXPECT_EQ(di4.total(), 51);

    for (int64_t i = 0L; i < 10; ++i) {
        di4 -= di2;
        EXPECT_EQ(di4.distinct_num(), 2);
        EXPECT_EQ(di4.total(), 51 - i * 2 - 2);
    }
    for (int64_t i = 0L; i < 10; ++i) {
        di4 -= di3;
        if (i == 9L) {
            EXPECT_EQ(di4.distinct_num(), 1);
        } else {
            EXPECT_EQ(di4.distinct_num(), 2);
        }
        EXPECT_EQ(di4.total(), 31 - i * 3 - 3);
    }
    di4 -= di; // will failed cause of di include d3's key
    EXPECT_EQ(di4.distinct_num(), 1);
    EXPECT_EQ(di4.total(), 1);
    di4 -= di1;
    EXPECT_EQ(di4.distinct_num(), 0);
    EXPECT_EQ(di4.total(), 0);
}

typedef std::vector<std::pair<uint64_t, std::vector<std::pair<uint64_t, int64_t>>>> ThreeOrderTreeList;
bool check_three_order_tree_item_node(ThreeOrderTreeItem item, ThreeOrderTreeList list) {
    if (item.map().size() != list.size()) {
        CWARNING_LOG("item.map().size[%u] != list.size[%u]", item.map().size(), list.size());
        return false;
    }
    int64_t total = 0L;
    for (const auto& leaf : list) {
        auto leaf_key = leaf.first;
        auto leaf_values = leaf.second;
        const auto& leaf_key_map = item.map();
        auto it = leaf_key_map.find(leaf_key);
        if (it == leaf_key_map.end()) {
            CWARNING_LOG("leaf_key[%lu] not in leaf_key_map", leaf_key);
            return false;
        }
        const auto& leaf_value_map = it->second->map();
        if (leaf_value_map.size() != leaf_values.size()) {
            CWARNING_LOG("leaf_value_map.size[%u] != leaf_values.size[%u]",
                    leaf_value_map.size(), leaf_values.size());
            return false;
        }
        for (auto& leaf_value : leaf_values) {
            auto key = leaf_value.first;
            auto value = leaf_value.second;
            if (leaf_value_map.find(key) == leaf_value_map.end()) {
                CWARNING_LOG("leaf_value key[%lu] not in leaf_value map", key);
                return false;
            }
            if (leaf_value_map.at(key) != value) {
                CWARNING_LOG("value[%ld] != leaf_value_map [%ld]", 
                        value, leaf_value_map.at(key));
                return false;
            }
            total += value;
        }
    }
    if (item.total() != total) {
        CWARNING_LOG("item.total[%ld] != total[%ld]", item.total(), total);
    }
    return true;
}

TEST_F(ItemTestSuite, tree_item_test) {
    ThreeOrderTreeItem node1("data_1", "cumu1");
    uint64_t data_view_sign1 = 0UL;
    uint64_t data_view_sign2 = 0UL;
    uint64_t cumulate_view_sign1 = 0UL;
    uint64_t cumulate_view_sign2 = 0UL;
    SignUtil::create_sign_md64("data_1", &data_view_sign1);
    EXPECT_EQ(1U, node1._total);
    EXPECT_EQ(1U, node1._root_map.size());
    EXPECT_TRUE(node1._root_map.find(data_view_sign1) != node1._root_map.end());
    EXPECT_EQ(1U, node1._root_map[data_view_sign1]->size());


    ThreeOrderTreeItem node2("data_1", "cumu1");
    node1 += node2;
    EXPECT_EQ(2U, node1._total);
    EXPECT_EQ(1U, node1._root_map.size());
    EXPECT_TRUE(node1._root_map.find(data_view_sign1) != node1._root_map.end());
    EXPECT_EQ(1U, node1._root_map[data_view_sign1]->size());
    const auto& cumulate_map = *(node1._root_map[data_view_sign1]);
    SignUtil::create_sign_md64("cumu1", &cumulate_view_sign1); 
    EXPECT_EQ(2U, cumulate_map.at(cumulate_view_sign1));

    ThreeOrderTreeItem node3("data_2", "cumu2");
    node1 += node3;
    EXPECT_EQ(3U, node1._total);
    EXPECT_EQ(2U, node1._root_map.size());
    EXPECT_TRUE(node1._root_map.find(data_view_sign1) != node1._root_map.end());
    EXPECT_EQ(1U, node1._root_map[data_view_sign1]->size());
    SignUtil::create_sign_md64("data_2", &data_view_sign2); 
    EXPECT_TRUE(node1._root_map.find(data_view_sign2) != node1._root_map.end());
    EXPECT_EQ(1U, node1._root_map[data_view_sign2]->size());
    const auto& cumulate_map2 = *(node1._root_map[data_view_sign2]);
    SignUtil::create_sign_md64("cumu2", &cumulate_view_sign2); 
    EXPECT_EQ(1U, cumulate_map2.at(cumulate_view_sign2));

    ThreeOrderTreeItem node4("data_1", "cumu2");
    node1 += node4;
    EXPECT_EQ(4U, node1._total);
    EXPECT_EQ(2U, node1._root_map.size());
    EXPECT_TRUE(node1._root_map.find(data_view_sign2) != node1._root_map.end());
    EXPECT_EQ(1U, node1._root_map[data_view_sign2]->size());
    EXPECT_EQ(1U, cumulate_map2.at(cumulate_view_sign2));

    ThreeOrderTreeList list = { 
        { data_view_sign1, 
            {
                {cumulate_view_sign1, 2},
                {cumulate_view_sign2, 1}
            }
        }, 
        { data_view_sign2,
            {
                {cumulate_view_sign2, 1}
            }
        }
    };
    EXPECT_TRUE(check_three_order_tree_item_node(node1, list));

    node1 -= node2;
    node1 -= node2;
    
    list = { 
        { data_view_sign1, 
            {
                {cumulate_view_sign2, 1}
            }
        }, 
        { data_view_sign2,
            {
                {cumulate_view_sign2, 1}
            }
        }
    };
    EXPECT_TRUE(check_three_order_tree_item_node(node1, list));
    node1 -= node3;
    list = { 
        { data_view_sign1, 
            {
                {cumulate_view_sign2, 1}
            }
        }
    };
    EXPECT_TRUE(check_three_order_tree_item_node(node1, list));
    list = {};
    node1 -= node4;
    EXPECT_TRUE(check_three_order_tree_item_node(node1, list));
}

TEST_F(ItemTestSuite, tree_item_serialize_and_deserialize) {
    ThreeOrderTreeItem node1("data_1", "cumu_1");
    ThreeOrderTreeItem node2("data_1", "cumu_1");
    ThreeOrderTreeItem node3("data_1", "cumu_2");
    ThreeOrderTreeItem node4("data_2", "cumu_1");
    ThreeOrderTreeItem node5("data_2", "cumu_2");
    node1 += node2;
    node1 += node3;
    node1 += node4;
    node1 += node5;

    uint64_t data_view_sign1 = 0UL;
    uint64_t data_view_sign2 = 0UL;
    uint64_t cumulate_view_sign1 = 0UL;
    uint64_t cumulate_view_sign2 = 0UL;
    SignUtil::create_sign_md64("data_1", &data_view_sign1);
    SignUtil::create_sign_md64("data_2", &data_view_sign2);
    SignUtil::create_sign_md64("cumu_1", &cumulate_view_sign1);
    SignUtil::create_sign_md64("cumu_2", &cumulate_view_sign2);

    ThreeOrderTreeList list = { 
        { data_view_sign1, 
            {
                {cumulate_view_sign1, 2},
                {cumulate_view_sign2, 1}
            }
        }, 
        { data_view_sign2,
            {
                {cumulate_view_sign1, 1},
                {cumulate_view_sign2, 1}
            }
        }
    };
    EXPECT_TRUE(check_three_order_tree_item_node(node1, list));
    
    using anti::themis::common_lib::FileArchive;
    FileArchive ar;
    const char* cpt_file = "./tree_item.cpt";
    system("rm -f ./tree_item.cpt");
    ASSERT_TRUE(ar.open_w(cpt_file));

    ASSERT_TRUE(node1.serialize<FileArchive>(&ar));
    ar.close();

    ThreeOrderTreeItem new_node;
    FileArchive ar2;
    ASSERT_TRUE(ar2.open_r(cpt_file));

    ASSERT_TRUE(new_node.deserialize<FileArchive>(&ar2));
    ar2.close();
    EXPECT_TRUE(check_three_order_tree_item_node(new_node, list));
    system("rm -f ./tree_item.cpt");
}

TEST_F(ItemTestSuite, test_tree_item_statistic_by_root) {
    TreeItemStatisticCond cond;
    cond.statistic_level = TreeItemStatisticCond::ROOT;
    cond.leaf_condition = "distinct > 1";
    uint64_t data_view_sign1 = 0UL;
    uint64_t data_view_sign2 = 0UL;
    uint64_t cumulate_view_sign1 = 0UL;
    uint64_t cumulate_view_sign2 = 0UL;
    SignUtil::create_sign_md64("data_1", &data_view_sign1);
    SignUtil::create_sign_md64("data_2", &data_view_sign2);
    SignUtil::create_sign_md64("cumu_1", &cumulate_view_sign1);
    SignUtil::create_sign_md64("cumu_2", &cumulate_view_sign2);
    ThreeOrderTreeItem node1(data_view_sign1, cumulate_view_sign1, cond);
    ThreeOrderTreeItem node2(data_view_sign1, cumulate_view_sign1, cond);
    ThreeOrderTreeItem node3(data_view_sign1, cumulate_view_sign2, cond);
    ThreeOrderTreeItem node4(data_view_sign1, cumulate_view_sign1, cond);
    ThreeOrderTreeItem node5(data_view_sign1, cumulate_view_sign2, cond);
    EXPECT_EQ(0, node1.condition_value());
    node1 += node2;
    EXPECT_EQ(0, node1.condition_value());
    node1 += node3;
    EXPECT_EQ(1, node1.condition_value());
    node1 += node4;
    EXPECT_EQ(1, node1.condition_value());
    node1 += node5;
    EXPECT_EQ(1, node1.condition_value());
    ThreeOrderTreeItem node6(data_view_sign2, 111, cond);
    node1 += node6;
    EXPECT_EQ(1, node1.condition_value());
    node1 += node6;
    EXPECT_EQ(1, node1.condition_value());
    ThreeOrderTreeItem node7(data_view_sign2, 222, cond);
    node1 += node7;
    EXPECT_EQ(2, node1.condition_value());
    CWARNING_LOG("===================-==================");
    ThreeOrderTreeItem node8(data_view_sign2, 111, cond);
    node1 -= node8; 
    EXPECT_EQ(2, node1.condition_value());
    node1 -= node8; 
    EXPECT_EQ(1, node1.condition_value());
}

TEST_F(ItemTestSuite, test_tree_item_static_by_distinct_leaf) {
    TreeItemStatisticCond cond;
    cond.statistic_level = TreeItemStatisticCond::DISTINCT_LEAF;
    cond.leaf_condition = "distinct > 1";
    ThreeOrderTreeItem node1(1, 1001, cond);
    EXPECT_EQ(0, node1.condition_value());
    ThreeOrderTreeItem node2(1, 1001, cond);
    node1 += node2;
    EXPECT_EQ(0, node1.condition_value());
    ThreeOrderTreeItem node3(1, 1002, cond);
    node1 += node3;
    EXPECT_EQ(2, node1.condition_value());
    ThreeOrderTreeItem node4(2, 1003, cond);
    node1 += node4;
    EXPECT_EQ(2, node1.condition_value());
    ThreeOrderTreeItem node5(2, 1004, cond);
    node1 += node5;
    EXPECT_EQ(4, node1.condition_value());
    ThreeOrderTreeItem node6(3, 1003, cond);
    node1 += node6;
    EXPECT_EQ(4, node1.condition_value());
    ThreeOrderTreeItem node7(3, 1005, cond);
    node1 += node7;
    EXPECT_EQ(5, node1.condition_value());
}

/* vim: set ts=4 sw=4 sts=4 tw=100 */
