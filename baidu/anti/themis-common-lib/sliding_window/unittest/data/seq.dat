##################
# segment conf number
# conf_num  $conf_num   $padding    $padding    $padding
conf_num    1   0   0   0
##################
# segment conf
# step_len max_step_num segment_len $padding
begin_conf    300 9   1800    0
##################
# point or test
# point key   coord   cumu_value    $padding
## first segment start
## step1, 2
point   1104    1   1   0
point   1104    12  1   0
# range   key start_coord end_coord   expect_cumu
# segment key expect_cumu   $padding    $padding
# lastseg    key    expect_cumu $padding    $padding
#range   1102    0   1800    0
#range   1104    0   1800    2
segment 1104    2   0   0
lastseg    1104    2    0   0
## first segment, second step
## step2, 4
point   1104    300 1   0
segment 1104    3   0   0
point   1104    304 2   0
segment 1104    5   0   0
point   1104    599 1   0
segment 1104    6   0   0
## step3, 2
point   1104    600 1   0
segment 1104    7   0   0
lastseg 1104    7   0   0
point   1104    700 1   0
segment 1104    8   0   0
## step4, 0
## step5, 0
## step6, 1
point   1104    1799    1   0
segment 1104    9   0   0
## next segment ,to slide a step
## step7, 2
point   1104    1800    1   0
segment 1104    8   0   0
lastseg 1104    9   0   0
point   1104    1802    1   0
segment 1104    9   0   0
lastseg 1104    9   0   0
## next segment, to slide a step
## step8, 2
point   1104    2120    2   0
segment 1104    7   0   0
lastseg 1104    9   0   0
## step9, 1
point   1104    2430    1   0
segment 1104    6   0   0
lastseg 1104    7   0   0
## step10, 0
point   1104    2701    0   0
segment 1104    6   0   0
lastseg 1104    6   0   0
## step11, 0
point   1104    3001    0   0
segment 1104    6   0   0
lastseg 1104    6   0   0
## step12, 0
point   1104    3301    0   0
segment 1104    5   0   0
lastseg 1104    6   0   0
## step13, 0
point   1104    3601    0   0
segment 1104    3   0   0
lastseg 1104    5   0   0
## step14, 0
point   1104    3901    0   0
segment 1104    1   0   0
lastseg 1104    3   0   0
## step15, 0
point   1104    4201    0   0
segment 1104    0   0   0
lastseg 1104    1   0   0
## step16, 3
point   1104    4500    1   0
segment 1104    1   0   0
lastseg 1104    0   0   0
point   1104    4600    1   0
segment 1104    2   0   0
lastseg 1104    0   0   0
point   1104    4700    1   0
segment 1104    3   0   0
lastseg 1104    0   0   0
## step17,18,19,20,21,22,23 no
## step24, 1
point   1104    6900    1   0
segment 1104    1   0   0
lastseg 1104    0   0   0
## step21, 0 -> 2
point   1104    6230    2   0
segment 1104    3   0   0
lastseg 1104    2   0   0

## test insert
insert1  1104    6233    3   0
insert2  1104    1233    4   0
insert3  1104    9033    5   0

# end-a-segment-conf    $padding    $padding    $padding    $padding
end_conf 0   0   0   0
##################
##################

