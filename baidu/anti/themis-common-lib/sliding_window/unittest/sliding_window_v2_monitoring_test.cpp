// Copyright (c) 2025 Baidu.com, Inc. All Rights Reserved
// @Author: l<PERSON><PERSON><PERSON><PERSON>(<EMAIL>)

#include "sliding_window_v2_test_base.h"

/**
 * @brief SlidingWindowV2监控功能测试类
 */
class SlidingWindowV2MonitoringTest : public SlidingWindowTestBase {
protected:
    void SetUp() override {
        SlidingWindowTestBase::SetUp();
    }

    void TearDown() override {
        SlidingWindowTestBase::TearDown();
    }
};

/**
 * @brief monitor基本功能测试
 */
TEST_F(SlidingWindowV2MonitoringTest, test_monitor_basic_functionality) {
    auto test_data = generate_test_data(5000);
    size_t success_count = 0;
    for (const auto& data : test_data) {
        if (_pool_sw->enter(data.key, data.coord, data.item)) {
            success_count++;
        }
    }

    bool monitor_ok = verify_monitor_basics(*_pool_sw);
    EXPECT_TRUE(monitor_ok);

    if (monitor_ok) {
        bsl::ResourcePool rp;
        bsl::var::Dict dict;
        _pool_sw->monitor(dict, rp);

        std::cout << "监控信息: "
                  << "总元素数=" << dict["TOTAL_ELEMENTS"].to_uint64()
                  << ", 滑动窗口开销=" << dict["SLIDING_WINDOW_OVERHEAD"].to_uint64() << "bytes";
        if (!dict.get("DATA_MEM").is_null()) {
            std::cout << ", 数据内存=" << dict["DATA_MEM"].to_uint64() 
                      << ", 总开销=" << dict["TOTAL_OVERHEAD"].to_uint64();
        }
        std::cout << std::endl;
    }
}

/**
 * @brief monitor性能测试
 */
TEST_F(SlidingWindowV2MonitoringTest, test_monitor_performance) {
    auto test_data = generate_test_data(10000);
    for (const auto& data : test_data) {
        _pool_sw->enter(data.key, data.coord, data.item);
    }

    const int iterations = 100;
    auto start = std::chrono::high_resolution_clock::now();

    for (int i = 0; i < iterations; ++i) {
        bsl::ResourcePool rp;
        bsl::var::Dict dict;
        _pool_sw->monitor(dict, rp);
    }

    auto end = std::chrono::high_resolution_clock::now();
    auto total_time = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    auto avg_time = total_time / iterations;

    std::cout << "monitor平均调用时间: " << avg_time.count() << "μs" << std::endl;

    EXPECT_LT(avg_time.count(), 10000);
}

/**
 * @brief monitor边界情况测试
 */
TEST_F(SlidingWindowV2MonitoringTest, test_monitor_edge_cases) {
    // 测试空容器
    bool empty_ok = verify_monitor_basics(*_pool_sw);
    EXPECT_TRUE(empty_ok);

    bsl::ResourcePool empty_rp;
    bsl::var::Dict empty_dict;
    _pool_sw->monitor(empty_dict, empty_rp);

    try {
        uint64_t empty_total = empty_dict["TOTAL_ELEMENTS"].to_uint64();
        EXPECT_EQ(empty_total, 0);
    } catch (...) {
        ADD_FAILURE() << "读取空容器monitor信息失败";
    }

    // 测试单元素
    SegmentItemV2 single_item(42);
    bool insert_ok = _pool_sw->enter(12345, 1000000, single_item);
    ASSERT_TRUE(insert_ok);

    bool single_ok = verify_monitor_basics(*_pool_sw);
    EXPECT_TRUE(single_ok);

    // 测试批量数据
    auto test_data = generate_test_data(1000);
    for (const auto& data : test_data) {
        _pool_sw->enter(data.key, data.coord, data.item);
    }

    bool batch_ok = verify_monitor_basics(*_pool_sw);
    EXPECT_TRUE(batch_ok);
}

/**
 * @brief 监控精确性验证测试
 * 验证监控统计数据与实际状态的精确匹配
 */
TEST_F(SlidingWindowV2MonitoringTest, test_monitoring_accuracy_verification) {
    auto test_data = generate_test_data(500);
    size_t actual_inserts = 0;

    for (const auto& data : test_data) {
        if (_pool_sw->enter(data.key, data.coord, data.item)) {
            actual_inserts++;
        }
    }

    bsl::ResourcePool rp;
    bsl::var::Dict dict;
    _pool_sw->monitor(dict, rp);

    // 验证关键统计的精确性
    uint64_t total_elements = dict["TOTAL_ELEMENTS"].to_uint64();

    // 验证总元素数
    uint64_t expected_total = _pool_sw->segment_view_sign_size() + _pool_sw->window_view_sign_size();
    EXPECT_EQ(total_elements, expected_total);

    // 验证滑动窗口开销内存大于0
    uint64_t sliding_window_overhead = dict["SLIDING_WINDOW_OVERHEAD"].to_uint64();
    EXPECT_GT(sliding_window_overhead, 0ULL);

    std::cout << "监控精确性验证: total=" << total_elements 
              << ", sliding_window_overhead=" << sliding_window_overhead << " bytes";
    if (!dict.get("TOTAL_OVERHEAD").is_null()) {
        uint64_t total_overhead = dict["TOTAL_OVERHEAD"].to_uint64();
        std::cout << ", total_overhead=" << total_overhead;
    }
    std::cout << std::endl;
}