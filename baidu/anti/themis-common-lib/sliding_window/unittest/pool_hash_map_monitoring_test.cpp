// Copyright (c) 2025 Baidu.com, Inc. All Rights Reserved
// @Author: l<PERSON><PERSON><PERSON><PERSON>(<EMAIL>)

#include "pool_hash_map_test_base.h"

/**
 * @brief PoolHashMap监控输出测试类
 * 验证monitor()方法的输出完整性和准确性
 */
class PoolHashMapMonitoringTest : public PoolHashMapTestBase {
protected:
    void SetUp() override {
        PoolHashMapTestBase::SetUp();
    }

    void TearDown() override {
        PoolHashMapTestBase::TearDown();
    }
};

/**
 * @brief Monitor基本字段测试
 * 验证monitor()方法输出包含所有必需字段
 */
TEST_F(PoolHashMapMonitoringTest, test_monitor_basic_fields) {
    // 测试空HashMap
    bool empty_ok = verify_monitor_fields(_pool_map);
    EXPECT_TRUE(empty_ok);

    // 填充测试数据
    auto test_data = generate_test_data(10000);
    for (const auto& item : test_data) {
        _pool_map->emplace(item.first, item.second);
    }

    // 测试有数据HashMap
    bool filled_ok = verify_monitor_fields(_pool_map);
    EXPECT_TRUE(filled_ok);

    // 获取基本监控信息
    bsl::ResourcePool rp;
    bsl::var::Dict& dict = rp.create<bsl::var::Dict>();
    _pool_map->monitor(dict, rp);

    uint64_t overhead_mem = dict["OVERHEAD_MEM"].to_uint64();

    // 基本验证 - 节点数通过size()方法直接获取
    EXPECT_EQ(test_data.size(), _pool_map->size());
    EXPECT_GT(overhead_mem, 0ULL);
}

/**
 * @brief Monitor数值准确性测试
 * 验证monitor输出的数值准确性
 */
TEST_F(PoolHashMapMonitoringTest, test_monitor_values_accuracy) {
    // 分阶段插入数据，验证monitor数值变化
    const size_t batch_size = 2000;
    const size_t batches = 5;

    for (size_t batch = 0; batch < batches; ++batch) {
        // 插入当前批次数据
        auto batch_data = generate_test_data(batch_size, batch * batch_size);
        for (const auto& item : batch_data) {
            _pool_map->emplace(item.first, item.second);
        }

        // 验证monitor数值
        bsl::ResourcePool rp;
        bsl::var::Dict& dict = rp.create<bsl::var::Dict>();
        _pool_map->monitor(dict, rp);

        uint64_t overhead_mem = dict["OVERHEAD_MEM"].to_uint64();

        size_t expected_size = (batch + 1) * batch_size;

        EXPECT_EQ(expected_size, _pool_map->size());
        EXPECT_GT(overhead_mem, 0ULL);
    }
}

/**
 * @brief Monitor边界情况测试
 * 测试monitor在各种边界情况下的行为
 */
TEST_F(PoolHashMapMonitoringTest, test_monitor_edge_cases) {
    // 测试1: 空HashMap
    bsl::ResourcePool rp1;
    bsl::var::Dict& empty_dict = rp1.create<bsl::var::Dict>();
    _pool_map->monitor(empty_dict, rp1);

    uint64_t empty_overhead_mem = empty_dict["OVERHEAD_MEM"].to_uint64();

    EXPECT_EQ(0U, _pool_map->size());
    EXPECT_GT(empty_overhead_mem, 0ULL);  // 对象本身和bucket数组仍占用内存

    // 测试2: 单元素HashMap
    _pool_map->emplace(12345, 67890);

    bsl::ResourcePool rp2;
    bsl::var::Dict& single_dict = rp2.create<bsl::var::Dict>();
    _pool_map->monitor(single_dict, rp2);

    uint64_t single_overhead_mem = single_dict["OVERHEAD_MEM"].to_uint64();

    EXPECT_EQ(1U, _pool_map->size());
    EXPECT_GT(single_overhead_mem, 0ULL);

    // 测试3: 大量删除后
    for (int i = 1; i <= 1000; ++i) {
        _pool_map->emplace(i, i * 100);
    }

    // 删除99%的元素
    for (int i = 1; i <= 990; ++i) {
        _pool_map->remove(i);
    }

    bsl::ResourcePool rp3;
    bsl::var::Dict& sparse_dict = rp3.create<bsl::var::Dict>();
    _pool_map->monitor(sparse_dict, rp3);

    uint64_t sparse_overhead_mem = sparse_dict["OVERHEAD_MEM"].to_uint64();

    EXPECT_EQ(11U, _pool_map->size());
    EXPECT_GT(sparse_overhead_mem, 0ULL);

    // 测试4: 清空后
    _pool_map->clear();

    bsl::ResourcePool rp4;
    bsl::var::Dict& cleared_dict = rp4.create<bsl::var::Dict>();
    _pool_map->monitor(cleared_dict, rp4);

    uint64_t cleared_overhead_mem = cleared_dict["OVERHEAD_MEM"].to_uint64();

    EXPECT_EQ(0U, _pool_map->size());
    EXPECT_GT(cleared_overhead_mem, 0ULL);  // 对象和bucket数组内存仍存在
}