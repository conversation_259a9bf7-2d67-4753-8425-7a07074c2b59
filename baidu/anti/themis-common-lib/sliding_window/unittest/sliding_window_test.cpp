/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * $Id$ 
 * 
 **************************************************************************/
 
/**
 * @file sliding_window/unittest/sliding_window_test.cpp
 * <AUTHOR>
 * @date 2015/04/02 14:15:16
 * @version 1.0.0.0 
 * @brief 
 *  
 **/

#include <stdio.h>
#include <stdlib.h>
#include <ul_file.h>
#include <gtest/gtest.h>
#include <bmock.h>
#include "archive.h"
#include "sliding_window.h"
#include "item.h"

using anti::themis::common_lib::SlidingWindow;
using anti::themis::common_lib::SegmentItem;
using std::string;

typedef SlidingWindow<SegmentItem> SldWin;

namespace anti {
namespace themis {
namespace common_lib {

/////////////////////

struct SWParam {
    int idx;
    int64_t step_len;
    int max_step_num;
    int64_t segment_len;
    bool expect_retval;
    string case_desc;
};

enum {
    INIT_CASE_NUM = 10,
    ENTER_CASE_NUM = 3
};

SWParam g_param_table[INIT_CASE_NUM] =  
{
    {0, 300, 7, 1800, true, "ok, edge"},
    {1, 300, 6, 1800, false, "error, edge"},
    {2, 300, 4, 1800, false, "error, less step num"},
    {3, 300, 0, 1800, false, "error, zero step len"},
    {4, 300, -1, 1800, false, "error, negative step len"},
    {5, 300, 20, 1800, true, "ok, buff step enough"},
    {6, 0, 3, 20, false, "error, zero step len"},
    {7, -2, 3, 40, false, "error, negative step len"},
    {8, 300, 4, 500, false, "error, can't exact division"},
    {9, 300, 5, 0, false, "error, segment len error"}
};

/////////////////////

void check_sldwin(const SldWin& sw, const SWParam& p) {
    EXPECT_EQ(sw._step_len, p.step_len);
    EXPECT_EQ(sw._step_num, p.segment_len / p.step_len);
    EXPECT_EQ(sw._max_step_num, p.max_step_num);
    EXPECT_EQ(sw._segment_len, p.segment_len);
    EXPECT_EQ(sw._oldest_coord, 
            ((p.segment_len / p.step_len) - p.max_step_num) * p.step_len);
    EXPECT_EQ(sw._segment_start_coord, 0);
    EXPECT_EQ(sw._latest_coord, 0);
    ASSERT_TRUE(sw._segment != NULL);
    ASSERT_TRUE(sw._window != NULL);
    EXPECT_EQ(sw._window->size(), static_cast<size_t>(p.max_step_num));
    for (int i = 0; i < p.max_step_num; i++) {
        ASSERT_TRUE(sw._window->at(i) !=  NULL);
        EXPECT_TRUE(sw._window->at(i)->empty());
    }
    return ;
}

void skip_annotation(FILE* fp) {
    assert(fp != NULL);
    int ch = 0;
    while (true) {
        ch = getc(fp);
        // eof
        if (ch == EOF) {
            break ;
        }
        // not annotation
        if (ch != '#') {
            ungetc(ch, fp);
            break ;
        }
        // annotation
        char* line = NULL;
        size_t n = 0;
        if (-1 == getline(&line, &n, fp)) {
            return ;
        }
        if (line != NULL) {
            free(line);
            line = NULL;
        }
    }
    return ;
}

}  // end namespace common_lib
}  // end namespace themis
}  // end namespace anti

using anti::themis::common_lib::g_param_table;
using anti::themis::common_lib::SWParam;

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}

class SlidingWindowTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
    }

    virtual void TearDown() {
    }

    static void SetUpTestCase() {}
};

TEST_F(SlidingWindowTestSuite, init_case) {
    for (size_t i = 0; i < sizeof(g_param_table) / sizeof(g_param_table[0]); i++) {
        SldWin sw;
        CWARNING_LOG(">>>>>>>test case: idx=%d, info=(%s)<<<<<<<<", 
                g_param_table[i].idx, g_param_table[i].case_desc.c_str());
        EXPECT_EQ(sw.init(g_param_table[i].step_len, 
                    g_param_table[i].max_step_num, 
                    g_param_table[i].segment_len), 
                g_param_table[i].expect_retval);
        if (g_param_table[i].expect_retval) {
            check_sldwin(sw, g_param_table[i]);
        }
    }
}

TEST_F(SlidingWindowTestSuite, uninit_case) {
    SldWin sw;
    ASSERT_TRUE(sw.init(300, 7, 1800));
    sw.uninit();
    EXPECT_EQ(sw._step_len, 0);
    EXPECT_EQ(sw._step_num, 0);
    EXPECT_EQ(sw._max_step_num, 0);
    EXPECT_EQ(sw._segment_len, 0);
    EXPECT_EQ(sw._oldest_coord, 0);
    EXPECT_EQ(sw._segment_start_coord, 0);
    EXPECT_EQ(sw._latest_coord, 0);
    EXPECT_TRUE(sw._segment == NULL);
    EXPECT_TRUE(sw._window == NULL);
}

TEST_F(SlidingWindowTestSuite, _check_segment_arg_case) {
    SldWin sw;
    for (size_t i = 0; i < sizeof(g_param_table) / sizeof(g_param_table[0]); i++) {
        CWARNING_LOG(">>>>case idx:%d, info=(%s)<<<<", 
                g_param_table[i].idx, g_param_table[i].case_desc.c_str());
        EXPECT_TRUE(g_param_table[i].expect_retval == 
                sw._check_segment_arg(
                        g_param_table[i].step_len,
                        g_param_table[i].max_step_num,
                        g_param_table[i].segment_len));
    }
}

TEST_F(SlidingWindowTestSuite, sliding_stat_case) {
    const char* data_filepath = "./data/seq.dat";
    FILE* fp = fopen(data_filepath, "r");
    assert(fp != NULL);
    char cmd[16];
    bzero(cmd, sizeof(cmd));
    int a = 0;
    int b = 0;
    int c = 0;
    int d = 0;
    
    // read segment conf num
    anti::themis::common_lib::skip_annotation(fp);
    ASSERT_EQ(5, fscanf(fp, "%s\t%d\t%d\t%d\t%d\n", cmd, &a, &b, &c, &d));
    if (strcmp(cmd, "conf_num") != 0) {
        CFATAL_LOG("error cmd, (%s)", cmd);
        ASSERT_TRUE(false);
    }

    int segment_conf_num = a;
    for (int seg_conf = 0; seg_conf < segment_conf_num; ++seg_conf) {
        // read segment conf
        anti::themis::common_lib::skip_annotation(fp);
        ASSERT_EQ(5, fscanf(fp, "%s\t%d\t%d\t%d\t%d\n", cmd, &a, &b, &c, &d));
        if (strcmp(cmd, "begin_conf") != 0) {
            CFATAL_LOG("error cmd, (%s)", cmd);
            ASSERT_TRUE(false);
        }
        int step_len = a;
        int max_step_num = b;
        int segment_len = c;
        CWARNING_LOG("start a segment, step_len=%d max_step_num=%d segment_len=%d",
                step_len, max_step_num, segment_len);
        SldWin sw;
        ASSERT_TRUE(sw.init(step_len, max_step_num, segment_len));
        SldWin sw2;
        ASSERT_TRUE(sw2.init(step_len, max_step_num, segment_len));

        uint64_t key = 0UL;
        int64_t coord = 0L;
        SegmentItem item;
        int64_t expect_cumulant = 0L;

        int64_t latest_coord = 0L;

        // read point or test
        while (true) {
            anti::themis::common_lib::skip_annotation(fp);
            ASSERT_EQ(5, fscanf(fp, "%s\t%d\t%d\t%d\t%d\n", cmd, &a, &b, &c, &d));
            if (strcmp(cmd, "point") == 0) {
                // deal point enter
                key = a;
                coord = b;
                item._cumulant = c;
                CWARNING_LOG("enter a point, key=%llu coord=%lld cumu=%lld", 
                        key, coord, item._cumulant);
                if (coord > latest_coord) {
                    latest_coord = coord;
                }

                // test enter
                ASSERT_TRUE(sw.enter(key, coord, item));

                // test enter and query
                const SegmentItem* p_seg_item;
                ASSERT_TRUE(sw2.enter(key, coord, item, &p_seg_item));
                ASSERT_TRUE(p_seg_item != NULL);
                const SegmentItem* item_q = sw2.query_segment(key);
                EXPECT_EQ(item_q->_cumulant, p_seg_item->_cumulant);
            } else if (strcmp(cmd, "segment") == 0) {
                // deal segment query test
                key = a;
                expect_cumulant = b;
                CWARNING_LOG("query segment, key=%llu expect=%lld",
                        key, expect_cumulant);

                // test query_segment
                const SegmentItem* q = sw.query_segment(key);
                EXPECT_EQ(q->_cumulant, expect_cumulant);

                // test latest_coord
                EXPECT_EQ(latest_coord, sw.latest_coord());
            } else if (strcmp(cmd, "lastseg") == 0) {
                // deal last segment query test
                key = a;
                expect_cumulant = b;
                CWARNING_LOG("query last segment, key=%llu expect=%lld",
                        key, expect_cumulant);

                // test query_last_segment
                SegmentItem q = sw.query_last_segment(key);
                EXPECT_EQ(q._cumulant, expect_cumulant);
            } else if (strncmp(cmd, "insert", 6) == 0) {
                key = a;
                coord = b;
                item._cumulant = c;
                CWARNING_LOG("enter a point, key=%llu coord=%lld cumu=%lld", 
                        key, coord, item._cumulant);
                if (coord > latest_coord) {
                    latest_coord = coord;
                }

                // test insert and query
                const SegmentItem* p_seg_item;
                int ret = sw.insert(key, coord, item);
                int ret2 = sw2.insert(key, coord, item, &p_seg_item);
                if (strcmp(cmd, "insert1") == 0) {
                    EXPECT_EQ(ret, anti::themis::common_lib::SLIDING_WINDOW_SUCC);
                    EXPECT_EQ(ret2, anti::themis::common_lib::SLIDING_WINDOW_SUCC);
                } else if (strcmp(cmd, "insert2") == 0) {
                    EXPECT_EQ(ret, anti::themis::common_lib::SLIDING_WINDOW_FAIL);
                    EXPECT_EQ(ret2, anti::themis::common_lib::SLIDING_WINDOW_FAIL);
                } else if (strcmp(cmd, "insert3") == 0) {
                    EXPECT_EQ(ret, anti::themis::common_lib::SLIDING_WINDOW_SLIDE);
                    EXPECT_EQ(ret2, anti::themis::common_lib::SLIDING_WINDOW_SLIDE);
                }
                ASSERT_TRUE(p_seg_item != NULL);
                const SegmentItem* item_q = sw2.query_segment(key);
                EXPECT_EQ(item_q->_cumulant, p_seg_item->_cumulant);
            } else if (strcmp(cmd, "end_conf") == 0) {
                // end this segment conf test suite
                CWARNING_LOG("end a segment conf");
                break;
            } else {
                CFATAL_LOG("error cmd, (%s)", cmd);
                ASSERT_TRUE(false);
            };
        }  // end while
        sw._clear();
    }  // end for
}

TEST_F(SlidingWindowTestSuite, serialize_deserialize_case) {
    // build base window
    SldWin sw;
    uint64_t step_len = 300;
    int max_step_num = 7;
    uint64_t segment_len = 1800;
    ASSERT_TRUE(sw.init(300, 7, 1800));

    // push some point into base window
    uint64_t key = 1104UL;
    int64_t coord = 0;
    SegmentItem item(1);
    uint64_t key2 = 3585UL;
    SegmentItem item2 = 2;
    int item_num = 5222;
    for (int i = 0; i < item_num; i++) {
        sw.enter(key, coord, item); 
        sw.enter(key2, coord, item2);
        for (int j = 0; j < 2; j++) {
            uint64_t key_tmp = rand() % 100000;
            if (key_tmp == key || key_tmp == key2) {
                continue;
            }
            SegmentItem item_tmp = rand() % 10;
            sw.enter(key_tmp, coord, item_tmp);
        }
        ++coord;
    }

    // check point push ok
    const SegmentItem* item_q1 = sw.query_segment(key);
    const SegmentItem* item_q2 = sw.query_segment(key2);
    SegmentItem item_q1_l = sw.query_last_segment(key);
    int valid_node = segment_len - step_len + item_num % step_len;
    EXPECT_EQ(item_q1->_cumulant, valid_node);
    EXPECT_EQ(item_q2->_cumulant, valid_node << 1);

    // dump base window
    std::string ckpt_filepath = "./sw.cpt";
    system("rm -rf ./sw.cpt");
    anti::themis::common_lib::FileArchive ar;
    ASSERT_TRUE(ar.open_w(ckpt_filepath.c_str()));
    sw.serialize<anti::themis::common_lib::FileArchive>(&ar);
    ar.flush();
    ASSERT_TRUE(ar.close());

    // load base cpt to diff window
    anti::themis::common_lib::FileArchive ar_read;
    ASSERT_TRUE(ar_read.open_r(ckpt_filepath.c_str()));
    SldWin sw2;
    ASSERT_TRUE(sw2.init(step_len, max_step_num + 1, segment_len));
    ASSERT_TRUE(sw2.deserialize<anti::themis::common_lib::FileArchive>(&ar_read));
    ar_read.close();
    const SegmentItem* item_d1 = sw2.query_segment(key);
    const SegmentItem* item_d2 = sw2.query_segment(key2);
    SegmentItem item_d1_l = sw2.query_last_segment(key);
    EXPECT_EQ(item_d1->_cumulant, item_q1->_cumulant);
    EXPECT_EQ(item_d2->_cumulant, item_q2->_cumulant);
    EXPECT_EQ(item_d1_l._cumulant, item_q1_l._cumulant);

    // load base cpt to ck window
    SldWin sw_ck;
    ASSERT_TRUE(sw_ck.init(step_len, max_step_num, segment_len));
    anti::themis::common_lib::FileArchive ar_ck_r;
    ASSERT_TRUE(ar_ck_r.open_r(ckpt_filepath.c_str()));
    ASSERT_TRUE(sw_ck.deserialize<anti::themis::common_lib::FileArchive>(&ar_ck_r));
    ar_ck_r.close();

    // dump ck window cpt
    std::string ckpt_filepath_ck = "./sw_ck.cpt";
    system("rm -rf ./sw_ck.cpt");
    anti::themis::common_lib::FileArchive ar_ck_w;
    ASSERT_TRUE(ar_ck_w.open_w(ckpt_filepath_ck.c_str()));
    ASSERT_TRUE(sw_ck.serialize<anti::themis::common_lib::FileArchive>(&ar_ck_w));
    ar_ck_w.flush();
    ar_ck_w.close();

    // check ck and base window's cpt filesize is equal
    off_t fz_base = ul_fsize("./", ckpt_filepath.c_str());
    off_t fz_ck = ul_fsize("./", ckpt_filepath_ck.c_str());
    CWARNING_LOG("sliding window cpt file size is %ld %ld Bytes", fz_base, fz_ck);
    EXPECT_TRUE(fz_base != (off_t)(-1));
    EXPECT_EQ(fz_base, fz_ck);

    // test only max_step_num become bigger
    SldWin sw3;
    ASSERT_TRUE(sw3.init(300, 8, 2100));
    ASSERT_TRUE(ar_read.open_r(ckpt_filepath.c_str()));
    EXPECT_FALSE(sw3.deserialize<anti::themis::common_lib::FileArchive>(&ar_read));
    ar_read.close();

    // test Archive ar is error
    anti::themis::common_lib::FileArchive ar_error;
    ASSERT_TRUE(ar_error.open_w(ckpt_filepath.c_str()));
    EXPECT_FALSE(sw3.deserialize<anti::themis::common_lib::FileArchive>(&ar_error));
    
    system("rm -rf ./sw.cpt  ./sw_ck.cpt");

    SldWin sw4;
    EXPECT_TRUE(sw4.init(300, 7, 1800));
    anti::themis::common_lib::FileArchive ar4;
    sw4._segment_len = 100000;
    EXPECT_FALSE(sw4.deserialize<anti::themis::common_lib::FileArchive>(&ar4));
    EXPECT_FALSE(sw4.serialize<anti::themis::common_lib::FileArchive>(&ar4));
    sw4.uninit();
    ASSERT_TRUE(sw4.init(300, 7, 1800));
    delete sw4._window->at(0);
    (*(sw4._window))[0] = NULL;
    EXPECT_FALSE(sw4.serialize<anti::themis::common_lib::FileArchive>(&ar4));
    sw4.uninit();
    ASSERT_TRUE(sw4.init(300, 7, 1800));
    ASSERT_TRUE(sw4.enter(1104, 1, 1));
    ASSERT_TRUE(sw4.enter(1104, 20, 1));
    ASSERT_TRUE(sw4.enter(1104, 300, 1));
    ASSERT_TRUE(sw4.enter(1104, 600, 1));
    ASSERT_TRUE(sw4.enter(1104, 900, 1));
    sw4._segment_start_coord = 1000000;
    EXPECT_FALSE(sw4.serialize<anti::themis::common_lib::FileArchive>(&ar4));
    sw4.uninit();
}

TEST_F(SlidingWindowTestSuite, latest_coord_case) {
    SldWin sw;
    ASSERT_TRUE(sw.init(300, 7, 1800));
    ASSERT_TRUE(sw.enter(1104, 20, 1));
    EXPECT_EQ(sw.latest_coord(), 20);
    ASSERT_TRUE(sw.enter(1104, 13, 1));
    EXPECT_EQ(sw.latest_coord(), 20);
    ASSERT_TRUE(sw.enter(1104, 33, 1));
    EXPECT_EQ(sw.latest_coord(), 33);
}

TEST_F(SlidingWindowTestSuite, ctor_dtor_case) {
    SldWin* p_sw = new SldWin();
    ASSERT_TRUE(p_sw != NULL);
    ASSERT_TRUE(p_sw->init(300, 7, 1800));
    ASSERT_TRUE(p_sw->enter(1104, 2, 1));
    p_sw->_clear();
    delete p_sw; 
    p_sw = NULL;

    p_sw = new SldWin();
    ASSERT_TRUE(p_sw->init(300, 7, 1800));
    delete p_sw->_window->at(0);
    (*(p_sw->_window))[0] = NULL;
    p_sw->uninit();
    delete p_sw;
    p_sw = NULL;
}

TEST_F(SlidingWindowTestSuite, mix_case) {
    SldWin sw;
    ASSERT_TRUE(sw.init(300, 7, 1800));
    ASSERT_TRUE(sw.enter(1104, 0, 1));
    ASSERT_TRUE(sw.enter(1104, 1, 1));
    ASSERT_TRUE(sw.enter(1104, 20, 1));
    ASSERT_TRUE(sw.enter(1104, 300, 1));
    ASSERT_TRUE(sw.enter(1104, 600, 1));
    ASSERT_TRUE(sw.enter(1104, 900, 1));
    ASSERT_TRUE(sw.enter(1104, 1200, 1));
    ASSERT_TRUE(sw.enter(1104, 1500, 1));
    sw._clear();
}

class MockArchive {
public:
    virtual int read(void* , size_t count) {
        return count;
    }
    virtual int write(const void* , size_t count) {
        return count;
    }
};

BMOCK_CLASS_METHOD2(MockArchive, read, int(void*, size_t));
BMOCK_CLASS_METHOD2(MockArchive, write, int(const void*, size_t));
using ::testing::Return;
using ::testing::_;

TEST_F(SlidingWindowTestSuite, serialize_case_1) {
    SldWin sw;
    MockArchive ar;

    sw._step_len = 300;
    sw._max_step_num = 7;
    sw._segment_len = 1800;
    EXPECT_FALSE(sw.serialize<MockArchive>(&ar));
}

TEST_F(SlidingWindowTestSuite, serialize_case_2) {
    SldWin sw;
    MockArchive ar;
    sw._step_len = 300;
    sw._max_step_num = 2;
    sw._segment_len = 1800;
    EXPECT_FALSE(sw.serialize<MockArchive>(&ar));
}

TEST_F(SlidingWindowTestSuite, serialize_case_2_1) {
    SldWin sw;
    MockArchive ar;

    ASSERT_TRUE(sw.init(300, 7, 1800));
    sw._segment_start_coord = 1000000;
    EXPECT_FALSE(sw.serialize<MockArchive>(&ar));
    sw.uninit();
}
 
TEST_F(SlidingWindowTestSuite, serialize_case_3) {
    SldWin sw;
    MockArchive ar;

    ASSERT_TRUE(sw.init(300, 7, 1800));
    (*(sw._window))[0] = NULL; // mem leak, don't care
    EXPECT_FALSE(sw.serialize<MockArchive>(&ar));
    sw.uninit();
}

TEST_F(SlidingWindowTestSuite, serialize_case_4) {
    SldWin sw;
    MockArchive ar;

    ASSERT_TRUE(sw.init(300, 7, 1800));
    SegmentItem si = 1;
    EXPECT_TRUE(sw.enter(1104, 1, si));
    EXPECT_TRUE(sw.enter(1104, 200, si));
    EXPECT_TRUE(sw.enter(1104, 900, si));
    EXPECT_CALL(BMOCK_CLASS_OBJECT(MockArchive, write), write(_, _))
            .WillOnce(Return(0));
    EXPECT_FALSE(sw.serialize<MockArchive>(&ar));
    EXPECT_CALL(BMOCK_CLASS_OBJECT(MockArchive, write), write(_, _))
            .WillOnce(Return(32))
            .WillOnce(Return(0));
    EXPECT_FALSE(sw.serialize<MockArchive>(&ar));
    EXPECT_CALL(BMOCK_CLASS_OBJECT(MockArchive, write), write(_, _))
            .WillOnce(Return(32))
            .WillOnce(Return(8))
            .WillOnce(Return(0));
    EXPECT_FALSE(sw.serialize<MockArchive>(&ar));
    sw.uninit();
}

TEST_F(SlidingWindowTestSuite, case_slide_to_coord) {
    SldWin sw;
    ASSERT_TRUE(sw.init(300, 8, 1800));
    uint64_t key = 1104u;
    SegmentItem item(1);

    int64_t coord = 1;
    ASSERT_TRUE(sw.enter(key, coord, item, NULL));
    coord = 301;
    ASSERT_TRUE(sw.enter(key, coord, item, NULL));
    coord = 1801;
    ASSERT_TRUE(sw.enter(key, coord, item, NULL));
    coord = 10023;
    ASSERT_TRUE(sw.enter(key, coord, item, NULL));
    EXPECT_EQ(sw._latest_coord, coord);
    EXPECT_EQ(sw._oldest_coord, coord - (coord % 300) - 2100);
    EXPECT_EQ(sw._segment_start_coord, coord - (coord % 300) - 1500);
}

// TestCase for ContinueCoordinate
using anti::themis::common_lib::ContinueCoordinate;

class ContinueCoordinateTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {
    }

    virtual void TearDown() {
    }

    static void SetUpTestCase() {}
};

TEST_F(ContinueCoordinateTestSuite, push) {
    ContinueCoordinate segments;

    segments.push(0, 10);

    EXPECT_EQ(segments._coords.size(), 1u);
    EXPECT_EQ(segments.first_coord(), 0);
}

TEST_F(ContinueCoordinateTestSuite, push_continue_coordinate) {
    // push
    // -> [0, 10)
    // -> [10, 20)
    // then get [0, 20)
    ContinueCoordinate segments;
    segments.push(0, 10);
    segments.push(10, 10);

    EXPECT_EQ(segments._coords.size(), 1u);
    EXPECT_EQ(segments.first_coord(), 0);

    segments.erase(0, 10);
    EXPECT_EQ(segments.first_coord(), 10);
}

TEST_F(ContinueCoordinateTestSuite, push_uncontinue_coordinate) {
    // push
    // -> [0, 10)
    // -> [20, 10)
    
    ContinueCoordinate segments;
    segments.push(0, 10);
    segments.push(20, 10);

    EXPECT_EQ(segments._coords.size(), 2u);
    EXPECT_EQ(segments.first_coord(), 0);

    segments.erase(0, 10);
    EXPECT_EQ(segments.first_coord(), 20);
}

TEST_F(ContinueCoordinateTestSuite, push_continue_coordinate_unorder) {
    // push
    // -> [0, 10)
    // -> [20, 10)
    // -> [50, 10)
    // -> [40, 10)
    // -> [40, 10)
    // -> [10, 10)
    ContinueCoordinate segments;
    segments.push(0, 10);
    segments.push(20, 10);
    EXPECT_EQ(segments._coords.size(), 2u);
    segments.push(50, 10);
    EXPECT_EQ(segments._coords.size(), 3u);
    segments.push(40, 10);
    EXPECT_EQ(segments._coords.size(), 3u);
    segments.push(10, 10);
    EXPECT_EQ(segments._coords.size(), 3u);

    int64_t target[] = {0, 20, 50};
    int idx = 0;
    while (segments.first_coord() != -1) {
        EXPECT_EQ(segments.first_coord(), target[idx++]) << " " << idx - 1;
        segments.erase(segments.first_coord(), 10);
    }
}

TEST_F(ContinueCoordinateTestSuite, push_continue_coordinate_by_merge) {
    // push
    // -> [0, 10)
    // -> [20, 10)
    // -> [10, 10)
    ContinueCoordinate segments;
    segments.push(0, 10);
    segments.push(20, 10);
    EXPECT_EQ(segments._coords.size(), 2u);
    segments.push(10, 10);
    EXPECT_EQ(segments._coords.size(), 2u);

    int64_t target[] = {0, 20};
    int idx = 0;
    while (segments.first_coord() != -1) {
        EXPECT_EQ(segments.first_coord(), target[idx++]);
        segments.erase(segments.first_coord(), 10);
    }
}

//-- online case ---
TEST(SlidingWindowCase, bad_case) {
    SldWin sw;

    ASSERT_TRUE(sw.init(450, 10, 3600));
    sw.set_log_key_first_appear();

    SegmentItem item(1);

    int64_t coord[] = {1445311998, 1445312250, 1445312259};
    
    sw.enter(123, coord[0], item);
    printf("---> %ld\n", sw.query_start_coord(123));
    EXPECT_EQ(sw.query_start_coord(123), 1445311800);
    sw.enter(123, coord[1], item);
    printf("---> %ld\n", sw.query_start_coord(123));
    EXPECT_EQ(sw.query_start_coord(123), 1445311800);
    sw.enter(123, coord[2], item);
    printf("---> %ld\n", sw.query_start_coord(123));
    EXPECT_EQ(sw.query_start_coord(123), 1445311800);
}

TEST(SlidingWindowCase, query_segment_with_coord_case) {
    SldWin sw;
    ASSERT_TRUE(sw.init(450, 10, 3600));
    // too old coord
    EXPECT_TRUE(sw.query_segment(0, -1000) == NULL);

    SegmentItem item;
    item._cumulant = 5;
    ASSERT_TRUE(sw.enter(123, 10, item));
    EXPECT_TRUE(sw.query_segment(123, 10) != NULL);
}

TEST(SlidingWindowCase, test_sliding_segment_start_coord) {
    SldWin sw;
    EXPECT_EQ(1526428800, sw.sliding_segment_start_coord(1526482800, 86400, 1));
    EXPECT_EQ(1526457600, sw.sliding_segment_start_coord(1526482800, 28800, 1));
}



/* vim: set ts=4 sw=4 sts=4 tw=100 */
