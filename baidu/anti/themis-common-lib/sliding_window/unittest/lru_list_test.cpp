// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: test_lru_list.cpp
// @Last modified: 2017-12-26 18:39:27
// @Brief: 

#include <functional>
#include <gtest/gtest.h>
#include "archive.h"
#include "lru_window.hpp"

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}

namespace anti {
namespace themis {
namespace common_lib {

class TestLRUWindow : public ::testing::Test {
public:
    virtual void SetUp() {}

    virtual void TearDown() {}
};

struct MyNode {
public:
    MyNode(uint64_t c, uint64_t s, uint64_t v) : co(c), sign(s), val(v) {}
    MyNode() : MyNode(0, 0, 0) {}
    MyNode(const MyNode& node) : co(node.co), sign(node.sign), val(node.val) {};
    
    uint64_t coord() const { return co; }
    uint64_t key() const { return sign; }
    
    bool operator==(const MyNode& rv) {
        return rv.co == co && rv.sign == sign && rv.val == val;
    }
    template<typename Archive>
    bool serialize(Archive* ar) const {
        return t_write(co, ar) && t_write(sign, ar) && t_write(val, ar);
    }
    
    template<typename Archive>
    bool deserialize(Archive* ar) {
        return t_read(&co, ar) && t_read(&sign, ar) && t_read(&val, ar);
    }
    uint64_t co;
    uint64_t sign;
    uint64_t val;
};

typedef LRUWindow<uint64_t, MyNode> MyLRU;
typedef std::list<MyNode>::iterator ListIter;
TEST_F(TestLRUWindow, ctr_case) {
    MyLRU obj(1, 1);
    EXPECT_EQ(1, obj._window_length);
    EXPECT_EQ(1, obj._max_list_num);
    EXPECT_EQ(0, obj._latest_coord);
}

TEST_F(TestLRUWindow, insert_case) {
    MyLRU obj(100, 100);
    std::shared_ptr<MyNode> node(new(std::nothrow) MyNode);
    ASSERT_FALSE(!node);
    obj.insert(node);
    std::shared_ptr<MyNode> node1(new(std::nothrow) MyNode(10, 1, 2));
    obj.insert(node1);
    ASSERT_EQ(2U, obj._node_list.size());
    ASSERT_EQ(2U, obj._node_index.size());
    EXPECT_TRUE(*node == *(obj._node_list.back()));
    EXPECT_TRUE(*node1 == *(obj._node_list.front()));
    auto iter = --obj._node_list.end();
    EXPECT_TRUE(obj._node_index[0] == iter);
    EXPECT_TRUE(obj._node_index[1] == obj._node_list.begin());
    std::shared_ptr<MyNode> node2(new (std::nothrow) MyNode(25, 1, 3));
    obj.insert(node2);
    ASSERT_EQ(2U, obj._node_index.size());
    ASSERT_EQ(2U, obj._node_list.size());
    EXPECT_TRUE(*(obj._node_list.front()) == *node2);
    EXPECT_TRUE(obj._node_index[1] == obj._node_list.begin());
    EXPECT_EQ(25, obj.latest_coord());
}

TEST_F(TestLRUWindow, dump_and_load_case) {
    ThreadSafeLRUWindow<uint64_t, MyNode> obj(100, 200);
    for (int i = 0; i < 1003; ++i) {
        std::shared_ptr<MyNode> node(new (std::nothrow) MyNode(i, i % 7, i % 11));
        ASSERT_FALSE(!node);
        obj.insert(node);
    }
    const char* file = "tmp_lru.ckpt";
    std::string cmd = "rm -f ";
    cmd += file;
    system(cmd.c_str());
    // dump
    FileArchive ar;
    ASSERT_TRUE(ar.open_w(file));
    ASSERT_TRUE(obj.serialize<FileArchive>(&ar));
    ar.close();
    // load fail case:
    FileArchive ar1;
    ASSERT_TRUE(ar1.open_r(file));
    ThreadSafeLRUWindow<uint64_t, MyNode> obj1(1000, 2000);
    ASSERT_FALSE(obj1.deserialize<FileArchive>(&ar1));
    ar1.close();
    // load
    FileArchive ar2;
    ASSERT_TRUE(ar2.open_r(file));
    ThreadSafeLRUWindow<uint64_t, MyNode> obj2(100, 200);
    ASSERT_TRUE(obj2.deserialize<FileArchive>(&ar2));
    ar2.close();
    system(cmd.c_str());

    ASSERT_TRUE(!obj2.find(100));
    ASSERT_FALSE(!obj2.find(0));
}

} // namespace common_lib
} // namespace themis
} // namespace anti

/* vim: set ts=4 sw=4: */

