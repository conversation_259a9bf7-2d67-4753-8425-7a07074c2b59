#ifndef SLIDING_WINDOW_V2_TEST_BASE_H
#define SLIDING_WINDOW_V2_TEST_BASE_H

#include <gtest/gtest.h>
#include <vector>
#include <chrono>
#include <algorithm>
#include <random>
#include <iostream>
#include <iomanip>

#include <gflags/gflags.h>
#include "sliding_window_v2.h"
#include "sliding_window.h"
#include "item.h"
#include "item_v2.h"
#include "archive.h"
#include "gc_slab_mempool_32.h"
#include <bsl/var/Dict.h>
#include <bsl/ResourcePool.h>

using namespace anti::themis::common_lib;

// 基本类型定义
typedef SlidingWindowV2<SegmentItemV2> TestSlidingWindow;
typedef SlidingWindow<SegmentItem> OriginalSlidingWindow;

// 测试常量
enum class TestConstants {
    DEFAULT_STEP_LEN = 300,
    DEFAULT_MAX_STEP_NUM = 15,
    DEFAULT_SEGMENT_LEN = 4200
};

/**
 * @brief 测试数据结构
 */
struct SlidingWindowTestData {
    uint64_t key;
    int64_t coord;
    SegmentItemV2 item;  // 使用V2版本

    SlidingWindowTestData(uint64_t k, int64_t c, int64_t item_val) :
            key(k), coord(c), item(item_val) {}
};

// =============================================================================
// 辅助函数
// =============================================================================

/**
 * @brief 生成测试数据
 */
inline std::vector<SlidingWindowTestData> generate_test_data(
        size_t count,
        uint64_t key_range = 1000) {
    std::vector<SlidingWindowTestData> data;
    std::random_device rd;
    std::mt19937 gen(rd());

    std::uniform_int_distribution<uint64_t> key_dist(1000, 1000 + key_range);
    std::uniform_int_distribution<int64_t> item_dist(1, 100);
    std::uniform_int_distribution<int64_t> coord_increment_dist(1, 20);

    int64_t base_coord = 1000000;
    data.reserve(count);

    for (size_t i = 0; i < count; ++i) {
        uint64_t key = key_dist(gen);
        base_coord += coord_increment_dist(gen);
        int64_t item_val = item_dist(gen);
        data.emplace_back(key, base_coord, item_val);
    }

    return data;
}

/**
 * @brief 提取唯一的key
 */
inline std::vector<uint64_t> extract_unique_keys(
        const std::vector<SlidingWindowTestData>& test_data) {
    std::vector<uint64_t> keys;
    for (const auto& data : test_data) {
        keys.push_back(data.key);
    }
    std::sort(keys.begin(), keys.end());
    keys.erase(std::unique(keys.begin(), keys.end()), keys.end());
    return keys;
}

/**
 * @brief 验证查询结果一致性
 */
template <typename SW1, typename SW2>
inline bool verify_query_consistency(SW1& sw1, SW2& sw2, const std::vector<uint64_t>& test_keys) {
    size_t query_failures = 0;
    for (uint64_t key : test_keys) {
        auto sw1_result = sw1.query_segment(key);
        auto sw2_result = sw2.query_segment(key);
        if ((sw1_result == nullptr) != (sw2_result == nullptr)) {
            query_failures++;
            continue;
        }
        if (sw1_result != nullptr && sw2_result != nullptr) {
            if (sw1_result->cumulant() != sw2_result->cumulant()) {
                query_failures++;
                continue;
            }
        }
        auto sw1_last = sw1.query_last_segment(key);
        auto sw2_last = sw2.query_last_segment(key);
        if (sw1_last.cumulant() != sw2_last.cumulant()) {
            query_failures++;
        }
    }
    return query_failures == 0;
}

/**
 * @brief 创建checkpoint文件
 */
template <typename SW>
inline bool create_checkpoint(SW& sw, const std::string& path) {
    system(("rm -f " + path).c_str());
    FileArchive ar;
    return ar.open_w(path.c_str()) && sw.serialize(&ar) && ar.close();
}

/**
 * @brief 从checkpoint加载
 */
template <typename SW>
inline bool load_from_checkpoint(
        SW& sw,
        const std::string& path,
        int64_t step_len,
        int max_step_num,
        int64_t segment_len) {
    if (!sw.init(step_len, max_step_num, segment_len)) {
        return false;
    }
    FileArchive ar;
    return ar.open_r(path.c_str()) && sw.deserialize(&ar) && ar.close();
}

/**
 * @brief 验证跨版本checkpoint兼容性
 */
inline bool verify_cross_version_compatibility(
        const std::vector<SlidingWindowTestData>& test_data) {
    TestSlidingWindow pool_sw;
    OriginalSlidingWindow orig_sw;

    if (!pool_sw.init(static_cast<int64_t>(TestConstants::DEFAULT_STEP_LEN), 
                      static_cast<int>(TestConstants::DEFAULT_MAX_STEP_NUM), 
                      static_cast<int64_t>(TestConstants::DEFAULT_SEGMENT_LEN)) ||
        !orig_sw.init(static_cast<int64_t>(TestConstants::DEFAULT_STEP_LEN), 
                      static_cast<int>(TestConstants::DEFAULT_MAX_STEP_NUM), 
                      static_cast<int64_t>(TestConstants::DEFAULT_SEGMENT_LEN))) {
        return false;
    }

    for (const auto& data : test_data) {
        pool_sw.enter(data.key, data.coord, data.item);
        // 原版SlidingWindow使用SegmentItem，需要转换
        SegmentItem orig_item(data.item.cumulant());
        orig_sw.enter(data.key, data.coord, orig_item);
    }

    auto test_keys = extract_unique_keys(test_data);

    // 测试1: Pool创建checkpoint，Orig加载
    if (!create_checkpoint(pool_sw, "./test_pool.cpt")) {
        pool_sw.uninit();
        orig_sw.uninit();
        return false;
    }

    OriginalSlidingWindow orig_loaded;
    if (!load_from_checkpoint(
                orig_loaded,
                "./test_pool.cpt",
                static_cast<int64_t>(TestConstants::DEFAULT_STEP_LEN),
                static_cast<int>(TestConstants::DEFAULT_MAX_STEP_NUM),
                static_cast<int64_t>(TestConstants::DEFAULT_SEGMENT_LEN))) {
        pool_sw.uninit();
        orig_sw.uninit();
        system("rm -f ./test_pool.cpt");
        return false;
    }

    bool pool_to_orig_ok = verify_query_consistency(pool_sw, orig_loaded, test_keys);
    orig_loaded.uninit();

    // 测试2: Orig创建checkpoint，Pool加载
    if (!create_checkpoint(orig_sw, "./test_orig.cpt")) {
        pool_sw.uninit();
        orig_sw.uninit();
        system("rm -f ./test_pool.cpt");
        return false;
    }

    TestSlidingWindow pool_loaded;
    if (!load_from_checkpoint(
                pool_loaded,
                "./test_orig.cpt",
                static_cast<int64_t>(TestConstants::DEFAULT_STEP_LEN),
                static_cast<int>(TestConstants::DEFAULT_MAX_STEP_NUM),
                static_cast<int64_t>(TestConstants::DEFAULT_SEGMENT_LEN))) {
        pool_sw.uninit();
        orig_sw.uninit();
        system("rm -f ./test_pool.cpt ./test_orig.cpt");
        return false;
    }

    bool orig_to_pool_ok = verify_query_consistency(pool_loaded, orig_sw, test_keys);

    // 清理
    pool_sw.uninit();
    orig_sw.uninit();
    pool_loaded.uninit();
    system("rm -f ./test_pool.cpt ./test_orig.cpt");

    return pool_to_orig_ok && orig_to_pool_ok;
}

/**
 * @brief 性能测试 - SlidingWindowV2版本
 */
inline std::pair<double, size_t> benchmark_pool_operations(
        const std::vector<SlidingWindowTestData>& test_data,
        int64_t step_len = static_cast<int64_t>(TestConstants::DEFAULT_STEP_LEN),
        int max_step_num = static_cast<int>(TestConstants::DEFAULT_MAX_STEP_NUM),
        int64_t segment_len = static_cast<int64_t>(TestConstants::DEFAULT_SEGMENT_LEN)) {
    TestSlidingWindow sw;
    if (!sw.init(step_len, max_step_num, segment_len)) {
        return {0.0, 0};
    }

    auto start = std::chrono::high_resolution_clock::now();

    size_t success_count = 0;
    for (const auto& data : test_data) {
        if (sw.enter(data.key, data.coord, data.item)) {
            success_count++;
        }
    }

    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);

    double avg_time_us =
            (duration.count() > 0) ? static_cast<double>(duration.count()) / success_count : 0.0;

    sw.uninit();
    return {avg_time_us, success_count};
}

/**
 * @brief 性能测试 - 原版SlidingWindow版本
 */
inline std::pair<double, size_t> benchmark_orig_operations(
        const std::vector<SlidingWindowTestData>& test_data,
        int64_t step_len = static_cast<int64_t>(TestConstants::DEFAULT_STEP_LEN),
        int max_step_num = static_cast<int>(TestConstants::DEFAULT_MAX_STEP_NUM),
        int64_t segment_len = static_cast<int64_t>(TestConstants::DEFAULT_SEGMENT_LEN)) {
    OriginalSlidingWindow sw;
    if (!sw.init(step_len, max_step_num, segment_len)) {
        return {0.0, 0};
    }

    auto start = std::chrono::high_resolution_clock::now();

    size_t success_count = 0;
    for (const auto& data : test_data) {
        // 原版SlidingWindow需要SegmentItem类型
        SegmentItem orig_item(data.item.cumulant());
        if (sw.enter(data.key, data.coord, orig_item)) {
            success_count++;
        }
    }

    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);

    double avg_time_us =
            (duration.count() > 0) ? static_cast<double>(duration.count()) / success_count : 0.0;

    sw.uninit();
    return {avg_time_us, success_count};
}

/**
 * @brief 验证监控基本功能
 */
inline bool verify_monitor_basics(TestSlidingWindow& sw) {
    bsl::ResourcePool rp;
    bsl::var::Dict dict;
    sw.monitor(dict, rp);

    try {
        dict["TOTAL_ELEMENTS"].to_uint64();
        dict["SLIDING_WINDOW_OVERHEAD"].to_uint64();
        // 根据内存池所有权验证不同字段
        if (!dict.get("DATA_MEM").is_null()) {
            // 拥有内存池模式
            dict["DATA_MEM"].to_uint64();
            dict["POOL_MGMT_OVERHEAD"].to_uint64();
            dict["TOTAL_OVERHEAD"].to_uint64();
        }
        return true;
    } catch (...) {
        return false;
    }
}

/**
 * @brief 测试基类
 */
class SlidingWindowTestBase : public ::testing::Test {
protected:
    void SetUp() override {
        _pool_sw = new TestSlidingWindow();
        _orig_sw = new OriginalSlidingWindow();

        if (!_pool_sw->init(static_cast<int64_t>(TestConstants::DEFAULT_STEP_LEN), 
                           static_cast<int>(TestConstants::DEFAULT_MAX_STEP_NUM), 
                           static_cast<int64_t>(TestConstants::DEFAULT_SEGMENT_LEN)) ||
            !_orig_sw->init(static_cast<int64_t>(TestConstants::DEFAULT_STEP_LEN), 
                           static_cast<int>(TestConstants::DEFAULT_MAX_STEP_NUM), 
                           static_cast<int64_t>(TestConstants::DEFAULT_SEGMENT_LEN))) {
            FAIL() << "SlidingWindow初始化失败";
        }
    }

    void TearDown() override {
        if (_pool_sw) {
            _pool_sw->uninit();
            delete _pool_sw;
        }
        if (_orig_sw) {
            _orig_sw->uninit();
            delete _orig_sw;
        }
    }

protected:
    TestSlidingWindow* _pool_sw = nullptr;
    OriginalSlidingWindow* _orig_sw = nullptr;
};

#endif  // SLIDING_WINDOW_V2_TEST_BASE_H