#include "ip6map.h"

namespace anti {
namespace themis {
namespace common_lib {

bool string_to_int(const char* str, int32_t* dec) {
    if (nullptr == str || nullptr == dec) {
        CWARNING_LOG("str is nullptr or dec is nullptr!");
        return false;
    }
    try {
        *dec = boost::lexical_cast<int32_t>(str);
    } catch (boost::bad_lexical_cast& e) {
        CWARNING_LOG("%s is not a number", str);
        return false;
    }
    return true;
}

ip6map_t::ip6map_t() : _ipv6lib(NULL) {}
ip6map_t::~ip6map_t() {}

int32_t ip6map_t::set_data_file(const char* filename,
        const std::vector<int32_t>& cols_selected,
        const char sep) {
    if (nullptr == filename || '\0' == filename[0]) {
        CWARNING_LOG("filename is nullptr or empty!");
        return -1;
    }
    _ipv6lib.reset(new (std::nothrow) IPv6Library());
    if (!_ipv6lib) {
        CWARNING_LOG("can't new IPv6Library!");
        return -1;
    }
    if (0 != _ipv6lib->load(filename, cols_selected, false)) {
        CWARNING_LOG("load %s failed!", filename);
        return -1;
    }
    return 0;
}

bool ip6map_t::get(const char* ip6str, int32_t& id1, int32_t& id2) const {
    if (nullptr == ip6str) {
        CWARNING_LOG("ip6str is nullptr!");
        return false;
    }
    IPv6Library::IPAttrs target_attr_values;
    int query_status = _ipv6lib->query(ip6str, target_attr_values);
    if (0 != query_status) {
        CWARNING_LOG("%s query failed, return code: %d!", ip6str, query_status);
        return false;
    }
    if (!string_to_int(target_attr_values[0], &id1) || !string_to_int(target_attr_values[1], &id2)) {
        CWARNING_LOG("trans %s or %s to number error!", target_attr_values[0], target_attr_values[1]);
        return false;
    }
    return true;
}

} // common_lib
} // themis
} // anti
