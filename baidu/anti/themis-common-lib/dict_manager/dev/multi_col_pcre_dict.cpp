// Copyright (c) 2024 Baidu.com, Inc. All Rights Reserved
// @Author: liushuai(<EMAIL>)
// 
// @File: multi_col_pcre_dict.cpp
// @Last modified: 2024-02-20 09:56:06
// @Brief: 

#include <com_log.h>
#include "multi_col_pcre_dict.h"

namespace anti {
namespace themis {
namespace common_lib {

static const int32_t MAX_LINE_SIZE = 1024;

MultiColPcreDict::MultiColPcreDict() : _cur_idx(0){}

void MultiColPcreDict::uninit() {
    for (uint32_t i = 0; i < VEC_NUM; ++i) {
        for(auto iter : _vecs[i]) {
            pcre_free(iter.first);
            iter.first = nullptr;
        }
        _vecs[i].clear();
    }      
    _cur_idx = 0;
}

bool MultiColPcreDict::init(const comcfg::ConfigUnit& conf) {
    try {
        _file_path = conf["file_path"].to_cstr();
        _match_cols = conf["match_cols"].to_int32();
        if(_match_cols < 1) {
            CWARNING_LOG("_match_cols(%d) is illegal, must greater than 0", _match_cols);
            return false;
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std Exception : %s", e.what());
        return false;
    }
    if (!reload()) {
        CWARNING_LOG("call reload fail, file(%s)", _file_path.data());
        return false;
    }
    return true;
}

bool MultiColPcreDict::_reload() {
    uint32_t idle = (_cur_idx + 1U) % VEC_NUM;
    for(auto iter : _vecs[idle]) {
        pcre_free(iter.first);
        iter.first = nullptr;
    }
    _vecs[idle].clear();
    std::ifstream ifs(_file_path.c_str(), std::ifstream::in);
    if (ifs.fail()) {
        CWARNING_LOG("open file(%s) fail", _file_path.c_str());
        return false;
    }
    std::string line;
    const char* err;
    int erroffset = 0;
    while (!getline(ifs, line).eof()) {
        if (line.size() > MAX_LINE_SIZE) {
            CDEBUG_LOG("line is greater than MAX_LINE_SIZE(%d) of dict(%s)", MAX_LINE_SIZE, _file_path.c_str());
            continue;
        }
        
        CDEBUG_LOG("line is [%s]", line.c_str());
        std::vector<std::string> terms;
        boost::split(terms, line, boost::is_any_of("\t"), boost::token_compress_off);
        CDEBUG_LOG("terms size is %d", terms.size());
        
        if(terms.size() <= _match_cols - 1) {
            CWARNING_LOG("line[%s] is illegal, terms size(%d) <= _match_cols(%d) - 1", line.c_str(), terms.size(), _match_cols);
            continue;
        }
        
        std::string match_term = terms[_match_cols - 1];
        CDEBUG_LOG("match_term is [%s]", match_term.c_str());

        pcre* pattern = pcre_compile(match_term.c_str(), 0, &err, &erroffset, NULL);
        if (pattern == nullptr) {
            CDEBUG_LOG("compile for pattern [%s] failed",line.c_str());
            continue;
        }
        _vecs[idle].emplace_back(std::make_pair(pattern, line));
    }
    _cur_idx = idle;
    return true;
}

bool MultiColPcreDict::lookup(const char* buf, int32_t len) const {
    if (buf == nullptr) {
        CFATAL_LOG("input buf is NULL");
        return false;
    }
    for (auto iter : _vecs[_cur_idx]) {
        if (pcre_exec(iter.first, NULL, buf, len, 0, 0, 0, 0) > PCRE_ERROR_NOMATCH) {
            return true;
        }
    }
    return false;
}

bool MultiColPcreDict::lookup_str(const std::string& input_str, std::vector<std::string>& outputs) const {
    if (input_str.empty()) {
        CFATAL_LOG("input_str is empty");
        return false;
    }
    for (auto iter : _vecs[_cur_idx]) {
        if (pcre_exec(iter.first, NULL, input_str.c_str(), input_str.size(), 0, 0, 0, 0) > PCRE_ERROR_NOMATCH) {
            std::string match_line = iter.second;
            outputs.emplace_back(match_line);
            return true;
        }
    }
    return true;
}

}  // namespace common_lib
}  // namespace themis
}  // namespace anti
/* vim: set ts=4 sw=4: */