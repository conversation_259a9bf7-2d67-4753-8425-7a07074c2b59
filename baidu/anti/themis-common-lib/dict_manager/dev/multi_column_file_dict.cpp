// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: multi_column_file_dict.cpp
// @Last modified: 2015-06-04 15:25:23
// @Brief: 

#include "multi_column_file_dict.h"
#include <fstream>
#include <boost/algorithm/string.hpp>
#include <com_log.h>
#include <sign_util.h>
#include "string_util.h"

using anti::baselib::SignUtil;

namespace anti {
namespace themis {
namespace common_lib {

bool MultiColumnFileDict::init(const comcfg::ConfigUnit& conf) {
    try {
        _file_path = conf["file_path"].to_cstr();
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std Exception : %s", e.what());
        return false;
    }
    // column is not necessary
    // if not given, default -1, which means use all column
    // if given, start at 0
    conf["column"].get_int32(&_column, -1);
    if (_column < -1) {
        CWARNING_LOG("Invalid column:%d", _column);
        return false;
    }

    if (!reload()) {
        CWARNING_LOG("call reload fail, file(%s)", _file_path.c_str());
        return false;
    }
    return true;
}

void MultiColumnFileDict::uninit() {
    for (uint32_t i = 0; i < SET_NUM; ++i) {
        _sets[i].clear();
    }
    _cur_idx = 0U;
    _column = -1;
}

bool MultiColumnFileDict::_reload() {
    uint32_t idle = (_cur_idx + 1U) % SET_NUM;
    _sets[idle].clear();
    std::ifstream ifs(_file_path.c_str(), std::ifstream::in);
    if (ifs.fail()) {
        CWARNING_LOG("open file(%s) fail", _file_path.c_str());
        return false;
    }
    std::string line;
    while (!getline(ifs, line).eof()) {
        std::vector<std::string> fields;
        boost::split(fields, line, boost::is_any_of("\t"));
        if (_column >= static_cast<int32_t>(fields.size())) {
            CWARNING_LOG("column(%d) is greater than columns of dict(%s)",
                    _column, _file_path.c_str());
            return false;
        }

        uint64_t sign = 0LU;
        if (_column == -1) {
            // use all columns
            SignUtil::create_sign_md64(StringUtil::join(fields, "_"), &sign);
        } else {
            // use given column
            SignUtil::create_sign_md64(fields[_column], &sign);
        }
        if (!_sets[idle].insert(sign).second) {
            CDEBUG_LOG("file (%s) insert line(%s) fail", _file_path.c_str(), line.c_str());
            continue;
        }
    }
    _cur_idx = idle;
    return true;
}

bool MultiColumnFileDict::lookup(const std::string& search_key) const {
    uint64_t sign = 0LU;
    SignUtil::create_sign_md64(search_key, &sign);
    return lookup(sign);
}

bool MultiColumnFileDict::lookup(const std::vector<std::string>& values) const {
    uint64_t sign = 0LU;
    CDEBUG_LOG("json_value is %s", (StringUtil::join(values, "_")).c_str());
    SignUtil::create_sign_md64(StringUtil::join(values, "_"), &sign);
    return lookup(sign);
}

bool MultiColumnFileDict::lookup(uint64_t sign) const {
    if (_cur_idx >= SET_NUM) {
        CFATAL_LOG("_cur_idx(%u) >= SET_NUM(%u)", _cur_idx, SET_NUM);
        return false;
    }

    if (_sets[_cur_idx].count(sign) == 0) {
        return false;
    }
    // find
    return true;
}
bool MultiColumnFileDict::lookup_str(const std::string& input_str, std::vector<std::string>& outputs) const {
    if (input_str.empty()) {
        CFATAL_LOG("input buf is empty");
        return false;
    }
    
    if (_cur_idx >= SET_NUM) {
        CFATAL_LOG("_cur_idx(%u) >= SET_NUM(%u)", _cur_idx, SET_NUM);
        return false;
    }
    
    bool ret = false;
    ret = lookup(input_str);
    if (ret) { 
        outputs.emplace_back(input_str);
        CDEBUG_LOG("input_str is %s", input_str.c_str());
    }
    return true;
}

bool MultiColumnFileDict::lookup_str(const std::vector<std::string>& values, std::vector<std::string>& outputs) const {
    if (values.empty()) {
        CFATAL_LOG("input vector is empty");
        return false;
    }

    if (_cur_idx >= SET_NUM) {
        CFATAL_LOG("_cur_idx(%u) >= SET_NUM(%u)", _cur_idx, SET_NUM);
        return false;
    }

    bool ret = false;
    ret = lookup(values);
    CDEBUG_LOG("ret is %s", std::to_string(ret).c_str());
    if (ret) { 
        for(int i = 0; i < values.size(); ++i) {
            outputs.emplace_back(values[i]);
            CDEBUG_LOG("lookup_str(%d) is %s", i, outputs[i].c_str());
        }
    }
    return true;
}

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

