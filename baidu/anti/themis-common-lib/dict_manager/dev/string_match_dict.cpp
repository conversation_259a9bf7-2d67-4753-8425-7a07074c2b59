// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: string_match_dict.cpp
// @Last modified: 2016-05-09 09:55:46
// @Brief: 

#include <com_log.h>
#include "string_match_dict.h"

namespace anti {
namespace themis {
namespace common_lib {

static const uint32_t MAX_NUM = 10000;
static int prop_str2int_p(char * prop) {
    // do nothing;
    // callback function
    return 0;
}

StringMatchDict::StringMatchDict() : _cur_idx(0){
    for (uint32_t i = 0; i < DICT_NUM; ++i) {
        _dicts[i] = NULL;
    }
}

void StringMatchDict::uninit() {
    for (uint32_t i = 0; i < DICT_NUM; ++i) {
        if (_dicts[i] != NULL) {
            dm_dict_del(_dicts[i]);
            _dicts[i] = NULL;
        }
    }   
    _cur_idx = 0;
}

bool StringMatchDict::init(const comcfg::ConfigUnit& conf) {
    try {
        _file_path = conf["file_path"].to_cstr();
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std Exception : %s", e.what());
        return false;
    }
    // init global member dm_prop_str2int_p in dm.
    // sad, we have to, event hough property has nothing to do with us, at now
    // because dm_load_dict will call this function,
    dm_prop_str2int_p = prop_str2int_p;
    if (!reload()) {
        CWARNING_LOG("call reload fail, file(%s)", _file_path.data());
        return false;
    }
    return true;
}

bool StringMatchDict::_reload() {
    uint32_t idle = (_cur_idx + 1U) % DICT_NUM;
    if (_dicts[idle] != NULL) {
        dm_dict_del(_dicts[idle]);
        _dicts[idle] = NULL;
    }
    _dicts[idle] = dm_dict_load(_file_path.data(), MAX_NUM); 
    if (_dicts[idle] == NULL) {
        CFATAL_LOG("call dm_dict_load fail, file(%s)", _file_path.data());
        return false;
    }
    _cur_idx = idle;
    return true;
}

bool StringMatchDict::lookup(const char* buf, int32_t len) const {
    if (buf == NULL) {
        CFATAL_LOG("input buf is NULL");
        return false;
    }
    int32_t ret = 0;
   
    thread_local std::shared_ptr<dm_pack_t> dm_pack(dm_pack_create(100), [](dm_pack_t* ptr){
            dm_pack_del(ptr);   
        }); 
    if (!dm_pack) {
        CWARNING_LOG("dm_pack_create failed!");
        return false;
    }
    if ((ret = dm_search(_dicts[_cur_idx], dm_pack.get(), buf, len, DM_OUT_FMM)) != 0) {
        CFATAL_LOG("call dm_search fail, ret(%d)", ret);
        return false;
    }
    return dm_pack->ppseg_cnt > 0;
}

bool StringMatchDict::lookup_str(
        const std::string& input_str, 
        std::vector<std::string>& outputs) const {
    if (input_str.empty()) {
        CFATAL_LOG("input buf is empty");
        return false;
    }
    int32_t ret = 0;
   
    thread_local std::shared_ptr<dm_pack_t> dm_pack(dm_pack_create(100), [](dm_pack_t* ptr){
            dm_pack_del(ptr);   
        }); 
    if (nullptr == dm_pack) {
        CWARNING_LOG("dm_pack_create failed!");
        return false;
    }
    if ((ret = dm_search(_dicts[_cur_idx], dm_pack.get(), input_str.data(), input_str.size(), DM_OUT_FMM)) != 0) {
        CFATAL_LOG("call dm_search fail, ret(%d)", ret);
        return false;
    }
    for(int i = 0; i < dm_pack->ppseg_cnt; i++) {
        if (dm_pack->ppseg != nullptr && (dm_pack->ppseg)[i] != nullptr && (dm_pack->ppseg)[i]->pstr != nullptr) {
            outputs.emplace_back(std::string((dm_pack->ppseg)[i]->pstr));
            CDEBUG_LOG("lookup_str(%d) is %s", i, outputs[i].c_str());
        }
    }
    return true;
}

}  // namespace common_lib
}  // namespace themis
}  // namespace anti
/* vim: set ts=4 sw=4: */

