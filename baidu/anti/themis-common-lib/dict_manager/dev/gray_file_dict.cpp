// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: gray_file_dict.cpp
// @Last modified: 2015-06-04 15:25:01
// @Brief: 

#include "gray_file_dict.h"
#include <fstream>
#include <boost/algorithm/string.hpp>
#include <boost/lexical_cast.hpp>
#include <com_log.h>
#include <sign_util.h>
#include "string_util.h"

using anti::baselib::SignUtil;

namespace anti {
namespace themis {
namespace common_lib {

bool GrayFileDict::init(const comcfg::ConfigUnit& conf) {
    try {
        _file_path = conf["file_path"].to_cstr();
        _view_num = conf["view_num"].to_uint32();
        if (_view_num == 0U) {
            CWARNING_LOG("view_num muste be greater than 0");
            return false;
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std Exception : %s", e.what());
        return false;
    }

    if (!reload()) {
        CWARNING_LOG("call reload fail, file(%s)", _file_path.c_str());
        return false;
    }
    return true;
}

void GrayFileDict::uninit() {
    for (uint32_t i = 0; i < MAP_NUM; ++i) {
        _maps[i].clear();
    }
    _cur_idx = 0U;
}

bool GrayFileDict::_reload() {
    uint32_t idle = (_cur_idx + 1U) % MAP_NUM;
    _maps[idle].clear();
    std::ifstream ifs(_file_path.c_str(), std::ifstream::in);
    if (ifs.fail()) {
        CWARNING_LOG("open file(%s) fail", _file_path.c_str());
        return false;
    }
    std::string line;
    while (!getline(ifs, line).eof()) {
        std::vector<std::string> fields;
        boost::split(fields, line, boost::is_any_of(" \t"), boost::token_compress_on);
        if (fields.size() != _view_num + 1U) {
            CWARNING_LOG("invalid line(%s), view_num must be %u", line.data(), _view_num);
            return false;
        }
        double threshold = 0.0;
        try {
            threshold = boost::lexical_cast<double>(fields.back());
        } catch (const boost::bad_lexical_cast& e) {
            CWARNING_LOG("invalid threshold(%s)", fields.back().data());
            return false;
        }
        fields.pop_back();
        uint64_t sign = 0LU;
        SignUtil::create_sign_md64(StringUtil::join(fields, "_"), &sign);
        if (!_maps[idle].emplace(sign, threshold).second) {
            CWARNING_LOG("file(%s) insert line(%s) fail", _file_path.data(), line.data());
            continue;
        }
    }
    _cur_idx = idle;
    return true;
}

bool GrayFileDict::lookup(
        const std::vector<std::string>& values, 
        double* threshold) const {
    if (values.size() != _view_num) {
        CFATAL_LOG("values size(%u) doesn't equal _view_num(%u)", values.size(), _view_num);
        return false;
    }
    uint64_t sign = 0U;
    SignUtil::create_sign_md64(StringUtil::join(values, "_"), &sign);
    return lookup(sign, threshold);
}

bool GrayFileDict::lookup(uint64_t sign, double* threshold) const {
    if (threshold == NULL || _cur_idx >= MAP_NUM) {
        CFATAL_LOG("input threshold == NULL or _cur_idx(%u) >= MAP_NUM(%u)", _cur_idx, MAP_NUM);
        return false;
    }
    std::unordered_map<uint64_t, double>::const_iterator iter = _maps[_cur_idx].find(sign);
    if (iter == _maps[_cur_idx].end()) {
        return false;
    }
    *threshold = iter->second;
    return true;
}

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

