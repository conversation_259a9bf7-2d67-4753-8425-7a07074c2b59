// Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved
// @Author: liushuai31(<EMAIL>)
// 
// @File: dqa_multi_term_string_match_dict.cpp
// @Last modified: 2023-09-19 09:56:06
// @Brief: 

#include <fstream>
#include <com_log.h>
#include <boost/algorithm/string.hpp>
#include "dqa_multi_term_string_match_dict.h"

namespace anti {
namespace themis {
namespace common_lib {

const std::string DqaMultiTermStringMatchDict::SEP = "&";
const int DM_BUFFER_SIZE = 200;

void DqaMultiTermStringMatchDict::uninit() {
    for (uint64_t i = 0; i < DICT_NUM; ++i) {
        _dicts[i]->uninit();
    }
    _cur_idx = 0U;
}

bool DqaMultiTermStringMatchDict::init(const comcfg::ConfigUnit& conf) {
    try {
        _file_path = conf["file_path"].to_cstr();
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std Exception : %s", e.what());
        return false;
    }
    if (!reload()) {
        CWARNING_LOG("call reload fail, file(%s)", _file_path.data());
        return false;
    }
    return true;
}

bool DqaMultiTermStringMatchDict::_reload() {
    uint32_t idle = (_cur_idx + 1U) % DICT_NUM;
    _dicts[idle].reset(new (std::nothrow) QueryMultiTermDicts());
    if(_dicts[idle] == nullptr) {
        CWARNING_LOG("new query_multi_term_dicts fail");
        return false;
    }
    std::ifstream ifs(_file_path.c_str(), std::ifstream::in);
    if (ifs.fail()) {
        CWARNING_LOG("open file(%s) fail", _file_path.c_str());
        return false;
    }
    CDEBUG_LOG("DqaMultiTermStringMatchDict _reload start");
    std::string line;
    int rule_idx = 0;
    int cur_term_size = 0;
    while (!getline(ifs, line).eof()) {
        if(cur_term_size >= SUB_TERM_MAX_NUM) {
            CFATAL_LOG("terms size greater than SUB_TERM_MAX_NUM");
            break;
        }
        CDEBUG_LOG("line is [%s]", line.c_str());
        std::vector<std::string> terms;
        boost::split(terms, line, boost::is_any_of(SEP), boost::token_compress_off); 
        cur_term_size += terms.size();
        if (terms.size() < 2){
            CDEBUG_LOG("terms size less than 2");
            continue;
        }
        for (int i = 0; i < terms.size(); ++i){
            std::string curr_term = terms[i];
            CDEBUG_LOG("curr_term is [%s]", curr_term.c_str());
            // term 入字典树
            if(curr_term.empty()) {
                CDEBUG_LOG("curr_term is empty", curr_term.c_str());
                continue;
            }
            dm_lemma_t lemma;
            lemma.pstr = (char*)(curr_term.c_str());
            lemma.len = curr_term.length();
            lemma.prop = 0;
            if (dm_add_lemma(_dicts[idle]->get_all_term_dict(), &lemma, DM_CHARSET_GB18030) < 0) {
                CWARNING_LOG("insert into dmdict failure:%s", curr_term.c_str());
                continue;
            }
            _dicts[idle]->get_term_to_rule_map()[curr_term].push_back(rule_idx);
        }
        // 记录每行的term size
        _dicts[idle]->get_rule_to_count_map()[rule_idx] = terms.size();
        // 记录idx到term的映射
        _dicts[idle]->get_idx_to_term_map()[rule_idx] = terms;
        rule_idx += 1;
    }
    _cur_idx = idle;
    CDEBUG_LOG("reload _cur_idx is : %d", _cur_idx);
    ifs.close();
    return true;
}

bool DqaMultiTermStringMatchDict::lookup(const std::string& buf) const {
    CDEBUG_LOG("look up: _cur_idx is : %d", _cur_idx);
    if (_cur_idx >= DICT_NUM) {
        CFATAL_LOG("_cur_idx(%u) >= NUM(%d), file(%s)", _cur_idx, DICT_NUM, _file_path.data());
        return false;
    }

    if(_dicts[_cur_idx] == nullptr) {
        CWARNING_LOG("_dicts[idle] is nullptr");
        return false;
    }

    thread_local std::shared_ptr<dm_pack_t> p_dm_pack(dm_pack_create(DM_BUFFER_SIZE), [](dm_pack_t* ptr){
            dm_pack_del(ptr);   
        }); 
    if (nullptr == p_dm_pack) {
        CWARNING_LOG("dm_pack_create failed!");
        return false;
    }

    CDEBUG_LOG("buf is [%s]", buf.c_str());
    int hit_ret = dm_search(_dicts[_cur_idx]->get_all_term_dict(), p_dm_pack.get(),
    buf.c_str(), buf.size(), DM_OUT_ALL);
    int hit_cnt = p_dm_pack->ppseg_cnt;
    std::map<int,int> rule_hit_count;
    std::set<std::string> hit_terms;

    if (hit_cnt != 0 && hit_ret == 0) {
        for (int i = 0; i < p_dm_pack->ppseg_cnt; i++) {
            std::string tmp = p_dm_pack->ppseg[i]->pstr;
            CDEBUG_LOG("tmp is [%s]", tmp.c_str());
            if (hit_terms.count(tmp) == 0){
                hit_terms.insert(tmp);
            }
            else {
                continue;
            }
            // 词典中没有找到命中的term
            if (_dicts[_cur_idx]->get_term_to_rule_map().count(tmp) == 0){
                continue;
            } else {
                // 命中term在原来词典中出现的行数
                std::vector<int>& hit_rules = _dicts[_cur_idx]->get_term_to_rule_map()[tmp]; 
                for (int i = 0; i < hit_rules.size();i++){
                    // 记录原来词典中每行内容命中query中term的数量
                    if (rule_hit_count.count(hit_rules[i]) == 0){
                        rule_hit_count[hit_rules[i]] = 1;
                    } else {
                        rule_hit_count[hit_rules[i]] += 1;
                    }
                }
            }
        }
        std::map<int,int>::iterator iter;
        iter = rule_hit_count.begin();
        while (iter != rule_hit_count.end()){
            int rule_idx = iter->first;
            int hit_count = iter->second;
            // query命中某一行词典 <== 某行词典的term数量>0 && query中命中term的数量>0 && 某行词典的term数量==query中命中term的数量
            if (hit_count > 0 && 0 != _dicts[_cur_idx]->get_rule_to_count_map().count(rule_idx) 
                            && hit_count == _dicts[_cur_idx]->get_rule_to_count_map()[rule_idx]){
                return true;
            }
            ++iter;
        }
    }
    return false;
}

bool DqaMultiTermStringMatchDict::lookup_str(const std::string& input_str, std::vector<std::string>& outputs) const {
    CDEBUG_LOG("look up: _cur_idx is : %d", _cur_idx);
    if (_cur_idx >= DICT_NUM) {
        CFATAL_LOG("_cur_idx(%u) >= NUM(%d), file(%s)", _cur_idx, DICT_NUM, _file_path.data());
        return false;
    }

    if(_dicts[_cur_idx] == nullptr) {
        CWARNING_LOG("_dicts[idle] is nullptr");
        return false;
    }

    thread_local std::shared_ptr<dm_pack_t> p_dm_pack(dm_pack_create(DM_BUFFER_SIZE), [](dm_pack_t* ptr){
            dm_pack_del(ptr);   
        }); 
    if (nullptr == p_dm_pack) {
        CWARNING_LOG("dm_pack_create failed!");
        return false;
    }

    CDEBUG_LOG("buf is [%s]", input_str.c_str());
    int hit_ret = dm_search(_dicts[_cur_idx]->get_all_term_dict(), p_dm_pack.get(),
    input_str.c_str(), input_str.size(), DM_OUT_ALL);
    int hit_cnt = p_dm_pack->ppseg_cnt;
    std::map<int,int> rule_hit_count;
    std::set<std::string> hit_terms;

    if (hit_cnt != 0 && hit_ret == 0) {
        for (int i = 0; i < p_dm_pack->ppseg_cnt; i++) {
            std::string tmp = p_dm_pack->ppseg[i]->pstr;
            CDEBUG_LOG("tmp is [%s]", tmp.c_str());
            if (hit_terms.count(tmp) == 0){
                hit_terms.insert(tmp);
            }
            else {
                continue;
            }
            // 词典中没有找到命中的term
            if (_dicts[_cur_idx]->get_term_to_rule_map().count(tmp) == 0){
                continue;
            } else {
                // 命中term在原来词典中出现的行数
                std::vector<int>& hit_rules = _dicts[_cur_idx]->get_term_to_rule_map()[tmp]; 
                for (int i = 0; i < hit_rules.size();i++){
                    // 记录原来词典中每行内容命中query中term的数量
                    if (rule_hit_count.count(hit_rules[i]) == 0){
                        rule_hit_count[hit_rules[i]] = 1;
                    } else {
                        rule_hit_count[hit_rules[i]] += 1;
                    }
                }
            }
        }
        std::map<int,int>::iterator iter;
        iter = rule_hit_count.begin();
        while (iter != rule_hit_count.end()){
            int rule_idx = iter->first;
            int hit_count = iter->second;
            // query命中某一行词典 <== 某行词典的term数量>0 && query中命中term的数量>0 && 某行词典的term数量==query中命中term的数量
            if (hit_count > 0 && 0 != _dicts[_cur_idx]->get_rule_to_count_map().count(rule_idx) 
                            && hit_count == _dicts[_cur_idx]->get_rule_to_count_map()[rule_idx]){
                outputs = _dicts[_cur_idx]->get_idx_to_term_map()[rule_idx];
                return true;
            }
            ++iter;
        }
    }
    return true;
}

}  // namespace common_lib
}  // namespace themis
}  // namespace anti
/* vim: set ts=4 sw=4: */

