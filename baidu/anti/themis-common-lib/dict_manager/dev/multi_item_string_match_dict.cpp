// Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
// @Author: <PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
// 
// @File: multi_term_string_match_dict.cpp
// @Last modified: 2022-11-18 15:44:06
// @Brief: 

#include <fstream>
#include <boost/algorithm/string.hpp>
#include <com_log.h>
#include "multi_item_string_match_dict.h"

namespace anti {
namespace themis {
namespace common_lib {

const std::string MultiItemStringMatchDict::SEP = "&";

void MultiItemStringMatchDict::uninit() {
    for (uint64_t i = 0; i < NUM; ++i) {
        _sets[i].clear();
    }
    _cur_idx = 0U;
}

bool MultiItemStringMatchDict::init(const comcfg::ConfigUnit& conf) {
    try {
        _file_path = conf["file_path"].to_cstr();
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std Exception : %s", e.what());
        return false;
    }
    if (!reload()) {
        CWARNING_LOG("call reload fail, file(%s)", _file_path.c_str());
        return false;
    }
    return true;
}

bool MultiItemStringMatchDict::_reload() {
    assert(NUM != 0U);
    uint64_t idle = (_cur_idx + 1U) % NUM;
    _sets[idle].clear();
    std::ifstream ifs(_file_path.c_str(), std::ifstream::in);
    if (ifs.fail()) {
        CWARNING_LOG("open file(%s) fail", _file_path.c_str());
        return false;
    }
    const uint32_t TERM_MUM = 3U;
    const uint32_t FIRST = 0U;
    const uint32_t SECOND = 1U;
    const uint32_t Third = 2U;
    std::string line;
    while (!getline(ifs, line).eof()) {
        std::vector<std::string> columns;
        boost::split(columns, line, boost::is_any_of(SEP), boost::token_compress_off); 
        if (columns.size() < 2 || columns.size() > TERM_MUM) {
            CWARNING_LOG("get line(%s) invalid, line splite size(%lu) invalid, should between 1 and  %d",
                    line.data(), columns.size(), TERM_MUM);
            continue;
        }
        // fill into DictInfoMap
        // 如果columns只有2列 那么增加一个默认值 -
        if (columns.size() < TERM_MUM) {
            columns.emplace_back("-");
        }
        CDEBUG_LOG("line is [%s]", line.c_str());
        if (_sets[idle].find(columns[FIRST]) == _sets[idle].end()) {
            ItemSetMap item_set_map;
            _sets[idle].emplace(std::pair<std::string, ItemSetMap>(columns[FIRST], item_set_map));
            if (_sets[idle][columns[FIRST]].find(columns[SECOND]) == _sets[idle][columns[FIRST]].end()) {
                ItemSet item_set;
                _sets[idle][columns[FIRST]].emplace(std::pair<std::string, ItemSet>(columns[SECOND], item_set));
            }
        }
        _sets[idle][columns[FIRST]][columns[SECOND]].emplace(columns[Third]);
    }
    _cur_idx = idle;
    ifs.close();
    CDEBUG_LOG("_sets[idle] size:  %d", _sets[idle].size());
    return true;
}

bool MultiItemStringMatchDict::lookup(const std::string& buf) const {
    if (_cur_idx >= NUM) {
        CFATAL_LOG("_cur_idx(%u) >= NUM(%d), file(%s)", _cur_idx, NUM, _file_path.data());
        return false;
    }
    CDEBUG_LOG("buf is [%s]", buf.c_str());
    std::vector<std::string> terms;
    boost::split(terms, buf, boost::is_any_of(","), boost::token_compress_off);
    int32_t hit = 0;
    ItemSetMap *item_set_map = NULL;
    ItemSet *item_set = NULL;
    for (auto term : terms) {
        CDEBUG_LOG("term is [%s]", term.c_str());
        if (hit == 0) {
            if (_sets[_cur_idx].find(term) == _sets[_cur_idx].end()) {
                continue;
            }
            item_set_map = &(_sets[_cur_idx][term]);
            hit++;
        }
        if (hit == 1 && item_set_map != NULL) {
            if (item_set_map->find(term) == item_set_map->end()) {
                continue;
            }
            item_set = &(item_set_map->at(term));
            // 词典中只有2个关键词,不判断第三层
            if (item_set->find("-") != item_set->end()) {
                CDEBUG_LOG("hit !!");
                CDEBUG_LOG("first term is [%s]",term.c_str());
                return true;
            }
            hit++;
        }
        if (hit == 2 && item_set != NULL) {
            if (item_set->find(term) == item_set->end()) {
                continue;
            }
            return true;
        }
        
    }
    return false;
}

bool MultiItemStringMatchDict::lookup_str(const std::string& input_str, std::vector<std::string>& outputs) const {
    if (input_str.empty()) {
        CFATAL_LOG("input buf is empty");
        return false;
    }
    if (_cur_idx >= NUM) {
        CFATAL_LOG("_cur_idx(%u) >= NUM(%d), file(%s)", _cur_idx, NUM, _file_path.data());
        return false;
    }
    CDEBUG_LOG("buf is [%s]", input_str.c_str());
    std::vector<std::string> terms;
    boost::split(terms, input_str, boost::is_any_of(","), boost::token_compress_off);
    int32_t hit = 0;
    ItemSetMap *item_set_map = NULL;
    ItemSet *item_set = NULL;
    
    const uint32_t TERM_MUM = 3U;
    bool is_hit = false;
    std::vector<std::string> hit_terms;
    hit_terms.resize(TERM_MUM);


    for (auto term : terms) {
        CDEBUG_LOG("term is [%s]", term.c_str());
        if (hit == 0) {
            if (_sets[_cur_idx].find(term) == _sets[_cur_idx].end()) {
                continue;
            }
            hit_terms[0] = term;
            item_set_map = &(_sets[_cur_idx][term]);
            hit++;
        }
        if (hit == 1 && item_set_map != NULL) {
            if (item_set_map->find(term) == item_set_map->end()) {
                continue;
            }
            hit_terms[1] = term;
            item_set = &(item_set_map->at(term));
            // 词典中只有2个关键词,不判断第三层
            if (item_set->find("-") != item_set->end()) {
                CDEBUG_LOG("hit !!");
                CDEBUG_LOG("first term is [%s]",term.c_str());
                is_hit = true;
            }
            hit++;
        }
        if (hit == 2 && item_set != NULL) {
            if (item_set->find(term) == item_set->end()) {
                continue;
            }
            hit_terms[2] = term;
            is_hit = true;
        }
        
    }
    if (is_hit) {
        for(int i = 0; i < TERM_MUM; ++i) {
            if (!hit_terms[i].empty()) {
                outputs.emplace_back(hit_terms[i]);
            }
        }
    }
    return true;

}
}  // namespace common_lib
}  // namespace themis
}  // namespace anti
/* vim: set ts=4 sw=4: */