// Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
// @Author: exiangnan(<EMAIL>)
// 
// @File: pcre_dict.cpp
// @Last modified: 2022-08-16 09:56:06
// @Brief: 

#include <com_log.h>
#include "pcre_dict.h"

namespace anti {
namespace themis {
namespace common_lib {

static const int32_t MAX_LINE_SIZE = 1024;

PcreDict::PcreDict() : _cur_idx(0){}

void PcreDict::uninit() {
    for (uint32_t i = 0; i < SET_NUM; ++i) {
        for(auto iter : _vecs[i]) {
            pcre_free(iter.first);
            iter.first = nullptr;
        }
        _vecs[i].clear();
    }      
    _cur_idx = 0;
}

bool PcreDict::init(const comcfg::ConfigUnit& conf) {
    try {
        _file_path = conf["file_path"].to_cstr();
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std Exception : %s", e.what());
        return false;
    }
    if (!reload()) {
        CWARNING_LOG("call reload fail, file(%s)", _file_path.data());
        return false;
    }
    return true;
}

bool PcreDict::_reload() {
    uint32_t idle = (_cur_idx + 1U) % SET_NUM;
    for(auto iter : _vecs[idle]) {
        pcre_free(iter.first);
        iter.first = nullptr;
    }
    _vecs[idle].clear();
    std::ifstream ifs(_file_path.c_str(), std::ifstream::in);
    if (ifs.fail()) {
        CWARNING_LOG("open file(%s) fail", _file_path.c_str());
        return false;
    }
    std::string line;
    const char* err;
    int erroffset = 0;
    while (!getline(ifs, line).eof()) {
        if (line.size() > MAX_LINE_SIZE) {
            CDEBUG_LOG("line is greater than MAX_LINE_SIZE(%d) of dict(%s)", MAX_LINE_SIZE, _file_path.c_str());
            continue;
        }
        pcre* pattern = pcre_compile(line.c_str(), 0, &err, &erroffset, NULL);
        if (pattern == nullptr) {
            CDEBUG_LOG("compile for pattern [%s] failed",line.c_str());
            continue;
        }
        _vecs[idle].emplace_back(std::make_pair(pattern, line));
    }
    _cur_idx = idle;
    return true;
}

bool PcreDict::lookup(const char* buf, int32_t len) const {
    if (buf == NULL) {
        CFATAL_LOG("input buf is NULL");
        return false;
    }
    for (auto iter : _vecs[_cur_idx]) {
        if (pcre_exec(iter.first, NULL, buf, len, 0, 0, 0, 0) > PCRE_ERROR_NOMATCH) {
            return true;
        }
    }
    return false;
}

bool PcreDict::lookup_str(const std::string& input_str, std::vector<std::string>& outputs) const {
    if (input_str.empty()) {
        CFATAL_LOG("input_str is empty");
        return false;
    }
    for (auto iter : _vecs[_cur_idx]) {
        if (pcre_exec(iter.first, NULL, input_str.c_str(), input_str.size(), 0, 0, 0, 0) > PCRE_ERROR_NOMATCH) {
            std::string match_line = iter.second;
            outputs.emplace_back(match_line);
            return true;
        }
    }
    return true;
}

}  // namespace common_lib
}  // namespace themis
}  // namespace anti
/* vim: set ts=4 sw=4: */