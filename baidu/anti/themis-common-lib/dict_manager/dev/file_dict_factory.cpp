// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
//
// @File: file_dict_factory.cpp
// @Last modified: 2016-05-10 18:17:37
// @Brief:

#include "file_dict_factory.h"
#include <com_log.h>
#include "multi_column_file_dict.h"
#include "gray_file_dict.h"
#include "ipmap_dict.h"
#include "cn_map_file_dict.h"
#include "array_file_dict.h"
#include "key_value_dict.h"
#include "corr_cnt_dict.h"
#include "string_match_dict.h"
#include "pcre_dict.h"
#include "multi_item_string_match_dict.h"
#include "dqa_multi_term_string_match_dict.h"
#include "multi_col_pcre_dict.h"
#include "str_match_with_space.h"

namespace anti {
namespace themis {
namespace common_lib {

FileDictInterface* FileDictFactory::create(const std::string& type) {
    FileDictInterface* dict = NULL;
    if (type == "mcf") {
        dict = new(std::nothrow) MultiColumnFileDict();
    } else if (type == "gray") {
        dict = new(std::nothrow) GrayFileDict();
    } else if (type == "ipmap") {
        dict = new(std::nothrow) IpmapDict();
    } else if (type == "ip6map") {
        dict = new(std::nothrow) Ip6mapDict();
    } else if (type == "cnmap") {
        dict = new(std::nothrow) CnMapFileDict();
    } else if (type == "acp") {
        dict = new(std::nothrow) AcpFileDict();
    } else if (type == "uint32kmv") {
        dict = new(std::nothrow) Uint32KeyMultiValueDict();
    } else if (type == "int32kmv") {
        dict = new(std::nothrow) Int32KeyMultiValueDict();
    } else if (type == "stringkmv") {
        dict = new(std::nothrow) StringKeyMultiValueDict();
    } else if (type == "uint32kv") {
        dict = new(std::nothrow) Uint32KeyValueDict();
    } else if (type == "int32kv") {
        dict = new(std::nothrow) Int32KeyValueDict();
    } else if (type == "stringkv") {
        dict = new(std::nothrow) StringKeyValueDict();
    } else if (type == "stringint32mapkmv") {
        dict = new(std::nothrow) StringInt32MapKeyMultiValueDict(); 
    } else if (type == "corr_cnt") {
        dict = new(std::nothrow) CorrCntDict();
    } else if (type == "str_match") {
        dict = new(std::nothrow) StringMatchDict();
    } else if (type == "cnt_info") {
        dict = new(std::nothrow) CntInfoDict();
    } else if (type == "user_tradeid") {
        dict = new(std::nothrow) UserTradeidDict();
    } else if (type == "regular_match") {
        dict = new(std::nothrow) PcreDict();
    } else if (type == "mul_item_str_match") {
        dict = new(std::nothrow) MultiItemStringMatchDict();
    } else if (type == "dqa_multi_iterm_str_match") {
        dict = new(std::nothrow) DqaMultiTermStringMatchDict();
    } else if (type == "multi_col_regular_match") {
        dict = new(std::nothrow) MultiColPcreDict();
    } else if (type == "str_match_with_space") {
        dict = new(std::nothrow) StringMatcWithSpacehDict();
    } else {
        CWARNING_LOG("invalid type(%s)", type.c_str());
        return NULL;
    }
    if (dict == NULL) {
        CFATAL_LOG("new type(%s) fail", type.data());
        return NULL;
    }
    return dict;
}

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

