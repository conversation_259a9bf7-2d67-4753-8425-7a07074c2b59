// Copyright (c) 2024 Baidu.com, Inc. All Rights Reserved
// @Author: liushuai(<EMAIL>)
// 
// @File: str_match_with_space.h
// @Last modified: 2024-02-20 09:56:06
// @Brief: 

#include <com_log.h>
#include "str_match_with_space.h"

namespace anti {
namespace themis {
namespace common_lib {

static const uint32_t MAX_NUM = 10000;

#define EXIT(fp)    \
do {    \
    if (fp != nullptr){ \
        fclose(fp); \
    } \
    dm_dict_del(pdict); \
    return nullptr; \
} while (0)

static int prop_str2int_p(char * prop) {
    // do nothing;
    // callback function
    return 0;
}

StringMatcWithSpacehDict::StringMatcWithSpacehDict() : _cur_idx(0){
    for (uint32_t i = 0; i < DICT_NUM; ++i) {
        _dicts[i] = nullptr;
    }
}

void StringMatcWithSpacehDict::uninit() {
    for (uint32_t i = 0; i < DICT_NUM; ++i) {
        if (_dicts[i] != nullptr) {
            dm_dict_del(_dicts[i]);
            _dicts[i] = nullptr;
        }
    }   
    _cur_idx = 0;
}

bool StringMatcWithSpacehDict::init(const comcfg::ConfigUnit& conf) {
    try {
        _file_path = conf["file_path"].to_cstr();
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std Exception : %s", e.what());
        return false;
    }
    // do nothing
    dm_prop_str2int_p = prop_str2int_p;
    if (!reload()) {
        CWARNING_LOG("call reload fail, file(%s)", _file_path.data());
        return false;
    }
    return true;
}

// 原来的load函数
dm_dict_t* StringMatcWithSpacehDict::dm_dict_load_with_space(const char* fullpath, int lemma_num, dm_charset_t charset) {
    if (fullpath == nullptr){   
        CWARNING_LOG("argument null exception: 'fullpath' cannot be null");
        return nullptr;
    }

    if (charset < 0 || charset >= DM_CHARSET_NUM) {
        CWARNING_LOG("argument out of range exception: 'charset' ");
        return nullptr;
    }

    FILE* fp = nullptr;
    char buff[2048];
    char word[256];
    char prop[] = "1";
    dm_dict_t* pdict = nullptr;

    pdict = dm_dict_create(lemma_num);
    if (!pdict) {
        CWARNING_LOG("create dict failed in dm_dict_load");
        EXIT(fp); 
    }

    fp = fopen(fullpath, "rb");
    if (fp == nullptr){
        CWARNING_LOG("file %s open failed", fullpath);
        EXIT(fp); 
    }

    while (fgets(buff, sizeof(buff), fp)) {
        // reload: replace space with '\a'
        std::string tmp(buff);
        std::replace(tmp.begin(), tmp.end(), ' ', '\a');
        
        if (sscanf(tmp.c_str(), "%s", word) != 1) {
            CWARNING_LOG("add line %s failed", buff);
            continue;
        }

        dm_lemma_t lm;
        lm.pstr = word;
        lm.len = strlen(word);
        lm.prop = dm_prop_str2int_p(prop);

        if (dm_add_lemma(pdict, &lm, charset) < 0) {
            CWARNING_LOG("add lemma failed");
            EXIT(fp); 
        }
    }

    fclose(fp);
    fp = nullptr;
    for (u_int i = 0; i < pdict->lmpos && i < pdict->lmlist; i++){
        pdict->lmlist[i].pstr = pdict->strbuf + pdict->lmlist[i].bpos;
    }
    return pdict;
}
bool StringMatcWithSpacehDict::_reload() {
    uint32_t idle = (_cur_idx + 1U) % DICT_NUM;
    if (_dicts[idle] != nullptr) {
        dm_dict_del(_dicts[idle]);
        _dicts[idle] = nullptr;
    }
    _dicts[idle] = dm_dict_load_with_space(_file_path.data(), MAX_NUM, DM_CHARSET_GB18030);
    if (_dicts[idle] == nullptr) {
        CFATAL_LOG("call dm_dict_load fail, file(%s)", _file_path.data());
        return false;
    }
    _cur_idx = idle;
    return true;
}

bool StringMatcWithSpacehDict::lookup(const char* buf, int32_t len) const {
    if (buf == nullptr) {
        CFATAL_LOG("input buf is NULL");
        return false;
    }
    int32_t ret = 0;
    thread_local std::shared_ptr<dm_pack_t> dm_pack(dm_pack_create(100), [](dm_pack_t* ptr){
            dm_pack_del(ptr);   
        }); 
    if (!dm_pack) {
        CWARNING_LOG("dm_pack_create failed!");
        return false;
    }
    // search: replace space with '\a'
    std::string tmp(buf);
    std::replace(tmp.begin(), tmp.end(), ' ', '\a');

    if ((ret = dm_search(_dicts[_cur_idx], dm_pack.get(), tmp.c_str(), len, DM_OUT_FMM)) != 0) {
        CFATAL_LOG("call dm_search fail, ret(%d)", ret);
        return false;
    }
    return dm_pack->ppseg_cnt > 0;
}

bool StringMatcWithSpacehDict::lookup_str(
        const std::string& input_str, 
        std::vector<std::string>& outputs) const {
    if (input_str.empty()) {
        CFATAL_LOG("input buf is empty");
        return false;
    }
    int32_t ret = 0;
    thread_local std::shared_ptr<dm_pack_t> dm_pack(dm_pack_create(100), [](dm_pack_t* ptr){
            dm_pack_del(ptr);   
        }); 
    
    if (nullptr == dm_pack) {
        CWARNING_LOG("dm_pack_create failed!");
        return false;
    }
    
    // search: replace space with '\a'
    std::string tmp(input_str);
    std::replace(tmp.begin(), tmp.end(), ' ', '\a');

    if ((ret = dm_search(_dicts[_cur_idx], dm_pack.get(), tmp.data(), input_str.size(), DM_OUT_FMM)) != 0) {
        CFATAL_LOG("call dm_search fail, ret(%d)", ret);
        return false;
    }

    for(int i = 0; i < dm_pack->ppseg_cnt; i++) {
        if (dm_pack->ppseg != nullptr && (dm_pack->ppseg)[i] != nullptr && (dm_pack->ppseg)[i]->pstr != nullptr) {
            std::string term((dm_pack->ppseg)[i]->pstr);
            // output: replace '\a' with space
            std::replace(term.begin(), term.end(), '\a', ' ');
            outputs.emplace_back(term);
            CDEBUG_LOG("lookup_str(%d) is %s", i, outputs.back().c_str());
        }
    }
    return true;
}

}  // namespace common_lib
}  // namespace themis
}  // namespace anti
/* vim: set ts=4 sw=4: */

