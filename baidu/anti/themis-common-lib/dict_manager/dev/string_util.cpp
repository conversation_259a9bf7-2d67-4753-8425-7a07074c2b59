// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: string_util.cpp
// @Last modified: 2017-03-21 17:48:48
// @Brief: 

#include "string_util.h"
#include <sstream>
#include <mutex>
#include <stdlib.h>
#include <limits.h>
#include <string.h>
#include <iomanip>

namespace anti {
namespace themis {
namespace common_lib {

std::string StringUtil::join(
        const std::vector<std::string>& strs,
        const std::string& sep) {
    return join(strs, sep, 0, strs.size());
}

// [start, end)
std::string StringUtil::join(
        const std::vector<std::string>& strs,
        const std::string& sep,
        uint32_t start,
        uint32_t end) {
    static std::mutex m;
    std::unique_lock<std::mutex> lock(m);
    if (end > strs.size() || start >= end) {
        return "";
    }
    static std::stringstream ss;
    ss.clear();
    ss.str("");
    for (uint32_t i = start; i< end - 1U; ++i) {
        ss << strs[i] << sep;
    }
    ss << strs[end -1];
    return ss.str();
}

void StringUtil::str_split(
        const std::string& line, 
        size_t start,
        size_t end, 
        const char* sep,
        std::vector<std::string>* results) {
    if (sep == NULL || results == NULL) {
        return;
    }
    size_t str_start = start;
    size_t str_end = line.find(sep, str_start);
    while (str_end != std::string::npos) {
        results->push_back(line.substr(str_start, str_end - str_start));
        str_start = str_end + 1;
        str_end = line.find(sep, str_start);  
    }
    results->push_back(line.substr(str_start, end - str_start));
}

/*
 * example:
 *  input:
 *      string = "朝阳区"
 *  out:
 *      std::unordered_map<std::string,int32_t>& frequency_map { <"e69c9d",1>, <"e998b3", 1> , <"e58cba", 1>}
 */
void StringUtil::get_utf8string_frequency(
            const std::string& line,
            std::unordered_map<std::string,int32_t>& frequency_map) {
    std::stringstream key;
    for(std::string::const_iterator it = line.begin(); it != line.end();) {
        int32_t value = (static_cast<short>(*it)&0xff);
        // UTF8 1 byte encode
        if (value >= 0 && value < 128) {
            key << std::hex << std::setw(2) << (static_cast<short>(*it)&0xff);
            it++;
        // UTF8 2 byte encode
        } else if (value >= 192 && value < 224) {
            for (int32_t i = 0 ; i < 2; i++) {
                key << std::hex << std::setw(2) << (static_cast<short>(*it)&0xff);
                it++;
            }
        // UTF8 3 byte encode
        } else if (value >= 224 && value < 240) {
            for (int32_t i = 0 ; i < 3; i++) {
                key << std::hex << std::setw(2) << (static_cast<short>(*it)&0xff);
                it++;
            }
        // UTF8 4 byte encode
        } else if (value >= 240 && value < 248) {
            for (int32_t i = 0 ; i < 4; i++) {
                key << std::hex << std::setw(2) << (static_cast<short>(*it)&0xff);
                it++;
            }
        // UTF8 5 byte encode
        } else if (value >= 248 && value < 252) {
            for (int32_t i = 0 ; i < 5; i++) {
                key << std::hex << std::setw(2) << (static_cast<short>(*it)&0xff);
                it++;
            }
        // UTF8 6 byte encode
        } else if (value >= 252 && value < 254) {
            for (int32_t i = 0 ; i < 6; i++) {
                key << std::hex << std::setw(2) << (static_cast<short>(*it)&0xff);
                it++;
            }
        // error case
        } else {
            CWARNING_LOG("get_utf8string_frequency error input %s", line.c_str());
            return;
        }
        frequency_map.find(key.str()) == frequency_map.end() ? 
            frequency_map[key.str()] = 1 : frequency_map[key.str()] += 1;
        key.str("");
    }
}

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

