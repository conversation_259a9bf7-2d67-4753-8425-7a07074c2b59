// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: file_dict_interface.cpp
// @Last modified: 2015-05-05 11:12:00
// @Brief: 

#include "file_dict_interface.h"
#include <sys/stat.h>
#include <com_log.h>

namespace anti {
namespace themis {
namespace common_lib {

bool FileDictBase::reload() {
    struct stat buf;
    if (stat(file_path().c_str(), &buf) != 0) {
        CFATAL_LOG("stat %s failed", file_path().c_str());
        return false;
    }
    
    if (_modify_time == buf.st_mtime) {
        return true;                    // no modify
    }

    if (!_reload()) {
        CWARNING_LOG("call file(%s) _reload() fail", file_path().c_str());
        return false;
    }
    _modify_time = buf.st_mtime;
    CWARNING_LOG("reload %s success", file_path().c_str());
    return true;
}

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

