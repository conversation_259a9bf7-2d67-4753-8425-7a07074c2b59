// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: zhouzhiqiang02(<EMAIL>)
// 
// @File: cn_map_file_dict.cpp
// @Last modified: 
// @Brief: 

#include "cn_map_file_dict.h"
#include <fstream>
#include <boost/algorithm/string.hpp>
#include <com_log.h>
#include <sign_util.h>
#include "string_util.h"

using anti::baselib::SignUtil;

namespace anti {
namespace themis {
namespace common_lib {

bool CnMapFileDict::init(const comcfg::ConfigUnit& conf) {
    try {
        _file_path = conf["file_path"].to_cstr();
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std Exception : %s", e.what());
        return false;
    }

    if (!reload()) {
        CWARNING_LOG("call reload fail, file(%s)", _file_path.c_str());
        return false;
    }
    return true;
}

void CnMapFileDict::uninit() {
    for (uint32_t i = 0; i < CN_MAP_KEY_NUM; ++i) {
        for (uint32_t j = 0; j < MAP_NUM; j++) {
            _inn_map[i][j].clear();
        }
    }

    _cur_idx = 0U;
}

bool CnMapFileDict::_reload() {
    uint32_t idle = (_cur_idx + 1U) % MAP_NUM;
    for (uint32_t i = 0; i < CN_MAP_KEY_NUM; i++) {
        _inn_map[i][idle].clear();
    }
    std::ifstream ifs(_file_path.c_str(), std::ifstream::in);
    if (ifs.fail()) {
        CWARNING_LOG("open file(%s) fail", _file_path.c_str());
        return false;
    }
    std::string line;
    while (!getline(ifs, line).eof()) {
        const uint32_t min_logic_col_num = 5;
        std::vector<std::string> fields;
        boost::split(fields, line, boost::is_any_of(" \t"), boost::token_compress_on);
        if (fields.size() < min_logic_col_num) {
            CWARNING_LOG("record format error.record:%s,col_num:%d,min_logic_col_num:%d",
                    line.c_str(), fields.size(), min_logic_col_num);
            return false;
        }

        CnMapValueType value;
        value.cn = fields[0];
        value.flow_group = fields[1];
        value.partner_tag = fields[2];
        value.channel_id = fields[4];

        for (uint32_t i = 0; i < CN_MAP_KEY_NUM; i++) {
            std::string key;
            switch (i) {
            case CN_MAP_KEY_CN : {
                key = fields.at(0);
                break;
            }
            case CN_MAP_KEY_CHANNEL : {
                key = fields.at(4);
                break;
            }
            default : {
                CWARNING_LOG("invalid map key %d, CN_MAP_KEY_NUM = %d",
                        i, CN_MAP_KEY_NUM);
                return false;                
            }
            }
            uint64_t sign = 0LU;
            SignUtil::create_sign_md64(key, &sign);
            if (!_inn_map[i][idle].insert(
                        std::pair<uint64_t, CnMapValueType>(sign, value)).second) {
                CDEBUG_LOG("insert to hashmap fail.key:%s sign %lu", 
                        key.c_str(), sign);
            }
        }
    }
    _cur_idx = idle;
    return true;
}

bool CnMapFileDict::get(
        const std::string& key, 
        CnMapValueType* value, 
        CnMapKeyIdx map_idx) const {
    if (value == NULL) {
        CWARNING_LOG("invalid param");
        return false;
    }

    uint64_t sign = 0;
    SignUtil::create_sign_md64(key, &sign);
    auto iter = _inn_map[map_idx][_cur_idx].find(sign);
    if (iter == _inn_map[map_idx][_cur_idx].end()) {
        return false;
    }
    
    *value = iter->second;
    return true;
}

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

