// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: corr_cnt_dict.cpp
// @Last modified: 2016-05-25 09:19:39
// @Brief: 

#include <fstream>
#include <boost/algorithm/string.hpp>
#include <com_log.h>
#include <sign_util.h>
#include "corr_cnt_dict.h"

using anti::baselib::SignUtil;

namespace anti {
namespace themis {
namespace common_lib {

const std::string CorrCntDict::SEP = "_";

void CorrCntDict::uninit() {
    for (uint32_t i = 0; i < NUM; ++i) {
        _maps[i].clear();
        _sets[i].clear();
    }
    _cur_idx = 0U;
}

bool CorrCntDict::init(const comcfg::ConfigUnit& conf) {
    try {
        _file_path = conf["file_path"].to_cstr();
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std Exception : %s", e.what());
        return false;
    }

    if (!reload()) {
        CWARNING_LOG("call reload fail, file(%s)", _file_path.c_str());
        return false;
    }
    return true;
}

bool CorrCntDict::_reload() {
    uint32_t idle = (_cur_idx + 1U) % NUM;
    _sets[idle].clear();
    _maps[idle].clear();
    std::ifstream ifs(_file_path.c_str(), std::ifstream::in);
    if (ifs.fail()) {
        CWARNING_LOG("open file(%s) fail", _file_path.c_str());
        return false;
    }
    const uint32_t LEGAL_COL_NUM = 3U;
    std::string line;
    while (!getline(ifs, line).eof()) {
        // gel online and each column
        std::vector<std::string> columns;
        boost::split(columns, line, boost::is_any_of("\t"), boost::token_compress_off); 
        if (columns.size() != LEGAL_COL_NUM) {
            CWARNING_LOG("get line(%s) invalid, line splite size(%lu) invalid, should be %d",
                    line.data(), columns.size(), LEGAL_COL_NUM);
            return false;
        }
        // split refs and cnts, expant them to one record with third column, etc, fixed cnt
        std::vector<std::string> refs;
        std::vector<std::string> cnts;
        const uint32_t REFS_COL = 0U;
        const uint32_t CNTS_COL = 1U;
        const uint32_t FIX_CNT_COL = 2U;
        boost::split(refs, columns[REFS_COL], boost::is_any_of(", "), boost::token_compress_on);
        boost::split(cnts, columns[CNTS_COL], boost::is_any_of(", "), boost::token_compress_on);
        for (auto ref_it = refs.begin(); ref_it != refs.end(); ++ref_it) {
            // add ref->fix_cnt into map
            uint64_t ref_sign = 0LU;                                     
            SignUtil::create_sign_md64(*ref_it, &ref_sign);
            _maps[idle].emplace(ref_sign, columns[FIX_CNT_COL]);
            for (auto cnt_it = cnts.begin(); cnt_it != cnts.end(); ++cnt_it) {
                // add ref_cnt join key into set 
                std::string join_fr_cnt = *ref_it + SEP + *cnt_it;
                uint64_t join_sign = 0LU;
                SignUtil::create_sign_md64(join_fr_cnt, &join_sign);
                _sets[idle].insert(join_sign);
            }
        }
    }
    _cur_idx = idle;
    return true;
}

CorrCntDict::CorrResult CorrCntDict::corr_cn(
        const std::string& fr,
        const std::string& cnt, 
        std::string* fix_cnt) const {
    if (_cur_idx >= NUM) {
        CFATAL_LOG("_cur_idx(%u) >= NUM(%s), file(%s)", _cur_idx, NUM, _file_path.data());
        return CORR_FAIL;
    }
    uint64_t ref_sign = 0LU;
    SignUtil::create_sign_md64(fr, &ref_sign);
    auto ref_it = _maps[_cur_idx].find(ref_sign);
    if (ref_it == _maps[_cur_idx].end()) { 
        // not in map then return corr_fail, not noneed
        return CORR_FAIL;
    }
    std::string join_fr_cnt = fr + SEP + cnt;
    uint64_t join_sign = 0LU;
    SignUtil::create_sign_md64(join_fr_cnt, &join_sign);
    // check ref-cnt pair
    if (_sets[_cur_idx].count(join_sign) == 1) {
        return NONEED_CORR;
    }
    *fix_cnt = ref_it->second;
    return CORR_SUCC;
}

}  // namespace common_lib
}  // namespace themis
}  // namespace anti
/* vim: set ts=4 sw=4: */
