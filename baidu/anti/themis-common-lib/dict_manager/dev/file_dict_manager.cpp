// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: file_dict_manager.cpp
// @Last modified: 2015-05-06 13:55:30
// @Brief: 

#include "file_dict_manager.h"
#include <sys/stat.h>
#include <mutex>
#include <com_log.h>
#include <Configure.h>
#include <unordered_set>
#include "file_dict_factory.h"

namespace anti {
namespace themis {
namespace common_lib {

typedef std::shared_ptr<FileDictInterface> DictPtr;
typedef std::shared_ptr<const FileDictInterface> ConstDictPtr;

FileDictManager::~FileDictManager() {
    uninit();
}

bool FileDictManager::init(
        const std::string& conf_path,
        const std::string& conf_file) {
    static std::mutex _mu;
    std::unique_lock<std::mutex> lock(_mu);
    if (_dicts.size() > 0U || _dicts_to_del.size() > 0U) {
        CFATAL_LOG("call init more than one time");
        return false;
    } 
    _conf_path = conf_path;
    _conf_file = conf_file;
    if (!reload()) {
        CWARNING_LOG("call reload fail, file(%s/%s)", 
                _conf_path.c_str(), _conf_file.c_str());
        return false;
    }
    CWARNING_LOG("init FileDictManager success");
    return true;
}

bool FileDictManager::multi_dicts_init(
        const std::vector<std::string>& conf_paths,
        const std::vector<std::string>& conf_files) {
    static std::mutex _mu;
    std::unique_lock<std::mutex> lock(_mu);
    if (_dicts.size() > 0U || _dicts_to_del.size() > 0U || 
            conf_paths.size() != conf_files.size() ||
            conf_paths.size() == 0U) {
        CFATAL_LOG("call multi_dicts_init more than one time");
        return false;
    }
    _dicts_conf_path_vec = conf_paths;
    _dicts_conf_file_vec = conf_files;
    _multi_dicts_modify_time_vec.assign(conf_paths.size(), 0U);
    if (!multi_dicts_reload()) {
        CWARNING_LOG("call multi_dicts_reload fail, one of file is (%s/%s)", 
                conf_paths[0].c_str(), conf_files[0].c_str());
        return false;
    }
    CWARNING_LOG("multi_init FileDictManager success");
    return true;
}

bool FileDictManager::multi_dicts_reload() {
    static std::mutex _mu;
    std::unique_lock<std::mutex> lock(_mu);
    bool need_reload = true;
    for (uint32_t i = 0; i < _dicts_conf_path_vec.size(); ++i) {
        std::string file = _dicts_conf_path_vec[i] + "/" + _dicts_conf_file_vec[i];
        struct stat buf;
        if (stat(file.c_str(), &buf) != 0) {
            CFATAL_LOG("multi dicts stat %s fail", file.c_str());
            return false;
        }
        if (_multi_dicts_modify_time_vec[i] != buf.st_mtime && need_reload) {
            if (!_multi_dicts_reload()) {
                CWARNING_LOG("call multi_dicts_reload fail, one of file(%s)", file.c_str());
                return false;
            }
            need_reload = false;
            _multi_dicts_modify_time_vec[i] = buf.st_mtime;
        }
    }
    std::unordered_map<std::string, DictPtr>::iterator iter = _dicts_to_del.begin(); 
    while (iter != _dicts_to_del.end()) {
        if ((*iter).second.unique()) {
            // _dicts_to_del is the only owner
            iter->second->uninit();
            CWARNING_LOG("multi dicts file_key(%s) released", (*iter).first.c_str());
            _dicts_to_del.erase(iter++);
            continue;
        }
        ++iter;
    }

    iter = _dicts.begin();
    while (iter != _dicts.end()) {
        if (!iter->second->reload()) {
            CFATAL_LOG("multi dicts call %s reload fail", (*iter).first.c_str());
        }
        ++iter;
    }
    CWARNING_LOG("multi dicts reload FileDictManager success");
    return true;
}

bool FileDictManager::reload() {
    static std::mutex _mu;
    std::unique_lock<std::mutex> lock(_mu);
    std::string file = _conf_path + "/" + _conf_file;
    struct stat buf;
    if (stat(file.c_str(), &buf) != 0) {
        CFATAL_LOG("stat %s fail", file.c_str());
        return false;
    }

    if (_modify_time != buf.st_mtime) {
        if (!_reload()) {
            CWARNING_LOG("call _reload fail, file(%s)", file.c_str());
            return false;
        }
        _modify_time = buf.st_mtime;
    }

    std::unordered_map<std::string, DictPtr>::iterator iter = _dicts_to_del.begin(); 
    while (iter != _dicts_to_del.end()) {
        if ((*iter).second.unique()) {
            // _dicts_to_del is the only owner
            iter->second->uninit();
            CWARNING_LOG("file_key(%s) released", (*iter).first.c_str());
            _dicts_to_del.erase(iter++);
            continue;
        }
        ++iter;
    }

    iter = _dicts.begin();
    while (iter != _dicts.end()) {
        if (!iter->second->reload()) {
            CFATAL_LOG("call %s reload fail", (*iter).first.c_str());
        }
        ++iter;
    }
    CWARNING_LOG("reload FileDictManager success");
    return true;
}

void FileDictManager::uninit() {
    for (std::unordered_map<std::string, DictPtr>::iterator iter = _dicts_to_del.begin();
            iter != _dicts_to_del.end();
            ++iter) {
        iter->second->uninit();
    }
    _dicts_to_del.clear();

    for (std::unordered_map<std::string, DictPtr>::iterator iter = _dicts.begin();
            iter != _dicts.end();
            ++iter) {
        iter->second->uninit();
    }
    _dicts.clear();

    _modify_time = 0U;
    _conf_path = "";
    _conf_file = "";

    _dicts_conf_path_vec.clear();
    _dicts_conf_file_vec.clear();
    _multi_dicts_modify_time_vec.clear();
    return;
}

ConstDictPtr FileDictManager::get_file_dict(const std::string& file_key) const {
    ConstDictPtr ptr;
    std::unordered_map<std::string, DictPtr>::const_iterator iter = _dicts.find(file_key);
    if (iter != _dicts.end()) {
        ptr = iter->second;
    }
    return ptr;
}

bool FileDictManager::_reload() {
    comcfg::Configure conf;
    if (conf.load(_conf_path.c_str(), _conf_file.c_str()) != 0) {
        CFATAL_LOG("load %s/%s failed", _conf_path.c_str(), _conf_file.c_str());
        return false;
    }
    _dicts_to_del.insert(_dicts.begin(), _dicts.end());
    uint32_t num = conf["file"].size();
    for (uint32_t i = 0; i < num; ++i) {
        try {
            std::string file_key(conf["file"][i]["file_key"].to_cstr());
            std::string file_type(conf["file"][i]["file_type"].to_cstr());
            // 1. remove file_key in conf, which means no need to delete;
            _dicts_to_del.erase(file_key);
            // 2. find file_key in dicts or not
            std::unordered_map<std::string, DictPtr>::iterator iter = _dicts.find(file_key);
            if (iter != _dicts.end()) {
                // 3. find means file_key was added before(not add this time), so continue
                continue;
            }
            DictPtr dict(FileDictFactory::create(file_type));
            if (!dict || !dict->init(conf["file"][i])) {
                // 4. create or init fail, continue
                CFATAL_LOG("call FileDictFactory::create(%s) or init fail", file_type.c_str());
                continue;
            }
            // 5. insert file_key and dict into _dicts
            _dicts[file_key] = dict;
        } catch (const comcfg::ConfigException& e) {
            CFATAL_LOG("ConfigException : %s", e.what());
            continue;
        } catch (...) {
            CFATAL_LOG("unknown exception");
            continue;
        }
    }

    // 6. remove dicts which not in conf(by dicts_to_del)
    std::unordered_map<std::string, DictPtr>::iterator iter = _dicts_to_del.begin(); 
    while (iter != _dicts_to_del.end()) {
        _dicts.erase((*iter++).first);
    }
    return true;
}

bool FileDictManager::_multi_dicts_reload() {
    // 不同词表文件如果出现相同的file_key 返回false
    std::vector<std::unordered_set<std::string>> all_dicts_file_key_vec(_dicts_conf_path_vec.size());
    std::unordered_set<std::string> dedup_file_key_set;
    //遍历词表file_key,统计结果
    for (uint32_t i = 0; i < _dicts_conf_path_vec.size(); ++i) {
        comcfg::Configure conf;
        if (conf.load(_dicts_conf_path_vec[i].c_str(), _dicts_conf_file_vec[i].c_str()) != 0) {
            CFATAL_LOG("multi dicts load %s/%s failed", _dicts_conf_path_vec[i].c_str(), 
                        _dicts_conf_file_vec[i].c_str());
            return false;
        }
        uint32_t num = conf["file"].size();
        for (uint32_t j = 0; j < num; ++j) {
            try {
                std::string file_key(conf["file"][j]["file_key"].to_cstr());
                all_dicts_file_key_vec[i].insert(file_key);
            } catch (const comcfg::ConfigException& e) {
                CFATAL_LOG("multi dicts ConfigException : %s", e.what());
                continue;
            } catch (...) {
                CFATAL_LOG("multi dicst unknown exception");
                continue;
            }
        }
    }
    // 判断不同词表文件file_key是否重复, 如果遇到重复的 返回false
    for (uint32_t i = 0; i < all_dicts_file_key_vec.size(); ++i) {
        for (auto& item : all_dicts_file_key_vec[i]) {
            if (dedup_file_key_set.count(item) > 0) {
                CFATAL_LOG("multi dicts file key[%s] is duplicated, one of in [%s/%s]", item.c_str(),
                                _dicts_conf_path_vec[i].c_str(), _dicts_conf_file_vec[i].c_str());
                return false;
            } else {
                dedup_file_key_set.insert(item);
            }
        }
    }

    _dicts_to_del.insert(_dicts.begin(), _dicts.end());
    for (uint32_t i = 0; i < _dicts_conf_path_vec.size(); ++i) {
        comcfg::Configure conf;
        if (conf.load(_dicts_conf_path_vec[i].c_str(), _dicts_conf_file_vec[i].c_str()) != 0) {
            CFATAL_LOG("multi dicts load %s/%s failed", _dicts_conf_path_vec[i].c_str(), 
                        _dicts_conf_file_vec[i].c_str());
            return false;
        }
        uint32_t num = conf["file"].size();
        for (uint32_t j = 0; j < num; ++j) {
            try {
                std::string file_key(conf["file"][j]["file_key"].to_cstr());
                std::string file_type(conf["file"][j]["file_type"].to_cstr());
                // 1. remove file_key in conf, which means no need to delete;
                _dicts_to_del.erase(file_key);
                // 2. find file_key in dicts or not
                std::unordered_map<std::string, DictPtr>::iterator iter = _dicts.find(file_key);
                if (iter != _dicts.end()) {
                    // 3. find means file_key was added before(not add this time), so continue
                    continue;
                }
                DictPtr dict(FileDictFactory::create(file_type));
                if (!dict || !dict->init(conf["file"][j])) {
                    // 4. create or init fail, continue
                    CFATAL_LOG("multi dicts call FileDictFactory::create(%s) or init fail", file_type.c_str());
                    continue;
                }
                // 5. insert file_key and dict into _dicts
                _dicts[file_key] = dict;
            } catch (const comcfg::ConfigException& e) {
                CFATAL_LOG("multi dicts ConfigException : %s", e.what());
                continue;
            } catch (...) {
                CFATAL_LOG("multi dicst unknown exception");
                continue;
            }
        }
        CWARNING_LOG("multi reload dict[%s/%s] success", _dicts_conf_path_vec[i].c_str(), _dicts_conf_file_vec[i].c_str());
    }

     // 6. remove dicts which not in conf(by dicts_to_del)
    std::unordered_map<std::string, DictPtr>::iterator iter = _dicts_to_del.begin(); 
    while (iter != _dicts_to_del.end()) {
        _dicts.erase((*iter++).first);
    }
    return true;
}

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

