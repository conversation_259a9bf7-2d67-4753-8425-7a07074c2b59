// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
//
// @File: file_dict_factory_test.cpp
// @Last modified: 2015-06-04 15:13:01
// @Brief:

#include <gtest/gtest.h>
#include "file_dict_factory.h"
#include "multi_column_file_dict.h"
#include "gray_file_dict.h"
#include "ipmap_dict.h"
#include "pcre_dict.h"
#include "multi_item_string_match_dict.h"

namespace anti {
namespace themis {
namespace common_lib {

TEST(FileDictFactoryTestSuite, create_by_different_type_case) {
    EXPECT_TRUE(FileDictFactory::create("xxxx") == NULL);

    FileDictInterface* obj = NULL;
    obj = FileDictFactory::create("mcf");
    EXPECT_TRUE(dynamic_cast<MultiColumnFileDict*>(obj) != NULL);
    delete obj;
    obj = NULL;

    obj = FileDictFactory::create("gray");
    EXPECT_TRUE(dynamic_cast<GrayFileDict*>(obj) != NULL);
    delete obj;
    obj = NULL;

    obj = FileDictFactory::create("ipmap");
    EXPECT_TRUE(dynamic_cast<IpmapDict*>(obj) != NULL);
    delete obj;
    obj = NULL;

    obj = FileDictFactory::create("ip6map");
    EXPECT_TRUE(dynamic_cast<Ip6mapDict*>(obj) != NULL);
    delete obj;
    obj = NULL;

    obj = FileDictFactory::create("regular_match");
    EXPECT_TRUE(dynamic_cast<PcreDict*>(obj) != NULL);
    delete obj;
    obj = NULL;

    obj = FileDictFactory::create("mul_item_str_match");
    EXPECT_TRUE(dynamic_cast<MultiItemStringMatchDict*>(obj) != NULL);
    delete obj;
    obj = NULL;
}

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

