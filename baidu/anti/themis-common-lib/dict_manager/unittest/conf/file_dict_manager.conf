# file2 and file3 are valid
[@file]
# miss file key
file_type : mcf
file_path : ./data/xxxxxx.data

[@file]
# miss file type
file_key  : file1
file_path : ./data/xxxxxx.data

[@file]
# invalid file_path
file_key  : file1
file_type : mcf
file_path : ./data/xxxxxx.data

[@file]
file_key  : file2
file_type : mcf
file_path : ./data/mcf.data

[@file]
# same file key, only insert one
file_key  : file2
file_type : mcf
file_path : ./data/mcf.data

[@file]
file_key  : file3
file_type : mcf
file_path : ./data/mcf.data
