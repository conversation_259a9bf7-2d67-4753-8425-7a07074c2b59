// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: string_match_dict_test.cpp
// @Last modified: 2016-05-12 15:07:58
// @Brief: 

#include <gtest/gtest.h>
#include "string_match_dict.h"

#include <stdlib.h>
#include "ul_dictmatch.h"
#include <string.h>
#include <stdio.h>
#include <getopt.h>
#include <assert.h>
#include <com_log.h>

namespace anti {
namespace themis {
namespace common_lib {

class StringMatchDictTestSuite : public ::testing::Test {
public:
    virtual void SetUp() {
        ASSERT_EQ(0, _conf.load("./conf", "smd_test.conf"));
    }

    virtual void TearDown() {}
private:
    comcfg::Configure _conf;
};

TEST_F(StringMatchDictTestSuite, construction_case) {
    StringMatchDict smd;
    EXPECT_EQ(0, smd._cur_idx);
    EXPECT_TRUE(smd._dicts[0] == NULL);
    EXPECT_TRUE(smd._dicts[1] == NULL);
    smd.uninit();
}

TEST_F(StringMatchDictTestSuite, init_case) {
    StringMatchDict smd;
    EXPECT_FALSE(smd.init(_conf["invalid"][0]));
}

TEST_F(StringMatchDictTestSuite, lookup_case) {
    StringMatchDict smd;
    ASSERT_TRUE(smd.init(_conf["valid"][0]));
    EXPECT_TRUE(smd._dicts[0] == NULL);
    EXPECT_TRUE(smd._dicts[1] != NULL);

    // case : input NULL
    EXPECT_FALSE(smd.lookup(NULL, 0));
    // case : lookup valid 
    std::vector<std::string> to_test;
    to_test.push_back("www.baidu.com");
    to_test.push_back("qq.com");
    to_test.push_back("xxxxx");
    bool exp[] = {true, false, false}; 
    for (uint32_t i = 0; i < 3; ++i) {
        EXPECT_EQ(exp[i], smd.lookup(to_test[i]));
    }
    smd.uninit();
}

TEST_F(StringMatchDictTestSuite, lookup_str_case) {
    StringMatchDict smd;
    ASSERT_TRUE(smd.init(_conf["valid"][0]));
    EXPECT_TRUE(smd._dicts[0] == NULL);
    EXPECT_TRUE(smd._dicts[1] != NULL);

    // case : input NULL
    std::string input_str = "";
    std::vector<std::string> outputs;
    EXPECT_FALSE(smd.lookup_str(input_str, outputs));
    
    // case : lookup valid 
    std::vector<std::string> to_test;
    to_test.push_back("www.baidu.com");
    to_test.push_back("qq.com");
    to_test.push_back("xxxxx");
    int exp[] = {1, 0, 0};
    for(int i = 0 ; i <to_test.size(); i++) {
        outputs.clear();
        EXPECT_TRUE(smd.lookup_str(to_test[i], outputs));
        EXPECT_EQ(exp[i], outputs.size());
    }
    smd.uninit();
}

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

