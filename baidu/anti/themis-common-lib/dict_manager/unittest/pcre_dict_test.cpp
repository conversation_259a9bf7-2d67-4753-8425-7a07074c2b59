// Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: pcre_dict_test.cpp
// @Last modified: 2022-08-16 15:07:58
// @Brief: 

#include <gtest/gtest.h>
#include <com_log.h>
#include "pcre_dict.h"

namespace anti {
namespace themis {
namespace common_lib {

class PcreDictTestSuite : public ::testing::Test {
public:
    virtual void SetUp() {
        ASSERT_EQ(0, _conf.load("./conf", "pcre_test.conf"));
    }

    virtual void TearDown() {}
private:
    comcfg::Configure _conf;
    // PcreDict _obj;
};

TEST_F(PcreDictTestSuite, construction_case) {
    PcreDict obj;
    EXPECT_EQ(0, obj._cur_idx);
    obj.uninit();
}

TEST_F(PcreDictTestSuite, init_case) {
    PcreDict obj;
    EXPECT_FALSE(obj.init(_conf["invalid"][0]));
}

TEST_F(PcreDictTestSuite, lookup_case) {
    PcreDict obj;
    ASSERT_TRUE(obj.init(_conf["valid"][0]));
    EXPECT_EQ(0, obj._vecs[0].size());
    EXPECT_EQ(9, obj._vecs[1].size());

    // case : input NULL
    EXPECT_FALSE(obj.lookup(NULL, 0));
    // case : lookup valid 
    std::vector<std::string> to_test;
    to_test.push_back("微信号ID11111111111");
    EXPECT_FALSE(to_test[0].empty());
    to_test.push_back("微信信号");
    to_test.push_back("weixing");
    std::vector<std::string> outputs;
    bool exp[] = {true, false, false}; 
    for (uint32_t i = 0; i < 3; ++i) {
        EXPECT_EQ(exp[i], obj.lookup(to_test[i]));
    }
    ASSERT_TRUE(obj.lookup_str(to_test[0], outputs));
    EXPECT_EQ(1, outputs.size());
    outputs.clear();
    ASSERT_TRUE(obj.lookup_str(to_test[1], outputs));
    EXPECT_EQ(0, outputs.size());
    obj.uninit();
    
}

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

