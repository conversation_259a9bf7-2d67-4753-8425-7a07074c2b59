// Copyright (c) 2024 Baidu.com, Inc. All Rights Reserved
// @Author: liushuai(<EMAIL>)
// 
// @File: string_match_with_sapce_test.cpp
// @Last modified: 2024-02-20 09:56:06
// @Brief: 

#include <gtest/gtest.h>
#include "str_match_with_space.h"
#include <stdlib.h>
#include "ul_dictmatch.h"
#include <string.h>
#include <stdio.h>
#include <getopt.h>
#include <assert.h>
#include <com_log.h>

namespace anti {
namespace themis {
namespace common_lib {

class StringMatchDictWithSpaceTestSuite : public ::testing::Test {
public:
    virtual void SetUp() {
        ASSERT_EQ(0, _conf.load("./conf", "smd_with_space_test.conf"));
    }

    virtual void TearDown() {}
private:
    comcfg::Configure _conf;
};

TEST_F(StringMatchDictWithSpaceTestSuite, construction_case) {
    StringMatcWithSpacehDict smd;
    EXPECT_EQ(0, smd._cur_idx);
    EXPECT_TRUE(smd._dicts[0] == NULL);
    EXPECT_TRUE(smd._dicts[1] == NULL);
    smd.uninit();
}

TEST_F(StringMatchDictWithSpaceTestSuite, init_case) {
    StringMatcWithSpacehDict smd;
    EXPECT_FALSE(smd.init(_conf["invalid"][0]));
}

TEST_F(StringMatchDictWithSpaceTestSuite, lookup_case) {
    StringMatcWithSpacehDict smd;
    ASSERT_TRUE(smd.init(_conf["valid"][0]));
    EXPECT_TRUE(smd._dicts[0] == NULL);
    EXPECT_TRUE(smd._dicts[1] != NULL);

    // case : input NULL
    EXPECT_FALSE(smd.lookup(NULL, 0));
    // case : lookup valid 
    std::vector<std::string> to_test;
    to_test.push_back("www.baidu.com");
    to_test.push_back("qq.com");
    to_test.push_back("xxxxx");
    bool exp[] = {true, false, false}; 
    for (uint32_t i = 0; i < 3; ++i) {
        EXPECT_EQ(exp[i], smd.lookup(to_test[i]));
    }
    smd.uninit();
}

TEST_F(StringMatchDictWithSpaceTestSuite, lookup_str_case) {
    StringMatcWithSpacehDict smd;
    ASSERT_TRUE(smd.init(_conf["valid"][0]));
    EXPECT_TRUE(smd._dicts[0] == NULL);
    EXPECT_TRUE(smd._dicts[1] != NULL);

    // case : input NULL
    std::string input_str = "";
    std::vector<std::string> outputs;
    EXPECT_FALSE(smd.lookup_str(input_str, outputs));
    
    // case : lookup valid 
    std::vector<std::string> to_test;
    to_test.push_back("www.baidu.com");
    to_test.push_back("qq.com");
    to_test.push_back("xxxxx");
    to_test.push_back("bai du.com");
    to_test.push_back("ba idu123");
    to_test.push_back("北京 天津");
    to_test.push_back("北京天津");
    to_test.push_back("习 近 平");
    to_test.push_back("习近平");

    int exp[] = {1, 0, 0, 1, 1, 1, 0, 1, 0};
    for(int i = 0 ; i <to_test.size(); i++) {
        outputs.clear();
        CDEBUG_LOG("test_string is [%s]", to_test[i].c_str());
        EXPECT_TRUE(smd.lookup_str(to_test[i], outputs));
        EXPECT_EQ(exp[i], outputs.size());
    }
    smd.uninit();
}

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

