// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: file_dict_interface_test.cpp
// @Last modified: 2015-04-23 12:11:48
// @Brief: 

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "file_dict_interface.h"

using ::testing::Return;
using ::testing::ReturnRef;

namespace anti {
namespace themis {
namespace common_lib {

class MockFileDictBase : public FileDictBase {
public:
    MOCK_METHOD1(init, bool(const comcfg::ConfigUnit& conf));
    MOCK_METHOD0(uninit, void());
    MOCK_CONST_METHOD0(file_path, const std::string&());
    MOCK_METHOD0(_reload, bool());
};

class FileDictBaseTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {}
    virtual void TearDown() {}

private:
    MockFileDictBase _obj;
};

TEST_F(FileDictBaseTestSuite, construction_case) {
    EXPECT_EQ(0U, _obj._modify_time);
}

TEST_F(FileDictBaseTestSuite, reload_fail_case) {
    std::string no_file("xxxxx");
    std::string empty_file("./data/empty.data");
    EXPECT_CALL(_obj, file_path()).Times(4)
            .WillOnce(ReturnRef(no_file))
            .WillOnce(ReturnRef(no_file))
            .WillRepeatedly(ReturnRef(empty_file));

    EXPECT_CALL(_obj, _reload()).WillOnce(Return(false));
    ASSERT_FALSE(_obj.reload());
    ASSERT_FALSE(_obj.reload());
}

TEST_F(FileDictBaseTestSuite, reload_success_case) {
    std::string empty_file("./data/empty.data");
    EXPECT_CALL(_obj, file_path()).WillRepeatedly(ReturnRef(empty_file));
    EXPECT_CALL(_obj, _reload()).WillOnce(Return(true));

    struct stat exp;
    ASSERT_EQ(0, stat(empty_file.data(), &exp));
    // call reload twice, but only call _reload once
    ASSERT_TRUE(_obj.reload());
    EXPECT_EQ(exp.st_mtime, _obj._modify_time);

    ASSERT_TRUE(_obj.reload());
    EXPECT_EQ(exp.st_mtime, _obj._modify_time);
}

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

