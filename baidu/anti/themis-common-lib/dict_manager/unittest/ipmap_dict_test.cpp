// Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
// @file ipmap_dict_test.cpp
// <AUTHOR>
// @date 2018/04/12 11:56:46
// @brief

#include <gtest/gtest.h>
#include "ipmap_dict.h"

namespace anti {
namespace themis {
namespace common_lib {

const std::string conf_path("./conf");
const std::string conf_file("ipmap_dict_test.conf");

int main(int argc, char **argv)
{
    testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}

class TestIpmapDictSuite : public ::testing::Test {
public:
    virtual void SetUp() {
        ASSERT_TRUE(_conf.load(conf_path.data(), conf_file.data()) == 0);
    }
    virtual void TearDown() {
    }
private:
    IpmapDict _obj;
    Ip6mapDict _obj6;
    comcfg::Configure _conf;
};

TEST_F(TestIpmapDictSuite, test_init_valid) {
    EXPECT_TRUE(_obj.init(_conf["valid"][0]));
    EXPECT_STREQ("\t", _obj._field_sep.data());
    EXPECT_EQ(4, _obj._field_num);
    EXPECT_STREQ("./data/ipmap.data", _obj._file_path.c_str());

    EXPECT_TRUE(_obj.init(_conf["valid"][1]));
    EXPECT_STREQ("|", _obj._field_sep.data());
    EXPECT_EQ(3, _obj._field_num);
    EXPECT_STREQ("./data/gateway_ip.data", _obj._file_path.c_str());

    EXPECT_TRUE(_obj.init(_conf["valid"][2]));
    EXPECT_STREQ("|", _obj._field_sep.data());
    EXPECT_EQ(2, _obj._field_num);
    EXPECT_STREQ("./data/gateway_ip_2.data", _obj._file_path.c_str());

    EXPECT_TRUE(_obj6.init(_conf["valid"][3]));
    EXPECT_STREQ("|", _obj6._field_sep.data());
    EXPECT_STREQ("./data/ip6map.data", _obj6._file_path.c_str());
}

TEST_F(TestIpmapDictSuite, test_lookup_pid_cid) {
    EXPECT_TRUE(_obj.init(_conf["valid"][0]));
    EXPECT_TRUE(_obj.lookup("*********"));
    EXPECT_FALSE(_obj.lookup("*******"));
    int pid = 0;
    int cid = 0;
    EXPECT_TRUE(_obj.lookup("*********", &pid, &cid));
    EXPECT_EQ(1, pid);
    EXPECT_EQ(11, cid);
    EXPECT_FALSE(_obj.lookup("*******", &pid, &cid));

    EXPECT_TRUE(_obj6.init(_conf["valid"][3]));
    EXPECT_TRUE(_obj6.lookup("240E:84::"));
    EXPECT_FALSE(_obj6.lookup("2001:6::"));
    EXPECT_TRUE(_obj6.lookup("240E:84::", &pid, &cid));
    EXPECT_EQ(15, pid);
    EXPECT_EQ(335, cid);
    EXPECT_FALSE(_obj6.lookup("2001:6::", &pid, &cid));
}

TEST_F(TestIpmapDictSuite, test_lookup_gateway_ip) {
    EXPECT_TRUE(_obj.init(_conf["valid"][1]));
    EXPECT_TRUE(_obj.lookup("**********"));
    EXPECT_FALSE(_obj.lookup("*******"));
    int pid = 0;
    int cid = 0;
    EXPECT_TRUE(_obj.lookup("**********", &pid, &cid));
    EXPECT_EQ(637, pid);
    EXPECT_EQ(0, cid);
    EXPECT_FALSE(_obj.lookup("*******", &pid, &cid));
}

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */
