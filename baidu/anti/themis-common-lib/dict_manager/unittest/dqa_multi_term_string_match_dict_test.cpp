// Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved
// @Author: liushuai31(<EMAIL>)
// 
// @File: dqa_multi_term_string_match_dict_test.cpp
// @Last modified: 2023-09-19 17:07:58
// @Brief: 

#include <gtest/gtest.h>
#include "dqa_multi_term_string_match_dict.h"
#include "com_log.h"

namespace anti {
namespace themis {
namespace common_lib {

class DqaMultiTermStringMatchDictTestSuite : public ::testing::Test {
public:
    virtual void SetUp() {}
    virtual void TearDown() {}

private:
    DqaMultiTermStringMatchDict _obj;
};

TEST_F(DqaMultiTermStringMatchDictTestSuite, construction_case) {
    EXPECT_EQ(0U, _obj._cur_idx);
}

TEST_F(DqaMultiTermStringMatchDictTestSuite, init_by_conf_case) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "dqa_mtd_test.conf"));
    for (uint32_t i = 0; i < conf["invalid"].size(); ++i) {
        CDEBUG_LOG("invalid:%u", i);
        EXPECT_FALSE(_obj.init(conf["invalid"][i]));
        _obj.uninit();
    }

    ASSERT_TRUE(_obj.init(conf["valid"][0]));
    EXPECT_EQ(1U, _obj._cur_idx);

    // no modify 
    ASSERT_TRUE(_obj.reload());
    EXPECT_EQ(1U, _obj._cur_idx);
    _obj.uninit();
}

TEST_F(DqaMultiTermStringMatchDictTestSuite, _reload_by_no_file_case) {
    _obj._file_path = "xxxx";
    EXPECT_FALSE(_obj._reload());
}

TEST_F(DqaMultiTermStringMatchDictTestSuite, lookup_case) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "dqa_mtd_test.conf"));
    ASSERT_TRUE(_obj.init(conf["valid"][0]));
    
    // in 
    std::vector<std::string> tmp;
    tmp.push_back("小学语文分数");
    tmp.push_back("我们小学生的语文分数都很好");
    tmp.push_back("大学性别课程");
    tmp.push_back("性别课程大学");    
    tmp.push_back("性别大学课程");        
    for (uint32_t i = 0; i < 5; ++i) {
        ASSERT_TRUE(_obj.lookup(tmp[i]));
    }

    // not in
    ASSERT_FALSE(_obj.lookup("中学历史分数"));
    ASSERT_FALSE(_obj.lookup("数学课程中国"));
    
    // cur_idx > 1
    _obj._cur_idx = 2U;
    ASSERT_FALSE(_obj.lookup("中学语文"));
    ASSERT_FALSE(_obj.lookup("高中历史"));
    _obj.uninit();
}

TEST_F(DqaMultiTermStringMatchDictTestSuite, lookup_str_case) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "dqa_mtd_test.conf"));
    ASSERT_TRUE(_obj.init(conf["valid"][0]));
    
    // in 
    std::vector<std::string> outputs;
    std::string tmp = "小学语文分数";
    ASSERT_TRUE(_obj.lookup_str(tmp, outputs));
    ASSERT_EQ(3, outputs.size());
    ASSERT_EQ("小学", outputs[0]);
    ASSERT_EQ("语文", outputs[1]);
    ASSERT_EQ("分数", outputs[2]);
    outputs.clear();

    tmp = "大学xx性别xx课程xxxx";
    ASSERT_TRUE(_obj.lookup_str(tmp, outputs));
    ASSERT_EQ(3, outputs.size());
    ASSERT_EQ("大学", outputs[0]);
    ASSERT_EQ("性别", outputs[1]);
    ASSERT_EQ("课程", outputs[2]);
    outputs.clear();

    tmp = "中学曾经数学不太好但是没有";
    ASSERT_TRUE(_obj.lookup_str(tmp, outputs));
    ASSERT_EQ(2, outputs.size());
    ASSERT_EQ("中学", outputs[0]);
    ASSERT_EQ("数学", outputs[1]);
    outputs.clear();

    // not in
    ASSERT_TRUE(_obj.lookup_str("中学历史分数", outputs));
    ASSERT_EQ(0, outputs.size());

    ASSERT_TRUE(_obj.lookup_str("数学课程中国", outputs));
    ASSERT_EQ(0, outputs.size());
    
    // cur_idx > 1
    _obj._cur_idx = 2U;
    ASSERT_FALSE(_obj.lookup_str("高中历史性别", outputs));
    ASSERT_EQ(0, outputs.size());

    ASSERT_FALSE(_obj.lookup_str("", outputs));
    ASSERT_EQ(0, outputs.size());
    _obj.uninit();
}

}  // namespace common_lib
}  // namespace themis
}  // namespace anti