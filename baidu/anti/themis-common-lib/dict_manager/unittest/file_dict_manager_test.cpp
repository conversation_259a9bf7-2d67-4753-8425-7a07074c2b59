// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: file_dict_manager_test.cpp
// @Last modified: 2015-06-04 15:15:02
// @Brief: 

#include <gtest/gtest.h>
#include "file_dict_manager.h"
#include "file_dict_factory.h"

namespace anti {
namespace themis {
namespace common_lib {

typedef std::shared_ptr<FileDictInterface> DictPtr;
typedef std::shared_ptr<const FileDictInterface> ConstDictPtr;

TEST(FileDictManagerTestSuite, construction_case) {
    EXPECT_EQ(0U, FileDictManagerSingleton::instance()._modify_time);
}

TEST(FileDictManagerTestSuite, init_case) {
    // no conf
    EXPECT_FALSE(FileDictManagerSingleton::instance().init("xxx", "xxx"));
    // init success
    ASSERT_TRUE(FileDictManagerSingleton::instance().init("./conf", "file_dict_manager.conf"));
    EXPECT_EQ(2U, FileDictManagerSingleton::instance()._dicts.size());
    struct stat tmp;
    ASSERT_EQ(0, stat("./conf/file_dict_manager.conf", &tmp));
    EXPECT_EQ(tmp.st_mtime, FileDictManagerSingleton::instance()._modify_time);

    // init twice
    ASSERT_FALSE(FileDictManagerSingleton::instance().init("./conf", "file_dict_manager.conf"));
    FileDictManagerSingleton::instance().uninit();
}

TEST(FileDictManagerTestSuite, get_file_dict_case) {
    ASSERT_TRUE(FileDictManagerSingleton::instance().init("./conf", "file_dict_manager.conf"));
    // get success
    std::string key[2] = {"file2", "file3"};
    for (uint32_t i = 0U; i < 2U; ++i) {
        ConstDictPtr ptr = FileDictManagerSingleton::instance().get_file_dict(key[i]);
        EXPECT_TRUE(ptr.get() != NULL);
        EXPECT_EQ(2, ptr.use_count());
    }
    // get fail
    {
        ConstDictPtr ptr = FileDictManagerSingleton::instance().get_file_dict("xxx");
        EXPECT_FALSE(ptr.get() != NULL);
    }
    FileDictManagerSingleton::instance().uninit();
}

TEST(FileDictManagerTestSuite, _reload_by_no_conf_case) {
    FileDictManagerSingleton::instance()._conf_path = "xxx";
    FileDictManagerSingleton::instance()._conf_file = "xxx";
    EXPECT_FALSE(FileDictManagerSingleton::instance()._reload());
    FileDictManagerSingleton::instance().uninit();
}

TEST(FileDictManagerTestSuite, reload_case) {
    // insert file0, file01 to FileDictManager
    {
        DictPtr p0(FileDictFactory::create("mcf"));
        FileDictManagerSingleton::instance()._dicts["file0"] = p0;
        DictPtr p01(FileDictFactory::create("mcf"));
        FileDictManagerSingleton::instance()._dicts["file01"] = p01;
        DictPtr p2(FileDictFactory::create("mcf"));
        FileDictManagerSingleton::instance()._dicts["file2"] = p2;
    }
    FileDictManagerSingleton::instance()._conf_path = "./conf";
    FileDictManagerSingleton::instance()._conf_file = "file_dict_manager.conf";

    ConstDictPtr ptr = FileDictManagerSingleton::instance().get_file_dict("file0");
    ASSERT_TRUE(ptr.get() != NULL);
    // file0, file01 mv to _dicts_to_del and rm file1
    // file2 reload fail
    // file3 insert && init success
    ASSERT_TRUE(FileDictManagerSingleton::instance().reload());
    EXPECT_EQ(2U, FileDictManagerSingleton::instance()._dicts.size());
    EXPECT_TRUE(FileDictManagerSingleton::instance()._dicts.count("file2") > 0);
    EXPECT_TRUE(FileDictManagerSingleton::instance()._dicts.count("file3") > 0);
    EXPECT_EQ(1U, FileDictManagerSingleton::instance()._dicts_to_del.size());
    EXPECT_TRUE(FileDictManagerSingleton::instance()._dicts_to_del.count("file0") > 0);
    
    // rm file0
    ptr.reset();
    ASSERT_TRUE(FileDictManagerSingleton::instance().reload());
    EXPECT_EQ(2U, FileDictManagerSingleton::instance()._dicts.size());
    EXPECT_TRUE(FileDictManagerSingleton::instance()._dicts.count("file2") > 0);
    EXPECT_TRUE(FileDictManagerSingleton::instance()._dicts.count("file3") > 0);
    EXPECT_EQ(0U, FileDictManagerSingleton::instance()._dicts_to_del.size());
    EXPECT_TRUE(FileDictManagerSingleton::instance()._dicts_to_del.count("file0") == 0);

    FileDictManagerSingleton::instance().uninit();
}

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

