// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// @Author: lijie(<EMAIL>)
// 
// @File: array_file_dict_test.cpp

#include <gtest/gtest.h>
#include "array_file_dict.h"

namespace anti {
namespace themis {
namespace common_lib {

typedef ArrayFileDict<double> FileDict;

class ArrayFileDictTestSuite : public ::testing::Test {
public:
    virtual void SetUp() {}
    virtual void TearDown() {}

private:
    FileDict _obj;
};

TEST_F(ArrayFileDictTestSuite, construction_case) {
    EXPECT_EQ(0U, _obj._cur_idx);
    EXPECT_EQ(0U, _obj._row);
    EXPECT_EQ(0U, _obj._col);
}

TEST_F(ArrayFileDictTestSuite, init_by_conf_case) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "array_test.conf"));
    for (uint32_t i = 0; i < conf["invalid"].size(); ++i) {
        EXPECT_FALSE(_obj.init(conf["invalid"][i]));
        _obj.uninit();
    }

    ASSERT_TRUE(_obj.init(conf["valid"][0]));
    EXPECT_EQ(1U, _obj._cur_idx);
    EXPECT_EQ(3U, _obj._vecs[_obj._cur_idx].size());
    
    ASSERT_TRUE(_obj.reload());
    EXPECT_EQ(1U, _obj._cur_idx);
    EXPECT_EQ(3U, _obj._vecs[_obj._cur_idx].size());
    _obj.uninit();

    FileDict obj;
    ASSERT_TRUE(obj.init(conf["valid"][1]));
    EXPECT_EQ(1U, obj._cur_idx);
    EXPECT_EQ(3U, obj._vecs[obj._cur_idx].size());
    
    ASSERT_TRUE(obj.reload());
    EXPECT_EQ(1U, obj._cur_idx);
    EXPECT_EQ(3U, obj._vecs[1].size());
    obj.uninit();
}

TEST_F(ArrayFileDictTestSuite, _reload_failed_case) {
    _obj._file_path = "xxxx";
    EXPECT_FALSE(_obj._reload());

    // row doesn't match
    _obj._file_path = "./data/array.data";
    _obj._row = 1U;
    EXPECT_FALSE(_obj._reload());

    // invalid col data
    _obj._file_path = "./data/invalid_array.data";
    _obj._row = 3U;
    _obj._col = 1U;
    EXPECT_FALSE(_obj._reload());
}

TEST_F(ArrayFileDictTestSuite, lookup_by_different_input_case) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "array_test.conf"));
    ASSERT_TRUE(_obj.init(conf["valid"][0]));
    EXPECT_FALSE(_obj.lookup(1, NULL));
    
    const std::vector<double>* array = NULL;
    EXPECT_FALSE(_obj.lookup(0, &array));

    ASSERT_TRUE(_obj.lookup(1, &array));
    EXPECT_DOUBLE_EQ(2.0, (*array)[0]);
    EXPECT_DOUBLE_EQ(2.0, (*array)[1]);

    ASSERT_TRUE(_obj.lookup(2, &array));
    EXPECT_DOUBLE_EQ(3.0, (*array)[0]);
    EXPECT_DOUBLE_EQ(3.0, (*array)[1]);

    _obj.uninit();
}

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

