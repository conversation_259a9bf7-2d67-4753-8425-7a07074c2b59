// Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
// @Author: <PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
// 
// @File: multi_term_string_match_dict_test.cpp
// @Last modified: 2022-11-21 17:07:58
// @Brief: 

#include <gtest/gtest.h>
#include "multi_item_string_match_dict.h"
#include "com_log.h"

namespace anti {
namespace themis {
namespace common_lib {

class MultiItemStringMatchDictTestSuite : public ::testing::Test {
public:
    virtual void SetUp() {}
    virtual void TearDown() {}

private:
    MultiItemStringMatchDict _obj;
};

TEST_F(MultiItemStringMatchDictTestSuite, construction_case) {
    EXPECT_EQ(0U, _obj._cur_idx);
}

TEST_F(MultiItemStringMatchDictTestSuite, init_by_conf_case) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "mtd_test.conf"));
    for (uint32_t i = 0; i < conf["invalid"].size(); ++i) {
        CDEBUG_LOG("invalid:%u", i);
        EXPECT_FALSE(_obj.init(conf["invalid"][i]));
        _obj.uninit();
    }

    ASSERT_TRUE(_obj.init(conf["valid"][0]));
    EXPECT_EQ(1U, _obj._cur_idx);
    EXPECT_EQ(4U, _obj._sets[_obj._cur_idx].size());
    // EXPECT_EQ(-1, _obj._column);
    
    ASSERT_TRUE(_obj.reload());
    EXPECT_EQ(1U, _obj._cur_idx);
    EXPECT_EQ(4U, _obj._sets[_obj._cur_idx].size());
    _obj.uninit();
}

TEST_F(MultiItemStringMatchDictTestSuite, _reload_by_no_file_case) {
    _obj._file_path = "xxxx";
    EXPECT_FALSE(_obj._reload());
}

TEST_F(MultiItemStringMatchDictTestSuite, lookup_case) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "mtd_test.conf"));
    ASSERT_TRUE(_obj.init(conf["valid"][0]));
    
    // in 
    std::vector<std::string> tmp;
    tmp.push_back("我们,22,小学,是,xxx,我的,语文,成绩,是,20,,,分数,很高");
    tmp.push_back("大学,xx,性别,xx,课程,xx,xx");
    tmp.push_back("中学,曾经,数学,不太好,但是,没有");
    tmp.push_back("高中,历史,性别");
    for (uint32_t i = 0; i < 4; ++i) {
        ASSERT_TRUE(_obj.lookup(tmp[i]));
    }

    // not in
    ASSERT_FALSE(_obj.lookup("历史,高中,性别"));
    ASSERT_FALSE(_obj.lookup("xxxxx"));
    
    _obj._cur_idx = 2U;
    ASSERT_FALSE(_obj.lookup("高中,历史,性别"));
    ASSERT_FALSE(_obj.lookup(""));
    _obj.uninit();
}

TEST_F(MultiItemStringMatchDictTestSuite, lookup_str_case) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "mtd_test.conf"));
    ASSERT_TRUE(_obj.init(conf["valid"][0]));
    
    // in 
    std::vector<std::string> outputs;
    std::string tmp = "我们,22,小学,是,xxx,我的,语文,成绩,是,20,,,分数,很高";
    ASSERT_TRUE(_obj.lookup_str(tmp, outputs));
    ASSERT_EQ(3, outputs.size());
    ASSERT_EQ("小学", outputs[0]);
    ASSERT_EQ("语文", outputs[1]);
    ASSERT_EQ("分数", outputs[2]);
    outputs.clear();

    tmp = "大学,xx,性别,xx,课程,xx,xx";
    ASSERT_TRUE(_obj.lookup_str(tmp, outputs));
    ASSERT_EQ(3, outputs.size());
    ASSERT_EQ("大学", outputs[0]);
    ASSERT_EQ("性别", outputs[1]);
    ASSERT_EQ("课程", outputs[2]);
    outputs.clear();

    tmp = "中学,曾经,数学,不太好,但是,没有";
    ASSERT_TRUE(_obj.lookup_str(tmp, outputs));
    ASSERT_EQ(2, outputs.size());
    ASSERT_EQ("中学", outputs[0]);
    ASSERT_EQ("数学", outputs[1]);
    outputs.clear();

    // not in
    ASSERT_TRUE(_obj.lookup_str("历史,高中,性别", outputs));
    ASSERT_EQ(0, outputs.size());

    ASSERT_TRUE(_obj.lookup_str("xxxxx", outputs));
    ASSERT_EQ(0, outputs.size());
    
    _obj._cur_idx = 2U;
    ASSERT_FALSE(_obj.lookup_str("高中,历史,性别", outputs));
    ASSERT_EQ(0, outputs.size());
    
    ASSERT_FALSE(_obj.lookup_str("", outputs));
    ASSERT_EQ(0, outputs.size());
    _obj.uninit();
}

}  // namespace common_lib
}  // namespace themis
}  // namespace anti