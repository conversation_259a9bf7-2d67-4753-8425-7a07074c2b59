// Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
// @file ipmap_test.cpp
// <AUTHOR>
// @date 2018/04/12 11:55:44
// @brief 
 
#include <ipmap.h>
#include <gtest/gtest.h>

namespace anti {
namespace themis {
namespace common_lib {

int main(int argc, char **argv)
{
    testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
 
class TestIpmapSuite : public ::testing::Test {
public:
    virtual void SetUp() {
    }
    virtual void TearDown() {
    }
private:
    ipmap_t _ipmap;
};
 
TEST_F(TestIpmapSuite, test_set_data_file_default_value) {
    EXPECT_EQ(0, _ipmap._set_data_file("./data/ipmap.data"));
}
 
TEST_F(TestIpmapSuite, test_set_data_file_with_param) {
    EXPECT_EQ(0, _ipmap._set_data_file("./data/gateway_ip.data", '|', 3));
    EXPECT_EQ(0, _ipmap._set_data_file("./data/gateway_ip_2.data", '|', 2));
}
 
TEST_F(TestIpmapSuite, test_set_data_file_fail) {
    EXPECT_EQ(-1, _ipmap._set_data_file("./data/gateway_ip.data"));
    EXPECT_EQ(-1, _ipmap._set_data_file("./data/gateway_ip_2.data", '|'));
}
 
TEST_F(TestIpmapSuite, test_get_pid_cid) {
    EXPECT_EQ(0, _ipmap._set_data_file("./data/ipmap.data"));
    int pid = 0;
    int cid = 0;
    ip_t ip = _ipmap.ip_from_str("*********");
    EXPECT_EQ(1, _ipmap.get(ip, pid, cid));
    EXPECT_EQ(1, pid);
    EXPECT_EQ(11, cid);
    
    pid = -1;
    cid = -1;
    ip = _ipmap.ip_from_str("*******");
    EXPECT_EQ(0, _ipmap.get(ip, pid, cid));
    EXPECT_EQ(-1, pid);
    EXPECT_EQ(-1, cid);
}
 
TEST_F(TestIpmapSuite, test_get_gateway_ip) {
    EXPECT_EQ(0, _ipmap._set_data_file("./data/gateway_ip.data", '|', 3));
    int pid = 0;
    int cid = 0;
    ip_t ip = _ipmap.ip_from_str("*******");
    EXPECT_EQ(0, _ipmap.get(ip, pid, cid));
    
    ip = _ipmap.ip_from_str("**********");
    EXPECT_EQ(1, _ipmap.get(ip, pid, cid));
}

TEST_F(TestIpmapSuite, test_get_gateway_ip_empty) {
    EXPECT_EQ(0, _ipmap._set_data_file("./data/black_ip.data", '\t', 4));
    int pid = 0;
    int cid = 0;
    EXPECT_EQ(0, _ipmap.get('-', pid, cid));
    EXPECT_EQ(1, _ipmap.get("0.0.0.0", pid, cid));
    EXPECT_EQ(1, _ipmap.get("***********", pid, cid));
    EXPECT_EQ(0, _ipmap.get("***********", pid, cid));
    EXPECT_EQ(1, _ipmap.get("**************", pid, cid));
    EXPECT_EQ(1, _ipmap.get("*************", pid, cid));
    EXPECT_EQ(1, _ipmap.get("**************", pid, cid));
    EXPECT_EQ(0, _ipmap.get("1.1.1.256", pid, cid));
    EXPECT_EQ(0, _ipmap.get("240E:84:3FFF:FFFF:FFFF:FFFF:FFFF:FFFF", pid, cid)); 
    EXPECT_EQ(0, _ipmap.get("FFFF:FFFF:FFFF:FFFF:FFFF:FFFF:FFFF:FFFF", pid, cid));
    EXPECT_EQ(0, _ipmap.get("0", pid, cid));
    EXPECT_EQ(0, _ipmap.get("", pid, cid));
}

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */
