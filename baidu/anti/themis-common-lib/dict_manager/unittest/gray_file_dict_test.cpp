// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: gray_file_dict_test.cpp
// @Last modified: 2015-06-04 15:19:48
// @Brief: 

#include <gtest/gtest.h>
#include "gray_file_dict.h"

namespace anti {
namespace themis {
namespace common_lib {

class GrayFileDictTestSuite : public ::testing::Test {
public:
    virtual void SetUp() {}
    virtual void TearDown() {}

private:
    GrayFileDict _obj;
};

TEST_F(GrayFileDictTestSuite, construction_case) {
    EXPECT_EQ(0U, _obj._cur_idx);
    EXPECT_EQ(0U, _obj._view_num);
}

TEST_F(GrayFileDictTestSuite, init_by_conf_case) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "gray_test.conf"));
    for (uint32_t i = 0; i < conf["invalid"].size(); ++i) {
        EXPECT_FALSE(_obj.init(conf["invalid"][i]));
        _obj.uninit();
    }

    ASSERT_TRUE(_obj.init(conf["valid"][0]));
    EXPECT_EQ(1U, _obj._cur_idx);
    EXPECT_EQ(2U, _obj._maps[_obj._cur_idx].size());
    
    ASSERT_TRUE(_obj.reload());
    EXPECT_EQ(1U, _obj._cur_idx);
    EXPECT_EQ(2U, _obj._maps[_obj._cur_idx].size());
    _obj.uninit();
}

TEST_F(GrayFileDictTestSuite, _reload_by_no_file_case) {
    _obj._file_path = "xxxx";
    EXPECT_FALSE(_obj._reload());

    // column doesn't match
    _obj._file_path = "./data/gray.data";
    _obj._view_num = 1U;
    EXPECT_FALSE(_obj._reload());

    // invalid threshold
    _obj._file_path = "./data/invalid_gray.data";
    _obj._view_num = 2U;
    EXPECT_FALSE(_obj._reload());
}

TEST_F(GrayFileDictTestSuite, lookup_by_different_input_case) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "gray_test.conf"));
    ASSERT_TRUE(_obj.init(conf["valid"][0]));
    std::vector<std::string> strs;
    EXPECT_FALSE(_obj.lookup(strs, NULL));
    
    strs.push_back("12");
    strs.push_back("345");
    EXPECT_FALSE(_obj.lookup(strs, NULL));

    double threshold = 0.0;
    ASSERT_TRUE(_obj.lookup(strs, &threshold));
    EXPECT_DOUBLE_EQ(8.0, threshold);

    strs[1] = "xx";
    ASSERT_FALSE(_obj.lookup(strs, &threshold));
    _obj.uninit();
}

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

