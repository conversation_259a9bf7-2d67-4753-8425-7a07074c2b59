// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: corr_cnt_dict_test.cpp
// @Last modified: 2016-04-28 11:36:04
// @Brief: 

#include <gtest/gtest.h>
#include <com_log.h>
#include "corr_cnt_dict.h"

namespace anti {
namespace themis {
namespace common_lib {

const std::string conf_path("./conf");
const std::string conf_file("corr_cn_dict_test.conf");

class CorrCntDictTestSuite : public ::testing::Test {
public:
    virtual void SetUp() {
        ASSERT_TRUE(_conf.load(conf_path.data(), conf_file.data()) == 0);
    }

    virtual void TearDown() {}
private:
    CorrCntDict _obj;
    comcfg::Configure _conf;
};

TEST_F(CorrCntDictTestSuite, init_by_conf_case) {
    for (uint32_t i = 0; i < _conf["invalid"].size(); ++i) {
        CDEBUG_LOG("invalid:%u", i);
        EXPECT_FALSE(_obj.init(_conf["invalid"][i]));
        _obj.uninit();
    }
    
    ASSERT_TRUE(_obj.init(_conf["valid"][0]));
    EXPECT_EQ(1U, _obj._cur_idx);
    EXPECT_EQ(11U, _obj._sets[_obj._cur_idx].size());
    EXPECT_EQ(4U, _obj._maps[_obj._cur_idx].size());
    _obj.uninit();
}

TEST_F(CorrCntDictTestSuite, corr_cn_case) {
    ASSERT_TRUE(_obj.init(_conf["valid"][0]));
    EXPECT_EQ(1U, _obj._cur_idx);
    EXPECT_EQ(11U, _obj._sets[_obj._cur_idx].size());
    EXPECT_EQ(4U, _obj._maps[_obj._cur_idx].size());

    char* frs[2] = {"baidu.com", "baidu.com"};
    char* cnts[2] = {"google", "baidu"};
    char* ex_cnts[2] = {"baidu", ""};
    CorrCntDict::CorrResult exp_ret[2] = {CorrCntDict::CORR_SUCC, CorrCntDict::NONEED_CORR};
    for (uint32_t i = 0; i < 2U; ++i) {
        std::string fix_cnt;    
        CorrCntDict::CorrResult ret = _obj.corr_cn(frs[i], cnts[i], &fix_cnt);
        EXPECT_EQ(exp_ret[i], ret);
        EXPECT_STREQ(ex_cnts[i], fix_cnt.data());
    }
}

}  // namespace common_lib
}  // namespace themis
}  // namespace anti


/* vim: set ts=4 sw=4: */

