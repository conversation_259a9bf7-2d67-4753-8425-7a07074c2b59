// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: multi_column_file_dict_test.cpp
// @Last modified: 2015-06-04 16:02:37
// @Brief: 

#include <gtest/gtest.h>
#include "multi_column_file_dict.h"
#include "com_log.h"

namespace anti {
namespace themis {
namespace common_lib {

class MultiColumnFileDictTestSuite : public ::testing::Test {
public:
    virtual void SetUp() {}
    virtual void TearDown() {}

private:
    MultiColumnFileDict _obj;
};

TEST_F(MultiColumnFileDictTestSuite, construction_case) {
    EXPECT_EQ(0U, _obj._cur_idx);
}

TEST_F(MultiColumnFileDictTestSuite, init_by_conf_case) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "mcf_test.conf"));
    for (uint32_t i = 0; i < conf["invalid"].size(); ++i) {
        CDEBUG_LOG("invalid:%u", i);
        EXPECT_FALSE(_obj.init(conf["invalid"][i]));
        _obj.uninit();
    }

    ASSERT_TRUE(_obj.init(conf["valid"][0]));
    EXPECT_EQ(1U, _obj._cur_idx);
    EXPECT_EQ(5U, _obj._sets[_obj._cur_idx].size());
    EXPECT_EQ(-1, _obj._column);
    
    ASSERT_TRUE(_obj.reload());
    EXPECT_EQ(1U, _obj._cur_idx);
    EXPECT_EQ(5U, _obj._sets[_obj._cur_idx].size());
    _obj.uninit();

    _obj._modify_time = 0;
    ASSERT_TRUE(_obj.init(conf["valid"][1]));
    EXPECT_EQ(1U, _obj._cur_idx);
    EXPECT_EQ(5U, _obj._sets[_obj._cur_idx].size());
    EXPECT_EQ(0, _obj._column);
    _obj.uninit();
}

TEST_F(MultiColumnFileDictTestSuite, _reload_by_no_file_case) {
    _obj._file_path = "xxxx";
    EXPECT_FALSE(_obj._reload());
}

TEST_F(MultiColumnFileDictTestSuite, lookup_by_different_input_case) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "mcf_test.conf"));
    ASSERT_TRUE(_obj.init(conf["valid"][0]));
    
    // in 
    std::vector<std::string> tmp[4] = {{"12"}, {"345"}, {"6789"}, {"0"}};
    for (uint32_t i = 0; i < 4; ++i) {
        ASSERT_TRUE(_obj.lookup(std::vector<std::string>(tmp[i])));
        ASSERT_TRUE(_obj.lookup(tmp[i]));
    }
    ASSERT_TRUE(_obj.lookup(std::vector<std::string>{"123", "45"}));
    ASSERT_TRUE(_obj.lookup("123_45"));

    // not in
    ASSERT_FALSE(_obj.lookup(std::vector<std::string>{"xxxxx"}));
    ASSERT_FALSE(_obj.lookup("xxxxx"));
    
    _obj._cur_idx = 2;
    ASSERT_FALSE(_obj.lookup(std::vector<std::string>{""}));
    ASSERT_FALSE(_obj.lookup(""));
    _obj.uninit();
}

TEST_F(MultiColumnFileDictTestSuite, lookup_by_column_is_zero) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "mcf_test.conf"));
    ASSERT_TRUE(_obj.init(conf["valid"][1]));
    
    // in 
    std::vector<std::string> tmp[4] = {{"12"}, {"345"}, {"6789"}, {"0"}};
    for (uint32_t i = 0; i < 4; ++i) {
        ASSERT_TRUE(_obj.lookup(std::vector<std::string>(tmp[i])));
        ASSERT_TRUE(_obj.lookup(tmp[i]));
    }

    // not in
    ASSERT_FALSE(_obj.lookup(std::vector<std::string>{"123", "45"}));
    ASSERT_FALSE(_obj.lookup("123_45"));
}
TEST_F(MultiColumnFileDictTestSuite, lookup_str_by_different_input_case) {
    comcfg::Configure conf;
    ASSERT_EQ(0, conf.load("./conf", "mcf_test.conf"));
    ASSERT_TRUE(_obj.init(conf["valid"][0]));
    
    // in: one term
    std::vector<std::string> tmp = {"12", "345", "6789", "0"};
    std::vector<std::string> outputs;
    for (uint32_t i = 0; i < 4; ++i) {
        // ASSERT_TRUE(_obj.lookup_str(std::vector<std::string>(tmp[i]), outputs));
        // ASSERT_EQ(tmp[i], outputs[0]);
        // outputs.clear();

        ASSERT_TRUE(_obj.lookup_str(tmp[i], outputs));
        ASSERT_EQ(tmp[i], outputs[0]);
        outputs.clear();
    }
    
    // in: multi term
    std::vector<std::string> input = {"123", "45"};
    ASSERT_TRUE(_obj.lookup_str(input, outputs));
    ASSERT_EQ(input.size(), outputs.size());
    for(int i = 0; i < input.size(); ++i) {
        ASSERT_EQ(input[i], outputs[i]);
    }
    outputs.clear();
    
    std::string input_str = "123_45";
    ASSERT_TRUE(_obj.lookup_str(input_str, outputs));
    ASSERT_EQ(input_str, outputs[0]);
    outputs.clear();    

    // not in
    ASSERT_TRUE(_obj.lookup_str(std::vector<std::string>{"xxxxx"}, outputs));
    ASSERT_TRUE(outputs.empty());
    outputs.clear();   

    ASSERT_TRUE(_obj.lookup_str("xxxxx", outputs));
    ASSERT_TRUE(outputs.empty());
    outputs.clear();   
    
    // index > SET_NUM
    _obj._cur_idx = 2;
    ASSERT_FALSE(_obj.lookup_str(std::vector<std::string>{""}, outputs));
    ASSERT_TRUE(outputs.empty());
    outputs.clear();   

    ASSERT_FALSE(_obj.lookup_str("", outputs));
    ASSERT_TRUE(outputs.empty());
    outputs.clear();   

    _obj.uninit();
}

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */