// Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
// @file ip6map_test.cpp
// <AUTHOR>
// @date 2018/11/30 11:55:44
// @brief

#include <ip6map.h>
#include <gtest/gtest.h>

namespace anti {
namespace themis {
namespace common_lib {

int main(int argc, char **argv)
{
    testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}

class TestIp6mapSuite : public ::testing::Test {
public:
    virtual void SetUp() {
    }
    virtual void TearDown() {
    }
private:
    ip6map_t _ip6map;
};

TEST_F(TestIp6mapSuite, test_set_data_file_default_value) {
    EXPECT_NE(0, _ip6map.set_data_file("./data/ipmap.data"));
}

TEST_F(TestIp6mapSuite, test_get_pid_cid) {
    EXPECT_EQ(0, _ip6map.set_data_file("./data/ip6map.data"));
    int pid = 0;
    int cid = 0;
    std::string ip = "240E:84::";
    EXPECT_EQ(true, _ip6map.get(ip.c_str(), pid, cid));
    EXPECT_EQ(15, pid);
    EXPECT_EQ(335, cid);

    pid = -1;
    cid = -1;
    ip = "240E:84:1300:1FFF::";
    EXPECT_EQ(true, _ip6map.get(ip.c_str(), pid, cid));
    EXPECT_EQ(15, pid);
    EXPECT_EQ(335, cid);

    pid = -1;
    cid = -1;
    ip = "240E:84:31FF:1FFF::";
    EXPECT_EQ(true, _ip6map.get(ip.c_str(), pid, cid));
    EXPECT_EQ(16, pid);
    EXPECT_EQ(101, cid);

    pid = -1;
    cid = -1;
    ip = "2001:6::";
    EXPECT_EQ(false, _ip6map.get(ip.c_str(), pid, cid));
    EXPECT_EQ(-1, pid);
    EXPECT_EQ(-1, cid);

    pid = -1;
    cid = -1;
    ip = "2001:6:::";
    EXPECT_EQ(false, _ip6map.get(ip, pid, cid));
    EXPECT_EQ(-1, pid);
    EXPECT_EQ(-1, cid);

    pid = -1;
    cid = -1;
    ip = "**********";
    EXPECT_EQ(false, _ip6map.get(ip.c_str(), pid, cid));
    EXPECT_EQ(-1, pid);
    EXPECT_EQ(-1, cid);

    pid = -1;
    cid = -1;
    ip = "240E:84:2100::";
    EXPECT_EQ(false, _ip6map.get(ip.c_str(), pid, cid));
    EXPECT_EQ(-1, pid);
    EXPECT_EQ(-1, cid);
}

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */
