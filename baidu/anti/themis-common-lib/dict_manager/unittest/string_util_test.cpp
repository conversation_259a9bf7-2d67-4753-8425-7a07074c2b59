// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: string_util_test.cpp
// @Last modified: 2015-06-04 15:21:00
// @Brief: 

#include <gtest/gtest.h>
#include "string_util.h"

namespace anti {
namespace themis {
namespace common_lib {

TEST(StringUtilTestSuite, join_case) {
    std::vector<std::string> strs;
    EXPECT_STREQ("", StringUtil::join(strs, "_").data());

    strs.push_back("123");
    EXPECT_STREQ("123", StringUtil::join(strs, "_").data());

    strs.push_back("78");
    EXPECT_STREQ("123_78", StringUtil::join(strs, "_").data());
}

TEST(StringUtilTestSuite, get_utf8string_frequency_case) {
    std::unordered_map<std::string, int32_t> frequency_map;
    std::string line = "朝阳区北苑路228号，北郊++";
    StringUtil::get_utf8string_frequency(line, frequency_map);
    EXPECT_EQ(frequency_map.size(), 12);
    // 朝
    EXPECT_TRUE(frequency_map.find("e69c9d") != frequency_map.end());
    EXPECT_EQ(frequency_map["e69c9d"], 1);
    // 阳
    EXPECT_TRUE(frequency_map.find("e998b3") != frequency_map.end());
    EXPECT_EQ(frequency_map["e998b3"], 1);
    // 区
    EXPECT_TRUE(frequency_map.find("e58cba") != frequency_map.end());
    EXPECT_EQ(frequency_map["e58cba"], 1);
    // 北
    EXPECT_TRUE(frequency_map.find("e58c97") != frequency_map.end());
    EXPECT_EQ(frequency_map["e58c97"], 2);
    // 苑
    EXPECT_TRUE(frequency_map.find("e88b91") != frequency_map.end());
    EXPECT_EQ(frequency_map["e88b91"], 1);
    //路
    EXPECT_TRUE(frequency_map.find("e8b7af") != frequency_map.end());
    EXPECT_EQ(frequency_map["e8b7af"], 1);
    // 2
    EXPECT_TRUE(frequency_map.find("32") != frequency_map.end());
    EXPECT_EQ(frequency_map["32"], 2);
    // 8
    EXPECT_TRUE(frequency_map.find("38") != frequency_map.end());
    EXPECT_EQ(frequency_map["38"], 1);
    // 号
    EXPECT_TRUE(frequency_map.find("e58fb7") != frequency_map.end());
    EXPECT_EQ(frequency_map["e58fb7"], 1);
    // ，
    EXPECT_TRUE(frequency_map.find("efbc8c") != frequency_map.end());
    EXPECT_EQ(frequency_map["efbc8c"], 1);
    // 郊
    EXPECT_TRUE(frequency_map.find("e9838a") != frequency_map.end());
    EXPECT_EQ(frequency_map["e9838a"], 1);
    //+
    EXPECT_TRUE(frequency_map.find("2b") != frequency_map.end());
    EXPECT_EQ(frequency_map["2b"], 2);
}

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

