// Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
// @Author: exiangnan(<EMAIL>)
// 
// @File: pcre_dict.h
// @Last modified: 2022-08-16 09:56:06
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_PCRE_DICT_H
#define APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_PCRE_DICT_H

#include <string>
#include <pcre.h>
#include <unordered_set>
#include <unordered_map>
#include "ul_dictmatch.h"
#include "file_dict_interface.h"
#include <fstream>
#include <vector>

namespace anti {
namespace themis {
namespace common_lib {

/***
* @description: pcreDict是基于Perl开源库实现正则匹配功能，相比于boost::regex性能优异
* @description: init函数实现pcredict词典的编译 
* @description: lookup返回是否正则匹配到
*/
class PcreDict : public SearchDictBase {
public:
    PcreDict();
    virtual ~PcreDict() {
        uninit();
    }

    virtual bool init(const comcfg::ConfigUnit& conf);
    virtual void uninit();
    
    virtual const std::string& file_path() const {
        return _file_path;
    }

    bool lookup(const char* buf, int len) const;

    bool lookup(const std::string& buf) const {
        return lookup(buf.c_str(), buf.size());
    }
    bool lookup_str(const std::string& input_str, std::vector<std::string>& outputs) const;        
private:
    virtual bool _reload();
    static const uint32_t SET_NUM = 2;

    uint32_t _cur_idx;
    std::string _file_path;
    std::vector<std::pair<pcre*, std::string>> _vecs[SET_NUM];
};

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

#endif // APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_PCRE_DICT_H

/* vim: set ts=4 sw=4: */

