// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: file_dict_factory.h
// @Last modified: 2015-05-19 16:28:26
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_FILE_DICT_FACTORY_H
#define APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_FILE_DICT_FACTORY_H

#include <string>
#include "file_dict_interface.h"

namespace anti {
namespace themis {
namespace common_lib {

class FileDictFactory {
public:
    static FileDictInterface* create(const std::string& type);
};

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_FILE_DICT_FACTORY_H

/* vim: set ts=4 sw=4: */

