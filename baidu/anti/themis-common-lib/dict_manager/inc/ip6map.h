// Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
// <AUTHOR> (chen<PERSON><EMAIL>)
// @Date: 2018-11-29
// @Brief: ip6map

#ifndef BAIDU_ANTI_THEMIS_COMMON_LIB_IP6MAP_H
#define BAIDU_ANTI_THEMIS_COMMON_LIB_IP6MAP_H

#include <boost/lexical_cast.hpp>
#include <string>
#include <vector>

#include <com_log.h>
#include "ipv6library.h"
#include "ipv6_int.h"

namespace anti {
namespace themis {
namespace common_lib {

bool string_to_int(const char* str, uint32_t* dec);

class ip6map_t {
public:
    ip6map_t();
    ~ip6map_t();

    int32_t set_data_file(const char* filename,
            const char sep = '|', const int field_num = 0) {
        std::vector<int32_t> cols_selected;
        cols_selected.push_back(0);
        cols_selected.push_back(1);
        return set_data_file(filename, cols_selected, sep);
    }

    int32_t set_data_file(const char* filename,
            const std::vector<int32_t>& cols_selected, const char sep);

    bool get(const char* ip6str, int32_t& id1, int32_t& id2) const;
    bool get(const std::string& ip6str, int32_t& id1, int32_t& id2) const {
        return get(ip6str.c_str(), id1, id2);
    }

private:
    std::shared_ptr<IPv6Library> _ipv6lib;
}; // ip6map_t

} // namespace common_lib
} // namespace themis
} // namespace anti

#endif // BAIDU_ANTI_THEMIS_COMMON_LIB_IP6MAP_H
