// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiax<PERSON>(<EMAIL>)
// 
// @File: file_dict_manager.h
// @Last modified: 2015-05-26 17:20:22
// @Brief: dict manager is responsible for init/uninit/reload dict in conf 
//         it's no need to restart when add/delete dict.
// @Usage:
//     1. FileDictManagerSingleton::instance().init()
//     2. shared_ptr<const FileDictInterface> xxx 
//             = FileDictManagerSingleton::instance().get_file_dict(file_key)
//     3. if !xxxx (get file dict failed).
//     4. dynamic_pointer_cast xxx from FileDictInterface
//     5. xxxx.lookup(yyyy, result)
//     6. if result (in file dict)
//     7. else not in file dict

#ifndef APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_FILE_DICT_MANAGER_H
#define APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_FILE_DICT_MANAGER_H

#include <string>
#include <boost/noncopyable.hpp>
#include <boost/thread/detail/singleton.hpp>
#include <memory>
#include <unordered_map>
#include "file_dict_interface.h"

namespace anti {
namespace themis {
namespace common_lib {

class FileDictManager : public boost::noncopyable {
public:
    FileDictManager() : _modify_time(0U) {}
    ~FileDictManager();

    bool init(const std::string& conf_path, const std::string& conf_file);
    bool reload();
    // for multi dicts
    bool multi_dicts_init(const std::vector<std::string>& conf_paths, 
                            const std::vector<std::string>& conf_files);
    bool multi_dicts_reload();

    void uninit();

    typedef std::shared_ptr<const FileDictInterface> ConstDictPtr;
    ConstDictPtr get_file_dict(const std::string& file_key) const;

private:
    bool _reload();
    bool _multi_dicts_reload();
    
    time_t _modify_time;
    std::string _conf_path;
    std::string _conf_file;

    // for multi dicts
    std::vector<std::string> _dicts_conf_path_vec;
    std::vector<std::string> _dicts_conf_file_vec;
    std::vector<time_t> _multi_dicts_modify_time_vec;

    typedef std::shared_ptr<FileDictInterface> DictPtr;
    std::unordered_map<std::string, DictPtr> _dicts;
    std::unordered_map<std::string, DictPtr> _dicts_to_del;
};

typedef ::boost::detail::thread::singleton<FileDictManager> FileDictManagerSingleton;

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_FILE_DICT_MANAGER_H

/* vim: set ts=4 sw=4: */

