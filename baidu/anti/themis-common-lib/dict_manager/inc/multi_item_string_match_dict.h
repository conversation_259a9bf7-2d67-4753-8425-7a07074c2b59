// Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
// @Author: <PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
// 
// @File: multi_term_string_match_dict.h
// @Last modified: 2022-11-18 15:44:06
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_MULTI_ITEM_STRING_MATCH_DICT_H
#define APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_MULTI_ITEM_STRING_MATCH_DICT_H

#include <string>
#include <unordered_set>
#include <unordered_map>
#include "file_dict_interface.h"

namespace anti {
namespace themis {
namespace common_lib {

class MultiItemStringMatchDict : public SearchDictBase {
public:
    typedef std::unordered_set<std::string> ItemSet;
    typedef std::unordered_map<std::string, ItemSet> ItemSetMap;
    typedef std::unordered_map<std::string, ItemSetMap> DictInfoMap;

    MultiItemStringMatchDict() : _cur_idx(0U) {
    }
    virtual ~MultiItemStringMatchDict() {
        uninit();
    }

    virtual bool init(const comcfg::ConfigUnit& conf);
    virtual void uninit();

    virtual const std::string& file_path() const {
        return _file_path;
    }

    bool lookup(const std::string& buf) const;
    bool lookup_str(const std::string& input_str, std::vector<std::string>& outputs) const;
private:
    virtual bool _reload();
    static const uint32_t NUM = 2U;

    uint32_t _cur_idx;
    std::string _file_path;
    DictInfoMap _sets[NUM];
    const static std::string SEP;
};

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

#endif // APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_MULTI_ITEM_STRING_MATCH_DICT_H