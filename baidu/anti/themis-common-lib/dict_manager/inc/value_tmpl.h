// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: value_tmpl.h
// @Last modified: 2017-03-23 14:16:35
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_VALUE_TMPL_H
#define APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_VALUE_TMPL_H

#include "string_util.h"
#include <unordered_map>
#include <sstream>

namespace anti {
namespace themis {
namespace common_lib {

struct TradeInfo {
    uint32_t tradeid1;
    uint32_t tradeid2;
    uint32_t tradeid3;
public:
    TradeInfo() : tradeid1(0U), tradeid2(0U), tradeid3(0U) {}
};

struct CntInfo {
    std::string flow_group;
    std::string partner_tag;
    uint32_t cntid;
    std::string cooper_mode;
    std::vector<std::string> flow_groups;
    std::string map_tn;
    std::string channel_id;
};

template<typename T>
class ValueCreator;

template<typename T>
class ValueCreator<std::list<std::vector<T>>> {
public:
    ValueCreator(uint32_t cols) : _val_cols(cols) {};
    ~ValueCreator() {}
    bool create(const std::string& line, uint32_t start, std::list<std::vector<T>>* value);
    
private:
    uint32_t _val_cols;
};

template<typename T>
bool ValueCreator<std::list<std::vector<T>>>::
        create(const std::string& line, uint32_t start, std::list<std::vector<T>>* values) {
    if (values == NULL) {
        CFATAL_LOG("input values is NULL");
        return false;
    }
    values->push_back(std::vector<T>());
    uint32_t idx = 0;
    uint32_t each_start = start;
    for (uint32_t i = start; i <= line.size() && idx < _val_cols; ++i) {
        if (line[i] != '\t' && line[i] != '\0') {
            continue;
        }
        try {
            values->back().push_back(boost::lexical_cast<T>(
                    std::string(line, each_start, i - each_start)));
            ++idx;
        } catch (const boost::bad_lexical_cast& e) {
            CWARNING_LOG("catch exception(%s)", e.what());
            return false;
        }
        each_start = i + 1;
    }
    if (idx < _val_cols) {
        CWARNING_LOG("line(%s) invalid, value cols(%d) less than conf(%d)",
                line.data(), idx, _val_cols);
        return false;
    }
    return true;
}

template<typename T>
class ValueCreator<std::list<T>> {
public:
    ValueCreator(uint32_t cols) : _val_cols(cols) {};

    ~ValueCreator() {}

    bool create(const std::string& line, uint32_t start, std::list<T>* values) {
    if (values == NULL) {
        CFATAL_LOG("input values is NULL");
        return false;
    }
    uint32_t idx = 0;
    uint32_t each_start = start;
    for (uint32_t i = start; i <= line.size() && idx < _val_cols; ++i) {
        if (line[i] != '\t' && line[i] != '\0') {
            continue;
        }
        try {
            values->push_back(
                    boost::lexical_cast<T>(
                    std::string(line, each_start, i - each_start)));
            ++idx;
        } catch (const boost::bad_lexical_cast& e) {
            CWARNING_LOG("catch exception(%s)", e.what());
            return false;
        }
        each_start = i + 1;
    }
    if (_val_cols != std::numeric_limits<uint32_t>::max()
            && idx < _val_cols) {
        CWARNING_LOG("line(%s) invalid, value cols(%u) less than conf(%u)",
                line.data(), idx, _val_cols);
        return false;
    }
    return true;
}
private:
    uint32_t _val_cols;
};

template<>
class ValueCreator<TradeInfo> {
public: 
    ValueCreator(uint32_t cols) : _val_cols(cols) {};    

    ~ValueCreator() {}

    bool create(const std::string& line, uint32_t start, TradeInfo* tradeinfo) {
        if (tradeinfo == NULL) {
            CFATAL_LOG("input tradeinfo ptr is NULL");
            return false;
        }
        const char* str_start = line.data() + start;
        char* endptr = NULL;
        errno = 0;
        tradeinfo->tradeid1 = static_cast<uint32_t>(strtoul(str_start, &endptr, 10));
        if (errno != 0 || endptr == NULL || endptr == str_start || *endptr == '\0') {
            CWARNING_LOG("get tradeid1 fail, line(%s)", line.data());
            return false;
        }
        str_start = endptr + 1;
        tradeinfo->tradeid2 = static_cast<uint32_t>(strtoul(str_start, &endptr, 10));
        if (errno != 0 || endptr == NULL || endptr == str_start || *endptr == '\0') {
            CWARNING_LOG("get tradeid2 fail, line(%s)", line.data());
            return false;
        }
        str_start = endptr + 1;
        tradeinfo->tradeid3 = static_cast<uint32_t>(strtoul(str_start, &endptr, 10));
        if (errno != 0 || endptr == NULL || endptr == str_start) {
            CWARNING_LOG("get tradeid3 fail, line(%s)", line.data());
            return false;
        }
        return true;
    }

private:
    uint32_t _val_cols;
};


template<>
class ValueCreator<CntInfo> {
public: 
    ValueCreator(uint32_t cols) : _val_cols(cols) {};    

    ~ValueCreator() {}

    bool create(const std::string& line, uint32_t start, CntInfo* cntinfo) {
        if (cntinfo == NULL) {
            CFATAL_LOG("input cntid ptr is NULL");
            return false;
        }
        const char* SEP = "\t";
        uint32_t col = 0U;
        size_t str_start = start;
        size_t str_end = str_start;
        while (col < _val_cols - 1) {
            str_end = line.find(SEP, str_start); 
            if (str_end == std::string::npos) {
                CWARNING_LOG("invalid line(%s), value cols num shuold be(%d)", line.data(), _val_cols);
                return false; 
            }

            if (!_extract_col(line, str_start, str_end - str_start, col, cntinfo)) {
                CWARNING_LOG("extract col(%d) fail, line(%s)", col, line.data());
                return false;
            }
            str_start = str_end + 1;
            ++col;
        }
        // handle last col in line
        str_end = line.find(SEP, str_start);
        if (str_end == std::string::npos) {
            str_end = line.length();
        }
        return _extract_col(line, str_start, str_end - str_start, col, cntinfo);
}

private:
    bool _extract_col(
            const std::string& line, 
            size_t str_start, 
            size_t length, 
            uint32_t col, 
            CntInfo* cntinfo) {
        switch (col) {
        case 0: {
            cntinfo->flow_group.assign(line, str_start, length);
            break;
        }
        case 1: {
            cntinfo->partner_tag.assign(line, str_start, length);
            break;
        }
        case 2: {
            std::string tmp_str(line, str_start, length);
            try {
                cntinfo->cntid = boost::lexical_cast<uint32_t>(tmp_str);
            } catch (const boost::bad_lexical_cast &e) {
                CWARNING_LOG("bad_boost::lexical_cast : %s", e.what());
                return false;
            }
            break;
        }
        case 3: {
            cntinfo->channel_id.assign(line, str_start, length);
        }
        case 7: {
            cntinfo->cooper_mode.assign(line, str_start, length);
            break;
        }
        case 8: {
            const char* FLOW_GROUP_SEP = ",";
            // do not use boost::split here because of its low efficiency
            StringUtil::str_split(line, str_start, str_start + length, FLOW_GROUP_SEP, &(cntinfo->flow_groups));
            break;
        }
        case 9: {
            cntinfo->map_tn.assign(line, str_start, length);
            break;
        }
        default:
            break;
        }
        return true;
    }
    uint32_t _val_cols;
};

template<>
class ValueCreator<std::vector<std::unordered_map<std::string, int32_t>>> {
public:
    ValueCreator(uint32_t cols) : _val_cols(cols) {};

    ~ValueCreator() {}

    bool create(const std::string& line, uint32_t start, 
                std::vector<std::unordered_map<std::string, int32_t>>* values) {
    if (values == NULL) {
        CFATAL_LOG("input values is NULL");
        return false;
    }
    uint32_t idx = 0;
    uint32_t each_start = start;
    std::unordered_map<std::string, int32_t> tmp_map;
    for (uint32_t i = start; i <= line.size() && idx < _val_cols; ++i) {
        if (line[i] != '\t' && line[i] != '\0') {
            continue;
        }
        try {
            StringUtil::get_utf8string_frequency(std::string(line, each_start, i - each_start), tmp_map);
            values->emplace_back(tmp_map);
            tmp_map.clear();
            ++idx;
        } catch (const boost::bad_lexical_cast& e) {
            CWARNING_LOG("catch exception(%s)", e.what());
            return false;
        }
        each_start = i + 1;
    }
    if (_val_cols != std::numeric_limits<uint32_t>::max()
            && idx < _val_cols) {
        CWARNING_LOG("line(%s) invalid, value cols(%u) less than conf(%u)",
                line.data(), idx, _val_cols);
        return false;
    }
    return true;
}
private:
    uint32_t _val_cols;
};

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

#endif // APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_VALUE_TMPL_H
/* vim: set ts=4 sw=4: */

