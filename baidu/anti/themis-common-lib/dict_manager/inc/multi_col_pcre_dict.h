// Copyright (c) 2024 Baidu.com, Inc. All Rights Reserved
// @Author: liushuai31(<EMAIL>)
// 
// @File: multi_col_pcre_dict.h
// @Last modified: 2024-02-20 09:56:06
// @Brief: 多列正则匹配词典，可指定任意列为正则表达式

#ifndef APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_PCRE_DICT_MULTI_COL_H
#define APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_PCRE_DICT_MULTI_COL_H

#include <string>
#include <pcre.h>
#include <unordered_set>
#include <unordered_map>
#include "ul_dictmatch.h"
#include "file_dict_interface.h"
#include <boost/algorithm/string.hpp>
#include <fstream>
#include <vector>

namespace anti {
namespace themis {
namespace common_lib {

/***
* @description: pcreDict是基于Perl开源库实现正则匹配功能，相比于boost::regex性能优异
* @description: init函数实现pcredict词典的编译 
* @description: lookup返回是否正则匹配到
*/
class MultiColPcreDict : public SearchDictBase {
public:
    MultiColPcreDict();
    virtual ~MultiColPcreDict() {
        uninit();
    }

    virtual bool init(const comcfg::ConfigUnit& conf);
    virtual void uninit();
    
    virtual const std::string& file_path() const {
        return _file_path;
    }

    bool lookup(const char* buf, int len) const;

    bool lookup(const std::string& buf) const {
        return lookup(buf.c_str(), buf.size());
    }
    bool lookup_str(const std::string& input_str, std::vector<std::string>& outputs) const;        
private:
    virtual bool _reload();
    static const uint32_t VEC_NUM = 2;
    uint32_t _match_cols;  // the position of regex pattern, at least 1
    uint32_t _cur_idx;
    std::string _file_path;
    std::vector<std::pair<pcre*, std::string>> _vecs[VEC_NUM];
};

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

#endif // APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_PCRE_DICT_H

/* vim: set ts=4 sw=4: */

