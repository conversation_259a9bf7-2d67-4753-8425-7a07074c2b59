// Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
// @brief

#ifndef APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_IPMAP_DICT_H
#define APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_IPMAP_DICT_H

#include <boost/lexical_cast.hpp>
#include <string>
#include <vector>
#include "file_dict_interface.h"
#include "ipmap.h"
#include "ip6map.h"

namespace anti {
namespace themis {
namespace common_lib {

template<typename IpmapType>
class IpmapDictTpl : public SearchDictBase {
public:
    IpmapDictTpl();
    ~IpmapDictTpl() {}

    virtual bool init(const comcfg::ConfigUnit& conf);
    virtual void uninit();

    virtual const std::string& file_path() const {
        return _file_path;
    }

    // @brief: lookup by strings or by sign
    // @retval: true(in gray) false(not in gray)
    virtual bool lookup(const std::string& ip) const;
    bool lookup(const std::string& ip, int32_t* pid, int32_t* cid) const;
    bool lookup(const int64_t ip) const;
    bool lookup(const int64_t ip, int32_t* pid, int32_t* cid) const;
    bool lookup_str(const std::string& input_str, std::vector<std::string>& outputs) const { 
        return true; 
    }

private:
    virtual bool _reload();

    static const uint32_t MAP_NUM = 2U;

    uint32_t _cur_idx;
    std::string _file_path;
    std::string _field_sep;
    int _field_num;
    //std::unordered_map<uint64_t, double> _maps[MAP_NUM];
    IpmapType* _maps[MAP_NUM];
};

typedef IpmapDictTpl<ipmap_t> IpmapDict;
typedef IpmapDictTpl<ip6map_t> Ip6mapDict;

} // namespace common_lib
} // namespace themis
} // namespace anti

#include "ipmap_dict.hpp"

#endif  // APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_IPMAP_DICT_H
