// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// @Author: lijie(<EMAIL>)
// 
// @File: array_file_dict.cpp

#include "array_file_dict.h"
#include <fstream>
#include <boost/algorithm/string.hpp>
#include <boost/lexical_cast.hpp>
#include <com_log.h>
#include <sign_util.h>
#include "string_util.h"

using anti::baselib::SignUtil;

namespace anti {
namespace themis {
namespace common_lib {

template<typename Number>
bool ArrayFileDict<Number>::init(const comcfg::ConfigUnit& conf) {
    try {
        _file_path = conf["file_path"].to_cstr();
        if (conf["row"].selfType() != comcfg::CONFIG_ERROR_TYPE) {
            _row = conf["row"].to_uint32();
        }
        _col = conf["col"].to_uint32();
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std Exception : %s", e.what());
        return false;
    }

    if (!reload()) {
        CWARNING_LOG("call reload fail, file(%s)", _file_path.c_str());
        return false;
    }
    return true;
}

template<typename Number>
void ArrayFileDict<Number>::uninit() {
    for (uint32_t i = 0; i < VEC_NUM; ++i) {
        _vecs[i].clear();
    }
    _cur_idx = 0U;
}

template<typename Number>
bool ArrayFileDict<Number>::_reload() {
    uint32_t idle = (_cur_idx + 1U) % VEC_NUM;
    _vecs[idle].clear();
    std::ifstream ifs(_file_path.c_str(), std::ifstream::in);
    if (ifs.fail()) {
        CWARNING_LOG("open file(%s) fail", _file_path.c_str());
        return false;
    }
    std::string line;
    while (!getline(ifs, line).eof()) {
        std::vector<std::string> fields;
        std::vector<Number> values;
        boost::split(fields, line, boost::is_any_of(" \t"), boost::token_compress_on);
        if (fields.size() != _col) {
            CWARNING_LOG("invalid line(%s), col num must be %u", line.data(), _col);
            return false;
        }
        try {
            for (size_t i = 0; i < fields.size(); ++i) {
                values.push_back(boost::lexical_cast<Number>(fields[i]));
            }
        } catch (const boost::bad_lexical_cast& e) {
            CWARNING_LOG("invalid line(%s), bad_lexical_cast(%s)", line.data(), e.what());
            return false;
        }
        _vecs[idle].push_back(values);
    }
    if (_row != 0 && _vecs[idle].size() != _row) {
        CWARNING_LOG("load failed, require row[%u], actually load row[%u]", 
                _row, _vecs[idle].size());
        return false;
    }
    _cur_idx = idle;
    return true;
}

template<typename Number>
bool ArrayFileDict<Number>::lookup(
    const uint32_t index, const std::vector<Number>** array) const {
    if (array == NULL || _cur_idx >= VEC_NUM 
        || index <= 0U || index > _vecs[_cur_idx].size()) {
        CFATAL_LOG("input node == NULL or _cur_idx(%u) >= VEC_NUM(%u) or index(%u) <= 0 or overrange", 
                _cur_idx, VEC_NUM, index);
        return false;
    }
    *array = &(_vecs[_cur_idx][index - 1]);
    return true;
}

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

/* vim: set ts=4 sw=4: */

