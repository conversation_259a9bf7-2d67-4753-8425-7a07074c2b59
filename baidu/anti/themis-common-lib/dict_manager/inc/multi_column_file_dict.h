// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: multi_column_file_dict.h
// @Last modified: 2015-06-04 15:04:17
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_MULTI_COLUMN_FILE_DICT_H
#define APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_MULTI_COLUMN_FILE_DICT_H

#include <string>
#include <vector>
#include <unordered_set>
#include "file_dict_interface.h"

namespace anti {
namespace themis {
namespace common_lib {

class MultiColumnFileDict : public SearchDictBase {
public:
    MultiColumnFileDict() : _cur_idx(0U), _column(-1) {}
    virtual ~MultiColumnFileDict() {
        uninit();
    }

    virtual bool init(const comcfg::ConfigUnit& conf);
    virtual void uninit();
    
    virtual const std::string& file_path() const {
        return _file_path;
    }
    // @brief: lookup by string, search_key is a string generated 
    //         by joining multiple strings with '_'
    virtual bool lookup(const std::string& search_key) const;
    
    // @brief: lookup by strings or by sign
    bool lookup(const std::vector<std::string>& values) const;
    bool lookup(uint64_t sign) const;
    bool lookup_str(const std::string& input_str, std::vector<std::string>& outputs) const;
    bool lookup_str(const std::vector<std::string>& values, std::vector<std::string>& outputs) const;
private:
    virtual bool _reload();
    static const uint32_t SET_NUM = 2U;

    uint32_t _cur_idx;
    int32_t _column;
    std::string _file_path;
    std::unordered_set<uint64_t> _sets[SET_NUM];
};

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_MULTI_COLUMN_FILE_DICT_H

/* vim: set ts=4 sw=4: */

