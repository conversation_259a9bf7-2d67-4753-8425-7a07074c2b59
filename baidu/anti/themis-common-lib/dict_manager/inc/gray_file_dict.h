// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: gray_file_dict.h
// @Last modified: 2015-06-04 14:42:29
// @Brief: the last column means thresholds for different views, 
//         so other columns are views(use tab or space as separators)

#ifndef APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_GRAY_FILE_DICT_H
#define APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_GRAY_FILE_DICT_H

#include <string>
#include <vector>
#include <unordered_map>
#include "file_dict_interface.h"

namespace anti {
namespace themis {
namespace common_lib {

class GrayFileDict : public FileDictBase {
public:
    GrayFileDict() : _cur_idx(0U), _view_num(0U) {}
    virtual ~GrayFileDict() {
        uninit();
    }

    virtual bool init(const comcfg::ConfigUnit& conf);
    virtual void uninit();
    
    virtual const std::string& file_path() const {
        return _file_path;
    }
    
    // @brief: lookup by strings or by sign
    // @retval: true(in gray) false(not in gray)
    bool lookup(
            const std::vector<std::string>& values, 
            double* threshold) const;
    bool lookup(uint64_t sign, double* threshold) const;
    
private:
    virtual bool _reload();

    static const uint32_t MAP_NUM = 2U;

    uint32_t _cur_idx;
    uint32_t _view_num;
    std::string _file_path;
    std::unordered_map<uint64_t, double> _maps[MAP_NUM];
};

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_GRAY_FILE_DICT_H

/* vim: set ts=4 sw=4: */

