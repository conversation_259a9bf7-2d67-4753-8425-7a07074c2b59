// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: string_match_dict.h
// @Last modified: 2016-05-09 09:56:06
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_STRING_MATCH_DICT_H
#define APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_STRING_MATCH_DICT_H

#include <string> 
#include "ul_dictmatch.h"
#include "file_dict_interface.h"

namespace anti {
namespace themis {
namespace common_lib {

class StringMatchDict : public SearchDictBase {
public:
    StringMatchDict();
    virtual ~StringMatchDict() {
        uninit();
    }

    virtual bool init(const comcfg::ConfigUnit& conf);
    virtual void uninit();
    
    virtual const std::string& file_path() const {
        return _file_path;
    }

    bool lookup(const char* buf, int len) const;

    bool lookup(const std::string& buf) const {
        return lookup(buf.data(), buf.size());
    }
    /***
    * @description: input : 待匹配的字符串 
    * @description: output : 会将查找到的string存入vector返回
    */
    bool lookup_str(const std::string& input_str, std::vector<std::string>& outputs) const;
    
private:
    virtual bool _reload();
    static const uint32_t DICT_NUM = 2;

    uint32_t _cur_idx;
    std::string _file_path;
    dm_dict_t* _dicts[DICT_NUM];
};

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

#endif // APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_STRING_MATCH_DICT_H

/* vim: set ts=4 sw=4: */

