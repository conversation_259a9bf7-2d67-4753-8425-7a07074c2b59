// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: string_util.h
// @Last modified: 2017-03-21 17:48:33
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_COMMON_LIB_DICT_MANAGER_DEV_STRING_UTIL_H
#define APP_ECOM_ANTI_THEMIS_COMMON_LIB_DICT_MANAGER_DEV_STRING_UTIL_H

#include <string>
#include <vector>
#include <unordered_map>
#include <com_log.h>

namespace anti {
namespace themis {
namespace common_lib {

class StringUtil {
public:
    static std::string join(
            const std::vector<std::string>& strs,
            const std::string& sep);
    
    // [start, end)
    static std::string join(
            const std::vector<std::string>& strs,
            const std::string& sep,
            uint32_t start,
            uint32_t end);
    static void str_split(
            const std::string& line, 
            size_t start,
            size_t end,
            const char* sep,
            std::vector<std::string>* results);
    /***
     * @description: 将UTF8编码的字符串中每个字符, 转换成对应的16进制UTF8编码值, 和其在字符串中出现的次数。作为key,value存在frequency_map中
     * @param {in: std::string& temp 输入字符串 }
     * @param {in: std::unordered_map<std::string,int32_t>& frequency_map}
     */
    static void get_utf8string_frequency(
            const std::string& temp,
            std::unordered_map<std::string,int32_t>& frequency_map);
};

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_COMMON_LIB_DICT_MANAGER_DEV_STRING_UTIL_H

/* vim: set ts=4 sw=4: */

