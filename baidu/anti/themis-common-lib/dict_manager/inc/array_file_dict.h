// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// @Author: lijie(<EMAIL>)
// 
// @File: array_file_dict.h

#ifndef APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_ARRAY_FILE_DICT_H
#define APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_ARRAY_FILE_DICT_H

#include <string>
#include <vector>
#include <unordered_map>
#include "file_dict_interface.h"

namespace anti {
namespace themis {
namespace common_lib {

template<typename Number>
class ArrayFileDict : public FileDictBase {
public:
    ArrayFileDict() : _cur_idx(0U), _row(0U), _col(0U) {}
    virtual ~ArrayFileDict() {
        uninit();
    }

    virtual bool init(const comcfg::ConfigUnit& conf);
    virtual void uninit();
    
    virtual const std::string& file_path() const {
        return _file_path;
    }
    
    // @brief: lookup by idx
    bool lookup(
            const uint32_t idx, 
            const std::vector<Number>** array) const;
    
private:
    virtual bool _reload();

    static const uint32_t VEC_NUM = 2U;

    uint32_t _cur_idx;
    uint32_t _row;
    uint32_t _col;
    std::string _file_path;
    std::vector<std::vector<Number> > _vecs[VEC_NUM];
};

typedef ArrayFileDict<double> AcpFileDict;

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

#include "array_file_dict.hpp"

#endif  // APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_ARRAY_FILE_DICT_H

/* vim: set ts=4 sw=4: */

