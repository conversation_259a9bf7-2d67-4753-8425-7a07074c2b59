// Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved
// @Author: liushuai31(<EMAIL>)
// 
// @File: dqa_multi_term_string_match_dict.h
// @Last modified: 2023-09-19 09:56:06
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_DQA_MULTI_TERM_STRING_MATCH_DICT_H
#define APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_DQA_MULTI_TERM_STRING_MATCH_DICT_H

#include <string> 
#include <set>
#include <map>
#include "ul_dictmatch.h"
#include "file_dict_interface.h"
#include "com_log.h"

namespace anti {
namespace themis {
namespace common_lib {

const int SUB_TERM_MAX_NUM = 3500000;
const uint32_t DICT_NUM = 2;
class QueryMultiTermDicts {
public:
    QueryMultiTermDicts(){
        _all_term_dict = dm_dict_create(SUB_TERM_MAX_NUM, DM_CHARSET_GB18030);
        _term_to_rule_map.clear();
        _rule_to_count_map.clear();
        _rule_to_srcid_map.clear();
        _idx_to_term_map.clear();
    }
    virtual ~QueryMultiTermDicts(){
        uninit();
    }
    virtual void uninit() {
        _term_to_rule_map.clear();
        _rule_to_count_map.clear();
        _rule_to_srcid_map.clear();
        _idx_to_term_map.clear();
    }

    std::map<std::string, std::vector<int>>& get_term_to_rule_map() {
        return this->_term_to_rule_map;
    }
    
    std::map<int, int>& get_rule_to_count_map() {
        return this->_rule_to_count_map;
    } 
    std::map<int, std::vector<std::string>>& get_idx_to_term_map() {
        return this->_idx_to_term_map;
    } 
    dm_dict_t* get_all_term_dict() {
        return this->_all_term_dict;
    } 
private:
    dm_dict_t* _all_term_dict;
    std::map<std::string, std::vector<int>> _term_to_rule_map;
    std::map<int, int> _rule_to_count_map;
    std::map<int, int> _rule_to_srcid_map;
    std::map<int, std::vector<std::string>> _idx_to_term_map;
    const static std::string SEP;
};
typedef std::shared_ptr<QueryMultiTermDicts> query_multi_term_dicts_ptr;

class DqaMultiTermStringMatchDict : public SearchDictBase {
public:
    DqaMultiTermStringMatchDict() {
        _cur_idx = 0;
        for(uint32_t i = 0; i < DICT_NUM; ++i) {
            _dicts[i].reset(new (std::nothrow) QueryMultiTermDicts());
        }
    }
    virtual ~DqaMultiTermStringMatchDict() {
        uninit();
    }

    virtual bool init(const comcfg::ConfigUnit& conf);
    virtual void uninit();
    
    virtual const std::string& file_path() const {
        return _file_path;
    }

    bool lookup(const std::string& buf) const ;
    bool lookup_str(const std::string& input_str, std::vector<std::string>& outputs) const;
private:
    virtual bool _reload();
    
    const static std::string SEP;
    uint32_t _cur_idx;
    std::string _file_path;
    query_multi_term_dicts_ptr _dicts[DICT_NUM];
};

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

#endif // APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_DQA_MULTI_TERM_STRING_MATCH_DICT_H

/* vim: set ts=4 sw=4: */

