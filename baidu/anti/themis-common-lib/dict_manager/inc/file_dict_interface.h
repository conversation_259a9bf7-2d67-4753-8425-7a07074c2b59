// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: yangjiaxue(<EMAIL>)
// 
// @File: file_dict_interface.h
// @Last modified: 2015-05-19 16:27:36
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_FILE_DICT_INTERFACE_H
#define APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_FILE_DICT_INTERFACE_H

#include <time.h>
#include <string>
#include <Configure.h>

namespace anti {
namespace themis {
namespace common_lib {

class FileDictInterface {
public:
    virtual ~FileDictInterface() {}
    virtual bool init(const comcfg::ConfigUnit& conf) = 0;
    virtual void uninit() = 0;
    virtual bool reload() = 0;
    virtual const std::string& file_path() const = 0;
};

// @brief: FileDictBase check the file was changed or not
//         if changed then call _reload
//         else return true
class FileDictBase : public FileDictInterface{
public:
    FileDictBase() : _modify_time(0U) {}
    virtual ~FileDictBase() {}

    virtual bool init(const comcfg::ConfigUnit& conf) = 0;
    virtual void uninit() = 0;
    virtual bool reload();
    virtual const std::string& file_path() const = 0;

private:
    virtual bool _reload() = 0;
    time_t _modify_time;
};

// @brief: SearchDictBase is the base class of dicts supported IN and NI operator
class SearchDictBase : public FileDictBase {
public:
    virtual ~SearchDictBase() {}
    virtual bool lookup(const std::string& search_key) const = 0;
    virtual bool lookup_str(const std::string& search_key, std::vector<std::string>& outputs) const = 0;
};

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_FILE_DICT_INTERFACE_H

/* vim: set ts=4 sw=4: */

