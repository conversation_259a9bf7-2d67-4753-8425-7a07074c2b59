// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: zhouzhiqiang02(<EMAIL>)
// 
// @File: cn_map_file_dict.h
// @Last modified: 
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_CN_MAP_FILE_DICT_H
#define APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_CN_MAP_FILE_DICT_H

#include <string>
#include <vector>
#include <unordered_map>
#include "file_dict_interface.h"

namespace anti {
namespace themis {
namespace common_lib {

typedef enum {
    CN_MAP_KEY_CN = 0,
    CN_MAP_KEY_CHANNEL = 1,
    CN_MAP_KEY_NUM
} CnMapKeyIdx;

typedef struct CnMapValue {
    std::string cn;
    std::string partner_tag;
    std::string flow_group;
    std::string channel_id;
} CnMapValueType;

class CnMapFileDict : public FileDictBase {
public:
    CnMapFileDict() : _cur_idx(0U) {}
    virtual ~CnMapFileDict() {
        uninit();
    }

    virtual bool init(const comcfg::ConfigUnit& conf);
    virtual void uninit();
    
    virtual const std::string& file_path() const {
        return _file_path;
    }
    
    bool get(const std::string& key, CnMapValueType* value, CnMapKeyIdx map_idx) const;

private:
    virtual bool _reload();
    static const uint32_t MAP_NUM = 2U;

    uint32_t _cur_idx;
    std::string _file_path;
    std::unordered_map<uint64_t, CnMapValueType> _inn_map[CN_MAP_KEY_NUM][MAP_NUM];
};

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_MULTI_COLUMN_FILE_DICT_H

/* vim: set ts=4 sw=4: */

