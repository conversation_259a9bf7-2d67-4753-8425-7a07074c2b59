// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: corr_cnt_dict.h
// @Last modified: 2016-04-18 12:43:51
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_CORR_CNT_DICT_H
#define APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_CORR_CNT_DICT_H

#include <unordered_set>
#include <unordered_map>
#include "file_dict_interface.h"

namespace anti {
namespace themis {
namespace common_lib {

class CorrCntDict : public FileDictBase {
public:
    CorrCntDict() : _cur_idx(0U) {};
    virtual ~CorrCntDict() {
        uninit();
    }

    virtual bool init(const comcfg::ConfigUnit& conf);

    virtual void uninit();

    virtual const std::string& file_path() const {
        return _file_path;
    }
    
    enum CorrResult {
        CORR_SUCC = 0,
        CORR_FAIL,
        NONEED_CORR
    };
    CorrResult corr_cn(const std::string& fr, const std::string& cnt, std::string* fix_cnt) const;

private:
    virtual bool _reload();
    static const uint32_t NUM = 2U;
    
    uint32_t _cur_idx;
    std::string _file_path;
    // fr_key->fix_cntname
    std::unordered_map<uint64_t, std::string> _maps[NUM];
    // fr_cnt joinkey
    std::unordered_set<uint64_t> _sets[NUM];
    const static std::string SEP;
};

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

#endif // APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_CORR_CNT_DICT_H
/* vim: set ts=4 sw=4: */
