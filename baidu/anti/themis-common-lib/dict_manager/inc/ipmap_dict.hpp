// Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
// @brief

namespace anti {
namespace themis {
namespace common_lib {

template<typename IpmapType>
IpmapDictTpl<IpmapType>::IpmapDictTpl() : _cur_idx(0) {
    for (uint32_t i = 0; i < MAP_NUM; i++) {
        _maps[i] = NULL;
    }
}

template<typename IpmapType>
bool IpmapDictTpl<IpmapType>::init(const comcfg::ConfigUnit& conf) {
    _field_sep = "\t";
    _field_num = 4;
    try {
        _file_path = conf["file_path"].to_cstr();
        if (conf["field_sep"].selfType() != comcfg::CONFIG_ERROR_TYPE) {
            _field_sep = conf["field_sep"].to_cstr();
        }
        if (conf["field_num"].selfType() != comcfg::CONFIG_ERROR_TYPE) {
            _field_num = conf["field_num"].to_int32();
        }
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std Exception : %s", e.what());
        return false;
    }

    for (uint32_t i = 0; i < MAP_NUM; ++i) {
        _maps[i] = NULL;
    }

    if (!reload()) {
        CWARNING_LOG("call reload fail, file(%s)", _file_path.c_str());
        return false;
    }
    return true;
}

template<typename IpmapType>
void IpmapDictTpl<IpmapType>::uninit() {}

template<typename IpmapType>
bool IpmapDictTpl<IpmapType>::_reload() {
    uint32_t idle = (_cur_idx + 1U) % MAP_NUM;
    if (_maps[idle] != NULL) {
        delete _maps[idle];
    }
    _maps[idle] = new (std::nothrow) IpmapType();
    if (_maps[idle] == NULL) {
        CWARNING_LOG("alloc ipmap failed");
        return false;
    }

    if (_maps[idle]->set_data_file(_file_path.c_str(),
                            _field_sep[0], _field_num) != 0) {
        CWARNING_LOG("load ipmap file failed, [file:%s]", _file_path.data());
        return false;
    }
    _cur_idx = idle;
    return true;
}

template<typename IpmapType>
bool IpmapDictTpl<IpmapType>::lookup(const std::string& ip) const {
    int32_t unused[2] = {0};
    return lookup(ip, &unused[0], &unused[1]);
}

template<typename IpmapType>
bool IpmapDictTpl<IpmapType>::lookup(const std::string& ip, int32_t* pid, int32_t* cid) const {
    if (pid == NULL || cid ==  NULL) {
        CFATAL_LOG("input pid or cid is NULL");
        return false;
    }

    if (_maps[_cur_idx]->get(ip.data(), *pid, *cid) == 1) {
        return true;
    }
    return false;
}

template<typename IpmapType>
bool IpmapDictTpl<IpmapType>::lookup(const int64_t ip) const {
    int32_t unused[2] = {0};
    return lookup(ip, &unused[0], &unused[1]);
}

template<typename IpmapType>
bool IpmapDictTpl<IpmapType>::lookup(const int64_t ip, int32_t* pid, int32_t* cid) const {
    if (pid == NULL || cid ==  NULL) {
        CFATAL_LOG("input pid or cid is NULL");
        return false;
    }

    if (_maps[_cur_idx]->get(ip, *pid, *cid) == 1) {
        return true;
    }
    return false;
}

} // namespace common_lib
} // namespace themis
} // namespace anti
