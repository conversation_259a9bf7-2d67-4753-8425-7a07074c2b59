// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: huanglina(<EMAIL>)
// 
// @File: key_multi_value_dict.h
// @Last modified: 2016-05-12 14:50:51
// @Brief: 

#ifndef APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_KEY_VALUE_DICT_H 
#define APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_KEY_VALUE_DICT_H

#include <fstream>
#include <vector>
#include <list>
#include <limits>
#include <unordered_map>
#include <boost/algorithm/string.hpp>
#include <boost/lexical_cast.hpp>
#include <com_log.h>
#include <sign_util.h>
#include "string_util.h"
#include "value_tmpl.h"
#include "file_dict_interface.h"

namespace anti {
namespace themis {
namespace common_lib {

template<typename T>
class KeyValueDict : public FileDictBase {
public:
    typedef T ValueContainer;
    typedef typename std::unordered_map<uint64_t, T>::iterator MapIter;
    typedef typename std::unordered_map<uint64_t, T>::const_iterator ConstMapIter;
    typedef anti::baselib::SignUtil SignUtil;
    
    KeyValueDict();
    virtual ~KeyValueDict() {
        uninit();
    }; 
    virtual bool init(const comcfg::ConfigUnit& conf);
    virtual void uninit();
    
    virtual const std::string& file_path() const {
        return _file_path;
    }   

    // lookup for key
    // return NULL when not found
    const T* lookup(uint64_t key) const;

    const T* lookup(const std::vector<std::string>& join_key) const;

    const T* lookup(const std::string& key) const; 

protected:
    static const char VAL_SEP = '\t';
    uint32_t _columns;
    uint32_t _key_columns;

private:
    virtual bool _reload();

    virtual bool _create_key(std::string& line, uint32_t* length, uint64_t* sign);

    static const uint32_t MAP_NUM = 2U; 
    static const std::string SEP;
    uint32_t _cur_idx;
    std::string _file_path;
    //std::unordered_map<uint64_t, T> _unordered_maps[MAP_NUM];
    std::unordered_map<uint64_t, T> _maps[MAP_NUM];
};

template<typename T>
const std::string KeyValueDict<T>::SEP = "_";

template<typename T>
KeyValueDict<T>::KeyValueDict() : 
        _columns(0),
        _key_columns(0),
        _cur_idx(0),
        _file_path("") {
}

template<typename T>
bool KeyValueDict<T>::init(const comcfg::ConfigUnit& conf) {
    try {
        _file_path = conf["file_path"].to_cstr();
        std::string kv_cols_str(conf["kv_cols"].to_cstr());
        std::vector<std::string> kv_cols;
        boost::split(kv_cols, kv_cols_str, boost::is_any_of(", "), boost::token_compress_on);
        // kv_cols format: max,key 
        // then split size should be 2
        if (kv_cols.size() != 2) {
            CFATAL_LOG("kv_cols.size(%d) is not 2! Assign kv_cols as format(_columns,key_cols)",
                    kv_cols.size());
            return false;
        }
        uint32_t columns = boost::lexical_cast<uint32_t>(kv_cols[0]);
        if (columns == 0u) {
            columns = std::numeric_limits<uint32_t>::max();
        }
        uint32_t key_columns = boost::lexical_cast<uint32_t>(kv_cols[1]);
        if (key_columns >= columns) {
            CFATAL_LOG("key_columns(%d) > columns(%d), file(%s)", 
                    key_columns, columns, _file_path.data());
            return false;
        }
        _key_columns = key_columns;
        _columns = columns;
    } catch (const comcfg::ConfigException& e) {
        CWARNING_LOG("ConfigException : %s", e.what());
        return false;
    } catch (const std::exception& e) {
        CWARNING_LOG("std Exception : %s", e.what());
        return false;
    }
    if (!reload()) {
        CWARNING_LOG("call reload fail, file(%s)", _file_path.data());
        return false;
    }
    return true;
}

template<typename T> 
void KeyValueDict<T>::uninit() {
    for (uint32_t i = 0; i < MAP_NUM; ++i) {
        _maps[i].clear();
    }
    _cur_idx = 0;
    _columns = 0;
    _key_columns = 0;
    _file_path = "";
}

template<typename T> 
bool KeyValueDict<T>::_create_key(std::string& line, uint32_t* length, uint64_t* sign) {
    uint32_t idx = 0U;
    for (uint32_t i = 0; i < line.size() && idx < _key_columns; ++i) {
        if (line[i] != VAL_SEP) {
            continue;
        }
        *length = i;
        ++idx;
        if (idx < _key_columns) {
            line[i] = SEP[0];
        }
    }
    if (idx < _key_columns) {
        CWARNING_LOG("line columns(%d) should be greater than _key_columns(%d)", 
                idx, _key_columns + 1);
        return false;
    }
    SignUtil::create_sign_md64(line.data(), *length, sign);
    return true;
}


template<typename T> 
bool KeyValueDict<T>::_reload() {
    uint32_t idle = (_cur_idx + 1U) % MAP_NUM;
    _maps[idle].clear();
    std::ifstream ifs(_file_path.c_str(), std::ifstream::in);
    if (ifs.fail()) {
        CWARNING_LOG("open file(%s) fail", _file_path.c_str());
        return false;
    }
    std::string line;
    uint32_t val_cols = 0u;
    if (_columns == std::numeric_limits<uint32_t>::max()) {
        val_cols = _columns;
    } else {
        val_cols = _columns - _key_columns;
    }
    ValueCreator<T> val_creator(val_cols);
    while (!getline(ifs, line).eof()) {
        std::vector<std::string> fields;
        uint32_t length;
        uint64_t sign = 0U;
        if (!_create_key(line, &length, &sign)) {
            CWARNING_LOG("call _create_key(%s) fail, _key_columns(%u)", line.data(), _key_columns);
            continue;
        }
        //_maps[idle][sign].push_back(values);
        MapIter iter = _maps[idle].find(sign);
        if (iter == _maps[idle].end()) {
            T container;
            iter = _maps[idle].emplace(sign, container).first;
        }
        if (!val_creator.create(line, length + 1, &(iter->second))) {
            CFATAL_LOG("call _create_value(%s) fail", line.data());
            return false;
        }
    }
    _cur_idx = idle;
    return true;
}

template<typename T>
const T* KeyValueDict<T>::lookup(uint64_t key) const {
    if (_cur_idx >= MAP_NUM) {
        CFATAL_LOG("terriable: _cur_idx(%u) >= MAP_NUM(%u)",
                _cur_idx, MAP_NUM);
        return NULL;
    }
    ConstMapIter citer = _maps[_cur_idx].find(key);
    if (citer == _maps[_cur_idx].end()) {
        return NULL;
    }
    return &citer->second;
}

template<typename T>
const T* KeyValueDict<T>::lookup(const std::vector<std::string>& join_key) const {
    if (join_key.size() != _key_columns) {
        CWARNING_LOG("input join_key size(%s) not match dict key_columns(%d)",
                join_key.size(), _key_columns);
        return NULL;
    }   
    uint64_t sign = 0LU; 
    SignUtil::create_sign_md64(StringUtil::join(join_key, SEP), &sign);
    return lookup(sign);
}

template<typename T> 
const T* KeyValueDict<T>::lookup(const std::string& key) const {
    uint64_t sign = 0LU;
    SignUtil::create_sign_md64(key, &sign);
    return lookup(sign);
}

typedef KeyValueDict<std::list<std::vector<int32_t>>> Int32KeyMultiValueDict;
typedef KeyValueDict<std::list<std::vector<uint32_t>>> Uint32KeyMultiValueDict;
typedef KeyValueDict<std::list<std::vector<std::string>>> StringKeyMultiValueDict;
typedef KeyValueDict<std::list<std::string>> StringKeyValueDict;
typedef KeyValueDict<std::list<int32_t>> Int32KeyValueDict;
typedef KeyValueDict<std::list<uint32_t>> Uint32KeyValueDict;
//unordered_map key = 字符UTF8 16进制编码值， value为该字符在字符串中出现的次数
typedef KeyValueDict<std::vector<std::unordered_map<std::string, int32_t>>> StringInt32MapKeyMultiValueDict;
typedef KeyValueDict<TradeInfo> UserTradeidDict;
typedef KeyValueDict<CntInfo> CntInfoDict;

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

#endif // APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_KEY_VALUE_DICT_H
/* vim: set ts=4 sw=4: */
