// Copyright (c) 2024 Baidu.com, Inc. All Rights Reserved
// @Author: liushua<PERSON>(<EMAIL>)
// 
// @File: str_match_with_space.h
// @Last modified: 2024-02-20 09:56:06
// @Brief: 支持带有空格的包含匹配词典

#ifndef APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_STRING_MATCH_WITH_SPACE_DICT_H
#define APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_STRING_MATCH_WITH_SPACE_DICT_H

#include <string> 
#include "ul_dictmatch.h"
#include "file_dict_interface.h"

namespace anti {
namespace themis {
namespace common_lib {

class StringMatcWithSpacehDict : public SearchDictBase {
public:
    StringMatcWithSpacehDict();
    virtual ~StringMatcWithSpacehDict() {
        uninit();
    }

    virtual bool init(const comcfg::ConfigUnit& conf);
    virtual void uninit();
    
    virtual const std::string& file_path() const {
        return _file_path;
    }

    bool lookup(const char* buf, int len) const;

    bool lookup(const std::string& buf) const {
        return lookup(buf.data(), buf.size());
    }
    /***
    * @description: input : 待匹配的字符串 
    * @description: output : 会将查找到的string存入vector返回
    */
    bool lookup_str(const std::string& input_str, std::vector<std::string>& outputs) const;
    /***
    * @description: 重写字典树的reload词典函数，将空格replace为\a存入
    * @description: input: const char* fullpath: 词典路径
    * @description: input: int lemma_num: 子串数量
    * @description: input: dm_charset_t charset: 字符类型
    */
    dm_dict_t* dm_dict_load_with_space(const char* fullpath, int lemma_num, dm_charset_t charset);
private:
    virtual bool _reload();
    static const uint32_t DICT_NUM = 2;

    uint32_t _cur_idx;
    std::string _file_path;
    dm_dict_t* _dicts[DICT_NUM];
};

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

#endif // APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_STRING_MATCH_WITH_SPACE_DICT_H

/* vim: set ts=4 sw=4: */

