// Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
// @Author: lang<PERSON><PERSON>(<EMAIL>)
// 
// @File: cdf.h
// @Brief:

#ifndef APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_CDF_H
#define APP_ECOM_ANTI_THEMIS_COMMON_LIB_INC_CDF_H
#include <inttypes.h>
#include <cstdint>
#include <map>
#include <unordered_map>
#include <algorithm>
#include <com_log.h>
#include "likely.h"

namespace anti {
namespace themis {
namespace common_lib {

// In order to improve the efficiency of the Container's iterator
// we can use skiplist to replace map
template <class Container = std::map<int64_t, uint64_t>>
class CumulativeDistribution {
public:
    CumulativeDistribution() : _total(0) {}
    ~CumulativeDistribution() {clear();}
    // O(logn)
    bool increase_coord(int64_t x, uint64_t y); 
    // O(logn)
    bool decrease_coord(int64_t x, uint64_t y);
    // O(1), Can't get the latest cdf val, unless refresh_cache first.
    bool get_cdf_val_from_cache(int64_t x, double* cdf_val) const; 
    bool clear(void); 
    // O(n)
    bool refresh_cache(void);

    template<typename Archive>
    bool serialize(Archive* ar) const;
    template<typename Archive>
    bool deserialize(Archive* ar);
private:
    bool _cleaning_distribution(); 
    bool _rebuild_cache();
private:
    // key : view value 
    // value : value count
    Container _distribution;
    struct KeyHash {
        std::size_t operator()(const int64_t& k) const {
            return std::abs(k);
        }
    };
    std::unordered_map<int64_t, double, KeyHash> _cdf_cache;
    uint64_t _total;
};

struct UpdateOp {
    UpdateOp(int64_t t_x, uint64_t t_y, bool inc) : 
        x(t_x), 
        y(t_y), 
        increase(inc) {}
    int64_t x;
    uint64_t y;
    bool increase;
};
template <class Container = std::map<int64_t, uint64_t>>
class BatchCumulativeDistribution {
public:
    bool increase_coord(int64_t x, uint64_t y); 
    bool decrease_coord(int64_t x, uint64_t y); 
    bool exec_batch_update() const;
    bool get_cdf_val(int64_t x, double* cdf_val) const;
    template<typename Archive>
    bool serialize(Archive* ar) const;
    template<typename Archive>
    bool deserialize(Archive* ar);
private:
    mutable CumulativeDistribution<Container> _cdf;
    mutable std::vector<UpdateOp> _update_op_batch;
};

}
}
}
#include "cdf.hpp"
#endif

