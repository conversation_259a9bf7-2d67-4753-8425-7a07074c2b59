// Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
// @Author: langwen<PERSON>(<EMAIL>)
// 
// @File: cdf.hpp
// @Brief:

#include "utils.h"
namespace anti {
namespace themis {
namespace common_lib {

template <class Container>
bool CumulativeDistribution<Container>::increase_coord(int64_t x, uint64_t y) {
    auto iter = _distribution.find(x);
    if (iter == _distribution.end()) {
        bool ok = _distribution.emplace(x, y).second;
        if (!ok) {
            CWARNING_LOG("emplace to _distribution failed!x:%" PRId64 " y:%" PRIu64 "\n", x, y);
            return false;
        }
    } 
    else {
        iter->second += y;
    }
    _total += y;
    return true;
}

template <class Container>
bool CumulativeDistribution<Container>::decrease_coord(int64_t x, uint64_t y) {
    auto iter = _distribution.find(x);
    if (iter == _distribution.end()) {
        return false;
    }
    uint64_t min_y = std::min(y, iter->second);
    iter->second -= min_y; 
    _total -= min_y;
    return true;
}

template <class Container>
bool CumulativeDistribution<Container>::get_cdf_val_from_cache(
        int64_t x, 
        double* cdf_val) const {
    if (UNLIKELY(!cdf_val)) {
        return false;
    }
    const auto iter = _cdf_cache.find(x);
    if (UNLIKELY(iter == _cdf_cache.cend())) {
        return false;
    }
    *cdf_val = iter->second;
    return true;
}
template <class Container>
bool CumulativeDistribution<Container>::clear(void) {
    _cdf_cache.clear();
    _distribution.clear();
    return true;
}
template <class Container>
bool CumulativeDistribution<Container>::refresh_cache(void) {
    bool ok = _cleaning_distribution();
    if (!ok) {
        CWARNING_LOG("_cleaning distribution and cache failed!");
    }
    ok = _rebuild_cache();
    if (!ok) {
        CWARNING_LOG("_refresh_cache failed!");
    }
    return ok;
}
template <class Container>
bool CumulativeDistribution<Container>::_cleaning_distribution() {
    std::vector<int64_t> trash;
    for (const auto& item : _distribution) {
        uint64_t y = item.second;
        if (y == 0) {
            trash.push_back(item.first);
        }
    } 
    for (const auto& x : trash) {
        _distribution.erase(x);  
    }
    return true;
}

template <class Container>
bool CumulativeDistribution<Container>::_rebuild_cache() {
    _cdf_cache.clear(); 
    uint64_t pre_sum = 0;
    for (const auto& item : _distribution) {
        const int64_t x = item.first; 
        const int64_t y = item.second;
        const uint64_t cur_sum = pre_sum + y;
        _cdf_cache[x] = cur_sum * 1.0 / _total;
        pre_sum = pre_sum + y;
    }
    return true;
}

template <class Container>
template <typename Archive>
bool CumulativeDistribution<Container>::serialize(Archive* ar) const {
    if (!ar) {
        CWARNING_LOG("ar is nullptr!");
        return false;
    }
    // write size first
    uint64_t size = _distribution.size();
    bool ok = t_write(size, ar);
    if (!ok) {
        CWARNING_LOG("write distribution size to ar failed!");
        return false;
    }
    ok = t_write(_total, ar);
    if (!ok) {
        CWARNING_LOG("write distribution total to ar failed!");
        return false;
    }
    // then write each x,y
    for (const auto& item : _distribution) {
        int64_t x = item.first;   
        uint64_t y = item.second;
        ok = t_write(x, ar);
        if (!ok) {
            CWARNING_LOG("write x to ar failed!");
            return false;
        }
        ok = t_write(y, ar);
        if (!ok) {
            CWARNING_LOG("write y to ar failed!");
            return false;
        }
    }
    return true;
}

template <class Container>
template <typename Archive>
bool CumulativeDistribution<Container>::deserialize(Archive* ar) {
    clear();
    if (!ar) {
        CWARNING_LOG("ar is nullptr!");
        return false;
    }
    uint64_t size = 0;
    bool ok = t_read(&size, ar);
    if (!ok) {
        CWARNING_LOG("read distribution size from ar failed!");
        return false;
    }
    uint64_t total = 0;
    ok = t_read(&total, ar);
    if (!ok) {
        CWARNING_LOG("read distribution size from ar failed!");
        return false;
    }
    for (uint64_t i = 0; i < size; i++) {
        uint64_t x = 0;
        uint64_t y = 0;
        ok = t_read(&x, ar);
        if (!ok) {
            CWARNING_LOG("read x from ar failed!");
            return false;
        }
        ok = t_read(&y, ar);
        if (!ok) {
            CWARNING_LOG("read y from ar failed!");
            return false;
        }
        ok = increase_coord(x, y);
        if (!ok) {
            CWARNING_LOG("increase coord failed!");
            return false;
        }
    }
    if (total != _total) {
        CWARNING_LOG("total check failed!");
        return false;
    }
    ok = refresh_cache();
    if (!ok) {
        CWARNING_LOG("refresh cache failed!");
        return false;
    }
    return true;
}

template <class Container>
bool BatchCumulativeDistribution<Container>::increase_coord(int64_t x, uint64_t y) {
    _update_op_batch.emplace_back(x, y, true);
    return true;
}

template <class Container>
bool BatchCumulativeDistribution<Container>::decrease_coord(int64_t x, uint64_t y) {
    _update_op_batch.emplace_back(x, y, false);
    return true;
}
template <class Container>
bool BatchCumulativeDistribution<Container>::exec_batch_update() const {
    for (const auto& op : _update_op_batch) {
        if (op.increase) {
            _cdf.increase_coord(op.x, op.y);
        } 
        else {
            _cdf.decrease_coord(op.x, op.y);
        }
    } 
    _cdf.refresh_cache();
    _update_op_batch.clear();
    return true;
}
template <class Container>
bool BatchCumulativeDistribution<Container>::get_cdf_val(int64_t x, double* cdf_val) const {
    return _cdf.get_cdf_val_from_cache(x, cdf_val);
}

template <class Container>
template <typename Archive>
bool BatchCumulativeDistribution<Container>::serialize(Archive* ar) const {
    if (!ar) {
        CWARNING_LOG("ar is nullptr!");
        return false;
    }
    bool ok = exec_batch_update(); 
    if (!ok) {
        CWARNING_LOG("exec batch update failed!");
        return false;
    }
    return _cdf.serialize(ar);
}

template <class Container>
template <typename Archive>
bool BatchCumulativeDistribution<Container>::deserialize(Archive* ar) {
    if (!ar) {
        CWARNING_LOG("ar is nullptr!");
        return false;
    }
    return _cdf.deserialize(ar);
}

}
}
}

