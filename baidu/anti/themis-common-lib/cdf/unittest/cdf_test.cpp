// Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
// @Author: langwen<PERSON>(<EMAIL>)
// 
// @File: fea_val_cdf_test.cpp

#include <gtest/gtest.h>
#include <bmock.h>
#include "archive.h"
#include "cdf.h"

using ::testing::_;
using ::testing::Return;
using ::testing::DoAll;
using ::testing::SetArgPointee;
using ::testing::SetArgumentPointee;

namespace anti {
namespace themis {
namespace common_lib {


class  CumulativeDistributionTestSuite : public ::testing::Test {
protected: 
    virtual void SetUp() {
    }
    virtual void TearDown() {
    }
private:
};

TEST_F(CumulativeDistributionTestSuite, cdf_simple_increase) {
    double cdf_val;
    CumulativeDistribution<> cdf;

    bool ok = cdf.increase_coord(0, 0);
    ASSERT_TRUE(ok);
    ASSERT_EQ(cdf._distribution.size(), 1);
    ok = cdf.refresh_cache();
    ASSERT_TRUE(ok);
    ASSERT_EQ(cdf._cdf_cache.size(), 0);
    ASSERT_EQ(cdf._distribution.size(), 0);
// --------------------------------------------------
    ok = cdf.increase_coord(0, 1); 
    ASSERT_TRUE(ok);
    ok = cdf.increase_coord(1, 1); 
    ASSERT_TRUE(ok);

    ASSERT_EQ(cdf._distribution.size(), 2);
    ASSERT_EQ(cdf._cdf_cache.size(), 0);
    ok = cdf.refresh_cache();
    ASSERT_TRUE(ok);
    ASSERT_EQ(cdf._cdf_cache.size(), 2);

    ASSERT_DOUBLE_EQ(0.5, cdf._cdf_cache[0]);
    ASSERT_DOUBLE_EQ(1.0, cdf._cdf_cache[1]);

    ASSERT_TRUE(cdf.get_cdf_val_from_cache(0, &cdf_val));
    ASSERT_DOUBLE_EQ(cdf_val, 0.5);
    ASSERT_TRUE(cdf.get_cdf_val_from_cache(1, &cdf_val));
    ASSERT_DOUBLE_EQ(cdf_val, 1.0);
// --------------------------------------------------
// 0:1, 1:1, 2:1, 3:1
    ok = cdf.increase_coord(2, 1); 
    ASSERT_TRUE(ok);
    ok = cdf.increase_coord(3, 1); 
    ASSERT_TRUE(ok);
    ASSERT_TRUE(cdf.refresh_cache());
    ASSERT_EQ(cdf._distribution.size(), 4);
    ASSERT_EQ(cdf._cdf_cache.size(), 4);
    ASSERT_EQ(cdf._distribution[0], 1);
    ASSERT_EQ(cdf._distribution[1], 1);
    ASSERT_EQ(cdf._distribution[2], 1);
    ASSERT_EQ(cdf._distribution[3], 1);
    ASSERT_TRUE(cdf.get_cdf_val_from_cache(0, &cdf_val));
    EXPECT_DOUBLE_EQ(cdf_val, 0.25);
    ASSERT_TRUE(cdf.get_cdf_val_from_cache(1, &cdf_val));
    EXPECT_DOUBLE_EQ(cdf_val, 0.5);
    ASSERT_TRUE(cdf.get_cdf_val_from_cache(2, &cdf_val));
    EXPECT_DOUBLE_EQ(cdf_val, 0.75);
    ASSERT_TRUE(cdf.get_cdf_val_from_cache(3, &cdf_val));
    EXPECT_DOUBLE_EQ(cdf_val, 1.0);
// -----------------------------------------------------
}

TEST_F(CumulativeDistributionTestSuite, cdf_simple_inc_and_dec) {
    double cdf_val;
    CumulativeDistribution<> cdf;
    bool ok = cdf.increase_coord(0, 1);
    ASSERT_TRUE(ok);
    ok = cdf.increase_coord(1, 2);
    ASSERT_TRUE(ok);
    ok = cdf.increase_coord(1, 2);
    ASSERT_TRUE(ok);
    ok = cdf.increase_coord(2, 2);
    ASSERT_TRUE(ok);
    ok = cdf.decrease_coord(2, 2);
    ASSERT_TRUE(ok);
    ASSERT_EQ(cdf._distribution.size(), 3);
    ASSERT_EQ(cdf._cdf_cache.size(), 0);
    ok = cdf.refresh_cache();
    ASSERT_TRUE(ok);
    ASSERT_EQ(cdf._distribution.size(), 2);
    ASSERT_EQ(cdf._cdf_cache.size(), 2);

    ASSERT_TRUE(cdf.get_cdf_val_from_cache(0, &cdf_val));
    ASSERT_DOUBLE_EQ(cdf_val, 0.2);
    ASSERT_TRUE(cdf.get_cdf_val_from_cache(1, &cdf_val));
    ASSERT_DOUBLE_EQ(cdf_val, 1);
// --------------------------------------------------
    ok = cdf.increase_coord(1, 3);
    ASSERT_TRUE(ok);
    ok = cdf.increase_coord(3, 5);
    ASSERT_TRUE(ok);
// 0:1 1:7 3:5
    ASSERT_EQ(cdf._distribution.size(), 3);
    ASSERT_EQ(cdf._cdf_cache.size(), 2);
    ASSERT_TRUE(cdf.get_cdf_val_from_cache(0, &cdf_val));
    ASSERT_DOUBLE_EQ(cdf_val, 0.2);
    ASSERT_TRUE(cdf.get_cdf_val_from_cache(1, &cdf_val));
    ASSERT_DOUBLE_EQ(cdf_val, 1);
    ok = cdf.refresh_cache();
    ASSERT_TRUE(ok);
    ASSERT_EQ(cdf._distribution.size(), 3);
    ASSERT_EQ(cdf._cdf_cache.size(), 3);

    ASSERT_TRUE(cdf.get_cdf_val_from_cache(0, &cdf_val));
    ASSERT_DOUBLE_EQ(cdf_val, (double)1 / 13);
    ASSERT_TRUE(cdf.get_cdf_val_from_cache(1, &cdf_val));
    ASSERT_DOUBLE_EQ(cdf_val, (double)8 / 13);
    ASSERT_TRUE(cdf.get_cdf_val_from_cache(3, &cdf_val));
    ASSERT_DOUBLE_EQ(cdf_val, 1.0);
}

class Timer {
public:
    Timer() {
        clear();
        start();
    }
    ~Timer() {}

    void start() {
        gettimeofday(&_start_time, NULL);
    }

    void end() {
        gettimeofday(&_end_time, NULL);
    }

    void clear() {
        _start_time.tv_sec = 0;
        _start_time.tv_usec = 0;
        _end_time.tv_sec = 0;
        _end_time.tv_usec = 0;
    }

    int64_t get_cost() {
        return _end_time.tv_sec * 1000000UL + _end_time.tv_usec 
            - _start_time.tv_sec * 1000000UL - _start_time.tv_usec;
    }

    int64_t get_us() {
        end();
        return get_cost();
    }

private:
    struct timeval _start_time;
    struct timeval _end_time;
};

class TimerGuard {
public:
    TimerGuard(int64_t* cost) : _cost(cost) {}
    ~TimerGuard() {
        if (_cost) {
            *_cost += _timer.get_us();
        }
    }
private:
    int64_t* _cost;
    Timer _timer;
};
TEST_F(CumulativeDistributionTestSuite, cdf_inc_then_dec_speed_test) {
    int inc_num = 1000000; 
    CumulativeDistribution<> cdf;
    int64_t cost = 0;
    {
        TimerGuard tg(&cost);
        for (int i = 0; i < inc_num; i++) {
            EXPECT_TRUE(cdf.increase_coord(i % 1000, 1));
        }
    }
    CFATAL_LOG("increase ops: %f", inc_num * 1.0 * 1000000 / cost);
    cost = 0;
    {
        TimerGuard tg(&cost);
        EXPECT_TRUE(cdf.refresh_cache()); 
    }
    CFATAL_LOG("refresh cache: %dms", cost / 1000);
    cost = 0;
    {
        TimerGuard tg(&cost);
        double cdf_val = 0.0;
        for (int i = 0; i < inc_num; i++) {
            EXPECT_TRUE(cdf.get_cdf_val_from_cache(i % 1000, &cdf_val));
        }
    }
    CFATAL_LOG("query ops:%f", inc_num * 1.0 * 1000000 / cost);
    cost = 0;
    {
        TimerGuard tg(&cost);
        for (int i = 0; i < inc_num; i++) {
            EXPECT_TRUE(cdf.decrease_coord(i % 1000, 1));
        }
    }
    CFATAL_LOG("decrease ops: %f", inc_num * 1.0 * 1000000/ cost);
}

TEST_F(CumulativeDistributionTestSuite, serialize_deserialize_test) {
    CumulativeDistribution<> cd;
    const int inc_num = 100000;
    for (int i = 0; i < inc_num; i++) {
        EXPECT_TRUE(cd.increase_coord(i % 10000, 1));
    }
    // dump base window
    std::string ckpt_filepath = "./cdf.ckpt";
    system("rm -rf ./cdf.ckpt");
    anti::themis::common_lib::FileArchive ar;
    ASSERT_TRUE(ar.open_w(ckpt_filepath.c_str()));
    ASSERT_TRUE(cd.serialize(&ar));
    ar.flush();
    ASSERT_TRUE(ar.close());
    
    // load base cpt to diff window
    anti::themis::common_lib::FileArchive ar_read;
    ASSERT_TRUE(ar_read.open_r(ckpt_filepath.c_str()));
    CumulativeDistribution<> cd_other;
    ASSERT_TRUE(cd_other.deserialize(&ar_read));
    ar_read.close();
    EXPECT_EQ(cd._distribution, cd_other._distribution);
}

class  BatchCumulativeDistributionTestSuite : public ::testing::Test {
protected: 
    virtual void SetUp() {
    }
    virtual void TearDown() {
    }
private:
};

TEST_F(BatchCumulativeDistributionTestSuite, cdf_simple_inc_and_dec) {
    BatchCumulativeDistribution<> bcdf; 
    ASSERT_TRUE(bcdf.increase_coord(0,1));
    ASSERT_TRUE(bcdf.increase_coord(1,1));
    EXPECT_EQ(2, bcdf._update_op_batch.size());

    EXPECT_EQ(0, bcdf._update_op_batch[0].x);
    EXPECT_EQ(1, bcdf._update_op_batch[0].y);
    EXPECT_TRUE(bcdf._update_op_batch[0].increase);

    EXPECT_EQ(1, bcdf._update_op_batch[1].x);
    EXPECT_EQ(1, bcdf._update_op_batch[1].y);
    EXPECT_TRUE(bcdf._update_op_batch[1].increase);
    

    ASSERT_TRUE(bcdf.decrease_coord(0,1));
    ASSERT_TRUE(bcdf.decrease_coord(1,1));

    EXPECT_EQ(0, bcdf._update_op_batch[2].x);
    EXPECT_EQ(1, bcdf._update_op_batch[2].y);
    EXPECT_FALSE(bcdf._update_op_batch[2].increase);

    EXPECT_EQ(1, bcdf._update_op_batch[3].x);
    EXPECT_EQ(1, bcdf._update_op_batch[3].y);
    EXPECT_FALSE(bcdf._update_op_batch[3].increase);
}

TEST_F(BatchCumulativeDistributionTestSuite, cdf_simple_inc_and_exec) {
    BatchCumulativeDistribution<> bcdf; 
    ASSERT_TRUE(bcdf.increase_coord(0,1));
    ASSERT_TRUE(bcdf.increase_coord(1,1));
    ASSERT_TRUE(bcdf.increase_coord(2,1));
    ASSERT_TRUE(bcdf.increase_coord(2,1));
    ASSERT_TRUE(bcdf.exec_batch_update());
    EXPECT_EQ(0, bcdf._update_op_batch.size());
    EXPECT_EQ(1, bcdf._cdf._distribution[0]);
    EXPECT_EQ(1, bcdf._cdf._distribution[1]);
    EXPECT_EQ(2, bcdf._cdf._distribution[2]);
    double cdf_val = 0.0;
    ASSERT_TRUE(bcdf._cdf.get_cdf_val_from_cache(0, &cdf_val));
    ASSERT_DOUBLE_EQ(0.25, cdf_val);
    ASSERT_TRUE(bcdf._cdf.get_cdf_val_from_cache(1, &cdf_val));
    ASSERT_DOUBLE_EQ(0.5, cdf_val);
    ASSERT_TRUE(bcdf._cdf.get_cdf_val_from_cache(2, &cdf_val));
    ASSERT_DOUBLE_EQ(1, cdf_val);
}

TEST_F(BatchCumulativeDistributionTestSuite, serialize_deserialize_test) {
    BatchCumulativeDistribution<> bcdf; 
    const int inc_num = 100000;
    for (int i = 0; i < inc_num; i++) {
        EXPECT_TRUE(bcdf.increase_coord(i % 10000, 1));
    }
    EXPECT_EQ(inc_num, bcdf._update_op_batch.size());
    // dump base window
    std::string ckpt_filepath = "./bcdf.ckpt";
    system("rm -rf ./bcdf.ckpt");
    anti::themis::common_lib::FileArchive ar;
    ASSERT_TRUE(ar.open_w(ckpt_filepath.c_str()));
    ASSERT_TRUE(bcdf.serialize(&ar));
    ar.flush();
    ASSERT_TRUE(ar.close());
    EXPECT_EQ(0, bcdf._update_op_batch.size());
    
    // load base cpt to diff window
    anti::themis::common_lib::FileArchive ar_read;
    ASSERT_TRUE(ar_read.open_r(ckpt_filepath.c_str()));
    BatchCumulativeDistribution<> bcdf_other;
    ASSERT_TRUE(bcdf_other.deserialize(&ar_read));
    ar_read.close();
    EXPECT_EQ(bcdf._cdf._distribution, bcdf_other._cdf._distribution);
    EXPECT_EQ(0, bcdf_other._update_op_batch.size());
}


} // namespace common_lib
} // namespace themis
} // namespace anti

/* vim: set ts=4 sw=4: */

