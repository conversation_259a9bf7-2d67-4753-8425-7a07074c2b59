#edit-mode: -*- python -*-
#coding:gbk

WORKROOT('../../../')
#Preprocessor flags.
CPPFLAGS('-DNDEBUG -D_GNU_SOURCE -D__STDC_LIMIT_MACROS -DMOCKVIRTUAL=')
PROTOFLAGS('--proto_path=./*/proto --python_out=./*/proto')
UTCPPFLAGS = '-DDEBUG -D_GNU_SOURCE -D__STDC_LIMIT_MACROS -DUNIT_TEST -fno-access-control'

#C++ flags.
COMPILER("gcc12")
if GLOBAL_GCC_VERSION() in ('gcc82', 'gcc12'):
    CFLAGS('-Wno-error=implicit-function-declaration')
    CXXFLAGS('-Wno-error=deprecated-declarations')
    CXXFLAGS('-Wno-error=format-truncation')
    CXXFLAGS('-Wno-error=implicit-function-declaration')
    CXXFLAGS('-Wno-error=misleading-indentation')
    CXXFLAGS('-Wno-error=parentheses')
    CXXFLAGS('-Wno-error=reorder')
    CXXFLAGS('-Wno-error=sign-compare')
    CXXFLAGS('-Wno-error=unused-value')
    ENABLE_GLOBAL_FLAGS()

if GLOBAL_GCC_VERSION() in ('gcc82'):
    CXXFLAGS('-luuid')

CXXFLAGS('-std=c++11 -g -pipe -W -Wall -Wno-unused-parameter -Wno-unused-local-typedefs -Wno-write-strings -fPIC -fno-omit-frame-pointer -fpermissive')

#-I path
INCPATHS('.')

#link flags
LDFLAGS('-lpthread -lcrypto -lrt  -ldl -lcrypt -Bsymbolic -rdynamic -lz -lssl -lbfd -lopcodes -liberty')

#CONFIGS('libsrc/others-ex@others-ex_3-1-25-0_PD_BL')
CONFIGS('third-64/gflags@gflags_1-6-0-100_PD_BL')
CONFIGS('lib2-64/others-ex@others-ex_3-1-25-0_PD_BL')
#CONFIGS('lib2-64/ullib@ullib_3-1-110-0_PD_BL')
CONFIGS('baidu/base/ullib@stable')
CONFIGS('baidu/base/bsl@stable')
CONFIGS('baidu/base/configure@stable')
CONFIGS("baidu/third-party/boost@boost_V1.70.0.3_GCC482_4U3_K3_GEN_PD_BL@git_tag")
CONFIGS('third-64/gtest@gtest_1-6-0-100_PD_BL')
CONFIGS('third-64/protobuf@protobuf_2-4-1-500_PD_BL')
CONFIGS('baidu/base/bmock@stable')
CONFIGS('baidu/base/fault@stable')
CONFIGS('baidu/anti/base-lib@master@git_branch')
CONFIGS('baidu/third-party/concurrentqueue@master@git_branch')
CONFIGS('baidu/colombo/ipv6-iplib-sdk@master@git_branch')
CONFIGS('lib2-64/nlpc/nlpc-wordrank@1.0.0')
CONFIGS('baidu/lib/nlpc-ernie-sim-slim@nlpc-ernie-sim-slim_1-0-2-4_gcc482_PD_BL@git_tag')
CONFIGS('lib2-64/nlpc/nlpc-lego@nlpc-lego_2-0-2-128177_PD_BL')
CONFIGS('baidu/nlpc/nlpc-c-api-gcc8-stable@stable')
CONFIGS("baidu/third-party/uuid@uuid_V1.0.3.1_r2_GCC12_4U3_K3_GEN_PD_BL@git_tag")

CONFIGS('lib2-64/nlpc/nlpc-common@nlpc-common_1-0-3-114117_PD_BL')
CONFIGS('baidu/nlpc/nlpc-statistics-gcc8@master@git_branch')
CONFIGS('baidu/nlpc/sofa-gcc12@stable')
proto_sources = GLOB('*/proto/*.proto')
proto_headers = GLOB_GEN_SRCS('*/proto/*.h')
StaticLibrary('message-proto', Sources(proto_sources))

#release headers
release_headers = GLOB('*/inc/* */dev/*.h')
HEADERS(release_headers, '$INC')
HEADERS(proto_headers, '$INC')
HEADERS(proto_sources, '$INC')

#release conf
OUTPUT('dict_manager/conf/file_dict_manager.conf', '$OUT/conf')

user_sources = GLOB('*/dev/*.cpp')
header_paths = GLOB('*/inc */dev')
StaticLibrary('common-lib', 
        Sources(user_sources, IncludePaths(header_paths, '$OUT/include')),
        Libraries('$OUT/lib/libmessage-proto.a'))

#ut
ut_sources=GLOB('*/unittest/*_test.cpp')
for test in ut_sources.split():
    path = os.path.splitext(test)[0]
    UTApplication(
        '../../' + path[0:path.rindex('/')] + '/bin' + path[path.rindex('/'):],
        Sources(test, IncludePaths(header_paths, '$OUT/include'), CppFlags(UTCPPFLAGS)),
        Libraries('$OUT/lib/libcommon-lib.a $OUT/lib/libmessage-proto.a'),
        UTOnServer(False)
    )
