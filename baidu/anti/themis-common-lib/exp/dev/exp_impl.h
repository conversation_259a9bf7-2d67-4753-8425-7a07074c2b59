// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: ch<PERSON><PERSON><PERSON><PERSON>(ch<PERSON><PERSON><PERSON><EMAIL>)
// 
// @File: exp.h
// @Last modified: 2018-03-28 14:29:55
// @Brief: 
//
#ifndef BAIDU_ANTI_THEMIS_COMMON_LIB_EXP_INC_EXP_IMPL_H
#define BAIDU_ANTI_THEMIS_COMMON_LIB_EXP_INC_EXP_IMPL_H

#include <vector>
#include <unordered_set>
#include <boost/lexical_cast.hpp>
#include <stack>
#include <unordered_map>
#include <memory>
#include <sign_util.h>
#include <com_log.h>

namespace anti {
namespace themis {
namespace common_lib {

class ExpressInterface;
typedef std::vector<double> Vector;
typedef std::shared_ptr<ExpressInterface> ExpressInterfacePtr;
typedef std::vector<ExpressInterfacePtr> Express;
const double EPSINON = 0.00000001;

// interface of exp
// it can be a operator or operand
class ExpressInterface {
public:
    ExpressInterface() : _value(""), _sign(0) {}
    // @brife : create a ExpressInterface
    // @param v : [IN] save an operator's or operand's value
    //            and create a sign by this value for find it 
    ExpressInterface(std::string v) : _value(v) {
        anti::baselib::SignUtil::create_sign_md64(_value, &_sign);
    }
    virtual ~ExpressInterface() {}

    enum Type {
        OPERAND = 0,
        UNARY_OPERATOR = 1,
        BINARY_OPERATOR = 2,
        PARENTHESE = 3
    };
    virtual Type type() = 0;
    virtual const std::string& value() {
        return _value;
    }
    virtual uint64_t sign() {
        return _sign;
    }
    virtual void set_sign(uint64_t sign) {
        _sign = sign;
    }
    virtual ExpressInterface* clone() const = 0;
    virtual void reset() {}
    virtual bool compute(
            std::stack<ExpressInterfacePtr>* stack) {
        return false;
    }
    virtual void debug_str() {}

private:
    std::string _value;
    uint64_t _sign;
};

class OperandExpress : public ExpressInterface {
public:
    OperandExpress() : 
            _value_type(NOT_EXIST), 
            _double_value(0.0),
            _uint64_value(0L),
            _int64_value(0L),
            _bool_value(false) {}
    OperandExpress(std::string v) :
            ExpressInterface(v),
            _value_type(NOT_EXIST), 
            _double_value(0.0),
            _uint64_value(0L),
            _int64_value(0L),
            _bool_value(false) {}
    virtual ~OperandExpress() {}
    virtual Type type() {
        return OPERAND;
    }
    virtual OperandExpress* clone() const {
        return new (std::nothrow) OperandExpress(*this);
    }
    enum ValueType {
        DOUBLE = 0,
        UINT64 = 1,
        INT64 = 2,
        BOOL = 3,
        VECTOR = 4,
        NOT_EXIST = 5,
        INVALID = 6,
        VALUE_TYPE_SIZE = 7
    };
    ValueType unary_state[VALUE_TYPE_SIZE] = {
            INVALID, INVALID, INVALID, BOOL, INVALID, NOT_EXIST, INVALID
    };

    ValueType binary_state[VALUE_TYPE_SIZE][VALUE_TYPE_SIZE] = {
            {DOUBLE, INVALID, INVALID, INVALID, INVALID, NOT_EXIST, INVALID},
            {INVALID, UINT64, INVALID, INVALID, INVALID, NOT_EXIST, INVALID},
            {INVALID, INVALID, INT64, INVALID, INVALID, NOT_EXIST, INVALID},
            {INVALID, INVALID, INVALID, BOOL, INVALID, INVALID, INVALID},
            {INVALID, INVALID, INVALID, INVALID, VECTOR, INVALID, INVALID},
            {NOT_EXIST, NOT_EXIST, NOT_EXIST, INVALID, NOT_EXIST, NOT_EXIST, INVALID},
            {INVALID, INVALID, INVALID, INVALID, INVALID, INVALID, INVALID}};
    virtual ValueType value_type() const {
        return _value_type;
    }
    // !
    ExpressInterfacePtr negation();
    // +
    ExpressInterfacePtr add(OperandExpress* rhs);
    // -
    ExpressInterfacePtr minus(OperandExpress* rhs);
    // *
    ExpressInterfacePtr multiply(OperandExpress* rhs);
    // /
    ExpressInterfacePtr division(OperandExpress* rhs);
    // >
    ExpressInterfacePtr greater(OperandExpress* rhs);
    // >=
    ExpressInterfacePtr equal_or_greater(OperandExpress* rhs);
    // >
    ExpressInterfacePtr less(OperandExpress* rhs);
    // >=
    ExpressInterfacePtr equal_or_less(OperandExpress* rhs);
    // ==
    ExpressInterfacePtr equal(OperandExpress* rhs);
    // !=
    ExpressInterfacePtr not_equal(OperandExpress* rhs);
    // &&
    ExpressInterfacePtr logical_and(OperandExpress* rhs);
    // ||
    ExpressInterfacePtr logical_or(OperandExpress* rhs);
    // ln
    ExpressInterfacePtr ln();
    // cos
    ExpressInterfacePtr vector_cos(OperandExpress* rhs);

    virtual double get_double_value() const {
        return _double_value;
    }
    virtual uint64_t get_uint64_value() const {
        return _uint64_value;
    }
    virtual int64_t get_int64_value() const {
        return _int64_value;
    }
    virtual bool get_bool_value() const {
        return _bool_value;
    }
    virtual const Vector& get_vector_value() const {
        return _vector;
    }

    virtual bool get_double_value(double* res) const;
    virtual bool get_uint64_value(uint64_t* res) const;
    virtual bool get_int64_value(int64_t* res) const;
    virtual bool get_bool_value(bool* res) const;

    virtual void set_value(double value) {
        _value_type = DOUBLE;
        _double_value = value;

    }
    virtual void set_value(float value) {
        _value_type = DOUBLE;
        _double_value = static_cast<double>(value);

    }
    virtual void set_value(uint64_t value) {
        _value_type = UINT64;
        _uint64_value = value;
    }
    virtual void set_value(int64_t value) {
        _value_type = INT64;
        _int64_value = value;
    }
    virtual void set_value(bool value) {
        _value_type = BOOL;
        _bool_value = value;
    }
    virtual void set_value(Vector v) {
        _value_type = VECTOR;
        _vector = v;
    }
    virtual void set_invalid_value() {
        _value_type = INVALID;
    }
    virtual void set_not_exist_value() {
        _value_type = NOT_EXIST;
    }
    virtual void set_value(std::string value) {
        // only constant express use this interface
        return;
    }
    virtual void reset() {
        _value_type = NOT_EXIST;
        _double_value = 0.0;
        _uint64_value = 0UL;
        _int64_value = 0L;
        _bool_value = false;
    }
    virtual void debug_str() {
        CDEBUG_LOG("value[%s], sign[%lu], value_type[%d]"
                "double_value[%lf], uint64_value[%lu], "
                "int64_value[%ld], bool_value[%d]",
                value().c_str(), sign(), _value_type,
                _double_value, _uint64_value, _int64_value, 
                _bool_value);
    }

protected:
    bool _convert(OperandExpress* rhs);
    double _vector_cos(const Vector& a, const Vector& b);
    ValueType _value_type;
    double _double_value;
    uint64_t _uint64_value;
    int64_t _int64_value;
    bool _bool_value;
    Vector _vector;
};

class ConstantExpress : public OperandExpress {
public:
    using OperandExpress::OperandExpress;
    virtual ~ConstantExpress() {}
    virtual void set_value(std::string value);

    virtual void reset() {
        // constant express do nothing
        return;
    }
};

class OperatorExpress : public ExpressInterface {
public:
    using ExpressInterface::ExpressInterface;
    virtual ~OperatorExpress() {}
    virtual Type type() = 0;
    virtual OperatorExpress* clone() const = 0;
    virtual bool compute(
            std::stack<ExpressInterfacePtr>* stack) {
        return false;
    }
};

class UnaryOperatorExpress : public OperatorExpress {
public:
    using OperatorExpress::OperatorExpress;
    virtual Type type() {
        return UNARY_OPERATOR;    
    };
    virtual UnaryOperatorExpress* clone() const {
        return new (std::nothrow) UnaryOperatorExpress(*this);
    }
    virtual bool compute(
            std::stack<ExpressInterfacePtr>* stack); 

};

class BinaryOperatorExpress : public OperatorExpress {
public:
    using OperatorExpress::OperatorExpress;
    virtual Type type() {
        return BINARY_OPERATOR;    
    };
    virtual BinaryOperatorExpress* clone() const {
        return new (std::nothrow) BinaryOperatorExpress(*this);
    }
    virtual bool compute(
            std::stack<ExpressInterfacePtr>* stack);
};

class ParentheseOperatorExpress : public OperatorExpress {
public:
    using OperatorExpress::OperatorExpress;
    virtual Type type() { 
        return PARENTHESE;
    }
    virtual ParentheseOperatorExpress* clone() const {
        return new (std::nothrow) ParentheseOperatorExpress(*this);
    }
    virtual bool compute(
            std::stack<ExpressInterfacePtr>* stack) {
        return false;
    }
};

class ExpSplitter {
public:
    ExpSplitter() {}
    ~ExpSplitter() {}
    static bool split(
            const std::string& exp_str, 
            const std::unordered_set<std::string>& atoms, 
            Express* exp);
    static const std::vector<std::string> PARENTHESE_DICT;
    static const std::vector<std::string> UNARY_DICT;
    static const std::vector<std::string> BINARY_DICT;

private:
    static bool _fetch(
            const std::unordered_set<std::string>& atoms, 
            const char* start, 
            uint32_t* offset,
            ExpressInterfacePtr* out);
};

} // common_lib
} // themis
} // anti

#endif // BAIDU_ANTI_THEMIS_COMMON_LIB_EXP_INC_EXP_IMPL_H

/* vim: set ts=4 sw=4: */

