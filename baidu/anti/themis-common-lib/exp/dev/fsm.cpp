// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: ch<PERSON><PERSON><PERSON><PERSON>(chen<PERSON><PERSON><EMAIL>)
// 
// @File: fsm.cpp
// @Last modified: 2018-04-02 10:59:33
// @Brief: 

#include "fsm.h"
#include <com_log.h>

namespace anti {
namespace themis {
namespace common_lib {

std::vector<StateTransfer> PAIR_TRANS_TABLE = {
        {NORMAL, SINGLE_QUOTE, IN_SINGLE_QUOTE},
        {NORMAL, DOUBLE_QUOTE, IN_DOUBLE_QUOTE},
        {NORMAL, LEFT_SQUARE_BRACKETS, IN_SQUARE_BRACKETS},
        {IN_SINGLE_QUOTE, ESCAPE_CHARACT, ESCAPE}, 
        {IN_DOUBLE_QUOTE, ESCAPE_CHARACT, ESCAPE},
        {IN_SQUARE_BRACKETS, SINGLE_QUOTE, IN_SINGLE_QUOTE},
        {IN_SQUARE_BRACKETS, DOUBLE_QUOTE, IN_DOUBLE_QUOTE}
};

std::vector<StateTransfer> STATE_TRANS_TABLE = {
        {IN_SINGLE_QUOTE, SINGLE_QUOTE, LAST}, 
        {IN_DOUBLE_QUOTE, DOUBLE_QUOTE,  LAST},
        {IN_SQUARE_BRACKETS, RIGHT_SQUARE_BRACKETS, NORMAL},
        {IN_SQUARE_BRACKETS, LEFT_SQUARE_BRACKETS, ERROR},
        {ESCAPE, SINGLE_QUOTE, LAST},
        {ESCAPE, DOUBLE_QUOTE, LAST},
        {ESCAPE, LEFT_SQUARE_BRACKETS, LAST},
        {ESCAPE, RIGHT_SQUARE_BRACKETS, LAST},
        {ESCAPE, ESCAPE_CHARACT, LAST},
};

std::unordered_map<char, Event> CHAR_EVENT_MAP = {
    {'\'', SINGLE_QUOTE},
    {'"', DOUBLE_QUOTE},
    {'[', LEFT_SQUARE_BRACKETS},
    {']', RIGHT_SQUARE_BRACKETS},
    {'\\', ESCAPE_CHARACT}
};

void StrFSM::transfer(const char& c, CharState* out) {
    Event input = OTHER;
    if (CHAR_EVENT_MAP.find(c) != CHAR_EVENT_MAP.end()) {
        input = CHAR_EVENT_MAP[c];
    }
    for (const auto& trans : PAIR_TRANS_TABLE) {
        if (trans.cur_state == _now && trans.event == input) {
            _save.push(_now);
            _now = trans.next_state;
            ++_loc;
            (*out).state = _now;
            (*out).loc = _loc;
            return;
        }
    }
    for (const auto& trans : STATE_TRANS_TABLE) {
        if (trans.cur_state == _now && trans.event == input) {
            _now = trans.next_state;
            if (_now == LAST) {
                _now = _save.top();
                _save.pop();
            }
            ++_loc;
            (*out).state = _now;
            (*out).loc = _loc;
            return;
        }
    }
    (*out).state = _now;
    (*out).loc = ++_loc;
    return;
}

} // common_lib
} // themis
} // anti

/* vim: set ts=4 sw=4: */

