// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: ch<PERSON><PERSON><PERSON><PERSON>(ch<PERSON><PERSON><PERSON><EMAIL>)
// 
// @File: fsm.h
// @Last modified: 2018-04-02 10:59:32
// @Brief: 

#ifndef BAIDU_ANTI_THEMIS_COMMON_LIB_EXP_INC_FSM_H
#define BAIDU_ANTI_THEMIS_COMMON_LIB_EXP_INC_FSM_H

#include <unordered_map>
#include <stack>
#include <vector>
#include <stdint.h>

namespace anti {
namespace themis {
namespace common_lib {

enum State {
    LAST = 1,
    NORMAL = 2,
    IN_SINGLE_QUOTE = 3,
    IN_DOUBLE_QUOTE = 4,
    IN_SQUARE_BRACKETS = 5,
    ESCAPE = 6,
    ERROR = 255, 
    // IN_PARENTHESIS = 6,
    // IN_BRACE = 7,
    // END = 8
};

enum Event {
    SINGLE_QUOTE = 1,
    DOUBLE_QUOTE = 2,
    LEFT_SQUARE_BRACKETS = 3,
    RIGHT_SQUARE_BRACKETS = 4,
    ESCAPE_CHARACT = 5,
    OTHER = 6
    // LEFT_PARENTHESIS = 7,
    // RIGHT_PARENTHESIS = 8,
    // LEFT_BRACE = 9,
    // RIGHT_BRACE = 10,
};

struct StateTransfer {
    State cur_state;
    Event event;
    State next_state;
};

struct CharState {
    State state;
    int32_t loc;
};

class StrFSM {
public:
    StrFSM() : _loc(-1), _now(NORMAL) {}
    ~StrFSM() {}
    void transfer(const char& event, CharState* state); 
    void reset() {
        _loc = -1;
        _now = NORMAL;
    }

private:
    int32_t _loc;
    State _now;
    std::stack<State> _save;
};

} // common_lib
} // themis
} // anti

#endif // BAIDU_ANTI_THEMIS_COMMON_LIB_EXP_INC_FSM_H

/* vim: set ts=4 sw=4: */

