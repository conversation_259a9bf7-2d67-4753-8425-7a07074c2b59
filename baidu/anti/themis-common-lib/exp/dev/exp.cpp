// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: ch<PERSON><PERSON><PERSON><PERSON>(ch<PERSON><PERSON><PERSON><PERSON>@baidu.com)
// 
// @File: exp.cpp
// @Last modified: 2018-04-02 10:57:16
// @Brief: 
//

#include "exp.h"
#include "fsm.h"
#include <com_log.h>
#include <boost/lexical_cast.hpp>
#include <boost/algorithm/string.hpp> 
#include <math.h>

namespace anti {
namespace themis {
namespace common_lib {

ExpressInterfacePtr OperandExpress::negation() {
    OperandExpress* tmp = new (std::nothrow) OperandExpress();
    if (tmp == NULL) {
        CFATAL_LOG("new OperandExpress failed!");
        return NULL;
    }
    if (_value_type == BOOL) {
        tmp->set_value(!_bool_value);
    } else if (_value_type == NOT_EXIST) {
        tmp->set_not_exist_value();
    } else {
        tmp->set_invalid_value();
    }
    return ExpressInterfacePtr(tmp);
}

ExpressInterfacePtr OperandExpress::ln() {
    OperandExpress* tmp = new (std::nothrow) OperandExpress();
    if (tmp == NULL) {
        CFATAL_LOG("new OperandExpress failed!");
        return NULL;
    }
    if (_value_type == DOUBLE) {
        tmp->set_value(std::log(_double_value));
    } else if (_value_type == UINT64) {
        tmp->set_value(std::log(_uint64_value));
    } else if (_value_type == INT64) {
        tmp->set_value(std::log(_int64_value));
    } else if (_value_type == NOT_EXIST) {
        tmp->set_not_exist_value();
    } else {
        tmp->set_invalid_value();
    }
    return ExpressInterfacePtr(tmp);
}

bool OperandExpress::_convert(OperandExpress* rhs) {
    // Unified operand types if the operands computable
    if (rhs == NULL) {
        CFATAL_LOG("invalid input rhs");
        return false;
    }
    try {
        double converted = 0.0;
        if (_value_type == DOUBLE) {
            if (rhs->value_type() == UINT64) {
                converted = boost::lexical_cast<double>(rhs->get_uint64_value());       
                rhs->set_value(converted);
                return true;
            }
            if (rhs->value_type() == INT64) {
                converted = boost::lexical_cast<double>(rhs->get_int64_value());
                rhs->set_value(converted);
                return true;
            }
        }
        if (rhs->value_type() == DOUBLE) {
            if (_value_type == UINT64) {
                converted = boost::lexical_cast<double>(_uint64_value);
                set_value(converted);
                return true;
            }
            if (_value_type == INT64) {
                converted = boost::lexical_cast<double>(_int64_value);
                set_value(converted);
                return true;
            }
        }
        if (_value_type == UINT64 && rhs->value_type() == INT64) {
            if (_uint64_value > INTMAX_MAX) {
                CWARNING_LOG("value[%lu] > INTMAX_MAX[%lu], convert failed!", 
                        _uint64_value, INTMAX_MAX);
                _value_type = INVALID;
                return true;
            }
            set_value(boost::lexical_cast<int64_t>(_uint64_value));
            return true;
        }
        if (_value_type == INT64 && rhs->value_type() == UINT64) {
            if (rhs->get_uint64_value() > INTMAX_MAX) {
                CWARNING_LOG("value[%lu] > INTMAX_MAX[%lu], convert failed!", 
                        rhs->get_uint64_value(), INTMAX_MAX);
                rhs->set_invalid_value();
                return true;
            }
            rhs->set_value(boost::lexical_cast<int64_t>(rhs->get_uint64_value()));
            return true;
        }
    } catch (const boost::bad_lexical_cast& e) {
        CFATAL_LOG("bad convert!");
        return false;
    }
    return true;
}

ExpressInterfacePtr OperandExpress::add(OperandExpress* rhs) {
    if (!_convert(rhs)) {
        CFATAL_LOG("bad convert!");
        return  NULL;
    }
    OperandExpress* tmp = new (std::nothrow) OperandExpress();
    if (tmp == NULL) {
        CFATAL_LOG("new OperandExpress failed!");
        return NULL;
    }
    auto res_type = binary_state[_value_type][rhs->value_type()];
    if (res_type == DOUBLE) {
        tmp->set_value(_double_value + rhs->get_double_value());
    } else if (res_type == UINT64) {
        tmp->set_value(_uint64_value + rhs->get_uint64_value()); 
    } else if (res_type == INT64) {
        tmp->set_value(_int64_value + rhs->get_int64_value()); 
    } else if (res_type == INVALID) {
        tmp->set_invalid_value();
    } else if (res_type == NOT_EXIST) {
        tmp->set_not_exist_value();
    }
    return ExpressInterfacePtr(tmp);
}

ExpressInterfacePtr OperandExpress::minus(OperandExpress* rhs) {
    if (!_convert(rhs)) {
        CFATAL_LOG("bad convert!");
        return  NULL;
    }
    OperandExpress* tmp = new (std::nothrow) OperandExpress();
    if (tmp == NULL) {
        CFATAL_LOG("new OperandExpress failed!");
        return NULL;
    }
    auto res_type = binary_state[_value_type][rhs->value_type()];
    if (res_type == DOUBLE) {
        tmp->set_value(_double_value - rhs->get_double_value());
    } else if (res_type == UINT64) {
        tmp->set_value(_uint64_value - rhs->get_uint64_value()); 
    } else if (res_type == INT64) {
        tmp->set_value(_int64_value - rhs->get_int64_value()); 
    } else if (res_type == INVALID) {
        tmp->set_invalid_value();
    } else if (res_type == NOT_EXIST) {
        tmp->set_not_exist_value();
    }
    return ExpressInterfacePtr(tmp);
}

ExpressInterfacePtr OperandExpress::multiply(OperandExpress* rhs) {
    if (!_convert(rhs)) {
        CFATAL_LOG("bad convert!");
        return  NULL;
    }
    OperandExpress* tmp = new (std::nothrow) OperandExpress();
    if (tmp == NULL) {
        CFATAL_LOG("new OperandExpress failed!");
        return NULL;
    }
    auto res_type = binary_state[_value_type][rhs->value_type()];
    if (res_type == DOUBLE) {
        tmp->set_value(_double_value * rhs->get_double_value());
    } else if (res_type == UINT64) {
        tmp->set_value(_uint64_value * rhs->get_uint64_value()); 
    } else if (res_type == INT64) {
        tmp->set_value(_int64_value * rhs->get_int64_value()); 
    } else if (res_type == INVALID) {
        tmp->set_invalid_value();
    } else if (res_type == NOT_EXIST) {
        tmp->set_not_exist_value();
    }
    return ExpressInterfacePtr(tmp);
}

ExpressInterfacePtr OperandExpress::division(OperandExpress* rhs) {
    if (!_convert(rhs)) {
        CFATAL_LOG("bad convert!");
        return nullptr;
    }
    OperandExpress* tmp = new (std::nothrow) OperandExpress();
    if (tmp == NULL) {
        CFATAL_LOG("new OperandExpress failed!");
        return nullptr;
    }
    auto res_type = binary_state[_value_type][rhs->value_type()];
    if (res_type == DOUBLE) {
        if (rhs->get_double_value() < EPSINON) {
            tmp->set_invalid_value();
        } else {
            tmp->set_value(_double_value / rhs->get_double_value()); 
        }
    } else if (res_type == UINT64) {
        if (rhs->get_uint64_value() == 0) {
            tmp->set_invalid_value();
        } else {
            tmp->set_value(_uint64_value / rhs->get_uint64_value()); 
        }
    } else if (res_type == INT64) {
        if (rhs->get_int64_value() == 0) {
            tmp->set_invalid_value();
        } else {
            tmp->set_value(_int64_value / rhs->get_int64_value());
        }
    }
    return ExpressInterfacePtr(tmp);
}

ExpressInterfacePtr OperandExpress::greater(OperandExpress* rhs) {
    if (!_convert(rhs)) {
        CFATAL_LOG("bad convert!");
        return  NULL;
    }
    OperandExpress* tmp = new (std::nothrow) OperandExpress();
    if (tmp == NULL) {
        CFATAL_LOG("new OperandExpress failed!");
        return NULL;
    }
    auto res_type = binary_state[_value_type][rhs->value_type()];
    if (res_type == DOUBLE) {
        tmp->set_value(_double_value - rhs->get_double_value() > EPSINON);
    } else if (res_type == UINT64) {
        tmp->set_value(_uint64_value > rhs->get_uint64_value()); 
    } else if (res_type == INT64) {
        tmp->set_value(_int64_value > rhs->get_int64_value()); 
    } else if (res_type == INVALID) {
        tmp->set_invalid_value();
    } else if (res_type == NOT_EXIST) {
        tmp->set_not_exist_value();
    }
    return ExpressInterfacePtr(tmp);
}

ExpressInterfacePtr OperandExpress::equal_or_greater(OperandExpress* rhs) {
    if (!_convert(rhs)) {
        CFATAL_LOG("bad convert!");
        return  NULL;
    }
    OperandExpress* tmp = new (std::nothrow) OperandExpress();
    if (tmp == NULL) {
        CFATAL_LOG("new OperandExpress failed!");
        return NULL;
    }
    auto res_type = binary_state[_value_type][rhs->value_type()];
    if (res_type == DOUBLE) {
        tmp->set_value(_double_value - rhs->get_double_value() > -1.0 * EPSINON);
    } else if (res_type == UINT64) {
        tmp->set_value(_uint64_value >= rhs->get_uint64_value()); 
    } else if (res_type == INT64) {
        tmp->set_value(_int64_value >= rhs->get_int64_value()); 
    } else if (res_type == INVALID) {
        tmp->set_invalid_value();
    } else if (res_type == NOT_EXIST) {
        tmp->set_not_exist_value();
    }
    return ExpressInterfacePtr(tmp);
}

ExpressInterfacePtr OperandExpress::less(OperandExpress* rhs) {
    if (!_convert(rhs)) {
        CFATAL_LOG("bad convert!");
        return  NULL;
    }
    OperandExpress* tmp = new (std::nothrow) OperandExpress();
    if (tmp == NULL) {
        CFATAL_LOG("new OperandExpress failed!");
        return NULL;
    }
    auto res_type = binary_state[_value_type][rhs->value_type()];
    if (res_type == DOUBLE) {
        tmp->set_value(_double_value - rhs->get_double_value() < -1.0 * EPSINON);
    } else if (res_type == UINT64) {
        tmp->set_value(_uint64_value < rhs->get_uint64_value()); 
    } else if (res_type == INT64) {
        tmp->set_value(_int64_value < rhs->get_int64_value()); 
    } else if (res_type == INVALID) {
        tmp->set_invalid_value();
    } else if (res_type == NOT_EXIST) {
        tmp->set_not_exist_value();
    }
    return ExpressInterfacePtr(tmp);
}

ExpressInterfacePtr OperandExpress::equal_or_less(OperandExpress* rhs) {
    if (!_convert(rhs)) {
        CFATAL_LOG("bad convert!");
        return  NULL;
    }
    OperandExpress* tmp = new (std::nothrow) OperandExpress();
    if (tmp == NULL) {
        CFATAL_LOG("new OperandExpress failed!");
        return NULL;
    }
    auto res_type = binary_state[_value_type][rhs->value_type()];
    if (res_type == DOUBLE) {
        tmp->set_value(_double_value - rhs->get_double_value() < EPSINON);
    } else if (res_type == UINT64) {
        tmp->set_value(_uint64_value <= rhs->get_uint64_value()); 
    } else if (res_type == INT64) {
        tmp->set_value(_int64_value <= rhs->get_int64_value()); 
    } else if (res_type == INVALID) {
        tmp->set_invalid_value();
    } else if (res_type == NOT_EXIST) {
        tmp->set_not_exist_value();
    }
    return ExpressInterfacePtr(tmp);
}

ExpressInterfacePtr OperandExpress::equal(OperandExpress* rhs) {
    if (!_convert(rhs)) {
        CFATAL_LOG("bad convert!");
        return  NULL;
    }
    OperandExpress* tmp = new (std::nothrow) OperandExpress();
    if (tmp == NULL) {
        CFATAL_LOG("new OperandExpress failed!");
        return NULL;
    }
    auto res_type = binary_state[_value_type][rhs->value_type()];
    if (res_type == DOUBLE) {
        tmp->set_value(std::abs(_double_value - rhs->get_double_value()) < EPSINON);
    } else if (res_type == UINT64) {
        tmp->set_value(_uint64_value == rhs->get_uint64_value()); 
    } else if (res_type == INT64) {
        tmp->set_value(_int64_value == rhs->get_int64_value()); 
    } else if (res_type == INVALID) {
        tmp->set_invalid_value();
    } else if (res_type == NOT_EXIST) {
        tmp->set_not_exist_value();
    }
    return ExpressInterfacePtr(tmp);
}

ExpressInterfacePtr OperandExpress::not_equal(OperandExpress* rhs) {
    if (!_convert(rhs)) {
        CFATAL_LOG("bad convert!");
        return  NULL;
    }
    OperandExpress* tmp = new (std::nothrow) OperandExpress();
    if (tmp == NULL) {
        CFATAL_LOG("new OperandExpress failed!");
        return NULL;
    }
    auto res_type = binary_state[_value_type][rhs->value_type()];
    if (res_type == DOUBLE) {
        tmp->set_value(std::abs(_double_value - rhs->get_double_value()) > EPSINON);
    } else if (res_type == UINT64) {
        tmp->set_value(_uint64_value != rhs->get_uint64_value()); 
    } else if (res_type == INT64) {
        tmp->set_value(_int64_value != rhs->get_int64_value()); 
    } else if (res_type == INVALID) {
        tmp->set_invalid_value();
    } else if (res_type == NOT_EXIST) {
        tmp->set_not_exist_value();
    }
    return ExpressInterfacePtr(tmp);
}

ExpressInterfacePtr OperandExpress::logical_and(OperandExpress* rhs) {
    if (!_convert(rhs)) {
        CFATAL_LOG("bad convert!");
        return  NULL;
    }
    OperandExpress* tmp = new (std::nothrow) OperandExpress();
    if (tmp == NULL) {
        CFATAL_LOG("new OperandExpress failed!");
        return NULL;
    }
    if (_value_type == BOOL && rhs->value_type() == BOOL) {
        tmp->set_value(_bool_value && rhs->get_bool_value());
    } else if (_value_type == NOT_EXIST) {
        tmp->set_not_exist_value();
    } else {
        tmp->set_invalid_value();
    }
    return ExpressInterfacePtr(tmp);
}

ExpressInterfacePtr OperandExpress::logical_or(OperandExpress* rhs) {
    if (!_convert(rhs)) {
        CFATAL_LOG("bad convert!");
        return  NULL;
    }
    OperandExpress* tmp = new (std::nothrow) OperandExpress();
    if (tmp == NULL) {
        CFATAL_LOG("new OperandExpress failed!");
        return NULL;
    }
    if (_value_type == BOOL && rhs->value_type() == BOOL) {
        tmp->set_value(_bool_value || rhs->get_bool_value());
    } else if (_value_type == NOT_EXIST) {
        tmp->set_not_exist_value();
    } else {
        tmp->set_invalid_value();
    }
    return ExpressInterfacePtr(tmp);
}

double OperandExpress::_vector_cos(
        const Vector& a,
        const Vector& b) {
    assert(a.size() == b.size());
    double numerator = 0.0;
    double a_dot = 0.0;
    double b_dot = 0.0;
    for (uint32_t i = 0; i < a.size(); ++i) {
        numerator += a[i] * b[i];
        a_dot += a[i] * a[i];
        b_dot += b[i] * b[i];
    }
    return numerator / (sqrt(a_dot) * sqrt(b_dot));
}

ExpressInterfacePtr OperandExpress::vector_cos(OperandExpress* rhs) {
    if (!_convert(rhs)) {
        CFATAL_LOG("bad convert!");
        return  NULL;
    }
    OperandExpress* tmp = new (std::nothrow) OperandExpress();
    if (tmp == NULL) {
        CFATAL_LOG("new OperandExpress failed!");
        return NULL;
    }
    if (_value_type == VECTOR 
            && rhs->value_type() == VECTOR
            && _vector.size() == rhs->get_vector_value().size()) {
        tmp->set_value(_vector_cos(_vector, rhs->get_vector_value()));
    } else if (_value_type == NOT_EXIST) {
        tmp->set_not_exist_value();
    } else {
        tmp->set_invalid_value();
    }
    return ExpressInterfacePtr(tmp);
}

const std::vector<std::string> ExpSplitter::PARENTHESE_DICT = { "(", ")" };
const std::vector<std::string> ExpSplitter::UNARY_DICT = { "!", "ln" };
const std::vector<std::string> ExpSplitter::BINARY_DICT = {
        "cos",
        ">", "<", ">=", "<=", "==", "!=",
        "&&", "||",
        "+", "-", "*", "/"
};

bool ExpSplitter::_fetch(
        const std::unordered_set<std::string>& atoms,
        const char* start, 
        uint32_t* offset,
        ExpressInterfacePtr* out) {
    if (start == NULL || offset == NULL || out == NULL) {
        CFATAL_LOG("invalid input!");
        return false;
    }
    for (const auto& item : PARENTHESE_DICT) {
        if (strncmp(start, item.c_str(), item.size()) == 0) {
            out->reset(new (std::nothrow) ParentheseOperatorExpress(item));   
            *offset = item.size();
            return true;
        }
    }
    for (const auto& item : UNARY_DICT) {
        if (strncmp(start, item.c_str(), item.size()) == 0) {
            out->reset(new (std::nothrow) UnaryOperatorExpress(item));   
            *offset = item.size();
            return true;
        }
    }
    for (const auto& item : BINARY_DICT) {
        if (strncmp(start, item.c_str(), item.size()) == 0) {
            out->reset(new (std::nothrow) BinaryOperatorExpress(item));   
            *offset = item.size();
            return true;
        }
    }
    for (const auto& item : atoms) {
        if (strncmp(start, item.c_str(), item.size()) == 0) {
            out->reset(new (std::nothrow) OperandExpress(item));   
            *offset = item.size();
            return true;
        }
    }
    char cur = *(start + *offset);
    // not surport 10e5 or -1.3
    while (cur == '.' || (cur >= '0' && cur <= '9')) {
        ++(*offset);
        cur = *(start + *offset);
    }
    std::string num_str(start, *offset);
    OperandExpress* tmp = new (std::nothrow) ConstantExpress(num_str);
    tmp->set_value(num_str);
    if (tmp->value_type() == OperandExpress::INVALID) {
        CFATAL_LOG("invalid num str[%s]", num_str.c_str());
        return false;
    }
    out->reset(tmp);
    return true;
}

void ConstantExpress::set_value(std::string value) {
    // don't need bool 
    // can't in this function
    //
    // if (value == "true") {
    //     _value_type = BOOL;
    //     _bool_value = true;
    //     return;
    // }
    // if (value == "false") {
    //     _value_type = BOOL; 
    //     _bool_value = false;
    //     return;
    // }
    if (value.find(".") == std::string::npos) {
        uint64_t num = 0UL;
        try {
            num = boost::lexical_cast<uint64_t>(value);
        } catch (const boost::bad_lexical_cast& e) {
            CFATAL_LOG("bad lexical_cast [%s]", e.what());
            _value_type = INVALID;
            return;
        }
        _value_type = UINT64; 
        _uint64_value = num;
        return;
    }
    double num = 0.0;
    try {
        num = boost::lexical_cast<double>(value);
    } catch (const boost::bad_lexical_cast& e) {
        CFATAL_LOG("bad lexical_cast [%s]", e.what());
        _value_type = INVALID;
        return;
    }
    _value_type = DOUBLE;
    _double_value = num;
    return;
}

bool UnaryOperatorExpress::compute(
        std::stack<ExpressInterfacePtr>* stack) {
    if (stack == NULL) {
        CFATAL_LOG("invalid stack ptr input");
        return false;
    }
    if (stack->size() < 1) {
        CFATAL_LOG("unary operator needs two operand!");
        return false;
    }
    ExpressInterfacePtr lhs = stack->top();
    stack->pop();
    OperandExpress* l = dynamic_cast<OperandExpress*>(lhs.get());
    if (l == NULL) {
        CFATAL_LOG("lhs can't dynamic_cast to OperandExpress!");
        return false;
    }
    ExpressInterfacePtr res;
    if (this->value() == "!") {
        res = l->negation();
    } else if (this->value() == "ln") {
        res = l->ln();
    } else {
        CFATAL_LOG("invalid operator[%s]", this->value().c_str());
        return false;
    }
    stack->push(res);
    return true;
}

bool BinaryOperatorExpress::compute(
        std::stack<ExpressInterfacePtr>* stack) {
    if (stack == NULL) {
        CFATAL_LOG("invalid stack ptr input");
        return false;
    }
    if (stack->size() < 2) {
        CFATAL_LOG("binary operator needs two operand!");
        return false;
    }
    ExpressInterfacePtr rhs = stack->top(); 
    stack->pop();  
    ExpressInterfacePtr lhs = stack->top();
    stack->pop();
    OperandExpress* l = dynamic_cast<OperandExpress*>(lhs.get());
    OperandExpress* r = dynamic_cast<OperandExpress*>(rhs.get());
    if (l == NULL || r == NULL) {
        CFATAL_LOG("lhs or rhs can't dynamic_cast to OperandExpress!");
        return false;
    }
    ExpressInterfacePtr res;
    if (this->value() == "+") {
        res = l->add(r);
    } else if (this->value() == "-") {
        res = l->minus(r);
    } else if (this->value() == "*") {
        res = l->multiply(r);
    } else if (this->value() == "/") {
        res = l->division(r);
    } else if (this->value() == ">") {
        res = l->greater(r);
    } else if (this->value() == ">=") {
        res = l->equal_or_greater(r);
    } else if (this->value() == "<") {
        res = l->less(r);
    } else if (this->value() == "<=") {
        res = l->equal_or_less(r);
    } else if (this->value() == "==") {
        res = l->equal(r);
    } else if (this->value() == "!=") {
        res = l->not_equal(r);
    } else if (this->value() == "&&") {
        res = l->logical_and(r);
    } else if (this->value() == "||") {
        res = l->logical_or(r);
    } else if (this->value() == "cos") {
        res = l->vector_cos(r);
    } else {
        CFATAL_LOG("invalid operator[%s]", this->value().c_str());
        return false;
    }
    if (res == nullptr) {
        CFATAL_LOG("ExpressInterfacePtr res is nullptr value %s", this->value());
        return false;
    }
    stack->push(res);
    return true;
}
        

bool ExpSplitter::split(
        const std::string& exp_str, 
        const std::unordered_set<std::string>& atoms, 
        Express* exp) {
    if (exp == NULL) {
        CFATAL_LOG("invalid input");
        return false;
    }
    exp->clear();
    const char* start = exp_str.c_str();
    uint32_t i = 0;
    while (i < exp_str.size()) {
        if (exp_str[i] == ' ' || exp_str[i] == ',') {
            i++;
            continue;
        }
        uint32_t offset = 0U;
        ExpressInterfacePtr item(NULL);
        if (!_fetch(atoms, start + i, &offset, &item)) {
            CFATAL_LOG("fetch item failed!");
            return false;
        }
        i += offset;
        exp->push_back(item);
    }
    return true;
}

bool OperandExpress::get_double_value(double* res) const {
    if (res == NULL || _value_type != DOUBLE) {
        return false;
    }
    *res = _double_value;
    return true;
}

bool OperandExpress::get_uint64_value(uint64_t* res) const {
    if (res == NULL || _value_type != UINT64) {
        return false;
    }
    *res = _uint64_value;
    return true;
}

bool OperandExpress::get_int64_value(int64_t* res) const {
    if (res == NULL || _value_type != INT64) {
        return false;
    }
    *res = _int64_value;
    return true;
}

bool OperandExpress::get_bool_value(bool* res) const {
    if (res == NULL || _value_type != BOOL) {
        return false;
    }
    *res = _bool_value;
    return true;
}

bool Exp::append(
        const std::string& equation,
        const std::vector<std::string>& atoms) {
    // uniq equations
    if (_equations_set.find(equation) != _equations_set.end()) {
        return true;
    }
    _equations_set.insert(equation);
    // uniq atoms
    _atoms_set.insert(atoms.begin(), atoms.end());
    _inner_atoms_set.insert(atoms.begin(), atoms.end());

    // split by =, get the calculation method 
    std::string exp_str = "";
    uint64_t exp_sign = 0UL;
    if (!_parse_equation(
            equation, 
            &_equation_map, 
            &exp_str, 
            &exp_sign,
            &_inner_atoms_set,
            &_unknown_set)) {
        CWARNING_LOG("parse_equation failed! equation[%s]", equation.c_str());
        return false;
    }
    Express infix_exp;
    Express postfix_exp;
    if (!ExpSplitter::split(exp_str, _inner_atoms_set, &infix_exp)) {
        CFATAL_LOG("split exp[%s] failed!", exp_str.c_str());
        return false;
    }
    // Specialization for carespace
     _carespace_add_logic_and(&infix_exp); 

    if (!_infix_to_postfix(infix_exp, &postfix_exp)) {
        CFATAL_LOG("infix exp to postfix failed!");
        return false;
    }
    if (!_check_exps(postfix_exp, _equation_map, _collector)) {
        CFATAL_LOG("check exp failed! please check[%s]", equation.c_str());
        return false;
    }
    _postfix_exps.emplace_back(exp_sign, postfix_exp);
    CWARNING_LOG("exp append[%s] success", equation.c_str());
    return true;
}

bool Exp::init(
        const std::vector<std::string>& equations,
        const std::vector<std::string>& atoms) {
    for (const auto& equation : equations) {
        if (!append(equation, atoms)) {
            CWARNING_LOG("append exp[%s] failed!", equation.c_str());
            return false;
        }
        // uniq the equation 
    }
    return true;
}

bool Exp::_carespace_add_logic_and(Express* infix_exp) {
    if (infix_exp == NULL) {
        CFATAL_LOG("invalid input!");
        return false;
    }
    if (infix_exp->size() < 2) {
        return true;
    }
    auto last = infix_exp->begin();
    bool last_is_carespace = _is_carespace(*last);
    for (auto it = last + 1; it != infix_exp->end(); ++it) {
        bool cur_is_carespace = _is_carespace(*it);
        if (last_is_carespace && cur_is_carespace) {
            ExpressInterfacePtr and_ptr(new (std::nothrow) BinaryOperatorExpress("&&"));
            if (!and_ptr) {
                CFATAL_LOG("new ExpressInterfacePtr failed!");
                return false;
            }
            it = infix_exp->insert(it, and_ptr);
            ++it;
        }
        last_is_carespace = cur_is_carespace;
    }
    return true;
}

bool Exp::_is_carespace(ExpressInterfacePtr ptr) {
    if (!ptr) {
        CFATAL_LOG("invalid input!");
        return false;
    }
    const auto& str = ptr->value();
    if (str.size() == 0) {
        return false;
    }
    return str[0] == '[' && str[str.size() - 1] == ']';
}

bool Exp::_parse_equation(
        const std::string& equation, 
        std::unordered_map<uint64_t, uint64_t>* equation_map,
        std::string* exp,
        uint64_t* exp_sign,
        std::unordered_set<std::string>* atoms,
        std::unordered_set<std::string>* unknown) {
    if (equation_map == NULL || exp == NULL || exp_sign == NULL) {
        CFATAL_LOG("invalid input!");
        return false;
    }
    std::vector<std::string> value;
    int32_t start_loc = 0;
    CharState char_state = {NORMAL, -1};
    StrFSM fsm;
    for (const char& c : equation) {
        fsm.transfer(c, &char_state);
        if (char_state.state == NORMAL && c == '=') {
            value.push_back(std::string(equation, start_loc, char_state.loc - start_loc));
            start_loc = char_state.loc + 1;
        }
    }
    if (char_state.state != NORMAL) {
        CFATAL_LOG("char state doesn't recover normal equantion[%s]"
                ", please check \"\'[]\\ pair", equation.c_str());
        return false;
    }
    value.push_back(std::string(equation, start_loc));
    *exp = _trim(value[value.size() - 1]);
    anti::baselib::SignUtil::create_sign_md64(*exp, exp_sign);
    // map entire exp
    if (value.size() > 1) {
        uint64_t entire_sign = 0UL;
        anti::baselib::SignUtil::create_sign_md64(_trim(equation), &entire_sign);
        if (equation_map->find(entire_sign) != equation_map->end()) {
            CWARNING_LOG("exp[%s] has been declear, please check!", _trim(equation).c_str());
            return false;
        }
        equation_map->emplace(entire_sign, *exp_sign);
    }
    // map other exp
    for (uint32_t i = 0; i < value.size() - 1; ++i) {
        uint64_t key_sign = 0UL;
        std::string lhs_str = _trim(value[i]);
        anti::baselib::SignUtil::create_sign_md64(lhs_str, &key_sign);
        if (equation_map->find(key_sign) != equation_map->end()) {
            CWARNING_LOG("left hand side value[%s] has been declear, please check!",
                    lhs_str.c_str());
            return false;
        }
        atoms->insert(lhs_str);
        unknown->insert(lhs_str);
        equation_map->emplace(key_sign, *exp_sign);
    }
    return true; 
}

bool Exp::_check_exps(
        const Express& postfix_exp,
        const std::unordered_map<uint64_t, uint64_t>& equation_map,
        const AtomsMap& collector) {
    ExpressInterfacePtr ptr = _calculate(postfix_exp, equation_map, collector);
    if (!ptr) {
        CFATAL_LOG("check postfix_exp failed!");
        return false;
    }
    return true;
}

template<typename T>
OperandExpress* Exp::_fetch_operand_ptr(const std::string& exp, T* res) const {
    if (res == NULL) {
        CFATAL_LOG("res is a NULL ptr");
        return NULL;
    }
    uint64_t sign = 0UL;
    anti::baselib::SignUtil::create_sign_md64(_trim(exp), &sign);
    if (_equation_map.find(sign) != _equation_map.end()) {
        sign = _equation_map.at(sign);
    }
    if (_collector.find(sign) == _collector.end()) {
        CWARNING_LOG("can't find exp[%s], please check your init func", exp.c_str());
        return NULL;
    }
    auto ptr = _collector.at(sign);
    return dynamic_cast<OperandExpress*>(ptr.get());
}

bool Exp::calculate(const std::string& exp, double* res) const {
    OperandExpress* res_operand_ptr = _fetch_operand_ptr(exp, res); 
    if (res_operand_ptr == NULL) {
        CFATAL_LOG("invalid res, can't be a operand");
        return false;
    }
    if (res_operand_ptr->value_type() != OperandExpress::DOUBLE) {
        return false;
    }
    *res = res_operand_ptr->get_double_value();
    return true;
}

bool Exp::calculate(const std::string& exp, uint64_t* res) const {
    OperandExpress* res_operand_ptr = _fetch_operand_ptr(exp, res); 
    if (res_operand_ptr == NULL) {
        CFATAL_LOG("invalid res, can't be a operand");
        return false;
    }
    if (res_operand_ptr->value_type() != OperandExpress::UINT64) {
        return false;
    }
    *res = res_operand_ptr->get_uint64_value();
    return true;
}

bool Exp::calculate(const std::string& exp, int64_t* res) const {
    OperandExpress* res_operand_ptr = _fetch_operand_ptr(exp, res); 
    if (res_operand_ptr == NULL) {
        CFATAL_LOG("invalid res, can't be a operand");
        return false;
    }
    if (res_operand_ptr->value_type() != OperandExpress::INT64) {
        return false;
    }
    *res = res_operand_ptr->get_int64_value();
    return true;
}

bool Exp::calculate(const std::string& exp, bool* res) const {
    OperandExpress* res_operand_ptr = _fetch_operand_ptr(exp, res); 
    if (res_operand_ptr == NULL) {
        CFATAL_LOG("invalid res, can't be a operand");
        return false;
    }
    if (res_operand_ptr->value_type() != OperandExpress::BOOL) {
        return false;
    }
    *res = res_operand_ptr->get_bool_value();
    return true;
}

void Exp::reset(
        const std::vector<std::pair<std::string, double>>& double_algebraic_value,
        const std::vector<std::pair<std::string, uint64_t>>& uint64_algebraic_value,
        const std::vector<std::pair<std::string, int64_t>>& int64_algebraic_value,
        const std::vector<std::pair<std::string, bool>>& bool_algebraic_value,
        const std::vector<std::pair<std::string, Vector>>& vector_algebraic_value) {
    _clear_algebraic_expression();
    _reset_algebraic_expression(double_algebraic_value, &_collector);
    _reset_algebraic_expression(uint64_algebraic_value, &_collector);
    _reset_algebraic_expression(int64_algebraic_value, &_collector);
    _reset_algebraic_expression(bool_algebraic_value, &_collector);
    _reset_algebraic_expression(vector_algebraic_value, &_collector);
    _calculate_exps(_equation_map, &_postfix_exps, &_collector); 
    return;
}

void Exp::_calculate_exps(
        const std::unordered_map<uint64_t, uint64_t> equation_map, 
        ExpressGroup* exps, 
        AtomsMap* collector) {
    if (exps == NULL) {
        CFATAL_LOG("invalid input!");
        return;
    }

    for (uint32_t i = 0; i < exps->size(); ++i) {
        const auto& exp = exps->at(i);
        uint64_t result_sign = exp.first;
        if (collector->find(result_sign) != collector->end()) {
            continue;
        }
        ExpressInterfacePtr result = _calculate(exp.second, equation_map, *collector);
        if (!result) {
            CWARNING_LOG("calculate exp failed!");
            continue;
        }

        // collector save result for second order calculate
        collector->emplace(result_sign, result);
    }
    return;
}

int Exp::_priority(ExpressInterfacePtr op) const {
    if (!op) {
        CFATAL_LOG("invalid input");
        return -1;
    }
    const auto& op_str = op->value();
    return PRIORITY.find(op_str) != PRIORITY.end() ? PRIORITY.at(op_str) : -1;
}

bool Exp::_infix_to_postfix(
        const Express& infix_exp,
        Express* postfix_exp) {
    if (postfix_exp == NULL) {
        CFATAL_LOG("invalid postfix_exp!");
        return false;
    }
    postfix_exp->clear();
    std::stack<ExpressInterfacePtr> stack;
    for (const ExpressInterfacePtr& op : infix_exp) {
        // if op is a operand, push into postfix_exp
        if (op->type() == ExpressInterface::OPERAND) {
            postfix_exp->emplace_back(op);
            continue;
        }
        // else op is a operator
        // judge "(" ")" or other
        std::string opera = op->value();
        if (opera == "(") {
            stack.push(op);
            continue;
        } 
        if (opera == ")") {
            while (!stack.empty()) {
                ExpressInterfacePtr opptr = stack.top();
                stack.pop();
                if (opptr->value() == "(") {
                    break;
                }
                postfix_exp->emplace_back(opptr);
            }
            continue;
        }
        // other operator, need to 
        // Order of Precedence For Comparisons
        while (!stack.empty()) {
            ExpressInterfacePtr top_op = stack.top();
            if (top_op->value() == "(" 
                    || _priority(op) > _priority(top_op)) {
                break;
            }
            postfix_exp->emplace_back(top_op);
            stack.pop();
        }
        stack.push(op);
    }
    while (!stack.empty()) {
        ExpressInterfacePtr op = stack.top();
        postfix_exp->emplace_back(op);
        stack.pop();
    }
    return true;
}

ExpressInterfacePtr Exp::_calculate(
        const Express& postfix_exp,
        const std::unordered_map<uint64_t, uint64_t>& equation_map,
        const AtomsMap& collector) {
    std::stack<ExpressInterfacePtr> stack;
    ExpressInterfacePtr res;
    for (const ExpressInterfacePtr& p : postfix_exp) {
        // operand: push into stack 
        if (p->type() == ExpressInterface::OPERAND) {
            // replace second order result
            auto sign = p->sign();
            if (equation_map.find(sign) != equation_map.end()) {
                sign = equation_map.at(sign);
            }
            // find value in collector
            stack.push(collector.find(sign) != collector.end() ? 
                    collector.at(sign) : p);
            continue;
        } 
        // operator, computing with stack
        if (!p->compute(&stack)) {
            CWARNING_LOG("operator[%s] computing failed!",
                    p->value().c_str());
            return nullptr;
        }
    }
    if (stack.size() != 1) {
        CWARNING_LOG("stack not a single value after calculate!");
        return nullptr;
    }
    return stack.top();
}

} // common_lib
} // themis
} // anti

/* vim: set ts=4 sw=4: */

