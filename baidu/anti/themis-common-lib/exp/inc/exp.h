// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: ch<PERSON><PERSON><PERSON><PERSON>(ch<PERSON><PERSON><PERSON><PERSON>@baidu.com)
// 
// @File: exp.h
// @Last modified: 2018-04-02 10:57:08
// @Brief: 

#ifndef BAIDU_ANTI_THEMIS_COMMON_LIB_EXP_INC_EXP_H
#define BAIDU_ANTI_THEMIS_COMMON_LIB_EXP_INC_EXP_H

#include "exp_impl.h"
#include <vector>
#include <unordered_set>
#include <boost/lexical_cast.hpp>
#include <stack>
#include <unordered_map>
#include <memory>
#include <sign_util.h>
#include <com_log.h>

namespace anti {
namespace themis {
namespace common_lib {

// Usage :
//     --------------------------------------------------
//     calculate : fea(888) = fea(1) + fea(2)
//     calculate : fea(999) = (fea(1) * 0.5) + fea(2)
//     --------------------------------------------------
//     init stage:
//     1. get exps in conf, push exp into exps:
//          std::vector<std::string> exps;
//          exps.push_back("fea(888) = fea(1) + fea(2)");
//          exps.push_back("fea(999) = (fea(1) * 0.5) + fea(2)");
//     2. get atoms in conf, which can get value in every round 
//          std::vector<std::string> atoms;
//          atoms.push_back("fea(1)");
//          atoms.push_back("fea(2)");
//     3. new a exp and init:
//          new Exp exp();
//          exp.init(exps, atoms);
//     --------------------------------------------------
//     every round:
//     1. get atoms value:
//          std::vector<std::pair<std::string, T>>& map = {
//              {"fea(1)", 0.5},
//              {"fea(2)", 1.0}
//          }
//     2. reset exp atoms, and every exp will be calculate:
//         exp.reset(map);
//     3. get value you want, The following expressions can all get value:
//         double res = 0.0;
//         calculate("fea(888)", res);
//         calculate("fea(1) + fea(2)", res);
//         calculate("fea(888) = fea(1) + fea(2)", res);
//         calculate("fea(999)", res);
//     --------------------------------------------------
//  Notice:
//     1. Compute backward compatibility
//          double arithmetic any = double
//          uint64 arithmetic int64 = int64
//     2. carespace like [a][b] add logic and middle
class Exp {
public:
    Exp() {}
    ~Exp() {}
    // @brief : parse exps by atoms
    // @param : exps : [IN] input exps, can has any number of equal
    //          eg : fea(999) = fea(888) = fea (2) + fea(3)
    //          last part will be calculate
    //          every part can get value
    // @param : atoms : [IN] algebraic atoms in an expression 
    //          which will can be get value,
    //          exps split by atoms
    // @return : false for exp error, true for success
    bool init(
            const std::vector<std::string>& exps,
            const std::vector<std::string>& atoms); 
    // @brief : reset value of algebraic atoms every round
    // @param : algebraic_value: [IN] 4 type of value 
    //          compute backward compatibility
    //
    bool append(
            const std::string& equation,
            const std::vector<std::string>& atoms);
    void reset(
            const std::vector<std::pair<std::string, double>>& double_algebraic_value,
            const std::vector<std::pair<std::string, uint64_t>>& uint64_algebraic_value,
            const std::vector<std::pair<std::string, int64_t>>& int64_algebraic_value,
            const std::vector<std::pair<std::string, bool>>& bool_algebraic_value,
            const std::vector<std::pair<std::string, Vector>>& vector_algebraic_value);
    // @brief : reset value of algebraic atoms every round
    // @param : algebraic_value: [IN] support double uint64_t int64_t bool 
    //          compute backward compatibility
    template<typename T>
    void reset(
            const std::vector<std::pair<std::string, T>>& map) {
        _clear_algebraic_expression();
        _reset_algebraic_expression(map, &_collector);
        // _setup(_collector, &_postfix_exps);
        _calculate_exps(_equation_map, &_postfix_exps, &_collector); 
        return;
    }
    // @brief : get result of exp
    // @param : exp [IN] can be any part of the equation
    //          eg : f(1) = f(2) = f(3) + f(4)
    //               "f(1)", "f(2)", "f(3) + f(4)", "f(1) = f(2) = f(3) + f(4)"
    //               can get the same value
    // @param : res [OUT] 
    // @return : false for result type error or some atoms not exist,
    //           true for success
    bool calculate(const std::string& exp, double* res) const;
    bool calculate(const std::string& exp, int64_t* res) const;
    bool calculate(const std::string& exp, uint64_t* res) const;
    bool calculate(const std::string& exp, bool* res) const;

    const std::unordered_set<std::string>& atoms() const {
        return _atoms_set;
    }

    const std::unordered_set<std::string>& exps() const {
        return _equations_set;
    }

    const std::unordered_set<std::string>& unknown() const {
        return _unknown_set;
    }

    const std::unordered_map<std::string, int> PRIORITY = {
        {"(", 9}, {")", 9},
        {"!", 8}, {"ln", 8}, {"cos", 8},
        {"**", 7},
        {"*", 6}, {"/", 6}, {"%", 6},
        {"+", 5}, {"-", 5},
        {"<", 4}, {">", 4}, {"<=", 4}, {">=", 4},
        {"==", 3}, {"!=", 3},
        {"&&", 2},
        {"||", 1}
    };

private:
    typedef std::vector<std::pair<uint64_t, Express>> ExpressGroup;
    typedef std::unordered_map<uint64_t, ExpressInterfacePtr> AtomsMap;

    bool _check_exps(
            const Express& postfix_exp,
            const std::unordered_map<uint64_t, uint64_t>& equation_map,
            const AtomsMap& collector);
    // void _setup(
    //         const AtomsMap& collector,
    //         ExpressGroup* exps_ptr);
    void _calculate_exps(
            const std::unordered_map<uint64_t, uint64_t> equation_map,
            ExpressGroup* exps,
            AtomsMap* collector);
    void _clear_algebraic_expression() {
        _collector.clear();
        return;
    }
    bool _carespace_add_logic_and(Express* infix_exp);
    bool _is_carespace(ExpressInterfacePtr ptr);

    bool _parse_equation(
            const std::string& equation, 
            std::unordered_map<uint64_t, uint64_t>* equation_map,
            std::string* exp,
            uint64_t* exp_sign,
            std::unordered_set<std::string>* inner_atoms,
            std::unordered_set<std::string>* unknown);
    std::string _trim(std::string str) const {
        str.erase(0, str.find_first_not_of(" "));  
        str.erase(str.find_last_not_of(" ") + 1);  
        return str;  
    }
    template<typename T>
    void _reset_algebraic_expression(
            const std::vector<std::pair<std::string, T>>& map,
            AtomsMap* collector) {
        for (const auto& pair : map) {
            OperandExpress* ptr = new (std::nothrow) OperandExpress();
            uint64_t sign = 0;
            anti::baselib::SignUtil::create_sign_md64(pair.first, &sign);
            ptr->set_sign(sign);
            ptr->set_value(pair.second);
            ExpressInterfacePtr tmp_ptr(ptr);
            if (collector->find(sign) != collector->end()) {
                CWARNING_LOG("algebraic expression[%s] sign[%lu] has beed defined",
                        pair.first.c_str(), sign);
                continue;
            }
            collector->emplace(sign, tmp_ptr);
        }
        return;
    }

    template<typename T>
    OperandExpress* _fetch_operand_ptr(
            const std::string& exp, 
            T* res) const;

    int _priority(ExpressInterfacePtr p) const;
    bool _infix_to_postfix(
            const Express& infix_exp,
            Express* postfix_exp);
    ExpressInterfacePtr _calculate(
            const Express& postfix_exp,
            const std::unordered_map<uint64_t, uint64_t>& equation_map,
            const AtomsMap& collector); 
    ExpressInterfacePtr _calculate_once(
            ExpressInterfacePtr p, 
            ExpressInterfacePtr lhs);
    ExpressInterfacePtr _calculate_once(
            ExpressInterfacePtr p, 
            ExpressInterfacePtr lhs, 
            ExpressInterfacePtr rhs);
    // for interface
    std::unordered_set<std::string> _equations_set;
    // original atoms
    // eg. fea(123) = fea(1) + fea(2)
    //     fea(123) not in _atoms
    std::unordered_set<std::string> _atoms_set;
    // original atoms add recursive atoms
    // eg. fea(123) = fea(1) + fea(2)
    //     fea(123) in _inner_atoms
    std::unordered_set<std::string> _inner_atoms_set;
    std::unordered_set<std::string> _unknown_set;
    AtomsMap _collector;
    ExpressGroup _postfix_exps;
    std::unordered_map<uint64_t, uint64_t> _equation_map;
};

} // common_lib
} // themis
} // anti

#endif // BAIDU_ANTI_THEMIS_COMMON_LIB_EXP_INC_EXP_H

/* vim: set ts=4 sw=4: */

