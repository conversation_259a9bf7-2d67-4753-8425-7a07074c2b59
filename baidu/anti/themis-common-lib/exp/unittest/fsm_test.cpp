// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: ch<PERSON><PERSON><PERSON><PERSON>(chen<PERSON><PERSON><EMAIL>)
// 
// @File: fsm.cpp
// @Last modified: 2018-03-19 20:02:12
// @Brief: 

#include <gtest/gtest.h>
#include "fsm.h"

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}

namespace anti {
namespace themis {
namespace common_lib {

class StrFSMTestSuite : public ::testing::Test {
public:
    virtual void SetUp() {}
    virtual void TearDonw() {}
private:
    StrFSM _obj;
};

TEST_F(StrFSMTestSuite, trans_test) {
    std::vector<char> input = {
            'a', '\'', 'a', '\'', 
            '"', 'a', '"', 
            '[', 'a', ']',
            '"', '[', 'a', '=', '"',
            '[', '\'', 'a', '=', '\'', ']',
            '"', '\\', '"', 'a', '"'
    };
    std::vector<State> check_res = {
        NORMAL, IN_SINGLE_QUOTE, IN_SINGLE_QUOTE, NORMAL,  
        IN_DOUBLE_QUOTE, IN_DOUBLE_QUOTE, NORMAL,
        IN_SQUARE_BRACKETS, IN_SQUARE_BRACKETS, NORMAL,
        IN_DOUBLE_QUOTE, IN_DOUBLE_QUOTE, IN_DOUBLE_QUOTE, IN_DOUBLE_QUOTE, NORMAL,
        IN_SQUARE_BRACKETS, IN_SINGLE_QUOTE, IN_SINGLE_QUOTE, 
        IN_SINGLE_QUOTE, IN_SQUARE_BRACKETS, NORMAL, 
        IN_DOUBLE_QUOTE, ESCAPE, IN_DOUBLE_QUOTE, IN_DOUBLE_QUOTE, NORMAL
    };
    CharState char_state = {NORMAL, -1};
    for (uint32_t i = 0; i < input.size(); ++i) {
        _obj.transfer(input[i], &char_state);  
        EXPECT_EQ(check_res[i], char_state.state);
        EXPECT_EQ(i, char_state.loc);
    }
}

} // common_lib
} // themis
} // anti

/* vim: set ts=4 sw=4: */

