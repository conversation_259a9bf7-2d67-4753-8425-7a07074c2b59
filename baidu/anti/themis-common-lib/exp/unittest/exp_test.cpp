// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @Author: ch<PERSON><PERSON><PERSON><PERSON>(chen<PERSON><PERSON><EMAIL>)
// 
// @File: test_exp.cpp
// @Last modified: 2018-04-02 11:02:40
// @Brief: 

#include <gtest/gtest.h>
#include <boost/lexical_cast.hpp>
#include "exp.h"

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}

namespace anti {
namespace themis {
namespace common_lib {

class ExpTestSuite : public ::testing::Test {
public:
    virtual void SetUp() {}
    virtual void TearDonw() {}
private:
    Exp _obj;
};

void check(ExpressInterfacePtr ptr, bool value) {
    OperandExpress* num = dynamic_cast<OperandExpress*>(ptr.get());
    EXPECT_EQ(value, num->get_bool_value());
    EXPECT_EQ(OperandExpress::BOOL, num->value_type());
}

void check(ExpressInterfacePtr ptr, double value) {
    OperandExpress* num = dynamic_cast<OperandExpress*>(ptr.get()); 
    EXPECT_DOUBLE_EQ(value, num->get_double_value());
    EXPECT_EQ(OperandExpress::DOUBLE, num->value_type());
}

void check(ExpressInterfacePtr ptr, int64_t value) {
    OperandExpress* num = dynamic_cast<OperandExpress*>(ptr.get()); 
    EXPECT_DOUBLE_EQ(value, num->get_int64_value());
    EXPECT_EQ(OperandExpress::INT64, num->value_type());
}

void check(ExpressInterfacePtr ptr, uint64_t value) {
    OperandExpress* num = dynamic_cast<OperandExpress*>(ptr.get()); 
    EXPECT_DOUBLE_EQ(value, num->get_uint64_value());
    EXPECT_EQ(OperandExpress::UINT64, num->value_type());
}

void check(ExpressInterfacePtr ptr, OperandExpress::ValueType type) {
    OperandExpress* num = dynamic_cast<OperandExpress*>(ptr.get()); 
    EXPECT_EQ(type, num->value_type());
}

TEST_F(ExpTestSuite, operand_negation_case) {
    OperandExpress num1;
    bool tmp = true;
    num1.set_value(tmp);
    ExpressInterfacePtr tmp_ptr = num1.negation();
    check(tmp_ptr, false);
    // invalid
    double d = 1.0;
    num1.set_value(d);
    tmp_ptr = num1.negation(); 
    check(tmp_ptr, OperandExpress::INVALID);
    uint64_t i = 1UL;
    num1.set_value(i);
    tmp_ptr = num1.negation();
    check(tmp_ptr, OperandExpress::INVALID);
    int64_t j = 1UL;
    num1.set_value(j);
    tmp_ptr = num1.negation();
    check(tmp_ptr, OperandExpress::INVALID);
}

TEST_F(ExpTestSuite, operand_add_case) {
    OperandExpress num1;
    OperandExpress num2;
    double d1 = 1.5;
    double d2 = 2.3;
    num1.set_value(d1);
    num2.set_value(d2);
    auto res = num1.add(&num2);
    check(res, 1.5 + 2.3);

    uint64_t u1 = 1UL;
    uint64_t u2 = 3UL;
    uint64_t u3 = 4UL;
    num1.set_value(d1);
    num2.set_value(u1);
    res = num1.add(&num2);
    check(res, 1.5 + 1.0); 

    num1.set_value(u1);
    num2.set_value(d2);
    res = num1.add(&num2);
    check(res, 1.0 + 2.3);

    num1.set_value(u1);
    num2.set_value(u2);
    res = num1.add(&num2);
    check(res, u3);

    int64_t i1 = 1L;
    int64_t i2 = 3L;
    int64_t i3 = 4L;
    num1.set_value(d1);
    num2.set_value(i1);
    res = num1.add(&num2);
    check(res, 1.5 + 1.0); 

    num1.set_value(i1);
    num2.set_value(d2);
    res = num1.add(&num2);
    check(res, 1.0 + 2.3);

    num1.set_value(i1);
    num2.set_value(i2);
    res = num1.add(&num2);
    check(res, i3);

    num1.set_value(i1);
    num2.set_value(u2);
    res = num1.add(&num2);
    check(res, i3);

    num1.set_value(u1);
    num2.set_value(i2);
    res = num1.add(&num2);
    check(res, i3);
}

TEST_F(ExpTestSuite, operand_minus_case) {
    OperandExpress num1;
    OperandExpress num2;
    double d1 = 3.0;
    double d2 = 1.0;
    double d3 = d1 - d2;
    num1.set_value(d1);
    num2.set_value(d2);
    auto res = num1.minus(&num2);
    check(res, d3);

    uint64_t u1 = 3UL;
    uint64_t u2 = 1UL;
    uint64_t u3 = u1 - u2;
    num1.set_value(d1);
    num2.set_value(u2);
    res = num1.minus(&num2);
    check(res, d3); 

    num1.set_value(u1);
    num2.set_value(d2);
    res = num1.minus(&num2);
    check(res, d3);

    num1.set_value(u1);
    num2.set_value(u2);
    res = num1.minus(&num2);
    check(res, u3);

    int64_t i1 = 3L;
    int64_t i2 = 1L;
    int64_t i3 = i1 - i2;
    num1.set_value(d1);
    num2.set_value(i2);
    res = num1.minus(&num2);
    check(res, d3); 

    num1.set_value(i1);
    num2.set_value(d2);
    res = num1.minus(&num2);
    check(res, d3);

    num1.set_value(i1);
    num2.set_value(i2);
    res = num1.minus(&num2);
    check(res, i3);

    num1.set_value(i1);
    num2.set_value(u2);
    res = num1.minus(&num2);
    check(res, i3);

    num1.set_value(u1);
    num2.set_value(i2);
    res = num1.minus(&num2);
    check(res, i3);
}

TEST_F(ExpTestSuite, operand_multiply_case) {
    OperandExpress num1;
    OperandExpress num2;
    double d1 = 3.0;
    double d2 = 1.0;
    double d3 = d1 * d2;
    num1.set_value(d1);
    num2.set_value(d2);
    auto res = num1.multiply(&num2);
    check(res, d3);

    uint64_t u1 = 3UL;
    uint64_t u2 = 1UL;
    uint64_t u3 = u1 * u2;
    num1.set_value(d1);
    num2.set_value(u2);
    res = num1.multiply(&num2);
    check(res, d3); 

    num1.set_value(u1);
    num2.set_value(d2);
    res = num1.multiply(&num2);
    check(res, d3);

    num1.set_value(u1);
    num2.set_value(u2);
    res = num1.multiply(&num2);
    check(res, u3);

    int64_t i1 = 3L;
    int64_t i2 = 1L;
    int64_t i3 = i1 * i2;
    num1.set_value(d1);
    num2.set_value(i2);
    res = num1.multiply(&num2);
    check(res, d3); 

    num1.set_value(i1);
    num2.set_value(d2);
    res = num1.multiply(&num2);
    check(res, d3);

    num1.set_value(i1);
    num2.set_value(i2);
    res = num1.multiply(&num2);
    check(res, i3);

    num1.set_value(i1);
    num2.set_value(u2);
    res = num1.multiply(&num2);
    check(res, i3);

    num1.set_value(u1);
    num2.set_value(i2);
    res = num1.multiply(&num2);
    check(res, i3);
}

TEST_F(ExpTestSuite, operand_division_case) {
    OperandExpress num1;
    OperandExpress num2;
    double d1 = 3.0;
    double d2 = 2.0;
    double d3 = d1 / d2;
    num1.set_value(d1);
    num2.set_value(d2);
    auto res = num1.division(&num2);
    check(res, d3);

    uint64_t u1 = 3UL;
    uint64_t u2 = 2UL;
    uint64_t u3 = u1 / u2;
    num1.set_value(d1);
    num2.set_value(u2);
    res = num1.division(&num2);
    check(res, d3); 

    num1.set_value(u1);
    num2.set_value(d2);
    res = num1.division(&num2);
    check(res, d3);

    num1.set_value(u1);
    num2.set_value(u2);
    res = num1.division(&num2);
    check(res, u3);

    int64_t i1 = 3L;
    int64_t i2 = 2L;
    int64_t i3 = i1 / i2;
    num1.set_value(d1);
    num2.set_value(i2);
    res = num1.division(&num2);
    check(res, d3); 

    num1.set_value(i1);
    num2.set_value(d2);
    res = num1.division(&num2);
    check(res, d3);

    num1.set_value(i1);
    num2.set_value(i2);
    res = num1.division(&num2);
    check(res, i3);

    num1.set_value(i1);
    num2.set_value(u2);
    res = num1.division(&num2);
    check(res, i3);

    num1.set_value(u1);
    num2.set_value(i2);
    res = num1.division(&num2);
    check(res, i3);
}

TEST_F(ExpTestSuite, operand_greater_case) {
    OperandExpress num1;
    OperandExpress num2;
    double d1 = 3.0;
    double d2 = 2.0;
    num1.set_value(d1);
    num2.set_value(d2);
    auto res = num1.greater(&num2);
    check(res, true);
    num1.set_value(d2);
    num2.set_value(d1);
    res = num1.greater(&num2);
    check(res, false);

    uint64_t u1 = 3UL;
    uint64_t u2 = 2UL;
    num1.set_value(d1);
    num2.set_value(u2);
    res = num1.greater(&num2);
    check(res, true); 
    num1.set_value(u2);
    num2.set_value(d1);
    res = num1.greater(&num2);
    check(res, false); 

    num1.set_value(u1);
    num2.set_value(d2);
    res = num1.greater(&num2);
    check(res, true);
    num1.set_value(d2);
    num2.set_value(u1);
    res = num1.greater(&num2);
    check(res, false);

    num1.set_value(u1);
    num2.set_value(u2);
    res = num1.greater(&num2);
    check(res, true);
    num1.set_value(u2);
    num2.set_value(u1);
    res = num1.greater(&num2);
    check(res, false);

    int64_t i1 = 3L;
    int64_t i2 = 2L;
    num1.set_value(d1);
    num2.set_value(i2);
    res = num1.greater(&num2);
    check(res, true); 
    num1.set_value(i2);
    num2.set_value(d1);
    res = num1.greater(&num2);
    check(res, false); 

    num1.set_value(i1);
    num2.set_value(d2);
    res = num1.greater(&num2);
    check(res, true);
    num1.set_value(d2);
    num2.set_value(i1);
    res = num1.greater(&num2);
    check(res, false);

    num1.set_value(i1);
    num2.set_value(i2);
    res = num1.greater(&num2);
    check(res, true);
    num1.set_value(i2);
    num2.set_value(i1);
    res = num1.greater(&num2);
    check(res, false);

    num1.set_value(i1);
    num2.set_value(u2);
    res = num1.greater(&num2);
    check(res, true);
    num1.set_value(u2);
    num2.set_value(i1);
    res = num1.greater(&num2);
    check(res, false);

    num1.set_value(u1);
    num2.set_value(i2);
    res = num1.greater(&num2);
    check(res, true);
    num1.set_value(i2);
    num2.set_value(u1);
    res = num1.greater(&num2);
    check(res, false);
    // equal
    num1.set_value(d1);
    num2.set_value(d1);
    res = num1.greater(&num2);
    check(res, false);
    num1.set_value(i1);
    num2.set_value(i1);
    res = num1.greater(&num2);
    check(res, false);
    num1.set_value(u1);
    num2.set_value(u1);
    res = num1.greater(&num2);
    check(res, false);
    num1.set_value(d1);
    num2.set_value(u1);
    res = num1.greater(&num2);
    check(res, false);
    num1.set_value(u1);
    num2.set_value(d1);
    res = num1.greater(&num2);
    check(res, false);
    num1.set_value(d1);
    num2.set_value(i1);
    res = num1.greater(&num2);
    check(res, false);
    num1.set_value(i1);
    num2.set_value(d1);
    res = num1.greater(&num2);
    check(res, false);
}

TEST_F(ExpTestSuite, operand_equal_or_equal_or_greater_case) {
    OperandExpress num1;
    OperandExpress num2;
    double d1 = 3.0;
    double d2 = 2.0;
    num1.set_value(d1);
    num2.set_value(d2);
    auto res = num1.equal_or_greater(&num2);
    check(res, true);
    num1.set_value(d2);
    num2.set_value(d1);
    res = num1.equal_or_greater(&num2);
    check(res, false);

    uint64_t u1 = 3UL;
    uint64_t u2 = 2UL;
    num1.set_value(d1);
    num2.set_value(u2);
    res = num1.equal_or_greater(&num2);
    check(res, true); 
    num1.set_value(u2);
    num2.set_value(d1);
    res = num1.equal_or_greater(&num2);
    check(res, false); 

    num1.set_value(u1);
    num2.set_value(d2);
    res = num1.equal_or_greater(&num2);
    check(res, true);
    num1.set_value(d2);
    num2.set_value(u1);
    res = num1.equal_or_greater(&num2);
    check(res, false);

    num1.set_value(u1);
    num2.set_value(u2);
    res = num1.equal_or_greater(&num2);
    check(res, true);
    num1.set_value(u2);
    num2.set_value(u1);
    res = num1.equal_or_greater(&num2);
    check(res, false);

    int64_t i1 = 3L;
    int64_t i2 = 2L;
    num1.set_value(d1);
    num2.set_value(i2);
    res = num1.equal_or_greater(&num2);
    check(res, true); 
    num1.set_value(i2);
    num2.set_value(d1);
    res = num1.equal_or_greater(&num2);
    check(res, false); 

    num1.set_value(i1);
    num2.set_value(d2);
    res = num1.equal_or_greater(&num2);
    check(res, true);
    num1.set_value(d2);
    num2.set_value(i1);
    res = num1.equal_or_greater(&num2);
    check(res, false);

    num1.set_value(i1);
    num2.set_value(i2);
    res = num1.equal_or_greater(&num2);
    check(res, true);
    num1.set_value(i2);
    num2.set_value(i1);
    res = num1.equal_or_greater(&num2);
    check(res, false);

    num1.set_value(i1);
    num2.set_value(u2);
    res = num1.equal_or_greater(&num2);
    check(res, true);
    num1.set_value(u2);
    num2.set_value(i1);
    res = num1.equal_or_greater(&num2);
    check(res, false);

    num1.set_value(u1);
    num2.set_value(i2);
    res = num1.equal_or_greater(&num2);
    check(res, true);
    num1.set_value(i2);
    num2.set_value(u1);
    res = num1.equal_or_greater(&num2);
    check(res, false);
    // equal
    num1.set_value(d1);
    num2.set_value(d1);
    res = num1.equal_or_greater(&num2);
    check(res, true);
    num1.set_value(i1);
    num2.set_value(i1);
    res = num1.equal_or_greater(&num2);
    check(res, true);
    num1.set_value(u1);
    num2.set_value(u1);
    res = num1.equal_or_greater(&num2);
    check(res, true);
    num1.set_value(d1);
    num2.set_value(u1);
    res = num1.equal_or_greater(&num2);
    check(res, true);
    num1.set_value(u1);
    num2.set_value(d1);
    res = num1.equal_or_greater(&num2);
    check(res, true);
    num1.set_value(d1);
    num2.set_value(i1);
    res = num1.equal_or_greater(&num2);
    check(res, true);
    num1.set_value(i1);
    num2.set_value(d1);
    res = num1.equal_or_greater(&num2);
    check(res, true);
}

TEST_F(ExpTestSuite, operand_less_case) {
    OperandExpress num1;
    OperandExpress num2;
    double d1 = 3.0;
    double d2 = 2.0;
    num1.set_value(d1);
    num2.set_value(d2);
    auto res = num1.less(&num2);
    check(res, false);
    num1.set_value(d2);
    num2.set_value(d1);
    res = num1.less(&num2);
    check(res, true);

    uint64_t u1 = 3UL;
    uint64_t u2 = 2UL;
    num1.set_value(d1);
    num2.set_value(u2);
    res = num1.less(&num2);
    check(res, false); 
    num1.set_value(u2);
    num2.set_value(d1);
    res = num1.less(&num2);
    check(res, true); 

    num1.set_value(u1);
    num2.set_value(d2);
    res = num1.less(&num2);
    check(res, false);
    num1.set_value(d2);
    num2.set_value(u1);
    res = num1.less(&num2);
    check(res, true);

    num1.set_value(u1);
    num2.set_value(u2);
    res = num1.less(&num2);
    check(res, false);
    num1.set_value(u2);
    num2.set_value(u1);
    res = num1.less(&num2);
    check(res, true);

    int64_t i1 = 3L;
    int64_t i2 = 2L;
    num1.set_value(d1);
    num2.set_value(i2);
    res = num1.less(&num2);
    check(res, false); 
    num1.set_value(i2);
    num2.set_value(d1);
    res = num1.less(&num2);
    check(res, true); 

    num1.set_value(i1);
    num2.set_value(d2);
    res = num1.less(&num2);
    check(res, false);
    num1.set_value(d2);
    num2.set_value(i1);
    res = num1.less(&num2);
    check(res, true);

    num1.set_value(i1);
    num2.set_value(i2);
    res = num1.less(&num2);
    check(res, false);
    num1.set_value(i2);
    num2.set_value(i1);
    res = num1.less(&num2);
    check(res, true);

    num1.set_value(i1);
    num2.set_value(u2);
    res = num1.less(&num2);
    check(res, false);
    num1.set_value(u2);
    num2.set_value(i1);
    res = num1.less(&num2);
    check(res, true);

    num1.set_value(u1);
    num2.set_value(i2);
    res = num1.less(&num2);
    check(res, false);
    num1.set_value(i2);
    num2.set_value(u1);
    res = num1.less(&num2);
    check(res, true);
    // equal
    num1.set_value(d1);
    num2.set_value(d1);
    res = num1.less(&num2);
    check(res, false);
    num1.set_value(i1);
    num2.set_value(i1);
    res = num1.less(&num2);
    check(res, false);
    num1.set_value(u1);
    num2.set_value(u1);
    res = num1.less(&num2);
    check(res, false);
    num1.set_value(d1);
    num2.set_value(u1);
    res = num1.less(&num2);
    check(res, false);
    num1.set_value(u1);
    num2.set_value(d1);
    res = num1.less(&num2);
    check(res, false);
    num1.set_value(d1);
    num2.set_value(i1);
    res = num1.less(&num2);
    check(res, false);
    num1.set_value(i1);
    num2.set_value(d1);
    res = num1.less(&num2);
    check(res, false);
}

TEST_F(ExpTestSuite, operand_equal_or_less) {
    OperandExpress num1;
    OperandExpress num2;
    double d1 = 3.0;
    double d2 = 2.0;
    num1.set_value(d1);
    num2.set_value(d2);
    auto res = num1.equal_or_less(&num2);
    check(res, false);
    num1.set_value(d2);
    num2.set_value(d1);
    res = num1.equal_or_less(&num2);
    check(res, true);

    uint64_t u1 = 3UL;
    uint64_t u2 = 2UL;
    num1.set_value(d1);
    num2.set_value(u2);
    res = num1.equal_or_less(&num2);
    check(res, false); 
    num1.set_value(u2);
    num2.set_value(d1);
    res = num1.equal_or_less(&num2);
    check(res, true); 

    num1.set_value(u1);
    num2.set_value(d2);
    res = num1.equal_or_less(&num2);
    check(res, false);
    num1.set_value(d2);
    num2.set_value(u1);
    res = num1.equal_or_less(&num2);
    check(res, true);

    num1.set_value(u1);
    num2.set_value(u2);
    res = num1.equal_or_less(&num2);
    check(res, false);
    num1.set_value(u2);
    num2.set_value(u1);
    res = num1.equal_or_less(&num2);
    check(res, true);

    int64_t i1 = 3L;
    int64_t i2 = 2L;
    num1.set_value(d1);
    num2.set_value(i2);
    res = num1.equal_or_less(&num2);
    check(res, false); 
    num1.set_value(i2);
    num2.set_value(d1);
    res = num1.equal_or_less(&num2);
    check(res, true); 

    num1.set_value(i1);
    num2.set_value(d2);
    res = num1.equal_or_less(&num2);
    check(res, false);
    num1.set_value(d2);
    num2.set_value(i1);
    res = num1.equal_or_less(&num2);
    check(res, true);

    num1.set_value(i1);
    num2.set_value(i2);
    res = num1.equal_or_less(&num2);
    check(res, false);
    num1.set_value(i2);
    num2.set_value(i1);
    res = num1.equal_or_less(&num2);
    check(res, true);

    num1.set_value(i1);
    num2.set_value(u2);
    res = num1.equal_or_less(&num2);
    check(res, false);
    num1.set_value(u2);
    num2.set_value(i1);
    res = num1.equal_or_less(&num2);
    check(res, true);

    num1.set_value(u1);
    num2.set_value(i2);
    res = num1.equal_or_less(&num2);
    check(res, false);
    num1.set_value(i2);
    num2.set_value(u1);
    res = num1.equal_or_less(&num2);
    check(res, true);
    // equal
    num1.set_value(d1);
    num2.set_value(d1);
    res = num1.equal_or_less(&num2);
    check(res, true);
    num1.set_value(i1);
    num2.set_value(i1);
    res = num1.equal_or_less(&num2);
    check(res, true);
    num1.set_value(u1);
    num2.set_value(u1);
    res = num1.equal_or_less(&num2);
    check(res, true);
    num1.set_value(d1);
    num2.set_value(u1);
    res = num1.equal_or_less(&num2);
    check(res, true);
    num1.set_value(u1);
    num2.set_value(d1);
    res = num1.equal_or_less(&num2);
    check(res, true);
    num1.set_value(d1);
    num2.set_value(i1);
    res = num1.equal_or_less(&num2);
    check(res, true);
    num1.set_value(i1);
    num2.set_value(d1);
    res = num1.equal_or_less(&num2);
    check(res, true);
}

TEST_F(ExpTestSuite, operand_equal_case) {
    OperandExpress num1;
    OperandExpress num2;
    double d1 = 3.0;
    double d2 = 2.0;
    num1.set_value(d1);
    num2.set_value(d2);
    auto res = num1.equal(&num2);
    check(res, false);
    num1.set_value(d2);
    num2.set_value(d1);
    res = num1.equal(&num2);
    check(res, false);

    uint64_t u1 = 3UL;
    uint64_t u2 = 2UL;
    num1.set_value(d1);
    num2.set_value(u2);
    res = num1.equal(&num2);
    check(res, false); 
    num1.set_value(u2);
    num2.set_value(d1);
    res = num1.equal(&num2);
    check(res, false); 

    num1.set_value(u1);
    num2.set_value(d2);
    res = num1.equal(&num2);
    check(res, false);
    num1.set_value(d2);
    num2.set_value(u1);
    res = num1.equal(&num2);
    check(res, false);

    num1.set_value(u1);
    num2.set_value(u2);
    res = num1.equal(&num2);
    check(res, false);
    num1.set_value(u2);
    num2.set_value(u1);
    res = num1.equal(&num2);
    check(res, false);

    int64_t i1 = 3L;
    int64_t i2 = 2L;
    num1.set_value(d1);
    num2.set_value(i2);
    res = num1.equal(&num2);
    check(res, false); 
    num1.set_value(i2);
    num2.set_value(d1);
    res = num1.equal(&num2);
    check(res, false); 

    num1.set_value(i1);
    num2.set_value(d2);
    res = num1.equal(&num2);
    check(res, false);
    num1.set_value(d2);
    num2.set_value(i1);
    res = num1.equal(&num2);
    check(res, false);

    num1.set_value(i1);
    num2.set_value(i2);
    res = num1.equal(&num2);
    check(res, false);
    num1.set_value(i2);
    num2.set_value(i1);
    res = num1.equal(&num2);
    check(res, false);

    num1.set_value(i1);
    num2.set_value(u2);
    res = num1.equal(&num2);
    check(res, false);
    num1.set_value(u2);
    num2.set_value(i1);
    res = num1.equal(&num2);
    check(res, false);

    num1.set_value(u1);
    num2.set_value(i2);
    res = num1.equal(&num2);
    check(res, false);
    num1.set_value(i2);
    num2.set_value(u1);
    res = num1.equal(&num2);
    check(res, false);
    // equal
    num1.set_value(d1);
    num2.set_value(d1);
    res = num1.equal(&num2);
    check(res, true);
    num1.set_value(i1);
    num2.set_value(i1);
    res = num1.equal(&num2);
    check(res, true);
    num1.set_value(u1);
    num2.set_value(u1);
    res = num1.equal(&num2);
    check(res, true);
    num1.set_value(d1);
    num2.set_value(u1);
    res = num1.equal(&num2);
    check(res, true);
    num1.set_value(u1);
    num2.set_value(d1);
    res = num1.equal(&num2);
    check(res, true);
    num1.set_value(d1);
    num2.set_value(i1);
    res = num1.equal(&num2);
    check(res, true);
    num1.set_value(i1);
    num2.set_value(d1);
    res = num1.equal(&num2);
    check(res, true);
}

TEST_F(ExpTestSuite, operand_not_equal_case) {
    OperandExpress num1;
    OperandExpress num2;
    double d1 = 3.0;
    double d2 = 2.0;
    num1.set_value(d1);
    num2.set_value(d2);
    auto res = num1.not_equal(&num2);
    check(res, true);
    num1.set_value(d2);
    num2.set_value(d1);
    res = num1.not_equal(&num2);
    check(res, true);

    uint64_t u1 = 3UL;
    uint64_t u2 = 2UL;
    num1.set_value(d1);
    num2.set_value(u2);
    res = num1.not_equal(&num2);
    check(res, true); 
    num1.set_value(u2);
    num2.set_value(d1);
    res = num1.not_equal(&num2);
    check(res, true); 

    num1.set_value(u1);
    num2.set_value(d2);
    res = num1.not_equal(&num2);
    check(res, true);
    num1.set_value(d2);
    num2.set_value(u1);
    res = num1.not_equal(&num2);
    check(res, true);

    num1.set_value(u1);
    num2.set_value(u2);
    res = num1.not_equal(&num2);
    check(res, true);
    num1.set_value(u2);
    num2.set_value(u1);
    res = num1.not_equal(&num2);
    check(res, true);

    int64_t i1 = 3L;
    int64_t i2 = 2L;
    num1.set_value(d1);
    num2.set_value(i2);
    res = num1.not_equal(&num2);
    check(res, true); 
    num1.set_value(i2);
    num2.set_value(d1);
    res = num1.not_equal(&num2);
    check(res, true); 

    num1.set_value(i1);
    num2.set_value(d2);
    res = num1.not_equal(&num2);
    check(res, true);
    num1.set_value(d2);
    num2.set_value(i1);
    res = num1.not_equal(&num2);
    check(res, true);

    num1.set_value(i1);
    num2.set_value(i2);
    res = num1.not_equal(&num2);
    check(res, true);
    num1.set_value(i2);
    num2.set_value(i1);
    res = num1.not_equal(&num2);
    check(res, true);

    num1.set_value(i1);
    num2.set_value(u2);
    res = num1.not_equal(&num2);
    check(res, true);
    num1.set_value(u2);
    num2.set_value(i1);
    res = num1.not_equal(&num2);
    check(res, true);

    num1.set_value(u1);
    num2.set_value(i2);
    res = num1.not_equal(&num2);
    check(res, true);
    num1.set_value(i2);
    num2.set_value(u1);
    res = num1.not_equal(&num2);
    check(res, true);
    // not_equal
    num1.set_value(d1);
    num2.set_value(d1);
    res = num1.not_equal(&num2);
    check(res, false);
    num1.set_value(i1);
    num2.set_value(i1);
    res = num1.not_equal(&num2);
    check(res, false);
    num1.set_value(u1);
    num2.set_value(u1);
    res = num1.not_equal(&num2);
    check(res, false);
    num1.set_value(d1);
    num2.set_value(u1);
    res = num1.not_equal(&num2);
    check(res, false);
    num1.set_value(u1);
    num2.set_value(d1);
    res = num1.not_equal(&num2);
    check(res, false);
    num1.set_value(d1);
    num2.set_value(i1);
    res = num1.not_equal(&num2);
    check(res, false);
    num1.set_value(i1);
    num2.set_value(d1);
    res = num1.not_equal(&num2);
    check(res, false);
}

TEST_F(ExpTestSuite, operand_logical_and_case) {
    OperandExpress num1;
    OperandExpress num2;
    bool d1 = true;
    bool d2 = false;
    num1.set_value(d1);
    num2.set_value(d2);
    auto res = num1.logical_and(&num2); 
    check(res, false);
    num1.set_value(d2);
    num2.set_value(d1);
    res = num1.logical_and(&num2);
    check(res, false);
    num1.set_value(d2);
    num2.set_value(d2);
    res = num1.logical_and(&num2);
    check(res, false);
    num1.set_value(d1);
    num2.set_value(d1);
    res = num1.logical_and(&num2);
    check(res, true);
    // invalid
    double d = 1.0;
    num1.set_value(d);
    res = num1.logical_and(&num2); 
    check(res, OperandExpress::INVALID);
    uint64_t i = 1UL;
    num1.set_value(i);
    res = num1.logical_and(&num2);
    check(res, OperandExpress::INVALID);
    int64_t j = 1UL;
    num1.set_value(j);
    res = num1.logical_and(&num2);
    check(res, OperandExpress::INVALID);
}

TEST_F(ExpTestSuite, operand_logical_or_case) {
    OperandExpress num1;
    OperandExpress num2;
    bool d1 = true;
    bool d2 = false;
    num1.set_value(d1);
    num2.set_value(d2);
    auto res = num1.logical_or(&num2); 
    check(res, true);
    num1.set_value(d2);
    num2.set_value(d1);
    res = num1.logical_or(&num2);
    check(res, true);
    num1.set_value(d2);
    num2.set_value(d2);
    res = num1.logical_or(&num2);
    check(res, false);
    num1.set_value(d1);
    num2.set_value(d1);
    res = num1.logical_or(&num2);
    check(res, true);
    // invalid
    double d = 1.0;
    num1.set_value(d);
    res = num1.logical_or(&num2); 
    check(res, OperandExpress::INVALID);
    uint64_t i = 1UL;
    num1.set_value(i);
    res = num1.logical_or(&num2);
    check(res, OperandExpress::INVALID);
    int64_t j = 1UL;
    num1.set_value(j);
    res = num1.logical_or(&num2);
    check(res, OperandExpress::INVALID);
}

TEST_F(ExpTestSuite, test_ln) {
    OperandExpress num1;
    double d1 = 1.5;
    num1.set_value(d1);
    auto res = num1.ln();
    check(res, std::log(d1));

    uint64_t u1 = 100UL;
    num1.set_value(u1);
    res = num1.ln();
    check(res, std::log(u1));

    int64_t i1 = 100UL;
    num1.set_value(i1);
    res = num1.ln();
    check(res, std::log(i1));
}

TEST_F(ExpTestSuite, exp_splitter_test) {
    std::string exp_str = "  a + b  * (c)";
    std::unordered_set<std::string> atoms = {"a", "b", "c"};
    Express exp;
    EXPECT_TRUE(ExpSplitter::split(exp_str, atoms, &exp));
    EXPECT_EQ(7, exp.size());
}

TEST_F(ExpTestSuite, exp_compute_negation_test) {
    OperandExpress* num1 = new OperandExpress();
    bool tmp = true;
    num1->set_value(tmp);
    ExpressInterfacePtr lhs(num1);
    UnaryOperatorExpress* op = new UnaryOperatorExpress("!");
    // stack 
    std::stack<ExpressInterfacePtr> stack;
    stack.push(lhs);
    EXPECT_TRUE(op->compute(&stack));
    ExpressInterfacePtr opptr = stack.top();
    OperandExpress* res = dynamic_cast<OperandExpress*>(opptr.get());
    EXPECT_EQ(OperandExpress::BOOL, res->value_type());
    EXPECT_FALSE(res->get_bool_value());
}

TEST_F(ExpTestSuite, exp_compute_add_test) {
    OperandExpress* num1 = new OperandExpress();
    double d1 = 2.4;
    num1->set_value(d1);
    ExpressInterfacePtr lhs(num1);
    OperandExpress* num2 = new OperandExpress();
    double d2 = 1.2;
    num2->set_value(d2);
    ExpressInterfacePtr rhs(num2);
    BinaryOperatorExpress* op = new BinaryOperatorExpress("+");
    // stack 
    std::stack<ExpressInterfacePtr> stack;
    stack.push(lhs);
    stack.push(rhs);
    EXPECT_TRUE(op->compute(&stack));
    ExpressInterfacePtr opptr = stack.top();
    OperandExpress* res = dynamic_cast<OperandExpress*>(opptr.get());
    EXPECT_EQ(OperandExpress::DOUBLE, res->value_type());
    EXPECT_DOUBLE_EQ(2.4 + 1.2, res->get_double_value());
}

TEST_F(ExpTestSuite, exp_compute_minus_test) {
    OperandExpress* num1 = new OperandExpress();
    double d1 = 2.4;
    num1->set_value(d1);
    ExpressInterfacePtr lhs(num1);
    OperandExpress* num2 = new OperandExpress();
    double d2 = 1.2;
    num2->set_value(d2);
    ExpressInterfacePtr rhs(num2);
    BinaryOperatorExpress* op = new BinaryOperatorExpress("-");
    // stack 
    std::stack<ExpressInterfacePtr> stack;
    stack.push(lhs);
    stack.push(rhs);
    EXPECT_TRUE(op->compute(&stack));
    ExpressInterfacePtr opptr = stack.top();
    OperandExpress* res = dynamic_cast<OperandExpress*>(opptr.get());
    EXPECT_EQ(OperandExpress::DOUBLE, res->value_type());
    EXPECT_DOUBLE_EQ(2.4 - 1.2, res->get_double_value());
}

TEST_F(ExpTestSuite, exp_compute_multi_test) {
    OperandExpress* num1 = new OperandExpress();
    double d1 = 2.4;
    num1->set_value(d1);
    ExpressInterfacePtr lhs(num1);
    OperandExpress* num2 = new OperandExpress();
    double d2 = 1.2;
    num2->set_value(d2);
    ExpressInterfacePtr rhs(num2);
    BinaryOperatorExpress* op = new BinaryOperatorExpress("*");
    // stack 
    std::stack<ExpressInterfacePtr> stack;
    stack.push(lhs);
    stack.push(rhs);
    EXPECT_TRUE(op->compute(&stack));
    ExpressInterfacePtr opptr= stack.top();
    OperandExpress* res = dynamic_cast<OperandExpress*>(opptr.get());
    EXPECT_EQ(OperandExpress::DOUBLE, res->value_type());
    EXPECT_DOUBLE_EQ(2.4 * 1.2, res->get_double_value());
}

TEST_F(ExpTestSuite, exp_compute_division_test) {
    OperandExpress* num1 = new OperandExpress();
    double d1 = 2.4;
    num1->set_value(d1);
    ExpressInterfacePtr lhs(num1);
    OperandExpress* num2 = new OperandExpress();
    double d2 = 1.2;
    num2->set_value(d2);
    ExpressInterfacePtr rhs(num2);
    BinaryOperatorExpress* op = new BinaryOperatorExpress("/");
    // stack 
    std::stack<ExpressInterfacePtr> stack;
    stack.push(lhs);
    stack.push(rhs);
    EXPECT_TRUE(op->compute(&stack));
    ExpressInterfacePtr opptr= stack.top();
    OperandExpress* res = dynamic_cast<OperandExpress*>(opptr.get());
    EXPECT_EQ(OperandExpress::DOUBLE, res->value_type());
    EXPECT_DOUBLE_EQ(2.4 / 1.2, res->get_double_value());
}

TEST_F(ExpTestSuite, exp_compute_binary_logic_and_test) {
    {
        OperandExpress* num1 = new OperandExpress();
        bool d1 = true;
        num1->set_value(d1);
        ExpressInterfacePtr lhs(num1);
        OperandExpress* num2 = new OperandExpress();
        bool d2 = false;
        num2->set_value(d2);
        ExpressInterfacePtr rhs(num2);
        BinaryOperatorExpress* op = new BinaryOperatorExpress("&&");
        // stack 
        std::stack<ExpressInterfacePtr> stack;
        stack.push(lhs);
        stack.push(rhs);
        EXPECT_TRUE(op->compute(&stack));
        ExpressInterfacePtr opptr= stack.top();
        OperandExpress* res = dynamic_cast<OperandExpress*>(opptr.get());
        EXPECT_EQ(OperandExpress::BOOL, res->value_type());
        EXPECT_DOUBLE_EQ(true && false, res->get_bool_value());
    }

    {
        OperandExpress* num1 = new OperandExpress();
        bool d1 = false;
        num1->set_value(d1);
        ExpressInterfacePtr lhs(num1);
        OperandExpress* num2 = new OperandExpress();
        bool d2 = true;
        num2->set_value(d2);
        ExpressInterfacePtr rhs(num2);
        BinaryOperatorExpress* op = new BinaryOperatorExpress("&&");
        // stack 
        std::stack<ExpressInterfacePtr> stack;
        stack.push(lhs);
        stack.push(rhs);
        EXPECT_TRUE(op->compute(&stack));
        ExpressInterfacePtr opptr= stack.top();
        OperandExpress* res = dynamic_cast<OperandExpress*>(opptr.get());
        EXPECT_EQ(OperandExpress::BOOL, res->value_type());
        EXPECT_DOUBLE_EQ(true && false, res->get_bool_value());
    }


    {
        OperandExpress* num1 = new OperandExpress();
        bool d1 = true;
        num1->set_value(d1);
        ExpressInterfacePtr lhs(num1);
        OperandExpress* num2 = new OperandExpress();
        bool d2 = true;
        num2->set_value(d2);
        ExpressInterfacePtr rhs(num2);
        OperandExpress* num3 = new OperandExpress();
        bool d3 = false;
        num3->set_value(d3);
        ExpressInterfacePtr ehs(num3);
        BinaryOperatorExpress* op = new BinaryOperatorExpress("&&");
        // stack 
        std::stack<ExpressInterfacePtr> stack;
        stack.push(lhs);
        stack.push(rhs);
        stack.push(ehs);
        EXPECT_TRUE(op->compute(&stack));
        ExpressInterfacePtr opptr= stack.top();
        OperandExpress* res = dynamic_cast<OperandExpress*>(opptr.get());
        EXPECT_EQ(OperandExpress::BOOL, res->value_type());
        EXPECT_DOUBLE_EQ(true && true && false, res->get_bool_value());
    }
}

TEST_F(ExpTestSuite, exp_compute_binary_logic_or_test) {
    {
        OperandExpress* num1 = new OperandExpress();
        bool d1 = true;
        num1->set_value(d1);
        ExpressInterfacePtr lhs(num1);
        OperandExpress* num2 = new OperandExpress();
        bool d2 = false;
        num2->set_value(d2);
        ExpressInterfacePtr rhs(num2);
        BinaryOperatorExpress* op = new BinaryOperatorExpress("||");
        // stack 
        std::stack<ExpressInterfacePtr> stack;
        stack.push(lhs);
        stack.push(rhs);
        EXPECT_TRUE(op->compute(&stack));
        ExpressInterfacePtr opptr= stack.top();
        OperandExpress* res = dynamic_cast<OperandExpress*>(opptr.get());
        EXPECT_EQ(OperandExpress::BOOL, res->value_type());
        EXPECT_DOUBLE_EQ(true || false, res->get_bool_value());
    }

    {
        OperandExpress* num1 = new OperandExpress();
        bool d1 = false;
        num1->set_value(d1);
        ExpressInterfacePtr lhs(num1);
        OperandExpress* num2 = new OperandExpress();
        bool d2 = true;
        num2->set_value(d2);
        ExpressInterfacePtr rhs(num2);
        BinaryOperatorExpress* op = new BinaryOperatorExpress("||");
        // stack 
        std::stack<ExpressInterfacePtr> stack;
        stack.push(lhs);
        stack.push(rhs);
        EXPECT_TRUE(op->compute(&stack));
        ExpressInterfacePtr opptr= stack.top();
        OperandExpress* res = dynamic_cast<OperandExpress*>(opptr.get());
        EXPECT_EQ(OperandExpress::BOOL, res->value_type());
        EXPECT_DOUBLE_EQ(true || false, res->get_bool_value());
    }


    {
        OperandExpress* num1 = new OperandExpress();
        bool d1 = true;
        num1->set_value(d1);
        ExpressInterfacePtr lhs(num1);
        OperandExpress* num2 = new OperandExpress();
        bool d2 = true;
        num2->set_value(d2);
        ExpressInterfacePtr rhs(num2);
        OperandExpress* num3 = new OperandExpress();
        bool d3 = false;
        num3->set_value(d3);
        ExpressInterfacePtr ehs(num3);
        BinaryOperatorExpress* op = new BinaryOperatorExpress("||");
        // stack 
        std::stack<ExpressInterfacePtr> stack;
        stack.push(lhs);
        stack.push(rhs);
        stack.push(ehs);
        EXPECT_TRUE(op->compute(&stack));
        ExpressInterfacePtr opptr= stack.top();
        OperandExpress* res = dynamic_cast<OperandExpress*>(opptr.get());
        EXPECT_EQ(OperandExpress::BOOL, res->value_type());
        EXPECT_DOUBLE_EQ(true || true || false, res->get_bool_value());
    }
}

TEST_F(ExpTestSuite, test_is_carespace) {
    ExpressInterfacePtr ptr(new OperandExpress("[ac]"));
    EXPECT_TRUE(_obj._is_carespace(ptr));
    ptr.reset(new OperandExpress("fea(12[]3)"));
    EXPECT_FALSE(_obj._is_carespace(ptr));
}

TEST_F(ExpTestSuite, test_carespace_add_logic_and) {
    EXPECT_FALSE(_obj._carespace_add_logic_and(NULL));
    {
        Express express;
        std::vector<std::string> cares = {
            "[care1]", "[care2]"
        };
        for (const auto& care : cares) {
            CWARNING_LOG("start care[%s]", care.c_str());
            ExpressInterfacePtr ptr(new OperandExpress(care)); 
            express.push_back(ptr);
        }
        EXPECT_TRUE(_obj._carespace_add_logic_and(&express));
        EXPECT_EQ(3, express.size());
        EXPECT_EQ("&&", express[1]->value());
    }
    {
        Express express;
        std::vector<std::string> cares = {
            "[care1]", "[care2]", "[care3]"
        };
        for (const auto& care : cares) {
            CWARNING_LOG("start care[%s]", care.c_str());
            ExpressInterfacePtr ptr(new OperandExpress(care)); 
            express.push_back(ptr);
        }
        EXPECT_TRUE(_obj._carespace_add_logic_and(&express));
        EXPECT_EQ(5, express.size());
        EXPECT_EQ("&&", express[1]->value());
        EXPECT_EQ("&&", express[3]->value());
    }
    {
        Express express;
        std::vector<std::string> cares = {
            "[care1]", "[care2]", "xxxx", "[care3]"
        };
        for (const auto& care : cares) {
            CWARNING_LOG("start care[%s]", care.c_str());
            ExpressInterfacePtr ptr(new OperandExpress(care)); 
            express.push_back(ptr);
        }
        EXPECT_TRUE(_obj._carespace_add_logic_and(&express));
        EXPECT_EQ(5, express.size());
        EXPECT_EQ("&&", express[1]->value());
    }
    {
        Express express;
        std::vector<std::string> cares = {
            "[care1]", "[care2]", "xxxx", "[care3]",
            "aaa", "[1]", "[3]"
        };
        for (const auto& care : cares) {
            CWARNING_LOG("start care[%s]", care.c_str());
            ExpressInterfacePtr ptr(new OperandExpress(care)); 
            express.push_back(ptr);
        }
        EXPECT_TRUE(_obj._carespace_add_logic_and(&express));
        EXPECT_EQ(9, express.size());
        EXPECT_EQ("&&", express[1]->value());
        EXPECT_EQ("&&", express[7]->value());
    }
}

bool set_exp_vector(
        const std::vector<std::pair<std::string, std::string>>& vec,
        std::vector<ExpressInterfacePtr>* postfix_exp) {
    postfix_exp->clear();
    for (const auto& pair : vec) {
        ExpressInterfacePtr op;
        if (pair.first == "unary_operator") {
            op.reset(new UnaryOperatorExpress(pair.second));
        } else if (pair.first == "binary_operator") {
            op.reset(new BinaryOperatorExpress(pair.second));
        } else if (pair.first == "parenthese") {
            op.reset(new ParentheseOperatorExpress(pair.second));
        } else if (pair.first == "double") {
            double tmp = 0.0;
            try {
                tmp = boost::lexical_cast<double>(pair.second);
            } catch (const boost::bad_lexical_cast& e) {
                return false;
            }
            OperandExpress* num = new OperandExpress();
            num->set_value(tmp);
            op.reset(num);
        } else if (pair.first == "uint64") {
            uint64_t tmp = 0;
            try {
                tmp = boost::lexical_cast<int>(pair.second);
            } catch (const boost::bad_lexical_cast& e) {
                return false;
            }
            OperandExpress* num = new OperandExpress();
            num->set_value(tmp);
            op.reset(num);
        } else if (pair.first == "int64") {
            int64_t tmp = 0;
            try {
                tmp = boost::lexical_cast<int>(pair.second);
            } catch (const boost::bad_lexical_cast& e) {
                return false;
            }
            OperandExpress* num = new OperandExpress();
            num->set_value(tmp);
            op.reset(num);
 
        } else if (pair.first == "bool") {
            bool tmp = true;
            try {
                tmp = boost::lexical_cast<bool>(pair.second);
            } catch (const boost::bad_lexical_cast& e) {
                return false;
            }
            OperandExpress* num = new OperandExpress();
            num->set_value(tmp);
            op.reset(num);
        } else if (pair.first == "algebra") {
            OperandExpress* num = new OperandExpress(pair.second);
            op.reset(num);
        } else {
            return false;
        }
        postfix_exp->emplace_back(op);
    }
    return true;
}

TEST_F(ExpTestSuite, exp_calculate_case) {
    std::vector<std::pair<std::string, std::string>> vec = {
        {"double", "0.5"},
        {"double", "1.5"},
        {"binary_operator", "+"},
        {"double", "3.0"},
        {"binary_operator", "*"},
        {"double", "6.0"},
        {"binary_operator", "/"}
    };
    std::vector<ExpressInterfacePtr> postfix_exp;
    set_exp_vector(vec, &postfix_exp);
    {
        ExpressInterfacePtr res_tmp = _obj._calculate(postfix_exp, _obj._equation_map, _obj._collector);
        OperandExpress* res = dynamic_cast<OperandExpress*>(res_tmp.get());
        EXPECT_EQ(OperandExpress::DOUBLE, res->value_type());
        EXPECT_DOUBLE_EQ((0.5 + 1.5) * 3.0 / 6.0, res->get_double_value());
    }
}

TEST_F(ExpTestSuite, exp_infix_to_postfix) {
    {
        std::vector<std::pair<std::string, std::string>> infix = {
            {"parenthese", "("},
            {"double", "0.5"},
            {"binary_operator", "+"},
            {"double", "1.5"},
            {"parenthese", ")"},
            {"binary_operator", "*"},
            {"double", "3.0"},
            {"binary_operator", "/"},
            {"double", "6.0"}
        };
        std::vector<ExpressInterfacePtr> infix_exp;
        set_exp_vector(infix, &infix_exp);
        std::vector<ExpressInterfacePtr> postfix_exp;
        EXPECT_TRUE(_obj._infix_to_postfix(infix_exp, &postfix_exp));
        EXPECT_EQ(7, postfix_exp.size());
        ExpressInterfacePtr res_tmp = _obj._calculate(postfix_exp, _obj._equation_map, _obj._collector);
        OperandExpress* res = dynamic_cast<OperandExpress*>(res_tmp.get());
        EXPECT_EQ(OperandExpress::DOUBLE, res->value_type());
        EXPECT_DOUBLE_EQ((0.5 + 1.5) * 3.0 / 6.0, res->get_double_value());
    }
    {
        std::vector<std::pair<std::string, std::string>> infix = {
            {"parenthese", "("},
            {"double", "1.0"},
            {"binary_operator", "+"},
            {"parenthese", "("},
            {"double", "1.5"},
            {"binary_operator", "*"},
            {"double", "3.0"},
            {"binary_operator", "+"},
            {"double", "2.0"},
            {"binary_operator", "/"},
            {"double", "0.5"},
            {"parenthese", ")"},
            {"parenthese", ")"},
            {"binary_operator", "*"},
            {"double", "2.0"}
        };
        std::vector<ExpressInterfacePtr> infix_exp;
        set_exp_vector(infix, &infix_exp);
        std::vector<ExpressInterfacePtr> postfix_exp;
        EXPECT_TRUE(_obj._infix_to_postfix(infix_exp, &postfix_exp));
        EXPECT_EQ(11, postfix_exp.size());
        ExpressInterfacePtr res_tmp = _obj._calculate(postfix_exp, _obj._equation_map, _obj._collector);
        OperandExpress* res = dynamic_cast<OperandExpress*>(res_tmp.get());
        EXPECT_EQ(OperandExpress::DOUBLE, res->value_type());
        EXPECT_DOUBLE_EQ((1.0 + (1.5 * 3.0 + 2.0 / 0.5)) * 2, res->get_double_value());
    }
    {
        std::vector<std::pair<std::string, std::string>> infix = {
            {"parenthese", "("},
            {"double", "1.0"},
            {"binary_operator", "-"},
            {"parenthese", "("},
            {"double", "1.5"},
            {"binary_operator", "*"},
            {"double", "3.0"},
            {"binary_operator", "-"},
            {"double", "2.0"},
            {"binary_operator", "/"},
            {"double", "0.5"},
            {"parenthese", ")"},
            {"parenthese", ")"},
            {"binary_operator", "*"},
            {"double", "2.0"}
        };
        std::vector<ExpressInterfacePtr> infix_exp;
        set_exp_vector(infix, &infix_exp);
        std::vector<ExpressInterfacePtr> postfix_exp;
        EXPECT_TRUE(_obj._infix_to_postfix(infix_exp, &postfix_exp));
        EXPECT_EQ(11, postfix_exp.size());
        ExpressInterfacePtr res_tmp = _obj._calculate(postfix_exp, _obj._equation_map, _obj._collector);
        OperandExpress* res = dynamic_cast<OperandExpress*>(res_tmp.get());
        EXPECT_EQ(OperandExpress::DOUBLE, res->value_type());
        EXPECT_DOUBLE_EQ((1.0 - (1.5 * 3.0 - 2.0 / 0.5)) * 2, res->get_double_value());
    }
}

TEST_F(ExpTestSuite, exp_reset_algebraic_expression) {
    std::vector<std::pair<std::string, double>> set = {
        {"fea(1)", 1.0},
        {"fea(2)", 2.0}
    };
    _obj._reset_algebraic_expression(set, &_obj._collector);
    EXPECT_EQ(2, _obj._collector.size());
    _obj._reset_algebraic_expression(set, &_obj._collector);
    EXPECT_EQ(2, _obj._collector.size());
    std::vector<std::pair<std::string, bool>> bset = {
        {"['accessid':='1']", true},
        {"['is_wiless':='1']", false}
    };

    _obj._reset_algebraic_expression(bset, &_obj._collector);
    EXPECT_EQ(4, _obj._collector.size());
}

TEST_F(ExpTestSuite, test_exp_calculate_exps) {
    // express1 : sign 888 fea(1) - fea(2)
    // express2 : sign 999 fea(3) * fea(4)
    
    // express1
    std::vector<std::pair<std::string, std::string>> exp1 = {
        {"algebra", "fea(1)"},
        {"algebra", "fea(2)"},
        {"binary_operator", "-"}
    };
    std::vector<ExpressInterfacePtr> postfix_exp;
    set_exp_vector(exp1, &postfix_exp);
    _obj._postfix_exps.emplace_back(std::make_pair(888UL, postfix_exp));
    // express2
    std::vector<std::pair<std::string, std::string>> exp2 = {
        {"algebra", "fea(3)"},
        {"algebra", "fea(4)"},
        {"binary_operator", "*"}
    };
    set_exp_vector(exp2, &postfix_exp);
    _obj._postfix_exps.emplace_back(std::make_pair(999UL, postfix_exp));
    
    std::vector<std::pair<std::string, double>> log1 = {
        {"fea(1)", 3.0},
        {"fea(2)", 1.0},
        {"fea(3)", 6.0},
        {"fea(4)", 2.0}
    };
    _obj._reset_algebraic_expression(log1, &_obj._collector);
    const std::unordered_map<uint64_t, uint64_t> equation_map;
    _obj._calculate_exps(equation_map, &_obj._postfix_exps, &_obj._collector);
    EXPECT_TRUE(_obj._collector.find(888) != _obj._collector.end());
    EXPECT_TRUE(_obj._collector.find(999) != _obj._collector.end());
    EXPECT_EQ(6, _obj._collector.size());
    auto ptr1 = _obj._collector[888];
    OperandExpress* l = dynamic_cast<OperandExpress*>(ptr1.get());
    EXPECT_TRUE(l != NULL);
    EXPECT_DOUBLE_EQ(2.0, l->get_double_value());
}

TEST_F(ExpTestSuite, test_exp_reset) {
    // express1 : sign 888 fea(1) - fea(2)
    // express2 : sign 999 fea(3) * fea(4)
    // two logs calculate    
    uint64_t sign1 = 0;
    anti::baselib::SignUtil::create_sign_md64("888", &sign1);

    uint64_t sign2 = 0;
    anti::baselib::SignUtil::create_sign_md64("999", &sign2);
    // express1
    std::vector<std::pair<std::string, std::string>> exp1 = {
        {"algebra", "fea(1)"},
        {"algebra", "fea(2)"},
        {"binary_operator", "-"}
    };
    std::vector<ExpressInterfacePtr> postfix_exp;
    set_exp_vector(exp1, &postfix_exp);
    _obj._postfix_exps.emplace_back(std::make_pair(sign1, postfix_exp));
    // express2
    std::vector<std::pair<std::string, std::string>> exp2 = {
        {"algebra", "fea(3)"},
        {"algebra", "fea(4)"},
        {"binary_operator", "*"}
    };
    set_exp_vector(exp2, &postfix_exp);
    _obj._postfix_exps.emplace_back(std::make_pair(sign2, postfix_exp));

    // log1
    std::vector<std::pair<std::string, double>> log1 = {
        {"fea(1)", 3.0},
        {"fea(2)", 1.0},
        {"fea(3)", 6.0},
        {"fea(4)", 2.0}
    };
    _obj.reset(log1);     
    double res = 0.0;
    EXPECT_TRUE(_obj.calculate("888", &res));
    EXPECT_DOUBLE_EQ(3.0 - 1.0, res);
    EXPECT_TRUE(_obj.calculate("999", &res));
    EXPECT_DOUBLE_EQ(6.0 * 2.0, res);
    // log2
    std::vector<std::pair<std::string, double>> log2 = {
        {"fea(1)", 5.0},
        {"fea(2)", 1.0},
        {"fea(3)", 3.0},
        {"fea(4)", 2.0}
    };
    _obj.reset(log2);     
    EXPECT_TRUE(_obj.calculate("888", &res));
    EXPECT_DOUBLE_EQ(5.0 - 1.0, res);
    EXPECT_TRUE(_obj.calculate("999", &res));
    EXPECT_DOUBLE_EQ(3.0 * 2.0, res);
}

TEST_F(ExpTestSuite, test_parse_equation) {
    std::string equation = "fea(123) = fea(456) + fea(789)";
    std::string exp = "";
    uint64_t sign = 0UL;
    std::unordered_set<std::string> inner_atoms;
    std::unordered_set<std::string> unknown;
    EXPECT_TRUE(_obj._parse_equation(
            equation, &_obj._equation_map, &exp, &sign, &inner_atoms, &unknown));
    uint64_t sign1 = 0;
    anti::baselib::SignUtil::create_sign_md64("fea(123)", &sign1);

    uint64_t sign2 = 0;
    anti::baselib::SignUtil::create_sign_md64("fea(456) + fea(789)", &sign2);
    uint64_t entire_sign = 0;
    anti::baselib::SignUtil::create_sign_md64("fea(123) = fea(456) + fea(789)", &entire_sign);
    EXPECT_EQ(2, _obj._equation_map.size());
    EXPECT_EQ(sign2, _obj._equation_map[sign1]); 
    EXPECT_EQ(sign2, _obj._equation_map[entire_sign]); 
    EXPECT_EQ("fea(456) + fea(789)", exp);
    EXPECT_EQ(sign2, sign);
    EXPECT_EQ(1, inner_atoms.size());
    EXPECT_TRUE(inner_atoms.find("fea(123)") != inner_atoms.end());
}

TEST_F(ExpTestSuite, test_parse_multi_equation) {
    std::string equation = " fea(001) = fea(123) = fea(456) + fea(789)";
    std::string exp = "";
    uint64_t sign = 0UL;
    std::unordered_set<std::string> inner_atoms;
    std::unordered_set<std::string> unknown;
    EXPECT_TRUE(_obj._parse_equation(
            equation, &_obj._equation_map, &exp, &sign, &inner_atoms, &unknown));
    uint64_t sign1 = 0;
    anti::baselib::SignUtil::create_sign_md64("fea(001)", &sign1);
    uint64_t sign2 = 0;
    anti::baselib::SignUtil::create_sign_md64("fea(123)", &sign2);
    uint64_t sign3 = 0;
    anti::baselib::SignUtil::create_sign_md64("fea(456) + fea(789)", &sign3);
    uint64_t entire_sign = 0;
    anti::baselib::SignUtil::create_sign_md64("fea(001) = fea(123) = fea(456) + fea(789)", &entire_sign);
    EXPECT_EQ(3, _obj._equation_map.size());
    EXPECT_EQ(sign3, _obj._equation_map[sign1]); 
    EXPECT_EQ(sign3, _obj._equation_map[sign2]); 
    EXPECT_EQ(sign3, _obj._equation_map[entire_sign]); 
    EXPECT_EQ("fea(456) + fea(789)", exp);
    EXPECT_EQ(sign3, sign);
    EXPECT_EQ(2, inner_atoms.size()); 
    EXPECT_TRUE(inner_atoms.find("fea(001)") != inner_atoms.end()); 
    EXPECT_TRUE(inner_atoms.find("fea(123)") != inner_atoms.end()); 
}

TEST_F(ExpTestSuite, test_interfaces) {
    std::vector<std::string> equations = {
        "fea(999) = (fea(1) + f(2))* fea3 -1",
        "fea(888) =(0.5 +fea(4) *3)"
    };
    std::vector<std::string> atoms = {
        "fea(1)", "f(2)", "fea3", "fea(4)"
    };
    EXPECT_TRUE(_obj.init(equations, atoms));
    // log1
    std::vector<std::pair<std::string, double>> log1 = {
        {"fea(1)", 3.0},
        {"f(2)", 1.0},
        {"fea3", 6.0},
        {"fea(4)", 2.0}
    };
    _obj.reset(log1);     
    double res = 0.0;
    EXPECT_TRUE(_obj.calculate("fea(999)", &res));
    EXPECT_DOUBLE_EQ((3.0 + 1.0) * 6.0 - 1, res);
    res = 0.0;
    EXPECT_TRUE(_obj.calculate("fea(999) = (fea(1) + f(2))* fea3 -1", &res));
    EXPECT_DOUBLE_EQ((3.0 + 1.0) * 6.0 - 1, res);
    res = 0.0;
    EXPECT_TRUE(_obj.calculate(" (fea(1) + f(2))* fea3 -1", &res));
    EXPECT_DOUBLE_EQ((3.0 + 1.0) * 6.0 - 1, res);
    res = 0.0;
    EXPECT_TRUE(_obj.calculate("fea(888)", &res));
    EXPECT_DOUBLE_EQ((0.5 + 2.0 * 3), res);
    res = 0.0;
    EXPECT_TRUE(_obj.calculate("fea(888) =(0.5 +fea(4) *3)", &res));
    EXPECT_DOUBLE_EQ((0.5 + 2.0 * 3), res);
    res = 0.0;
    EXPECT_TRUE(_obj.calculate("(0.5 +fea(4) *3)", &res));
    EXPECT_DOUBLE_EQ((0.5 + 2.0 * 3), res);

    // log2
    std::vector<std::pair<std::string, double>> log2 = {
        {"fea(1)", 5.0},
        {"f(2)", 1.0},
        {"fea3", 3.0},
        {"fea(4)", 2.7}
    };
    _obj.reset(log2);     
    EXPECT_TRUE(_obj.calculate("fea(999)", &res));
    EXPECT_DOUBLE_EQ((5.0 + 1.0) * 3.0 - 1, res);
    res = 0.0;
    EXPECT_TRUE(_obj.calculate("fea(999) = (fea(1) + f(2))* fea3 -1", &res));
    EXPECT_DOUBLE_EQ((5.0 + 1.0) * 3.0 - 1, res);
    res = 0.0;
    EXPECT_TRUE(_obj.calculate(" (fea(1) + f(2))* fea3 -1", &res));
    EXPECT_DOUBLE_EQ((5.0 + 1.0) * 3.0 - 1, res);
    res = 0.0;
    EXPECT_TRUE(_obj.calculate("fea(888)", &res));
    EXPECT_DOUBLE_EQ((0.5 + 2.70 * 3), res);
    res = 0.0;
    EXPECT_TRUE(_obj.calculate("fea(888) =(0.5 +fea(4) *3)", &res));
    EXPECT_DOUBLE_EQ((0.5 + 2.70 * 3), res);
    res = 0.0;
    EXPECT_TRUE(_obj.calculate("(0.5 +fea(4) *3)", &res));
    EXPECT_DOUBLE_EQ((0.5 + 2.70 * 3), res);
}

TEST_F(ExpTestSuite, test_carespace_1) {
    std::vector<std::string> equations = {
        "[#TAG1]=[1]&&[2]",
        "[#TAG2]=[1] || [#TAG1]",
    };
    std::vector<std::string> atoms = { 
        "[1]", "[2]"
    };
    EXPECT_TRUE(_obj.init(equations, atoms));
    std::vector<std::pair<std::string, bool>> log1 = {
        {"[1]", true},
        {"[2]", false},
    };
    _obj.reset(log1);
    bool res = false;
    EXPECT_TRUE(_obj.calculate("[#TAG1]", &res)); 
    EXPECT_DOUBLE_EQ(true && false, res); 
    EXPECT_TRUE(_obj.calculate("[1]&&[2]", &res)); 
    EXPECT_DOUBLE_EQ(true && false, res); 
    EXPECT_TRUE(_obj.calculate("[#TAG2]", &res)); 
    EXPECT_DOUBLE_EQ(true || false, res); 
}

TEST_F(ExpTestSuite, test_carespace) {
    std::vector<std::string> equations = {
        "[#TAG1]=[1]&&[2]",
        "[#TAG2]=[1]||[2]",
        "[#TAG3]=[#TAG2] && [#TAG1]",
        "[#TAG4] =  ( [1] || [2])  && [1]"
    };
    std::vector<std::string> atoms = { 
        "[1]", "[2]"
    };
    EXPECT_TRUE(_obj.init(equations, atoms));
    std::vector<std::pair<std::string, bool>> log1 = {
        {"[1]", true},
        {"[2]", false},
    };
    _obj.reset(log1);
    bool res = false;
    EXPECT_TRUE(_obj.calculate("[#TAG1]", &res)); 
    EXPECT_DOUBLE_EQ(true && false, res); 
    EXPECT_TRUE(_obj.calculate("[1]&&[2]", &res)); 
    EXPECT_DOUBLE_EQ(true && false, res); 
    EXPECT_TRUE(_obj.calculate("[#TAG2]", &res)); 
    EXPECT_DOUBLE_EQ(true || false, res); 
    EXPECT_TRUE(_obj.calculate("[1]||[2]", &res)); 
    EXPECT_DOUBLE_EQ(true || false, res); 
    EXPECT_TRUE(_obj.calculate("[#TAG3]", &res)); 
    EXPECT_DOUBLE_EQ((true && false) && (true || false), res); 
    EXPECT_TRUE(_obj.calculate("[#TAG2] && [#TAG1]", &res)); 
    EXPECT_DOUBLE_EQ((true && false) && (true || false), res); 

    EXPECT_TRUE(_obj.calculate("[#TAG4]", &res)); 
    EXPECT_DOUBLE_EQ((true||false) && true, res); 

    EXPECT_TRUE(_obj.calculate("  ( [1] || [2])  && [1]", &res)); 
    EXPECT_DOUBLE_EQ((true||false) && true, res); 
}

TEST_F(ExpTestSuite, test_carespace_tag_and_append) { 
    std::vector<std::string> equations = {
        "[#TAG1]=[1][2]",
        "[#TAG2]=[1]||[2]",
        "[#TAG3]=[#TAG2][#TAG1]",
        "[#TAG4] =  ( [1] || [2])  && [1]"
    };
    std::vector<std::string> atoms = { 
        "[1]", "[2]"
    };
    for (const auto& str : equations) {
        _obj.append(str, atoms);
    }
    std::vector<std::pair<std::string, bool>> log1 = {
        {"[1]", true},
        {"[2]", false},
    };
    _obj.reset(log1);
    bool res = false;
    EXPECT_TRUE(_obj.calculate("[#TAG1]", &res)); 
    EXPECT_DOUBLE_EQ(true && false, res); 
    EXPECT_TRUE(_obj.calculate("[1][2]", &res)); 
    EXPECT_DOUBLE_EQ(true && false, res); 
    EXPECT_TRUE(_obj.calculate("[#TAG2]", &res)); 
    EXPECT_DOUBLE_EQ(true || false, res); 
    EXPECT_TRUE(_obj.calculate("[1]||[2]", &res)); 
    EXPECT_DOUBLE_EQ(true || false, res); 
    EXPECT_TRUE(_obj.calculate("[#TAG3]", &res)); 
    EXPECT_DOUBLE_EQ((true && false) && (true || false), res); 
    EXPECT_TRUE(_obj.calculate("[#TAG2][#TAG1]", &res)); 
    EXPECT_DOUBLE_EQ((true && false) && (true || false), res); 

    EXPECT_TRUE(_obj.calculate("[#TAG4]", &res)); 
    EXPECT_DOUBLE_EQ((true||false) && true, res); 

    EXPECT_TRUE(_obj.calculate("  ( [1] || [2])  && [1]", &res)); 
    EXPECT_DOUBLE_EQ((true||false) && true, res); 
}

TEST_F(ExpTestSuite, test_carespace_with_no_and) { 
    std::vector<std::string> equations = {
        "[#TAG1]=[1][2]",
        "[#TAG2]=[1]||[2]",
        "[#TAG3]=[#TAG2][#TAG1]",
        "[#TAG4] =  ( [1] || [2])  && [1]"
    };
    std::vector<std::string> atoms = { 
        "[1]", "[2]"
    };
    EXPECT_TRUE(_obj.init(equations, atoms));
    std::vector<std::pair<std::string, bool>> log1 = {
        {"[1]", true},
        {"[2]", false},
    };
    _obj.reset(log1);
    bool res = false;
    EXPECT_TRUE(_obj.calculate("[#TAG1]", &res)); 
    EXPECT_DOUBLE_EQ(true && false, res); 
    EXPECT_TRUE(_obj.calculate("[1][2]", &res)); 
    EXPECT_DOUBLE_EQ(true && false, res); 
    EXPECT_TRUE(_obj.calculate("[#TAG2]", &res)); 
    EXPECT_DOUBLE_EQ(true || false, res); 
    EXPECT_TRUE(_obj.calculate("[1]||[2]", &res)); 
    EXPECT_DOUBLE_EQ(true || false, res); 
    EXPECT_TRUE(_obj.calculate("[#TAG3]", &res)); 
    EXPECT_DOUBLE_EQ((true && false) && (true || false), res); 
    EXPECT_TRUE(_obj.calculate("[#TAG2][#TAG1]", &res)); 
    EXPECT_DOUBLE_EQ((true && false) && (true || false), res); 

    EXPECT_TRUE(_obj.calculate("[#TAG4]", &res)); 
    EXPECT_DOUBLE_EQ((true||false) && true, res); 

    EXPECT_TRUE(_obj.calculate("  ( [1] || [2])  && [1]", &res)); 
    EXPECT_DOUBLE_EQ((true||false) && true, res); 
}

TEST_F(ExpTestSuite, test_carespace_split) { 
    std::vector<std::string> equations = {
        "[#TAG1]=['accessid':='1','29','31','37']&&['cmatch':='204','227','248','249']&&['cntname'NI'473.whitecnt']&&['cmatch'NI'q.whitecmatch']",
        "[#TAG2]=(['accessid':='1','29','31','37']||['baiduid'HA])&&(['wise_bwsid'!='2']||[#TAG1])"
    };
    std::vector<std::string> atoms = {
        "['accessid':='1','29','31','37']", 
        "['cmatch':='204','227','248','249']",
        "['cntname'NI'473.whitecnt']", 
        "['cmatch'NI'q.whitecmatch']",
        "['baiduid'HA]",
        "['wise_bwsid'!='2']",
    };
    EXPECT_TRUE(_obj.init(equations, atoms));
}

TEST_F(ExpTestSuite, test_init_failed) {
    {
        std::vector<std::string> equations = {
            "fea(999) = (fea(1) + f(2)* fea3 -1"
        };
        std::vector<std::string> atoms = {
            "fea(1)", "f(2)", "fea3", "fea(4)"
        };
        EXPECT_FALSE(_obj.init(equations, atoms));
    }
    {
        std::vector<std::string> equations = {
            "fea(888) =(0.5 +fea(4) *+3)"
        };
        std::vector<std::string> atoms = {
            "fea(1)", "f(2)", "fea3", "fea(4)"
        };
        EXPECT_FALSE(_obj.init(equations, atoms));
    }
    {
        std::vector<std::string> equations = {
            "fea(888) =(0.5 +fea(4) +3) 1"
        };
        std::vector<std::string> atoms = {
            "fea(1)", "f(2)", "fea3", "fea(4)"
        };
        EXPECT_FALSE(_obj.init(equations, atoms));
    }
    {
        std::vector<std::string> equations = {
            "fea(888) =(0.5 +fea(4) *3) -"
        };
        std::vector<std::string> atoms = {
            "fea(1)", "f(2)", "fea3", "fea(4)"
        };
        EXPECT_FALSE(_obj.init(equations, atoms));
    }
    {
        std::vector<std::string> equations = {
            "[#TAG1]=[1][2]([3])"
        };
        std::vector<std::string> atoms = {
            "[1]", "[2]", "[3]"
        };
        EXPECT_FALSE(_obj.init(equations, atoms));
    }
    {
        std::vector<std::string> equations = {
            "[#TAG1]=[1][2][3]("
        };
        std::vector<std::string> atoms = {
            "[1]", "[2]", "[3]"
        };
        EXPECT_FALSE(_obj.init(equations, atoms));
    }
    {
        std::vector<std::string> equations = {
            "[#TAG2]=[1]||[2]&&([3])"
        };
        std::vector<std::string> atoms = {
            "[1]", "[2]", "[3]"
        };
        EXPECT_TRUE(_obj.init(equations, atoms));
    }
    {
        // uniq
        std::vector<std::string> equations = {
            "[#TAG2]=[1]||[2]&&([3])"
        };
        std::vector<std::string> atoms = {
            "[1]", "[2]", "[3]"
        };
        EXPECT_TRUE(_obj.init(equations, atoms));
    }
    {
        // redefine
        std::vector<std::string> equations = {
            "[#TAG2]=[1]||[2]&&([3]||[4])"
        };
        std::vector<std::string> atoms = {
            "[1]", "[2]", "[3]", "[4]"
        };
        EXPECT_FALSE(_obj.init(equations, atoms));
    }
    {
        std::vector<std::string> equations = {
            "['price':<200]",
            "['price':>500]",
            "['price':>50]['price':<80]",
            "['price':>50]['price':<200]",
            "['price':>50]['price':<80]||['price':<200]",
            "['price':>50]['price':<80]||['price':<20]"
        };
        std::vector<std::string> atoms = {
            "[1]", "[2]", "[3]", "[4]"
        };
        EXPECT_FALSE(_obj.init(equations, atoms));
    }
}

TEST_F(ExpTestSuite, test_ln_exps) { 
    {
        std::vector<std::string> equations = {
            "x = ln(100) + ln10 + ln(5 + 6) + [1]"
        };
        std::vector<std::string> atoms = {
            "[1]", "[2]", "[3]"
        };
        EXPECT_TRUE(_obj.init(equations, atoms));
        std::vector<std::pair<std::string, double>> log1 = {
            {"[1]", 5.5},
            {"[2]", 6.7},
        };
        _obj.reset(log1);
        double res = false;
        EXPECT_TRUE(_obj.calculate("x", &res)); 
        EXPECT_DOUBLE_EQ(std::log(100) + std::log(10) + std::log(5 + 6) + 5.5, res); 
    }
}

TEST_F(ExpTestSuite, test_one) { 
    {
        std::vector<std::string> equations = {
            "['price'HA]"
        };
        std::vector<std::string> atoms = {
            "['price'HA]"
        };
        EXPECT_TRUE(_obj.init(equations, atoms));
        std::vector<std::pair<std::string, bool>> log1 = {
            {"['price'HA]", true}
        };
        _obj.reset(log1);
        bool res = false;
        EXPECT_TRUE(_obj.calculate("['price'HA]", &res)); 
        EXPECT_DOUBLE_EQ(true, res); 
    }
}

TEST_F(ExpTestSuite, test_cos) {
    {
        std::vector<std::string> equations = {
            "x = cos(fea1, fea2)"
        };
        std::vector<std::string> atoms = {
            "fea1", "fea2", "[3]"
        };
        EXPECT_TRUE(_obj.init(equations, atoms));
        std::vector<std::pair<std::string, Vector>> log1 = {
            {"fea1", {5.5, 1.0}},
            {"fea2", {6.7, 2.8}},
        };
        _obj.reset(log1);
        double res = false;
        EXPECT_TRUE(_obj.calculate("x", &res)); 
        EXPECT_DOUBLE_EQ(
                (5.5 * 6.7 + 1.0 * 2.8) / 
                (sqrt(5.5 * 5.5 + 1.0 * 1.0) * sqrt(6.7 * 6.7 + 2.8 * 2.8)), res); 
    }

}

TEST_F(ExpTestSuite, test_bug_logic_or) { 
    std::vector<std::string> equations = {
        "[#tag1] = ['cmatch':='204','227','248','249']",
        "[#tag2] = ['accessid':='1','31']['flow_group':='app_lu_mid']",
        "[#tag3] = [#tag2] &&  ['cmatch':='354'] && [#tag1]",
        "[#tag2]||['accessid':='1','31']['cmatch':='354']",
    };
    std::vector<std::string> atoms = { 
        "['cmatch':='204','227','248','249']", "['accessid':='1','31']", 
        "['flow_group':='app_lu_mid']", "['cmatch':='354']"
    };
    for (const auto& a : equations) {
        _obj.append(a, atoms);
    }
    std::vector<std::pair<std::string, bool>> log1 = {
        {"['cmatch':='204','227','248','249']", true},
        {"['accessid':='1','31']", true},
        {"['flow_group':='app_lu_mid']", true},
        {"['cmatch':='354']", false},
    };
    for (uint32_t i = 0; i < 5; ++i) {
        _obj.reset(log1);
        bool res = false;
        EXPECT_TRUE(_obj.calculate("[#tag2]||['accessid':='1','31']['cmatch':='354']", &res)); 
        EXPECT_EQ(true, res);
        EXPECT_TRUE(_obj.calculate("[#tag3]", &res));
        EXPECT_EQ(false, res);
    }
    std::vector<std::pair<std::string, bool>> log2 = {
        {"['cmatch':='204','227','248','249']", false},
        {"['accessid':='1','31']", true},
        {"['flow_group':='app_lu_mid']", false},
        {"['cmatch':='354']", false},
    };
    for (uint32_t i = 0; i < 5; ++i) {
        _obj.reset(log2);
        bool res = false;
        EXPECT_TRUE(_obj.calculate("[#tag2]||['accessid':='1','31']['cmatch':='354']", &res)); 
        EXPECT_EQ(false, res);
        EXPECT_TRUE(_obj.calculate("[#tag3]", &res));
        EXPECT_EQ(false, res);
    }
}

} // common_lib
} // themis
} // anti

/* vim: set ts=4 sw=4: */

