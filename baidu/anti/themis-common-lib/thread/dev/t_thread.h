// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// @brief 
// The Backend Threadpool for runing jobs. Accelerate the progress
// of main-logic. Return the future<T> to Invoke for waiting the job
// ends.
//
// 1) In themis mainly work scene, the jobs are created by an main-thread.
// Then those jobs are running, and the main-thread waits them finish. So
// the future<T> will very useful.
// 2) most of jobs created by main-thread can be run parallelly in any way.
// But if the jobs need write to the same file, they should be excuted one by one.
// thence, Threadpool support user submit job with an ID,
// the job with the same id will not excuted parallelly.
//
// to achive this requirements, the threadpool holds two type of job queue.
// 
// 1) the parallel_q for ALL threads
// 2) the order_q[] for Writing threads
// 
// and if exists N run-threads, the [0-M](M<N) threads will execute order_q[] as a writing thread.

#ifndef APP_ECOM_ANTI_THEMIS_COMMON_LIB_THREAD_H
#define APP_ECOM_ANTI_THEMIS_COMMON_LIB_THREAD_H

#include <thread>
#include <functional>
#include <future>
#include <chrono>
#include <concurrentqueue/blockingconcurrentqueue.h>

namespace anti {
namespace themis {
namespace common_lib {
namespace thread {

class FunctionWrapper {
private:
    struct impl_base {
        virtual void call() = 0;
        virtual ~impl_base() {}
    };

    std::unique_ptr<impl_base> _impl;
    
    template<typename F>
    struct impl_type: impl_base {
        F f;
        impl_type(F&& f_): f(std::move(f_)) {}
        void call() { f(); }
    };

public:
    template<typename F>
    FunctionWrapper(F&& f) : _impl(new impl_type<F>(std::move(f))) {}

    void operator()() { _impl->call(); }

    FunctionWrapper() = default;

    FunctionWrapper(FunctionWrapper&& rhs): _impl(std::move(rhs._impl)) {}

    FunctionWrapper& operator=(FunctionWrapper&& rhs) {
        _impl = std::move(std::move(rhs._impl));
        return *this;
    }

    FunctionWrapper(const FunctionWrapper&) = delete;
    FunctionWrapper(FunctionWrapper&) = delete;
    FunctionWrapper& operator=(const FunctionWrapper&) = delete;
};

class ThreadPool {
    typedef moodycamel::BlockingConcurrentQueue<FunctionWrapper> BlockingConcurrentQueue;
public:
    ThreadPool();
    ~ThreadPool();

    // @brief,
    // Pease notice all args will passed by value when sumit job with paraments.
    // IF u want pass refer variables, use pointer!
    template<typename Func, typename... Args>
    std::future<typename std::result_of<Func(Args...)>::type>
    submit_with_id(int32_t jobid, Func f, Args... args) {
        typedef typename std::result_of<Func(Args...)>::type result_type;

        std::packaged_task<result_type()> task(std::move(
                    std::bind(f, args...)));
        std::future<result_type> res(task.get_future());

        enqueue_job(jobid, std::move(task));

        return res;
    }

    template<typename Func, typename... Args>
    std::future<typename std::result_of<Func(Args...)>::type>
    submit(Func f, Args... args) {
        return submit_with_id(0, f, args...);
    }

    static ThreadPool* instance() {
        static ThreadPool pool;
        return &pool;
    }

private:
    void worker_thread(uint32_t thread_id) {
        while (!_exit) {
            FunctionWrapper wrapper;
            // execut self-order queue firstly
            if (thread_id + 1 < _order_q.size()
                    && _order_q[thread_id + 1].try_dequeue(wrapper)) {
                wrapper();
            } else if (_order_q[0].wait_dequeue_timed(wrapper, std::chrono::milliseconds(200))) {
                wrapper();
            } else {
                std::this_thread::yield();
            }
        }
    }

    template<typename T>
    void enqueue_job(uint32_t id, T&& task) {
        int pos = 0;
        if (id > 0) {
            pos = (id % (_order_q.size() - 1) + 1);
        }

        _order_q[pos].enqueue(std::move(task));
    }


    std::atomic_bool _exit;
    std::vector<std::thread> _threads;

    // each queue can only consumed by one thread, except order_q[0].
    // so the job with the same id will not executed parallel.
    //
    // the order_q[0] treated as parallel queue. every thread can grab
    // job from it.
    std::vector<BlockingConcurrentQueue> _order_q;
};

// ----------------
// non-member function

// @brief,
// Pease notice all args will passed by value when sumit job with paraments.
// IF u want pass refer variables, use pointer!
template<typename Func, typename... Args>
std::future<typename std::result_of<Func(Args...)>::type>
submit(Func f, Args... args) {
    return ThreadPool::instance()->submit(f, args...);
}

template<typename Func, typename... Args>
std::future<typename std::result_of<Func(Args...)>::type>
submit_with_id(int32_t id, Func f, Args... args) {
    return ThreadPool::instance()->submit_with_id(id, f, args...);
}

}
}
}
}

#endif
