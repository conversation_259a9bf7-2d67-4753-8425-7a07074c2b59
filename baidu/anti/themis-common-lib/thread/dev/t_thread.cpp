// Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
// @brief 

#include <gflags/gflags.h>
#include "t_thread.h"

namespace anti {
namespace themis {
namespace common_lib {

DECLARE_int32(thread_pool_capacity);

namespace thread {

ThreadPool::ThreadPool() : _exit(false) {
    uint32_t cnt = std::thread::hardware_concurrency();
    if (FLAGS_thread_pool_capacity >= 1) {
        cnt = FLAGS_thread_pool_capacity;
    }
    try {
        uint32_t q_size = cnt / 2 + 1;
        _order_q.resize(q_size > 1 ? q_size : 2);
        for (uint32_t i = 0; i < cnt; ++i) {
            _threads.push_back(
                    std::thread(&ThreadPool::worker_thread, this, i));
        }
    } catch (...) {
        _exit = true;
    }
}

ThreadPool::~ThreadPool() {
    _exit = true;
    for (auto& th : _threads) { th.join(); }
}

}
}
}
}
