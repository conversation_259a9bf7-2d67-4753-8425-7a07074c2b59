// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// @brief 

#include <gtest/gtest.h>
#include "t_thread.h"

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    //google::ParseCommandLineFlags(&argc, &argv, true);
    return RUN_ALL_TESTS();
}

namespace anti {
namespace themis {
namespace common_lib {
namespace thread {

class ThreadPoolTestSuite : public ::testing::Test {
public:
    ThreadPoolTestSuite() {}
    ~ThreadPoolTestSuite() {}

    virtual void SetUp() {}
    virtual void TearDown() {}

private:
    ThreadPool _pool;
};

std::atomic<int32_t> v;
bool invoke() {
    for (int i = 0; i < 10; ++i) { v += 1; }
    return true;
}

TEST_F(ThreadPoolTestSuite, test) {
    auto ret1 = _pool.submit(invoke);
    auto ret2 = _pool.submit(invoke);

    EXPECT_TRUE(ret1.get());
    EXPECT_TRUE(ret2.get());

    EXPECT_EQ(v, 20);
}

class RunClass {
public:
    void func() { ; }
};

TEST_F(ThreadPoolTestSuite, test_class_function) {
    RunClass r;
    auto to = _pool.submit(std::bind(&RunClass::func, &r));

    to.get();
}

TEST_F(ThreadPoolTestSuite, test_lambda) {
    auto func = [&] () {
        return true;
    };

    auto run = _pool.submit(func);
    run.get();
}

int non_member(int a) {
    return a * 2;
}

TEST_F(ThreadPoolTestSuite, submit_non_param) {
    auto ret = _pool.submit(invoke);
    ret.get();
}

TEST_F(ThreadPoolTestSuite, non_member_submit) {
    v = 0;
    auto ret = submit(invoke);

    EXPECT_TRUE(ret.get());
    EXPECT_EQ(v, 10);
}

TEST_F(ThreadPoolTestSuite, submit_non_member) {
    auto ret = _pool.submit(std::bind(non_member, 12));
}

TEST_F(ThreadPoolTestSuite, submit_lambda_func) {
    auto func = [] (int* a) {
        *a = 15;
        return true;
    };

    int a;
    auto ret = _pool.submit(func, &a);

    ret.get();
    EXPECT_EQ(a, 15);
}

void wait_for_ms(uint32_t ms) {
    auto start = std::chrono::high_resolution_clock::now();
    auto end = start + std::chrono::milliseconds(ms * 10);
    do {
        std::this_thread::yield();
    } while (std::chrono::high_resolution_clock::now() < end);
}

TEST_F(ThreadPoolTestSuite, submit_job_with_id) {
    auto func = [] (uint32_t ms, uint32_t r2, std::vector<uint32_t>* ret) {
        wait_for_ms(ms);

        ret->push_back(r2);
        return true;
    };

    // -- no jobid, execute unorder --
    {
        std::vector<uint32_t> res;
        std::vector<std::future<bool>> r;
        uint32_t cnt = 10;
        for (uint32_t i = 0; i < cnt; ++i) {
            auto ret = _pool.submit(func, cnt - i, i, &res);
            r.push_back(std::move(ret));
        }
        for (auto& e : r) { e.get(); }
        for (uint32_t i = 0; i < cnt; ++i) { EXPECT_EQ(res[i], cnt - i - 1); }
    }

    {
        std::vector<uint32_t> res;
        std::vector<std::future<bool>> r;
        uint32_t cnt = 10;
        for (uint32_t i = 0; i < cnt; ++i) {
            auto ret = _pool.submit_with_id(cnt, func, cnt - i, i, &res);
            r.push_back(std::move(ret));
        }
        for (auto& e : r) { e.get(); }
        for (uint32_t i = 0; i < cnt; ++i) { EXPECT_EQ(res[i], i); }
    }
}

}
}
}
}
