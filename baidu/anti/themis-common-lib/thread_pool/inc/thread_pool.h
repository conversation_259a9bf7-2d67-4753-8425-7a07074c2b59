// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @brief 

#ifndef APP_ECOM_ANTI_THEMIS_WORKER_WORKER_LIB_THREAD_THREAD_POOL_H
#define APP_ECOM_ANTI_THEMIS_WORKER_WORKER_LIB_THREAD_THREAD_POOL_H

#include <thread>
#include <vector>
#include <memory>
#include <mutex>
#include <gflags/gflags.h>
#include "task_queue.h"
#include "condition_variable"

namespace anti {
namespace themis {
namespace common_lib {

DECLARE_int32(thread_pool_sleep_time);
DECLARE_int32(thread_pool_capacity);

class ThreadPool {
private:
    typedef std::unique_ptr<ThTask> TaskPtr;
public:
    ThreadPool(int32_t backend_threadnum);
    ~ThreadPool();

    template<typename T>
    bool add_task(T&& task) {
        return add_task(task, &_task_queue, &_mutex, &_cv);
    }

    template<typename T>
    bool add_data_export_task(T&& task) {
        return add_task(task, &_data_out_queue, &_write_mutex, &_write_cv);
    }

    void exit();
    static ThreadPool* instance();


private:
    void backend_run(
            TaskQueue* task_queue,
            std::mutex* mutex,
            std::condition_variable* cv);

    template<typename T>
    bool add_task(
            T&& task,
            TaskQueue* queue,
            std::mutex* mutex,
            std::condition_variable* cv) {
        std::unique_ptr<ThTask> obj;
        using def_type = typename std::remove_reference<T>::type;
        obj.reset(new def_type(std::move(task)));
        std::unique_lock<std::mutex> locker(*mutex);
        queue->produce(std::move(obj));
        cv->notify_one();
        return true;
    }


private:
    bool _init(int32_t backend_threadnum);
    TaskQueue _task_queue;
    std::vector<std::unique_ptr<std::thread>> _threads;

    // Special async thread only for output Data.
    // because most of data will be write into files.
    // and file only receive data one by one,
    // multi-thread write one file will cause problem.
    // Here using one special thread to output Data
    TaskQueue _data_out_queue;
    std::unique_ptr<std::thread> _write_thread;

    std::mutex _mutex;
    std::condition_variable _cv;
    std::mutex _write_mutex;
    std::condition_variable _write_cv;
};

template<typename T>
bool add_task(T&& task) {
    return ThreadPool::instance()->add_task(std::move(task));
}

template<typename T>
bool add_data_export_task(T&& task) {
    return ThreadPool::instance()->add_data_export_task(std::move(task));
}

template<typename T>
bool add_func(T&& func) {
    using def_type = typename std::remove_reference<T>::type;
    return add_task(SimpleTask<def_type>(std::move(func)));
}

template<typename T>
bool add_data_export_func(T&& func) {
    using def_type = typename std::remove_reference<T>::type;
    return add_data_export_task(SimpleTask<def_type>(std::move(func)));
}

}
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_WORKER_WORKER_LIB_THREAD_THREAD_POOL_H

// Codes are auto generated by God
