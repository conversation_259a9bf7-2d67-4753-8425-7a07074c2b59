// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// <AUTHOR>
// @brief 

#ifndef APP_ECOM_ANTI_THEMIS_WORKER_WORKER_LIB_THREAD_TASK_QUEUE_H
#define APP_ECOM_ANTI_THEMIS_WORKER_WORKER_LIB_THREAD_TASK_QUEUE_H

#include <mutex>
#include <atomic>
#include <list>
#include <memory>

namespace anti {
namespace themis {
namespace common_lib {

template<typename T, typename... Args>
std::unique_ptr<T> make_unique(Args&&... args) {
    return std::unique_ptr<T>(new T(std::forward<Args>(args)...));
}

class ThTask {
public:
    virtual ~ThTask() {}
    virtual bool run() = 0;
};


template<typename Func>
class SimpleTask : public ThTask {
public:
    SimpleTask(SimpleTask&& rhs) : _func(std::move(rhs._func)) {}
    SimpleTask(Func&& func) : _func(std::move(func)) {}

    virtual bool run() { return _func(); }
private:
    Func _func;
};

class TaskQueue {
public:
    TaskQueue() : _exit_flag(false) {}
    ~TaskQueue() {}

    void produce(std::unique_ptr<ThTask>&& rhs) {
        _task_list.emplace_back(std::move(rhs));
    }

    std::unique_ptr<ThTask> consume() {
        std::unique_ptr<ThTask> ret;
        if (_task_list.empty()) { return ret; }

        ret = std::move(_task_list.front());
        _task_list.pop_front();

        return ret;
    }

    bool empty() const {
        return _task_list.size() == 0;
    }

    void exit() { _exit_flag = true; }
    bool is_exit() { return _exit_flag; }

private:
    std::atomic_bool _exit_flag;
    std::list<std::unique_ptr<ThTask>> _task_list;
};

}
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_WORKER_WORKER_LIB_THREAD_TASK_QUEUE_H

// Codes are auto generated by God
