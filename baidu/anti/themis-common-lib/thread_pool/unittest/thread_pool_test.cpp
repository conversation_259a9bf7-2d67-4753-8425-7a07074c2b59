// Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
// @brief 

#include <gtest/gtest.h>
#include <com_log.h>
#include "thread_pool.h"

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    google::ParseCommandLineFlags(&argc, &argv, true);
    anti::themis::common_lib::FLAGS_thread_pool_capacity = 10;
    return RUN_ALL_TESTS();
}

namespace anti {
namespace themis {
namespace common_lib {

class TaskQueueTestSuite : public ::testing::Test {
public:
    TaskQueueTestSuite() {
    }
    ~TaskQueueTestSuite() {}

    virtual void SetUp() {}

    virtual void TearDown() {}

private:
    TaskQueue _task_queue;
};


// ============================================
// ThreadPool Test
TEST(ThreadPoolTestSuite, init_succ) {
    ThreadPool* pool = ThreadPool::instance();
    usleep(100 * 1000);
    EXPECT_EQ(pool->_threads.size(), 10);
}

class SampleTask : public ThTask {
public:
    SampleTask(int32_t* v) : _val(v) {}
    ~SampleTask() {}

    virtual bool run() {
        (*_val)++;
        return true;
    }

private:
    int* _val = NULL;
};

TEST(ThreadPoolTestSuite, add_tasks) {
    ThreadPool* pool = ThreadPool::instance();
    ASSERT_TRUE(pool != NULL);
    int invoke_cnt = 0;
    // add right ref obj;
    pool->add_task(SampleTask(&invoke_cnt));
    // add left ref obj;
    SampleTask obj(&invoke_cnt);
    pool->add_task(obj);
    usleep(100 * 1000);
    EXPECT_EQ(invoke_cnt, 2);
}

TEST(ThreadPoolTestSuite, multi_backend_processor) {
    ThreadPool* pool = ThreadPool::instance();
    int cnt = 1000;
    std::vector<int> arr(cnt, 0);
    for (int i = 0; i < cnt; ++i) {
        pool->add_task(SampleTask(&arr[i]));
    }
    int num = 1;
    pool->add_data_export_task(SampleTask(&num));
    usleep(100 * 1000);
    for (int i = 0; i < cnt; ++i) { EXPECT_EQ(arr[i], 1); }
    EXPECT_EQ(2, num);
    pool->exit();
}

}
}
}
