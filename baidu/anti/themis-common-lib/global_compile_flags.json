{"clang10": {"global_cppflags": [], "global_cflags": ["-Wno-error=array-bounds", "-Wno-error=braced-scalar-init", "-Wno-error=implicit-function-declaration", "-Wno-error=non-literal-null-conversion", "-Wno-error=parentheses", "-Wno-error=sometimes-uninitialized", "-Wno-error=unknown-warning-option", "-Wno-error=unused-command-line-argument", "-Wno-error=unused-function", "-std=c++17"], "global_cxxflags": ["-Wno-error=c++11-narrowing", "-Wno-error=defaulted-function-deleted", "-Wno-error=delete-non-abstract-non-virtual-dtor", "-Wno-error=deprecated-copy", "-Wno-error=deprecated-declarations", "-Wno-error=deprecated-register", "-Wno-error=expansion-to-defined", "-Wno-error=extern-c-compat", "-Wno-error=format-security", "-Wno-error=ignored-attributes", "-Wno-error=implicit-function-declaration", "-Wno-error=implicit-int-float-conversion", "-Wno-error=inconsistent-missing-override", "-Wno-error=literal-conversion", "-Wno-error=logical-op-parentheses", "-Wno-error=misleading-indentation", "-Wno-error=mismatched-tags", "-Wno-error=non-literal-null-conversion", "-Wno-error=null-dereference", "-Wno-error=null-pointer-arithmetic", "-Wno-error=overloaded-virtual", "-Wno-error=parentheses-equality", "-Wno-error=pessimizing-move", "-Wno-error=range-loop-construct", "-Wno-error=reserved-user-defined-literal", "-Wno-error=return-type", "-Wno-error=return-type-c-linkage", "-Wno-error=sign-compare", "-Wno-error=sizeof-pointer-div", "-Wno-error=uninitialized", "-Wno-error=unknown-warning-option", "-Wno-error=unused-command-line-argument", "-Wno-error=unused-const-variable", "-Wno-error=unused-function", "-Wno-error=unused-lambda-capture", "-Wno-error=unused-local-typedef", "-Wno-error=unused-parameter", "-Wno-error=unused-private-field", "-Wno-error=unused-value", "-Wno-error=unused-variable", "-std=c++17"], "global_ldflags": []}, "gcc10": {"global_cppflags": [], "global_cflags": ["-Wno-error=cast-function-type", "-Wno-error=implicit-fallthrough", "-Wno-error=implicit-function-declaration", "-Wno-error=missing-profile", "-fprofile-correction", "-std=c++17"], "global_cxxflags": ["-Wno-error=address", "-Wno-error=address-of-packed-member", "-Wno-error=cast-function-type", "-Wno-error=catch-value", "-Wno-error=class-memaccess", "-Wno-error=conversion", "-Wno-error=deprecated-copy", "-Wno-error=deprecated-declarations", "-Wno-error=float-conversion", "-Wno-error=format-truncation", "-Wno-error=ignored-qualifiers", "-Wno-error=implicit-fallthrough", "-Wno-error=implicit-function-declaration", "-Wno-error=inline", "-Wno-error=literal-suffix", "-Wno-error=maybe-uninitialized", "-Wno-error=misleading-indentation", "-Wno-error=missing-profile", "-Wno-error=nonnull-compare", "-Wno-error=parentheses", "-Wno-error=pessimizing-move", "-Wno-error=sign-compare", "-Wno-error=sizeof-pointer-div", "-Wno-error=stringop-overflow", "-Wno-error=stringop-truncation", "-Wno-error=unused-but-set-variable", "-Wno-error=unused-function", "-Wno-error=unused-local-typedefs", "-faligned-new", "-fpermissive", "-fprofile-correction", "-std=c++17"], "global_ldflags": ["-fprofile-correction"]}, "gcc12": {"global_cppflags": ["-U__const__"], "global_cflags": ["-Wno-error=cast-function-type", "-Wno-error=implicit-fallthrough", "-Wno-error=implicit-function-declaration", "-Wno-error=missing-profile", "-Wno-error=type-limits"], "global_cxxflags": ["-Wno-register", "-Wno-error=type-limits", "-Wno-error=address", "-Wno-error=address-of-packed-member", "-Wno-error=cast-function-type", "-Wno-error=catch-value", "-Wno-error=class-memaccess", "-Wno-error=conversion", "-Wno-error=deprecated-copy", "-Wno-error=deprecated-declarations", "-Wno-error=float-conversion", "-Wno-error=format-truncation", "-Wno-error=ignored-qualifiers", "-Wno-error=implicit-fallthrough", "-Wno-error=implicit-function-declaration", "-Wno-error=inline", "-Wno-error=literal-suffix", "-Wno-error=maybe-uninitialized", "-Wno-error=misleading-indentation", "-Wno-error=missing-profile", "-Wno-error=nonnull-compare", "-Wno-error=parentheses", "-Wno-error=pessimizing-move", "-Wno-error=sign-compare", "-Wno-error=sizeof-pointer-div", "-Wno-error=stringop-overflow", "-Wno-error=stringop-truncation", "-Wno-error=unused-but-set-variable", "-Wno-error=unused-function", "-Wno-error=unused-local-typedefs", "-Wno-deprecated-declarations", "-faligned-new", "-fpermissive", "-Wno-unused-parameter", "-Wno-error=unused-value", "-Wno-error=infinite-recursion", "-Wno-error=ignored-attributes", "-Wno-error=use-after-free", "-Wno-error=dangling-pointer=", "-Wno-error=reorder", "-Wno-error=int-in-bool-context", "-Wno-error=placement-new"], "global_ldflags": []}}