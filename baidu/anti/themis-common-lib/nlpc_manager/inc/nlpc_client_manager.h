// Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
// @Author: chenlin19(<EMAIL>)
//
// @File: nlpc_client_manager.h
// @Brief:
//

#ifndef ANTI_THEMIS_COMMON_LIB_NLPC_CLIENT_MANAGER_H
#define ANTI_THEMIS_COMMON_LIB_NLPC_CLIENT_MANAGER_H

#include <string>
#include <memory>
#include <mutex>
#include <unordered_map>
#include <boost/noncopyable.hpp>
#include <boost/thread/detail/singleton.hpp>
#include <com_log.h>
#include <Configure.h>
#include <gflags/gflags.h>
#include <nlpc_client.h>

namespace anti {
namespace themis {
namespace common_lib {

typedef ::drpc::NLPCClient NLPCClient;
typedef std::shared_ptr<NLPCClient> NLPCClientPtr;

class NLPCClientManager : public boost::noncopyable {
public:
    NLPCClientManager() {}
    ~ NLPCClientManager();

    bool init(const std::string& conf_path, const std::string& conf_file);
    void uninit();

    NLPCClient* get_nlpc_client(const std::string& client_key) const;

private:
    std::unordered_map<std::string, NLPCClientPtr> _nlpc_clients;
};

typedef ::boost::detail::thread::singleton<NLPCClientManager> NLPCClientManagerSingleton;

class NLPCClientManagerGuard {
public:
    NLPCClientManagerGuard(const comcfg::ConfigUnit& conf);
    ~NLPCClientManagerGuard();
};

} // namespace common_lib
} // namespace themis
} //namespace anti

#endif // ANTI_THEMIS_COMMON_LIB_NLPC_CLIENT_MANAGER_H
