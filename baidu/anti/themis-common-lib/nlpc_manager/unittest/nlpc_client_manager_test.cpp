// Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
// @Author: chenlin(<EMAIL>)
//
// @File: nlpc_client_manager_test.cpp
//
// @Readme: if you want to run the test, you should generate the config of nlpc before.
// follow the steps:
// 1. go to http://nlpc.baidu.com/platform/project/new_project to apply a Operator
// 2. get config
//      cd unittest/conf
//      wget http://nlpc.baidu.com/public/newarchConf/config-local-nlpc_2019052714015712221.tar
//      tar xf xxx
//      modify the path in config

#include <gtest/gtest.h>
#include "nlpc_client_manager.h"

namespace anti {
namespace themis {
namespace common_lib {

class NLPCClientManagerTestSuite : public ::testing::Test {
protected:
    virtual void SetUp() {}
    virtual void TearDown() {}
};

TEST(NLPCClientManagerTestSuite, init_case) {
    // no conf
    EXPECT_FALSE(NLPCClientManagerSingleton::instance().init("xxx", "xxx"));
    // no nlpc_conf_path or nlpc_conf_file
    EXPECT_FALSE(NLPCClientManagerSingleton::instance().init("./conf", "nlpc_manager_bad.conf"));
    //init success
    ASSERT_TRUE(NLPCClientManagerSingleton::instance().init("./conf", "nlpc_manager.conf"));
    auto ptr = NLPCClientManagerSingleton::instance().get_nlpc_client("wordrank");
    ASSERT_TRUE(ptr != nullptr);
    auto ptr2 = NLPCClientManagerSingleton::instance().get_nlpc_client("wordrank2");
    ASSERT_TRUE(ptr2 != nullptr);
    auto ptr3 = NLPCClientManagerSingleton::instance().get_nlpc_client("nlpc_wordrank_xxx");
    ASSERT_TRUE(ptr3 == nullptr);
    NLPCClientManagerSingleton::instance().uninit();
}

} // namespace common_lib
} // namespace themis
} // namespcae anti
