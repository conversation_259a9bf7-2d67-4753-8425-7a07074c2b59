// Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
// @Author: chenlin19(<EMAIL>)
//
// @File: nlpc_client_manager.cpp
// @Brief:
//

#include <mutex>
#include <gflags/gflags.h>
#include <com_log.h>
#include "nlpc_client_manager.h"

DEFINE_bool(open_nlpc, false, "open nlpc");

namespace anti {
namespace themis {
namespace common_lib {

NLPCClientManager::~NLPCClientManager() {
    CDEBUG_LOG("deconstruct NLPCClientManager");
    uninit();
}

bool NLPCClientManager::init(
        const std::string& conf_path,
        const std::string& conf_file) {
    static std::mutex _mu;
    std::unique_lock<std::mutex> lock(_mu);
    comcfg::Configure conf;
    if (conf.load(conf_path.c_str(), conf_file.c_str()) != 0) {
        CFATAL_LOG("load %s/%s failed", conf_path.c_str(), conf_file.c_str());
        return false;
    }

    std::string drpc_conf_path = "./conf/config";
    std::string drpc_conf_file = "drpc_client.xml";
    if (conf["drpc_conf_path"].selfType() != comcfg::CONFIG_ERROR_TYPE) {
        drpc_conf_path = conf["drpc_conf_path"].to_cstr();
    }
    if (conf["drpc_conf_file"].selfType() != comcfg::CONFIG_ERROR_TYPE) {
        drpc_conf_file = conf["drpc_conf_file"].to_cstr();
    }

    std::string drpc_conf = drpc_conf_path + "/" + drpc_conf_file;
    
    if (::drpc::init_env(drpc_conf) < 0) {
        CFATAL_LOG("drpc init failed, wordrank_drpc_conf_path[%s]", drpc_conf.data());
        return false;
    }
    CDEBUG_LOG("drpc::init_env done");
    
    for (uint32_t i = 0; i < conf["CLIENT"].size(); ++i) {
        try {
            std::string client_key(conf["CLIENT"][i]["client_key"].to_cstr());
            std::string client_init_param(conf["CLIENT"][i]["client_init_param"].to_cstr());
            auto iter = _nlpc_clients.find(client_key);
            if (iter != _nlpc_clients.end()) {
                continue;
            }
            NLPCClientPtr nlpc_client(new (std::nothrow) NLPCClient());
            if (!nlpc_client || nlpc_client->init(client_init_param) != 0) {
                CWARNING_LOG("nlpc client init failed! param[%s]", client_init_param.c_str());
                return false;
            }
            _nlpc_clients.emplace(client_key, nlpc_client);
        } catch (const comcfg::ConfigException& e) {
            CFATAL_LOG("ConfigException : %s", e.what());
            continue;
        } catch (...) {
            CFATAL_LOG("unknown exception");
            continue;
        }
    }
    CWARNING_LOG("init NLPCClientManager success");
    return true;
}

NLPCClient* NLPCClientManager::get_nlpc_client(const std::string& client_key) const {
    auto iter = _nlpc_clients.find(client_key);
    if (iter == _nlpc_clients.end()) {
        CDEBUG_LOG("no such nlpc init param[%s]", client_key.data());
        return NULL;
    }
    CDEBUG_LOG("find client_key[%s]", client_key.data());
    return iter->second.get();
}
// 使用nlpc_manager必须显式调用uninit，否则部分情况下会导致init_env先于nlpc_client析构，触发core
void NLPCClientManager::uninit() {
    for (auto iter = _nlpc_clients.begin(); iter != _nlpc_clients.end(); ++iter) {
        iter->second.reset();
    }
    _nlpc_clients.clear();
}

NLPCClientManagerGuard::NLPCClientManagerGuard(const comcfg::ConfigUnit& conf) {
    if (FLAGS_open_nlpc) {
        const std::string nlpc_path = conf["path"].to_cstr();
        const std::string nlpc_file = conf["file"].to_cstr();
        auto& nlpc_client = NLPCClientManagerSingleton::instance();
        if (!nlpc_client.init(nlpc_path, nlpc_file)) {
            CFATAL_LOG("fail to init nlpc client, exit.");
            exit(1);
        }
        CWARNING_LOG("succ load nlpc conf");
    }
}

NLPCClientManagerGuard::~NLPCClientManagerGuard() {
    if (FLAGS_open_nlpc) {
        auto& nlpc_client = NLPCClientManagerSingleton::instance();
        nlpc_client.uninit();
    }
}

} // namespace common_lib
} // namespace themis
} //namespace anti
