// Copyright (c) 2025 Baidu.com, Inc. All Rights Reserved
// <AUTHOR> kun(<EMAIL>)
// @brief 

#ifndef APP_ECOM_ANTI_THEMIS_COMMON_LIB_IMEMPOOL_H
#define APP_ECOM_ANTI_THEMIS_COMMON_LIB_IMEMPOOL_H

#include "vector"
#include "bsl/var/Dict.h"
#include "bsl/ResourcePool.h"

namespace anti {
namespace themis {
namespace common_lib {

template <class VirtualAddrType>
class IMemDataPool {
public:
    typedef VirtualAddrType TVaddr;

public:
    virtual ~IMemDataPool() {
        // do nothing.
    }

    virtual TVaddr malloc(size_t size) = 0;
    virtual int free(const TVaddr& vaddr) = 0;
    virtual void clear() = 0;

    virtual bool is_thread_safe() = 0;
    virtual void start_gc() = 0;
    virtual void stop_gc() = 0;

    virtual void monitor(
            bsl::var::Dict& dict,
            bsl::ResourcePool& rp) const = 0;
};

}  // namespace common_lib
}  // namespace themis
}  // namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_COMMON_LIB_IMEMPOOL_H

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
