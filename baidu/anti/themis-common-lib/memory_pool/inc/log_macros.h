// Copyright (c) 2025 Baidu.com, Inc. All Rights Reserved
// <AUTHOR> kun(<EMAIL>)
// @brief 

#ifndef  APP_ECOM_ANTI_THEMIS_COMMON_LIB_LOG_MACROS_H
#define  APP_ECOM_ANTI_THEMIS_COMMON_LIB_LOG_MACROS_H

#include <stdint.h>
#include "com_log.h"

#define ANTI_UNLIKE(x) __builtin_expect((x), 0)
#define ANTI_LIKELY(x) __builtin_expect((x), 1)

#define ANTI_CHECK_OR_RET_VOID(cond) \
do { \
    if (ANTI_UNLIKE(!(cond))) { \
        CFATAL_LOG("cond["#cond"] failed!"); \
        return; \
    } \
} while(0)

#define ANTI_SAFE_DELETE(ptr) \
do { \
    if (ANTI_LIKELY(ptr != nullptr)) { \
        delete ptr; \
        ptr = nullptr; \
    } \
} while(0)

#define ANTI_SAFE_DELETE_ARRAY_PTR(ptr) \
do { \
    if (ANTI_LIKELY(ptr != nullptr)) { \
        delete [] ptr; \
        ptr = nullptr; \
    } \
} while(0)

#define ANTI_CHECK_OR_RET(cond, ret) \
do { \
    if (ANTI_UNLIKE(!(cond))) { \
        CFATAL_LOG("cond["#cond"] failed!"); \
        return ret; \
    } \
} while(0)

#define ANTI_CHECK_OR_RETURN_WITH_MSG(cond, ret, fmt, ...) \
do { \
    if (ANTI_UNLIKE(!(cond))) { \
        CFATAL_LOG("cond["#cond"] failed, " fmt, ## __VA_ARGS__); \
        return ret; \
    } \
} while(0)

#define ANTI_NOT_NULL_OR_RET_VOID(t) ANTI_CHECK_OR_RET_VOID(t != NULL);
#define ANTI_NOT_NULL_OR_RET(t, ret) ANTI_CHECK_OR_RET(t != NULL, ret);

#define ANTI_SAFE_SNPRINTF_OR_RET(ret_value, dst, dst_size, fmt, ...) \
    do {  \
    int ret = snprintf(dst, dst_size, fmt, ## __VA_ARGS__);   \
    ANTI_CHECK_OR_RETURN_WITH_MSG(ret > 0 && ret < (int)dst_size, ret_value, "failed to snprintf full path["#fmt"].", ## __VA_ARGS__);    \
    } while (0)   

#endif // APP_ECOM_ANTI_THEMIS_COMMON_LIB_LOG_MACROS_H