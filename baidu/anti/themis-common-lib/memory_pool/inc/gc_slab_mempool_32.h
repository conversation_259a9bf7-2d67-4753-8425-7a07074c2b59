// Copyright (c) 2025 Baidu.com, Inc. All Rights Reserved
// <AUTHOR> kun(<EMAIL>)
// @brief 

#ifndef APP_ECOM_ANTI_THEMIS_COMMON_LIB_GCSLABMEMPOOL32_H
#define APP_ECOM_ANTI_THEMIS_COMMON_LIB_GCSLABMEMPOOL32_H

#include <vector>
#include <stdint.h>
#include <gflags/gflags.h>
#include <queue>

#include "log_macros.h"
#include "imem_pool.h"

DECLARE_uint64(menpool_gc_free_block_redundance);

namespace anti {
namespace themis {
namespace common_lib {

class GCSlabMempool32 : public IMemDataPool<uint32_t> {
public:
    const static TVaddr NULL_VADDR = 0xFFFFFFFF;

public:
    GCSlabMempool32(){}

    virtual ~GCSlabMempool32();

    int create(const uint32_t slabs[],
            uint32_t slabs_len,
            uint32_t max_block_item_num);

    <PERSON><PERSON><PERSON> malloc(size_t len);
    TVaddr realloc(TVaddr old_vaddr, size_t new_size);
    int free(const TVaddr& vaddr);

    void clear();

    void* mem_address(const TVaddr& vaddr) const;

    void monitor(bsl::var::Dict& dict, bsl::ResourcePool& rp) const;

    static TVaddr null() {
        return NULL_VADDR;
    }

    int get_vaddr_item_size(const TVaddr& vaddr, uint32_t& item_size);

    int slab_block_status(const uint32_t len);

    bool is_thread_safe() override
    {
        return false;
    }

    // check free statu
    void check_gc(const uint32_t item_size, uint64_t& used_cnt, 
            uint64_t& malloc_cnt, uint64_t& cnt_per_block);
    // prepare gc_index
    void start_gc() override;
    // clear gc_index
    void stop_gc() override;
    // check one addr whether will do gc
    bool check_vaddr_gc(const TVaddr vaddr);
    // re alloc one addr to replace the old
    // note: will not free old addr direct, old addr will free by *Table
    TVaddr gc_realloc(const TVaddr vaddr);
    TVaddr force_realloc(const TVaddr vaddr);

private:
    struct mem_block_t {
        char*       start;
        uint32_t    item_size;      // fixed value, item size in this block
        uint32_t    max_item_num;   // fixed value, max item cnt in this block
        uint32_t    slab_idx;       // fixed value, slab_idx of slab which own this block
        uint32_t    slab_blocklist_idx; // fixed value, block index in slab's blocklist
        uint32_t    max_used_idx;   // max item index in this block which has been malloced 
        TVaddr      free_list;      // head of free list in this block, NULL_VADDR shows no free vaddr left
        uint32_t    used_cnt;       // number of item in use, 0 shows this block is not used

        mem_block_t() {
            start = NULL;          
            item_size = 0;
            max_item_num = 0;
            slab_idx = 0;
            slab_blocklist_idx = 0;
            max_used_idx = 0;
            free_list = NULL_VADDR;
            used_cnt = 0;
        }
        void release() {  // called in GC, after release, this block can be used again in the future
            if (start != NULL) {
                delete [] start;
                start = NULL;
            }        
            item_size = 0;
            max_item_num = 0;
            slab_idx = 0;
            slab_blocklist_idx = 0;
            max_used_idx = 0;
            free_list = NULL_VADDR;
            used_cnt = 0;
        }
    };

    struct mem_slab_t {
        std::vector<uint32_t> block_list;
        uint32_t              block_max_item_num;    // fixed value,  max item cnt of block
        uint32_t              item_size;             // fixed value,  item size
        uint32_t              malloc_blocklist_idx;  // index of block_list, block before this index have no freelist
        uint32_t              min_gc_index;          // blocks will do gc if index is bigger than min_gc_index, 0 means do not gc
        mem_slab_t() {
            block_max_item_num = 0;          
            item_size = 0;
            malloc_blocklist_idx = 0;
            min_gc_index = 0;
        }
    };

private:
    void compute_split_bits(uint32_t max_block_item_num);

    int create_slabs(const uint32_t slabs[], uint32_t slabs_len);

    void create_blocks();

    // malloc one vaddr
    TVaddr malloc_from_freelist(mem_slab_t& slab);
    TVaddr malloc_from_freelist(mem_block_t& block);
    TVaddr malloc_from_block(mem_slab_t& slab, const uint32_t slab_idx);
    TVaddr malloc_from_new_block(mem_slab_t& slab, const uint32_t slab_idx);
    
    int malloc_new_block_for_slab(mem_slab_t& slab, 
                                  const uint32_t slab_idx,
                                  uint32_t& block_id);

    uint32_t make_vaddr(uint32_t block_idx, uint32_t offset);

    inline void split_vaddr(
            const TVaddr& vaddr,
            uint32_t& block_idx,
            uint32_t& offset) const {
        block_idx = vaddr >> _idx2_bits;
        offset = vaddr & _idx2_mask;
    }

    int find_slab(size_t len);

    int push_into_freelist(mem_block_t& block, uint32_t vaddr);

    int get_vaddr_block_info(const TVaddr& vaddr, uint32_t& block_idx, uint32_t& off_set);

private:
    const static uint32_t MIN_SLAB_ITEM_SIZE = 4;
    const static uint32_t SUGGEST_MAX_SLABS_LEN = 40;

    const static uint32_t MIN_IDX2_BITS = 10;
    const static uint32_t MAX_IDX2_BITS = 20;
    const static uint32_t MAX_BLOCK_SIZE = 100 * 1024 * 1024;   // 100 MB

    const static uint32_t INVALID_BLOCK_IDX = 0xFFFFFFFF;

    uint32_t    _idx1_bits {0};
    uint32_t    _idx2_bits {0};
    uint32_t    _idx2_mask {0};

    uint32_t    _max_block_num {0};
    uint32_t    _max_block_item_num {0};

private:
    const static uint32_t WARNING_BLOCK_RATE = 85;
    const static uint32_t MAX_WARNING_TIMES = 20;

    uint32_t    _warning_block_idx {0};
    uint32_t    _cur_warning_times {0}; 

private:
    std::vector<uint32_t>       _slab_lens;

    std::queue<uint32_t>        _free_blocks;   // after GC, there may be some empty blocks

    std::vector<mem_block_t>   _blocks;
    std::vector<mem_slab_t>    _slabs;

    size_t                  _node_num {0};   // node in use
    size_t                  _malloc_num {0};  // node been malloced
    size_t                  _mem_consume {0};
    size_t                  _actual_mem_need {0};
    size_t                  _actual_mem_used {0};
    uint64_t                _reallocs{0};
    uint64_t                _reallocs_succ{0};
    uint64_t                _need_realloc{0};
};

}  // namespace common_lib
}  // namespace themis
}  // namespace anti


#endif  // APP_ECOM_ANTI_THEMIS_COMMON_LIB_GCSLABMEMPOOL32_H

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
