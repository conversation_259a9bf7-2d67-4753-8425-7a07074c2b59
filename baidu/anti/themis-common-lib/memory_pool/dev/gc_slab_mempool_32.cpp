// Copyright (c) 2025 Baidu.com, Inc. All Rights Reserved
// <AUTHOR> kun(<EMAIL>)
// @brief 

#include <algorithm>

#include "com_log.h"

#include "bsl/var/Int32.h"
#include "bsl/var/Int64.h"

#include "gc_slab_mempool_32.h"

DEFINE_uint64(menpool_gc_free_block_redundance, 1, "blocks left more while cal gc slab's min keep block index, \
        usually less than free_block_minimun");

namespace anti {
namespace themis {
namespace common_lib {

const GCSlabMempool32::TVaddr GCSlabMempool32::NULL_VADDR;
const uint32_t GCSlabMempool32::SUGGEST_MAX_SLABS_LEN;
const uint32_t GCSlabMempool32::MIN_IDX2_BITS;
const uint32_t GCSlabMempool32::MAX_IDX2_BITS;
const uint32_t GCSlabMempool32::MAX_BLOCK_SIZE;
const uint32_t GCSlabMempool32::INVALID_BLOCK_IDX;
const uint32_t GCSlabMempool32::WARNING_BLOCK_RATE;
const uint32_t GCSlabMempool32::MAX_WARNING_TIMES;

GCSlabMempool32::~GCSlabMempool32()
{
    size_t block_num = _blocks.size();
    for (uint32_t i = 0; i < block_num; ++i) {
        mem_block_t& block = _blocks[i];
        block.release();
    }
    _blocks.clear();
}

/************************************ init ***********************************/
int GCSlabMempool32::create(
        const uint32_t slabs[],
        uint32_t slabs_len,
        uint32_t max_block_item_num)
{
    compute_split_bits(max_block_item_num);
    if (create_slabs(slabs, slabs_len) < 0) {
        CFATAL_LOG("create slabs failed!");
        return -1;
    }

    create_blocks();

    return 0;
}

// when max block item num is 1M, block id bits is 10bits, for fix size pool
// when max block item num is 128K, block id bits is 15bits, for var pool which may need more blocks
// because var pool may be not well-distributed
void GCSlabMempool32::compute_split_bits(
        uint32_t max_block_item_num)
{
    _idx2_bits = 0;

    for (uint32_t cur = max_block_item_num; cur > 0; cur = cur >> 1) {
        ++_idx2_bits;
    }

    if (_idx2_bits < MIN_IDX2_BITS) {
        _idx2_bits = MIN_IDX2_BITS;
        _max_block_item_num = 1 << MIN_IDX2_BITS;
    } else if (_idx2_bits > MAX_IDX2_BITS) {
        _idx2_bits = MAX_IDX2_BITS;
        _max_block_item_num = 1 << MAX_IDX2_BITS;
    } else {
        _max_block_item_num = max_block_item_num;
    }

    _idx1_bits = (uint32_t)sizeof(uint32_t) * 8 - _idx2_bits;
    _idx2_mask = (1 << _idx2_bits) - 1;
}

int GCSlabMempool32::create_slabs(
        const uint32_t slabs[],
        uint32_t slabs_len)
{
    if (slabs == NULL || slabs_len == 0) {
        CFATAL_LOG("Invalid create_slabs parameters!");
        return -1;
    }

    if (slabs_len > SUGGEST_MAX_SLABS_LEN) {
        CWARNING_LOG(
                "Too many slabs! slabs=[%u], suggest=[%u]",
                slabs_len, SUGGEST_MAX_SLABS_LEN);
    }

    for (uint32_t i = 0; i < slabs_len; ++i) {
        uint32_t item_size = slabs[i];
        if (item_size < MIN_SLAB_ITEM_SIZE) {
            CFATAL_LOG(
                    "slab's item size should not less than %u.",
                    MIN_SLAB_ITEM_SIZE);
            continue;
        }
        _slab_lens.emplace_back(slabs[i]);
    }

    std::sort(_slab_lens.begin(), _slab_lens.end());

    _slabs.resize(_slab_lens.size());
    size_t slab_num = _slabs.size();
    for (size_t i = 0; i < slab_num; ++i) {
        mem_slab_t& slab = _slabs[i];
        slab.block_list.clear();
        uint32_t item_size = _slab_lens[i];
        uint32_t sug_item_num = (MAX_BLOCK_SIZE / item_size);
        slab.block_max_item_num = (sug_item_num < _max_block_item_num?
            sug_item_num : _max_block_item_num);
        slab.item_size = item_size;
        slab.malloc_blocklist_idx = 0;
    }

    _mem_consume += _slab_lens.capacity() * sizeof(uint32_t);
    _mem_consume += _slabs.capacity() * sizeof(mem_slab_t);
    return 0;
}

void GCSlabMempool32::create_blocks()
{
    _max_block_num = 1 << _idx1_bits;
    _blocks.clear();

    _warning_block_idx = _max_block_num * WARNING_BLOCK_RATE / 100;

    while (!_free_blocks.empty()) {
        _free_blocks.pop();
    }
}

/************************************ malloc one vaddr ***********************************/
// PUBLIC FUNCTION : malloc one vaddr
GCSlabMempool32::TVaddr GCSlabMempool32::malloc(size_t len)
{
    int slab_idx = find_slab(len);
    if (slab_idx < 0) {
        CFATAL_LOG(
                "Failed to find the slab for len[%zu]", len);
        return NULL_VADDR;
    }
    // mem ever needed
    _actual_mem_need += _slab_lens[slab_idx];

    mem_slab_t& slab = _slabs[slab_idx];

    TVaddr vaddr = malloc_from_freelist(slab);
    if (vaddr != NULL_VADDR) {
        return vaddr;
    }

    // malloc from block means actually used mem increase
    _actual_mem_used += _slab_lens[slab_idx];
    return malloc_from_block(slab, (uint32_t)slab_idx);
}

// malloc one vaddr from freelist
GCSlabMempool32::TVaddr GCSlabMempool32::malloc_from_freelist(mem_slab_t& slab)
{
    uint32_t start_idx = slab.malloc_blocklist_idx;
    for (uint32_t i = start_idx; i < slab.block_list.size(); i++) {
        uint32_t block_idx = slab.block_list[i];
        mem_block_t& block = _blocks[block_idx];
        TVaddr vaddr = malloc_from_freelist(block);
        if (vaddr != NULL_VADDR) {
            slab.malloc_blocklist_idx = i;
            return vaddr;
        }
    }

    return NULL_VADDR;
}

// malloc one vaddr from block's freelist
GCSlabMempool32::TVaddr GCSlabMempool32::malloc_from_freelist(mem_block_t& block) 
{
    if (block.free_list == NULL_VADDR) {
        return NULL_VADDR;
    }

    void* next_node = mem_address(block.free_list);
    if (next_node == NULL) {
        CFATAL_LOG("next node is NULL, there must be something wrong!");
        return NULL_VADDR;
    }

    TVaddr* p_next_vaddr = (TVaddr*) next_node;
    TVaddr next_vaddr = (TVaddr) * p_next_vaddr;
    TVaddr vaddr = block.free_list;
    block.free_list = next_vaddr;
    ++_node_num;
    ++block.used_cnt;

    return vaddr;
}

// malloc one vaddr from block
GCSlabMempool32::TVaddr GCSlabMempool32::malloc_from_block(mem_slab_t& slab, 
                                                    const uint32_t slab_idx)
{
    if (slab.block_list.size() == 0) {
        return malloc_from_new_block(slab, slab_idx);
    }

    size_t block_num = slab.block_list.size();
    uint32_t last_block_id = slab.block_list[block_num - 1];
  
    mem_block_t& block = _blocks[last_block_id];
    if (block.max_used_idx < block.max_item_num) {
        TVaddr vaddr = make_vaddr(last_block_id, block.max_used_idx);
        ++block.max_used_idx;
        ++block.used_cnt;
        ++_node_num;
        ++_malloc_num;
        slab.malloc_blocklist_idx = (uint32_t)block_num - 1;
        return vaddr;
    }

    return malloc_from_new_block(slab, slab_idx);
}

// malloc one vaddr from new block
GCSlabMempool32::TVaddr GCSlabMempool32::malloc_from_new_block(mem_slab_t& slab, 
                                                        const uint32_t slab_idx)
{ 
    uint32_t block_id = 0;
    int ret = malloc_new_block_for_slab(slab, slab_idx, block_id);
    if (ANTI_UNLIKE(ret < 0)) {
        CFATAL_LOG("fail to malloc new block for slab!");
        return NULL_VADDR;
    }

    mem_block_t& new_block = _blocks[block_id];

    new_block.max_used_idx = 1U;
    new_block.used_cnt = 1U;

    slab.malloc_blocklist_idx = (uint32_t)(slab.block_list.size()) - 1;

    ++_node_num;
    ++_malloc_num;

    return make_vaddr(block_id, 0);
}

/************************************ free one vaddr ***********************************/
// PUBLIC FUNCTION : free one vaddr
int GCSlabMempool32::free(const TVaddr& vaddr)
{
    if (vaddr == NULL_VADDR) {
        return 0;
    }

    uint32_t block_idx = 0;
    uint32_t offset = 0;
    split_vaddr(vaddr, block_idx, offset);

    if (block_idx >= _blocks.size()) {
        CFATAL_LOG(
                "Invalid vaddr [%u], block_idx[%u], max_block_idx[%lu]",
                vaddr, block_idx, _blocks.size());
        return -1;
    }

    mem_block_t& block = _blocks[block_idx];
    if (offset >= block.max_used_idx) {
        CFATAL_LOG(
                "Invalid vaddr [%u], offset[%u], block.max_used_idx[%u]",
                vaddr, offset, block.max_used_idx);
        return -1;
    }

    if(push_into_freelist(block, vaddr) < 0) {
        CFATAL_LOG(
                "Invalid vaddr [%u], offset[%u], block_idx[%u]",
                vaddr, offset, block_idx);
        return -1;
    }

    uint32_t slab_idx = block.slab_idx;
    mem_slab_t& slab = _slabs[slab_idx];

    // update malloc block list idx every time
    if (slab.malloc_blocklist_idx > block.slab_blocklist_idx) {
        slab.malloc_blocklist_idx = block.slab_blocklist_idx;
    }

    _actual_mem_need -= _slab_lens[slab_idx];
    
    return 0;
}

// free one vaddr to block
int GCSlabMempool32::push_into_freelist(mem_block_t& block, uint32_t vaddr)
{
    uint32_t* addr = (uint32_t*) mem_address(vaddr);
    if (addr == NULL) {
        CFATAL_LOG(
                "It's invalid virtual address [%u] slab[%d]",
                vaddr, block.slab_idx);
        return -1;
    }

    if (block.free_list == NULL_VADDR) {
        *addr = NULL_VADDR;
        block.free_list = vaddr;
    } else {
        *addr = block.free_list;
        block.free_list = vaddr;
    }

    --_node_num;
    --block.used_cnt;

    return 0;
}

/************************************ other funcs ***********************************/
int GCSlabMempool32::find_slab(size_t len)
{
    size_t k = 0;
    size_t n = _slab_lens.size();
    while (k < n && _slab_lens[k] < len) {
        ++k;
    }

    return (k < n ? (int)k : -1);
}

uint32_t GCSlabMempool32::make_vaddr(
        uint32_t block_idx,
        uint32_t offset)
{
    return (block_idx << _idx2_bits | offset);
}

// NOTE: This is PERFORMANCE CRITICAL!!!
void* GCSlabMempool32::mem_address(const GCSlabMempool32::TVaddr& vaddr) const
{
    if (vaddr == NULL_VADDR) {
        return NULL;
    }

    uint32_t block_idx = 0;
    uint32_t offset = 0;
    split_vaddr(vaddr, block_idx, offset);

    const mem_block_t& block = _blocks[block_idx];
    
    return block.start + offset * block.item_size;
}

// not used
void GCSlabMempool32::clear()
{
    size_t n_blocks = _blocks.size();
    for (size_t i = 0; i < n_blocks; ++i) {
        mem_block_t& block = _blocks[i];
        block.max_used_idx = 0;
        block.free_list = NULL_VADDR;
        block.used_cnt = 0;
    }

    size_t n_slabs = _slabs.size();
    for (size_t i = 0; i < n_slabs; ++i) {
        mem_slab_t& slab = _slabs[i];
        slab.malloc_blocklist_idx = 0;
    }
    
    _node_num = 0;
    _malloc_num = 0;
    _actual_mem_need = 0;
}

void GCSlabMempool32::monitor(bsl::var::Dict& dict, bsl::ResourcePool& rp) const
{
    try {
        dict["NODE_NUM"] = rp.create<bsl::var::Int32>(_node_num);
        dict["MALLOC_NUM"] = rp.create<bsl::var::Int32>(_malloc_num);
        dict["USED_BLOCK_NUM"] = rp.create<bsl::var::Int32>(_blocks.size());
        dict["MEM_CONSUME"] = rp.create<bsl::var::Int64>(_mem_consume);
        dict["ACTUAL_MEM_NEED"] = rp.create<bsl::var::Int64>(_actual_mem_need);
        dict["ACTUAL_MEM_USED"] = rp.create<bsl::var::Int64>(_actual_mem_used);
        
        uint64_t management_overhead = 0;
        management_overhead += _blocks.capacity() * sizeof(mem_block_t);     // Dynamically allocated blocks
        management_overhead += _slabs.capacity() * sizeof(mem_slab_t);       // slabs
        management_overhead += _slab_lens.capacity() * sizeof(uint32_t);     // slab_lens arr
        management_overhead += _free_blocks.size() * sizeof(uint32_t);       // free_blocks size
        management_overhead += sizeof(GCSlabMempool32);                      // obj size
        
        dict["POOL_MANAGEMENT_OVERHEAD"] = rp.create<bsl::var::Int64>(management_overhead);
        
        uint64_t effective_pool_mem = _actual_mem_used + management_overhead;
        dict["EFFECTIVE_POOL_MEMORY"] = rp.create<bsl::var::Int64>(effective_pool_mem);
    } catch (bsl::Exception& e) {
        CFATAL_LOG("An bsl::Exception catched: %s", e.what());
    } catch (...) {
        CFATAL_LOG("GCSlabMempool32 failed to get monitor info");
    }
}

// FOR DEBUG
int GCSlabMempool32::slab_block_status(const uint32_t len) {
    int slab_idx = find_slab(len);
    if (ANTI_UNLIKE(slab_idx < 0)) {
        CFATAL_LOG("Failed to find the slab for len[%u]", len);
        return -1;
    }

    mem_slab_t& slab = _slabs[slab_idx];
    bsl::string status;
    status.appendf("item size[%u]", len);
    uint64_t used_cnt = 0;
    uint64_t malloc_cnt = 0;
    for (uint32_t i = 0; i < slab.block_list.size(); ++i) {
        uint32_t block_idx = slab.block_list[i];
        mem_block_t& block = _blocks[block_idx];
        status.appendf("[%u,%u,%u,%u]", block_idx, i, block.used_cnt, block.max_used_idx);
        used_cnt += block.used_cnt;
        malloc_cnt += block.max_used_idx;
    }
    CDEBUG_LOG("block stats:\n%s", status.c_str());
    CDEBUG_LOG("block's used cnt[%lu], malloc cnt[%lu], gc all[%lu], need[%lu] succ[%lu]", 
                used_cnt, malloc_cnt, _reallocs, _need_realloc, _reallocs_succ);
    return 0;
}

int GCSlabMempool32::get_vaddr_item_size(const TVaddr& vaddr, uint32_t& item_size)
{
    item_size = 0;
    if (ANTI_UNLIKE(vaddr == NULL_VADDR)) {
        return -1;
    }

    uint32_t block_idx = 0;
    uint32_t offset = 0;
    split_vaddr(vaddr, block_idx, offset);

    if (ANTI_UNLIKE(block_idx >= _blocks.size())) {
        CFATAL_LOG(
                "Invalid vaddr [%u], block_idx[%u], max_block_idx[%lu]",
                vaddr, block_idx, _blocks.size());
        return -1;
    }

    mem_block_t& block = _blocks[block_idx];
    if (ANTI_UNLIKE(offset >= block.max_used_idx)) {
        CFATAL_LOG(
                "Invalid vaddr [%u], offset[%u], block.max_used_idx[%u]",
                vaddr, offset, block.max_used_idx);
        return -1;
    }
    item_size = block.item_size;
    return 0;
}

int GCSlabMempool32::malloc_new_block_for_slab(mem_slab_t& slab, const uint32_t slab_idx, 
                                               uint32_t& block_id)
{
    // after GC, there may be null block inside _blocks, try find one
    block_id = 0;
    bool reuse_block = false;
    if (!_free_blocks.empty()) {
        block_id = _free_blocks.front();
        _free_blocks.pop();
        reuse_block = true;
    }
    if (!reuse_block) {
        // need new block
        if (_blocks.size() >= _max_block_num) {
            CFATAL_LOG(
                "Blocks are all used! num[%lu], max[%u]",
                _blocks.size(), _max_block_num);
            return -1;
        }
        mem_block_t new_block;
        _blocks.emplace_back(new_block);
        block_id = (uint32_t)_blocks.size() - 1;

        if (block_id > _warning_block_idx
                && _cur_warning_times < MAX_WARNING_TIMES) {
            CFATAL_LOG(
                "WARNING: block ids nearly to be exhausted. max: %u, cur: %u",
                _max_block_num - 1, block_id);
            ++_cur_warning_times;
            // NOT return -1, print FATAL is enough
        }
    }
    
    uint64_t n_bytes = static_cast<uint64_t>(slab.item_size) * slab.block_max_item_num;
    mem_block_t& new_block = _blocks[block_id];
    new_block.start = new(std::nothrow) char[n_bytes];
    if (new_block.start == NULL) {
        CFATAL_LOG("Failed to malloc a new block!");
        _free_blocks.push(block_id);
        return -1;
    }

    new_block.item_size = slab.item_size;
    new_block.max_item_num = slab.block_max_item_num;
    new_block.slab_idx = slab_idx;
    new_block.slab_blocklist_idx = (uint32_t)slab.block_list.size();
    new_block.max_used_idx = 0;
    new_block.free_list = NULL_VADDR;
    new_block.used_cnt = 0;

    slab.block_list.emplace_back(block_id);

    _mem_consume += n_bytes;

    if (!reuse_block) {
        _mem_consume += sizeof(mem_block_t);
    }

    return 0;
}

GCSlabMempool32::TVaddr GCSlabMempool32::realloc(TVaddr old_vaddr, size_t new_size)
{
    // get vaddr item size
    uint32_t item_size = 0;
    int ret = this->get_vaddr_item_size(old_vaddr, item_size);
    if (ANTI_UNLIKE(ret < 0)) {
        CFATAL_LOG("fail to get vaddr item size! vaddr is [%u]", old_vaddr);
        return NULL_VADDR;
    }
    // old vaddr big enough, do not realloc
    if (item_size >= new_size) {
        return old_vaddr;
    } else {
        return this->malloc(new_size);
    }
}

int GCSlabMempool32::get_vaddr_block_info(const TVaddr& vaddr, uint32_t& block_idx, uint32_t& off_set) {
    uint32_t idx = 0;
    uint32_t offset = 0;
    split_vaddr(vaddr, idx, offset);
    if (idx >= _blocks.size()) {
        CFATAL_LOG(
                "Invalid vaddr [%u], block_idx[%u], max_block_idx[%lu]",
                vaddr, idx, _blocks.size());
        return -1;
    }
    mem_block_t& block = _blocks[idx];
    if (offset >= block.max_used_idx) {
        CFATAL_LOG(
                "Invalid vaddr [%u], offset[%u], block.max_used_idx[%u]",
                vaddr, offset, block.max_used_idx);
        return -1;
    }
    block_idx = idx;
    off_set = offset;
    return 0;
}

/************************************ other funcs ***********************************/
void GCSlabMempool32::check_gc(const uint32_t item_size, 
        uint64_t& used_cnt, uint64_t& malloc_cnt, uint64_t& cnt_per_block) { 
    used_cnt = 0;
    malloc_cnt = 0; 
    cnt_per_block = 0;
    int slab_idx = find_slab(item_size);
    if (slab_idx < 0) {
        CFATAL_LOG(
                "Failed to find the slab for len[%zu]", item_size);
        return ;
    }
    mem_slab_t& slab = _slabs[slab_idx];
    cnt_per_block = slab.block_max_item_num;

    for (uint32_t i = 0; i < slab.block_list.size(); i++) {
        uint32_t block_idx = slab.block_list[i];
        if (block_idx >= _blocks.size()) {
            continue;
        }
        mem_block_t& block = _blocks[block_idx];

        used_cnt += block.used_cnt;
        malloc_cnt += block.max_used_idx;
    }
    CDEBUG_LOG("check gc: itemsize[%u], slabidx[%d], block_list.size[%u], "
            "used_cnt[%u], malloc_cnt[%u]", 
            item_size, slab_idx, slab.block_list.size(), used_cnt, malloc_cnt);
}

void GCSlabMempool32::start_gc() {
    size_t n_slabs = _slabs.size();
    for (size_t i = 0; i < n_slabs; ++i) {
        uint64_t used_cnt = 0;
        uint64_t malloc_cnt = 0;
        uint64_t cnt_per_block = 0;
        mem_slab_t& slab = _slabs[i];
        CDEBUG_LOG("check slab[%u]", slab.item_size);
        check_gc(slab.item_size, used_cnt, malloc_cnt, cnt_per_block);

        if (cnt_per_block == 0) {
            CFATAL_LOG("get slab[%u] cnt status failed", slab.item_size);
            continue;
        }

        uint64_t used_block = (used_cnt + cnt_per_block - 1) / cnt_per_block;
        slab.min_gc_index = used_block + FLAGS_menpool_gc_free_block_redundance;
        if (slab.min_gc_index > 0) {
            --slab.min_gc_index;  //index in vecotr
        }
        CDEBUG_LOG("slab len[%u] min_gc_index[%u]", 
                slab.item_size, slab.min_gc_index);
    }
    _reallocs = 0;
    _reallocs_succ = 0;
    _need_realloc = 0;
}

void GCSlabMempool32::stop_gc() {
    size_t n_slabs = _slabs.size();
    for (size_t i = 0; i < n_slabs; ++i) {
        _slabs[i].min_gc_index = 0;
    }
    CDEBUG_LOG("stop gc, all[%lu], need[%lu] succ[%lu]", 
            _reallocs, _need_realloc, _reallocs_succ);
}

bool GCSlabMempool32::check_vaddr_gc(const TVaddr vaddr) {
    if (vaddr == NULL_VADDR) {
        return false;
    }

    uint32_t block_idx = 0;
    uint32_t offset = 0;
    if (get_vaddr_block_info(vaddr, block_idx, offset) < 0) {
        return false;
    }
    mem_block_t& block = _blocks[block_idx];
    uint32_t slab_idx = block.slab_idx;
    mem_slab_t& slab = _slabs[slab_idx];

    if (slab.min_gc_index > 0 && block.slab_blocklist_idx > slab.min_gc_index) {
        return true;
    }
    return false;
}

GCSlabMempool32::TVaddr GCSlabMempool32::gc_realloc(const TVaddr vaddr) {
    if (vaddr == NULL_VADDR) {
        return vaddr;
    }
    ++_reallocs;

    uint32_t block_idx = 0;
    uint32_t offset = 0;
    if (get_vaddr_block_info(vaddr, block_idx, offset) < 0) {
        return vaddr;
    }
    mem_block_t& block = _blocks[block_idx];
    
    uint32_t slab_idx = block.slab_idx;
    mem_slab_t& slab = _slabs[slab_idx];

    if (slab.min_gc_index > 0 && block.slab_blocklist_idx > slab.min_gc_index) {
        ++_need_realloc;
        TVaddr n_vaddr = malloc(slab.item_size);
        if (n_vaddr == NULL_VADDR) {
            CFATAL_LOG("realloc new vaddr failed");
            return vaddr;
        }

        char* s_addr = (char*) (block.start + offset * block.item_size);  //mem_address
        if (s_addr == nullptr) {
            CFATAL_LOG("Invalid nullptr vaddr[%u]", vaddr);
            return vaddr;
        }

        char* d_addr = (char*) mem_address(n_vaddr);
        if (d_addr == nullptr) {
            CFATAL_LOG("Invalid n_vaddr[%u]", n_vaddr);
            return vaddr; 
        }
        memcpy(d_addr, s_addr, slab.item_size);
        ++_reallocs_succ;
        return n_vaddr;
    }

    return vaddr;
}

GCSlabMempool32::TVaddr GCSlabMempool32::force_realloc(const TVaddr vaddr) {
    if (vaddr == NULL_VADDR) {
        return NULL_VADDR;
    }

    uint32_t block_idx = 0;
    uint32_t offset = 0;
    if (get_vaddr_block_info(vaddr, block_idx, offset) < 0) {
        return NULL_VADDR;
    }
    mem_block_t& block = _blocks[block_idx];
    
    uint32_t slab_idx = block.slab_idx;
    mem_slab_t& slab = _slabs[slab_idx];
    
    TVaddr n_vaddr = malloc(slab.item_size);
    if (n_vaddr == NULL_VADDR) {
        CFATAL_LOG("realloc new vaddr failed");
        return NULL_VADDR;
    }

    char* s_addr = (char*) (block.start + offset * block.item_size);  //mem_address
    if (s_addr == nullptr) {
        CFATAL_LOG("Invalid nullptr vaddr[%u]", vaddr);
        return NULL_VADDR;
    }

    char* d_addr = (char*) mem_address(n_vaddr);
    if (d_addr == nullptr) {
        CFATAL_LOG("Invalid n_vaddr[%u]", n_vaddr);
        return NULL_VADDR; 
    }
    memcpy(d_addr, s_addr, slab.item_size);
    return n_vaddr;
}


}  // namespace common_lib
}  // namespace themis
}  // namespace anti  // namespace im

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
