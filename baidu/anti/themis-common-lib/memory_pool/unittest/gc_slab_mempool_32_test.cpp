#include "gc_slab_mempool_32_test.h"
// #include "base/time.h"
#include <vector>

namespace anti {
namespace themis {
namespace common_lib {

const GCSlabMempool32::TVaddr TestGCSlabMempool32::NULL_VADDR;

typedef GCSlabMempool32::mem_block_t mem_block_t;
typedef GCSlabMempool32::mem_slab_t mem_slab_t;

void print_gc_monitor(GCSlabMempool32* p_pool) {
    bsl::ResourcePool rp;
    bsl::var::Dict dict = rp.create<bsl::var::Dict>();
    p_pool->monitor(dict, rp);
    std::cout << "=============start print gc monitor===============" << std::endl;
    std::cout << "SLABPOOL NODE_NUM: " << dict["NODE_NUM"].to_uint64() << std::endl;
    std::cout << "SLABPOOL MALLOC_NUM: " << dict["MALLOC_NUM"].to_uint64() << std::endl;
    std::cout << "SLABPOOL USED_BLOCK_NUM: " << dict["USED_BLOCK_NUM"].to_uint64() << std::endl;
    std::cout << "SLABPOOL MEM_CONSUME: " << dict["MEM_CONSUME"].to_uint64() << std::endl;
    std::cout << "SLABPOOL ACTUAL_MEM_NEED: " << dict["ACTUAL_MEM_NEED"].to_uint64() << std::endl;
    std::cout << "SLABPOOL ACTUAL_MEM_USED: " << dict["ACTUAL_MEM_USED"].to_uint64() << std::endl;
    std::cout << "=============stop print gc monitor===============" << std::endl;
}

void TestGCSlabMempool32::SetUp() {
    static const uint32_t slabs[] = {
        5, 8, 13, 21, 34, 55, 89, 144,
        173, 208, 250, 300, 360, 432,
        518, 622, 747, 896, 1075, 1290,
        1548, 1858, 2230, 2676, 3211, 4097,
        5887, 8193, 9098, 14985, 16385,
        24083, 32769, 39068, 63151, 65537
    };

    uint32_t slabs_len = sizeof(slabs) / sizeof(slabs[0]);
    uint32_t block_item_num = 128 * 1024;   // 128k nodes/per block

    m_gc_pool = new GCSlabMempool32();
    m_gc_pool->create(slabs, slabs_len, block_item_num);

}

TEST_F(TestGCSlabMempool32, test_lots_malloc_and_free) {
    std::vector<uint32_t> vaddr_list;
    vaddr_list.reserve(1000000);

    uint32_t malloc_times = 500000;
    for (uint32_t i = 0; i < malloc_times; i++) {
        uint32_t vaddr = m_gc_pool->malloc(30);
        vaddr_list.emplace_back(vaddr);
    }
    std::cout << "---after m_gc_pool->malloc(30) 500000 times---" << std::endl;
    print_gc_monitor(m_gc_pool);

    uint32_t free_idx = 0;
    for (uint32_t i = 0; i < (20 * 1024); i++) {
        m_gc_pool->free(vaddr_list[free_idx]);
        free_idx++;
    }
    std::cout << "---after m_gc_pool->free() 20k times---" << std::endl;
    print_gc_monitor(m_gc_pool);
}

TEST_F(TestGCSlabMempool32, test_time_for_gcslabpool) {
    uint32_t malloc_times = 1000000;
    std::vector<uint32_t> vaddr_list;
    vaddr_list.reserve(malloc_times);

    uint64_t malloc_time = 0;
    //base::Timer timer_malloc;
    //timer_malloc.start();
    for (uint32_t i = 0; i < malloc_times; i++) {
        //base::Timer tmp_timer;
        //tmp_timer.start();
        uint32_t vaddr = m_gc_pool->malloc(30);
        //tmp_timer.stop();
        //malloc_time += tmp_timer.n_elapsed();
        vaddr_list.emplace_back(vaddr);
        char* p_addr = (char*)m_gc_pool->mem_address(vaddr);
        snprintf(p_addr, 30, "%s", "hello");
        p_addr[15] = 'a';
        p_addr[16] = 'b';
        p_addr[23] = 'c';
    }
    //timer_malloc.stop();
    //std::cout << "gcslabpool total time " << timer_malloc.n_elapsed() / malloc_times << std::endl;
    //std::cout << "gcslabpool malloc time " << malloc_time / malloc_times << std::endl;

    print_gc_monitor(m_gc_pool);

    //base::Timer timer_free;
    //timer_free.start();
    for (uint32_t i = 0; i < vaddr_list.size(); i++) {
        uint32_t vaddr = vaddr_list[i];
        m_gc_pool->free(vaddr);
    }
    //timer_free.stop();
    //std::cout << "gcslabpool free time " << timer_free.n_elapsed() / malloc_times << std::endl;

    print_gc_monitor(m_gc_pool);

    vaddr_list.clear();
    //timer_malloc.start();
    for (uint32_t i = 0; i < malloc_times; i++) {
        //base::Timer tmp_timer;
        //tmp_timer.start();
        uint32_t vaddr = m_gc_pool->malloc(30);
        //tmp_timer.stop();
        //malloc_time += tmp_timer.n_elapsed();
        vaddr_list.emplace_back(vaddr);
        char* p_addr = (char*)m_gc_pool->mem_address(vaddr);
        snprintf(p_addr, 30, "%s", "hello");
        p_addr[15] = 'a';
        p_addr[16] = 'b';
        p_addr[23] = 'c';
    }
    //timer_malloc.stop();
    //std::cout << "gcslabpool total time " << timer_malloc.n_elapsed() / malloc_times << std::endl;
    //std::cout << "gcslabpool malloc time " << malloc_time / malloc_times << std::endl;

    print_gc_monitor(m_gc_pool);

    //timer_free.start();
    for (uint32_t i = 0; i < vaddr_list.size(); i++) {
        uint32_t vaddr = vaddr_list[i];
        m_gc_pool->free(vaddr);
    }
    //timer_free.stop();
    //std::cout << "gcslabpool free time " << timer_free.n_elapsed() / malloc_times << std::endl;

    print_gc_monitor(m_gc_pool);
}

TEST_F(TestGCSlabMempool32, members_should_be_initialized_by_default_constructor) {
    GCSlabMempool32 pool;
    EXPECT_EQ(0U, pool._idx1_bits);
    EXPECT_EQ(0U, pool._idx2_bits);
    EXPECT_EQ(0U, pool._node_num);
    EXPECT_EQ(0U, pool._malloc_num);
    EXPECT_EQ(0U, pool._warning_block_idx);
    EXPECT_EQ(0U, pool._cur_warning_times);
}

TEST_F(TestGCSlabMempool32, idx2_bits_should_not_less_than_MIN_IDX2_BITS) {
    GCSlabMempool32 pool;
    pool.compute_split_bits(1);
    EXPECT_EQ(pool.MIN_IDX2_BITS, pool._idx2_bits);
    EXPECT_EQ(32 - pool.MIN_IDX2_BITS, pool._idx1_bits);
    pool._idx1_bits = 0;
}

TEST_F(TestGCSlabMempool32, idx2_bits_should_not_lager_than_MAX_IDX2_BITS) {
    GCSlabMempool32 pool;
    pool.compute_split_bits(1 << (pool.MAX_IDX2_BITS + 1));
    EXPECT_EQ(pool.MAX_IDX2_BITS, pool._idx2_bits);
    EXPECT_EQ(32 - pool.MAX_IDX2_BITS, pool._idx1_bits);
    EXPECT_EQ(1U << pool.MAX_IDX2_BITS, pool._max_block_item_num);
}

TEST_F(TestGCSlabMempool32, idx2_bits_should_equal_to_the_bits_of_slab_node_num) {
    GCSlabMempool32 pool;
    uint32_t bits = 13;
    pool.compute_split_bits(1 << (bits - 1));
    EXPECT_EQ(bits, pool._idx2_bits);
    EXPECT_EQ(32 - bits, pool._idx1_bits);
    EXPECT_EQ(1U << (bits - 1), pool._max_block_item_num);
}

TEST_F(TestGCSlabMempool32, create_slabs_failed_if_input_param_is_NULL) {
    GCSlabMempool32 pool;
    EXPECT_EQ(-1, pool.create_slabs(NULL, 1));
}

TEST_F(TestGCSlabMempool32, create_slabs_failed_if_input_slabs_len_is_0) {
    GCSlabMempool32 pool;
    uint32_t slabs[2] = {6, 8};
    EXPECT_EQ(-1, pool.create_slabs(slabs, 0));
}

TEST_F(TestGCSlabMempool32, create_slabs_normally) {
    GCSlabMempool32 pool;
    uint32_t slabs[3] = {6, 8, 10};
    ASSERT_EQ(0, pool.create_slabs(slabs, 3));
    ASSERT_EQ(3U, pool._slabs.size());

    for (int i = 0; i < 3; ++i) {
        EXPECT_EQ(0U, pool._slabs[i].block_list.size());
    }
}

TEST_F(TestGCSlabMempool32, create_slabs_normally_if_too_many_slabs) {
    GCSlabMempool32 pool;
    std::vector<uint32_t> slabs;

    for (uint32_t i = 0; i < pool.SUGGEST_MAX_SLABS_LEN + 100; ++i) {
        slabs.push_back(2 ^ (i + 4));
    }

    ASSERT_EQ(0, pool.create_slabs(&slabs[0], slabs.size()));
    ASSERT_EQ(slabs.size(), pool._slabs.size());

    for (uint32_t i = 0; i < slabs.size(); ++i) {
        EXPECT_EQ(0U, pool._slabs[i].block_list.size());
    }
}

TEST_F(TestGCSlabMempool32, ignore_too_small_slabs_during_create_slabs) {
    GCSlabMempool32 pool;
    uint32_t slabs[5] = {1, 2, 4, 8, 16};
    ASSERT_EQ(0, pool.create_slabs(slabs, 5));
    ASSERT_EQ(3U, pool._slab_lens.size());
    EXPECT_EQ(4U, pool._slab_lens[0]);
    EXPECT_EQ(8U, pool._slab_lens[1]);
    EXPECT_EQ(16U, pool._slab_lens[2]);
}

TEST_F(TestGCSlabMempool32, slabs_should_be_in_order) {
    GCSlabMempool32 pool;
    uint32_t slabs[3] = {6, 12, 8};
    ASSERT_EQ(0, pool.create_slabs(slabs, 3));
    EXPECT_EQ(6U, pool._slab_lens[0]);
    EXPECT_EQ(8U, pool._slab_lens[1]);
    EXPECT_EQ(12U, pool._slab_lens[2]);
}

TEST_F(TestGCSlabMempool32, create_blocks_normally) {
    GCSlabMempool32 pool;
    pool._idx1_bits = 2;
    pool.create_blocks();

    EXPECT_EQ(0U, pool._blocks.size());
    EXPECT_EQ(4U, pool._max_block_num);
    EXPECT_EQ(4 * pool.WARNING_BLOCK_RATE / 100, pool._warning_block_idx);
}

TEST_F(TestGCSlabMempool32, create_failed_if_create_slab_failed) {
    GCSlabMempool32 pool;
    ASSERT_EQ(-1, pool.create_slabs(NULL, 2));
    EXPECT_EQ(-1, pool.create(NULL, 2, 123));
}

TEST_F(TestGCSlabMempool32, find_slab_normally) {
    GCSlabMempool32 pool;
    uint32_t slabs[4] = {6, 8, 12, 16};
    ASSERT_EQ(0, pool.create(slabs, 4, 123));
    EXPECT_EQ(0, pool.find_slab(1));
    EXPECT_EQ(0, pool.find_slab(6));
    EXPECT_EQ(1, pool.find_slab(7));
    EXPECT_EQ(2, pool.find_slab(10));
    EXPECT_EQ(3, pool.find_slab(14));
    EXPECT_EQ(3, pool.find_slab(16));
}

TEST_F(TestGCSlabMempool32, find_slab_failed_if_length_is_too_large) {
    GCSlabMempool32 pool;
    uint32_t slabs[4] = {6, 8, 12, 16};
    ASSERT_EQ(0, pool.create(slabs, 4, 123));
    EXPECT_EQ(-1, pool.find_slab(17));
    EXPECT_EQ(-1, pool.find_slab(20));
}

TEST_F(TestGCSlabMempool32, make_vaddr_normally) {
    GCSlabMempool32 pool;
    pool._idx2_bits = 12;
    uint32_t block_idx = 0xABCDE;
    uint32_t offset = 0xF01;
    EXPECT_EQ(0xABCDEF01, pool.make_vaddr(block_idx, offset));
}

TEST_F(TestGCSlabMempool32, mem_address_return_NULL_if_vaddr_is_NULL_VADDR) {
    GCSlabMempool32 pool;
    EXPECT_EQ((void *)NULL, pool.mem_address(NULL_VADDR));
}

TEST_F(TestGCSlabMempool32, translate_vaddr_normally) {
    GCSlabMempool32 pool;
    mem_slab_t slab;

    uint32_t slabs[5] = {5, 6, 8, 12, 16};
    ASSERT_EQ(0, pool.create(slabs, 5, 100));

    for (uint32_t i = 0; i < pool._max_block_item_num + 100; ++i) {
        uint32_t vaddr = pool.malloc(10);
        ASSERT_NE(NULL_VADDR, vaddr);
        void *addr = pool.mem_address(vaddr);
        ASSERT_NE((void *)NULL, addr) << i << " is NULL";
        uint32_t *p_addr = (uint32_t *) addr;
        (*p_addr) = i;
    }

    for (uint32_t i = 0; i < pool._max_block_item_num; ++i) {
        void *addr = pool._blocks[0].start + pool._blocks[0].item_size * i;
        uint32_t *p_addr = (uint32_t *) addr;
        EXPECT_EQ(i, *p_addr);
    }

    for (uint32_t i = 0; i < 100; ++i) {
        void *addr = pool._blocks[1].start + pool._blocks[0].item_size * i;
        uint32_t *p_addr = (uint32_t *) addr;
        EXPECT_EQ(i + pool._max_block_item_num, *p_addr);
    }
}

TEST_F(TestGCSlabMempool32, malloc_from_freelist_failed_if_freelist_is_empty) {
    GCSlabMempool32 pool;
    mem_block_t block;
    block.free_list = NULL_VADDR;
    EXPECT_EQ(NULL_VADDR, pool.malloc_from_freelist(block));
}

TEST_F(TestGCSlabMempool32, malloc_failed_if_the_required_length_larger_than_max_item_size) {
    GCSlabMempool32 pool;

    uint32_t slabs[5] = {5, 6, 8, 12, 16};
    ASSERT_EQ(0, pool.create(slabs, 5, 100));
    ASSERT_EQ(-1, pool.find_slab(20));

    EXPECT_EQ(NULL_VADDR, pool.malloc(20));
}

TEST_F(TestGCSlabMempool32, malloc_from_block_if_freelist_is_empty) {
    GCSlabMempool32 pool;

    uint32_t slabs[5] = {5, 6, 8, 12, 16};
    ASSERT_EQ(0, pool.create(slabs, 5, 100));
    ASSERT_EQ(3, pool.find_slab(10));

    ASSERT_EQ(0U, pool._slabs[3].block_list.size());

    uint32_t vaddr = pool.malloc(10);
    ASSERT_NE(NULL_VADDR, vaddr);
    ASSERT_EQ(1U, pool._slabs[3].block_list.size());
    ASSERT_EQ(12U, pool._blocks[0].item_size);
    ASSERT_EQ(1U, pool._blocks[0].max_used_idx);
}

TEST_F(TestGCSlabMempool32, malloc_from_freelist_if_freelist_is_not_empty) {
    GCSlabMempool32 pool;

    uint32_t slabs[5] = {5, 6, 8, 12, 16};
    ASSERT_EQ(0, pool.create(slabs, 5, 100));
    ASSERT_EQ(3, pool.find_slab(10));

    uint32_t vaddr1 = pool.malloc(10);
    ASSERT_EQ(1U, pool._slabs[3].block_list.size());
    ASSERT_EQ(0, pool.free(vaddr1));
    ASSERT_EQ(vaddr1, pool._blocks[0].free_list);

    uint32_t vaddr2 = pool.malloc(10);
    EXPECT_EQ(vaddr1, vaddr2);
    EXPECT_EQ(NULL_VADDR, pool._blocks[0].free_list);
}

TEST_F(TestGCSlabMempool32, ignore_NULL_vaddr_in_free) {
    GCSlabMempool32 pool;
    uint32_t slabs[5] = {5, 6, 8, 12, 16};
    ASSERT_EQ(0, pool.create(slabs, 5, 100));
    ASSERT_EQ(3, pool.find_slab(10));

    uint32_t vaddr = NULL_VADDR;
    EXPECT_EQ(0, pool.free(vaddr));

    bsl::ResourcePool rp;
    bsl::var::Dict dict = rp.create<bsl::var::Dict>();
    pool.monitor(dict, rp);
    EXPECT_EQ(0U, dict["NODE_NUM"].to_uint32());
}

TEST_F(TestGCSlabMempool32, free_failed_if_block_idx_out_of_range) {
    GCSlabMempool32 pool;
    uint32_t slabs[5] = {5, 6, 8, 12, 16};
    ASSERT_EQ(0, pool.create(slabs, 5, 100));
    ASSERT_EQ(3, pool.find_slab(10));

    // 先分配一个地址，确保有block存在
    uint32_t valid_vaddr = pool.malloc(10);
    ASSERT_NE(NULL_VADDR, valid_vaddr);
    
    // 构造超出范围的vaddr
    uint32_t vaddr = pool.make_vaddr(pool._blocks.size(), 0);
    EXPECT_EQ(-1, pool.free(vaddr));

    bsl::ResourcePool rp;
    bsl::var::Dict dict = rp.create<bsl::var::Dict>();
    pool.monitor(dict, rp);
    EXPECT_EQ(1U, dict["NODE_NUM"].to_uint32());
}

TEST_F(TestGCSlabMempool32, free_failed_if_block_offset_out_of_range) {
    GCSlabMempool32 pool;
    uint32_t slabs[5] = {5, 6, 8, 12, 16};
    ASSERT_EQ(0, pool.create(slabs, 5, 100));
    ASSERT_EQ(3, pool.find_slab(10));

    // 先分配一个地址，确保有block存在
    uint32_t valid_vaddr = pool.malloc(10);
    ASSERT_NE(NULL_VADDR, valid_vaddr);
    
    // 构造超出offset范围的vaddr (使用有效的block_idx但无效的offset)
    uint32_t vaddr = pool.make_vaddr(0, 999);  // offset=999，远超出max_used_idx
    EXPECT_EQ(-1, pool.free(vaddr));

    bsl::ResourcePool rp;
    bsl::var::Dict dict = rp.create<bsl::var::Dict>();
    pool.monitor(dict, rp);
    EXPECT_EQ(1U, dict["NODE_NUM"].to_uint32());
}

TEST_F(TestGCSlabMempool32, split_vaddr_normally) {
    GCSlabMempool32 pool;
    uint32_t vaddr = 0xABCDEF01;
    pool._idx2_bits = 16;
    pool._idx2_mask = (1 << 16) - 1;

    uint32_t block_idx = 0;
    uint32_t offset = 0;
    pool.split_vaddr(vaddr, block_idx, offset);

    EXPECT_EQ(0xABCDU, block_idx);
    EXPECT_EQ(0xEF01U, offset);
}

TEST_F(TestGCSlabMempool32, clean_the_block_offset_and_freelist) {
    GCSlabMempool32 pool;
    uint32_t slabs[5] = {5, 6, 8, 12, 16};
    ASSERT_EQ(0, pool.create(slabs, 5, 100));
    ASSERT_EQ(3, pool.find_slab(10));

    pool.malloc(10);
    uint32_t vaddr = pool.malloc(10);
    ASSERT_EQ(2U, pool._blocks[0].max_used_idx);
    ASSERT_EQ(0, pool.free(vaddr));
    ASSERT_EQ(vaddr, pool._blocks[0].free_list);

    pool.clear();
    EXPECT_EQ(0U, pool._blocks[0].max_used_idx);
    EXPECT_EQ(NULL_VADDR, pool._blocks[0].free_list);
    EXPECT_EQ(0U, pool._node_num);
}

TEST_F(TestGCSlabMempool32, reuse_the_block_after_clear) {
    GCSlabMempool32 pool;
    uint32_t slabs[5] = {5, 6, 8, 12, 16};
    ASSERT_EQ(0, pool.create(slabs, 5, 100));
    ASSERT_EQ(3, pool.find_slab(10));

    pool.malloc(10);
    ASSERT_EQ(1U, pool._blocks[0].max_used_idx);

    pool.clear();
    ASSERT_EQ(0U, pool._blocks[0].max_used_idx);

    pool.malloc(10);
    EXPECT_EQ(1U, pool._blocks[0].max_used_idx);
    EXPECT_EQ(1U, pool._node_num);
}

TEST_F(TestGCSlabMempool32, get_vaddr_item_size) {
    GCSlabMempool32 pool;
    uint32_t slabs[5] = {5, 6, 8, 12, 16};
    ASSERT_EQ(0, pool.create(slabs, 5, 100));
    uint32_t item_size = 0;
    uint32_t vaddr = pool.malloc(10);
    int ret = pool.get_vaddr_item_size(vaddr, item_size);
    EXPECT_EQ(12, item_size);
    EXPECT_EQ(0, ret);
}

TEST_F(TestGCSlabMempool32, test_realloc)
{
    GCSlabMempool32 pool;
    uint32_t slabs[5] = {5, 6, 8, 12, 16};
    ASSERT_EQ(0, pool.create(slabs, 5, 12800));

    uint32_t vaddr = pool.malloc(10);
    EXPECT_NE(NULL_VADDR, vaddr);

    uint32_t new_vaddr = pool.realloc(vaddr, 10);
    EXPECT_EQ(new_vaddr, vaddr);

    new_vaddr = pool.realloc(vaddr, 4);
    EXPECT_EQ(new_vaddr, vaddr);

    new_vaddr = pool.realloc(vaddr, 12);
    EXPECT_EQ(new_vaddr, vaddr);

    new_vaddr = pool.realloc(vaddr, 13);
    EXPECT_NE(NULL_VADDR, new_vaddr);
    EXPECT_NE(new_vaddr, vaddr);
}

TEST_F(TestGCSlabMempool32, mem_address_returns_null_for_invalid_block_idx) {
    GCSlabMempool32 pool;
    uint32_t slabs[5] = {5, 6, 8, 12, 16};
    ASSERT_EQ(0, pool.create(slabs, 5, 100));
    
    // 构造无效的vaddr（block_idx超出范围）
    uint32_t invalid_vaddr = pool.make_vaddr(9999, 0);
    EXPECT_EQ(NULL, pool.mem_address(invalid_vaddr));
}

TEST_F(TestGCSlabMempool32, mem_address_handles_released_blocks_safely) {
    GCSlabMempool32 pool;
    uint32_t slabs[5] = {5, 6, 8, 12, 16};
    ASSERT_EQ(0, pool.create(slabs, 5, 100));
    
    uint32_t vaddr = pool.malloc(10);
    ASSERT_NE(NULL_VADDR, vaddr);
    
    // 模拟block被释放的情况
    uint32_t block_idx, offset;
    pool.split_vaddr(vaddr, block_idx, offset);
    pool._blocks[block_idx].release();  // 直接释放block
    
    // mem_address 应该安全地返回 NULL
    EXPECT_EQ(NULL, pool.mem_address(vaddr));
}

} // end namespace common_lib
} // end namespace themis
} // end namespace anti

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
