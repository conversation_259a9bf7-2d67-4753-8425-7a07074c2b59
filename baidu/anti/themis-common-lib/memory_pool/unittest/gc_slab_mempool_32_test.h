#ifndef APP_ECOM_ANTI_THEMIS_COMMON_LIB_GCSLABMEMPOOL_TEST_H
#define APP_ECOM_ANTI_THEMIS_COMMON_LIB_GCSLABMEMPOOL_TEST_H

#include "gtest/gtest.h"

#include "gc_slab_mempool_32.h"

namespace anti {
namespace themis {
namespace common_lib {

class TestGCSlabMempool32 : public ::testing::Test {
public:
    TestGCSlabMempool32(){}
    virtual ~TestGCSlabMempool32(){}
protected:
    const static GCSlabMempool32::TVaddr NULL_VADDR = GCSlabMempool32::NULL_VADDR;
    virtual void SetUp();
    virtual void TearDown() {}
private:
    GCSlabMempool32* m_gc_pool;
};

} // end namespace common_lib
} // end namespace themis
} // end namespace anti

#endif  // APP_ECOM_ANTI_THEMIS_COMMON_LIB_GCSLABMEMPOOL_TEST_H

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
