#!/bin/sh
CITOOLS_PATH="../../../app/ecom/im/im-script/citools"
SVN_PATH="baidu/anti/themis-common-lib"
COV_PATH="*"

function install_citools()
{
    [[ -z "$CITOOLS_PATH" ]] && echo "no CITOOLS defined, so quit" && return 1
    local cur_path=`pwd`
    local imscript_dir=`dirname $CITOOLS_PATH`
    local basedir=`dirname $imscript_dir`
    mkdir -p $basedir

    cd $basedir
    rm -rf im-script im_trunk_citools.tgz
    rm -rf production
    wget  -q hdc.baidu-int.com:9119/home/<USER>/ecom_im/hudson/CI/hudson/jobs/im_trunk_citools/builds/lastsuccessful/im_trunk_citools.tgz
    wget -c hdc.baidu-int.com:9119/home/<USER>/ecom_im/hudson/CI/hudson/jobs/im_trunk_citools/builds/lastsuccessful/im_trunk_citools.tgz
    
    tar zxvf im_trunk_citools.tgz &> citools.tar.log
    [[ $? != 0 ]] && echo "extract citools from tar failed" && return 1
    cd $cur_path
    return 0
}
install_citools
[[ $? != 0 ]] && echo "install citools failed" && exit 1
source $CITOOLS_PATH/lib/localbuild_lib.sh
source $CITOOLS_PATH/lib/xts_cov_lib.sh
source ~/.bash_profile
trap_exit
build_arr_init $@
PROJECT_WORKROOT=$(pwd)
SOURCE_CODE_PATH="$SVN_PATH/*/dev $SVN_PATH/*/inc"
source $CITOOLS_PATH/anti_tools/anti_common_lib_new.sh
