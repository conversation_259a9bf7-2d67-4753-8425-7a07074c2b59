// Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
// <AUTHOR>
// @brief 

#ifndef APP_ECOM_ANTI_THEMIS_COMMON_LIB_PB_REFLECTOR_H
#define APP_ECOM_ANTI_THEMIS_COMMON_LIB_PB_REFLECTOR_H

#include <google/protobuf/message.h>
#include <vector>
#include <string>

namespace anti {
namespace themis {
namespace common_lib {

template<class Type>
bool str2num(const std::string& field_value, Type* val);

// @brief reflector need format path of message
//        format path is:
//          ignore-head:1-level.2-level.[liked]
//        PbPath will segment each level and save it
//        function next_field() will return 1-level then 2-level.
//        at end the next_field() will return NULL.
//        ATTENTION!
//        before use PbPath, should invoke reset() to reset the index
//        of level to 1-level

class PbPath {
public:
    PbPath() : _idx(0) {}
    ~PbPath() {}

    PbPath(const PbPath& pb_path)
    {
        _path.assign(pb_path._path.begin(), pb_path._path.end());
        _idx = pb_path._idx;
    }

    const char* ignore_head(const char* ptr, const char* end)
    {
        const char* delim = strchr(ptr, ':');
        if (delim == NULL || delim >= end) {
            return ptr;
        } else {
            return delim + 1;
        }
    }

    bool parse_path(const char* full_path)
    {
        if (full_path == NULL) {
            return false;
        }

        _path.clear();
        const char* ptr = full_path;
        const char* end = ptr + strlen(full_path);
        ptr = ignore_head(ptr, end);
        const char* pre = ptr;
        while (ptr < end) {
            if (*ptr == '.') {
                std::string tmp(pre, ptr - pre);
                _path.push_back(tmp);
                pre = ++ptr;
            }
            ptr++;
        }
        if (ptr != pre) {
            std::string tmp(pre, ptr - pre);
            _path.push_back(tmp);
        }
        return true;
    }

    const char* next_field()
    {
        if ((uint32_t)_idx >= _path.size()) {
            return NULL;
        }
        return _path[_idx++].c_str();
    }

    void reset()
    {
        _idx = 0;
    }

private:
    std::vector<std::string> _path;
    int32_t _idx;
};

class PbReflector {
public:
    static bool get_field(
            const google::protobuf::Message& msg,
            const std::string& path,
            std::string* value);
    static bool get_field(
            const google::protobuf::Message& msg,
            PbPath& path,
            std::string* value);

    static bool get_field(
            const google::protobuf::Message& msg,
            const std::string& path,
            std::vector<std::string>* values);
    static bool get_field(
            const google::protobuf::Message& msg,
            PbPath& path,
            std::vector<std::string>* values);

    static bool set_field(
            google::protobuf::Message* msg,
            std::string& field,
            std::string& value);

    static bool set_field(
            google::protobuf::Message* msg,
            const std::string& field,
            const std::vector<std::string>& value);

    static bool set_field_val(
            google::protobuf::Message* msg,
            const std::string& field,
            const std::string& value);

    static bool set_field_val(
            google::protobuf::Message* msg,
            const std::string& field,
            const std::vector<std::string>& values);

    static bool set_msg_field_val(
            const google::protobuf::Reflection*, 
            const google::protobuf::FieldDescriptor* field_descriptor,
            const std::string& field_value,
            google::protobuf::Message* msg);

    static bool set_msg_repeated_field_val(
            const google::protobuf::Reflection* ref,
            const google::protobuf::FieldDescriptor* field_des,
            const std::vector<std::string>& values, 
            google::protobuf::Message* msg);
};

}
}
}

#endif  // APP_ECOM_ANTI_THEMIS_COMMON_LIB_PB_REFLECTOR_H
