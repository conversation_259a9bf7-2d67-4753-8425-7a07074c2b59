// Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
// <AUTHOR>
// @brief 

#include <sstream>
#include <com_log.h>
#include <google/protobuf/descriptor.h>
#include <boost/format.hpp>
#include <boost/tokenizer.hpp>
#include <boost/algorithm/string.hpp>
#include <boost/lexical_cast.hpp>
#include "pb_reflector.h"
#include "sign_util.h"

using google::protobuf::Message;
using google::protobuf::Descriptor;
using google::protobuf::FieldDescriptor;
using google::protobuf::Reflection;
using anti::baselib::SignUtil;

namespace anti {
namespace themis {
namespace common_lib {

bool has_field(
        const Message& message,
        const FieldDescriptor* field_descriptor) {
    if (field_descriptor == NULL) {
        CWARNING_LOG("invalid input for _has_field");
        return false;
    }

    const Reflection* ref = message.GetReflection();
    if (ref == NULL) {
        CWARNING_LOG("get reflection failed");
        return false;
    }

    if (field_descriptor->is_repeated()) {
        return ref->FieldSize(message, field_descriptor) > 0;
    } else {
        return ref->HasField(message, field_descriptor);
    }
}
// @brief get field from message by reflect and cvt it to string
bool get_str_from_msg(
        const Reflection* ref,
        const FieldDescriptor* field_des,
        const Message* ptr_msg,
        std::string* value)
{
    if (ref == NULL || field_des == NULL || ptr_msg == NULL || value == NULL) {
        CWARNING_LOG("input param is NULL");
        return false;
    }
    // no field, set "" and return true
    if (!has_field(*ptr_msg, field_des)) {
        *value = "";
        return true;
    }

    std::stringstream ss;
    // only support get few type.
    // be careful to use it!
    switch (field_des->type()) {
        case FieldDescriptor::TYPE_STRING: {
            *value = ref->GetString(*ptr_msg, field_des);
            break;
        }
        case FieldDescriptor::TYPE_FIXED32:
        case FieldDescriptor::TYPE_UINT32: {
            ss << ref->GetUInt32(*ptr_msg, field_des);
            break;
        }
        case FieldDescriptor::TYPE_INT32: {
            ss << ref->GetInt32(*ptr_msg, field_des);
            break;
        }
        case FieldDescriptor::TYPE_INT64: {
            ss << ref->GetInt64(*ptr_msg, field_des);
            break;
        }
        case FieldDescriptor::TYPE_UINT64:
        case FieldDescriptor::TYPE_FIXED64: {
            ss << ref->GetUInt64(*ptr_msg, field_des);
            break;
        }
        case FieldDescriptor::TYPE_BYTES: {
            ss << ref->GetString(*ptr_msg, field_des);
            break;
        }
        case FieldDescriptor::TYPE_DOUBLE: {
            ss << ref->GetDouble(*ptr_msg, field_des);
            break;
        }
        default: {
            CWARNING_LOG("un-supported message type");
            return false;
        }
    }
    ss >> *value;
    return true;
}

// @brief get repeated field from message by reflect and cvt it to string
bool get_str_from_msg(
        const Reflection* ref,
        const FieldDescriptor* field_des,
        const Message* ptr_msg,
        std::vector<std::string>* values)
{
    if (ref == NULL || field_des == NULL || ptr_msg == NULL || values == NULL) {
        CWARNING_LOG("input param is NULL");
        return false;
    }

    if (field_des->is_repeated() == false) {
        return false;
    }

    int32_t size = ref->FieldSize(*ptr_msg, field_des);
    for (int32_t i = 0; i < size; ++i) {
        std::stringstream ss;
        std::string tmp;
        switch (field_des->type()) {
            case FieldDescriptor::TYPE_STRING: {
                ss << ref->GetRepeatedString(*ptr_msg, field_des, i);
                break;
            }
            case FieldDescriptor::TYPE_FIXED32:
            case FieldDescriptor::TYPE_UINT32: {
                ss << ref->GetRepeatedUInt32(*ptr_msg, field_des, i);
                break;
            }
            case FieldDescriptor::TYPE_INT32: {
                ss << ref->GetRepeatedInt32(*ptr_msg, field_des, i);
                break;
            }
            case FieldDescriptor::TYPE_UINT64:
            case FieldDescriptor::TYPE_INT64:
            case FieldDescriptor::TYPE_FIXED64: {
                ss << ref->GetRepeatedInt64(*ptr_msg, field_des, i);
                break;
            }
            case FieldDescriptor::TYPE_DOUBLE: {
                ss << ref->GetRepeatedDouble(*ptr_msg, field_des, i);
                break;
            }
            default: {
                CWARNING_LOG("un-supported message type");
                return false;
            }
        }
        ss >> tmp;
        values->push_back(tmp);
    }
    return true;
}

template<class Type>
bool str2num(const std::string& field_value, Type* val) {
    try {
        *val = boost::lexical_cast<Type>(field_value);
    } catch (boost::bad_lexical_cast& e) {
        CWARNING_LOG("error convert[%s] field[%s]", e.what(), field_value.data());
        return false;
    }
    return true;
}

bool PbReflector::set_msg_repeated_field_val(
        const Reflection* ref,
        const FieldDescriptor* field_des,
        const std::vector<std::string>& values, 
        Message* ptr_msg) {
    if (ref == NULL || field_des == NULL || ptr_msg == NULL) {
        CWARNING_LOG("input param is NULL");
        return false;
    }

    for (size_t i = 0; i < values.size(); ++i) {
        switch (field_des->type()) {
        case FieldDescriptor::TYPE_FIXED64:
        case FieldDescriptor::TYPE_UINT64: {
            uint64_t num = 0;
            if (!str2num<uint64_t>(values[i], &num)) {
                CWARNING_LOG("str to uint64 fail, value:%s", values[i].c_str());
                return false;
            }

            ref->AddUInt64(ptr_msg, field_des, num);
            break;
        }
        case FieldDescriptor::TYPE_INT64: {
            int64_t num = 0;
            if (!str2num<int64_t>(values[i], &num)) {
                CWARNING_LOG("str to int64 fail, value:%s", values[i].c_str());
                return false;
            }

            ref->AddInt64(ptr_msg, field_des, num);
            break;
        }
        case FieldDescriptor::TYPE_INT32: {
            int32_t num = 0;
            if (!str2num<int32_t>(values[i], &num)) {
                CWARNING_LOG("str to int32 fail, value:%s", values[i].c_str());
                return false;
            }

            ref->AddInt32(ptr_msg, field_des, num);
            break;
        }
        case FieldDescriptor::TYPE_FIXED32:
        case FieldDescriptor::TYPE_UINT32: {
            uint32_t num = 0;
            if (!str2num<uint32_t>(values[i], &num)) {
                CWARNING_LOG("str to uint32 fail, value:[%s]", values[i].c_str());
                return false;
            }
            ref->AddUInt32(ptr_msg, field_des, num);
            break;
        }
        case FieldDescriptor::TYPE_FLOAT: {
            float val = 0.0;
            if (!str2num<float>(values[i], &val)) {
                CWARNING_LOG("convert field str to float failed, field[%s]", 
                        values[i].c_str());
                return false;
            }
            ref->AddFloat(ptr_msg, field_des, val);
            break;
        }
        case FieldDescriptor::TYPE_DOUBLE: {
            double val = 0.0;
            if (!str2num<double>(values[i], &val)) {
                CWARNING_LOG("convert field str to doule failed, field[%s]", 
                        values[i].c_str());
                return false;
            }
            ref->AddDouble(ptr_msg, field_des, val);
            break;
        }
        case FieldDescriptor::TYPE_STRING: {
            ref->AddString(ptr_msg, field_des, values[i]);
            break;
        }
        default:
            CWARNING_LOG("invalid field_des->type(%d)", field_des->type());
            return false;
        }
    }
    return true;
}


bool PbReflector::set_msg_field_val(
        const Reflection* reflection,
        const FieldDescriptor* field_descriptor,
        const std::string& field_value,
        Message* msg) {
    if (field_descriptor == NULL || msg == NULL) {
        CWARNING_LOG("invalid param field_descriptor or msg is NULL");
        return false;
    }
    switch (field_descriptor->type()) {
        case FieldDescriptor::TYPE_FIXED32:
        case FieldDescriptor::TYPE_UINT32:
            {
                uint32_t val = 0U;
                if (!str2num<uint32_t>(field_value, &val)) {
                    CWARNING_LOG("convert field str to uint32 failed, field[%s]", 
                            field_value.data());
                    return false;
                }
                reflection->SetUInt32(msg, field_descriptor, val);
                break;
            }
        case FieldDescriptor::TYPE_UINT64:
            {
                uint64_t val = 0LU;
                if (!str2num<uint64_t>(field_value, &val)) {
                    CWARNING_LOG("convert field str to uint64 failed, field[%s]", 
                            field_value.data());
                    return false;
                }
                reflection->SetUInt64(msg, field_descriptor, val);
                break;
            }
        case FieldDescriptor::TYPE_INT32:
            {
                int32_t val = 0U;
                if (!str2num<int32_t>(field_value, &val)) {
                    CWARNING_LOG("convert field str to int32 failed, field[%s]", 
                            field_value.data());
                    return false;
                }
                reflection->SetInt32(msg, field_descriptor, val);
                break;
            }
        case FieldDescriptor::TYPE_INT64:
            {
                int64_t val = 0LU;
                if (!str2num<int64_t>(field_value, &val)) {
                    CWARNING_LOG("convert field str to int64 failed, field[%s]", 
                            field_value.data());
                    return false;
                }
                reflection->SetInt64(msg, field_descriptor, val);
                break;
            }
        case FieldDescriptor::TYPE_FLOAT:
            {
                float val = 0.0;
                if (!str2num<float>(field_value, &val)) {
                    CWARNING_LOG("convert field str to float failed, field[%s]", 
                            field_value.data());
                    return false;
                }
                reflection->SetFloat(msg, field_descriptor, val);
                break;
            }
        case FieldDescriptor::TYPE_DOUBLE:
            {
                double val = 0.0;
                if (!str2num<double>(field_value, &val)) {
                    CWARNING_LOG("convert field str to doule failed, field[%s]", 
                            field_value.data());
                    return false;
                }
                reflection->SetDouble(msg, field_descriptor, val);
                break;
            }
        case FieldDescriptor::TYPE_STRING:
        case FieldDescriptor::TYPE_BYTES:
            {
                reflection->SetString(msg, field_descriptor, field_value.data());
                break;
            }
        default:
            CWARNING_LOG("field type[%d] is error, field_value:[%s]", 
                    field_descriptor->type(), field_value.data());
            return false;
    }
    return true;
}

bool set_repeated_str_for_msg(
        const Reflection* ref,
        const FieldDescriptor* field_des,
        Message* ptr_msg,
        const std::vector<std::string>& values) {
    if (ref == NULL || field_des == NULL || ptr_msg == NULL) {
        CWARNING_LOG("input param is NULL");
        return false;
    }

    for (size_t i = 0; i < values.size(); ++i) {
        switch (field_des->type()) {
        case FieldDescriptor::TYPE_UINT64: {
            uint64_t sign = 0;
            if (!SignUtil::create_sign_md64(values[i], &sign)) {
                CWARNING_LOG("create_sign_md64 fail, value:%s", values[i].c_str());
                return false;
            }

            ref->AddUInt64(ptr_msg, field_des, sign);
            break;
        }
        case FieldDescriptor::TYPE_UINT32: {
            uint32_t sign = 0;
            if (!SignUtil::create_sign_md32(values[i], &sign)) {
                CWARNING_LOG("create_sign_md32 fail, value:[%s]", values[i].c_str());
                return false;
            }
            ref->AddUInt32(ptr_msg, field_des, sign);
            break;
        }
        case FieldDescriptor::TYPE_STRING: {
            ref->AddString(ptr_msg, field_des, values[i]);
            break;
        }
        default:
            CWARNING_LOG("invalid field_des->type(%d)", field_des->type());
            return false;
        }
    }
    return true;
}

bool set_str_for_msg(
        const Reflection* ref,
        const FieldDescriptor* field_des,
        Message* ptr_msg,
        const std::vector<std::string>& values) {
    if (ref == NULL || field_des == NULL || ptr_msg == NULL) {
        CWARNING_LOG("input param is NULL, ref(%p), field(%p), ptr_msg(%p)", ref, field_des, ptr_msg);
        return false;
    }

    if (values.size() == 0) {
        CWARNING_LOG("values size is zero, no need set");
        return true;
    }
    if (field_des->is_repeated()) {
        return set_repeated_str_for_msg(ref, field_des, ptr_msg, values);
    }

    if (values.size() > 1) {
        CWARNING_LOG("values size[%d] is greater than 1, "
            "but field is not repeated, only use values(0)", 
            values.size());
    }

    switch (field_des->type()) {
    case FieldDescriptor::TYPE_STRING: {
        ref->SetString(ptr_msg, field_des, values[0]);
        break;
    }
    case FieldDescriptor::TYPE_UINT64: {
        uint64_t sign = 0;
        if (!SignUtil::create_sign_md64(values[0], &sign)) {
            CWARNING_LOG("create_sign_md64 fail");
            return false;
        }
        ref->SetUInt64(ptr_msg, field_des, sign);
        break;
    }
    case FieldDescriptor::TYPE_UINT32: {
        uint32_t sign = 0;
        if (!SignUtil::create_sign_md32(values[0], &sign)) {
            CWARNING_LOG("create_sign_md32 fail");
            return false;
        }
        ref->SetUInt32(ptr_msg, field_des, sign);
        break;
    }
    default: {
        CWARNING_LOG("field type unsupport");
        return false;
    }
    }

    return true;
}

bool set_str_for_msg(
        const Reflection* ref,
        const FieldDescriptor* field_des,
        Message* ptr_msg,
        std::string& value) {
    std::vector<std::string> value_vec;
    value_vec.push_back(value);
    return set_str_for_msg(ref, field_des, ptr_msg, value_vec);
}

bool PbReflector::get_field(
        const Message& msg,
        const std::string& path,
        std::string* value) {
    PbPath pb_path;
    if (!pb_path.parse_path(path.c_str())) {
        CWARNING_LOG("parse from path(%s) fail", path.c_str());
        return false;
    }
    return get_field(msg, pb_path, value);
}

bool PbReflector::get_field(
        const Message& msg,
        PbPath& path,
        std::string* value)
{
    if (value == NULL) {
        CWARNING_LOG("input param is null");
        return false;
    }

    const char* field = 0;
    const Message* ptr_msg = &msg;
    *value = "";
    const Reflection* ref;
    const Descriptor* des;

    path.reset();
    const FieldDescriptor* field_des = NULL;
    while (1) {
        field = path.next_field();
        if (field == NULL) {
            break;
        }
        ref = ptr_msg->GetReflection();
        des = ptr_msg->GetDescriptor();

        field_des = des->FindFieldByName(field);
        if (field_des == NULL) {
            CWARNING_LOG("field_des error, [field_name=%s]", field);
            return false;
        }
        // only can reflect un-repeated field
        if (field_des->is_repeated()) {
            CWARNING_LOG("only handle un-repeated field");
            return false;
        }
        if (field_des->type() ==
                FieldDescriptor::TYPE_MESSAGE) {
            ptr_msg = &(ref->GetMessage(*ptr_msg, field_des));
        } else {
            if (!get_str_from_msg(ref, field_des, ptr_msg, value)) {
                CWARNING_LOG("get field from msg error");
                return false;
            }
        }
    }
    const char *s = path.next_field();
    if (s != NULL) {
        CWARNING_LOG("illegal path [path=%s]", s);
        return false;
    }
    return true;
}

bool re_get_field(
        const Message& msg,
        PbPath& path,
        std::vector<std::string>* values)
{
    const char* field = 0;
    const Message* ptr_msg = &msg;
    const Reflection* ref;
    const Descriptor* des;
    const FieldDescriptor* field_des = NULL;

    while (true) {
        field = path.next_field();
        if (field == NULL) {
            break;
        }
        ref = ptr_msg->GetReflection();
        des = ptr_msg->GetDescriptor();

        field_des = des->FindFieldByName(field);
        if (field_des == NULL) {
            CWARNING_LOG("field_des error, [field_name=%s]", field);
            return false;
        }

        // handle repeated solution
        if (field_des->is_repeated()) {
            if (field_des->type() ==
                    FieldDescriptor::TYPE_MESSAGE) {
                int32_t size = ref->FieldSize(*ptr_msg, field_des);
                for (int32_t i = 0; i < size; ++i) {
                    // TODO clone path
                    PbPath tmp_path(path);
                    if (!re_get_field(
                            ref->GetRepeatedMessage(*ptr_msg, field_des, i),
                            tmp_path,
                            values)) {
                        return false;
                    }
                }
            } else {
                if (!get_str_from_msg(ref, field_des, ptr_msg, values)) {
                    CWARNING_LOG("get field from msg error");
                    return false;
                }
            }
            break;
        } else {
            if (field_des->type() ==
                    FieldDescriptor::TYPE_MESSAGE) {
                ptr_msg = &(ref->GetMessage(*ptr_msg, field_des));
            } else {
                std::string value;
                if (!get_str_from_msg(ref, field_des, ptr_msg, &value)) {
                    CWARNING_LOG("get field from msg error");
                    return false;
                }
                values->push_back(value);
                const char *s = path.next_field();
                if (s != NULL) {
                    CWARNING_LOG("illegal path [path=%s]", s);
                    return false;
                }
            }
        }
    }
    return true;
}

bool PbReflector::get_field(
        const Message& msg,
        const std::string& path,
        std::vector<std::string>* values)
{
    PbPath pb_path;
    if (!pb_path.parse_path(path.c_str())) {
        CWARNING_LOG("parse from path(%s) fail", path.c_str());
        return false;
    }
    return get_field(msg, pb_path, values);
}

bool PbReflector::get_field(
        const Message& msg,
        PbPath& path,
        std::vector<std::string>* values)
{
    if (values == NULL) {
        CWARNING_LOG("input param is null");
        return false;
    }

    path.reset();

    return re_get_field(msg, path, values);
}

bool PbReflector::set_field(
        Message* msg,
        const std::string& field,
        const std::vector<std::string>& values) {
    if (msg == NULL) {
        CWARNING_LOG("invalid unput param");
        return false;
    }

    const Reflection* ref = msg->GetReflection();
    const Descriptor* des = msg->GetDescriptor();
    const FieldDescriptor* field_des = des->FindFieldByName(field);
    return set_str_for_msg(ref, field_des, msg, values);
}

bool PbReflector::set_field(
        Message* msg,
        std::string& field,
        std::string& value) {
    std::vector<std::string> value_vec;
    value_vec.push_back(value);
    return set_field(msg, field, value_vec);
}

bool PbReflector::set_field_val(
        Message* msg,
        const std::string& full_path,
        const std::vector<std::string>& values) {
    if (msg == NULL) {
        CWARNING_LOG("invalid unput param");
        return false;
    }
    PbPath path;
    if (!path.parse_path(full_path.c_str())) {
        CWARNING_LOG("path(%s) is invalid", full_path.c_str());
        return false;
    }
    const Reflection* ref = NULL;
    const Descriptor* des = NULL;
    const FieldDescriptor* field_des = NULL;
    while (true) {
        auto field = path.next_field();
        if (field == NULL) {
            CWARNING_LOG("invalid full path(%s)", full_path.c_str());
            return false;
        }
        ref = msg->GetReflection();
        des = msg->GetDescriptor();
        field_des = des->FindFieldByName(field);
        if (!field_des || field_des->type() != FieldDescriptor::TYPE_MESSAGE) {
            break;
        }
        msg = ref->MutableMessage(msg, field_des);
    }
    if (!ref || !des || !field_des) {
        CWARNING_LOG("find field(%s) fail", full_path.c_str());
        return false;
    }
    if (field_des->is_repeated()) {
        return set_msg_repeated_field_val(ref, field_des, values, msg);
    } else {
        return set_msg_field_val(ref, field_des, values[0], msg);
    }

}

bool PbReflector::set_field_val(
        Message* msg,
        const std::string& field,
        const std::string& value) {
    return set_field_val(msg, field, std::vector<std::string>(1, value));
}

}
}
}
