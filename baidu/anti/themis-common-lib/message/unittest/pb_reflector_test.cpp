// Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
// <AUTHOR>
// @brief 

#include <gtest/gtest.h>
#include <google/protobuf/descriptor.h>
#include "sample.pb.h"
#include "pb_reflector.h"
#include "sign_util.h"

namespace anti {
namespace themis {
namespace common_lib {

using namespace google::protobuf;
using anti::baselib::SignUtil;

void fill_message(SampleMessage& message)
{
    message.set_id(12);
    message.set_id2(13);
    message.set_name("fuxxxxxxxxxxxxxxxxx");
    message.set_db1(15.876);
    message.add_db2(12.345);
    message.add_db2(12.346);

    Field* field = message.mutable_field1();
    field->set_var(3);
    Item* item = field->mutable_item();
    item->set_time(1);

    field = message.add_field2();
    field->set_var(4);
    field->add_time(1001);
    field->add_time(1002);
    field = message.add_field2();
    field->set_var(5);
    field->add_time(1003);
    field->add_time(1004);
}

TEST(test_reflection_of_pb, test)
{
    SampleMessage message;
    fill_message(message);
    printf("%s\n", message.DebugString().c_str());
}

//TEST(test_reflection_of_pb, reflect_message)
//{
//    SampleMessage message;
//    fill_message(message);
//
//    const google::protobuf::Reflection* ref = message.GetReflection();
//    const Descriptor* des = message.GetDescriptor();
//
//    const FieldDescriptor* field1_des = des->FindFieldByName("id");
//    if (field1_des->type() == FieldDescriptor::TYPE_MESSAGE) {
//        const Message& field1_msg = ref->GetMessage(message, field1_des);
//        printf("%s\n", field1_msg.DebugString().c_str());
//    } else {
//        std::cout << ref->GetInt32(message, field1_des) << std::endl;
//    }
//}

TEST(test_reflection_of_pb, reflect_message)
{
    SampleMessage message;
    fill_message(message);

    const google::protobuf::Reflection* ref = message.GetReflection();
    const Descriptor* des = message.GetDescriptor();

    const FieldDescriptor* field2_des = des->FindFieldByName("field2");

    ASSERT_TRUE(field2_des != NULL);
    std::cout << "index:" << field2_des->index() << std::endl;
    std::cout << "size:" << ref->FieldSize(message, field2_des) << std::endl;

    const FieldDescriptor* field1_des = des->FindFieldByName("field1");
    const Message& field1_msg = ref->GetMessage(message, field1_des);
    printf("%s\n", field1_msg.DebugString().c_str());

    ref = field1_msg.GetReflection();
    des = field1_msg.GetDescriptor();
    field1_des = des->FindFieldByName("var");

    std::cout << ref->GetInt32(field1_msg, field1_des) << std::endl;
}

TEST(test_pb_reflector, Pb_path)
{
    PbPath pb_path;
    const char* ptr = "field1.var";
    pb_path.parse_path(ptr);
    EXPECT_STREQ(pb_path.next_field(), "field1");
    EXPECT_STREQ(pb_path.next_field(), "var");
    EXPECT_TRUE(pb_path.next_field() == NULL);
}

TEST(test_pb_reflector, Pb_path_with_head)
{
    PbPath pb_path;
    const char* ptr = "something:field1.var";
    pb_path.parse_path(ptr);
    EXPECT_STREQ(pb_path.next_field(), "field1");
    EXPECT_STREQ(pb_path.next_field(), "var");
    EXPECT_TRUE(pb_path.next_field() == NULL);
}

TEST(test_pb_reflector, reflect)
{
    SampleMessage message;
    fill_message(message);

    std::string value;

    PbPath pb_path;
    const char* ptr = "field1.var";
    pb_path.parse_path(ptr);

    ASSERT_TRUE(PbReflector::get_field(
            message,
            pb_path,
            &value));
    EXPECT_STREQ(value.c_str(), "3");

    const char* ptr2 = "db1";
    pb_path.parse_path(ptr2);

    ASSERT_TRUE(PbReflector::get_field(
            message,
            pb_path,
            &value));
    EXPECT_STREQ(value.c_str(), "15.876");
}

TEST(test_pb_reflector, reflect2)
{
    SampleMessage message;
    fill_message(message);

    std::string value;
    ASSERT_TRUE(PbReflector::get_field(
            message,
            "field1.var",
            &value));
    EXPECT_STREQ(value.c_str(), "3");

    ASSERT_TRUE(PbReflector::get_field(
            message,
            "db1",
            &value));
    EXPECT_STREQ(value.c_str(), "15.876");
}

TEST(test_pb_reflector, reflect_repeated)
{
    SampleMessage message;
    fill_message(message);

    std::vector<std::string> value;

    PbPath pb_path;
    const char* ptr = "field2.var";
    pb_path.parse_path(ptr);

    ASSERT_TRUE(PbReflector::get_field(
            message,
            pb_path,
            &value));

    EXPECT_EQ(value.size(), 2u);
    EXPECT_STREQ(value[0].c_str(), "4");
    EXPECT_STREQ(value[1].c_str(), "5");

    const char* ptr2 = "db2";
    pb_path.parse_path(ptr2);

    ASSERT_TRUE(PbReflector::get_field(
            message,
            pb_path,
            &value));
    EXPECT_STREQ(value[2].c_str(), "12.345");
    EXPECT_STREQ(value[3].c_str(), "12.346");

    message.mutable_field2(0)->clear_var();
    const char* ptr3 = "field2.var";
    pb_path.parse_path(ptr3);
    value.clear();
    ASSERT_TRUE(PbReflector::get_field(
            message,
            pb_path,
            &value));
    EXPECT_EQ((unsigned int) message.field2_size(), value.size());
}

TEST(test_pb_reflector, reflect_repeated_after_repeated)
{
    SampleMessage message;
    fill_message(message);

    std::vector<std::string> value;

    PbPath pb_path;
    const char* ptr = "field2.time";
    pb_path.parse_path(ptr);

    ASSERT_TRUE(PbReflector::get_field(
            message,
            pb_path,
            &value));

    EXPECT_EQ(value.size(), 4u);
    EXPECT_STREQ(value[0].c_str(), "1001");
    EXPECT_STREQ(value[1].c_str(), "1002");
    EXPECT_STREQ(value[2].c_str(), "1003");
    EXPECT_STREQ(value[3].c_str(), "1004");
}

TEST(test_pb_reflector, reflect_set_string_value) {
    SampleMessage message;
    std::string value = "test_name";
    uint64_t sign = 0;
    ASSERT_TRUE(SignUtil::create_sign_md64(value, &sign));
    uint32_t result[2];
    result[0] = *(uint32_t*)(&sign);
    result[1] = *((uint32_t*)(&sign) + 1);
    result[0] += result[1];

    std::string field = "name";
    ASSERT_TRUE(PbReflector::set_field(
                &message,
                field,
                value));
    ASSERT_EQ(value, message.name());

    field = "db3";
    ASSERT_TRUE(PbReflector::set_field(
                &message,
                field,
                value));
    ASSERT_EQ(message.db3(), sign);

    field = "db4";
    ASSERT_TRUE(PbReflector::set_field(
                &message,
                field,
                value));
    ASSERT_EQ(message.db4(), result[0]);
}

TEST(test_pb_reflector, pb_ser_and_des)
{
    ImprSampleMessage a;
    a.set_id(12);
    a.set_id2(0.5);
    a.set_name("nobody");
    a.set_add(16);

    std::string binary;

    a.SerializeToString(&binary);

    SampleMessage message;
    message.ParseFromString(binary);

    fprintf(stderr, "%s\n", message.DebugString().c_str());

    message.SerializeToString(&binary);

    ImprSampleMessage b;
    b.ParseFromString(binary);

    fprintf(stderr, "%s\n", b.DebugString().c_str());
}

TEST(test_pb_reflector, set_msg_field_val) {
    SampleMessage message;
    fill_message(message);
    std::string name = "name";
    const Reflection* ref = message.GetReflection();
    const Descriptor* des = message.GetDescriptor();
    const FieldDescriptor* field_des = des->FindFieldByName(name);
    ASSERT_TRUE(PbReflector::set_msg_field_val(ref, field_des, "xxx", &message));
    ASSERT_STREQ("xxx", message.name().c_str());

    field_des = des->FindFieldByName("db4");
    ASSERT_TRUE(PbReflector::set_msg_field_val(ref, field_des, "32", &message));
    ASSERT_EQ(32U, message.db4());

    field_des = des->FindFieldByName("db3");
    ASSERT_TRUE(PbReflector::set_msg_field_val(ref, field_des, "64", &message));
    ASSERT_EQ(64U, message.db3());

    field_des = des->FindFieldByName("db1");
    ASSERT_TRUE(PbReflector::set_msg_field_val(ref, field_des, "0.1", &message));
    ASSERT_EQ(0.1, message.db1());
}

TEST(test_pb_reflector, set_msg_repeated_field_val) {
    SampleMessage message;
    fill_message(message);
    const Reflection* ref = message.GetReflection();
    const Descriptor* des = message.GetDescriptor();
    const FieldDescriptor* field_des = des->FindFieldByName("str1");
    ASSERT_TRUE(PbReflector::set_msg_repeated_field_val(ref, field_des, {"123", "456"}, &message));
    ASSERT_EQ(2, message.str1_size());
    ASSERT_EQ("123", message.str1(0));
    ASSERT_EQ("456", message.str1(1));

    field_des = des->FindFieldByName("db2");
    ASSERT_TRUE(PbReflector::set_msg_repeated_field_val(ref, field_des, {"0.1", "0.2"}, &message));
    printf("proto:%s", message.ShortDebugString().c_str());
    ASSERT_EQ(4, message.db2_size());
    ASSERT_EQ(0.1, message.db2(2));
    ASSERT_EQ(0.2, message.db2(3));

    field_des = des->FindFieldByName("db6");
    ASSERT_TRUE(PbReflector::set_msg_repeated_field_val(ref, field_des, {"64", "65"}, &message));
    ASSERT_EQ(2, message.db6_size());
    ASSERT_EQ(64U, message.db6(0));
    ASSERT_EQ(65U, message.db6(1));

    field_des = des->FindFieldByName("db5");
    ASSERT_TRUE(PbReflector::set_msg_repeated_field_val(ref, field_des, {"32", "33"}, &message));
    ASSERT_EQ(2, message.db5_size());
    ASSERT_EQ(32, message.db5(0));
    ASSERT_EQ(33, message.db5(1));

    Field msg;
    ref = msg.GetReflection();
    des = msg.GetDescriptor();
    field_des = des->FindFieldByName("time");
    ASSERT_TRUE(PbReflector::set_msg_repeated_field_val(ref, field_des, {"32", "33"}, &msg));
    ASSERT_EQ(2, msg.time_size());
    ASSERT_EQ(32, msg.time(0));
    ASSERT_EQ(33, msg.time(1));
}

TEST(test_pb_reflector, str2num) {
    uint32_t xxx = 123U;
    ASSERT_FALSE(str2num<uint32_t>("xxx", &xxx));
    ASSERT_EQ(xxx, 123U);
    ASSERT_TRUE(str2num<uint32_t>("1000", &xxx));
    ASSERT_EQ(xxx, 1000U);
    uint64_t xxx_uint64 = 123LU;
    ASSERT_TRUE(str2num<uint64_t>("2000", &xxx_uint64));
    ASSERT_EQ(xxx_uint64, 2000U);
}

TEST(test_pb_reflector, set_field_ok) {
    SampleMessage message; 
    fill_message(message);
    message.clear_str1();
    std::vector<std::string> value;
    ASSERT_TRUE(PbReflector::set_field(&message, "str1", value));
    value.push_back("123");
    value.push_back("456");
    ASSERT_TRUE(PbReflector::set_field(&message, "str1", value));
    EXPECT_EQ(message.str1_size(), 2);
    EXPECT_STREQ("123", message.str1(0).c_str());
    EXPECT_STREQ("456", message.str1(1).c_str());
    ASSERT_TRUE(PbReflector::set_field(&message, "name", value));
    EXPECT_STREQ("123", message.name().c_str());
}

TEST(test_pb_reflector, set_full_path_fail_with_field_not_found) {
    SampleMessage message; 
    fill_message(message);
    std::vector<std::string> value;
    ASSERT_FALSE(PbReflector::set_field_val(&message, "xxx", value));
    value.push_back("123");
    value.push_back("456");
    value.push_back("-123");
    ASSERT_FALSE(PbReflector::set_field_val(&message, "xxx.yyy", value));
    ASSERT_FALSE(PbReflector::set_field_val(&message, "field1.xxx", value));
}

TEST(test_pb_reflector, set_full_path_ok) {
    SampleMessage message; 
    fill_message(message);
    message.clear_field1();
    message.clear_str1();
    std::vector<std::string> value;
    ASSERT_TRUE(PbReflector::set_field_val(&message, "str1", value));
    value.push_back("123");
    value.push_back("456");
    value.push_back("-123");
    ASSERT_TRUE(PbReflector::set_field_val(&message, "str1", value));
    EXPECT_EQ(message.str1_size(), 3);
    EXPECT_STREQ("123", message.str1(0).c_str());
    EXPECT_STREQ("456", message.str1(1).c_str());
    EXPECT_STREQ("-123", message.str1(2).c_str());

    ASSERT_TRUE(PbReflector::set_field_val(&message, "field1.var", value));
    EXPECT_EQ(123, message.field1().var());

    ASSERT_TRUE(PbReflector::set_field_val(&message, "field1.time", value));
    EXPECT_EQ(3, message.field1().time_size());
    EXPECT_EQ(123, message.field1().time(0));
    EXPECT_EQ(456, message.field1().time(1));
    EXPECT_EQ(-123, message.field1().time(2));
}

} // end namespace common_lib
} // end namespace themis
} // end namespace anti
