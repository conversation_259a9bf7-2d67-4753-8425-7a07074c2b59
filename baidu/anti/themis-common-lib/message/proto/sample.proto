// Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
// <AUTHOR>
// @brief  it's a sample of protobuf message for testing!

message Item {
    optional int32 time = 1;
}

message Field {
    optional int32 var = 1;
    optional Item item = 2;
    repeated int32 time = 3;
}

message SampleMessage {
    required int32 id = 1;
    required float id2 = 2;
    required string name = 3;

    optional Field field1 = 4;
    repeated Field field2 = 5;
    optional double db1 = 6;
    repeated double db2 = 7;
    optional uint64 db3 = 8;
    optional uint32 db4 = 9;
    repeated string str1 = 10;
    repeated uint32 db5 = 11;
    repeated uint64 db6 = 12;
}

message ImprSampleMessage {
    required int32 id = 1;
    required float id2 = 2;
    required string name = 3;

    optional Field field1 = 4;
    repeated Field field2 = 5;

    optional int32 add = 6;
}
