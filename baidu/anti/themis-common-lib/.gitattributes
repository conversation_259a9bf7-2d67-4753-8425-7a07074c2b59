* text=auto !eol
/BCLOUD -text
/build.sh -text
/ci.xml -text
dict_manager/conf/file_dict_manager.conf -text
dict_manager/dev/cn_map_file_dict.cpp -text
dict_manager/dev/corr_cnt_dict.cpp -text
dict_manager/dev/file_dict_factory.cpp -text
dict_manager/dev/file_dict_interface.cpp -text
dict_manager/dev/file_dict_manager.cpp -text
dict_manager/dev/gray_file_dict.cpp -text
dict_manager/dev/ipmap.cpp -text
dict_manager/dev/ipmap_dict.cpp -text
dict_manager/dev/multi_column_file_dict.cpp -text
dict_manager/dev/string_match_dict.cpp -text
dict_manager/dev/string_util.cpp -text
dict_manager/inc/array_file_dict.h -text
dict_manager/inc/array_file_dict.hpp -text
dict_manager/inc/cn_map_file_dict.h -text
dict_manager/inc/corr_cnt_dict.h -text
dict_manager/inc/dayu_tvec.h -text
dict_manager/inc/file_dict_factory.h -text
dict_manager/inc/file_dict_interface.h -text
dict_manager/inc/file_dict_manager.h -text
dict_manager/inc/gray_file_dict.h -text
dict_manager/inc/ipmap.h -text
dict_manager/inc/ipmap_dict.h -text
dict_manager/inc/key_value_dict.h -text
dict_manager/inc/multi_column_file_dict.h -text
dict_manager/inc/string_match_dict.h -text
dict_manager/inc/string_util.h -text
dict_manager/inc/value_tmpl.h -text
dict_manager/unittest/array_file_dict_test.cpp -text
dict_manager/unittest/build.sh -text
dict_manager/unittest/conf/array_test.conf -text
dict_manager/unittest/conf/corr_cn_dict_test.conf -text
dict_manager/unittest/conf/file_dict_manager.conf -text
dict_manager/unittest/conf/gray_test.conf -text
dict_manager/unittest/conf/kmv_test.conf -text
dict_manager/unittest/conf/mcf_test.conf -text
dict_manager/unittest/conf/smd_test.conf -text
dict_manager/unittest/corr_cnt_dict_test.cpp -text
dict_manager/unittest/data/array.data -text
dict_manager/unittest/data/cnt_group.txt -text
dict_manager/unittest/data/cnt_group.txt.inv -text
dict_manager/unittest/data/corr_cnt.dat -text
dict_manager/unittest/data/empty.data -text
dict_manager/unittest/data/gray.data -text
dict_manager/unittest/data/invalid_array.data -text
dict_manager/unittest/data/invalid_gray.data -text
dict_manager/unittest/data/kmv.data -text
dict_manager/unittest/data/mcf.data -text
dict_manager/unittest/data/smd.data -text
dict_manager/unittest/data/stringkv.data -text
dict_manager/unittest/data/userid_tradeid_map.dat -text
dict_manager/unittest/file_dict_factory_test.cpp -text
dict_manager/unittest/file_dict_interface_test.cpp -text
dict_manager/unittest/file_dict_manager_test.cpp -text
dict_manager/unittest/gray_file_dict_test.cpp -text
dict_manager/unittest/key_multi_value_dict_test.cpp -text
dict_manager/unittest/multi_column_file_dict_test.cpp -text
dict_manager/unittest/string_match_dict_test.cpp -text
dict_manager/unittest/string_util_test.cpp -text
message/dev/pb_reflector.cpp -text
message/inc/pb_reflector.h -text
message/proto/sample.proto -text
message/unittest/pb_reflector_test.cpp -text
sliding_window/dev/archive.cpp -text
sliding_window/inc/archive.h -text
sliding_window/inc/auto_click_segment.hpp -text
sliding_window/inc/click_node.hpp -text
sliding_window/inc/click_segment.hpp -text
sliding_window/inc/goe_list.hpp -text
sliding_window/inc/item.h -text
sliding_window/inc/item.hpp -text
sliding_window/inc/multi_segment.h -text
sliding_window/inc/sliding_window.h -text
sliding_window/inc/sliding_window.hpp -text
sliding_window/inc/utils.h -text
sliding_window/inc/utils.hpp -text
sliding_window/unittest/archive_test.cpp -text
sliding_window/unittest/auto_click_segment_test.cpp -text
sliding_window/unittest/build.sh -text
sliding_window/unittest/click_segment_test.cpp -text
sliding_window/unittest/data/seq.dat -text
sliding_window/unittest/goe_list_test.cpp -text
sliding_window/unittest/item_test.cpp -text
sliding_window/unittest/multi_segment_test.cpp -text
sliding_window/unittest/sliding_window_test.cpp -text
sliding_window/unittest/utils_test.cpp -text
thread/inc/thread.h -text
thread/inc/threadsafe_queue.h -text
thread/unittest/thread_test.cpp -text
thread/unittest/threadsafe_queue_test.cpp -text
thread_pool/dev/thread_pool.cpp -text
thread_pool/inc/task_queue.h -text
thread_pool/inc/thread_pool.h -text
thread_pool/unittest/thread_pool_test.cpp -text
