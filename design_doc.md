Accumulator 内存优化二期：二级累积器重构

# 背景与现状
## 项目背景
第一期在三种简单Item累积器（`SegmentFeatureAccumulator`、`RatioFeatureAccumulator`、`DistributionFeatureAccumulator`）中集成了自研内存池（`GCSlabMempool32`）、哈希表（`PoolHashMap`）和滑动窗口（`SlidingWindowV2`）。

**第二期目标**：优化两种复杂Item累积器，特点是内部有嵌套的std::unordered_map容器，需要替换为PoolMiniHashMap实现。

`DistinctFeatureAccumulator`**和**`ConcentrationFeatureAccumulator`**占总内存的61.6%，是内存优化的关键目标，预计整体内存可降低30%+。**

## 当前实现分析
### Item 数据结构
#### **DistinctItem**
```
class DistinctItem {
    std::unordered_map<uint64_t, int64_t> _map;  // 需要替换为PoolMiniHashMap
    int64_t _total;
};
```
* 功能：去重计数统计，维护 *data_view_sign* 到计数的映射
* 结构相对简单：1 个 `std::unordered_map` 和 1 个累计值
* 内存占用：`sizeof(DistinctItem) = 64 Byte`

#### **ConcentrationItem**
```
template<typename Number, typename Node>
class TopkSet {
    uint64_t _k;
    Number _total;
    std::unordered_map<uint64_t, Number> _map;      // 需要替换
    std::set<Node> _k_set;
};

typedef TopkSet<int64_t, ConNode> TopkHeap; 
class ConcentrationItem {
    Number _cumulant;
    std::unordered_map<uint64_t, Number> _value_map;        // 需要替换
    std::unordered_map<uint64_t, std::string> _str_value_map; // 需要替换
    TopkHeap _topk_heap; // 内部也有std::unordered_map和std::set
};
```
* 功能：流量集中度分析
* 结构复杂：3 个 `std::unordered_map`、1 个 `set::set`、3 个 `uint64`
* 内存占用：`sizeof(ConcentrationItem) = 240` 字节

### 一期二期差异
* 一期简单的`SegmentItem`和 `RatioItem` 只是对基础类型的封装，本身的成员不涉及堆内存分配，可以直接由 `FeatureAccumulator` 创建后传入 `SlidingWindowV2` 即可，只需要由 `SlidingWindowV2` 持有内存池。
* 二期复杂如果要使用内存池的 `PoolMiniHashMap` 替换 Item 中的 `std::unordered_map` ，必须在 `FeatureAccumulator` 创建 `Item` 时传入内存池指针，这要求内存池生命周期由 `FeatureAccumulator` 管理。

### 数据流转模式
```
// 统一的处理模式
bool FeatureAccumulator::update_and_query(FeatureValueProto* fea) {
    // 1. 基于data_view_sign创建临时Item（二期需要传入内存池指针）
    Item item(fea->data_view_sign(), count, &_mem_pool);  // 新增内存池参数
    
    // 2. 进入滑动窗口：按view_sign+coord查找，存在则合并，不存在则插入
    const Item* result = nullptr;
    _window.enter(fea->view_sign(), fea->coord(), item, &result);
    
    // 3. 将累积结果写回FeatureValueProto
    return _fillup_feature(result, fea);
}
```
## 一期遗留问题
### 开关粒度
一期在适配三个一级累积器时主要是通过修改原版 `FeatureAccumulator` 的代码，添加了两个成员变量，在涉及到使用 `SlidingWindow` 的地方，通过判断 flag 来决定走 `SlidingWindowV2` 还是 `SlidingWindow`。

```
SlidingWindow _std_window;
SlidingWindowV2 _pool_window;
bool _use_mem_pool;
```
```
if (_use_mem_pool) {
    return _pool_window.enter(fea.view_sign(), fea.coord(), SegmentItem(_get_cumulant(fea)));
} else {
    return _std_window.enter(fea.view_sign(), fea.coord(), SegmentItem(_get_cumulant(fea)));
}
```
### PoolMiniHashMap 拷贝构造
`PoolMiniHashMap` 目前的拷贝构造是默认实现，相当于浅拷贝，如果要对 `PoolMiniHashMap` 做拷贝的话，存在两个 `PoolMiniHashMap` 对象使用同一个 `bucket` 数组（double free / use after free）的问题。

# 总体设计 
## 整体类层次
copy 原版 `xxxFeatureAccumulator` 的代码，新建一个 `xxxFeatureAccumulatorV2`，将其中 `SlidingWindow` 替换为 `SlidingWindowV2`，然后新增一个内存池成员变量。取舍：

1. 问题是会带来很多重复代码
2. 好处是**整体架构会更清晰，**`xxxFeatureAccumulator` 的体积会小一点点（一个 `SlidingWindow` 的体积），分支会少一些（有分支预测，性能影响很小）。
3. 一期已经验证了基于内存池的架构是稳定的，后续如果要迭代 `xxxFeatureAccumulator` 也应该以 Pool 版本为主。
4. 二期不仅要使用 `SlidingWindowV2`，还需要使用新版的 `PoolItem`，需要 `if else` 包裹的代码量更大。

> 为什么不用模板，将 SlidingWindow 和 Item 作为模板参数？
> Pool 版本和原版的Item 以及 SlidingWindow 的创建和初始化接口不同，在 C++11 下要基于模板来做需要使用大量的模板特性，复杂度比较高。
**新架构层次**：

```
工厂层：FeatureAccumulatorFactory
├── 原版本累积器（保持不变）
│   ├── SegmentFeatureAccumulator
│   ├── RatioFeatureAccumulator
│   ├── DistributionFeatureAccumulator
│   ├── DistinctFeatureAccumulator
│   └── ConcentrationFeatureAccumulator
└── V2版本累积器
    ├── SegmentFeatureAccumulatorV2 
    ├── RatioFeatureAccumulatorV2
    ├── DistributionFeatureAccumulatorV2
    ├── DistinctFeatureAccumulatorV2
    └── ConcentrationFeatureAccumulatorV2
```
## 数据结构优化
### DistinctItem
**新实现 DistinctItemV2：**

* `std::unordered_map -> PoolMiniHashMap`
* 使用新的 PoolMiniHashMap 解决元素较少带来的桶数组浪费

**预计优化效果**：

* Item 总大小从 64 Bytes 降至 32 Bytes
* 消除桶空间浪费，提高内存利用率

### ConcentrationItem
**新实现 ConcentrationItem：**

* 去除嵌套的 `TopkSet` 结构，将 TopK 逻辑打平到 Item 中
* `std::set -> std::vector`，采用 `std::sort` 维护 TopK vector，适配小 k 值场景
* TopK 排序逻辑与原版本完全相同

**预计优化效果：**

* Item 总大小从 240 Bytes 将至 84 Bytes
* 使用新的 MiniHashMap 解决元素较少带来的桶数组浪费
* 消除 `TopkSet` 和 Item 之间的两个 map 的重复存储
* 去掉 `std::set` 的管理内存开销

## 内存池管理
### 内存池生命周期
**从 **`SlidingWindow`** 管理上移到 **`xxxFeatureAccumulator`** 管理**：

* 统一由 `xxxFeatureAccumulator` 创建和管理内存池生命周期。
* 为一期涉及的三个累积器也重构为这种模式，统一逻辑。

### 内存池共享
* 同一 `xxxFeatureAccumulator` 内的所有 `HashMap` 共享同一内存，包括 `SlidingWindowV2`、复杂 Item 内部的多个 `PoolMiniHashMap`

>     * **比较特殊的是 **`ConcentrationItem`** 中的 **`std::unordered_map<uint64, std::string>`**，也需要替换成 **`PoolMiniHashMap<unit64, std::string, GCSlabMemPool32>`** 有两种处理方式：**
>         * `ConcentrationFeatureAccumulator` 持有两个内存池，其中一个专门用来分配 `std::string` 底层的 `cstr`，好处是所有堆内存都由内存池分配，监控更准确，坏处是实现更复杂，应该需要自己实现一个 String 类型。
>         * `ConcentrationFeatureAccumulator` 只持有一个内存池，因为 std::string 本身是定长的，由它自己的 RAII 释放 `cstr`，好处是实现简单，坏处是这部分字符串内存不好监控。
> 
> 
# 详细实现
## DistinctFeatureAccumulatorV2 实现
### 数据结构
原版完全相同的对外接口，仅在内部实现上使用内存池优化。

主要变化是将`SlidingWindow`替换为`SlidingWindowV2`，并由 DistinctFeatureAccumulatorV2 管理内存池生命周期。

```
class DistinctFeatureAccumulatorV2 : public FeatureAccumulatorInterface {
    // 保持与原版完全相同的公有接口
    // 内部实现使用内存池优化
    GCSlabMempool32 _mem_pool;      // 统一管理的内存池
    SlidingWindowV2 _pool_window;  // 使用池化版本的滑动窗口
};
```
### 初始化实现
**内存池初始化策略**：

* 收集所有组件需要的 ItemSize 大小
* 去重
* 初始化

```
bool DistinctFeatureAccumulatorV2::init(const comcfg::ConfigUnit& conf) {
    // ......
    // 读取配置字段，初始化成员变量（与原版相同）
    // ......
    
    // 1. 初始化内存池 - 这是Pool版本的核心优化
    _init_memory_pool();
    
    // 2. 初始化滑动窗口，使用外部内存池模式
    // 与一期不同，内存池现在由FeatureAccumulator管理而非SlidingWindow创建
    const int CACHE_STEP_NUM = 1;
    int max_step_num = CACHE_STEP_NUM + window_length / step_length;
    _pool_window.init(step_length, max_step_num, window_length, &_mem_pool);
    
    return true;
}

bool DistinctFeatureAccumulatorV2::_init_memory_pool() {
    std::vector<uint32_t> slab_sizes;
    
    // 静态收集所有需要的 ItemSize
    Item::append_required_slab_sizes(slab_sizes);
    SlidingWindowV2::append_required_slab_sizes(slab_sizes);
    
    // 排序并去重
    std::sort(slab_sizes.begin(), slab_sizes.end());
    slab_sizes.erase(std::unique(slab_sizes.begin(), slab_sizes.end()), slab_sizes.end());
    
    return _mem_pool.create(slab_sizes.data(), slab_sizes.size(), FLAGS_pool_mempool_block_item_num) == 0;
}
```
### 核心业务逻辑
1. 临时 Item 创建时需要传入内存池指针，确保其内部 `PoolMiniHashMap` 使用正确的内存池
2. `SlidingWindow.enter()` 会通过拷贝构造将临时Item复制到窗口中
3. `PoolMiniHashMap` 的拷贝构造需要实现深拷贝，拷贝与被拷贝对象共用内存池

```
bool DistinctFeatureAccumulatorV2::update(const FeatureValueProto& fea) {
    // 创建临时Item，关键是传入内存池指针
    Item temp_item(fea.data_view_sign(), 1L, &_mem_pool);
    return _pool_window.enter(fea.view_sign(), fea.coord(), temp_item);
}

bool DistinctFeatureAccumulatorV2::update_and_query(FeatureValueProto* fea) {
    // 临时Item的生命周期仅在此函数内
    // 但其数据会通过PoolMiniHashMap的深拷贝正确传递到SlidingWindow中
    Item temp_item(fea->data_view_sign(), 1L, &_mem_pool);
    
    const Item* res = NULL;
    _pool_window.enter(fea->view_sign(), fea->coord(), temp_item, &res);
    
    return _fillup_feature(res, fea);
}

bool DistinctFeatureAccumulatorV2::query(
        const FValQRequestProto& request,
        FValQResponseProto* response) const {
   
    const Item* item = _pool_window.query_segment(request.view_sign());
    
    if (!request.has_max_value_num()) {
        response->add_value(boost::lexical_cast<std::string>(item->distinct_num()));
    } else {
        uint32_t count = 0;
        // 使用traverse接口遍历map
        // 注意：遍历顺序与 std::unordered_map 不同，但是这里只是遍历查找
        item->map().traverse([&](const uint64_t& key, const int64_t& value) {
            if (count < request.max_value_num()) {
                response->add_value(std::to_string(key));
                ++count;
            }
        });
    }
    return true;
}

bool DistinctFeatureAccumulatorV2::_fillup_feature(const Item* item, FeatureValueProto* fea) const {
    fea->set_valid(true);
    if (item == NULL) {
        fea->set_value("0");
        return true;
    }
    
    // distinct_num()返回map中不同key的数量
    int64_t distinct_num = item->distinct_num();
    fea->set_value(boost::lexical_cast<std::string>(distinct_num));
    
    if (!FLAGS_need_set_distinct_data_view_signs) {
        return true;
    }
    
    uint32_t count = 0;
    fea->clear_data_view_signs();
    // 使用traverse遍历所有data_view_sign
    // 虽然顺序可能与原版不同，但对于distinct统计来说顺序不影响正确性
    item->map().traverse([&](const uint64_t& key, const int64_t& value) {
        if (count < FLAGS_max_distinct_data_view_signs_num) {
            fea->add_data_view_signs(std::to_string(key));
            ++count;
        }
    });
    
    return true;
}
```
## ConcentrationFeatureAccumulatorV2实现
### 数据结构
```
class ConcentrationFeatureAccumulatorV2 : public FeatureAccumulatorInterface {
    // 保持所有原版接口不变
    SlidingWindowV2 _pool_window;
    GCSlabMempool32 _mem_pool;
};
```
### 初始化实现
```
bool ConcentrationFeatureAccumulatorV2::init(const comcfg::ConfigUnit& conf) {
    // ......
    // 读取配置字段，初始化成员变量
    // ......
    
    // 1. 初始化内存池 - 收集所有组件需要的slab大小
    _init_memory_pool()；
    
    // 2. 初始化滑动窗口
    const int CACHE_STEP_NUM = 1;
    int max_step_num = CACHE_STEP_NUM + window_len / step_length;
    _pool_window.init(step_length, max_step_num, window_len, &_mem_pool);
    
    // 3. 如果需要，初始化ClickSegment（用于acc_main_view模式）
    if (_acc_main_view && !_segment.init(window_len)) {
        return false;
    }
    return true;
}

bool ConcentrationFeatureAccumulatorV2::_init_memory_pool() {
    std::vector<uint32_t> slab_sizes;
    
    // ConcentrationItem需要的slab包括：
    // - value_map的节点（uint64_t, int64_t）
    // - str_value_map的节点（uint64_t, std::string）
    Item::append_required_slab_sizes(slab_sizes);
    
    // SlidingWindowV2需要的slab
    SlidingWindowV2::append_required_slab_sizes(slab_sizes);
    
    // 去重并排序
    std::sort(slab_sizes.begin(), slab_sizes.end());
    slab_sizes.erase(std::unique(slab_sizes.begin(), slab_sizes.end()), slab_sizes.end());
    
    return _mem_pool.create(slab_sizes.data(), slab_sizes.size(), FLAGS_pool_mempool_block_item_num) == 0;
}
```
### 核心业务逻辑
1. Item 初始化时需要传入内存池，用于内部两个 `PoolMiniHashMap`
2. TopK 的计算逻辑保持与原版完全一致

```
bool ConcentrationFeatureAccumulatorV2::update_and_query(FeatureValueProto* fea) {
    if (fea->has_data_view_sign()) {
        Item item;
        if (!_init_con_item(&item, *fea)) {
            return false;
        }
        
        const Item* res = NULL;
        const SegLimitNode* seg_res = NULL;
        
        _pool_window.enter(fea->view_sign(), fea->coord(), item, &res);
        if (_acc_main_view) {
            seg_res = _segment.query_segment(fea->view_sign());
        }
        
        return _fillup_feature(res, seg_res, fea);
    } else if (_acc_main_view) {
        _segment.enter(SegLimitNode(fea->view_sign(), fea->coord()), NULL);
        return _fillup_feature(NULL, NULL, fea);
    }
    return false;
}

bool ConcentrationFeatureAccumulatorV2::_init_con_item(Item* item, const FeatureValueProto& fea) {
    // 根据是否需要保存data_view的字符串值，调用不同的初始化方法
    // 关键是传入内存池指针
    if (_need_top_data_views) {
        if (!fea.has_data_view_value()) {
            return false;
        }
        // 初始化时传入内存池，同时保存字符串值
        if (!item->init(_top, 1LU, fea.data_view_sign(), fea.data_view_value(), &_mem_pool)) {
            return false;
        }
        return true;
    }
    // 只保存数值信息
    return item->init(_top, 1LU, fea.data_view_sign(), &_mem_pool);
}

```
## Item实现
### DistinctItemV2实现
* 将`std::unordered_map`替换为`PoolMiniHashMap`，初始桶数优化为1（大部分场景只有1-2个data_view）
* 保持所有对外接口不变，确保行为兼容
* 序列化格式与原版完全兼容

```
class DistinctItemV2 {
private:
    PoolMiniHashMap<uint64_t, int64_t, GCSlabMempool32> _map;
    int64_t _total;

public:
    DistinctItemV2() : _map(nullptr), _total(0) {}
    
    // 带内存池的构造函数
    DistinctItemV2(uint64_t data_view_sign, int64_t count, GCSlabMempool32* pool) 
        : _map(pool, 1),  // 初始桶数为1
          _total(count) {
        if (data_view_sign != 0) {
            _map.emplace(data_view_sign, count);
        }
    }
    
    // 累积操作 - 语义与原版完全一致
    DistinctItemV2& operator+=(const DistinctItemV2& other) {
        _total += other._total;
        
        // 使用traverse遍历other的map，合并到当前map中
        // 注意：虽然遍历顺序与std::unordered_map不同，但累积结果相同
        other._map.traverse([this](const uint64_t& key, const int64_t& value) {
            auto result = _map.emplace(key, value);
            if (!result.second) {
                // 键已存在，累加值
                *(result.first) += value;
            }
        });
        
        return *this;
    }
    
    // 减法操作 - 语义与原版完全一致
    DistinctItemV2& operator-=(const DistinctItemV2& other) {
        _total -= other._total;
        
        other._map.traverse([this](const uint64_t& key, const int64_t& value) {
            int64_t* value_ptr = _map.find(key);
            if (value_ptr) {
                *value_ptr -= value;
                if (*value_ptr <= 0) {
                    _map.remove(key);
                }
            }
        });
        
        return *this;
    }
    
    // 查询接口
    int64_t distinct_num() const {
        return _map.size();  // 返回不同key的数量
    }
    
    // 序列化接口 - 格式与原版std::unordered_map完全兼容
    template<typename Archive>
    bool serialize(Archive* ar) const {
        // 1. 写入_total
        if (!t_write<int64_t, Archive>(_total, ar)) {
            CWARNING_LOG("serialize _total failed");
            return false;
        }
        
        // 2. 写入map大小
        size_t map_size = _map.size();
        if (!t_write<size_t, Archive>(map_size, ar)) {
            CWARNING_LOG("serialize map_size failed");
            return false;
        }
        
        // 3. 遍历写入所有键值对
        // 注意：虽然遍历顺序与原版不同
        // 也就是同一内存状态，std::unordered_map 和 PoolMiniHashMap写出的 ckpt 内容是不同的，但反序列化后的内存状态一致
        bool success = true;
        _map.traverse([&](const uint64_t& key, const int64_t& value) {
            if (!success) return;
            
            if (!t_write<uint64_t, Archive>(key, ar) || 
                !t_write<int64_t, Archive>(value, ar)) {
                CWARNING_LOG("serialize map entry failed");
                success = false;
            }
        });
        
        return success;
    }
    
    // 静态方法：提供slab配置信息
    static void append_required_slab_sizes(std::vector<uint32_t>& slab_sizes) {
        // DistinctItem使用的PoolMiniHashMap节点大小：
        // sizeof(VAddr) + sizeof(uint64_t) + sizeof(int64_t) = 4 + 8 + 8 = 20字节
        slab_sizes.push_back(PoolMiniHashMap<uint64_t, int64_t, GCSlabMempool32>::get_node_size());
    }
};
```
### ConcentrationItemV2实现
* 核心优化：去除 `TopkSet`嵌套结构，将 TopK 逻辑打平到 Item 中
* 使用`std::vector<ConNode>`代替`std::set<ConNode>`，利用K值通常很小（1-3）的特点
* 每次修改后需要调用`update_from_map()`重建TopK，模拟了原版TopkSet的行为

```
// PoolTopkSet - 轻量级的 TopK 维护类
template<typename Number>
class PoolTopkSet {
private:
    uint32_t _k;
    std::vector<ConNode> _topk_nodes;  // 始终保持有序，最多 k 个元素
    
public:
    PoolTopkSet() : _k(0) {}
    
    explicit PoolTopkSet(uint32_t k) : _k(k) {
        _topk_nodes.reserve(_k);
    }
    
    // 初始化
    void init(uint32_t k) {
        _k = k;
        _topk_nodes.clear();
        _topk_nodes.reserve(_k);
    }
    
    // 从 value_map 重建 TopK
    template<typename MapType>
    void update_from_map(const MapType& value_map) {
        _topk_nodes.clear();
        
        if (_k == 0) {
            return;
        }
        
        // 收集所有节点
        std::vector<ConNode> all_nodes;
        all_nodes.reserve(value_map.size());
        
        value_map.traverse([&all_nodes](const uint64_t& key, const Number& value) {
            all_nodes.emplace_back(key, value);
        });
        
        // 使用 partial_sort 找出最大的 K 个
        if (all_nodes.size() <= _k) {
            _topk_nodes = std::move(all_nodes);
            std::sort(_topk_nodes.begin(), _topk_nodes.end());
        } else {
            std::partial_sort(all_nodes.begin(), all_nodes.begin() + _k, all_nodes.end());
            _topk_nodes.assign(all_nodes.begin(), all_nodes.begin() + _k);
        }
    }
    
    // 获取 K 值
    uint32_t k() const { return _k; }
    
    // 获取 TopK 节点数
    size_t size() const { return _topk_nodes.size(); }
    
    // 计算 TopK 的累积值（用于序列化兼容）
    Number kcumulant() const {
        Number sum = 0;
        for (const auto& node : _topk_nodes) {
            sum += node.value();
        }
        return sum;
    }
    
    // 获取 TopK 节点（const 版本）
    const std::vector<ConNode>& topk_nodes() const {
        return _topk_nodes;
    }
    
    // 序列化 - 兼容原版 TopkSet 格式
    template<typename Archive>
    bool serialize(Archive* ar) const {
        // 写入 k 值
        uint64_t k_64 = _k;
        if (!t_write<uint64_t, Archive>(k_64, ar)) {
            return false;
        }
        
        // 写入 total (kcumulant)
        Number total = kcumulant();
        if (!t_write<Number, Archive>(total, ar)) {
            return false;
        }
        
        // 写入 map size - 只序列化 TopK 中的元素
        size_t map_size = _topk_nodes.size();
        if (!t_write<size_t, Archive>(map_size, ar)) {
            return false;
        }
        
        // 写入 set size
        size_t set_size = _topk_nodes.size();
        if (!t_write<size_t, Archive>(set_size, ar)) {
            return false;
        }
        
        // 写入 map 内容（TopK 节点的键值对）
        for (const auto& node : _topk_nodes) {
            if (!t_write<uint64_t, Archive>(node.key(), ar) ||
                !t_write<Number, Archive>(node.value(), ar)) {
                return false;
            }
        }
        
        // 写入 set 内容（ConNode 序列化）
        for (const auto& node : _topk_nodes) {
            if (!node.serialize(ar)) {
                return false;
            }
        }
        
        return true;
    }
    
    // 反序列化 - 只读取元数据，实际数据通过 update_from_map 重建
    template<typename Archive>
    bool deserialize(Archive* ar) {
        uint64_t k_64;
        Number total;
        size_t map_size, set_size;
        
        if (!t_read<uint64_t, Archive>(&k_64, ar) || 
            !t_read<Number, Archive>(&total, ar) ||
            !t_read<size_t, Archive>(&map_size, ar) ||
            !t_read<size_t, Archive>(&set_size, ar)) {
            return false;
        }
        
        _k = static_cast<uint32_t>(k_64);
        
        // 跳过 map 内容
        for (size_t i = 0; i < map_size; ++i) {
            uint64_t key;
            Number value;
            if (!t_read<uint64_t, Archive>(&key, ar) || 
                !t_read<Number, Archive>(&value, ar)) {
                return false;
            }
        }
        
        // 跳过 set 内容
        for (size_t i = 0; i < set_size; ++i) {
            ConNode node;
            if (!node.deserialize(ar)) {
                return false;
            }
        }
        
        return true;
    }
};

// 重构后的 ConcentrationItemV2
class ConcentrationItemV2 {
private:
    typedef int64_t Number;
    
    Number _cumulant;
    PoolMiniHashMap<uint64_t, Number, GCSlabMempool32> _value_map;
    PoolMiniHashMap<uint64_t, std::string, GCSlabMempool32> _str_value_map;
    PoolTopkSet<Number> _topk_set;  // 使用封装的 TopK 管理类

public:
    // 默认构造函数
    ConcentrationItemV2() : 
        _cumulant(0), 
        _value_map(nullptr), 
        _str_value_map(nullptr) {}
    
    // 拷贝构造函数
    ConcentrationItemV2(const ConcentrationItemV2& other) :
        _cumulant(other._cumulant),
        _value_map(other._value_map),
        _str_value_map(other._str_value_map),
        _topk_set(other._topk_set) {}
    
    // 拷贝赋值运算符
    ConcentrationItemV2& operator=(const ConcentrationItemV2& other) {
        if (this != &other) {
            _cumulant = other._cumulant;
            _value_map = other._value_map;
            _str_value_map = other._str_value_map;
            _topk_set = other._topk_set;
        }
        return *this;
    }
    
    // 初始化方法 - 不保存字符串值
    bool init(uint64_t k, Number count, uint64_t data_view_sign, GCSlabMempool32* pool) {
        _cumulant = count;
        
        // 初始化 maps
        _value_map = PoolMiniHashMap<uint64_t, Number, GCSlabMempool32>(pool, 1);
        _str_value_map = PoolMiniHashMap<uint64_t, std::string, GCSlabMempool32>(pool, 1);
        
        // 初始化 topk set
        _topk_set.init(static_cast<uint32_t>(k));
        
        if (data_view_sign != 0) {
            auto result = _value_map.emplace(data_view_sign, count);
            if (!result.second) {
                CWARNING_LOG("init _value_map emplace failed");
                return false;
            }
        }
        
        // 始终更新 TopK（即使 map 为空）
        _topk_set.update_from_map(_value_map);
        
        return true;
    }
    
    // 初始化方法 - 保存字符串值
    bool init(uint64_t k, Number count, uint64_t data_view_sign, 
              const std::string& data_view_value, GCSlabMempool32* pool) {
        if (!init(k, count, data_view_sign, pool)) {
            return false;
        }
        
        if (data_view_sign != 0 && !data_view_value.empty()) {
            auto result = _str_value_map.emplace(data_view_sign, data_view_value);
            if (!result.second) {
                CWARNING_LOG("init _str_value_map emplace failed");
                return false;
            }
        }
        
        return true;
    }
    
    // 累积操作
    ConcentrationItemV2& operator+=(const ConcentrationItemV2& other) {
        _cumulant += other._cumulant;
        
        // 合并 value_map
        other._value_map.traverse([this](const uint64_t& key, const Number& value) {
            auto result = _value_map.emplace(key, value);
            if (!result.second) {
                *(result.first) += value;
            }
        });
        
        // 合并 str_value_map
        other._str_value_map.traverse([this](const uint64_t& key, const std::string& value) {
            _str_value_map.emplace(key, value);
        });
        
        // 更新 TopK
        _topk_set.update_from_map(_value_map);
        
        return *this;
    }
    
    // 减法操作
    ConcentrationItemV2& operator-=(const ConcentrationItemV2& other) {
        _cumulant -= other._cumulant;
        
        // 处理 value_map
        other._value_map.traverse([this](const uint64_t& key, const Number& value) {
            Number* value_ptr = _value_map.find(key);
            if (value_ptr) {
                *value_ptr -= value;
                if (*value_ptr <= 0) {
                    _value_map.remove(key);
                    _str_value_map.remove(key);
                }
            }
        });
        
        // 更新 TopK
        _topk_set.update_from_map(_value_map);
        
        return *this;
    }
    
    // 查询接口
    Number cumulant() const {
        return _cumulant;
    }
    
    // 获取 TopK 的累积值
    Number kcumulant() const {
        return _topk_set.kcumulant();
    }
    
    // 获取 TopK 信息，返回 ValueCumNode 格式
    bool get_topk_value_cum(std::vector<ValueCumNode>* topk_vec) const {
        if (!topk_vec) {
            CWARNING_LOG("get_topk_value_cum null pointer");
            return false;
        }
        
        topk_vec->clear();
        const auto& topk_nodes = _topk_set.topk_nodes();
        topk_vec->reserve(topk_nodes.size());
        
        for (const auto& node : topk_nodes) {
            const std::string* str_value = _str_value_map.find(node.key());
            std::string str_val = str_value ? *str_value : "_";
            topk_vec->emplace_back(str_val, node.value());
        }
        
        return true;
    }
    
    // 获取 TopK 信息，返回 ConNode 格式
    bool get_topk(std::vector<ConNode>* topk_vec) const {
        if (!topk_vec) {
            CWARNING_LOG("get_topk null pointer");
            return false;
        }
        
        *topk_vec = _topk_set.topk_nodes();
        return true;
    }
    
    // 序列化接口
    template<typename Archive>
    bool serialize(Archive* ar) const {
        // 1. 写入 cumulant
        if (!t_write<Number, Archive>(_cumulant, ar)) {
            CWARNING_LOG("serialize _cumulant failed");
            return false;
        }
        
        // 2. 写入 value_map
        size_t value_map_size = _value_map.size();
        if (!t_write<size_t, Archive>(value_map_size, ar)) {
            CWARNING_LOG("serialize value_map_size failed");
            return false;
        }
        
        bool success = true;
        _value_map.traverse([&](const uint64_t& key, const Number& value) {
            if (!success) return;
            
            if (!t_write<uint64_t, Archive>(key, ar) || 
                !t_write<Number, Archive>(value, ar)) {
                CWARNING_LOG("serialize _value_map node failed");
                success = false;
            }
        });
        if (!success) return false;
        
        // 3. 序列化 TopK 信息
        return _topk_set.serialize(ar);
    }
    
    template<typename Archive>
    bool deserialize(Archive* ar) {
        // 1. 读取 cumulant
        if (!t_read<Number, Archive>(&_cumulant, ar)) {
            CWARNING_LOG("deserialize _cumulant failed");
            return false;
        }
        
        // 2. 读取 value_map
        _value_map.clear();
        size_t value_map_size = 0;
        if (!t_read<size_t, Archive>(&value_map_size, ar)) {
            CWARNING_LOG("deserialize value_map_size failed");
            return false;
        }
        
        for (size_t i = 0; i < value_map_size; ++i) {
            uint64_t key;
            Number value;
            if (!t_read<uint64_t, Archive>(&key, ar) || 
                !t_read<Number, Archive>(&value, ar)) {
                CWARNING_LOG("deserialize _value_map node failed");
                return false;
            }
            if (!_value_map.emplace(key, value).second) {
                CWARNING_LOG("deserialize _value_map emplace failed");
                return false;
            }
        }
        
        // 3. 反序列化 TopK 元数据
        if (!_topk_set.deserialize(ar)) {
            return false;
        }
        
        // 4. 从 value_map 重建 TopK
        _topk_set.update_from_map(_value_map);
        
        return true;
    }
    
    // 静态方法：提供 slab 配置
    static void append_required_slab_sizes(std::vector<uint32_t>& slab_sizes) {
        slab_sizes.push_back(PoolMiniHashMap<uint64_t, Number, GCSlabMempool32>::get_node_size());
        slab_sizes.push_back(PoolMiniHashMap<uint64_t, std::string, GCSlabMempool32>::get_node_size());
    }
};
```
## 工厂模式集成
* 共用一个flag：`FLAGS_enable_mem_pool_for_acc`

```
// feature_accumulator_factory.cpp
FeatureAccumulatorInterface* FeatureAccumulatorFactory::create(const std::string& type) {
    FeatureAccumulatorInterface* obj = nullptr;
    
    if (type == "distinct") {
        if (FLAGS_enable_mem_pool_for_acc) {
            obj = new(std::nothrow) DistinctFeatureAccumulatorV2();
        } else {
            obj = new(std::nothrow) DistinctFeatureAccumulator();
        }
    } else if (type == "concentration") {
        if (FLAGS_enable_mem_pool_for_acc) {
            obj = new(std::nothrow) ConcentrationFeatureAccumulatorV2();
        } else {
            obj = new(std::nothrow) ConcentrationFeatureAccumulator();
        }
    } else if (type == "segment") {
        if (FLAGS_enable_mem_pool_for_acc) {
            obj = new(std::nothrow) SegmentFeatureAccumulatorV2();
        } else {
            obj = new(std::nothrow) SegmentFeatureAccumulator();
        }
    } else if (type == "ratio") {
        if (FLAGS_enable_mem_pool_for_acc) {
            obj = new(std::nothrow) RatioFeatureAccumulatorV2();
        } else {
            obj = new(std::nothrow) RatioFeatureAccumulator();
        }
    } else if (type == "distribution") {
        if (FLAGS_enable_mem_pool_for_acc) {
            obj = new(std::nothrow) PoolDistributionFeatureAccumulator();
        } else {
            obj = new(std::nothrow) DistributionFeatureAccumulator();
        }
    }
    
    return obj;
}
```
## PoolMiniHashMap 拷贝构造
```
// 拷贝构造函数 - 实现深拷贝，共享内存池
PoolMiniHashMap(const PoolMiniHashMap& other) 
    : _mem_pool(other._mem_pool),      // 复用相同内存池
      _bucket_count(other._bucket_count),
      _size(0) {
    
    if (!_mem_pool || !other._buckets || _bucket_count == 0) {
        _buckets = nullptr;
        _bucket_count = 0;
        return;
    }
    
    // 分配新的桶数组（不使用内存池，避免碎片）
    _buckets = new (std::nothrow) VAddr[_bucket_count];
    if (!_buckets) {
        _bucket_count = 0;
        return;
    }
    
    // 初始化所有桶为空
    for (uint32_t i = 0; i < _bucket_count; ++i) {
        _buckets[i] = MemPoolType::null();
    }
    
    // 深拷贝所有节点
    for (uint32_t i = 0; i < other._bucket_count; ++i) {
        VAddr current = other._buckets[i];
        while (current != MemPoolType::null()) {
            const HashNode* node = other.get_node_ptr(current);
            
            // 在新HashMap中插入节点，实现深拷贝
            // emplace会在内存池中分配新节点
            emplace(node->key(), node->value());
            
            current = node->next;
        }
    }
}

// 拷贝赋值运算符
PoolMiniHashMap& operator=(const PoolMiniHashMap& other) {
    if (this != &other) {
        // 清空当前内容，释放所有节点到内存池
        clear();
        
        // 确保使用相同的内存池
        _mem_pool = other._mem_pool;
        
        // 重新分配桶数组（如果大小不同）
        if (_bucket_count != other._bucket_count) {
            delete[] _buckets;
            _bucket_count = other._bucket_count;
            _buckets = new (std::nothrow) VAddr[_bucket_count];
            if (!_buckets) {
                _bucket_count = 0;
                return *this;
            }
        }
        
        // 初始化桶数组
        for (uint32_t i = 0; i < _bucket_count; ++i) {
            _buckets[i] = MemPoolType::null();
        }
        _size = 0;
        
        // 深拷贝所有节点
        for (uint32_t i = 0; i < other._bucket_count; ++i) {
            VAddr current = other._buckets[i];
            while (current != MemPoolType::null()) {
                const HashNode* node = other.get_node_ptr(current);
                emplace(node->key(), node->value());
                current = node->next;
            }
        }
    }
    return *this;
}
```
## 新增 MiniHashMap
```
template <typename Key, typename Value, typename MemPoolType>
class PoolMiniHashMap {
private:
    MemPoolType* _mem_pool;  // 内存池指针, 8Bytes
    // 优化的桶存储结构：2桶时直接存储，否则使用数组
    union BucketStorage {
        VAddr* array_buckets;     // 数组模式 (当桶数 > 2)
        struct {
            VAddr bucket0;        // 直接存储模式 (当桶数 = 2)
            VAddr bucket1;
        } direct_buckets;     
        BucketStorage() {
            direct_buckets.bucket0 = MemPoolType::null();
            direct_buckets.bucket1 = MemPoolType::null();
        }
    } _bucket_storage;
    uint32_t _bucket_count;  // bucket数量, 4Bytes
    uint32_t _size;          // 当前存储的元素数量, 4Bytes
};
```
## 监控实现重新设计
由于二期将内存池管理从 SlidingWindowV2 上移到 FeatureAccumulator，监控逻辑需要相应调整：

**一期监控流程**：

```
FeatureAccumulator.print_monitor_log()
  └── SlidingWindowV2.monitor() [包含内存池监控]
```
**二期监控流程**：

```
FeatureAccumulator.print_monitor_log()
  ├── GCSlabMempool32.monitor() [内存池监控]
  └── SlidingWindowV2.monitor() [仅滑动窗口监控]
```
### 监控项
* **数据内存**：实际存储的Item数据占用的内存
* **管理开销**：
    * 内存池管理开销
    * 滑动窗口管理开销
    * 累积器自身开销

* **元素统计**：窗口中的元素数、段中的元素数

#### 3.4.2 内存池监控信息获取
由于内存池现在由 `xxxFeatureAccumulator` 管理，需要直接调用内存池的监控接口：

```
void DistinctFeatureAccumulatorV2::_get_memory_pool_stats(
        uint64_t& data_mem, 
        uint64_t& pool_mgmt_overhead) const {
    if (!_mem_pool) {
        data_mem = 0;
        pool_mgmt_overhead = 0;
        return;
    }
    
    bsl::var::Dict pool_stats;
    bsl::ResourcePool rp;
    _mem_pool->monitor(pool_stats, rp);
    
    data_mem = pool_stats["ACTUAL_MEM_USED"].to_uint64();
    pool_mgmt_overhead = pool_stats["POOL_MANAGEMENT_OVERHEAD"].to_uint64();
}
```
#### 3.4.3 监控实现
```
void DistinctFeatureAccumulatorV2::print_monitor_log() const {
    // 1. 从SlidingWindowV2获取元素统计和滑动窗口开销
    bsl::var::Dict window_dict;
    bsl::ResourcePool rp;
    _pool_window.monitor(window_dict, rp);
    
    // 元素统计
    uint64_t total_elements = window_dict["TOTAL_ELEMENTS"].to_uint64();
    uint64_t window_elements = window_dict["WINDOW_ELEMENTS"].to_uint64();
    uint64_t segment_elements = window_dict["SEGMENT_ELEMENTS"].to_uint64();
    
    // 滑动窗口管理开销（注意：由于内存池外置，这里只有窗口自身开销）
    uint64_t sliding_window_overhead = window_dict["SLIDING_WINDOW_OVERHEAD"].to_uint64();
    
    // 2. 从内存池获取数据内存和内存池管理开销
    uint64_t data_mem = 0;
    uint64_t pool_mgmt_overhead = 0;
    _get_memory_pool_stats(data_mem, pool_mgmt_overhead);
    
    // 3. 累积器自身开销
    uint64_t accumulator_overhead = sizeof(DistinctFeatureAccumulatorV2);
    
    // 4. 计算总内存使用
    uint64_t total_overhead = pool_mgmt_overhead + sliding_window_overhead + accumulator_overhead;
    uint64_t total_mem = data_mem + total_overhead;
    
    // 5. 计算平均每个元素的内存占用
    double avg_mem_per_element = total_elements > 0 ? 
        static_cast<double>(total_mem) / total_elements : 0.0;
    
    // 6. 打印监控日志（保持与一期相同的格式）
    CNOTICE_LOG("Feature: ID=%lu, Type=POOL_DISTINCT, Elements=%lu (Window:%lu, Segment:%lu), "
               "DataMem=%luMB, PoolMgmt=%luMB, SlidingWindow=%luMB, Accumulator=%luB, "
               "TotalMem=%luMB, MemPerElement=%.2fB",
               _feature_id, total_elements, window_elements, segment_elements,
               data_mem / (1024 * 1024),
               pool_mgmt_overhead / (1024 * 1024),
               sliding_window_overhead / (1024 * 1024),
               accumulator_overhead,
               total_mem / (1024 * 1024),
               avg_mem_per_element);
}

void ConcentrationFeatureAccumulatorV2::print_monitor_log() const {
    if (_acc_main_view) {
        // 使用segment模式时，只统计元素数量
        // 因为ClickSegment不使用内存池，无法提供详细的内存统计
        uint64_t segment_elements = _segment.window_view_sign_size();
        CNOTICE_LOG("Feature: ID=%lu, Type=POOL_CONCENTRATION, Elements=%lu (Segment mode), Memory=N/A",
                   _feature_id, segment_elements);
    } else {
        // 使用window模式时，提供详细的内存统计
        bsl::var::Dict window_dict;
        bsl::ResourcePool rp;
        _pool_window.monitor(window_dict, rp);
        
        // 元素统计
        uint64_t total_elements = window_dict["TOTAL_ELEMENTS"].to_uint64();
        uint64_t window_elements = window_dict["WINDOW_ELEMENTS"].to_uint64();
        uint64_t segment_elements = window_dict["SEGMENT_ELEMENTS"].to_uint64();
        
        // 滑动窗口管理开销
        uint64_t sliding_window_overhead = window_dict["SLIDING_WINDOW_OVERHEAD"].to_uint64();
        
        // 从内存池获取数据内存和管理开销
        uint64_t data_mem = 0;
        uint64_t pool_mgmt_overhead = 0;
        _get_memory_pool_stats(data_mem, pool_mgmt_overhead);
        
        // 累积器自身开销
        uint64_t accumulator_overhead = sizeof(ConcentrationFeatureAccumulatorV2);
        
        uint64_t total_overhead = pool_mgmt_overhead + sliding_window_overhead + accumulator_overhead;
        uint64_t total_mem = data_mem + total_overhead;
        
        double avg_mem_per_element = total_elements > 0 ? 
            static_cast<double>(total_mem) / total_elements : 0.0;
        
        // 额外输出TopK值
        CNOTICE_LOG("Feature: ID=%lu, Type=POOL_CONCENTRATION, Elements=%lu (Window:%lu, Segment:%lu), "
                   "DataMem=%luMB, PoolMgmt=%luMB, SlidingWindow=%luMB, Accumulator=%luB, "
                   "TotalMem=%luMB, MemPerElement=%.2fB, TopK=%lu",
                   _feature_id, total_elements, window_elements, segment_elements,
                   data_mem / (1024 * 1024),
                   pool_mgmt_overhead / (1024 * 1024),
                   sliding_window_overhead / (1024 * 1024),
                   accumulator_overhead,
                   total_mem / (1024 * 1024),
                   avg_mem_per_element,
                   _top);
        // 如果当前 Feature 使用了 _need_top_data_views，那么会导致 Item 中存储了 std::string，这部分内存不由内存池托管，需要额外打印一行日志提醒。
        CNOTICE_LOG("Feature: ID=%lu, Due to the current feature using _need_top_data_views, some memory is not being monitored and requires additional attention.", _feature_id);
    }
}
```