首先完全理解现有的所有代码和文档,现在的代码是一期已经上线了的版本,虽然和一期详细设计文档中有一些出入,但是核心思想一致，所以理解时应该以代码为准。

其中：

1. xxx_feature_accumulator.cpp/h 是每一种 FeatureAccumulator 的具体实现，他们都实现了 FeatureAccumulatorInterface

2. feature_accumulator_manager.cpp/h 是 FeatureAccumulator 的直接管理者，feature_accumulator_collector.cpp/h 是 FeatureAccumulatorManager 的管理者。

3. feature_accumulator_factory.cpp 是 FeatureAccumulator 的工厂方法实现

4. gc_slab_mempool_32.cpp/h 是我自己实现的一个内存池，由多 slab 大小、32 虚地址的功能

5. pool_hash_map.hpp 中是我基于内存池自己实现的一个HashMap，接口和 std::unordered_map 不完全兼容，但是有相似的地方，其中用到了很多小技巧来节省内存

6. mini_pool_hash_map.hpp 中是 pool_hash_map.hpp 在小数据量（桶数小于 2）的时候的优化版本，设计出来是为了在 PoolconcentraionItem 和 PoolDistinctItem 中使用，因为根据前期调研，他们持有的 hashmap 元素数量都很少

7. pool_sliding_window.hpp/h 是 sliding_window.hpp/h 的替代版本，其中使用 pool_hash_map 替换了 sliding_window 中的 std::unordered_map，也是为了节省内存

8. item.hpp/h 中是原本所有类型的 Item 的实现

9. archive.cpp 中是读写 checkpoint 文件的接口