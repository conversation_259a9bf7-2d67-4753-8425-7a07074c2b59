# CLAUDE.md

## 仓库概述
该仓库包含两个主要组件: 
1. **themis-common-lib** (`baidu/anti/themis-common-lib/`): 核心通用库，包括内存池、滑动窗口、字典管理和实用功能
2. **feature-lib** (`baidu/anti/feature-lib/`): 用于反作弊系统中特性累积、提取和策略执行的特征处理库

## 当前状态（第二阶段已完成 - DistinctFeatureAccumulator优化）
代码库已完成两个阶段的内存池优化：
- **第一阶段已完成**: 简单累加器的内存池优化
- **第二阶段已完成**: `DistinctFeatureAccumulatorV2`和`DistinctItemV2`的完整实现，实现了预期的30%+内存减少目标

### 第二阶段成果
✅ **DistinctFeatureAccumulatorV2实现完成**:
- 使用`PoolMiniHashMap`替代`std::unordered_map`，针对1-2个元素的常见场景优化
- 内存池生命周期由`FeatureAccumulator`管理，支持外部内存池模式
- 完全向后兼容，支持运行时通过`FLAGS_enable_mem_pool_for_acc`切换
- 包含完整的监控、序列化和错误处理机制

⏳ **待实现**: `ConcentrationFeatureAccumulatorV2`优化（更复杂的嵌套结构，占总内存的剩余部分）

## 核心架构
### 特性处理流水线
```
日志数据 → 特征提取 → 特征累积 → 策略评估 → 安全决策
```

### 关键组件
**FeatureAccumulatorInterface** (`accumulator/inc/feature_accumulator_interface.h`):
- 所有特性累加器的基础接口
- 核心方法: `init()`, `update()`, `update_and_query()`, `query()`, `load()`, `dump()`
- 支持检查点序列化和监控

**FeatureAccumulatorFactory** (`accumulator/dev/feature_accumulator_factory.cpp`):
- 用于创建不同累加器类型的工厂模式
- 支持20多种累加器类型，包括distinct、concentration、segment、ratio等
- 使用基于字符串的类型识别

### 内存池架构（第一阶段实现）
**GCSlabMempool32** (`memory_pool/inc/gc_slab_mempool_32.h`):
- 具有32位虚拟寻址的多slab大小内存池
- 支持垃圾回收和内存监控
- 具有基于块分配的线程安全操作

**PoolHashMap** (`sliding_window/inc/pool_hash_map.hpp`):
- 基于内存池的`std::unordered_map`替代品
- 使用链式解决冲突
- 通过自定义哈希和负载因子管理优化性能
- 为滑动窗口操作提供深拷贝语义

**PoolMiniHashMap** (`sliding_window/inc/pool_mini_hash_map.hpp`):
- 针对桶数≤2的专用版本
- 直接存储优化以减少内存开销
- 为DistinctItem和ConcentrationItem用例设计

**SlidingWindowV2** (`sliding_window/inc/sliding_window_v2.hpp`):
- 支持内存池的滑动窗口实现
- 支持内部和外部内存池管理（V2版本使用外部内存池模式）
- 具有可配置步长的时间分段
- 基于`PoolHashMap`实现StepContainer

### 项目数据结构
**DistinctItem** (在 `sliding_window/inc/item.h`):
```cpp
class DistinctItem {
    std::unordered_map<uint64_t, int64_t> _map;  // data_view_sign -> count
    int64_t _total;
    // 大小: 约64字节
};
```

**DistinctItemV2** (在 `sliding_window/inc/item_v2.h`) - ✅ 已实现:
```cpp
class DistinctItemV2 {
    PoolMiniHashMap<uint64_t, int64_t, GCSlabMempool32> _map;  // 内存池优化
    int64_t _total;
    // 大小: 约32字节（50%内存减少）
    // 支持深拷贝语义和序列化兼容性
};
```

**ConcentrationItem** (在 `sliding_window/inc/item.h`):
```cpp
class ConcentrationItem {
    Number _cumulant;
    std::unordered_map<uint64_t, Number> _value_map;
    std::unordered_map<uint64_t, std::string> _str_value_map;
    TopkHeap _topk_heap;  // 包含 std::unordered_map + std::set
    // 大小: 约240字节
};
```

### V2优化架构和成果
**已完成的优化（DistinctFeatureAccumulator）**:
- ✅ 内存减少50%：从64字节降至32字节
- ✅ 内存池生命周期管理：从SlidingWindow转向FeatureAccumulator管理  
- ✅ 深拷贝语义：PoolMiniHashMap支持滑动窗口的+=、-=操作
- ✅ 序列化兼容性：与原版二进制格式完全兼容
- ✅ 工厂集成：运行时可通过`FLAGS_enable_mem_pool_for_acc`切换

**待优化目标（ConcentrationFeatureAccumulator）**:
- ⏳ **复杂嵌套结构**: ConcentrationItem有3个unordered_maps + 1个set
- ⏳ **TopK逻辑**: 在保持兼容性的同时精简TopkSet结构
- ⏳ **内存减少目标**: 从240字节实现30%+减少

## 数据流模式
### 标准处理流程
```cpp
bool FeatureAccumulator::update_and_query(FeatureValueProto* fea) {
    // 1. 创建带有内存池指针的临时Item（第二阶段要求）
    Item item(fea->data_view_sign(), count, &_mem_pool);
    
    // 2. 进入滑动窗口（通过深拷贝实现拷贝语义）
    const Item* result = nullptr;
    _window.enter(fea->view_sign(), fea->coord(), item, &result);
    
    // 3. 用累积结果填充响应
    return _fillup_feature(result, fea);
}
```

### 内存池集成点
- **Item构造**: 必须将内存池指针传递给嵌套的PoolHashMaps
- **滑动窗口操作**: Item聚合的深拷贝语义（+=, -=）
- **检查点序列化**: 与std::unordered_map兼容的二进制格式
- **监控**: 跨池、窗口和累加器的内存使用跟踪

## 实现指南
### 第二阶段开发方法
1. **双重实现**: 创建V2版本与原始类并存
2. **工厂集成**: 使用`FLAGS_enable_mem_pool_for_acc`进行选择
3. **接口兼容性**: 保持相同的外部API
4. **序列化兼容性**: 二进制兼容的 checkpoint 格式

### 内存池使用模式（V2实现）
```cpp
// V2版本内存池生命周期管理 - ✅ 已实现
class DistinctFeatureAccumulatorV2 {
    GCSlabMempool32 _mem_pool;          // 由累加器拥有和管理
    SlidingWindowV2<DistinctItemV2> _pool_window;     // 使用外部内存池
    
    bool init(const comcfg::ConfigUnit& conf) {
        _init_memory_pool();  // 系统化收集所有组件的slab大小
        _pool_window.init(step_length, max_step_num, window_length, &_mem_pool);
    }
    
private:
    bool _init_memory_pool() {
        std::vector<uint32_t> slab_sizes;
        // 1. 收集DistinctItemV2需要的slab大小
        DistinctItemV2::append_required_slab_sizes(slab_sizes);
        // 2. 收集SlidingWindowV2内部PoolHashMap节点大小
        slab_sizes.push_back(StepContainer::get_node_size());
        // 3. 去重并创建内存池
        return _mem_pool.create(slab_sizes.data(), slab_sizes.size(), FLAGS_pool_mempool_block_item_num) == 0;
    }
};
```

### 关键设计模式
- **RAII**: 适当的构造函数/析构函数用于内存管理
- **异常安全**: 在错误条件下无内存泄漏
- **线程安全**: 互斥锁保护并发访问
- **监控集成**: 统计数据收集以实现操作可见性

## 关键文件及其用途

### 核心接口文件
- `accumulator/inc/feature_accumulator_interface.h`: 基础接口定义
- `accumulator/dev/feature_accumulator_factory.cpp`: 工厂实现（已支持V2版本切换）
- `accumulator/dev/feature_accumulator_gflags.cpp`: 统一的GFLAGS定义

### V2实现文件（✅ 已完成）
- `accumulator/dev/distinct_feature_accumulator_v2.h`: V2累加器接口定义
- `accumulator/dev/distinct_feature_accumulator_v2.cpp`: V2累加器完整实现
- `sliding_window/inc/item_v2.h`: DistinctItemV2数据结构定义

### 内存池基础设施
- `memory_pool/inc/gc_slab_mempool_32.h`: 内存池接口和实现
- `sliding_window/inc/pool_hash_map.hpp`: 基于内存池的HashMap（SlidingWindowV2使用，已支持深拷贝）
- `sliding_window/inc/pool_mini_hash_map.hpp`: 小数据量优化版本（ItemV2使用，已支持深拷贝）
- `sliding_window/inc/sliding_window_v2.hpp`: V2滑动窗口实现

### 原版数据结构（保持兼容）
- `sliding_window/inc/item.h`: 原版Item类型定义
- `sliding_window/inc/sliding_window.hpp`: 原版滑动窗口实现
- `sliding_window/dev/archive.cpp`: 检查点序列化
- `accumulator/dev/distinct_feature_accumulator.h/.cpp`: 原版累加器实现

### 管理层
- `accumulator/inc/feature_accumulator_manager.h`: 累加器生命周期管理
- `accumulator/inc/feature_accumulator_collector.h`: 高级累加器协调

## 部署和使用

### 启用V2版本
```bash
# 通过GFLAGS启用内存池优化版本
--enable_mem_pool_for_acc=true
```

### 监控和调试
V2版本提供增强的监控功能：
```cpp
// V2版本专用监控日志格式
Feature: ID=123, Type=POOL_DISTINCT_V2, Elements=1000, 
DataMem=5MB, PoolMgmt=1MB, SlidingWindow=2MB, 
TotalMem=8MB, MemPerElement=8.0B
```

### 性能基准
- **内存减少**: DistinctItem从64字节降至32字节（50%减少）
- **分配效率**: 内存池批量分配减少系统调用开销
- **兼容性**: 100%向后兼容，支持热切换

## 开发注意事项
- **无编译环境**: 此环境无法构建/运行代码，专注于代码分析和设计
- **代码优先方法**: 使用实际实现作为设计文档的真实来源
- **命名风格**: V2版本使用xxxV2命名，避免与Pool前缀混淆
- **渐进式部署**: 支持运行时切换，可安全地在生产环境中测试和部署
